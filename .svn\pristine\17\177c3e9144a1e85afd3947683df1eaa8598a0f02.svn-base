<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>交通运输-中间</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <link rel="stylesheet" href="/static/citybrain/shgl/css/jtys-middle.css" />
    <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
</head>
<style></style>

<body>
    <div id="jtys-middle" v-cloak>
        <div class="jtys-container" v-if="isShow">
            <div class="head">
                <span>{{title}}详情</span>
                <div class="img" @click="closeDialog"></div>
            </div>
            <!-- 地图点击弹窗 -->
            <div class="map-dialog">
                <li v-for="(item,index) in stationData" :key="index">
                    <span class="name">{{item.label}}:</span>
                    <span class="value">{{item.value}}</span>
                </li>
                <div class="bottom-btn" v-if="isVisible===1">
                    <div :class="[currentTab===index?'active':'','bottom-btn-item']" v-for="(item,index) in tabList"
                        @click="changeTab(index,item.value)" :key="index">
                        {{item.name}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>

<script>
    var vm = new Vue({
        el: "#jtys-middle",
        data() {
            return {
                title: "", //站点标题
                isShow: false, //是否展示站点详情
                stationData: [], //站点详情数据
                isVisible: 0, //是否展示底部按钮
                historyPathList: [], //历史轨迹
                tabList: [
                    {
                        name: "实时轨迹",
                        value: "1",
                    },
                    {
                        name: "历史轨迹",
                        value: "2",
                    },
                ],
                currentTab: 0,
            };
        },
        mounted() {
            let that = this;
            window.addEventListener("message", function (event) {
                if (Object.prototype.toString.call(event.data) === "[object Object]" && event.data) {
                    if (event.data.type == "pointClick") {
                        that.isShow = true;
                        let data = JSON.parse(event.data.data.data);
                        console.log(333, data.obj);
                        that.title = data.obj.name;
                        that.isVisible = data.obj.isVisible;
                        console.log(that.isVisible);
                        $get("/shgl/doing/jtysdwxq").then((res) => {
                            let arr = [];
                            arr = res.filter((item) => {
                                return item.id === data.obj.id;
                            });
                            that.stationData = arr[0].detail;
                            that.historyPathList = arr[0].path;
                        });
                    }
                }
            });
        },
        methods: {
            changeTab(index, type) {
                console.log(this.historyPathList);
                this.currentTab = index;
                const imgIcon = `${baseURL.url}/static/citybrain/tckz/img/tckz_gj/${this.title}.png`;
                let color = "";
                if (type == 2) {
                    color = "#e86056";
                } else {
                    color = "#85d4b1";
                }
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "createTrajectory",
                        data: {
                            id: "TDT_TITLE_ID",
                            type: "dynamic",
                            icon: imgIcon,
                            coordinates: this.historyPathList,
                            iconStyle: {
                                "icon-size": 0.7,
                                "icon-rotate": 360,
                            },
                            style: {
                                "line-width": 10,
                            },
                            isGlowLine: false,
                            isBezierCurve: false,
                            color: [color],
                            loop: false,
                            steps: 100,
                        },
                    })
                );
            },
            // drawLine() {

            // },
            closeDialog() {
                this.isShow = false;
            },
        },
        destroyed() {
            top.document.getElementById("map").contentWindow.egs1.contentWindow.map.TDT_TITLE_ID.remove();
        },
    });
</script>

</html>