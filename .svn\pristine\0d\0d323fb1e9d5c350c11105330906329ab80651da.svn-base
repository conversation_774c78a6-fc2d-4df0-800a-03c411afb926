<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <title>城市安全管控指标分析-右</title>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <script src="/Vue/vue.js"></script>
        <script src="/static/js/jslib/vue-count-to.min.js"></script>
        <script src="/echarts/echarts.min.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/datav.min.vue.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <style>
            * {
                margin: 0;
                padding: 0;
            }
            #app {
                position: relative;
                width: 1050px;
                height: 1930px;
                background: url("/img/left-bg.png") no-repeat;
                background-size: 100% 100%;
                padding: 30px;
                box-sizing: border-box;
                overflow: hidden;
            }
            .header-title2[data-v-4d0d1712] {
                width: 100% !important;
            }
            .main_list {
                width: 420px !important;
                height: 130px !important;
                margin: 10px 30px;
                background: linear-gradient(
                    to bottom,
                    rgba(31, 105, 218, 0.2),
                    rgba(31, 105, 218, 0.5),
                    rgba(31, 105, 218, 0.9)
                );
                text-align: center;
                padding-top: 40px;
            } /* 下拉框 */
            .select {
                position: absolute;
                left: 820px;
                top: 445px;
            }
            .select1 {
                position: absolute;
                left: 820px;
                top: 1185px;
            }
            .el-select {
                width: 150px;
            }
            .el-input__inner {
                height: 50px !important;
                width: 200px !important;
                background-color: #00487f;
                color: #fff;
                font-size: 28px;
            }
            .el-select-dropdown {
                border: 1px solid #2578a6;
                background-color: #032f46d3;
            }
            .el-select-dropdown__item.hover,
            .el-select-dropdown__item:hover {
                background-color: #00487f;
            }
            .el-select-dropdown__item {
                color: #fff;
                background-color: #00487f;
                font-size: 28px;
                height: 50px;
                line-height: 50px;
            }
            .el-select-dropdown__list {
                background-color: #00487f;
            }
            .el-select .el-input .el-select__caret {
                position: relative;
                left: 40px;
                font-size: 28px;
                color: #fff;
            }
            .el-select .el-input__inner {
                /* border-radius: 30px !important; */
            }
            .el-scrollbar {
                width: 200px;
            }
            .el-input.is-disabled .el-input__inner {
                background-color: #2d4a67;
            }
        </style>
    </head>
    <body>
        <div id="app">
            <div class="s-flex s-flex-wrap s-row-between">
                <dv-border-box-8 class="main_list" v-for="(item,index) in list" :key="index">
                    <p class="s-font-30 s-c-white">{{item.name}}</p>
                    <p class="s-font-30 s-c-white">
                        <count-to
                            :start-val="0"
                            :end-val="item.value"
                            :duration="3000"
                            class="s-c-yellow-gradient s-font-40"
                        ></count-to>
                        {{item.unit}}
                    </p>
                </dv-border-box-8>
            </div>

            <nav class="s-m-t-20 s-m-b-10">
                <s-header-title title="火灾事件分析" htype="2"></s-header-title>
            </nav>
            <div class="select">
                <el-select v-model="value" @change="selectMothFun" placeholder="月份">
                    <el-option v-for="item,index in options" :key="index" :label="item" :value="item"> </el-option>
                </el-select>
            </div>
            <div id="bar_eh1" style="width: 100%; height: 610px"></div>
            <nav class="s-m-t-20 s-m-b-10">
                <s-header-title title="区域分类统计分析" htype="2"></s-header-title>
            </nav>
            <div class="select1">
                <el-select v-model="value1" @change="selectMothFun1" placeholder="月份">
                    <el-option v-for="item,index in options1" :key="index" :label="item" :value="item"> </el-option>
                </el-select>
            </div>
            <div id="bar_eh2" style="width: 100%; height: 610px"></div>
        </div>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script>
            let vm = new Vue({
                el: "#app",
                data: {
                    list: [],
                    value: "一月",
                    options: [
                        "一月",
                        "二月",
                        "三月",
                        "四月",
                        "五月",
                        "六月",
                        "七月",
                        "八月",
                        "九月",
                        "十月",
                        "十一月",
                        "十二月",
                    ],
                    value1: "一月",
                    options1: [
                        "一月",
                        "二月",
                        "三月",
                        "四月",
                        "五月",
                        "六月",
                        "七月",
                        "八月",
                        "九月",
                        "十月",
                        "十一月",
                        "十二月",
                    ],
                },
                mounted() {
                    this.initApi();
                },
                methods: {
                    initApi() {
                        $api("ldst_shgl_csaqgk", { type1: 1 }).then((res) => {
                            this.list = res.slice(4);
                        });
                        $api("ldst_shgl_csaqgk_month", { type1: 4, month: "一月" }).then((res) => {
                            this.getBar("bar_eh1", res);
                        });
                        $api("ldst_shgl_csaqgk_month", { type1: 5, month: "一月" }).then((res) => {
                            this.getBar("bar_eh2", res);
                        });
                    },
                    selectMothFun(e) {
                        $api("ldst_shgl_csaqgk_month", { type1: 4, month: e }).then((res) => {
                            this.getBar("bar_eh1", res);
                        });
                    },
                    selectMothFun1(e) {
                        $api("ldst_shgl_csaqgk_month", { type1: 5, month: e }).then((res) => {
                            this.getBar("bar_eh2", res);
                        });
                    },

                    getBar(id, echartsData) {
                        const myChartsRun = echarts.init(document.getElementById(id));
                        let xdata = [],
                            ydata1 = [],
                            ydata2 = [],
                            line = [],
                            length = [];

                        if (id == "bar_eh1") {
                            length = ["发生次数", "死亡人数", "趋势"];
                            echartsData.map((ele) => {
                                xdata.push(ele.name);
                                ydata1.push(ele.total);
                                ydata2.push(ele.num);
                                line.push(ele.qs);
                            });
                        } else {
                            length = ["发生次数", "死亡人数"];
                            echartsData.map((ele) => {
                                xdata.push(ele.name);
                                ydata1.push(ele.gfq);
                                ydata2.push(ele.dfq);
                            });
                        }

                        var option = {
                            tooltip: {
                                //提示框组件
                                trigger: "axis",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                textStyle: {
                                    color: "white",
                                    fontSize: "24",
                                },
                            },
                            grid: {
                                left: "1%",
                                right: "2%",
                                bottom: "5%",
                                top: "12%",
                                containLabel: true,
                            },
                            legend: {
                                itemGap: 16,
                                itemWidth: 20,
                                itemHeight: 20,
                                textStyle: {
                                    color: "#fff",
                                    fontStyle: "normal",
                                    fontFamily: "微软雅黑",
                                    fontSize: 24,
                                },
                            },
                            xAxis: [
                                {
                                    type: "category",
                                    data: xdata,
                                    axisLabel: {
                                        margin: 20,
                                        rotate: -20,
                                        color: "#fff",
                                        fontSize: "26px",
                                    },
                                    axisLine: {
                                        show: true,
                                        lineStyle: {
                                            color: "#bbb",
                                        },
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#195384",
                                        },
                                    },
                                },
                            ],
                            yAxis: [
                                {
                                    type: "value",
                                    name: "单位：起",
                                    splitNumber: 5,
                                    nameTextStyle: {
                                        color: "#fff",
                                        fontSize: 24,
                                        padding: [10, 0, 20, 0],
                                    },
                                    axisLabel: {
                                        formatter: "{value}",
                                        textStyle: {
                                            color: "#fff",
                                            fontSize: "24px",
                                        },
                                    },
                                    axisLine: {
                                        lineStyle: {
                                            color: "#fff",
                                        },
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#5087EC",
                                        },
                                    },
                                },
                                {
                                    type: "value",
                                    nameTextStyle: {
                                        color: "#fff",
                                        fontSize: 24,
                                        padding: [0, 0, 20, 50],
                                    },
                                    axisLabel: {
                                        formatter: "{value}%",
                                        textStyle: {
                                            color: "#fff",
                                            fontSize: "24px",
                                        },
                                    },
                                    axisLine: {
                                        lineStyle: {
                                            color: "#fff",
                                        },
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#5087EC",
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    name: length[0],
                                    type: "bar",
                                    data: ydata1,
                                    barWidth: 20,
                                    itemStyle: {
                                        //图形样式
                                        normal: {
                                            barBorderRadius: [5, 5, 0, 0],
                                            color: new echarts.graphic.LinearGradient(
                                                0,
                                                0,
                                                0,
                                                1,
                                                [
                                                    {
                                                        offset: 1,
                                                        color: "rgba(50, 150, 250, 0.1)",
                                                    },
                                                    {
                                                        offset: 0.5,
                                                        color: "rgba(50, 150, 250, 0.5)",
                                                    },
                                                    {
                                                        offset: 0,
                                                        color: "rgba(50, 150, 250, 1)",
                                                    },
                                                ],
                                                false
                                            ),
                                        },
                                    },
                                },
                                {
                                    name: length[1],
                                    type: "bar",
                                    data: ydata2,
                                    barWidth: 20,
                                    itemStyle: {
                                        //图形样式
                                        normal: {
                                            barBorderRadius: [5, 5, 0, 0],
                                            color: new echarts.graphic.LinearGradient(
                                                0,
                                                0,
                                                0,
                                                1,
                                                [
                                                    {
                                                        offset: 1,
                                                        color: "rgba(62, 208, 178, 0.1)",
                                                    },
                                                    {
                                                        offset: 0.5,
                                                        color: "rgba(62, 208, 178, 0.5)",
                                                    },
                                                    {
                                                        offset: 0,
                                                        color: "rgba(62, 208, 178, 1)",
                                                    },
                                                ],
                                                false
                                            ),
                                        },
                                    },
                                },
                                {
                                    name: length[2],
                                    type: "line",
                                    data: line,
                                    yAxisIndex: 1,
                                    itemStyle: {
                                        normal: {
                                            color: "#ffd767",
                                        },
                                    },
                                },
                            ],
                        };
                        myChartsRun.setOption(option);
                        tools.loopShowTooltip(myChartsRun, option, {
                            loopSeries: true,
                        }); //轮播
                    },
                },
            });
        </script>
    </body>
</html>
