.container{
    border-top-left-radius: 0!important;
    border-top-right-radius: 0!important;
    width: 1400px!important;
    height: 1100px!important;
}
.container .head{
    border-top-left-radius: 0!important;
    border-top-right-radius: 0!important;
    background-image: linear-gradient(0deg, #073346 0%, #00a9e282 100%), linear-gradient(#ffffff, #ffffff);
}
.TitleTop{
    display: flex;
    align-items: center;
}

.TitleTop span{
    font-size: 35px;
    background: linear-gradient(to top, #83b8ff, #74b4f4, #f0ffff);
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 800;
}

.image{
  margin-top: 8px;
  margin-right: 15px;
}
.thName{
    display: flex;
    line-height: 80px;
    justify-content: space-around;
    font-size: 35px;
    color: #ffffff;
    background-color: #105391a6;
}
.table{
    margin-top: 20px;
}
.tbody{
    display: flex;
    line-height: 80px;
    justify-content: space-around;
    font-size: 35px;
    color: #ffffff;
}
.tbody:nth-child(2n){
    background-color: #0e376294;
}
.ulTitle{
    font-size: 35px;
    color: #ffff;
}
.ulTitle li{
    list-style: none;
    line-height: 80px;
}