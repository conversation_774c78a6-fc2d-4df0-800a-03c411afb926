<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>食品药品监管分析右侧</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <link rel="stylesheet" href="/static/citybrain/scjg/css/spypjg-right.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
    <!-- 轮播toolTip -->
</head>

<body>
    <div id="app" class="container" v-cloak>
        <!-- 食品投诉统计 -->
        <div class="spts">
            <nav>
                <s-header-title-2 htype="1" title="食品药品监管信息">
                    </s-header-title>
            </nav>
            <div class="spts-con">
                <!-- 食品药品监管信息统计展示 -->
                <div class="list">
                    <nav>
                        <s-header-title-2 htype="1" title="食品药品监管信息统计展示">
                            </s-header-title>
                    </nav>
                    <div class="chart-con" id="chart1" style="height: 92%;"></div>
                </div>
                <!-- 食品药品生产经营主体统计展示 -->
                <div class="list">
                    <nav>
                        <s-header-title-2 htype="1" title="食品药品生产经营主体统计展示">
                            </s-header-title>
                    </nav>
                    <div class="chart-con" id="chart2"></div>
                </div>
                <!-- 检查工作执行情况统计展示 -->
                <div class="list">
                    <nav>
                        <s-header-title-2 htype="1" title="检查工作执行情况统计展示">
                            </s-header-title>
                    </nav>
                    <div class="chart-con" id="chart3"></div>
                </div>
            </div>
            <div class="spts-con">
                <div class="list" style="width: 425px;">
                    <div class="chart-con" style="margin-top: 75px;">
                        <li v-for="(item,index) in tjList" :key="index">
                            <div>{{item.name}}</div>
                            <div>{{item.value}}</div>
                        </li>
                    </div>
                </div>
                <!-- 监督检查概况展示 -->
                <div class="list" style="width: 50%;">
                    <nav>
                        <s-header-title-2 htype="1" title="监督检查概况展示">
                            </s-header-title>
                    </nav>
                    <div class="chart-con" id="chart4"></div>
                </div>
                <!-- 检查工作统计展示 -->
                <div class="list">
                    <nav>
                        <s-header-title-2 htype="1" title="检查工作统计展示">
                            </s-header-title>
                    </nav>
                    <div class="chart-con" id="chart5"></div>
                </div>

            </div>
        </div>
        <!-- 食品药品风险预警 -->
        <div class="ypts">
            <nav>
                <s-header-title-2 htype="1" title="食品药品风险预警">
                    </s-header-title>
            </nav>
            <div class="spts-con">
                <!-- 预警总体概况统计展示 -->
                <div class="list" style="width: 425px;">
                    <nav>
                        <s-header-title-2 htype="1" title="预警总体概况统计展示">
                            </s-header-title>
                    </nav>
                    <div class="chart-con">
                        <li v-for="(item,index) in tjList1" :key="index"
                            style="height: 28%;line-height: 42px;padding: 5px 40px;font-size: 24px;">
                            <div>{{item.value}}</div>
                            <div>{{item.name}}</div>
                        </li>
                    </div>
                </div>
                <!-- 许可证到期预警趋势统计展示 -->
                <div class="list" style="position: relative;">
                    <nav>
                        <s-header-title-2 htype="1" title="许可证到期预警趋势统计展示">
                            </s-header-title>
                    </nav>
                    <div class="tab-con">
                        <li v-for="(item,index) in tabList" :key="index" @click="clickTab(index,item)"
                            :class="currentIndex===index?'active':''">
                            {{item}}
                        </li>
                    </div>
                    <div class="s-c-blue-gradient s-font-25 s-text-center">许可证到期预警总数:102次 </div>
                    <div class="chart-con" id="chart6"></div>
                </div>
                <!-- 整改预警趋势统计展示-->
                <div class="list">
                    <nav>
                        <s-header-title-2 htype="1" title="整改预警趋势统计展示">
                            </s-header-title>
                    </nav>
                    <div class="chart-con" id="chart7"></div>
                </div>
                <!-- 健康证到期预警统计展示 -->
                <div class="list" style="position: relative;">
                    <nav>
                        <s-header-title-2 htype="1" title="健康证到期预警统计展示">
                            </s-header-title>
                    </nav>
                    <div class="tab-con">
                        <li v-for="(item,index) in tabList" :key="index" @click="clickTab1(index,item)"
                            :class="currentIndex1===index?'active':''">
                            {{item}}
                        </li>
                    </div>
                    <div class="s-c-blue-gradient s-font-25 s-text-center">健康证到期预警总数 139次</div>
                    <div class="chart-con" id="chart8"></div>
                </div>
            </div>
            <div class="spts-con">
                <!-- 12315月度投诉总数统计展示 -->
                <div class="list">
                    <nav>
                        <s-header-title-2 htype="1" title="12315月度投诉总数统计展示">
                            </s-header-title>
                    </nav>

                    <div class="chart-con" id="chart9"></div>
                </div>
                <!-- 按地区维度投诉解决比率统计展示 -->
                <div class="list">
                    <nav>
                        <s-header-title-2 htype="1" title="按地区维度投诉解决比率统计展示">
                            </s-header-title>
                    </nav>

                    <div class="chart-con" id="chart10"></div>
                </div>
                <!-- 按食品药品被投诉企业数量统计展示 -->
                <div class="list" style="position: relative;">
                    <nav>
                        <s-header-title-2 htype="1" title="按食品药品被投诉企业数量统计展示">
                            </s-header-title>
                    </nav>
                    <div class="tab-con">
                        <li v-for="(item,index) in tabList2" :key="index" @click="clickTab2(index,item)"
                            :class="currentIndex2===index?'active':''">
                            {{item}}
                        </li>
                    </div>
                    <div class="xq-btn" @click="openIframe">详情</div>
                    <div class="chart-con" id="chart11"></div>
                </div>
            </div>
        </div>

    </div>
</body>
<script type="module">
    new Vue({
        el: "#app",
        data: {
            tjList: [
                {
                    name: "专项检查总次数",
                    value: "1208次"
                },
                {
                    name: "检查主体数",
                    value: "387户"
                },
            ],
            tjList1: [
                {
                    name: "许可证到期预警",
                    value: "102次"
                },
                {
                    name: "整改预警",
                    value: "212次"
                },
                {
                    name: "健康证到期预警",
                    value: "139次"
                },
            ],
            tabList: ["时间", "区域"],
            currentIndex: 0,
            currentIndex1: 0,
            tabList2: ["食品", "药品"],
            currentIndex2: 0,
        },
        methods: {
            //查看详情
            openIframe() {
                let iframe1 = {
                    type: "openIframe",
                    name: "spypjg-dialog1",
                    src: baseURL.url + "/static/citybrain/scjg/commont/spypjg-dialog1.html",
                    width: "1245px",
                    height: "460px",
                    left: "5855px",
                    top: "1375px",
                    zIndex: "100",
                };
                window.parent.postMessage(JSON.stringify(iframe1), "*");
            },
            //切换点击
            clickTab(index, data) {
                this.currentIndex = index;
                this.getData(index + 1);
            },
            //切换点击
            clickTab1(index, data) {
                this.currentIndex1 = index;
                this.getData1(index + 1);
            },
            //切换点击
            clickTab2(index, data) {
                this.currentIndex2 = index;
                this.getData2(index + 1);
            },
            init() {
                $api("spypjg_spypjgxxtj_right01").then((res) => {
                    this.getPie("chart1", res);

                });
                $api("syypjg_spypscjy_right02").then((res) => {
                    this.BarchartsShow("chart2", '日常检查总次数', '检查主体数', '飞行检查覆盖率', res);
                });
                $api("spypjg_jcgzzx_right03").then((res) => {
                    this.BarchartsShow("chart3", '日常检查总次数', '检查主体数', '飞行检查覆盖率', res);
                });
                $api("spypjg_jdjcgk_right04").then((res) => {
                    this.getChart02("chart4", res);
                });
                $api("spypjg_jcgztj_right05").then((res) => {
                    this.getPie("chart5", res);
                });
                $api("spypjg_tjztgs").then((res) => {
                    this.tjList1 = res;
                });
                $api("spypjg_zgyjqs_right07").then((res) => {
                    this.BarchartsShow("chart7", '整改预警数', '历史预警平均值', '同比', res);
                });
                $api("spypjg_ydtszs_right09 ").then((res) => {
                    this.getChart03('chart9', res, '投诉总数', '同比', false)
                });
                $api("spypjg_adqwdts_right10 ").then((res) => {
                    this.getChart03('chart10', res, '投诉信息数量', '投诉信息数量对比', false)
                });
                this.getData(1)
                this.getData1(1)
                this.getData2(1)
            },
            getData(index) {
                $api("spypjg_xkzdqyj_right06", { type: index }).then((res) => {
                    this.getChart03('chart6', res, '预警总数', '同比', true)
                });
            },
            getData1(index) {
                $api("spypjg_jkzdqyj_right08", { type: index }).then((res) => {
                    if (index == 1) {
                        console.log(index)
                        this.getChart03('chart8', res, '预警总数', '同比', true)
                    } else if (index == 2) {
                        console.log(index, 1111)
                        let result = [];
                        res.forEach((item) => {
                            result.push({
                                name: item.name,
                                value: item.value1
                            })
                        })
                        this.getPie("chart8", result);
                    }

                });
            },
            getData2(index) {
                $api("spypjg_aspypbtsqysl_right11", { type: index }).then((res) => {
                    this.getChart04('chart11', res)
                });
            },
            //绘制饼图
            getPie(id, echartsData) {
                echarts.init(document.getElementById(id)).dispose();
                let myEc = echarts.init(document.getElementById(id));
                let option = {
                    tooltip: {
                        trigger: "item",
                        formatter: "{b} : {c} ({d}%)",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "30",
                        },
                    },
                    series: [
                        {
                            type: "pie",
                            radius: "70%",
                            center: ["50%", "50%"],
                            label: {
                                show: true,
                                formatter: "{b}\n{d}%",
                                textStyle: {
                                    fontSize: 25,
                                    color: "#fff",
                                },
                            },
                            data: echartsData,
                            itemStyle: {
                                borderRadius: 5,
                            },
                        },
                    ],
                };

                myEc.setOption(option);
                myEc.getZr().on("mousemove", (param) => {
                    myEc.getZr().setCursorStyle("default");
                });
            },
            //两柱加折线图
            BarchartsShow(id, name1, name2, name3, data) {
                const myChartsState = echarts.init(document.getElementById(id));
                var fontColor = "#30eee9";
                let x = data.map((item) => {
                    return item.name;
                });
                let y1 = data.map((item) => {
                    return item.value1;
                });
                let y2 = data.map((item) => {
                    return item.value2;
                });
                let y3 = data.map((item) => {
                    return item.value3;
                });
                let option = {
                    tooltip: {
                        trigger: "item",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "30",
                        },
                    },

                    grid: {
                        left: "8%",
                        top: "25%",
                        right: "8%",
                        bottom: "14%",
                    },
                    legend: {
                        textStyle: {
                            color: "#fff",
                            fontSize: 28,
                        },
                    },
                    xAxis: {
                        data: x,
                        axisLine: {
                            show: true, //隐藏X轴轴线
                            lineStyle: {
                                color: "#aaa",
                                width: 1,
                            },
                        },
                        axisTick: {
                            show: true, //隐藏X轴刻度
                            alignWithLabel: true,
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: "#fff", //X轴文字颜色
                                fontSize: 28,
                            },
                            interval: 0,
                            rotate: 30,
                        },
                    },
                    yAxis: [
                        {
                            type: "value",
                            name: "单位:次",
                            nameTextStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    width: 1,
                                    color: "#3d5269",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                        },
                        {
                            type: "value",
                            name: "单位:%",
                            nameTextStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                            position: "right",
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: "#fff",
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                show: true,
                                formatter: "{value} ", //右侧Y轴文字显示
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: name1,
                            type: "bar",
                            barWidth: 30,
                            color: "#5087EC",
                            itemStyle: {
                                normal: {
                                    color: "#5087EC",
                                },
                            },
                            data: y1,
                        },
                        {
                            name: name2,
                            type: "bar",
                            barWidth: 30,
                            color: "#68bbc4",
                            itemStyle: {
                                normal: {
                                    color: "#68bbc4",
                                },
                            },
                            data: y2,
                        },
                        {
                            name: name3,
                            type: "line",
                            yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                            showAllSymbol: true, //显示所有图形。
                            // symbol: "circle", //标记的图形为实心圆
                            symbolSize: 10, //标记的大小
                            itemStyle: {
                                normal: {
                                    color: "#26D9FF",
                                    lineStyle: {
                                        color: "#26D9FF",
                                        width: 4,
                                    },
                                },
                            },
                            data: y3,
                        },
                    ],
                };
                myChartsState.setOption(option);
                tools.loopShowTooltip(myChartsState, option, {
                    loopSeries: true,
                }); //轮播
            },
            //绘制柱图
            getChart02(id, echartsData) {
                const myChartsRun = echarts.init(document.getElementById(id));
                let option = {
                    tooltip: {
                        trigger: "item",
                        backgroundColor: "rgba(50,50,50,0.7)",
                        formatter: "{b} </br>{a} {c}",
                        borderColor: "rgba(50,50,50,0.7)",
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    grid: {
                        left: "12%",
                        right: "4%",
                        bottom: "10%",
                        top: "16%",
                        containLabel: true,
                    },
                    legend: {
                        itemWidth: 16,
                        itemHeight: 16,
                        textStyle: {
                            color: "#fff",
                            fontSize: "24",
                        },
                    },
                    xAxis: {
                        type: "category",
                        data: echartsData.map((item) => item.name),
                        offset: 10,
                        axisLabel: {
                            interval: 0, //强制全部显示
                            // rotate: 15,
                            textStyle: {
                                fontSize: 25,
                                color: "#fff",
                            },
                        },
                    },
                    yAxis: {
                        type: "value",

                        splitLine: {
                            show: false,
                        },
                        axisLabel: {
                            textStyle: {
                                fontSize: 30,
                                color: "#fff",
                            },
                        },
                    },
                    series: [
                        {
                            name: "检查总次数",
                            barMaxWidth: 45,
                            data: echartsData.map((item) => item.value),
                            type: "bar",
                            label: {
                                show: true,
                                textStyle: {
                                    color: "#5087ec",
                                    fontSize: 30,
                                },
                                position: "outside",
                            },
                        },
                    ],
                };
                myChartsRun.setOption(option);
                tools.loopShowTooltip(myChartsRun, option, {
                    loopSeries: true,
                }); //轮播
            },
            //绘制柱图+折线
            getChart03(id, data, name1, name2, isVisible) {
                echarts.init(document.getElementById(id)).dispose();
                const myChartsDivine = echarts.init(document.getElementById(id));
                let x = data.map((item) => {
                    return item.name;
                });
                let y = data.map((item) => {
                    return item.value1;
                });
                let y1 = data.map((item) => {
                    return item.value2;
                });
                let option = {
                    tooltip: {
                        trigger: "item",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "30",
                        },
                    },

                    grid: {
                        left: "12%",
                        top: "18%",
                        right: "12%",
                        bottom: "15%",
                    },
                    legend: {
                        textStyle: {
                            color: "#fff",
                            fontSize: 28,
                        },
                    },
                    xAxis: {
                        data: x,
                        axisLine: {
                            show: true, //隐藏X轴轴线
                            lineStyle: {
                                color: "#aaa",
                                width: 1,
                            },
                        },
                        axisTick: {
                            show: true, //隐藏X轴刻度
                            alignWithLabel: true,
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: "#fff", //X轴文字颜色
                                fontSize: 22,
                            },
                            interval: 1,
                            // rotate: 15,
                        },
                    },
                    yAxis: [
                        {
                            type: "value",
                            name: "单位:次",
                            nameTextStyle: {
                                color: "#fff",
                                fontSize: 22,
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    width: 1,
                                    color: "#3d5269",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                        },
                        {
                            type: "value",
                            name: "单位:%",
                            nameTextStyle: {
                                color: "#fff",
                                fontSize: 22,
                            },
                            position: "right",
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: "#fff",
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                show: true,
                                formatter: "{value} ", //右侧Y轴文字显示
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: name1,
                            type: "bar",
                            barWidth: 25,
                            color: "#5087EC",
                            label: {
                                show: isVisible,
                                textStyle: {
                                    color: "#5087ec",
                                    fontSize: 30,
                                },
                                position: "outside",
                            },
                            itemStyle: {
                                normal: {
                                    color: "#5087EC",
                                },
                            },
                            data: y,
                        },

                        {
                            name: name2,
                            type: "line",
                            yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                            showAllSymbol: true, //显示所有图形。
                            // symbol: "circle", //标记的图形为实心圆
                            symbolSize: 10, //标记的大小
                            label: {
                                show: isVisible,
                                textStyle: {
                                    color: "#5087ec",
                                    fontSize: 30,
                                },
                                position: "outside",
                            },
                            itemStyle: {
                                normal: {
                                    color: "#26D9FF",
                                    lineStyle: {
                                        color: "#26D9FF",
                                        width: 4,
                                    },
                                },
                            },
                            data: y1,
                        },
                    ],
                };

                myChartsDivine.setOption(option);
                tools.loopShowTooltip(myChartsDivine, option, {
                    loopSeries: true,
                }); //轮播
            },
            //绘制进度柱图
            getChart04(id, chartData) {
                const myEc = echarts.init(document.getElementById(id));
                let yData = chartData.map((item) => {
                    return item.name;
                });
                let value = chartData.map((item) => {
                    return item.value1;
                });
                let option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 24,
                        },
                    },
                    grid: {
                        top: "10%",
                        bottom: "10%",
                        left: "45%",
                    },
                    xAxis: {
                        type: "value",

                        splitLine: {
                            show: false,
                        },
                        axisLabel: {
                            fontSize: 22,
                            color: "#ffff",
                            interval: 0,
                        },
                    },
                    yAxis: {
                        type: "category",
                        data: yData,
                        axisLabel: {
                            fontSize: 24,
                            color: "#ffff",
                            interval: 0,
                        },
                    },
                    series: [
                        {
                            name: "数据",
                            type: "bar",
                            data: value,
                            label: {
                                normal: {
                                    show: true,
                                    position: "right",
                                    formatter: "{c}",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 20,
                                    },
                                },
                            },
                        },
                    ],
                };
                myEc.setOption(option);
            },
        },
        //项目生命周期
        mounted() {
            this.init();
        },
    });
</script>

</html>