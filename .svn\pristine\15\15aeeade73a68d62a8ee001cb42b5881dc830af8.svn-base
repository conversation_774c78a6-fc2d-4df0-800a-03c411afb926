<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>安全生产事故月度态势分析</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <link rel="stylesheet" href="../css/shgl-csaq-aqscsgndtsfx-left.css" />
    <link rel="stylesheet" href="../css/shgl-csaq-aqscsgndtsfx-right.css" />
  </head>

  <body>
    <div id="shgl-csaq-aqscsgndtsfx-right">
      <div class="content">
        <div class="title topndTitlt">
          <nav style="padding: 20px 0 0 0">
            <s-header-title2
              title="安全生产巡查展示"
              htype="2"
            ></s-header-title2>
          </nav>
          <nav style="padding: 20px 0 0 0">
            <s-header-title2
              title="安全生产执法展示"
              htype="2"
            ></s-header-title2>
          </nav>
        </div>
        <div class="sjfxBox">
          <div>
            <div class="sjBox">
              <div class="sgkgTitle">
                <span class="cf"></span>
                <div>巡查次数</div>
              </div>
            </div>
            <div>
              <div v-for="(item ,index) in xccsData" class="xccsTitle">
                <span>{{item.name}}</span>
                <div class="NumTitle">
                  <h1>{{item.value}}</h1>
                  <span>{{item.dw}}</span>
                </div>
              </div>
            </div>
          </div>

          <div>
            <div class="sjBox">
              <div class="sgkgTitle">
                <span class="cf"></span>
                <div>发现问题</div>
              </div>
            </div>
            <div>
              <div>
                <div class="qyTitle">
                  <div>{{jrfxList.name}}</div>
                  <div class="topTitle">
                    +{{jrfxList.value}}{{jrfxList.dw}}↑
                  </div>
                </div>
                <div class="ttNum">
                  <h1>{{jrfxList.value2}}</h1>
                  <span>{{jrfxList.dw1}}</span>
                </div>
              </div>

              <div id="barEcharts001" style="width: 400px; height: 90px"></div>
            </div>
            <div>
              <div>
                <div class="qyTitle">
                  <div>{{ljfxList.name}}</div>
                  <div class="topTitle">
                    +{{ljfxList.value}}{{ljfxList.dw}}↑
                  </div>
                </div>
                <div class="ttNum">
                  <h1>{{ljfxList.value2}}</h1>
                  <span>{{ljfxList.dw1}}</span>
                </div>
              </div>

              <div id="barEcharts002" style="width: 400px; height: 90px"></div>
            </div>
          </div>
          <div>
            <div class="sjBox">
              <div class="sgkgTitle">
                <span class="cf"></span>
                <div>整改情况</div>
              </div>
            </div>
            <div>
              <div class="zgqkBox" v-for="item in zgqkData">
                <img :src="item.img" alt="" />
                <div class="zgTitle">
                  <div class="yzgTitle">{{item.name}}</div>
                  <div>{{item.value}}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="rightBox">
            <div class="sjBox">
              <div class="sgkgTitle">
                <span class="cf"></span>
                <div>巡查次数</div>
              </div>
            </div>
            <div>
              <div class="zgqkBox" v-for="item in xccsList">
                <img :src="item.img" alt="" />
                <div class="zgTitle">
                  <div class="yzgTitle">{{item.name}}</div>
                  <div>{{item.value}}</div>
                </div>
              </div>
            </div>
          </div>
          <div>
            <div class="sjBox">
              <div class="sgkgTitle">
                <span class="cf"></span>
                <div>检查类型</div>
              </div>
            </div>
            <div class="jclxBox">
              <div class="zgqkBox" v-for="item in jclxData">
                <img :src="item.img" alt="" />
                <div class="zgTitle">
                  <div class="yzgTitle">{{item.name}}</div>
                  <div>{{item.value}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="title">
          <nav style="padding: 20px 0 20px 0">
            <s-header-title2
              style="width: 100%"
              title="高危企业排行"
              htype="1"
            ></s-header-title2>
          </nav>
        </div>
        <div class="sjfxBox">
          <div>
            <div class="sjBox">
              <div class="sgkgTitle">
                <span class="cf"></span>
                <div>企业排名Top20</div>
              </div>
            </div>
            <div class="tabTitle">
              <div
                v-for="(item ,index) in qypm"
                @click="changeEcharts(index)"
                class="titleSG tabTitle-qy"
                :class="{active:isActive===index}"
              >
                {{item}}
              </div>
            </div>
            <div class="bgBox">
              <div class="lineTitle" v-for="item in qypmData">
                <div class="numBG">{{item.num}}</div>
                <div>{{item.name}}</div>
              </div>
            </div>
          </div>
          <div>
            <div class="tabTitle barTitle">
              <div
                v-for="(item ,index) in qypm"
                @click="changeEchartsOne(index)"
                class="titleSG tabTitle-qy"
                :class="{active:isActiveOne===index}"
              >
                {{item}}
              </div>
            </div>
            <div id="barEcharts003" style="width: 560px; height: 550px"></div>
          </div>
          <div>
            <div class="tabTitle barTitle">
              <div
                v-for="(item ,index) in qypm"
                @click="changeEchartsTwo(index)"
                class="titleSG tabTitle-qy"
                :class="{active:isActiveTwo===index}"
              >
                {{item}}
              </div>
            </div>
            <div class="jclxBox last-Box">
              <div class="zgqkBox lastBox" v-for="item in kshData">
                <img :src="item.img" alt="" />
                <div class="zgTitle">
                  <div class="yzgTitle">{{item.name}}</div>
                  <div>{{item.value}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  var vm = new Vue({
    el: "#shgl-csaq-aqscsgndtsfx-right",
    data: {
      xccsData: [],
      jrfxList: [],
      ljfxList: [],
      zgqkData: [],
      xccsList: [],
      jclxData: [],
      qypmData: [],
      kshData: [],
      qypm: ["全市高危企业", "分行业高危企业"],
      isActive: 0,
      isActiveOne: 0,
      isActiveTwo: 0,
    },
    mounted() {
      this.initFun();
    },
    methods: {
      initFun() {
        $api("shgl_aqscsgydtsfx-right001").then((res) => {
          this.xccsData = res;
        });
        $api("shgl_aqscsgydtsfx-right002").then((res) => {
          this.getEcharts01("barEcharts001", res);
        });
        $api("shgl_aqscsgydtsfx-right004").then((res) => {
          this.jrfxList = res[0];
          this.ljfxList = res[1];
        });
        $api("shgl_aqscsgydtsfx-right003").then((res) => {
          this.getEcharts02("barEcharts002", res);
        });
        $api("shgl_aqscsgydtsfx-right005").then((res) => {
          this.zgqkData = res;
        });
        $api("shgl_aqscsgydtsfx-right006").then((res) => {
          this.xccsList = res;
        });
        $api("shgl_aqscsgydtsfx-right007").then((res) => {
          this.jclxData = res;
        });
        $api("shgl_aqscsgydtsfx-right008", { type: "全市高危企业" }).then(
          (res) => {
            this.qypmData = res;
          }
        );
        $api("shgl_aqscsgydtsfx-right009", { type: "全市高危企业" }).then(
          (res) => {
            this.getEcharts03("barEcharts003", res);
          }
        );
        $api("shgl_aqscsgydtsfx-right010", { type: "全市高危企业" }).then(
          (res) => {
            this.kshData = res;
          }
        );
      },
      getEcharts01(dom, echartData) {
        let echarts2 = echarts.init(document.getElementById(dom));
        var baifenbi = echartData.map((item) => {
          return item.value;
        });
        var grayBar = echartData.map((item) => {
          return item.value1;
        });
        var city = echartData.map((item) => {
          return item.time;
        });
        let option = {
          color: ["#4CC970"],
          grid: {
            // left: "15%",
            // right: "15%",
            // bottom: "50%",
            top: "90px",
            containLabel: true,
          },
          xAxis: [
            {
              show: false,
            },
            {
              show: false,
            },
          ],
          yAxis: {
            type: "category",
            axisLabel: {
              show: true, //让Y轴数据不显示
              interval: 0,
              color: "#3AC0FC",
              fontSize: 12,
              padding: [5, 5, 5, 5],
            },
            itemStyle: {},
            axisTick: {
              show: false, //隐藏Y轴刻度
            },
            axisLine: {
              show: false, //隐藏Y轴线段
            },

            data: city,
          },
          series: [
            //背景色--------------------我是分割线君------------------------------//
            {
              show: true,
              type: "bar",
              barGap: "-100%",
              barWidth: "15px", //统计条宽度
              itemStyle: {
                normal: {
                  barBorderRadius: 5,
                  color: "rgba(0,0,0,0.2)",
                },
              },
              z: 1,
              data: grayBar,
            },
            //蓝条--------------------我是分割线君------------------------------//
            {
              show: true,
              type: "bar",
              barGap: "-100%",
              barWidth: "15px", //统计条宽度
              max: 1,
              itemStyle: {
                normal: {
                  barBorderRadius: 5,
                  color: "#4bced0",
                },
              },

              labelLine: {
                show: false,
              },
              z: 2,
              data: baifenbi,
            },
          ],
        };
        echarts2.setOption(option);
      },
      getEcharts02(dom, echartData) {
        let echarts2 = echarts.init(document.getElementById(dom));
        var baifenbi = echartData.map((item) => {
          return item.value;
        });
        var grayBar = echartData.map((item) => {
          return item.value1;
        });
        var city = echartData.map((item) => {
          return item.time;
        });
        let option = {
          color: ["#4CC970"],
          grid: {
            // left: "15%",
            // right: "15%",
            // bottom: "50%",
            top: "90px",
            containLabel: true,
          },
          xAxis: [
            {
              show: false,
            },
            {
              show: false,
            },
          ],
          yAxis: {
            type: "category",
            axisLabel: {
              show: true, //让Y轴数据不显示
              interval: 0,
              color: "#3AC0FC",
              fontSize: 12,
              padding: [5, 5, 5, 5],
            },
            itemStyle: {},
            axisTick: {
              show: false, //隐藏Y轴刻度
            },
            axisLine: {
              show: false, //隐藏Y轴线段
            },

            data: city,
          },
          series: [
            //背景色--------------------我是分割线君------------------------------//
            {
              show: true,
              type: "bar",
              barGap: "-100%",
              barWidth: "15px", //统计条宽度
              itemStyle: {
                normal: {
                  barBorderRadius: 5,
                  color: "rgba(0,0,0,0.2)",
                },
              },
              z: 1,
              data: grayBar,
            },
            //蓝条--------------------我是分割线君------------------------------//
            {
              show: true,
              type: "bar",
              barGap: "-100%",
              barWidth: "15px", //统计条宽度
              max: 1,
              itemStyle: {
                normal: {
                  barBorderRadius: 5,
                  color: "#4bced0",
                },
              },

              labelLine: {
                show: false,
              },
              z: 2,
              data: baifenbi,
            },
          ],
        };
        echarts2.setOption(option);
      },
      changeEcharts(index) {
        this.isActive = index;
        if (this.isActive == 0) {
          $api("shgl_aqscsgydtsfx-right008", { type: "全市高危企业" }).then(
            (res) => {
              this.qypmData = res;
            }
          );
        } else if (this.isActive == 1) {
          $api("shgl_aqscsgydtsfx-right008", { type: "分行业高危企业" }).then(
            (res) => {
              this.qypmData = res;
            }
          );
        }
      },
      changeEchartsOne(index) {
        this.isActiveOne = index;
        if (this.isActiveOne == 0) {
          $api("shgl_aqscsgydtsfx-right009", { type: "全市高危企业" }).then(
            (res) => {
              this.getEcharts03("barEcharts003", res);
            }
          );
        } else if (this.isActiveOne == 1) {
          $api("shgl_aqscsgydtsfx-right009", { type: "分行业高危企业" }).then(
            (res) => {
              this.getEcharts03("barEcharts003", res);
            }
          );
        }
      },
      changeEchartsTwo(index) {
        console.log("changeEchartsTwo");
        this.isActiveTwo = index;
        if (this.isActiveTwo == 0) {
          $api("shgl_aqscsgydtsfx-right010", { type: "全市高危企业" }).then(
            (res) => {
              this.kshData = res;
            }
          );
        } else if (this.isActiveTwo == 1) {
          $api("shgl_aqscsgydtsfx-right010", { type: "分行业高危企业" }).then(
            (res) => {
              this.kshData = res;
            }
          );
        }
      },
      getEcharts03(dom, echartData) {
        let echarts2 = echarts.init(document.getElementById(dom));
        let xData = echartData.map((item) => {
          return item.name;
        });
        let yData = echartData.map((item) => {
          return item.value;
        });
        let yData1 = echartData.map((item) => {
          return item.value1;
        });
        let yData2 = echartData.map((item) => {
          return item.value2;
        });
        let option = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
          },
          legend: {
            data: ["A级", "B级", "C级"],
            icon: "square",
            textStyle: {
              fontSize: 30,
              color: "#157f85",
            },
          },
          grid: {
            left: "3%",
            // right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          xAxis: {
            type: "value",
            min: 0,
            max: 1000,
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                fontSize: 30,
                color: "#14869d",
              },
            },
          },
          yAxis: {
            type: "category",
            data: xData,
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLine: {
              lineStyle: {},
            },
            axisLabel: {
              textStyle: {
                fontSize: 30,
                color: "#14869d",
              },
            },
          },
          series: [
            {
              name: "A级",
              type: "bar",
              stack: "总量",
              data: yData,
            },
            {
              name: "B级",
              type: "bar",
              stack: "总量",
              data: yData1,
            },
            {
              name: "C级",
              type: "bar",
              stack: "总量",
              data: yData2,
            },
          ],
        };
        echarts2.setOption(option);
      },
    },
  });
</script>
