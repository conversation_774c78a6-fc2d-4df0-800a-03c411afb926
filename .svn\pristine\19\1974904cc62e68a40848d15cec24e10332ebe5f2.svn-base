/*
 * @Author: CK
 * @email: <EMAIL>
 * @Date: 2022-08-01 17:53:16
 * @LastEditTime: 2022-08-03 16:35:45
 * @FilePath: \jinhua\js\work\removeCreateLine.js
 * @Description: 删除创建的线段
 */

import { lineVariable } from '../globalVariable/mapFor2D.js';

function removeCreateLine (data) {
    if (data && data.id) {
        if (Array.isArray(data.id)) {
            const length = data.id.length;
            for (let i = 0; i < length; i++) {
                if (egis.getLayer(data.id[i])){
                    egis.removeLayer(data.id[i]);
                    egis.removeSource(data.id[i]);
                    lineVariable.createLineLayerId = lineVariable.createLineLayerId.filter(item => item != data.id[i]);
                }
            }
        } else {
            if (egis.getLayer(data.id)){
                egis.removeLayer(data.id);
                egis.removeSource(data.id);
                lineVariable.createLineLayerId = lineVariable.createLineLayerId.filter(item => item != data.id);
            }
        }
    } else {
        const length = lineVariable.createLineLayerId.length;
        if (length > 0) {
            for (let i = 0; i < length; i++) {
                if (egis.getLayer(lineVariable.createLineLayerId[i])){
                    egis.removeLayer(lineVariable.createLineLayerId[i]);
                    egis.removeSource(lineVariable.createLineLayerId[i]);
                }
            }
            lineVariable.createLineLayerId = [];
        }
    }
}

export default removeCreateLine
