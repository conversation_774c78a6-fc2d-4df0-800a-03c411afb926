<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title></title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css">
    <link rel="stylesheet" href="/static/citybrain/ggfw/css/common-dialog.css">
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script> <!-- 轮播toolTip -->
</head>

<body>
    <div id="app" class="container" style="width: 1515px;min-height: 560px;" v-cloak>
        <div class="head">
            <span>就业保障资金投入</span>
            <div class="img" @click="closeDialog" style="cursor: pointer;"></div>
        </div>
        <div class="content">
            <div class="spajqd">
                <div class="table1" style="height: 358px">
                    <div class="th">
                        <div class="th_td" style="flex: 0.36" v-for="(item,index) in theadListMonth" :key="index">
                            {{item}}
                        </div>
                    </div>
                    <div class="tbody" style="height: calc(100% - 75px);" id="box0" @mouseover="mouseenterEvent()"
                        @mouseleave="mouseleaveEvent()">
                        <div class="tr" :style="i%2 == 0?'height: 120px;':'height: 60px;'"
                            v-for="(item ,i) in tableList1" :key="i">
                            <div class="tr_td" style="flex: 0.36">{{item.a}}</div>
                            <div class="tr_td" style="flex: 0.36">{{item.b}}</div>
                            <div class="tr_td" style="flex: 0.36">{{item.c}}</div>
                            <div class="tr_td" style="flex: 0.36">{{item.d}}</div>
                            <div class="tr_td" style="flex: 0.36">{{item.e}}</div>
                            <div class="tr_td" style="flex: 0.36">{{item.f}}</div>
                            <div class="tr_td" style="flex: 0.36">{{item.g}}</div>
                            <div class="tr_td" style="flex: 0.36">{{item.h}}</div>
                            <div class="tr_td" style="flex: 0.36">{{item.i}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
<script type="module">

    new Vue({
        el: '#app',
        data: {
            theadListMonth: ['年份','婺城区', '武义县', '兰溪市', '金东区', '浦江县',
                '永康市',
                '东阳市',
                '磐安县',
            ],
            tableList1: [],//表格数据
        },
        methods: {
            mouseenterEvent() {
                clearInterval(this.time)
            },
            mouseleaveEvent() {
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },
            autoScroll() {
                this.dom = document.getElementById('box0')
                // this.scpDom = document.getElementsByClassName('text')
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },
            //关闭弹窗
            closeDialog() {
                top.commonObj.funCloseIframe({
                    name: 'tjfx-table-dialog'
                })
            },
            //数据初始化
            init() {
                $api("/ggfw_rsfw_tjfx08").then((res)=>{
                    this.tableList1 = res;
                })
            }
        },
        //项目生命周期
        mounted() {
            this.init();
            this.autoScroll();
        }


    })


</script>

</html>