<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>领域1-中间</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
</head>
<style>
    [v-cloak] {
        display: none;
    }

    html,
    body,
    ul,
    p {
        padding: 0;
        margin: 0;
        list-style: none;
    }

    .container {
        width: 1700px;
        height: 1930px;
        background-color: #0a2443;
        padding: 30px;
        box-sizing: border-box;
    }

    .header-title2[data-v-4d0d1712] {
        width: 100% !important;
    }

    .tabs {
        width: 100%;
        height: 50px;
        line-height: 45px;
        display: flex;
        justify-content: space-evenly;
        margin: 10px 0;
        padding: 0 50px;
        box-sizing: border-box;
    }

    .tab_item {
        width: 40%;
        font-size: 30px;
        color: #fff;
        text-align: center;
        cursor: pointer;
    }

    .tab_active {
        color: #5a8feb;
        border-bottom: 1px #5a8feb solid;
    }

    .xxhxm {
        width: 100%;
        height: 750px;
    }

    .jjtsxx {
        width: 100%;
        height: 960px;
    }

    .jjtsxx-top {
        display: flex;
        justify-content: space-evenly;
        margin: 20px 0;
    }

    .item {
        width: 15%;
        height: 130px;
        background-color: #0087ec;
        padding: 10px 0;
        box-sizing: border-box;
    }

    .item>p {
        text-align: center;
        font-size: 30px;
        color: white;
        margin: 10px 0;
    }

    .s-font-22 {
        font-size: 22px !important;
    }

    .part {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;
    }

    .part1 {
        height: 680px;
    }

    .part2 {
        height: 720px;
    }

    .part-item {
        width: 50%;
        height: 50%;
    }

    .part .text[data-v-4d0d1712] {
        font-size: 30px !important;
    }

    .part .header-title2[data-v-4d0d1712] {
        height: 60px !important;
    }

    .el-progress-circle {
        width: 200px !important;
        height: 200px !important;
    }

    .el-progress__text {
        font-size: 30px !important;
        color: #fff;
    }

    .part-item-part .el-progress-circle {
        width: 150px !important;
        height: 150px !important;
    }

    .part-item-part .el-progress__text {
        font-size: 30px !important;
        color: #fff;
    }

    .btn {
        width: 100%;
        height: 30px;
        /* background-color: #5EB0F5; */
        color: #fff;
        font-size: 28px;
        text-align: center;
        line-height: 30px;
    }
    .btn1 {
        width: 100px;
        height: 40px;
        background-color: #5EB0F5;
        color: #fff;
        font-size: 28px;
        text-align: center;
        line-height: 40px;
        cursor: pointer;
    }
</style>

<body>
    <div id="app" class="container" v-cloak>
        <nav>
            <s-header-title-2 title="信息化项目审批汇集展示" htype="1"></s-header-title-2>
        </nav>
        <div class="xxhxm">
            <div class="tabs">
                <div class="tab_item" v-for="(item,index) in tabList1" :key="index" @click="changTab1(index)"
                    :class="tabIndex1==index?'tab_active':''">
                    {{item}}
                </div>
            </div>
            <div class="btn">办件总量: 12.09万&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                节约填表时间量: 30%&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                办证时间减少量: 45%&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                减少跑窗口次数: 3次</div>
            <div class="part part1" v-show="tabIndex1==0">
                <div id="chart01" style="width: 820px; height: 340px"></div>
                <div id="chart02" style="width: 820px; height: 340px"></div>
                <div id="chart03" style="width: 1640px; height: 300px"></div>
            </div>
            <div class="part part1" v-show="tabIndex1==1">
                <div id="chart04" style="width: 820px; height: 500px"></div>
                <div id="chart05" style="width: 820px; height: 500px"></div>
            </div>
            <div class="part part1" v-show="tabIndex1==2">
                <div id="chart06" style="width: 1640px; height: 320px"></div>
                <div id="chart07" style="width: 1640px; height: 320px"></div>
            </div>
        </div>
        <nav>
            <s-header-title-2 title="经济态势信息汇集展示" htype="1"></s-header-title-2>
        </nav>
        <div class="jjtsxx">
            <div class="jjtsxx-top">
                <div class="item item1" v-for="(item,index) in itemList" :key="index">
                    <p>{{item.value + item.unit}}</p>
                    <p class="s-font-22">{{item.name}}</p>
                </div>
            </div>
            <div class="tabs">
                <div class="tab_item" v-for="(item,index) in tabList2" :key="index" @click="changTab2(index)"
                    :class="tabIndex2==index?'tab_active':''">
                    {{item}}
                </div>
            </div>
            <div style="display: flex;justify-content:right;margin-top:-55px;margin-bottom:20px">
                <div class="btn1" >地图</div>
            </div>
            <div class="part part2" v-show="tabIndex2==0">
                <div class="part-item">
                    <nav>
                        <s-header-title-2 title="投资促进" htype="0"></s-header-title-2>
                    </nav>
                    <div id="chart08" style="width: 820px; height: 300px"></div>
                </div>
                <div class="part-item">
                    <nav>
                        <s-header-title-2 title="财源监管指标分析" htype="0"></s-header-title-2>
                    </nav>
                    <div id="chart09" style="width: 820px; height: 300px"></div>
                </div>
              
                <div class="part-item">
                    <nav>
                        <s-header-title-2 title="内资利用情况" htype="0"></s-header-title-2>
                    </nav>
                    <div id="chart10" style="width: 820px; height: 300px"></div>
                </div>
                <div class="part-item">
                    <nav>
                        <s-header-title-2 title="到位内资完成率" htype="0"></s-header-title-2>
                    </nav>
                    <el-progress type="circle" :percentage="81" style="margin: 20px 320px"></el-progress>
                    <p class="s-font-30 s-c-white" style="text-align: center">到位内资完成百分比</p>
                </div>
            </div>
            <div class="part part2" v-show="tabIndex2==1">
                <div class="part-item">
                    <nav>
                        <s-header-title-2 title="财政本年支出趋势" htype="0"></s-header-title-2>
                    </nav>
                    <div id="chart12" style="width: 820px; height: 300px"></div>
                </div>
                <div class="part-item">
                    <nav>
                        <s-header-title-2 title="重点支出" htype="0"></s-header-title-2>
                    </nav>
                    <div style="display:flex">
                        <div class="part-item-part" >
                            <el-progress type="circle" :percentage="61" style="margin: 20px 40px"></el-progress>
                            <p class="s-font-30 s-c-white" style="text-align: center">民生支出</p>
                        </div>
                        <div class="part-item-part" >
                            <el-progress type="circle" :percentage="63" style="margin: 20px 60px"></el-progress>
                            <p class="s-font-30 s-c-white" style="text-align: center">功能分类支出占比</p>
                        </div>
                        <div class="part-item-part" >
                            <el-progress type="circle" :percentage="61" style="margin: 20px 40px"></el-progress>
                            <p class="s-font-30 s-c-white" style="text-align: center">经济分类支出占比</p>
                        </div>
                    </div>
                </div>
                <div class="part-item">
                    <nav>
                        <s-header-title-2 title="支出结构" htype="0"></s-header-title-2>
                    </nav>
                    <div id="chart14" style="width: 820px; height: 300px"></div>
                </div>
                <div class="part-item">
                    <nav>
                        <s-header-title-2 title="政府采购支出" htype="0"></s-header-title-2>
                    </nav>
                    <div class="s-font-30 s-c-white" style="text-align: center">
                        {{title.name}} {{title.value}} {{title.unit}}
                    </div>
                    <div style="display: flex; justify-content: space-evenly; flex-wrap: wrap; margin-top: 50px">
                        <div class="part-item-part" v-for="(item,index) in partList">
                            <el-progress type="circle" :percentage="Number(item.value)"></el-progress>
                            <p class="s-font-30 s-c-white" style="text-align: center">{{item.name}}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>

<script type="module">
    new Vue({
        el: "#app",
        data: {
            tabIndex1: 0,
            tabList1: ["智能审批信息展示", "工程项目并联审批展示", "先照后证展示"],
            tabIndex2: 0,
            tabList2: ["投资促进", "财源监管指标分析"],

            itemList: [],
            partList: [],
            title: {
                name: "",
                value: "",
                unit: "",
            },
        },
        //项目生命周期
        mounted() {
            this.init();
        },

        methods: {
            init() {
                // $api("ldst_shgl_ly1", { type: 16 }).then((res) => {
                //     this.itemList = res;
                // });
                // $api("ldst_shgl_ly1", { type: 17 }).then((res) => {
                //     this.barEchart01("chart01", res, "20%");
                // });
                // $api("ldst_shgl_ly1", { type: 18 }).then((res) => {
                //     this.pieEchart01("chart02", res, "0%");
                // });
                // $api("ldst_shgl_ly1", { type: 19 }).then((res) => {
                //     this.barEchart02("chart03", res);
                // });
                // $api("ldst_shgl_ly1", { type: 20 }).then((res) => {
                //     this.barEchart01("chart04", res, "42%");
                // });
                // $api("ldst_shgl_ly1", { type: 21 }).then((res) => {
                //     this.pieEchart01("chart05", res, "45%");
                // });
                // $api("ldst_shgl_ly1", { type: 22 }).then((res) => {
                //     this.barEchart03("chart06", res);
                // });
                // $api("ldst_shgl_ly1", { type: 23 }).then((res) => {
                //     this.barEchart03("chart07", res);
                // });
                //
                // $api("ldst_shgl_ly1", { type: 24 }).then((res) => {
                //     this.barEchart03("chart08", res);
                // });
                // $api("ldst_shgl_ly1", { type: 25 }).then((res) => {
                //     this.pieEchart01("chart09", res, "45%");
                // });
                // $api("ldst_shgl_ly1", { type: 26 }).then((res) => {
                //     this.barEchart02("chart10", res);
                // });
                //
                // $api("ldst_shgl_ly1", { type: 27 }).then((res) => {
                //     this.lineEchart02("chart12", res);
                // });
                // $api("ldst_shgl_ly1", { type: 28 }).then((res) => {});
                // $api("ldst_shgl_ly1", { type: 29 }).then((res) => {
                //     this.pieEchart01("chart14", res, "0%");
                // });
                // $api("ldst_shgl_ly1", { type: 30 }).then((res) => {
                //     this.title = res[0];
                //     this.partList = res.slice(1, 4);
                // });

                $get("/3840/shgl/ly1/ly1_16").then((res) => {
                    this.itemList = res;
                });
                $get("/3840/shgl/ly1/ly1_17").then((res) => {
                    this.barEchart01("chart01", res, "20%");
                });
                $get("/3840/shgl/ly1/ly1_18").then((res) => {
                    this.pieEchart01("chart02", res, "0%");
                });
                $get("/3840/shgl/ly1/ly1_19").then((res) => {
                    this.barEchart02("chart03", res);
                });
                $get("/3840/shgl/ly1/ly1_20").then((res) => {
                    this.barEchart01("chart04", res, "42%");
                });
                $get("/3840/shgl/ly1/ly1_21").then((res) => {
                    this.pieEchart01("chart05", res, "45%");
                });
                $get("/3840/shgl/ly1/ly1_22").then((res) => {
                    this.barEchart03("chart06", res);
                });
                $get("/3840/shgl/ly1/ly1_23").then((res) => {
                    this.barEchart03("chart07", res);
                });

                $get("/3840/shgl/ly1/ly1_24").then((res) => {
                    this.barEchart031("chart08", res);
                });
                $get("/3840/shgl/ly1/ly1_25").then((res) => {
                    this.pieEchart01("chart09", res, "45%");
                });
                $get("/3840/shgl/ly1/ly1_26").then((res) => {
                    this.barEchart02("chart10", res);
                });

                $get("/3840/shgl/ly1/ly1_27").then((res) => {
                    this.lineEchart02("chart12", res);
                });
                $get("/3840/shgl/ly1/ly1_28").then((res) => { });
                $get("/3840/shgl/ly1/ly1_29").then((res) => {
                    this.pieEchart01("chart14", res, "0%");
                });
                $get("/3840/shgl/ly1/ly1_30").then((res) => {
                    this.title = res[0];
                    this.partList = res.slice(1, 4);
                });
            },

            changTab1(index) {
                this.tabIndex1 = index;
            },

            changTab2(index) {
                this.tabIndex2 = index;
            },

            barEchart01(id, barData, left) {
                const myEc = echarts.init(document.getElementById(id));
                barData.sort(function (a, b) {
                    return a.value - b.value;
                });
                let yData = barData.map((item) => {
                    return item.name;
                });
                let value = barData.map((item) => {
                    return item.value;
                });
                let option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 24,
                        },
                    },
                    grid: {
                        top: "10%",
                        bottom: "10%",
                        left: left,
                    },
                    xAxis: {
                        type: "value",

                        splitLine: {
                            show: false,
                        },
                        axisLabel: {
                            fontSize: 24,
                            color: "#ffff",
                        },
                    },
                    yAxis: {
                        type: "category",
                        data: yData,
                        axisLabel: {
                            fontSize: 24,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            name: "数据",
                            type: "bar",
                            data: value,
                            label: {
                                normal: {
                                    show: true,
                                    position: "right",
                                    formatter: "{c}",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 24,
                                    },
                                },
                            },
                        },
                    ],
                };
                myEc.setOption(option);
            },

            barEchart02(id, barData) {
                const myEc = echarts.init(document.getElementById(id));
                let x = barData.map((item) => {
                    return item.name;
                });
                let y = barData.map((item) => {
                    return item.value;
                });
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "30",
                        },
                    },
                    grid: {
                        left: "10%",
                        top: "18%",
                        right: "10%",
                        bottom: "12%",
                    },
                    // legend: {
                    //   x: "center",
                    //   y: "10",
                    //   itemWidth: 40,
                    //   itemHeight: 20,
                    //   textStyle: {
                    //       color: "#fff",
                    //       fontSize:"24",
                    //   },
                    // },
                    xAxis: {
                        data: x,
                        axisLine: {
                            show: true, //隐藏X轴轴线
                            lineStyle: {
                                color: "#aaa",
                                width: 1,
                            },
                        },
                        axisTick: {
                            show: true, //隐藏X轴刻度
                            alignWithLabel: true,
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: "#fff", //X轴文字颜色
                                fontSize: 28,
                            },
                            // interval: 0,
                            // rotate: 30,
                        },
                    },
                    yAxis: [
                        {
                            type: "value",
                            // name: "单位:个",
                            nameTextStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    width: 1,
                                    color: "#3d5269",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "数据",
                            type: "bar",
                            barWidth: 60,
                            yAxisIndex: 0,
                            color: "#5087EC",
                            itemStyle: {
                                normal: {
                                    color: "#5087EC",
                                },
                            },
                            data: y,
                        },
                    ],
                };
                myEc.setOption(option);
            },

            barEchart03(id, barData) {
                const myEc = echarts.init(document.getElementById(id));
                let x = barData.map((item) => {
                    return item.name;
                });
                let y1 = barData.map((item) => {
                    return item.value;
                });
                let y2 = barData.map((item) => {
                    return item.value1;
                });
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "30",
                        },
                    },
                    grid: {
                        left: "10%",
                        top: "18%",
                        right: "10%",
                        bottom: "10%",
                    },
                    legend: {
                        x: "center",
                        y: "20",
                        itemWidth: 40,
                        itemHeight: 20,
                        textStyle: {
                            color: "#fff",
                            fontSize: "24",
                        },
                    },
                    xAxis: {
                        data: x,
                        axisLine: {
                            show: true, //隐藏X轴轴线
                            lineStyle: {
                                color: "#aaa",
                                width: 1,
                            },
                        },
                        axisTick: {
                            show: true, //隐藏X轴刻度
                            alignWithLabel: true,
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: "#fff", //X轴文字颜色
                                fontSize: 28,
                            },
                            interval: 0,
                            // rotate: 30,
                        },
                    },
                    yAxis: [
                        {
                            type: "value",
                            // name: "单位:万人",
                            nameTextStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    width: 1,
                                    color: "#3d5269",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "先照",
                            type: "bar",
                            barWidth: 30,
                            yAxisIndex: 0,
                            color: "#5087EC",
                            itemStyle: {
                                normal: {
                                    color: "#5087EC",
                                },
                            },
                            data: y1,
                        },
                        {
                            name: "后证",
                            type: "bar",
                            barWidth: 30,
                            yAxisIndex: 0,
                            color: "#25d1ca",
                            itemStyle: {
                                normal: {
                                    color: "#25d1ca",
                                },
                            },
                            data: y2,
                        },
                    ],
                };
                myEc.setOption(option);
            },
            barEchart031(id, barData) {
                const myEc = echarts.init(document.getElementById(id));
                let x = barData.map((item) => {
                    return item.name;
                });
                let y1 = barData.map((item) => {
                    return item.value;
                });
                let y2 = barData.map((item) => {
                    return item.value1;
                });
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "30",
                        },
                    },
                    grid: {
                        left: "10%",
                        top: "18%",
                        right: "10%",
                        bottom: "10%",
                    },
                    legend: {
                        x: "center",
                        y: "20",
                        itemWidth: 40,
                        itemHeight: 20,
                        textStyle: {
                            color: "#fff",
                            fontSize: "24",
                        },
                    },
                    xAxis: {
                        data: x,
                        axisLine: {
                            show: true, //隐藏X轴轴线
                            lineStyle: {
                                color: "#aaa",
                                width: 1,
                            },
                        },
                        axisTick: {
                            show: true, //隐藏X轴刻度
                            alignWithLabel: true,
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                color: "#fff", //X轴文字颜色
                                fontSize: 28,
                            },
                            interval: 0,
                            // rotate: 30,
                        },
                    },
                    yAxis: [
                        {
                            type: "value",
                            // name: "单位:万人",
                            nameTextStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    width: 1,
                                    color: "#3d5269",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "引进数量",
                            type: "bar",
                            barWidth: 30,
                            yAxisIndex: 0,
                            color: "#5087EC",
                            itemStyle: {
                                normal: {
                                    color: "#5087EC",
                                },
                            },
                            data: y1,
                        },
                        {
                            name: "投资金额",
                            type: "bar",
                            barWidth: 30,
                            yAxisIndex: 0,
                            color: "#25d1ca",
                            itemStyle: {
                                normal: {
                                    color: "#25d1ca",
                                },
                            },
                            data: y2,
                        },
                    ],
                };
                myEc.setOption(option);
            },

            lineEchart01(id, lineData) {
                const myEc = echarts.init(document.getElementById(id));
                let datax = [],
                    datay = [];
                lineData.map((ele) => {
                    datax.push(ele.name);
                    datay.push(ele.value);
                });
                let option = {
                    legend: {
                        show: true,
                        x: "center",
                        y: "10",
                        itemWidth: 40,
                        itemHeight: 20,
                        textStyle: {
                            color: "#fff",
                            fontSize: "28",
                        },
                    },
                    grid: {
                        left: "3%",
                        right: "4%",
                        bottom: "8%",
                        containLabel: true,
                    },
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        axisPointer: {
                            lineStyle: {
                                color: "rgba(11, 208, 241, 1)",
                                type: "slider",
                            },
                        },
                        textStyle: {
                            color: "rgba(212, 232, 254, 1)",
                            fontSize: 28,
                        },
                    },
                    xAxis: [
                        {
                            type: "category",
                            offset: 20,
                            axisLine: {
                                //坐标轴轴线相关设置。数学上的x轴
                                show: true,
                                lineStyle: {
                                    color: "rgba(108, 166, 219, 0.3)",
                                },
                            },
                            axisLabel: {
                                //坐标轴刻度标签的相关设置
                                // rotate: -30,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: "#192a44",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            data: datax,
                        },
                    ],
                    yAxis: [
                        {
                            name: "单位:",
                            min: (value) => {
                                return parseInt(value.min - 1);
                            },
                            nameTextStyle: {
                                fontSize: 24,
                                color: "#D6E7F9",
                                padding: [0, 20, 10, 0],
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 28,
                                    color: "#D6E7F9",
                                },
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: "#233653",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "系统访问数",
                            type: "line",
                            symbolSize: 10,
                            itemStyle: {
                                normal: {
                                    // color: "#3A84FF",
                                    lineStyle: {
                                        // color: "#1b759c",
                                        width: 2,
                                    },
                                },
                            },
                            data: datay,
                        },
                    ],
                };
                myEc.setOption(option);
            },

            lineEchart02(id, lineData) {
                const myEc = echarts.init(document.getElementById(id));
                let datax = [],
                    datay1 = [],
                    datay2 = [];
                lineData.map((ele) => {
                    datax.push(ele.name);
                    datay1.push(ele.value);
                    datay2.push(ele.value1);
                });
                let option = {
                    legend: {
                        show: true,
                        x: "center",
                        y: "10",
                        itemWidth: 40,
                        itemHeight: 20,
                        textStyle: {
                            color: "#fff",
                            fontSize: "28",
                        },
                    },
                    grid: {
                        left: "3%",
                        right: "4%",
                        bottom: "8%",
                        containLabel: true,
                    },
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        axisPointer: {
                            lineStyle: {
                                color: "rgba(11, 208, 241, 1)",
                                type: "slider",
                            },
                        },
                        textStyle: {
                            color: "rgba(212, 232, 254, 1)",
                            fontSize: 28,
                        },
                    },
                    xAxis: [
                        {
                            type: "category",
                            offset: 20,
                            axisLine: {
                                //坐标轴轴线相关设置。数学上的x轴
                                show: true,
                                lineStyle: {
                                    color: "rgba(108, 166, 219, 0.3)",
                                },
                            },
                            axisLabel: {
                                //坐标轴刻度标签的相关设置
                                // rotate: -30,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: "#192a44",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            data: datax,
                        },
                    ],
                    yAxis: [
                        {
                            name: "单位:",
                            min: (value) => {
                                return parseInt(value.min - 1);
                            },
                            nameTextStyle: {
                                fontSize: 24,
                                color: "#D6E7F9",
                                padding: [0, 20, 10, 0],
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 28,
                                    color: "#D6E7F9",
                                },
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: "#233653",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "支出金额",
                            type: "line",
                            symbolSize: 10,
                            itemStyle: {
                                normal: {
                                    // color: "#3A84FF",
                                    lineStyle: {
                                        // color: "#1b759c",
                                        width: 2,
                                    },
                                },
                            },
                            data: datay1,
                        },
                        {
                            name: "重点支出金额",
                            type: "line",
                            symbolSize: 10,
                            itemStyle: {
                                normal: {
                                    // color: "#3A84FF",
                                    lineStyle: {
                                        // color: "#1b759c",
                                        width: 2,
                                    },
                                },
                            },
                            data: datay2,
                        },
                    ],
                };
                myEc.setOption(option);
            },

            pieEchart01(id, pieData, radius_start) {
                const myEc = echarts.init(document.getElementById(id));
                var fontColor = "#30eee9";
                let option = {
                    grid: {
                        top: "10%",
                        bottom: "0%",
                        containLabel: true,
                    },
                    tooltip: {
                        trigger: "item",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "24",
                        },
                    },
                    color: ["#EE752F", "#5087EC", "#68BBC4", "#58A55C", "#F2BD42"],
                    legend: {
                        show: false,
                    },
                    series: [
                        {
                            name: "",
                            type: "pie",
                            radius: [radius_start, "70%"],
                            center: ["50%", "50%"],
                            data: pieData,
                            itemStyle: {
                                color: "#fff",
                                emphasis: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: "rgba(0, 0, 0, 0.5)",
                                },
                            },
                            itemStyle: {
                                normal: {
                                    label: {
                                        show: true,
                                        color: "#fff",
                                        fontSize: "24",
                                        formatter: "{b}:\n{d}%",
                                    },
                                },
                                labelLine: { show: true },
                            },
                        },
                    ],
                };
                myEc.setOption(option);
            },
        },
    });
</script>

</html>