<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>安全隐患区域分布统计-中-详情</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <!-- 轮播toolTip -->
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <style>
      .topTitle {
        width: 100% !important;
      }
    </style>
    <style>
      #app {
        background-color: #0c1a38;
        padding: 16px;
        width: 1460px;
        height: 600px;
      }

      /*表格*/
      .table {
        width: 100%;
        height: 440px;
        padding: 20px 0 0 0;
        box-sizing: border-box;
      }

      .table-th {
        display: flex;
        display: -webkit-flex;
        width: 100%;
        height: 60px;
        margin-bottom: 10px;
      }

      .th {
        flex: 0.5;
        text-align: center;
        font-size: 32px;
        line-height: 60px;
        color: #77b3f1;
        margin-left: 0 !important;
        background-color: #035b86;
      }

      .th:nth-child(1) {
        flex: 0.6;
      }

      .table-tr {
        width: 100%;
        height: calc(100% - 80px);
        overflow-y: auto;
      }

      .tr {
        margin: 5px 0;
        display: flex;
        display: -webkit-flex;
        width: 100%;
        padding: 10px 0;
        background-color: #0f2b4d;
        margin-bottom: 0px !important;
      }

      .td {
        flex: 0.5;
        text-align: center;
        word-break: break-all;
        font-size: 32px;
        color: #d6e7f9;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .tr .td:nth-child(1) {
        flex: 0.6;
      }

      .tr .td:nth-child(1) {
        background: linear-gradient(
          to bottom,
          #ccf4ff,
          #ffffff,
          #00baf8,
          #ffffff
        );
        -webkit-background-clip: text;
        color: transparent;
      }

      .td > div > div {
        background-size: 100% 100%;
      }

      ::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 4px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
        /* scrollbar-arrow-color: red; */
      }

      ::-webkit-scrollbar-thumb {
        border-radius: 4px;
        background: #20aeff;
        height: 8px;
      }
      .top-close {
        position: absolute;
        right: 0;
        top: 0;
        width: 80px;
        height: 80px;
        cursor: pointer;
        background-image: url('/static/images/common/components/close-1.png');
        background-size: 100% 100%;
      }
      .topItem {
        border: 1px solid #20aeff;
        padding: 16px;
        font-size: 26px;
        color: #ffffff;
        display: flex;
        flex-direction: column;
        width: 280px;
      }
      .chart {
        width: 1400px;
        height: 400px;
        margin-top: 50px;
      }
    </style>
  </head>

  <body>
    <div id="app">
      <el-row :gutter="20" type="flex" justify="space-around">
        <el-col :span="1" :offset="0">
          <div class="topItem">
            <div>事故名称：泄露</div>
            <div>发生时间：2022-08-01</div>
          </div>
        </el-col>
        <el-col :span="1" :offset="0">
          <div class="topItem">
            <div>事故名称：泄露</div>
            <div>发生时间：2022-08-01</div>
          </div>
        </el-col>
        <el-col :span="6" :offset="0">
          <div class="topItem">
            <div>事故名称：泄露</div>
            <div>发生时间：2022-08-01</div>
          </div>
        </el-col>
      </el-row>
      <div id="line" class="chart"></div>
    </div>

    <script>
      new Vue({
        el: '#app',
        data() {
          return {}
        },
        mounted() {
          this.getLineChart()
        },
        methods: {
          getLineChart() {
            const myCharts = echarts.init(document.getElementById('line'))
            const option = {
              tooltip: {
                trigger: 'axis',
                axisPointer: {
                  // 坐标轴指示器，坐标轴触发有效
                  type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
                },
                borderWidth: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                textStyle: {
                  color: 'white',
                  fontSize: '28',
                },
              },
              xAxis: {
                type: 'category',
                data: ['10分钟', '20分钟', '30分钟', '40分钟', '50分钟'],
                axisLabel: {
                  textStyle: {
                    color: '#fff',
                    fontSize: 22,
                  },
                },
              },
              yAxis: [
                {
                  name: '件',
                  type: 'value',
                  splitLine: {
                    show: false,
                  },
                  axisLabel: {
                    textStyle: {
                      color: '#fff',
                      fontSize: 22,
                    },
                  },
                  nameTextStyle: {
                    fontSize: 24,
                    color: '#fff',
                    // padding: [0, 20, 10, 0],
                  },
                },
              ],
              series: [
                {
                  data: [100, 140, 224, 100, 135],
                  type: 'line',
                },
              ],
            }
            myCharts.setOption(option)
            myCharts.getZr().on('mousemove', (param) => {
              myCharts.getZr().setCursorStyle('default')
            })
          },
        },
      })
    </script>
  </body>
</html>
