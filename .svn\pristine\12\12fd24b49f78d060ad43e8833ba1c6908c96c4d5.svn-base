<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>弹窗</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <!-- <link rel="stylesheet" href="/static/citybrain/ggfw/css/jyfw-diaolog.css" /> -->
    <!-- <link rel="stylesheet" href="../css/city.css" />
  <link rel="stylesheet" href="../css/common.css" /> -->
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <!-- 轮播toolTip -->
  </head>
  <style>
    .container {
      width: 800px;
      margin: 0 auto;
      height: 360px;
      background-color: #031827;
      box-shadow: -3px 2px 35px 0px #000000;
    }

    .container .head {
      width: 100%;
      height: 80px;
      line-height: 100px;
      background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%), linear-gradient(#ffffff, #ffffff);
      background-blend-mode: normal, normal;
      padding: 10px 50px;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
    }

    .head span {
      font-size: 40px !important;
      font-weight: 500;
      color: #fff;
      font-weight: bold;
    }

    .head .img {
      display: inline-block;
      margin: 20px;
      float: right;
      width: 34px;
      height: 34px;
      background-image: url(/static/citybrain/csdn/img/cstz2-middle/close-hover.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
      position: absolute;
      right: 6px;
    }

    .item {
      display: flex;
      align-items: center;
      padding: 16px;
      font-size: 32px;
      color: #00aae2;
    }
  </style>

  <body>
    <div id="app" class="szfz-dialog container">
      <div class="head">
        <span>模型分析结果</span>
        <div class="img" @click="closeDialog"></div>
      </div>
      <div>
        <div class="item">
          <span>预测伤亡人员：</span>
          <span>
            <el-input placeholder="" size="normal" clearable></el-input>
          </span>
        </div>
        <div class="item">
          <span>预测损坏建筑：</span>
          <span>
            <el-input placeholder="" size="normal" clearable></el-input>
          </span>
        </div>
      </div>
      <div style="text-align: center; padding-top: 16px">
        <el-button type="primary" style="font-size: 26px" size="default">保存</el-button>
      </div>
    </div>
  </body>
  <script type="module">
    new Vue({
      el: '#app',
      data: {},
      //项目生命周期
      mounted() {},
      methods: {
        closeDialog() {
          top.commonObj.funCloseIframe({
            name: 'szfz-dialog',
          })
        },
      },
    })
  </script>
</html>
