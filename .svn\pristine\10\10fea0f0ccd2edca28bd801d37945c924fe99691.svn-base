<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <script src="./Vue/vue.js"></script>
    <script src="./jquery/jquery-3.4.1.min.js"></script>
    <script src="./echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="./elementui/css/elementui.css">
    <script src="./elementui/js/elementui.js"></script>
</head>
<body>
<div id="app">
    <div id="title1">
        <div class="titleline1">
            <div style="position:relative;left: 30px;top: 5px;"><img src="./img/logo.png" alt=""></div>
            <div class="titletext" style="width: 300px; height: 70px;text-align: center;line-height: 70px;position: relative;left: 15px;">民生服务</div>
            <div style="position: relative;top: 5px;"><img src="./img/headerimage2.png" style="width: 1160px;min-height: 15px" alt=""></div>
            <div style="z-index: 10; position: relative;left: 20px;top: 5px;right: 40px;" class="time">
                数据截止时间:{{time}}
            </div>
        </div>
        <div class="titleline2">
            <div style="position: relative;left: 30px;bottom: 40px"><img src="./img/headerimage.png" alt=""></div>
        </div>
    </div>

    <div class="relative" style="left: 20px;">
        <div style="display: flex;justify-content: space-evenly;align-items: center;bottom: 40px" class="relative">
            <div class="line" style="align-items: center;position: relative;top: 30px;">
                <div><img src="./img/right.png" alt=""></div>
                <div class="titletext" style="font-size: 40px;text-align: center;width: 120px;">政务</div>
                <div><img src="./img/left.png" alt=""></div>
            </div>

            <div class="line" style="align-items: center;position: relative;top: 30px;">
                <div><img src="./img/right.png" alt=""></div>
                <div class="titletext" style="font-size: 40px;text-align: center;width: 120px;">社保</div>
                <div><img src="./img/left.png" alt=""></div>
            </div>

            <div class="line" style="align-items: center;position: relative;top: 30px;">
                <div><img src="./img/right.png" alt=""></div>
                <div class="titletext" style="font-size: 40px;text-align: center;width: 120px;">就业</div>
                <div><img src="./img/left.png" alt=""></div>
            </div>
        </div>
        <div class="line relative" style="justify-content: space-evenly;align-items: center;bottom: 95px">
            <div class="line relative" style="top: 35px;right: 35px;">
                <div id="img5"><img src="./img/img5.png" alt=""></div>
                <div style="align-items: center;position: relative;left: 40px;">
                    <div class="text1" style="position: relative;top: 25px;">网上可办率</div>
                    <div style="position: relative;bottom: 24px">
                        <div class="number" style="text-align: center;">{{data1}}<span style="font-size: 40px">%</span></div>
                        <div><img src="./img/numberback.png" alt=""></div>
                    </div>
                </div>
            </div>

            <div class="relative" style="right: 35px;">
                <div style="position: relative;left: 80px;top: 50px;">
                    <div class="text2">户籍人口医保参保率</div>
                    <div class="text2" style="position: relative;top: 60px;">全省排名:<span class="number2">{{data2}}</span></div>
                </div>
                <div><img src="./img/Base3.png" alt=""></div>
            </div>

            <div class="relative" style="top:15px;right: 50px;">
                <div class="text2 relative" style="text-align: center;top: 70px;">城镇新增就业人数</div>
                <div class="number2 relative" style="top: 90px;left: 1px;text-align: center">{{data3}}<span class="unit2">万人</span></div>
                <div class="relative" style="bottom: 5px"><img src="./img/Base5.png" alt="" style="width: 400px;"></div>
            </div>
        </div>
        <div class="line relative" style="justify-content: space-evenly;align-items: center;bottom: 165px">
            <div class="line relative" style="top: 35px;right: 35px;">
                <div id="img6"><img src="./img/img6.png" alt=""></div>
                <div style="align-items: center;position: relative;left: 40px;">
                    <div class="text1" style="position: relative;top: 25px;">掌上可办率</div>
                    <div style="position: relative;bottom: 24px">
                        <div class="number" style="text-align: center;">{{data4}}<span style="font-size: 40px">%</span></div>
                        <div><img src="./img/numberback.png" alt=""></div>
                    </div>
                </div>
            </div>

            <div class="relative" style="right: 35px;">
                <div style="position: relative;left: 80px;top: 50px;">
                    <div class="text2">户籍人口医保参保率:</div>
                    <div class="text2" style="position: relative;top: 60px;"><span class="number2">{{data5}}%</span></div>
                </div>
                <div><img src="./img/Base3.png" alt=""></div>
            </div>

            <div class="relative" style="top:15px;right: 50px;">
                <div class="text2 relative" style="text-align: center;top: 70px;">城镇失业人员再就业</div>
                <div class="number2 relative" style="top: 90px;left: 1px;text-align: center">{{data6}}<span class="unit2">万人</span></div>
                <div class="relative" style="bottom: 5px"><img src="./img/Base5.png" alt="" style="width: 400px;"></div>
            </div>
        </div>
        <div class="line relative" style="justify-content: space-evenly;align-items: center;bottom: 225px">
            <div class="line relative" style="top: 35px;right: 35px;">
                <div id="img7"><img src="./img/img7.png" alt=""></div>
                <div style="align-items: center;position: relative;left: 40px;">
                    <div class="text1" style="position: relative;top: 25px;">跑零次可办率</div>
                    <div style="position: relative;bottom: 24px">
                        <div class="number" style="text-align: center;">{{data7}}<span style="font-size: 40px">%</span></div>
                        <div><img src="./img/numberback.png" alt=""></div>
                    </div>
                </div>
            </div>

            <div class="relative" style="right: 35px;">
                <div style="position: relative;left: 80px;top: 50px;">
                    <div class="text2">基本医疗保险参保人数:</div>
                    <div class="text2" style="position: relative;top: 60px;">
                        <span class="number2">{{data8}}</span>
                        <span class="unit">万人</span>
                    </div>
                </div>
                <div><img src="./img/Base3.png" alt=""></div>
            </div>

            <div class="relative" style="top:15px;right: 50px;">
                <div class="text2 relative" style="text-align: center;top: 70px;">城镇登记失业率</div>
                <div class="number2 relative" style="top: 90px;left: 1px;text-align: center">{{data9}}<span style="font-size: 40px">%</span></div>
                <div class="relative" style="bottom: 5px"><img src="./img/Base5.png" alt="" style="width: 400px;"></div>
            </div>
        </div>
    </div>


    <div id="title2">
        <div class="titleline1">
            <div style="position:relative;left: 30px;top: 5px;"><img src="./img/logo.png" alt=""></div>
            <div class="titletext" style="width: 300px; height: 70px;text-align: center;line-height: 70px;position: relative;left: 15px;">生态治理</div>
            <div style="position: relative;top: 5px;"><img src="./img/headerimage2.png" style="width: 1160px;min-height: 15px" alt=""></div>
            <div style="z-index: 10; position: relative;top: 5px;left: 20px;right: 40px;" class="time">
                数据截止时间:{{time}}
            </div>
        </div>
        <div class="titleline2">
            <div style="position: relative;left: 30px;bottom: 40px"><img src="./img/headerimage.png" alt=""></div>
        </div>
    </div>


    <div class="relative" style="bottom: 240px">
        <!--大气环境 水环境标题-->
        <div class="line relative" style="justify-content: space-evenly;bottom: 15px">
            <div class="line" style="align-items: center;position: relative;">
                <div><img src="./img/right.png" alt=""></div>
                <div class="titletext relative" style="font-size: 40px;text-align: center;bottom: 5px">大气环境</div>
                <div><img src="./img/left.png" alt=""></div>
            </div>

            <div class="line" style="align-items: center;position: relative;left: 150px;">
                <div><img src="./img/right.png" alt=""></div>
                <div class="titletext relative" style="font-size: 40px;text-align: center;bottom: 5px">水环境</div>
                <div><img src="./img/left.png" alt=""></div>
            </div>
        </div>

        <div class="line" style="justify-content: space-evenly">

            <!--左侧三块-->
            <div class="relative" style="bottom: 150px;right: 50px;">
                <div class="relative" style="left: 55px;">
                    <div class="number2 relative numbertitle">今日AQI指数</div>
                    <div class="text3 relative" style="top: 155px;left: 50px;">{{data10}}  </div>
                    <div><img src="./img/img8.png" alt=""></div>
                </div>


                <div class="relative" style="bottom: 140px;left: 55px;">
                    <div class="number2 relative numbertitle">今日PM2.5浓度</div>
                    <div class="text3 relative" style="top: 155px;left: 16px;">{{data11}}ug/m³</div>
                    <div><img src="./img/img9.png" alt=""></div>
                </div>

                <div class="relative" style="bottom: 280px;left: 55px;">
                    <div class="number2 relative numbertitle">空气质量优良天数</div>
                    <div class="text3 relative" style="top: 155px;left: 50px;">{{data12}}天</div>
                    <div><img src="./img/img10.png" alt=""></div>
                </div>
            </div>


            <div class="relative" style="bottom: 210px">
                <div class="text4" style="text-align: center">1-8月优良天数</div>
                <div class="line">
                    <div class="text4">2021年</div>
                    <progress value="10"  max="300"  id="progress1"></progress>
                    <div class="text5">{{data13}} <span style="font-size: 40px">天</span> </div>
                </div>
                <div class="line">
                    <div class="text4">2020年</div>
                    <progress value="50" max="300" id="progress2"></progress>
                    <div class="text5">{{data14}} <span style="font-size: 40px">天</span> </div>
                </div>

                <div class="text4" style="position: relative;top: 30px;text-align: center">优良率同比上升{{data15}}</div>
                <div class="text4" style="margin-top: 80px;text-align: center">1-8月PM2.5年均浓度</div>
                <div class="line" style="justify-content: space-between">
                    <div class="text4">2021年</div>
                    <div class="text5">{{data16}} <span style="font-size: 40px">ug/m³</span> </div>
                </div>
                <div class="line" style="justify-content: space-between">
                    <div class="text4">2021年</div>
                    <div class="text5">{{data16}} <span style="font-size: 40px">ug/m³</span> </div>
                </div>
            </div>


            <div class="relative" style="bottom: 260px">
                <div class="line relative" style="align-items: center; top: 34px;">
                    <div class="relative" style="top: 10px;"><img src="./img/point.png" alt=""></div>
                    <div class="text4 relative">水质优良断面比例 <span class="text5" style="top: 5px;left: 1px;">{{data18}}</span> %</div>
                </div>
                <div class="relative" style="width: 700px;height: 550px;top: 70px;" id="box6"></div>
            </div>
        </div>

    </div>

</div>
</body>
<script>




    const app = new Vue({
        el:'#app',
        data:{
            time:"2021年6月",
            data1:99.4, //网上可办率
            data2:1, //医保参保率全省排名
            data3:20.6,   //城镇新增就业人数
            data4:95.8,   //掌上可办理率
            data5:99.85, //户籍人口医保参保率
            data6:0.97,  //城镇失业人员再就业
            data7:97.9,  //跑零次可办率
            data8:527.22,  //基本医疗保险参保人数
            data9:1.77,   //城镇登记失业率
            data10:"76(良)",  //AQI指数
            data11:20,  //PM2.5浓度
            data12:235,  //空气质量优良天数
            data13:235,  //2021年1-8月优良天数
            data14:226,  //2020年1-8月优良天数
            data15:"3.98%",  //优良率同比上升值
            data16:25,  //1-8月PM2.5年均浓度 2021
            data17:25,  //1-8月PM2.5年均浓度 2020
            data18:100, //水质优良断面比例
            xdata:['2015','2016','2017','2018','2019','2020'],
            ydata:[100,100,100,100,100,100]
        },
        methods:{
            opacity(){
                let opa = {
                    "-webkit-animation": "opacity 1s",
                    "animation": "opacity 1s",
                    "animation-fill-mode":"forwards",
                    "-webkit-animation-timing-function":"linear"
                }
                $("#img5").css(opa);
            },
        },
        mounted(){
            const charts = echarts.init(document.getElementById("box6"))
            document.getElementById('progress1').value = this.data13;
            document.getElementById('progress2').value = this.data14;


            let option = {
                title:{
                    text:"国/省考核断面达标率",
                    left:200,
                    textStyle:{
                        color:"#ffffff",
                        fontSize:30,
                        fontWeight:500
                    }
                },
                    tooltip: {
                        trigger: 'axis',
                        triggerOn:'mousemove',
                        borderWidth:0,
                        backgroundColor: '#000000',
                        textStyle:{
                            color:'white',
                            fontSize:'35'
                        },
                        formatter:function (arg) {
                            console.log(arg);
                            return arg[0].axisValue + '的达标率为 '+arg[0].value + "%"
                        }
                    },
                xAxis: {
                    type:"category",
                    data:this.xdata,
                    axisLine:{
                        lineStyle:{
                            color:'rgb(255,255,255)'
                        }
                    },
                    axisLabel:{
                      fontSize:27
                    }
                },
                yAxis:{
                    name:"单位: %",
                    nameTextStyle:{
                      fontSize:27,
                    },
                    type:"value",
                    axisLine:{
                        lineStyle:{
                            color:'rgb(255,255,255)'
                        }
                    },
                    axisLabel:{
                        fontSize:27
                    }
                },
                series:[
                    {
                        data:this.ydata,
                        type:"line",
                        left:20,
                        symbolSize:5,
                        itemStyle: {
                            normal: {
                                color: '#40a4de',
                                lineStyle: {
                                    color: '#40a4de',
                                    width:5
                                }
                            }
                        }
                    }
                ]
            }
            charts.setOption(option)
        }
    })
</script>
<style>
    body{
        width: 2078px;
        height: 1849px;
        background: url("./img/bg.png") no-repeat;
    }

    #title1{
        position: relative;
        left: 25px;
        top: 30px;
    }

    #title2{
        position: relative;
        left: 25px;
        bottom: 200px;
    }

    #title3{
        position: relative;
        bottom: 100px;
    }

    #title4{
        position: relative;
        left: 25px;
        bottom: 160px;
    }


    .titleline1{
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-top: 20px;
    }

    .titletext{
        width: 217px;
        height: 51px;
        font-family: SourceHanSansCN-Medium;
        font-size: 54px;
        font-weight: 800;
        font-stretch: normal;
        line-height: 38px;
        letter-spacing: 3px;
        background: linear-gradient(to bottom, #f0ffff,#74b4f4,#83b8ff);
        -webkit-background-clip: text;
        color: transparent;
        white-space: nowrap;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .time{
        width: 349px;
        height: 30px;
        font-family: FZZZHONGJW--GB1-0;
        font-size: 30px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 30px;
        letter-spacing: 0px;
        background: linear-gradient(to bottom,#83b8ff,#74b4f4,#f0ffff);
        -webkit-background-clip: text;
        color: transparent;
    }

    .line{
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
    }

    #img5,#img6,#img7{
        -webkit-animation: opacity 3s infinite;
        animation: opacity 3s infinite;
    }

    @-webkit-keyframes opacity {
        30% {opacity: 0;}
    }

    .number{
        font-family: BebasNeueRegular;
        font-size: 72px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 58px;
        letter-spacing: 30px;
        position: relative;
        left: 5px;
        top: 85px;
        background: linear-gradient(to bottom,#ffd8a1,#fffcf3,#ffb637,#ffdb9b,#ffffff);
        -webkit-background-clip: text;
        color: transparent;
    }

    .number2{
        height: 80px;
        font-family: BebasNeueRegular;
        font-size: 60px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 4px;
        background: linear-gradient(to bottom,#ffd8a1,#fffcf3,#ffb637,#ffdb9b,#ffffff);
        -webkit-background-clip: text;
        color: transparent;
        position: relative;
        top: 7px;
        left: 24px;
        line-height: 80px;
    }

    .unit{
        font-size: 40px;
        background: linear-gradient(to bottom,#ffd8a1,#fffcf3,#ffb637,#ffdb9b,#ffffff);
        -webkit-background-clip: text;
        color: transparent;
        position: relative;
        left: 20px;
        top: 2px;
    }

    .unit2{
        font-size: 40px;
        background: linear-gradient(to bottom,#ffd8a1,#fffcf3,#ffb637,#ffdb9b,#ffffff);
        -webkit-background-clip: text;
        color: transparent;
        position: relative;
        left: 5px;
        bottom: 4px;
    }

    .text1{
        font-size: 32px;
        color: #fff;
        font-family: SourceHanSansSC-Regular;
    }

    .text2{
        font-family: SourceHanSansSC-Regular;
        font-size: 40px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 40px;
        letter-spacing: 0px;
        color: #d6e7f9;
        position: relative;
        top: 60px;
    }

    .text3{
        background: linear-gradient(to bottom,#bcaaff,#f4f1ff,#8d6fff,#ffffff);
        -webkit-background-clip: text;
        color: transparent;
        font-size: 60px;
    }


    .text4{
        font-family: SourceHanSansSC-Regular;
        font-size: 40px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 40px;
        letter-spacing: 0px;
        color: #d6e7f9;
        margin-right: 20px;
        margin-left: 10px;
    }


    .text5{
        background: linear-gradient(to bottom,#dcf6ff,#40a4de);
        -webkit-background-clip: text;
        color: transparent;
        font-size: 60px;
        position: relative;
        left: 20px;
        bottom: 5px;
    }

    .relative{
        position: relative;
    }

    .numbertitle{
        letter-spacing: 0px;
        font-size: 25px;
        top: 165px;
        left: 75px;
        font-weight: 800
    }

    progress {
        height: 40px;
        width: 400px;
        background-color: #fff;
        /*bgc 需要被设置，否则背景色无法被显示出来*/
    }

    /* 表示总长度背景色 */
    progress::-webkit-progress-bar {
        background-color: #113348;
    }

    /* 表示已完成进度背景色 */
    progress::-webkit-progress-value {
        background: linear-gradient(to right,#2acfff,#1affcc);
    }
</style>
</html>
