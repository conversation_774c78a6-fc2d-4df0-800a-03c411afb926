<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>弹窗</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <link rel="stylesheet" href="/static/citybrain/ggfw/css/jyfw-diaolog.css" />
    <link rel="stylesheet" href="../css/city.css" />
    <link rel="stylesheet" href="../css/common.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
    <script src="/static/js/jslib/echarts-wordcloud.min.js"></script>
    <!-- 轮播toolTip -->
  </head>

  <style>
    .shgl-dialog {
      padding: 30px;
      background: rgb(0, 49, 111);
    }

    .wraps {
      display: flex;
    }

    .wrap {
      margin-right: 20px;
      color: #fff;
    }

    .wrap-header {
      text-align: center;
      font-size: 30px;
    }

    .wrap-body {
    }

    .wrap-body .part {
      margin: 30px 0;
    }

    .wrap-body .part-title {
      text-align: center;
      font-size: 30px;
    }

    .wrap-body .part-content {
      margin-top: 20px;
    }

    .gfhy-chart {
      background: #fff;
    }

    .gzmcslx-chart {
      background: #fff;
    }

    /*表格*/
    .table {
      width: 100%;
      height: 390px;
      padding: 20px 20px 0;
      box-sizing: border-box;
    }

    .table-th {
      display: flex;
      display: -webkit-flex;
      width: 100%;
      height: 60px;
      margin-bottom: 10px;
    }

    .th {
      text-align: center;
      font-size: 32px;
      line-height: 60px;
      color: #fff;
      margin-left: 0 !important;
      background-color: #035b86;
    }

    .table1 .th:nth-child(1) {
      width: 180px;
    }

    .table1 .th:nth-child(2) {
      width: 360px;
    }

    .table2 .th:nth-child(1) {
      width: 180px;
    }

    .table2 .th:nth-child(2) {
      width: 260px;
    }

    .table2 .th:nth-child(3) {
      width: 260px;
    }

    .table-tr {
      width: 100%;
      height: calc(100% - 80px);
      overflow-y: auto;
    }

    .tr {
      margin: 5px 0;
      display: flex;
      display: -webkit-flex;
      width: 100%;
      padding: 10px 0;
      background-color: #0f2b4d;
      margin-bottom: 0px !important;
    }

    .td {
      text-align: center;
      word-break: break-all;
      font-size: 32px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .table1 .td:nth-child(1) {
      width: 180px;
    }

    .table1 .td:nth-child(2) {
      width: 360px;
    }

    .table2 .td:nth-child(1) {
      width: 180px;
    }

    .table2 .td:nth-child(2) {
      width: 260px;
    }

    .table2 .td:nth-child(3) {
      width: 260px;
    }

    ::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 4px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
      /* scrollbar-arrow-color: red; */
    }

    ::-webkit-scrollbar-thumb {
      border-radius: 4px;
      background: #20aeff;
      height: 8px;
    }

    .top-close {
      width: 150px;
      height: 150px;
      cursor: pointer;
      background-image: url("/static/images/common/components/close-1.png");
      background-size: 100%;
    }
  </style>

  <body>
    <div id="app" class="shgl-dialog">
      <div
        id="opendiv-bmjr-dialog"
        style="position: absolute; right: 0; z-index: 1000"
      >
        <div class="top-close" @click="closeWin()"></div>
      </div>
      <div class="wraps">
        <div class="wrap">
          <div class="wrap-header">安全生产事件行业特征</div>
          <div class="wrap-body">
            <div class="part">
              <div class="part-title">高发行业</div>
              <div class="part-content">
                <div
                  id="gfhy-chart"
                  class="gfhy-chart"
                  style="height: 400px"
                ></div>
              </div>
            </div>
            <div class="part">
              <div class="part-title">高发企业</div>
              <div class="part-content">
                <div class="table table1">
                  <div class="table-th">
                    <div
                      class="th"
                      v-for="(item,index) in gfqyThData"
                      :key="index"
                    >
                      {{item}}
                    </div>
                  </div>
                  <div class="table-tr">
                    <div
                      class="tr"
                      v-for="(item,index) in gfqyTrData"
                      :key="index"
                    >
                      <div class="td">{{item.name}}</div>
                      <div class="td">{{item.value}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="wrap">
          <div class="wrap-header">安全生产事件场所分布特征</div>
          <div class="wrap-body">
            <div class="part">
              <div class="part-title">高发场所</div>
              <div class="part-content">
                <div class="table table2">
                  <div class="table-th">
                    <div
                      class="th"
                      v-for="(item,index) in gfcsThData"
                      :key="index"
                    >
                      {{item}}
                    </div>
                  </div>
                  <div class="table-tr">
                    <div
                      class="tr"
                      v-for="(item,index) in gfcsTrData"
                      :key="index"
                    >
                      <div class="td">{{item.name}}</div>
                      <div class="td">{{item.value}}</div>
                      <div class="td">{{item.value1}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="part">
              <div class="part-title">高致命场所类型</div>
              <div class="part-content">
                <div
                  id="gzmcslx-chart"
                  class="gzmcslx-chart"
                  style="height: 400px"
                ></div>
              </div>
            </div>
          </div>
        </div>
        <div class="wrap">
          <div class="wrap-header">安全生产事件诱发因素贡献率分析</div>
          <div class="wrap-body">
            <div class="leida-chart-wrap">
              <div
                id="leida-chart"
                class="leida-chart"
                style="width: 700px; height: 700px"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script type="module">
    new Vue({
      el: "#app",
      data: {
        gfhyWords: [],
        gfqyThData: ["区域", "企业名称"],
        gfqyTrData: [],
        gfcsThData: ["区域", "场所名称", "伤亡比"],
        gfcsTrData: [],
        gzmcsWords: [],
        leidaData: [],
      },
      methods: {
        closeWin() {
          top.commonObj.funCloseIframe({ name: "shgl-csaq-aqscsg-dialog" });
        },
        // closeDialog() {
        //   top.commonObj.funCloseIframe({
        //     name: "jyfw-dialog",
        //   });
        // },
        //绘制雷达图
        renderLeida() {
          let myEc = echarts.init(document.getElementById("leida-chart"));
          let option = {
            tooltip: {
              //雷达图的tooltip不会超出div，也可以设置position属性，position定位的tooltip 不会随着鼠标移动而位置变化，不友好
              confine: true,
              enterable: true, //鼠标是否可以移动到tooltip区域内
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "28",
              },
            },
            radar: {
              name: {
                textStyle: {
                  color: "#FFFFFF",
                  fontSize: 28,
                },
              },
              shape: "polygon",
              center: ["50%", "50%"],
              radius: "60%",
              // startAngle: 120,
              scale: true,
              axisLine: {
                lineStyle: {
                  color: "rgba(5, 213, 255, .8)",
                },
              },
              splitLine: {
                show: true,
                lineStyle: {
                  width: 1,
                  color: "rgba(5, 213, 255, .8)", // 设置网格的颜色
                },
              },
              indicator: this.leidaData,
              splitArea: {
                show: false,
              },
            },
            grid: {
              //   position: "center",
              bottom: 0,
            },
            polar: {
              center: ["80%", "50%"], // 默认全局居中
              radius: "0%",
            },
            angleAxis: {
              min: 0,
              interval: 5,
              clockwise: false,
              axisTick: {
                show: false,
              },
              axisLabel: {
                show: false,
              },
              axisLine: {
                show: false,
              },
              splitLine: {
                show: false,
              },
            },
            radiusAxis: {
              min: 0,
              interval: 20,
              splitLine: {
                show: false,
              },
            },
            legend: {
              top: 25,
              right: 20,
              icon: "rect",
              itemWidth: 17, // 图例标记的图形宽度。[ default: 25 ]
              itemHeight: 12, // 图例标记的图形高度。[ default: 14 ]
              itemGap: 9, // 图例每项之间的间隔。[ default: 10 ]横向布局时为水平间隔，纵向布局时为纵向间隔。
              textStyle: {
                fontSize: 16,
                color: "#fff",
              },
              data: this.leidaData.map((a) => a.name),
            },
            series: [
              {
                name: "监测指标",
                type: "radar",
                symbol: "circle", // 拐点的样式，还可以取值'rect','angle'等
                symbolSize: 10, // 拐点的大小
                itemStyle: {
                  normal: {
                    color: "#05D5FF",
                  },
                },
                areaStyle: {
                  normal: {
                    color: "#05D5FF",
                    opacity: 0.3,
                  },
                },
                lineStyle: {
                  width: 2,
                  color: "#05D5FF",
                },
                label: {
                  normal: {
                    show: true,
                    formatter: (params) => {
                      return params.value;
                    },
                    color: "#fff",
                  },
                },
                data: [
                  {
                    value: this.leidaData.map((a) => a.value),
                  },
                ],
              },
            ],
          };
          myEc.setOption(option);
        },
        getTextStyle() {
          return {
            color:
              "rgb(" +
              [
                Math.round(Math.random() * 250),
                Math.round(Math.random() * 250),
                Math.round(Math.random() * 250),
              ].join(",") +
              ")",
          };
        },
        renderGfhyChart(dataes) {
          let that = this;
          let myChart = echarts.init(document.getElementById("gfhy-chart"));
          // 高空抛物、占道停车、火警、交通事故、电梯困人、环境污染、噪音扰民、超载超限、乱倒垃圾、占道经营
          let datas = dataes.map((item, index) => {
            item.name = item.num;
            item.value = index == 0 ? 18 : index == 1 ? 22 : 20 + index * 2;
            item.textStyle = that.getTextStyle();
            return item;
          });

          let option = {
            series: [
              {
                type: "wordCloud",
                // 网格大小，各项之间间距
                gridSize: 50,
                // 形状 circle 圆，cardioid  心， diamond 菱形，
                // triangle-forward 、triangle 三角，star五角星
                shape: "star",
                // 字体大小范围
                sizeRange: [80, 10],
                // 文字旋转角度范围
                rotationRange: [0, 0],
                // 旋转步值
                // rotationStep: 90,
                // 自定义图形
                // maskImage: maskImage,
                left: "center",
                top: "center",
                right: null,
                bottom: null,
                // 画布宽
                width: "100%",
                // 画布高
                height: "100%",
                // 是否渲染超出画布的文字
                drawOutOfBound: false,
                data: datas,
              },
            ],
          };
          myChart.setOption(option);
        },
        renderGzmcslxChart(res) {
          let that = this;
          let myChart = echarts.init(document.getElementById("gzmcslx-chart"));
          // 高空抛物、占道停车、火警、交通事故、电梯困人、环境污染、噪音扰民、超载超限、乱倒垃圾、占道经营
          let datas = res.map((item, index) => {
            item.name = item.num;
            item.value = index == 0 ? 18 : index == 1 ? 22 : 20 + index * 2;
            item.textStyle = that.getTextStyle();
            return item;
          });

          let option = {
            series: [
              {
                type: "wordCloud",
                // 网格大小，各项之间间距
                gridSize: 50,
                // 形状 circle 圆，cardioid  心， diamond 菱形，
                // triangle-forward 、triangle 三角，star五角星
                shape: "star",
                // 字体大小范围
                sizeRange: [80, 10],
                // 文字旋转角度范围
                rotationRange: [0, 0],
                // 旋转步值
                // rotationStep: 90,
                // 自定义图形
                // maskImage: maskImage,
                left: "center",
                top: "center",
                right: null,
                bottom: null,
                // 画布宽
                width: "100%",
                // 画布高
                height: "100%",
                // 是否渲染超出画布的文字
                drawOutOfBound: false,
                data: datas,
              },
            ],
          };
          myChart.setOption(option);
        },
      },
      //项目生命周期
      mounted() {
        $api("shgl_csjt_add4", { type: "高发行业" }).then((res) => {
          this.gfhyWords = res;
          this.renderGfhyChart(this.gfhyWords);
        });
        $api("shgl_csjt_add4", { type: "高发企业" }).then((res) => {
          this.gfqyTrData = res;
        });
        $api("shgl_csjt_add4", { type: "高发场所" }).then((res) => {
          this.gfcsTrData = res;
        });
        $api("shgl_csjt_add4", { type: "高致命场所类型" }).then((res) => {
          this.renderGzmcslxChart(res);
        });
        $api("shgl_csjt_add4", { type: "因素" }).then((res) => {
          this.leidaData = res;
          this.renderLeida();
        });
      },
    });
  </script>
</html>
