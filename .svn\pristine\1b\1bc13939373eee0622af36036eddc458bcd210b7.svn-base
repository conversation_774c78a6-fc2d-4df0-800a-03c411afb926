<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <title>城市安全-企业安全生产情况分析-区域分布特征分析</title>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <script src="/Vue/vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <link rel="stylesheet" href="/static/citybrain/shgl/css/shgl-csaq-qyaqscqk-left.css" />
        <style>
            #csaq-1 {
                background-color: #0c1a38;
                padding: 16px;
            }

            /*表格*/
            .table {
                width: 100%;
                height: 400px;
                padding: 20px 0 0 0;
                box-sizing: border-box;
            }

            .table-th {
                display: flex;
                display: -webkit-flex;
                width: 100%;
                height: 60px;
                margin-bottom: 10px;
            }

            .th {
                flex: 0.5;
                text-align: center;
                font-size: 32px;
                line-height: 60px;
                color: #77b3f1;
                margin-left: 0 !important;
                background-color: #035b86;
            }

            .th:nth-child(1) {
                flex: 0.6;
            }

            .table-tr {
                width: 100%;
                height: calc(100% - 80px);
                overflow-y: auto;
            }

            .tr {
                margin: 5px 0;
                display: flex;
                display: -webkit-flex;
                width: 100%;
                padding: 10px 0;
                background-color: #0f2b4d;
                margin-bottom: 0px !important;
            }

            .td {
                flex: 0.5;
                text-align: center;
                word-break: break-all;
                font-size: 32px;
                color: #d6e7f9;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .tr .td:nth-child(1) {
                flex: 0.6;
            }

            .tr .td:nth-child(1) {
                background: linear-gradient(to bottom, #ccf4ff, #ffffff, #00baf8, #ffffff);
                -webkit-background-clip: text;
                color: transparent;
            }

            .td > div > div {
                background-size: 100% 100%;
            }

            ::-webkit-scrollbar {
                /*滚动条整体样式*/
                width: 4px;
                /*高宽分别对应横竖滚动条的尺寸*/
                height: 1px;
                /* scrollbar-arrow-color: red; */
            }

            ::-webkit-scrollbar-thumb {
                border-radius: 4px;
                background: #20aeff;
                height: 8px;
            }
            .top-close {
                position: absolute;
                right: 0;
                top: 0;
                width: 80px;
                height: 80px;
                cursor: pointer;
                background-image: url("/static/images/common/components/close-1.png");
                background-size: 100% 100%;
            }
        </style>
    </head>

    <body>
        <div id="csaq-1" v-cloak>
            <div
                class="top-close"
                onclick="top.commonObj.funCloseIframe({name:'shgl-csaq-qyaqscqk-left-table1-dialog3'});"
            ></div>
            <div class="table">
                <div class="table-th">
                    <div class="th" v-for="(item,index) in thData" :key="index">{{item}}</div>
                </div>
                <div class="table-tr">
                    <div class="tr" v-for="(item,index) in trData" :key="index">
                        <div class="td" :style="i==0?'cursor: pointer':'cursor: default'" v-for="(el,i) in labels">
                            <div>{{item[el]}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            var vm = new Vue({
                el: "#csaq-1",
                data: {
                    thData: [
                        "企业经纬度",
                        "所属行政区划",
                        "企业名称",
                        "企业类型",
                        "企业经营状态",
                        "纳税数据",
                        "营业额",
                    ],
                    labels: ["qyjwd", "xzqh", "qymc", "qylx", "jyzt", "nssj", "yye"],
                    trData: [],
                },
                computed: {},
                mounted() {
                    $api("shgl_zbxx_08").then((res) => {
                        this.trData = res;
                    });
                },
            });
        </script>
    </body>
</html>
