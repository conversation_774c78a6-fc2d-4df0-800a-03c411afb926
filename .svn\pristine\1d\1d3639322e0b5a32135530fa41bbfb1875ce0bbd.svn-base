<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>数字经济指标分析左侧面板</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <link
      rel="stylesheet"
      href="/static/citybrain3840/szhgg/css/szjjzbfx-left.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts-liquidfill.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
    <!-- 轮播toolTip -->
  </head>

  <body>
    <div id="app" class="container" v-cloak>
      <nav>
        <s-header-title-2 htype="1" title="数字贸易发展情况"></s-header-title-2>
      </nav>
      <div class="tj-con">
        <li v-for="(item,index) in szmyData" :key="index">
          <div class="title1">{{item.value}}{{item.unit}}</div>
          <div class="title2">{{item.name}}</div>
        </li>
      </div>
      <div id="szmy-chart1"></div>
      <div id="szmy-chart2"></div>
    </div>
  </body>
  <script type="module">
    new Vue({
      el: '#app',
      data: {
        szmyData: [],
      },
      methods: {
        initIframe() {
          let iframe1 = {
            type: 'openIframe',
            name: 'index-middle-top',
            src: '/static/citybrain3840/szhgg/pages/szzfzbfx/index-middle-top.html',
            width: '930px',
            height: '170px',
            left: '1590px',
            top: '230px',
            zIndex: '10',
          }
          let iframe4 = {
            type: 'openIframe',
            name: 'list',
            src: '/static/citybrain3840/szhgg/commont/list1.html',
            width: '450px',
            height: '125px',
            left: '1090px',
            top: '230px',
            zIndex: '10',
          }
          window.parent.postMessage(JSON.stringify(iframe1), '*')
          window.parent.postMessage(JSON.stringify(iframe4), '*')
        },
        initMap() {
          top.mapUtil.loadRegionLayer({
            layerid: 'szjjzbfx_bk',
            data: [
              { name: '婺城区', color: [78, 107, 221, 1], height: 2800 },
              { name: '开发区', color: [78, 107, 221, 1], height: 2600 },
              { name: '金东区', color: [46, 81, 221, 1], height: 2400 },
              { name: '兰溪市', color: [78, 107, 221, 1], height: 2200 },
              { name: '浦江县', color: [110, 133, 221, 1], height: 2000 },
              { name: '义乌市', color: [110, 133, 221, 1], height: 1800 },
              { name: '东阳市', color: [78, 107, 221, 1], height: 1600 },
              { name: '磐安县', color: [110, 133, 221, 1], height: 1400 },
              { name: '永康市', color: [46, 81, 221, 1], height: 1200 },
              { name: '武义县', color: [110, 133, 221, 1], height: 1000 },
            ],
          })
          top.mapUtil.flyTo({
            x: 120.42947964091805,
            y: 26.653373263516,
            z: 326456.6924844431,
            heading: 350.39657276492284,
            tilt: 38.15583652942755,
          })
          $get('/city.json').then((res) => {
            top.mapUtil.loadTextLayer({
              layerid: 'szjj_3Dtext',
              data: res,
              style: {
                size: 32,
                color: [242, 242, 242, 1],
              },
            })
          })
        },
        init() {
          $api('ldst_szhgg_szjj', { type: 2 }).then((res) => {
            this.szmyData = res
          })
          $api('ldst_szhgg_szjj', { type: 3 }).then((res) => {
            this.BarchartsShow('szmy-chart1', '经济总量', '增长率', res)
          })
          $api('ldst_szhgg_szjj', { type: 4 }).then((res) => {
            this.BarchartsShow1(res)
          })
        },
        //绘制柱图
        BarchartsShow(id, name1, name2, data) {
          const myChartsDivine = echarts.init(document.getElementById(id))
          let x = data.map((item) => {
            return item.name
          })
          let y = data.map((item) => {
            return item.value
          })
          let y1 = data.map((item) => {
            return item.value1
          })
          let option = {
            tooltip: {
              trigger: 'axis',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
              axisPointer: {
                type: 'shadow',
              },
            },

            grid: {
              left: '8%',
              top: '18%',
              right: '8%',
              bottom: '10%',
            },
            legend: {
              textStyle: {
                color: '#fff',
                fontSize: 28,
              },
            },
            xAxis: {
              // data: x,
              data: ['6月','7月','8月','9月','10月','11月','12月','1月','2月','3月'],
              axisLine: {
                show: true, //隐藏X轴轴线
                lineStyle: {
                  color: '#aaa',
                  width: 1,
                },
              },
              axisTick: {
                show: true, //隐藏X轴刻度
                alignWithLabel: true,
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#fff', //X轴文字颜色
                  fontSize: 28,
                },
                interval: 0,
                // rotate: 30,
              },
            },
            yAxis: [
              {
                type: 'value',
                name: '单位:起',
                nameTextStyle: {
                  color: '#fff',
                  fontSize: 28,
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    width: 1,
                    color: '#3d5269',
                  },
                },
                axisTick: {
                  show: false,
                },
                axisLine: {
                  show: false,
                },
                axisLabel: {
                  show: true,
                  textStyle: {
                    color: '#fff',
                    fontSize: 28,
                  },
                },
              },
              {
                type: 'value',
                name: '',
                nameTextStyle: {
                  color: '#fff',
                  fontSize: 28,
                },
                position: 'right',
                splitLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLine: {
                  show: false,
                  lineStyle: {
                    color: '#fff',
                    width: 2,
                  },
                },
                axisLabel: {
                  show: true,
                  formatter: '{value}%', //右侧Y轴文字显示
                  textStyle: {
                    color: '#fff',
                    fontSize: 28,
                  },
                },
              },
            ],
            series: [
              {
                name: name1,
                type: 'bar',
                barWidth: 70,
                color: '#5087EC',
                itemStyle: {
                  normal: {
                    color: '#5087EC',
                  },
                },
                data: y,
              },

              {
                name: name2,
                type: 'line',
                yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                showAllSymbol: true, //显示所有图形。
                // symbol: "circle", //标记的图形为实心圆
                symbolSize: 10, //标记的大小
                itemStyle: {
                  normal: {
                    color: '#26D9FF',
                    lineStyle: {
                      color: '#26D9FF',
                      width: 4,
                    },
                  },
                },
                data: y1,
              },
            ],
          }

          myChartsDivine.setOption(option)
          tools.loopShowTooltip(myChartsDivine, option, {
            loopSeries: true,
          }) //轮播
        },
        //绘制柱图2
        BarchartsShow1(data) {
          const myChartsState = echarts.init(
            document.getElementById('szmy-chart2')
          )
          var fontColor = '#30eee9'
          let x = data.map((item) => {
            return item.name
          })
          let y1 = data.map((item) => {
            return item.value
          })
          let y2 = data.map((item) => {
            return item.value1
          })
          let y3 = data.map((item) => {
            return item.value2
          })
          let option = {
            tooltip: {
              trigger: 'axis',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
              axisPointer: {
                type: 'shadow',
              },
            },

            grid: {
              left: '8%',
              top: '18%',
              right: '8%',
              bottom: '10%',
            },
            legend: {
              textStyle: {
                color: '#fff',
                fontSize: 28,
              },
            },
            xAxis: {
              data: x,
              axisLine: {
                show: true, //隐藏X轴轴线
                lineStyle: {
                  color: '#aaa',
                  width: 1,
                },
              },
              axisTick: {
                show: true, //隐藏X轴刻度
                alignWithLabel: true,
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#fff', //X轴文字颜色
                  fontSize: 28,
                },
                interval: 0,
                // rotate: 30,
              },
            },
            yAxis: [
              {
                type: 'value',
                name: '单位:起',
                nameTextStyle: {
                  color: '#fff',
                  fontSize: 28,
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    width: 1,
                    color: '#3d5269',
                  },
                },
                axisTick: {
                  show: false,
                },
                axisLine: {
                  show: false,
                },
                axisLabel: {
                  show: true,
                  textStyle: {
                    color: '#fff',
                    fontSize: 28,
                  },
                },
              },
              {
                type: 'value',

                position: 'right',
                splitLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLine: {
                  show: false,
                  lineStyle: {
                    color: '#fff',
                    width: 2,
                  },
                },
                axisLabel: {
                  show: true,
                  formatter: '{value}%', //右侧Y轴文字显示
                  textStyle: {
                    color: '#fff',
                    fontSize: 28,
                  },
                },
              },
            ],
            series: [
              {
                name: '电商',
                type: 'bar',
                barWidth: 30,
                color: '#5087EC',
                itemStyle: {
                  normal: {
                    color: '#5087EC',
                  },
                },
                data: y1,
              },
              {
                name: '轻工',
                type: 'bar',
                barWidth: 30,
                color: '#68bbc4',
                itemStyle: {
                  normal: {
                    color: '#68bbc4',
                  },
                },
                data: y2,
              },
              {
                name: '增长率',
                type: 'line',
                yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                showAllSymbol: true, //显示所有图形。
                // symbol: "circle", //标记的图形为实心圆
                symbolSize: 10, //标记的大小
                itemStyle: {
                  normal: {
                    color: '#26D9FF',
                    lineStyle: {
                      color: '#26D9FF',
                      width: 4,
                    },
                  },
                },
                data: y3,
              },
            ],
          }
          myChartsState.setOption(option)
          tools.loopShowTooltip(myChartsState, option, {
            loopSeries: true,
          }) //轮播
        },
      },

      //项目生命周期
      mounted() {
        this.init()
        this.initIframe()
        this.initMap()
      },
    })
  </script>
</html>
