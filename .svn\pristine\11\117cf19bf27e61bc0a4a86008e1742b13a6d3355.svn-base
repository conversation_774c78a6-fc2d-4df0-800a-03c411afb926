<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <script src="../js/vue.js"></script>
    <script src="../js/echarts.min.js"></script>
    <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Sans+SC:100,300,400,500,700,900"> -->
    <link rel="stylesheet" href="../css/dqhj.css" />
    <link rel="stylesheet" href="../css/common.css" />


</head>

<body>
    <div id="dqwr_app">
        <div class="dqwr_app_box">
            <div class="dqwr_header">
                <i class="title_icon_one"></i>
                <h2 class="title">大气环境</h2>
                <div class="title_hr"></div>
                <span>数据截止时间 : 2021年6月</span>
            </div>
            <div class="dqwr_container">
                <div class="dqwr_container_head">
                    <i class="icon_left"></i>
                    <h2>实时空气质量</h2>
                    <i class="icon_right"></i>
                </div>
                <!-- 实时空气质量 -->
                <div class="sskqzl">
                    <div class="left_list">
                        <img src="../img/dqhj/组1062060.png" />
                    </div>
                    <div class="right_canvas">
                        <div class="aqi_opt">
                            <div id="aqi_num" style="height: 320px; width: 350px;position: relative; top:40px"></div>
                            <div>实时AQI值</div>
                        </div>
                        <div class="main_opt">
                            <ul>
                                <li v-for="(item,index) in pmList">
                                    <div class="pm_title"> {{ item.name}}</div>
                                    <div class="pm_yl">
                                        <span style="font-size: 36px;">{{ item.rest}}</span>
                                        <span style="display: inline-block;margin-top: 5px;">{{ item.pmdj}}</span>
                                    </div>
                                    <div>
                                        <progressbar :rest="item.rest" :total="item.total"
                                            :barwidth="(item.rest / item.total) *100 "></progressbar>
                                    </div>
                                </li>
                            </ul>
                            <div class="bottom_title">主要污染物数值</div>
                        </div>

                        <div class="aqi_opt">
                            <div id="wrw_num" style="height: 320px; width: 350px;position: relative; top:40px"></div>
                            <div>今日首要污染物</div>
                        </div>
                    </div>
                </div>
                <!-- 空气质量考核 -->
                <div class="dqwr_container_head">
                    <i class="icon_left"></i>
                    <h2>空气质量考核</h2>
                    <i class="icon_right"></i>
                </div>
                <div class="kqzlkh">
                    <div class="aqi_item">
                        <div class="title">
                            <i class="three_title"></i>
                            <span>AQI(环境空气质量)优良率</span>
                        </div>
                        <div class="aqi_item_price">
                            <div>
                                单位:%
                            </div>
                            <div>
                                <span>------</span>
                                国标&gt;87.5%
                            </div>
                        </div>
                        <div id="kqzl_aqi" style="width: 580px;height:360px "></div>
                        <!-- <div class="compare_model">
                            <div class="aqitooltip">
                                <div>同比</div>
                                <div style="margin-top:-8px;"><i class="aqi_icon"></i>
                             
                                    0.2%</div>
                            </div>
                        </div> -->
                    </div>
                    <div class="aqi_item">
                        <div class="title">
                            <i class="three_title"></i>
                            <span>空气质量优良天数</span>
                        </div>
                        <div class="aqi_item_price">
                            <div>
                                单位 : 天数
                            </div>
                            <div>
                                <span>------</span>
                                国标 &gt;319
                            </div>
                        </div>
                        <div id="kqzl_yzts" style="width: 580px;height:360px "></div>
                        <!-- <div class="compare_model">
                            <div class="aqitooltip">
                                <div>同比</div>
                                <div style="margin-top:-8px;"><i class="aqi_icon"></i>
                                 
                                    0.6%</div>
                            </div>
                        </div> -->
                    </div>
                    <div class="aqi_item">
                        <div class="title">
                            <i class="three_title"></i>
                            <span>PM2.5年均值</span>
                        </div>
                        <div class="aqi_item_price">
                            <div>
                                单位:μg/m3
                            </div>
                            <div>
                                <span>------</span>
                                国标 &lt;35
                            </div>
                        </div>
                        <div id="kqzl_pm" style="width: 580px;height:360px "></div>
                        <!-- <div class="compare_model">
                            <div class="aqitooltip">
                                <div>同比</div>
                                <div style="margin-top:-8px;"><i class="aqi_icondown"></i>
                                    15.13%</div>
                            </div>
                        </div> -->
                    </div>
                </div>
                <!-- 空气质量排行 -->
                <div class="dqwr_header">
                    <i class="title_icon_one"></i>
                    <h2 class="title">单位GDP能耗指标</h2>
                    <div class="title_hr" style="width: 1300px;"></div>
                </div>
                <div class="kqzlkh" style="padding-top: 130px;">
                    <div class="aqi_item">
                        <img src="../img/dqhj/GDP1.png" />
                    </div>
                    <div class="aqi_item">
                        <img src="../img/dqhj/GDP2.png" />
                    </div>
                    <div class="aqi_item">
                        <img src="../img/dqhj/GDP3.png" />
                    </div>
                </div>

            </div>
        </div>




    </div>



</body>

</html>


<script>
    Vue.component('progressbar', {
        template: `<div class="progressContainer">        
                          <div  v-show = "barwidth <'100'" class="progressItem" v-bind:style="{width:barwidth+'%'}"></div>
                          <div  v-show = "barwidth >'100'" class="progressItemQd" v-bind:style="{width:100+'%'}"></div>
                        </div> `,
        props: ['total', "rest", "barwidth"],
        data: function () {
            return {
            }
        }
    });
    var vm = new Vue({
        el: '#dqwr_app',
        data: {
            //空气质量排行- 综合指数 - 同比降低率
            kqzlph: [6, 7, 6, 7],
            // 进度条
            pmList: [
                {
                    name: "PM2.5",
                    pmdj: "优",
                    total: 100,
                    rest: "20",
                    barwidth: 10,
                },
                {
                    name: "PM10",
                    pmdj: "优",
                    total: 100,
                    rest: "38",
                    barwidth: 10,
                },
                {
                    name: "臭氧",
                    pmdj: "轻度",
                    total: 100,
                    rest: "131",
                    barwidth: 10,
                },
                {
                    name: "一氧化碳",
                    pmdj: "优",
                    total: 100,
                    rest: "0.4",
                    barwidth: 10,
                },
                {
                    name: "二氧化氮",
                    pmdj: "优",
                    total: 100,
                    rest: "18",
                    barwidth: 10,
                },
                {
                    name: "二氧化硫",
                    pmdj: "优",
                    total: 100,
                    rest: "4",
                    barwidth: 10,
                },
            ],
            items1: [
                { id: 11, name: '2021年' },
                { id: 22, name: '2022年' },
                { id: 33, name: '2020年' }
            ],
            items2: [
                { id: 11, name: '2021年' },
                { id: 22, name: '2022年' },
                { id: 33, name: '2020年' }
            ],
            selectItem1: '2021年',
            selectItem2: '2021年',
            sskqzl: {
                data: { //显示的数据
                    "name": '空气良',
                    "num": 76
                },
                data2: {
                    "name": '臭氧',
                    "num": 131
                }
            },
            kqzl_pm: {
                data: {
                    datax: ["舟水", "丽水", "台州", "宁波", "衢州", "金华", "温州", "绍兴", "嘉兴", "杭州", "湖州"],
                    datay1: [1, 4, 2, 2, 4, 1, 1.5, 3, 2, 2, 1],
                    datay2: [5, 10, 20, 15, 20, -5, 5, 5, 5, 5, 6],
                    linearGradient: [
                        { offset: 0, color: 'rgb(97,204,249)' },                   //柱图渐变色
                        { offset: 1, color: 'rgb(30,89,155)' },                 //柱图渐变色
                    ],
                    min1: 0,
                    max1: 4,
                    interval1: 1,
                    min2: -20,
                    max2: 20,
                    interval2: 10,
                    color1: 'rgb(254,196,96)',
                    colorline: "rgb(254,196,96)"
                },
                data2: {
                    datax: ["舟水", "丽水", "台州", "宁波", "衢州", "金华", "温州", "绍兴", "嘉兴", "杭州", "湖州"],
                    datay1: [1, 4, 2, 2, 4, 1, 1.5, 3, 2, 2, 1],
                    datay2: [5, 10, 20, 15, 20, -5, 5, 5, 5, 5, 6],
                    linearGradient: [
                        { offset: 0, color: 'rgb(246,201,80)' },                   //柱图渐变色
                        { offset: 1, color: 'rgb(193,144,65)' },
                    ],
                    min1: 0,
                    max1: 4,
                    interval1: 1,
                    min2: -20,
                    max2: 20,
                    interval2: 10,
                    color1: 'rgb(97,204,249)',
                    colorline: "rgb(97,204,249)"
                }
            }

        },
        mounted() {
            //切换地图场景
            top.document.getElementById("map").contentWindow.Work.change3D(5);
            // 仪表盘
            this.setupEChart("aqi_num", this.getSskqzl(this.sskqzl.data))
            this.setupEChart("wrw_num", this.getSskqzl(this.sskqzl.data2))
            // 空气质量
            this.getkqzl_aqi();
            // 空气质量排名
            // this.setupEChart('kqpm_1', this.getkqzl_pm(this.kqzl_pm.data));
            // this.setupEChart('kqpm_2', this.getkqzl_pm(this.kqzl_pm.data2));

        },
        methods: {
            // 进度条
            onBuy: function () {
                this.rest += 1;
                if (this.rest >= 100) this.rest = 100;
                this.barwidth = Math.round(this.rest / this.total * 100);
            },
            // 下拉框
            selectFn1(e) {
                console.log(e.target.selectedIndex) // 选择项的index索引
                console.log(e.target.value) // 选择项的value
                this.selectItem1 = e.target.value
            },
            selectFn2(e) {
                this.selectItem2 = e.target.value
            },
            // echarts实例kqzl_aqi
            setupEChart(id, options) {
                console.log('11')
                const chart = echarts.init(document.getElementById(id));
                chart.clear();
                chart.setOption(options);
                window.onresize = () => {
                    chart.resize();
                };
            },
            // 实时空气质量
            getSskqzl(data) {
                let timeTickId, timer, max = 500, kqzhopt1;
                let that = this
                return kqzhopt = {
                    angleAxis: {
                        show: false,
                        max: max * 3 / 2, //这里将极坐标最大值转换成仪表盘的最大值，(360度除以240度)
                        type: 'value',
                        startAngle: 210, //极坐标初始角度，从第一象限算起，大约在7-8点钟角度之间
                        splitLine: {
                            show: false //隐藏坐标
                        }
                    },
                    barMaxWidth: 18, //圆环宽度
                    radiusAxis: { //隐藏坐标
                        show: false,
                        type: 'category',
                    },
                    polar: { //设置圆环位置和大小
                        center: ['50%', '50%'],
                        radius: '236'
                    },
                    series: [{
                        type: 'bar',
                        data: [{ //上层圆环，用于显示真实数据
                            value: data.num,
                            itemStyle: {
                                color: { //图形渐变颜色方法，四个数字分别代表，右，下，左，上，offset表示0%到100%
                                    type: 'linear',
                                    x: 0,
                                    y: 0,
                                    x2: 1, //从左到右 0-1
                                    y2: 0,
                                    colorStops: [{
                                        offset: 0,
                                        color: 'rgb(28,210,248)' // 0% 处的颜色
                                    }, {
                                        offset: 1,
                                        color: 'rgb(28,210,248)' // 100% 处的颜色
                                    }],
                                    globalCoord: false // 缺省为 false
                                },
                                shadowColor: 'rgba(255, 255, 255, 0.2)', //加白色阴影产生高亮效果
                                shadowBlur: 10
                            }
                        }],
                        barGap: '-100%', //柱间距离,用来将上下两层圆环重合
                        coordinateSystem: 'polar', //类型，极坐标
                        roundCap: true, //顶端圆角
                        z: 2 //圆环层级，和zindex相似
                    }, { //下层圆环，用于显示最大值
                        type: 'bar',
                        data: [{
                            value: max,
                            itemStyle: {
                                color: 'rgb(34,37,35)',
                                shadowColor: 'rgba(0, 0, 0, 0.2)', //加白色阴影产生高亮效果
                                shadowBlur: 5,
                                shadowOffsetY: 2
                            }
                        }],
                        barGap: '-100%', //柱间距离,用来将上下两层圆环重合
                        coordinateSystem: 'polar', //类型，极坐标
                        roundCap: true, //顶端圆角
                        z: 1 //圆环层级，和zindex相似
                    },
                    { //仪表盘
                        type: 'gauge',
                        radius: '100%',
                        startAngle: 210, //起始角度，同极坐标
                        endAngle: -30, //终止角度，同极坐标
                        max: max,
                        splitNumber: 5, //分割线个数（除原点外）
                        axisLine: {            // 坐标轴线
                            lineStyle: {       // 属性lineStyle控制线条样式
                                color: [[1, 'rgb(73,144,248)']],//修改指针颜色,指针颜色根据仪表盘颜色变化
                                width: 3,

                            }
                        },
                        pointer: {
                            show: true,
                        },
                        axisLabel: {
                            // 坐标轴数字
                            textStyle: {
                                fontSize: 8,
                                color: "#13B5FC"
                            },

                        },
                        axisTick: { // 坐标轴标记
                            length: 10,
                            lineStyle: {
                                color: "#13B5FC"
                            }
                        },
                        splitLine: { // 分隔线
                            length: 5,
                            lineStyle: {
                                width: 1,
                            }
                        },
                        title: { //标题
                            textStyle: {
                                color: 'rgb(93,147,198)',
                                shadowColor: '#fff',
                                fontSize: 25
                            },
                            offsetCenter: ["0", '60%'] //位置偏移
                        },
                        detail: { //仪表盘数值
                            formatter: function (params) {
                                var name = data.num.toString()
                                var list = ''
                                for (var i = 0; i < name.length; i++) {
                                    list += '{value|' + name[i] + '}' //每个数字用border隔开
                                    if (i !== name.length - 1) {
                                        list += '{margin|}' //添加margin值
                                    }
                                }
                                return [list]
                            },
                            offsetCenter: ["0", '20%'],
                            rich: { //编辑富文本样式
                                value: {
                                    fontSize: 40,
                                    fontWeight: 600,
                                    padding: [0, 0, 0, 0],
                                    color: 'yellow',
                                },

                            }

                        },
                        data: [{
                            value: data.num,
                            name: data.name
                        }]
                    }
                    ]
                }
            },
            // 空气质量  // AQI
            getkqzl_aqi() {
                let aqiopt1 = {
                    tooltip: {
                        trigger: "axis",
                        // formatter:"{b} : {c}件",
                        //show:false
                        "textStyle": { "fontSize": 28 }
                    },
                    xAxis: {
                        type: 'category',
                        data: ['2019年', '2020年', '2021年',],
                        axisLine: {
                            lineStyle: {
                                type: 'solid',
                                color: 'rgb(66,106,149)',//左边线的颜色
                                width: '2'//坐标线的宽度
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#fff',//坐标值得具体的颜色
                            },
                            fontSize: 30,
                        }
                    },
                    yAxis: {
                        type: 'value',
                        scale: true,
                        max: 100,
                        min: 80,
                        splitNumber: 5,
                        axisLabel: {
                            textStyle: {
                                color: '#fff',//坐标值得具体的颜色
                            },
                            fontSize: 30,
                        },
                        axisLine: {
                            lineStyle: {
                                type: 'solid',
                                color: 'rgb(66,106,149)',//左边线的颜色
                                width: '0'//坐标线的宽度
                            },
                        },
                        splitLine: { // 虚线
                            show: true,
                            lineStyle: {
                                type: 'dashed',
                                color: 'rgb(66,106,149)',//左边线的
                            }
                        },
                    },
                    // tooltip: {
                    //     trigger: 'axis',
                    //     formatter: function (params, ticket, callback) {
                    //         return (params[0].value - 87.5) > 0 ? `<div class="aqitooltip" style="text-align: left">
                    //             <div>同比</div>
                    //             <br/>
                    //             <div><i  class="aqi_icon"></i>
                    //                 ${params[0].value - 87.5}%</div>
                    //             </div><br/>`
                    //             :
                    //             `<div class="aqitooltip" style="text-align: left">
                    //             <div>同比</div>
                    //             <br/>
                    //             <div><i  class=" aqi_icondown"></i>
                    //                 ${params[0].value - 87.5}%</div>
                    //             </div><br/>`
                    //     }
                    // },
                    series: [
                        {
                            data: [88.8, 92.1, 93.8],
                            type: 'bar',
                            barWidth: 25,//柱图宽度
                            itemStyle: {
                                normal: {
                                    barBorderRadius: [5, 5, 0, 0],
                                    color: new echarts.graphic.LinearGradient(
                                        0, 0, 0, 1,
                                        [
                                            { offset: 0, color: 'rgb(97,204,249)' },                   //柱图渐变色
                                            { offset: 1, color: 'rgb(30,89,155)' },                 //柱图渐变色
                                        ]
                                    )
                                },
                            },
                            // 水平线
                            markLine: {
                                symbol: ['none', 'none'],//去掉箭头
                                itemStyle: {
                                    normal: {
                                        lineStyle: { type: 'solid', color: 'blue' }
                                        , label: { show: false, position: 'left' }
                                    }
                                },
                                data: [
                                    {
                                        name: 'Y 轴值为 100 的水平线',
                                        yAxis: 87.5,
                                        lineStyle: {
                                            type: 'dashed',
                                            color: '#e2ba43',//左边线的
                                            width: "2"
                                        }
                                    },
                                ]
                            }
                        }
                    ]
                };
                this.setupEChart("kqzl_aqi", aqiopt1);
                let aqiopt2 = {
                    tooltip: {
                        trigger: "axis",
                        // formatter:"{b} : {c}件",
                        //show:false
                        "textStyle": { "fontSize": 28 }
                    },
                    xAxis: {
                        type: 'category',
                        data: ['2019年', '2020年', '2021年',],
                        axisLine: {
                            lineStyle: {
                                type: 'solid',
                                color: 'rgb(66,106,149)',//左边线的颜色
                                width: '0'//坐标线的宽度
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#fff',//坐标值得具体的颜色
                            },
                            fontSize: 30,
                        }
                    },
                    yAxis: {
                        type: 'value',
                        scale: true,
                        min: 0,
                        max: 5,
                        axisLabel: {
                            formatter: function (value) {
                                var texts = [];
                                if (value == 0) {
                                    texts.push('0');
                                }
                                else if (value <= 1) {
                                    texts.push('73');
                                }
                                else if (value <= 2) {
                                    texts.push('146');
                                }
                                else if (value <= 3) {
                                    texts.push('219');
                                }
                                else if (value <= 4) {
                                    texts.push('292');
                                }
                                else {
                                    texts.push('365');
                                }
                                return texts;
                            },
                            textStyle: {
                                color: '#fff',//坐标值得具体的颜色
                            },
                            fontSize: 29,
                        },
                        splitLine: { // 虚线
                            show: true,
                            lineStyle: {
                                type: 'dashed',
                                color: 'rgb(66,106,149)',//左边线的
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                type: 'solid',
                                color: 'rgb(66,106,149)',//左边线的颜色
                                width: '0'//坐标线的宽度
                            },
                        },
                    },
                    series: [
                        {
                            data: [4.8, 4.5, 4.7],
                            type: 'bar',
                            barWidth: 25,//柱图宽度
                            itemStyle: {
                                normal: {
                                    barBorderRadius: [5, 5, 0, 0],
                                    color: new echarts.graphic.LinearGradient(
                                        0, 0, 0, 1,
                                        [
                                            { offset: 0, color: 'rgb(224,254,155)' },                   //柱图渐变色
                                            { offset: 1, color: 'rgb(254,159,106)' },                 //柱图渐变色
                                        ]
                                    )
                                },
                            },
                            // 水平线
                            markLine: {
                                symbol: ['none', 'none'],//去掉箭头
                                itemStyle: {
                                    normal: {
                                        lineStyle: { type: 'solid', color: 'blue' }
                                        , label: { show: false, position: 'left' }
                                    }
                                },
                                data: [
                                    {
                                        name: 'Y 轴值为 293 的水平线',
                                        yAxis: 4.5,
                                        lineStyle: {
                                            type: 'dashed',
                                            color: '#e2ba43',
                                            width: "2"
                                        }
                                    },
                                ]
                            }
                        },

                    ]
                };
                this.setupEChart("kqzl_yzts", aqiopt2);
                let aqiopt3 = {
                    tooltip: {
                        trigger: "axis",
                        // formatter:"{b} : {c}件",
                        //show:false
                        "textStyle": { "fontSize": 28 }
                    },
                    xAxis: {
                        type: 'category',
                        data: ['2019年', '2020年', '2021年',],
                        axisLine: {
                            lineStyle: {
                                type: 'solid',
                                color: 'rgb(66,106,149)',//左边线的颜色
                                width: '0'//坐标线的宽度
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#fff',//坐标值得具体的颜色
                            },
                            fontSize: 30,
                        }
                    },
                    yAxis: {
                        type: 'value',
                        scale: true,
                        min: 0,
                        max: 35,
                        axisLabel: {
                            // formatter: function (value) {
                            //     var texts = [];
                            //     if (value == 0) {
                            //         texts.push('0');
                            //     }
                            //     else if (value <= 1) {
                            //         texts.push('7');
                            //     }
                            //     else if (value <= 2) {
                            //         texts.push('14');
                            //     }
                            //     else if (value <= 3) {
                            //         texts.push('21');
                            //     }
                            //     else if (value <= 4) {
                            //         texts.push('28');
                            //     }
                            //     else {
                            //         texts.push('35');
                            //     }
                            //     return texts;
                            // },
                            textStyle: {
                                color: '#fff',//坐标值得具体的颜色
                            },
                            fontSize: 30,
                        },
                        splitLine: { // 虚线
                            show: true,
                            lineStyle: {
                                type: 'dashed',
                                color: 'rgb(66,106,149)',//左边线的
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                type: 'solid',
                                color: 'rgb(66,106,149)',//左边线的颜色
                                width: '0'//坐标线的宽度
                            },
                        },
                    },
                    series: [
                        {
                            data: [30, 29, 28],
                            type: 'bar',
                            barWidth: 25,//柱图宽度
                            itemStyle: {
                                normal: {
                                    barBorderRadius: [5, 5, 0, 0],
                                    color: new echarts.graphic.LinearGradient(
                                        0, 0, 0, 1,
                                        [
                                            { offset: 0, color: 'rgb(246,201,80)' },                   //柱图渐变色
                                            { offset: 1, color: 'rgb(193,144,65)' },                 //柱图渐变色
                                        ]
                                    )
                                },
                            },
                            // 水平线
                            markLine: {
                                symbol: ['none', 'none'],//去掉箭头
                                itemStyle: {
                                    normal: {
                                        lineStyle: { type: 'solid', color: 'blue' }
                                        , label: { show: false, position: 'left' }
                                    }
                                },
                                data: [
                                    {
                                        name: 'Y 轴值为30 的水平线',
                                        yAxis: 35,
                                        lineStyle: {
                                            type: 'dashed',
                                            color: '#e2ba43',//左边线的
                                            width: "2"
                                        }
                                    },
                                ]
                            }
                        }
                    ]
                };
                this.setupEChart("kqzl_pm", aqiopt3);
            },
            // 空气质量排名
            // getkqzl_pm(data) {
            //     return {
            //         xAxis: [
            //             {
            //                 type: 'category',
            //                 data: data.datax,
            //                 axisPointer: {
            //                     type: 'shadow'
            //                 },
            //                 axisTick: {
            //                     show: true,
            //                     interval: 0
            //                 },
            //                 axisLabel: {
            //                     textStyle: {
            //                         color: '#fff',//坐标值得具体的颜色
            //                     },
            //                     fontSize: 25,
            //                 }

            //             }
            //         ],
            //         yAxis: [
            //             {
            //                 type: 'value',
            //                 // name: '数量',
            //                 min: data.min1,
            //                 max: data.max1,
            //                 interval: data.interval1,
            //                 axisLabel: {
            //                     textStyle: {
            //                         color: '#fff',//坐标值得具体的颜色
            //                     },
            //                     fontSize: 26,
            //                 },
            //                 splitLine: { // 虚线
            //                     show: true,
            //                     lineStyle: {
            //                         type: 'dashed',
            //                         color: 'rgb(66,106,149)',//左边线的
            //                     }
            //                 },
            //                 axisLine: {
            //                     lineStyle: {
            //                         type: 'solid',
            //                         color: 'rgb(66,106,149)',//左边线的颜色
            //                         width: '0'//坐标线的宽度
            //                     },
            //                 },
            //             },

            //             {
            //                 type: 'value',
            //                 min: data.min2,
            //                 max: data.max2,
            //                 interval: data.interval2,
            //                 axisLabel: {
            //                     // formatter: '{value} %',
            //                     textStyle: {
            //                         color: '#fff',//坐标值得具体的颜色
            //                     },
            //                     fontSize: 26,
            //                 }, splitLine: { // 虚线
            //                     show: true,
            //                     lineStyle: {
            //                         type: 'dashed',
            //                         color: 'rgb(66,106,149)',//左边线的
            //                     }
            //                 },
            //                 axisLine: {
            //                     lineStyle: {
            //                         type: 'solid',
            //                         color: 'rgb(66,106,149)',//左边线的颜色
            //                         width: '0'//坐标线的宽度
            //                     },
            //                 },

            //             }
            //         ],
            //         series: [
            //             {
            //                 name: ' ',
            //                 type: 'bar',
            //                 data: data.datay1,
            //                 barWidth: '25%',
            //                 itemStyle: {
            //                     normal: {
            //                         barBorderRadius: [5, 5, 0, 0],
            //                         color: new echarts.graphic.LinearGradient(
            //                             0, 0, 0, 1, data.linearGradient

            //                         )
            //                     },
            //                 }
            //             },
            //             {
            //                 name: '  ',
            //                 type: 'line',
            //                 yAxisIndex: 1,
            //                 data: data.datay2,
            //                 symbolSize: 10,
            //                 showSymbol: true,
            //                 symbol: 'circle',  //设定为实心点 
            //                 color: data.color1,   //设定实线点的颜色
            //                 lineStyle: {
            //                     normal: {
            //                         width: 2,
            //                         color: data.colorline, //设置实线的颜色
            //                     },
            //                 },
            //             },
            //         ]

            //     };

            // }
        }




    })






</script>