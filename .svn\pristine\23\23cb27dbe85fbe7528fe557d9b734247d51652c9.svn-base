<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>河长制监测-左侧</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/jslib/vue-count-to.min.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/datav.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <link rel="stylesheet" href="/static/citybrain/shgl/css/hczjc-left.css" />
        <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
    </head>
    <style></style>

    <body>
        <div id="app" class="container" v-cloak>
            <nav>
                <s-header-title htype="1" title="河道基本信息汇聚" data-time="2022年2月11日"></s-header-title>
            </nav>
            <div class="jbxx">
                <div class="table table1">
                    <div class="th">
                        <div class="th_td" v-for="(item,index) in theadList1" :key="index">{{item}}</div>
                    </div>
                    <div class="tbody" id="tbody1" @mouseover="mouseenterEvent1()" @mouseleave="mouseleaveEvent1()">
                        <div class="tr" v-for="(item ,i) in tbodyList1" :key="i">
                            <div class="tr_td" style="flex: 0.1">{{item.hdmc}}</div>
                            <div class="tr_td" style="flex: 0.1">{{item.ckcc}}</div>
                            <div class="tr_td" style="flex: 0.1">{{item.hddlwz}}</div>
                            <div class="tr_td" style="flex: 0.15">{{item.hdlx}}</div>
                            <div class="tr_td" style="flex: 0.125" :title="item.qd">{{item.qd}}</div>
                            <div class="tr_td" style="flex: 0.125" :title="item.zd">{{item.zd}}</div>
                            <div class="tr_td" style="flex: 0.2" :title="item.hdglfwx">{{item.hdglfwx}}</div>
                            <div class="tr_td" style="flex: 0.1">{{item.hdlsx}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="middle">
                <div class="middle-left">
                    <nav>
                        <s-header-title htype="2" title="巡河信息汇聚" data-time=""></s-header-title>
                    </nav>
                    <div class="xhxx">
                        <div class="table table2">
                            <div class="th">
                                <div class="th_td" v-for="(item,index) in theadList2" :key="index">{{item}}</div>
                            </div>
                            <div
                                class="tbody"
                                id="tbody2"
                                @mouseover="mouseenterEvent2()"
                                @mouseleave="mouseleaveEvent2()"
                            >
                                <div class="tr" v-for="(item ,i) in tbodyList2" :key="i">
                                    <div class="tr_td" style="flex: 0.25">{{item.xhrq}}</div>
                                    <div class="tr_td" style="flex: 0.15">{{item.xhry}}</div>
                                    <div class="tr_td" style="flex: 0.15">{{item.xhdd}}</div>
                                    <div class="tr_td" style="flex: 0.2">{{item.xhslqk}}</div>
                                    <div class="tr_td" style="flex: 0.25">{{item.zgsj}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="middle-right">
                    <nav>
                        <s-header-title htype="2" title="设施信息汇聚" data-time=""></s-header-title>
                    </nav>
                    <div class="ssxx">
                        <div id="chart01" style="width: 100%; height: 100%"></div>
                    </div>
                </div>
            </div>
            <nav>
                <s-header-title htype="1" title="监测信息汇聚" data-time="2022年2月11日"></s-header-title>
            </nav>
            <div class="jcxx">
                <div class="jcxx-left">
                    <div class="table table3">
                        <div class="th">
                            <div class="th_td" v-for="(item,index) in theadList3" :key="index">{{item}}</div>
                        </div>
                        <div class="tbody" id="tbody3" @mouseover="mouseenterEvent3()" @mouseleave="mouseleaveEvent3()">
                            <div class="tr" v-for="(item ,i) in tbodyList3" :key="i" @click="changeVideo(i,item.dmmc)">
                                <div class="tr_td" style="flex: 0.2">{{item.dmmc}}</div>
                                <div class="tr_td" style="flex: 0.25">{{item.qymc}}</div>
                                <div class="tr_td" style="flex: 0.15">{{item.szlb}}</div>
                                <div class="tr_td" style="flex: 0.25">{{item.zywrw}}</div>
                                <div class="tr_td" style="flex: 0.15">{{item.sfdb}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="jcxx-right">
                    <div class="video-box" id="dom0"></div>
                    <p>{{ title || ''}}</p>
                </div>
            </div>
        </div>
    </body>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>

    <script>
        const DHWsInstance = DHWs.getInstance({
            reConnectCount: 2,
            connectionTimeout: 30 * 1000,
            messageEvents: {
                loginState() {
                    console.log("aaaa");
                },
            },
        });
        var vm = new Vue({
            el: "#app",
            data() {
                return {
                    time1: null,
                    time2: null,
                    time3: null,
                    dom1: null,
                    dom2: null,
                    dom3: null,
                    theadList1: [
                        "河道名称",
                        "长宽尺寸",
                        "河道地理位置",
                        "河道类型",
                        "起点",
                        "终点",
                        "河道管理范围线",
                        "河道临水线",
                    ],
                    theadList2: ["巡河日期", "巡河人员", "巡河地点", "巡河四乱情况", "整改时间"],
                    theadList3: ["断面名称", "区域名称", "水质类别", "主要污染物", "是否达标"],
                    tbodyList1: [],
                    tbodyList2: [],
                    tbodyList3: [],
                    echartData: [],

                    // 视频
                    ws: DHWsInstance,
                    isLogin: false,
                    videoList: [],
                    showVideoList: [],
                    title: "",
                };
            },
            mounted() {
                this.init();
                // this.drawMap();
                this.scroll1();
                this.scroll2();
                this.scroll3();
                this.login();
            },
            methods: {
                changeVideo(i,name){
                    this.getVideoList(i,name)
                },
                //绘制地图
                drawMap() {
                    const imgIcon = `${baseURL.url}/static/citybrain/tckz/img/tckz_gj/河道.png`;
                    $api("shgl_hczjc_map").then((res) => {
                        console.log(1111111111, res);
                        let pointData = [];
                        res.forEach((item) => {
                            let str = {
                                data: {
                                    pointId: "jtys",
                                    obj: item,
                                },
                                point: item.lnglat,
                            };
                            pointData.push(str);
                        });
                        top.document.getElementById("map").contentWindow.Work.funChange(
                            JSON.stringify({
                                funcName: "pointLoad", //功能名称
                                pointType: "河道", //点位类型图标
                                pointId: "0" + 1,
                                setClick: true,
                                pointData: pointData,
                                imageConfig: { iconSize: 1 },
                                size: [0.01, 0.01, 0.01, 0.01],
                                popup: {
                                    offset: [50, -100],
                                },
                            })
                        );
                    });
                },
                init() {
                    top.document.getElementById("map").contentWindow.Work.change3D(7);
                    $api("shgl_hczjc_left01").then((res) => {
                        this.tbodyList1 = res;
                    });
                    $api("shgl_hczjc_left02").then((res) => {
                        this.tbodyList2 = res;
                    });
                    $api("shgl_hczjc_left03").then((res) => {
                        this.tbodyList3 = res;
                    });
                    $api("shgl_hczjc_left04").then((res) => {
                        this.echartData = res;
                        this.getChart01("chart01");
                    });
                },
                scroll1() {
                    this.dom1 = document.getElementById("tbody1");
                    this.time1 = setInterval(() => {
                        this.dom1.scrollTop += 2;
                        if (this.dom1.scrollTop >= this.dom1.scrollHeight - this.dom1.offsetHeight) {
                            this.dom1.scrollTop = 0;
                        }
                    }, 20);
                },
                scroll2() {
                    this.dom2 = document.getElementById("tbody2");
                    this.time2 = setInterval(() => {
                        this.dom2.scrollTop += 2;
                        if (this.dom2.scrollTop >= this.dom2.scrollHeight - this.dom2.offsetHeight) {
                            this.dom2.scrollTop = 0;
                        }
                    }, 20);
                },
                scroll3() {
                    this.dom3 = document.getElementById("tbody3");
                    this.time3 = setInterval(() => {
                        this.dom3.scrollTop += 2;
                        if (this.dom3.scrollTop >= this.dom3.scrollHeight - this.dom3.offsetHeight) {
                            this.dom3.scrollTop = 0;
                        }
                    }, 20);
                },
                mouseenterEvent1() {
                    clearInterval(this.time1);
                },
                mouseleaveEvent1() {
                    this.time1 = setInterval(() => {
                        this.dom1.scrollTop += 2;
                        if (this.dom1.scrollTop >= this.dom1.scrollHeight - this.dom1.offsetHeight) {
                            this.dom1.scrollTop = 0;
                        }
                    }, 20);
                },
                mouseenterEvent2() {
                    clearInterval(this.time2);
                },
                mouseleaveEvent2() {
                    this.time2 = setInterval(() => {
                        this.dom2.scrollTop += 2;
                        if (this.dom2.scrollTop >= this.dom2.scrollHeight - this.dom2.offsetHeight) {
                            this.dom2.scrollTop = 0;
                        }
                    }, 20);
                },
                mouseenterEvent3() {
                    clearInterval(this.time3);
                },
                mouseleaveEvent3() {
                    this.time3 = setInterval(() => {
                        this.dom3.scrollTop += 2;
                        if (this.dom3.scrollTop >= this.dom3.scrollHeight - this.dom3.offsetHeight) {
                            this.dom3.scrollTop = 0;
                        }
                    }, 20);
                },
                getChart01(id) {
                    const myCharts = echarts.init(document.getElementById(id));
                    let title = "设施总数";
                    let color = ["#0E7CE2", "#FF8352", "#E271DE", "#F8456B", "#00FFFF", "#4AEAB0"];
                    let formatNumber = function (num) {
                        let reg = /(?=(\B)(\d{3})+$)/g;
                        return num.toString().replace(reg, ",");
                    };
                    let total = this.echartData.reduce((a, b) => {
                        return a + b.value * 1;
                    }, 0);

                    option = {
                        color: color,
                        // tooltip: {
                        //     trigger: 'item'
                        // },
                        title: [
                            {
                                text: "{name|" + title + "}\n{val|" + formatNumber(total) + "}",
                                top: "center",
                                left: "center",
                                textStyle: {
                                    rich: {
                                        name: {
                                            fontSize: 40,
                                            fontWeight: "normal",
                                            color: "#fff",
                                            padding: [10, 0],
                                        },
                                        val: {
                                            fontSize: 28,
                                            fontWeight: "bold",
                                            color: "#fff",
                                        },
                                    },
                                },
                            },
                            {
                                text: "",
                                top: 20,
                                left: 20,
                                textStyle: {
                                    fontSize: 28,
                                    color: "#666666",
                                    fontWeight: 400,
                                },
                            },
                        ],
                        series: [
                            {
                                type: "pie",
                                radius: ["45%", "60%"],
                                center: ["50%", "50%"],
                                data: this.echartData,
                                hoverAnimation: false,
                                itemStyle: {
                                    normal: {
                                        borderWidth: 0,
                                    },
                                },
                                labelLine: {
                                    normal: {
                                        length: 20,
                                        length2: 60,
                                        lineStyle: {
                                            color: "#e6e6e6",
                                        },
                                    },
                                },
                                label: {
                                    normal: {
                                        formatter: (params) => {
                                            return (
                                                "{name|" + params.name + "}\n{value|" + formatNumber(params.value) + "}"
                                            );
                                        },
                                        rich: {
                                            name: {
                                                fontSize: 28,
                                                padding: [0, 10, 0, 4],
                                                color: "#fff",
                                            },
                                            value: {
                                                fontSize: 30,
                                                fontWeight: "bold",
                                                color: "#fff",
                                                padding: [5, 0],
                                                align: "center",
                                            },
                                        },
                                    },
                                },
                            },
                        ],
                    };

                    myCharts.setOption(option);
                    myCharts.getZr().on("mousemove", (param) => {
                        myCharts.getZr().setCursorStyle("default");
                    });
                },

                login() {
                    // 调用登录接口
                    this.ws.detectConnectQt().then((res) => {
                        if (res) {
                            // 连接客户端成功
                            this.ws.login({
                                loginIp: "*************",
                                loginPort: "8001",
                                userName: "yjgl",
                                userPwd: "yjgl1234",
                                token: "",
                                https: 0,
                            });
                            // this.$Message.info('登录中...')
                            console.log("登录中...");
                            this.ws.on("loginState", (res) => {
                                this.isLogin = res;
                                if (res) {
                                    console.log("登录成功");
                                    this.getVideoList(0,'东关村');
                                } else {
                                    console.log("登录失败");
                                }
                            });
                        } else {
                            // 连接客户端失败
                            this.$Message.info("请重新安装客户端");
                        }
                    });
                },
                getVideoList(i,name) {
                    $api("/csdn/cstz/cstzRight009", { code: 1 }).then((res) => {
                        this.videoList = res.map((ele, index) => {
                            let str = {
                                channelId: ele.channelId,
                                title: ele.title,
                                dom: "dom" + index,
                                num: index,
                            };
                            return str;
                        });
                        console.log(this.videoList);
                        this.showVideoList = this.videoList.slice(i, i+1);
                        // this.title = this.showVideoList[0].title;
                        this.title = name;
                        this.create(i);
                    });
                },
                create(i) {
                    // 调用创建控件接口
                    var this_ = this;
                    var paramList = [];
                    console.log(this_.showVideoList);
                    for (let index = 0; index < this_.showVideoList.length; index++) {
                        let item = this_.showVideoList[index];
                        console.log(item);
                        paramList[index] = {
                            ctrlType: "playerWin",
                            ctrlCode: "ctrl" + item.num,
                            ctrlProperty: {
                                displayMode: 1,
                                splitNum: 1,
                                channelList: [
                                    {
                                        channelId: item.channelId,
                                    },
                                ],
                            },
                            visible: true,
                            // domId: item.dom,
                            domId: "dom0",
                        };
                    }
                    console.log("paramList===>", paramList);

                    setTimeout(function () {
                        this_.ws
                            .createCtrl(paramList)
                            .then((res) => {
                                console.log(res);
                            })
                            .catch((e) => {
                                console.log(e);
                            });
                        this_.ws.on("createCtrlResult", (res) => {
                            console.warn(res);
                        });
                    }, 2000);
                },
            },
        });
    </script>
</html>
