[v-cloak] {
  display: none;
}

* {
  margin: 0;
  padding: 0;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

#szss-left {
  overflow: hidden;
}

.szss-left_box {
  position: relative;
  width: 2070px;
  height: 1850px;
  background: url('/img/right-bg.png') no-repeat;
  background-size: 100%;
  padding: 10px 55px 30px;
  box-sizing: border-box;
}

.box-title {
  margin-top: 20px;
}

.top-con {
  width: 100%;
  height: 500px;
  display: flex;
  justify-content: space-between;
}

.top-left,
.left-box {
  width: 700px;
  height: 100%;
}

.top-right,
.right-box {
  width: 1200px;
  height: 100%;
}

.sanicon {
  display: inline-block;
  width: 40px;
  height: 27px;
  background: url("/static/citybrain/csdn/img/ywt/三级标题图标.png") no-repeat;
}

.box1 {
  width: 100%;
  height: 580px;
}

.list {
  width: 100%;
  height: 80px;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  box-sizing: border-box;
  margin-bottom: 20px;
}

.list-item {
  flex: 0.25;
  height: 100%;
  text-align: center;
  line-height: 80px;
  font-size: 30px;
  padding: 0 20px;
  box-sizing: border-box;
  color: #fff;
  background-image: url('/static/citybrain/csdn/img/ywt4-left/szjj-left-bg.png');
  background-size: 100% 100%;
}

.list-item>span:first-child {
  margin-right: 20px;
}

.list-item>span:nth-child(2) {
  font-size: 34px;
}

.box1-bottom {
  display: flex;
  justify-content: space-between;
}

.left-box {
  height: 420px;
}

.right-box {
  height: 420px;
}

.box2-bottom {
  width: 100%;
  height: 450px;
}

.alt-title {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
  box-sizing: border-box;
}

.alt-title-item {
  font-size: 30px;
  color: #fff;
  margin-right: 50px;
}

.alt-title-item>span {
  margin-left: 20px;
}

.s-c-red2-gradient {
  background: linear-gradient(to bottom, #df5151, #ffcfcf, #ff4949, #ffcfcf);
  -webkit-background-clip: text;
  color: transparent;
}

/*表格*/
.table {
  width: 100%;
  height: 320px;
  padding: 20px 20px 0;
  box-sizing: border-box;
}

.table-th {
  display: flex;
  display: -webkit-flex;
  width: 100%;
  height: 60px;
  margin-bottom: 10px;
}

.th:nth-child(1) {
  flex: 1;
}

.th:nth-child(2),
.th:nth-child(3) {
  flex: 2;
}

.th {
  width: 0;
  text-align: center;
  font-size: 32px;
  line-height: 60px;
  color: #77b3f1;
  margin-left: 0 !important;
  background-color: #035B86;
}

.table-tr {
  width: 100%;
  height: calc(100% - 80px);
  overflow-y: auto;
}

.tr {
  margin: 5px 0;
  display: flex;
  display: -webkit-flex;
  width: 100%;
  padding: 10px 0;
  background-color: #0F2B4D;
  margin-bottom: 0px !important;
}

.td {
  flex: 2;
  width: 0;
  text-align: center;
  word-break: break-all;
  font-size: 32px;
  color: #D6E7F9;
  display: flex;
  align-items: center;
  justify-content: center;
}

.td:nth-child(1) {
  flex: 1;
}

.td:nth-child(2),
.th:nth-child(3) {
  flex: 2;
}

.tr .td:nth-child(1) {
  background: linear-gradient(to bottom, #ccf4ff, #ffffff, #00baf8, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.td>div>div {
  background-size: 100% 100%;
}

::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */

}

::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: #20aeff;
  height: 8px;
}

@keyframes rowUp {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }

  100% {
    transform: translate3d(0, -100%, 0);
    -webkit-transform: translate3d(0, -100%, 0);
    -moz-transform: translate3d(0, -100%, 0);
    -ms-transform: translate3d(0, -100%, 0);
    -o-transform: translate3d(0, -100%, 0);
  }
}

.tr {
  animation: 10s rowUp linear infinite normal;
  -webkit-animation: 10s rowUp linear infinite normal;
}

.table-tr:hover .tr {
  animation-play-state: paused;
}


.cicle_bg{
  width: 100%;
  height: 250px;
  background-image: url('/static/citybrain/shgl/img/jb_bg.png');
  background-repeat: no-repeat;
  background-size: 100%;
  padding-top: 50px;
  box-sizing: border-box;
  margin-top: 30px;
}
.ws-icon{
  width: 150px;
  height:150px;
  margin:  0 auto;
  background-image: url('/static/citybrain/shgl/img/icon_ws.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.text-box{
  width: 100%;
  margin-top: 20px;
  font-size: 32px;
  display: flex;
  justify-content: space-between;
  text-align: center;
}
.text-item>span{
  font-size: 40px;
}
.dianxian-infos {
  margin-right: 30px;
  display: flex;
  justify-content: flex-end;
}
.dianxian-info {
  margin-left: 30px;
  font-size: 30px;
  color: #fff;
}