<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <script src="./Vue/vue.js"></script>
    <title>Title</title>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/js/jslib/jquery-3.4.1.min.js"></script>
  </head>

  <body>
    <div id="app">
      <!-- <dv-decoration-5
        style="
          width: 2000px;
          height: 180px;
          position: absolute;
          left: 2480;
          top: 0;
        "
      /> -->
      <div class="box">
        <div>
          <!-- <div class="table" id="infoTable" style="display: none"> -->
            <div class="table" id="qy-table" v-show="scztxxShow">
              <div class="tabletitle">
                <div class="titletext">
                  <img
                    src="/static/citybrain/scjg/img/sy/table_header_left.png"
                    style="width: 26px;height: 30px;margin: 0px 20px;"
                  />
                  <span>市场主体信息</span>
                </div>
              </div>
              <div class="table-content">
                <div class="tableline">
                  <div v-for="(item, i) in qyTableArr">
                    <div
                      class="linename"
                      :class="{ active: qyTableIndex === i }"
                      @click="func2(item,i)"
                    >
                      <span class="line-text">{{ item }}</span>
                      <span class="check-btn"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <div class="table" id="jy-table">
            <div class="tabletitle">
              <!-- <div class="titletext" @click="fun('五大千亿产业户数','户')"> -->
              <div class="titletext">
                <img
                  src="/static/citybrain/scjg/img/sy/table_header_left.png"
                  style="width: 26px;height: 30px;margin: 0px 20px;"
                />
                <span>经营情况</span>
                <img
                  class="flag-img"
                  v-show="infoTableToggleFlag1"
                  @click="toggleProductPanel1"
                  src="/static/citybrain/scjg/img/sy/middle_table_shrink.png"
                />
                <img
                  class="flag-img"
                  v-show="!infoTableToggleFlag1"
                  @click="toggleProductPanel1"
                  src="/static/citybrain/scjg/img/sy/middle_table_expand.png"
                />
              </div>
              <!-- <div class="titlenumber">
                {{data5}}
                <span style="font-size: 30px">{{titleUnit}}</span>
              </div> -->
            </div>
            <div class="table-content">
              <div class="tableline">
                <div v-for="(item, i) in jyTableArr">
                  <div
                    class="linename"
                    :class="{ active: jyTableIndex === i }"
                    @click="func1(item,i)"
                  >
                    <span class="line-text">{{ item }}</span>
                    <span class="check-btn"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="table" id="infoTable">
            <div class="tabletitle">
              <!-- <div class="titletext" @click="fun('五大千亿产业户数','户')"> -->
              <div class="titletext">
                <img
                  src="/static/citybrain/scjg/img/sy/table_header_left.png"
                />
                <span>五大千亿产品户数</span>
                <img
                  class="flag-img"
                  v-show="infoTableToggleFlag"
                  @click="toggleProductPanel"
                  src="/static/citybrain/scjg/img/sy/middle_table_shrink.png"
                />
                <img
                  class="flag-img"
                  v-show="!infoTableToggleFlag"
                  @click="toggleProductPanel"
                  src="/static/citybrain/scjg/img/sy/middle_table_expand.png"
                />
              </div>
              <!-- <div class="titlenumber">
                {{data5}}
                <span style="font-size: 30px">{{titleUnit}}</span>
              </div> -->
            </div>
            <div class="table-content">
              <div class="tableline">
                <div v-for="(item, i) in infoTableArr">
                  <div
                    class="linename"
                    :class="{ active: infoTableIndex === i }"
                    @click="fun(item, '户', 'info', i)"
                  >
                    <span class="line-text">{{ item }}</span>
                    <span class="check-btn"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="table register-table">
            <div class="tabletitle">
              <!-- <div class="titletext" @click="fun('五大千亿产业注册资本','亿元')"> -->
              <div class="titletext">
                <img
                  src="/static/citybrain/scjg/img/sy/table_header_right.png"
                />
                <span>五大千亿产业注册资本</span>
                <img
                  class="flag-img"
                  v-show="registerTableToggleFlag"
                  @click="toggleRegisterPanel"
                  src="/static/citybrain/scjg/img/sy/middle_table_shrink.png"
                />
                <img
                  class="flag-img"
                  v-show="!registerTableToggleFlag"
                  @click="toggleRegisterPanel"
                  src="/static/citybrain/scjg/img/sy/middle_table_expand.png"
                />
              </div>
            </div>
            <div class="table-content">
              <div class="tableline">
                <div v-for="(item, i) in registerTableArr">
                  <div
                    class="linename"
                    :class="{ active: registerTableIndex === i }"
                    @click="fun(item, '亿元', 'register', i)"
                  >
                    <span class="line-text">{{ item }}</span>
                    <span class="check-img"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="top-dialog" v-show="topDialogVisible">
            <div class="dialog-header">
              <span>{{ topTitle }}</span>
            </div>
            <div class="dialog-content">
              <ul>
                <li
                  v-for="(item, i) in topDialogArr"
                  :key="i"
                  :class="{ one: i === 0, Two: i === 1, Tree: i === 2 }"
                >
                  <span class="rank">{{ 'NO.' + (i + 1) }}</span>
                  <span class="name">{{ item.name }}</span>
                  <span class="num">{{ item.num + '户' }}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="middle-header">
          <div class="line">
            <div class="text">市场主体总量</div>
            <div
              style="
                display: flex;
                justify-content: flex-end;
                margin-top: 15px;
                align-items: center;
              "
            >
              <div class="number">
                <!-- {{data1}} -->
                <dv-digital-flop
                  :config="{number:[data1],style:{
                        fontSize: 80,
                        gradientColor: ['#f0b55f', '#ffffff'],
                        gradientParams: [0, 80, 0, 0],
                        gradientWith: 'fill',
                        fontWeight: 800
                  }}"
                  style="height: 80px"
                />
              </div>
              <div class="text">万</div>
            </div>
          </div>
          <div class="line">
            <div class="text">放心消费指数</div>
            <div
              style="
                display: flex;
                justify-content: flex-end;
                align-items: center;
              "
            >
              <div class="number" style="padding: unset">
                <!-- {{data3}} -->
                <dv-digital-flop
                  :config="{number:[data3],toFixed:2,style:{
                      fontSize: 80,
                      gradientColor: ['#f0b55f', '#ffffff'],
                      gradientParams: [0, 80, 0, 0],
                      gradientWith: 'fill',
                      fontWeight: 800
                }}"
                  style="height: 80px; width: 200px"
                />
              </div>
            </div>
          </div>
          <div class="line">
            <div class="text">主体总数全省排名</div>
            <div class="num">第2名</div>
          </div>
          <div class="line">
            <div class="text">营商环境全省排名</div>
            <div class="num">第4名</div>
          </div>
        </div>
        <div class="middle-header" v-show="scztxxShow" style="margin-top:160px;">
          <div class="line">
            <div class="text s-font-30">2019年</div>
            <div class="num">第2名</div>
          </div>
          <div class="line">
            <div class="text s-font-30">2020年</div>
            <div class="num">第3名</div>
          </div>
          <div class="line s-font-30">
            <div class="text">2021年</div>
            <div class="num">第3名</div>
          </div>
          <div class="line s-font-30">
            <div class="text">2022年</div>
            <div class="num">第1名</div>
          </div>
        </div>
      </div>
    </div>
    <script>
      var vm = new Vue({
        el: '#app',
        data: {
          scztxxShow:false,
          infoTableToggleFlag: true,
          infoTableToggleFlag1: true,
          registerTableToggleFlag: true,
          topDialogVisible: false,
          topDialogArr: [],
          qyTableArr:[
            '小微企业',
            '民营企业',
            '外资企业',
            '上市公司',
          ],
          jyTableArr:[
            '营业收入总额',
            '盈亏',
            '放心消费指数',
            '营商环境',
            '双随机一公开',
          ],
          infoTableArr: [
            '休闲旅游服务',
            '信息网络经济',
            '先进装备制造',
            '文化影视时尚',
            '生物医疗健康',
          ],
          registerTableArr: [
            '休闲旅游服务',
            '信息网络经济',
            '先进装备制造',
            '文化影视时尚',
            '生物医疗健康',
          ],
          qyTableIndex:-1,
          jyTableIndex:-1,
          infoTableIndex: -1,
          registerTableIndex: -1,
          topTitle: '',
          title: '',
          unit: '',
          titleUnit: '',
          data1: 0, //市场主体总量
          data2: 0, //主体总数全省排名
          data3: 0, //放心消费指数
          data4: 0, //营商环境全省排名
          data5: '', //千亿产业户数
          data0: [],
        },
        mounted() {
          this.data1 = 145 //市场主体总量
          this.data2 = 2 //主体总数全省排名
          this.data3 = 0.85 //放心消费指数
          this.data4 = 4 //营商环境全省排名
          // 默认给信息网络经济打点
          this.fun('信息网络经济', '户', 'info', 1)
          this.flyTo()

          top.emiter && top.emiter.on('topMap',(res)=>{
              console.log(res)
              this.scztxxShow = res
          })
        },
        methods: {
          func1(item,i){
            this.rm3Dtext();
            this.rmHistogram()
            this.jyTableIndex = i
            this.qyTableIndex = -1
            this.infoTableIndex= -1
            this.registerTableIndex = -1

            if(i==0){
              this.Histogram1()
            }else if(i==1){
              this.Histogram2()
            }else if(i==2){
              this.Histogram3()
            }else if(i==3){
              this.Histogram4()
            }else if(i==4){
              this.Histogram5()
            }
            
          },
          func2(item,i){
            this.rm3Dtext();
            this.rmHistogram()
            this.qyTableIndex = i
            this.jyTableIndex = -1
            this.infoTableIndex= -1
            this.registerTableIndex = -1

            if(i==0){
              this.HistogramYq1()
            }else if(i==1){
              this.HistogramYq2()
            }else if(i==2){
              this.HistogramYq3()
            }else if(i==3){
              this.HistogramYq4()
            }
          },

          //区划地图柱状图
          HistogramYq1(){
              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  "funcName":"Histogram" , //功能名称
                  "HistogramData":  [   //name 对应的区划，num柱状图高度 ，unit 展示单位
                      {name: '浦江县',num:10, unit: '万'}, 
                      {name: '兰溪市',num:5, unit: '万'}, 
                      {name: '婺城区',num:8, unit: '万'},
                      {name: '金义新区',num:7, unit: '万'}, 
                      {name: '义乌市',num:3, unit: '万'}, 
                      {name: '武义县',num:12, unit: '万'}, 
                      {name: '永康市',num:5, unit: '万'}, 
                      {name: '东阳市',num:9, unit: '万'}, 
                      {name: '磐安县',num:4.3, unit: '万'}
                  ],
                  color:[
                    1,"rgba(255,133,179,1)",
                    2,"rgba(255,133,179,1)",
                    3,"rgba(255,133,179,1)",
                    4,"rgba(255,133,179,1)",
                    5,"rgba(255,133,179,1)",
                    6,"rgba(255,133,179,1)",
                    7,"rgba(255,133,179,1)",
                    8,"rgba(255,133,179,1)",
                    9,"rgba(255,133,179,1)","rgba(255,133,179,1)"
                  ]
                })
              );
            },
            HistogramYq2(){
              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  "funcName":"Histogram" , //功能名称
                  "HistogramData":  [   //name 对应的区划，num柱状图高度 ，unit 展示单位
                      {name: '浦江县',num:2.5, unit: '万'}, 
                      {name: '兰溪市',num:6, unit: '万'}, 
                      {name: '婺城区',num:7, unit: '万'},
                      {name: '金义新区',num:8, unit: '万'}, 
                      {name: '义乌市',num:9, unit: '万'}, 
                      {name: '武义县',num:10, unit: '万'}, 
                      {name: '永康市',num:15, unit: '万'}, 
                      {name: '东阳市',num:3, unit: '万'}, 
                      {name: '磐安县',num:4, unit: '万'}
                  ],
                  color:[
                    1,"rgba(34,123,179,1)",
                    2,"rgba(34,123,179,1)",
                    3,"rgba(34,123,179,1)",
                    4,"rgba(34,123,179,1)",
                    5,"rgba(34,123,179,1)",
                    6,"rgba(34,123,179,1)",
                    7,"rgba(34,123,179,1)",
                    8,"rgba(34,123,179,1)",
                    9,"rgba(34,123,179,1)","rgba(34,123,179,1)"
                  ]
                })
              );
            },
            HistogramYq3(){
              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  "funcName":"Histogram" , //功能名称
                  "HistogramData":  [   //name 对应的区划，num柱状图高度 ，unit 展示单位
                      {name: '浦江县',num:1.3, unit: '万 ★'}, 
                      {name: '兰溪市',num:0.9, unit: '万'}, 
                      {name: '婺城区',num:0.8, unit: '万'},
                      {name: '金义新区',num:2.0, unit: '万'}, 
                      {name: '义乌市',num:3, unit: '万'}, 
                      {name: '武义县',num:0.6, unit: '万'}, 
                      {name: '永康市',num:0.9, unit: '万 ★'}, 
                      {name: '东阳市',num:0.4, unit: '万 ★'}, 
                      {name: '磐安县',num:1.2, unit: '万 ★'}
                  ],
                  color:[
                    1,"rgba(64,158,255,1)",
                    2,"rgba(64,158,255,1)",
                    3,"rgba(64,158,255,1)",
                    4,"rgba(64,158,255,1)",
                    5,"rgba(64,158,255,1)",
                    6,"rgba(64,158,255,1)",
                    7,"rgba(64,158,255,1)",
                    8,"rgba(64,158,255,1)",
                    9,"rgba(64,158,255,1)","rgba(64,158,255,1)"
                  ]
                })
              );
            },
            HistogramYq4(){
              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  "funcName":"Histogram" , //功能名称
                  "HistogramData":  [   //name 对应的区划，num柱状图高度 ，unit 展示单位
                      {name: '浦江县',num:0.9, unit: '万'}, 
                      {name: '兰溪市',num:0.2, unit: '万'}, 
                      {name: '婺城区',num:0.6, unit: '万'},
                      {name: '金义新区',num:0.5, unit: '万'}, 
                      {name: '义乌市',num:0.4, unit: '万'}, 
                      {name: '武义县',num:0.5, unit: '万'}, 
                      {name: '永康市',num:0.4, unit: '万'}, 
                      {name: '东阳市',num:0.3, unit: '万'}, 
                      {name: '磐安县',num:0.6, unit: '万'}
                  ],
                  color:[
                    1,"rgba(64,158,1,1)",
                    2,"rgba(64,158,1,1)",
                    3,"rgba(64,158,1,1)",
                    4,"rgba(64,158,1,1)",
                    5,"rgba(64,158,1,1)",
                    6,"rgba(64,158,1,1)",
                    7,"rgba(64,158,1,1)",
                    8,"rgba(64,158,1,1)",
                    9,"rgba(64,158,1,1)","rgba(64,158,1,1)"
                  ]
                })
              );
            },

          //区划地图柱状图
          Histogram1(){
              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  "funcName":"Histogram" , //功能名称
                  "HistogramData":  [   //name 对应的区划，num柱状图高度 ，unit 展示单位
                      {name: '浦江县',num:50, unit: '个 第5名'}, 
                      {name: '兰溪市',num:56, unit: '个 第4名'}, 
                      {name: '婺城区',num:40, unit: '个 第6名'},
                      {name: '金义新区',num:78, unit: '个 第1名'}, 
                      {name: '义乌市',num:77, unit: '个 第2名'}, 
                      {name: '武义县',num:65, unit: '个 第3名'}, 
                      {name: '永康市',num:24, unit: '个 第9名'}, 
                      {name: '东阳市',num:37, unit: '个 第7名'}, 
                      {name: '磐安县',num:32, unit: '个 第8名'}
                  ] 
                })
              );
            },
            Histogram2(){
              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  "funcName":"Histogram" , //功能名称
                  "HistogramData":  [   //name 对应的区划，num柱状图高度 ，unit 展示单位
                      {name: '浦江县',num:50, unit: '个 第5名'}, 
                      {name: '兰溪市',num:66, unit: '个 第4名'}, 
                      {name: '婺城区',num:40, unit: '个 第6名'},
                      {name: '金义新区',num:88, unit: '个 第1名'}, 
                      {name: '义乌市',num:87, unit: '个 第2名'}, 
                      {name: '武义县',num:85, unit: '个 第3名'}, 
                      {name: '永康市',num:24, unit: '个 第9名'}, 
                      {name: '东阳市',num:37, unit: '个 第7名'}, 
                      {name: '磐安县',num:32, unit: '个 第8名'}
                  ] 
                })
              );
            },
            Histogram3(){
              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  "funcName":"Histogram" , //功能名称
                  "HistogramData":  [   //name 对应的区划，num柱状图高度 ，unit 展示单位
                      {name: '浦江县',num:26, unit: '个 ★'}, 
                      {name: '兰溪市',num:45, unit: '个'}, 
                      {name: '婺城区',num:41, unit: '个'},
                      {name: '金义新区',num:68, unit: '个'}, 
                      {name: '义乌市',num:84, unit: '个'}, 
                      {name: '武义县',num:53, unit: '个'}, 
                      {name: '永康市',num:24, unit: '个 ★'}, 
                      {name: '东阳市',num:37, unit: '个 ★'}, 
                      {name: '磐安县',num:32, unit: '个 ★'}
                  ] 
                })
              );
            },
            Histogram4(){
              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  "funcName":"Histogram" , //功能名称
                  "HistogramData":  [   //name 对应的区划，num柱状图高度 ，unit 展示单位
                      {name: '浦江县',num:52, unit: '个'}, 
                      {name: '兰溪市',num:72, unit: '个'}, 
                      {name: '婺城区',num:22, unit: '个'},
                      {name: '金义新区',num:65, unit: '个'}, 
                      {name: '义乌市',num:83, unit: '个'}, 
                      {name: '武义县',num:49, unit: '个'}, 
                      {name: '永康市',num:63, unit: '个'}, 
                      {name: '东阳市',num:84, unit: '个'}, 
                      {name: '磐安县',num:95, unit: '个'}
                  ] 
                })
              );
            },
            Histogram5(){
              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  "funcName":"Histogram" , //功能名称
                  "HistogramData":  [   //name 对应的区划，num柱状图高度 ，unit 展示单位
                      {name: '浦江县',num:84, unit: '个'}, 
                      {name: '兰溪市',num:64, unit: '个'}, 
                      {name: '婺城区',num:45, unit: '个'},
                      {name: '金义新区',num:36, unit: '个'}, 
                      {name: '义乌市',num:45, unit: '个'}, 
                      {name: '武义县',num:73, unit: '个'}, 
                      {name: '永康市',num:56, unit: '个'}, 
                      {name: '东阳市',num:42, unit: '个'}, 
                      {name: '磐安县',num:61, unit: '个'}
                  ] 
                })
              );
            },

            //清除柱状体
            rmHistogram(){
              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  "funcName":"rmHistogram" , //清除柱状体
                })
              )
            },
          fun(data, type, tableType, index) {
            this.jyTableIndex = -1
            this.qyTableIndex = -1
            let that=this
            this.updateTable(data, type)
            fetch('/static/data/data.json', {
              method: 'GET',
              mode: 'cors',
              headers: new Headers({
                Accept: 'application/json',
              }),
            })
              .then((res) => res.json())
              .then((e) => {
                console.log('信息=>', data, type, tableType, index)
                that.rm3Dtext()
                if (type == '户') {
                  if (data == '五大千亿产业户数') {
                    e[data].forEach((e) => {
                      top.document
                        .getElementById('map')
                        .contentWindow.Work.funChange(JSON.stringify(e))
                    })
                  } else {
                    e[`市场监管_首页_${data}_人口`].forEach((e) => {
                      top.document
                        .getElementById('map')
                        .contentWindow.Work.funChange(JSON.stringify(e))
                    })
                  }
                } else if (type == '亿元') {
                  if (data == '五大千亿产业注册资本') {
                    e[data].forEach((e) => {
                      top.document
                        .getElementById('map')
                        .contentWindow.Work.funChange(JSON.stringify(e))
                    })
                  } else {
                    e[`市场监管_首页_${data}_资本`].forEach((e) => {
                      top.document
                        .getElementById('map')
                        .contentWindow.Work.funChange(JSON.stringify(e))
                    })
                  }
                } else {
                  e[data].forEach((e) => {
                    top.document
                      .getElementById('map')
                      .contentWindow.Work.funChange(JSON.stringify(e))
                  })
                }
              })

            fetch('/static/data/market.json', {
              method: 'GET',
              mode: 'cors',
              headers: new Headers({
                Accept: 'application/json',
              }),
            })
              .then((res) => res.json())
              .then((e) => {
                this.topDialogArr = []

                this.topTitle = data

                const resArr = e[data].data

                resArr.forEach((item) => {
                  this.topDialogArr.push({
                    name: item.name,
                    num: item.value,
                  })
                })
              })

            this.topDialogVisible = true

            this[
              tableType === 'info' ? 'infoTableIndex' : 'registerTableIndex'
            ] = index

            this[
              tableType === 'info' ? 'registerTableIndex' : 'infoTableIndex'
            ] = -1
          },

          toggleProductPanel1() {
            $('#jy-table .table-content').slideToggle(500)

            this.infoTableToggleFlag1 = !this.infoTableToggleFlag1
          },

          /*
           * 五大千亿产品户数面板展开收起切换
           */
          toggleProductPanel() {
            $('#infoTable .table-content').slideToggle(500)

            this.infoTableToggleFlag = !this.infoTableToggleFlag
          },

          /*
           * 五大千亿产业注册资本面板展开收起切换
           */
          toggleRegisterPanel() {
            $('.register-table .table-content').slideToggle(500)

            this.registerTableToggleFlag = !this.registerTableToggleFlag
          },

          updateTable(data, type) {
            console.log('zzz', data, type)
            fetch('/static/data/market.json', {
              method: 'GET',
              mode: 'cors',
              headers: new Headers({
                Accept: 'application/json',
              }),
            })
              .then((res) => res.json())
              .then((e) => {
                this.title = e[data]['title']
                this.unit = type
                if (e[data]['total']) {
                  this.data5 = e[data]['total']
                  this.titleUnit = type
                } else {
                  this.data5 = ''
                  this.titleUnit = ''
                }
                if (type == '户') {
                  this.data0 = e[data]['data']
                } else if (type == '亿元') {
                  this.data0 = e[data]['data1']
                }
              })
          },
          
          //清除柱状图+弹窗
          rm3Dtext() {
            top.document.getElementById('map').contentWindow.Work.funChange(
              JSON.stringify({
                funcName: 'rm3Dtext',
              })
            )
          },

          //飞入
          flyTo(){
            top.document.getElementById("map").contentWindow.Work.change3D(9)
            top.document.getElementById('map').contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "flyto", //功能名称
                flyData: {
                  center: [119.95478050597587, 29.01613226366889],
                  zoom: 10.5,
                  pitch: 40,
                  bearing: 0,
                },
              })
            )
          },

          
        

        },
      })
      // var updateTable = function f(data, type) {
      //   console.log('zzz', data, type)
      //   fetch('/static/data/market.json', {
      //     method: 'GET',
      //     mode: 'cors',
      //     headers: new Headers({
      //       Accept: 'application/json',
      //     }),
      //   })
      //     .then((res) => res.json())
      //     .then((e) => {
      //       app.title = e[data]['title']
      //       app.unit = type
      //       if (e[data]['total']) {
      //         app.data5 = e[data]['total']
      //         app.titleUnit = type
      //       } else {
      //         app.data5 = ''
      //         app.titleUnit = ''
      //       }
      //       if (type == '户') {
      //         app.data0 = e[data]['data']
      //       } else if (type == '亿元') {
      //         app.data0 = e[data]['data1']
      //       }
      //     })
      // }
      // window.updateTable = updateTable
    </script>
  </body>
  <style>
    ::-webkit-scrollbar {
      width: 0;
    }

    body {
      width: 3427px;
      height: 888px;
    }

    #app {
      position: absolute;
      left: 2150px;
      top: 230px;
    }

    .box {
      /* height: 888px;
      line-height: 888px; */
      /* display: flex; */
      justify-content: space-between;
      align-items: center;
      /*position: absolute;*/
      /*left: 2750px;*/
    }

    .line {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding: 20px 0 20px 0px;
    }

    .text {
      font-family: SourceHanSansCN-Medium;
      font-size: 50px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 50px;
      letter-spacing: 2px;
      background: linear-gradient(to bottom, #e7f0fd, #accbee);
      -webkit-background-clip: text;
      color: transparent;
      text-align: center;
      white-space: nowrap;
    }

    .number {
      font-family: Square721BT-Roman;
      font-size: 80px;
      font-weight: bold;
      font-stretch: normal;
      line-height: 80px;
      letter-spacing: 0px;
      background: linear-gradient(to bottom, #f0b55f, #ffffff);
      -webkit-background-clip: text;
      color: transparent;
      text-align: center;
      white-space: nowrap;
      padding: 0 30px 0 30px;
    }

    .table {
      width: 500px;
      background-color: #091e35;
      box-shadow: 0px 3px 35px 0px #000000;
      border-style: solid;
      border-width: 2px;
      border-image-source: linear-gradient(-32deg, #359cf8 0%, #afdcfb 100%);
      border-image-slice: 1;
    }

    .tabletitle {
      width: 500px;
      height: 68px;
      background-image: linear-gradient(
          0deg,
          rgba(0, 89, 147, 0.9) 0%,
          rgba(0, 32, 52, 0.9) 100%
        ),
        linear-gradient(
          0deg,
          rgba(103, 200, 255, 0.2) 0%,
          rgba(110, 176, 231, 0.1) 100%
        );
      background-blend-mode: normal, normal;
      display: flex;
      align-items: center;
    }

    .tableline {
      width: 500px;
      /* height: 58px;  */
      /* border-bottom: 1px solid #a8d8fb; */
      /* margin: 30px 0 0 72px; */
      /* display: flex; */
      justify-content: space-between;
      align-items: center;
    }

    .titletext {
      display: flex;
      align-items: center;
      font-family: SourceHanSansCN-Bold;
      font-size: 32px;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 1px;
      color: #d6e7f9;
      text-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.6);
    }

    .titlenumber {
      font-family: BebasNeue;
      font-size: 62px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 45px;
      letter-spacing: 1px;
      background: linear-gradient(
        to bottom,
        #ffd8a1,
        #fffcf3,
        #ffb637,
        #ffdb9b,
        #ffffff
      );
      -webkit-background-clip: text;
      color: transparent;
      text-shadow: 0px 2px 27px 0px rgba(0, 0, 0, 0.67);
      margin-top: 12px;
    }

    .linename {
      height: 48px;
      line-height: 44px;
      margin: 0px 10px 0px 30px;
      padding: 8px 0 6px 40px;
      font-family: SourceHanSansCN-Regular;
      font-size: 30px;
      font-weight: normal;
      font-stretch: normal;
      letter-spacing: 0px;
      color: #d6e7f9;
    }

    .linenumber {
      font-family: SourceHanSansCN-Medium;
      font-size: 36px;
      font-weight: normal;
      font-stretch: normal;
      line-height: 34px;
      letter-spacing: 0px;
      background: linear-gradient(to bottom, #c2e5ff, #ffffff);
      -webkit-background-clip: text;
      color: transparent;
      text-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.6);
    }

    .middle-header {
      position: absolute;
      top: 0px;
      left: 315px;
      display: flex;
      width: 2693px;
      height: 144px;
      background-color: #03101f;
      border-style: solid;
      border-width: 2px;
      border-image-source: linear-gradient(0deg, #3975bb 0%, #22436a 100%);
      border-image-slice: 1;
      opacity: 0.8;
    }

    .middle-header .line {
      width: 25%;
      padding-left: 40px;
    }

    .middle-header .line .text {
      font-size: 40px;
      color: #fff;
    }

    .middle-header .line .num {
      font-family: SourceHanSansCN-Medium;
      font-size: 60px;
      background: linear-gradient(to bottom, #ffffff, #ffc2c2, #ff4949);
      -webkit-background-clip: text;
      color: transparent;
      font-weight: 600;
      margin-left: 20px;
    }

    #infoTable {
      position: absolute;
      top: 1050px;
      background-color: #132c4e;
    }

    #infoTable .titletext img:first-child,
    .register-table .titletext img:first-child {
      width: 26px;
      height: 30px;
      margin: 0 20px 0 10px;
    }

    #infoTable .titletext img.flag-img {
      margin-left: 100px;
    }

    .register-table .titletext img.flag-img {
      margin-left: 35px;
    }

    .register-table {
      position: absolute;
      top: 1450px;
      background-color: #132c4e;
    }
    #jy-table {
      position: absolute;
      top: 645px;
      background-color: #132c4e;
    }
    #qy-table {
      position: absolute;
      top: 300px;
      background-color: #132c4e;
    }
    .linename .check-btn {
      position: relative;
      float: right;
      width: 26px;
      height: 26px;
      margin: 8px 33px 0 0;
      border: solid 2px #cde7fe;
      border-radius: 50%;
    }

    .linename.active {
      background: #244477;
    }

    .linename.active .line-text {
      background: linear-gradient(to bottom, #f0b55f, #ffffff);
      -webkit-background-clip: text;
      color: transparent;
    }

    .linename.active .check-btn {
      border-color: #f0b55f;
    }

    .linename.active .check-btn::before {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      content: '';
      width: 12px;
      height: 12px;
      background: #f0b55f;
      border-radius: 50%;
    }

    .linename .check-img {
      position: relative;
      float: right;
      width: 26px;
      height: 26px;
      background: url('/static/citybrain/scjg/img/sy/table_uncheck_bg.png')
        no-repeat;
      margin: 8px 33px 0 0;
    }

    .linename.active .check-img {
      background: url('/static/citybrain/scjg/img/sy/table_check_bg.png')
        no-repeat;
    }

    .top-dialog {
      position: absolute;
      left: 2770px;
      top: 1030px;
      width: 605px;
      background-color: rgba(9, 30, 53, 0.9);
    }

    ul,
    li {
      list-style: none;
    }

    .top-dialog .dialog-header {
      width: 605px;
      height: 84px;
      background-image: linear-gradient(
          0deg,
          rgba(0, 89, 147, 0.9) 0%,
          rgba(0, 32, 52, 0.9) 100%
        ),
        linear-gradient(
          0deg,
          rgba(103, 200, 255, 0.2) 0%,
          rgba(110, 176, 231, 0.1) 100%
        );
      background-blend-mode: normal, normal;
    }

    .top-dialog .dialog-header span {
      font-family: SourceHanSansCN-Bold;
      font-size: 48px;
      line-height: 84px;
      background: linear-gradient(to bottom, #c2e5ff, #ffffff);
      -webkit-background-clip: text;
      color: transparent;
      font-weight: 600px;
      margin-left: 30px;
    }

    .top-dialog .dialog-content ul li.one {
      width: 547px;
      height: 46px;
      margin: 30px auto;
      background: url('/static/citybrain/scjg/img/sy/dialog_li_1_bg.png');
    }

    .top-dialog .dialog-content ul li.Two {
      width: 547px;
      height: 46px;
      margin: 30px auto;
      background: url('/static/citybrain/scjg/img/sy/dialog_li_2_bg.png');
    }

    .top-dialog .dialog-content ul li.Three {
      width: 547px;
      height: 46px;
      margin: 30px auto;
      background: url('/static/citybrain/scjg/img/sy/dialog_li_3_bg.png');
    }

    .top-dialog .dialog-content ul li {
      width: 547px;
      height: 46px;
      margin: 30px auto;
      background: url('/static/citybrain/scjg/img/sy/dialog_li_4_bg.png');
    }

    .top-dialog .dialog-content li .rank {
      margin-left: 20px;
      font-family: BebasNeue;
      font-size: 36px;
      color: #fff;
      opacity: 0.7;
    }

    .top-dialog .dialog-content li .name {
      margin-left: 20px;
      font-family: SourceHanSansCN-Medium;
      font-size: 32px;
      color: #d6e7f9;
    }

    .top-dialog .dialog-content li .num {
      float: right;
      margin-right: 40px;
      font-family: SourceHanSansCN-Medium;
      font-size: 34px;
      background: linear-gradient(to bottom, #f0b55f, #ffffff);
      -webkit-background-clip: text;
      color: transparent;
      font-weight: 600;
    }
  </style>
</html>
