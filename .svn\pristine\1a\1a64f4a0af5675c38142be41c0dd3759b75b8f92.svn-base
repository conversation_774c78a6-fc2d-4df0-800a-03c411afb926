<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <title>食品药品监管分析底部</title>
  <script src="/Vue/vue.js"></script>
  <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
  <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/citybrain/scjg/css/spypjg-bottom.css" />
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
  <!-- 轮播toolTip -->
</head>

<body>
  <div id="app" class="container" v-cloak>
    <nav style="position: relative;">
      <s-header-title-2 htype="1" title="食品药品抽检信息">
        </s-header-title>

    </nav>
    <div class="xq-btn" @click="openIframe">详情</div>
    <div class="cjxx-con">
      <!-- 抽检监测概况统计展示 -->
      <div class="cjjc">
        <nav>
          <s-header-title-2 htype="1" title="抽检监测概况统计展示">
            </s-header-title>
        </nav>
        <div id="chart1"></div>
      </div>
      <!-- 食品药品检查总次数统计展示-->
      <div class="spyj">
        <nav>
          <s-header-title-2 htype="1" title="食品药品检查总次数统计展示">
            </s-header-title>
        </nav>
        <div class="s-c-blue-gradient s-font-25 s-text-center">当年检查总次数:2876次 同比:20%</div>
        <div class="tab-con">
          <li v-for="(item,index) in tabList" :key="index" @click="clickTab(index,item)"
            :class="currentIndex===index?'active':''">
            {{item}}
          </li>
        </div>
        <div id="chart2"></div>
      </div>
    </div>
  </div>
</body>
<script type="module">
  new Vue({
    el: "#app",
    data: {
      tabList: ["年度", "季度", "月度"],
      currentIndex: 0,
    },
    methods: {
      //切换点击
      clickTab(index, data) {
        this.currentIndex = index;
        this.getData(index + 1);
      },
      //查看详情
      openIframe() {
        let iframe1 = {
          type: "openIframe",
          name: "spypjg-dialog",
          src: baseURL.url + "/static/citybrain/scjg/commont/spypjg-dialog.html",
          width: "1255px",
          height: "855px",
          left: "3210px",
          top: "575px",
          zIndex: "100",
        };
        window.parent.postMessage(JSON.stringify(iframe1), "*");
      },
      //数据初始化
      init() {
        $api("spypjg_cjjczpc_bottom1").then((res) => {
          this.getChart01("chart1", res);
        });
        this.getData(1)
      },
      //获取食品药品检查总次数
      getData(index) {
        $api("spypjg_spypjc_bottom02", { 'type': index }).then((res) => {
          if(index==1){
              res.forEach(item => {
                  item.month=Number(item.month)+1
              });
          }else if(index==2){
              res.forEach(item => {
                  item.month=item.month.replace('2021','2023')
                  item.month=item.month.replace('2022','2024')
              });
          }
          this.getChart02("chart2", res);
        });
      },
      //绘制雷达图
      getChart01(id, chartData) {
        const myChart = echarts.init(document.getElementById(id));
        let data = chartData;
        let xdata = []; //横轴data
        let ydata1 = []; //纵轴data
        let ydata2 = []; //纵轴data
        data.forEach((item) => {
          xdata.push(item.name);
          ydata1.push(item.value);
        })
        let option = {
          legend: {
            trigger: 'item',
            orient: 'vertical',
            top: '40px',
            left: '50%',
            textStyle: {
              color: '#fff',
            },
          },
          tooltip: {
            trigger: "item",
            borderWidth: 0,
            borderRadius: 5,
            padding: 15,
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "#d0e1f5",
              fontSize: 35,
            },
          },
          radar: {
            indicator: [
              {
                text: "抽检监测总批次",
                max: 230,
              },
              {
                text: "涉及食品相关主体数",
                max: 230,
              },
              {
                text: "抽检覆盖率",
                max: 230,
              },
              {
                text: "抽检不合格率",
                max: 230,
              },
            ],
            center: ["54%", "50%"],
            radius: "75%",
            axisName: {
              color: "#fff",
              fontSize: 26,
            },
            splitArea: {
              show: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#118def",
              },
            },
            splitLine: {
              lineStyle: {
                color: "#118def",
              },
            },
          },
          series: [
            {
              name: "2022",
              type: "radar",
              lineStyle: {
                width: 4,
              },
              data: [
                {
                  value: ydata1,
                  symbolSize: 16,
                },
              ],
              symbolColor: "#fff",
              itemStyle: {
                color: "#0269CB",
              },
            },
          ]
        };
        myChart.setOption(option);
      },
      //绘制柱图
      getChart02(id, echartsData) {
        const myChartsRun = echarts.init(document.getElementById(id));
        let option = {
          tooltip: {
            trigger: "item",
            backgroundColor: "rgba(50,50,50,0.7)",
            formatter: "{b} </br>{a} {c}",
            borderColor: "rgba(50,50,50,0.7)",
            textStyle: {
              fontSize: 30,
              color: "#fff",
            },
          },
          grid: {
            left: "12%",
            right: "4%",
            bottom: "10%",
            top: "16%",
            containLabel: true,
          },
          legend: {
            itemWidth: 16,
            itemHeight: 16,
            textStyle: {
              color: "#fff",
              fontSize: "24",
            },
          },
          xAxis: {
            type: "category",
            data: echartsData.map((item) => item.month),
            offset: 10,
            axisLabel: {
              interval: 0, //强制全部显示
              // rotate: 15,
              textStyle: {
                fontSize: 25,
                color: "#fff",
              },
            },
          },
          yAxis: {
            type: "value",

            splitLine: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                fontSize: 30,
                color: "#fff",
              },
            },
          },
          series: [
            {
              name: "检查总次数",
              barMaxWidth: 45,
              data: echartsData.map((item) => item.value),
              type: "bar",
              label: {
                show: true,
                textStyle: {
                  color: "#5087ec",
                  fontSize: 30,
                },
                position: "outside",
              },
            },
          ],
        };
        myChartsRun.setOption(option);
        tools.loopShowTooltip(myChartsRun, option, {
          loopSeries: true,
        }); //轮播
      },
    },
    //项目生命周期
    mounted() {
      this.init();
    },
  });
</script>

</html>