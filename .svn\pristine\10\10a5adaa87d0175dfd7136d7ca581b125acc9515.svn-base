[v-cloak] {
  display: none;
}

html,
body,
ul,
p {
  padding: 0;
  margin: 0;
  list-style: none;
}

.container {
  width: 2070px;
  height: 1850px;
  background: url("/img/right-bg.png") no-repeat;
  background-size: 100% 100%;
  border-radius: 10px;
  padding: 30px 40px;
  box-sizing: border-box;
}

/* 切换tab样式 */
.top-tab {
  width: 100%;
  height: 60px;
  display: flex;
  flex-wrap: wrap;
  margin: 0px 20px;
}

.top-tab li {
  /* min-width: 200px; */
  border: 1px solid #116297;
  padding: 0px 20px;
  text-align: center;
  box-sizing: border-box;
  height: 100%;
  line-height: 56px;
  font-size: 28px;
  color: #fff;
  margin-top: 10px;
  cursor: pointer;
}

.top-tab li:first-child {
  border-radius: 10px 0px 0px 10px;
}

.top-tab li:last-child {
  border-radius: 0px 10px 10px 0px;
}

.tab-active {
  background-color: #2466a5;
}

.content {
  width: 100%;
  height: 80%;
  position: relative;
}

.warning-analysis {
  font-size: 28px;
  color: #fff;
  right: 100px;
  border: 1px solid #116297;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 220px;
  height: 50px;
  position: absolute;
  cursor: pointer;
}

/* 住院监控 */
.zyjk {
  width: 100%;
  height: 50%;
}

.top-part {
  width: 100%;
  height: 25%;
  display: flex;
  padding: 47px 200px;
  box-sizing: border-box;
}

.top-part li {
  width: 30%;
  height: 100%;
  display: flex;
}

.left-icon {
  width: 140px;
  height: 120px;
  background: url(/static/citybrain/ggfw/img/2.png) no-repeat;
  background-size: 100% 100%;
  margin-right: 90px;
  margin-top: -20px;
}

.split {
  width: 2px;
  height: 49px;
  background-color: #77b5f7;
  margin-top: 20px;
  margin-left: -21px;
  margin-right: 24px;
}

.img-right .name {
  font-size: 28px;
  /* background: linear-gradient(to top, #83b8ff, #74b4f4, #f0ffff);
  -webkit-background-clip: text; */
  color: #889aa0;
  font-weight: bold;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: 365px;
}

.img-right .value {
  font-size: 42px;
  background: linear-gradient(to top, #83b8ff, #74b4f4, #f0ffff);
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 800;
  text-align: center;
}

.img-right .value span {
  font-size: 20px;
  font-weight: 500;
  color: #74b4f4;
  margin-left: 10px;
}

.bottom-con {
  width: 100%;
  height: 85%;
  display: flex;
}

.bottom-con .left-part {
  width: 50%;
  height: 100%;
}

#zyjk-left-chart {
  width: 100%;
  height: 75%;

}

.bottom-con .right-part {
  width: 50%;
  height: 100%;
  position: relative;
}

#zyjk-right-chart {
  width: 95%;
  height: 70%;
}

/* 就诊监控 */
.jzjk {
  width: 100%;
  height: 50%;
}

.top-con {
  width: 100%;
  height: 25%;
  display: flex;
  flex-wrap: wrap;
  padding: 0px 20px;
  box-sizing: border-box;
  margin-bottom: 20px;
}

.top-con .title {
  display: inline-block;
  width: 360px;
  text-align: right;
}

.top-con li {
  list-style: none;
  display: flex;
  /* justify-content: space-between; */
  margin-top: 20px;
  display: flex;
  width: 33%;
}

.top-con span {
  font-size: 30px;
  color: #fff;
}

.num-bg {
  width: 245px;
  height: 57px;
  background-image: url(/static/citybrain/ggfw/img/title-bg.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-left: 20px;
  text-align: center;
  margin-top: -4px;
}

.num-bg .num {
  font-size: 40px;
  background: linear-gradient(to top, #83b8ff, #74b4f4, #f0ffff);
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 800;
}

.num-bg .unit {
  font-size: 20px;
  background: linear-gradient(to top, #83b8ff, #74b4f4, #f0ffff);
  -webkit-background-clip: text;
  color: transparent;
  font-weight: 500;
  margin-left: 10px;
}

#jzjk-left-chart,
#jzjk-right-chart {
  width: 100%;
  height: 85%;
}

/* 表格 */
.table {
  width: 100%;
  height: 93%;
  overflow-y: auto;
  padding: 10px 30px 50px;
  box-sizing: border-box;
}

.table1 .th {
  width: 100%;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-style: italic;
  font-weight: 700;
  font-size: 32px;
  line-height: 60px;
  background: #00396f;
  color: #FFFFFF;
}

.table1 .th_td {
  letter-spacing: 0px;
  text-align: center;
}

.table1 .tbody {
  width: 100%;
  height: calc(100% - 90px);
  /* overflow-y: auto; */
  overflow: hidden;
}

.table1 .tbody:hover {
  overflow-y: auto;
}

.table1 .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table1 .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table1 .tr {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 70px;
  line-height: 60px;
  font-size: 28px;
  color: #FFFFFF;
  cursor: pointer;
}

.table1 .tr:nth-child(2n) {
  background: rgb(0 57 111 / 30%);
}

.table1 .tr:nth-child(2n+1) {
  /* background: #035b86; */
}

.table1 .tr:hover {
  background-color: #6990b6;
}

.table1 .tr_td {
  letter-spacing: 0px;
  text-align: center;
  box-sizing: border-box;
}

.select {
  position: absolute;
  right: 60px;
  top: 180px;
  z-index: 99;
}

.selectOne {
  position: absolute;
  right: 60px;
  top: 180px;
  z-index: 99;
}

.trend-analysis {
  width: 180px;
  height: 50px;
  border: 1px solid #116297;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28px;
  color: #fff;
  cursor: pointer;
  top: 155px;
  position: absolute;
  right: 20px;
  z-index: 99;
}

.hospitalized-days {
  width: 260px;
  height: 50px;
  border: 1px solid #116297;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28px;
  color: #fff;
  bottom: 40px;
  position: absolute;
}

.selectTwo {
  top: 100px;
  position: absolute;
  right: 20px;
  z-index: 99;
}

.selectTwo .el-select {
  width: 160px !important;
}

.selectTwo .el-select .el-input__inner {
  width: 160px !important;
}

.selectTwo .el-select .el-input .el-select__caret {
  position: relative;
  left: -10px !important;
  font-size: 28px;
  color: #fff;
}

.selectOne .el-select {
  width: 190px;
}

.selectOne .el-select .el-input__inner {
  width: 240px !important;
}

/* .el-scrollbar {
  width: 235px;
} */

/* 下拉框 */
.el-select {
  width: 150px;
  margin-right: 20px;
}

.el-input__inner {
  height: 50px !important;
  width: 200px !important;
  background-color: #00487f;
  color: #fff;
  font-size: 28px;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: #00487f;
}

.el-select-dropdown__item {
  color: #fff;
  background-color: #00487f;
  font-size: 28px;
}

.el-select-dropdown__list {
  background-color: #00487f;
}

.el-select .el-input .el-select__caret {
  position: relative;
  left: 40px;
  font-size: 28px;
  color: #fff;
}

.box2-content {
  display: unset;
}

.el-select .el-input__inner {
  border-radius: 30px !important;
}

.el-scrollbar {
  min-width: 198px;
}