<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>质量技术监管-中间</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
</head>
<style>
    #container {
        width: 2000px;
        height: 260px;
        box-sizing: border-box;
        padding: 20px;
        /* background: url(/img/left-bg.png) no-repeat;
        background-size: 100% 100%; */
        display: flex;
        justify-content: space-between;
    }
    .tj-con{
        width: 600px;
        height: 215px;
        background: url(/static/citybrain/shgl/img/bg.svg) no-repeat;
        background-size: 100% 100%;
        padding: 35px;
        box-sizing: border-box;
    }
</style>

<body>
    <div id="container" v-cloak>
         <div class="tj-con" v-for="(item,index) in tjData" :key="index">
             <div class="s-c-white s-font-40 " style="line-height: 70px;">{{item.name}}</div>
             <div class="s-font-60 s-c-yellow-gradient">{{item.value}}<span class="s-font-30 s-c-yellow-gradient">起</span><span class="s-font-30 s-c-yellow-gradient" style="margin-left: 40px;">{{item.value1}}%</span></span></div>
         </div>
    </div>
</body>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>

<script>
    var vm = new Vue({
        el: "#container",
        data() {
            return {
              tjData:[
                  {  
                      name:"一级救援",
                      value:"243",
                      value1:"23"
                  },
                  {  
                      name:"二级救援",
                      value:"184",
                      value1:"15"
                  },
                  {  
                      name:"三级救援",
                      value:"67",
                      value1:"6"
                  },
              ],
            };
        },
        mounted() {
            $api("zljsjg_middle").then(res=>{
                this.tjData=res
            })
        },
        methods: {

        },

    });
</script>

</html>