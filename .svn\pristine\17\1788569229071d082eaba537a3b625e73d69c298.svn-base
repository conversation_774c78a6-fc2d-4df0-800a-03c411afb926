<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>人社服务-右</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
    <style>
      @font-face {
        font-family: num1;
        src: url("/static/fonts/时尚中黑简体.ttf");
      }
      .cl_gry {
        color: #b6c4c9;
      }
      .title_bg {
        display: inline-block;
        width: 60%;
        height: 80px;
        text-align: center;
        line-height: 80px;
        border-top: 2px solid #143958d8;
        background: #092035;
        font-size: 30px;
        color: #b6c4c9;
      }
      .dv-border-box-8 {
        cursor: pointer !important;
        width: 30% !important;
        height: 150px !important;
        border: 1px solid transparent !important;
      }
      .main_right {
        display: flex;
        margin: 20px;
        line-height: 60px;
      }
      .value {
        font-size: 50px;
        font-weight: 600;
        font-family: num1;
      }
      .active_box {
        border: 1px solid #338c9e !important;
        background-color: #338c9e46 !important;
      }
    </style>
  </head>
  <body>
    <div id="app" class="rsfw-left-main">
      <nav>
        <s-header-title title="人事服务" :data-time="nowTime"> </s-header-title>
      </nav>
      <div class="s-flex s-row-around">
        <div
          class="s-flex s-row-between s-col-center"
          style="width: 25%"
          v-for="item,i in rsfw"
        >
          <span class="cl_gry s-font-30">{{item.name}}</span>
          <span class="title_bg">
            <span
              class="s-c-blue-gradient1 s-font-50 s-w7"
              style="font-family: num1"
              >{{item.num}}</span
            >
            {{item.unit}}
          </span>
        </div>
      </div>
      <div id="bar_echarts01" style="width: 100%; height: 400px"></div>
      <nav class="s-m-t-20 s-m-b-20">
        <s-header-title title="劳动关系" :data-time="nowTime" :click-flag="true" @click="clickToMap(0)"> </s-header-title>
      </nav>
      <div class="s-flex s-row-around s-flex-wrap">
        <dv-border-box-8
          :class="click_index==index?'active_box s-m-b-20':'s-m-b-20'"
          v-for="(item,index) in ldgx"
          :index="index"
        >
          <div class="main_right" @click="ldgxClick(index)">
            <img
              src="/static/citybrain/ggfw/img/list.png"
              width="35px"
              height="35px"
              class="s-m-r-30"
            />
            <div>
              <div class="name s-font-38 cl_gry">{{item.name}}</div>
              <div class="value s-c-blue-gradient1">
                {{item.num}}<span class="s-w4 s-font-35">{{item.unit}}</span>
                <i :class="`${item.change ? 'el-icon-top' : 'el-icon-bottom'}`" :style="`font-size: 44px;color: ${item.change ? 'red' : 'green'}`"></i>
                <span class="s-w4 s-font-35">({{item.target}})</span>
              </div>
            </div>
          </div>
        </dv-border-box-8>
      </div>
      <div id="bar_echarts02" style="width: 100%; height: 400px"></div>
    </div>

    <script>
      new Vue({
        el: "#app",
        data: {
          click_index: 0,
          nowTime: "", //当前时间
          rsfw: [],
          ldgx: [],
        },
        mounted() {
          this.getTime();
          this.initApi();
        },
        methods: {
          initApi() {
            $api("/ggfw_rsfw_rsfw_right_dialog_rsfw").then((res) => {
              this.rsfw = res;
            });
            $api("/ggfw_rsfw_rsfw_right_dialog_rsfw_bar_echart").then(
              (res) => {
                this.getLine01("bar_echarts01", res);
              }
            );
            $api("/ggfw_rsfw_rsfw_right_dialog_ldgx").then((res) => {
              this.ldgx = res;
              this.ldgx[0].target = 3500;
              this.ldgx[1].target = 10000;
              this.ldgx[2].target = 200;
              this.ldgx[3].target = 300;
              this.ldgx[4].target = 7000;
              this.ldgx[5].target = 2000;
              this.ldgx[6].target = 30;
              this.ldgx[7].target = 12000;
              this.ldgx[8].target = 600;
              this.ldgx[0].change = true;
              this.ldgx[1].change = false;
              this.ldgx[2].change = false;
              this.ldgx[3].change = true;
              this.ldgx[4].change = true;
              this.ldgx[5].change = false;
              this.ldgx[6].change = true;
              this.ldgx[7].change = true;
              this.ldgx[8].change = false;
            });
            $api("/ggfw_rsfw_rsfw_right_dialog_ldgx_bar_echart").then(
              (res) => {
                this.getLine02("bar_echarts02", res);
              }
            );
          },
          //获取当前时间
          getTime() {
            var data = new Date();
            var yesterday = new Date(data.setDate(data.getDate() - 1));
            this.nowTime =
              yesterday.getFullYear() +
              "年" +
              (yesterday.getMonth() + 1) +
              "月" +
              yesterday.getDate() +
              "日";
          },

          clickToMap(index){
            top.emiter.emit('rsfw',index)
            this.closeDialog()
          },

          closeDialog() {
            top.commonObj.funCloseIframe({
                name: "rsfw-dialog",
            });
          },

          // 人事服务
          getLine01(dom, echartsData) {
            console.log(echartsData);
            let echarts0 = echarts.init(document.getElementById(dom));
            var xData = echartsData.map((item) => {
                return item.name;
              }),
              yData1 = echartsData.map((item) => {
                return item.value1;
              }),
              yData2 = echartsData.map((item) => {
                return item.value2;
              }),
              yData3 = echartsData.map((item) => {
                return item.value3;
              }),
              yData4 = echartsData.map((item) => {
                return item.value4;
              }),
              borderData = [],
              legend = [
                "求职人数-目标值",
                "求职人数-实际值",
                "供给岗位数量-目标值",
                "供给岗位数量-实际值",
              ],
              colorArr = [
                {
                  start: "rgba(71, 173, 245,1)",
                  end: "rgba(18, 58, 86,0.5)",
                },
                {
                  start: "rgba(218, 201, 126,1)",
                  end: "rgba(18, 58, 86,0.5)",
                },
                {
                  start: "rgba(86, 242, 161,1)",
                  end: "rgba(27, 87, 75,0.5)",
                },
                {
                  start: "rgba(243, 88, 88,1)",
                  end: "rgba(73, 42, 53,0.5)",
                },
              ];
              xData.push("金华市");
              yData1.push(3800);
              yData2.push(3600);
              yData3.push(3000);
              yData4.push(4000);
            var normalColor = ["rgba(255,255,255,0.9)","rgba(255,255,255,0.9)","rgba(255,255,255,0.9)","rgba(255,255,255,0.9)","rgba(255,255,255,0.9)","rgba(255,255,255,0.9)","rgba(255,255,255,0.9)","rgba(255,255,255,0.9)","rgba(255,255,255,0.9)",];
            let seriesData = [];
            var borderHeight = 4;
            xData.forEach((element) => {
              borderData.push(borderHeight);
            });
            yData1.forEach((item, index) => {
              if (Math.abs(item - yData2[index]) > 320) {
                normalColor[index]  = "red";
              }
            });
            yData3.forEach((item, index) => {
              if (Math.abs(item - yData4[index]) > 500) {
                normalColor[index]  = "red";
              }
            });
            [yData1, yData2, yData3, yData4].forEach((item, index) => {
              var obj1 = {};
              if (index < 4) {
                obj1 = {
                  name: legend[index],
                  type: "bar",
                  // stack: legend[index],
                  data: item,
                  barWidth: "12%",
                  itemStyle: {
                    normal: {
                      color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                          {
                            offset: 0,
                            color: colorArr[index].start + "0.7)",
                          },
                          {
                            offset: 0.5,
                            color: colorArr[index].start + "0.3)",
                          },
                          {
                            offset: 1,
                            color: colorArr[index].end,
                          },
                        ],
                        globalCoord: false,
                      },
                    },
                  },
                };
                seriesData.push(obj1);
              }
            });
            let option = {
              grid: {
                top: "20%",
                right: 0,
                left: "2%",
                bottom: 0,
                containLabel: true,
              },
              legend: {
                show: true,
                itemWidth: 30,
                icon: "square",
                itemGap: 100,
                itemHeight: 30,
                top: "2%",
                textStyle: {
                  color: "#fff",
                  fontSize: 30,
                },
                data: legend,
              },
              tooltip: {
                trigger: "axis",
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "30",
                },
                formatter: function (params) {
                  var str = "";
                  for (var i = 0; i < params.length; i++) {
                    if (params[i].seriesName !== "") {
                      str +=
                        params[i].name +
                        ":" +
                        params[i].seriesName +
                        params[i].value +
                        "<br/>";
                    }
                  }
                  return str;
                },
              },
              xAxis: [
                {
                  type: "category",
                  data: xData,
                  axisPointer: {
                    type: "shadow",
                  },
                  axisLabel: {
                    textStyle: {
                      color: (params, index) => {
                        return normalColor[index];
                      },
                      fontSize: 30,
                    },
                  },
                  axisLine: {
                    lineStyle: {
                      color: "#0e3a63",
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  splitLine: {
                    show: false,
                  },
                },
              ],
              yAxis: [
                {
                  type: "value",
                  name: "数量 个",
                  nameTextStyle: {
                    color: normalColor,
                    fontSize: 26,
                    padding: 10,
                  },
                  interval: 400,
                  axisLabel: {
                    formatter: "{value}",
                    textStyle: {
                      color: normalColor,
                      fontSize: 30,
                    },
                  },
                  axisLine: {
                    show: false,
                  },
                  axisTick: {
                    show: false,
                  },
                  splitLine: {
                    show: true,
                    lineStyle: {
                      color: "#0e3a63",
                    },
                  },
                },
              ],
              series: seriesData,
            };
            echarts0.setOption(option);
          },
          getLine02(dom, echartsData) {
            console.log(echartsData);
            let echarts0 = echarts.init(document.getElementById(dom));
            var xData = echartsData.map((item) => {
                return item.name;
              }),
              yData1 = echartsData.map((item) => {
                return item.value1;
              }),
              yData2 = echartsData.map((item) => {
                return item.value2;
              }),
              yData3 = echartsData.map((item) => {
                return item.value3;
              }),
              yData4 = echartsData.map((item) => {
                return item.value4;
              }),
              borderData = [],
              legend = ["数值", "阈值"],
              colorArr = [
                {
                  start: "rgba(71, 173, 245,1)",
                  end: "rgba(18, 58, 86,0.5)",
                },
                {
                  start: "rgba(218, 201, 126,1)",
                  end: "rgba(18, 58, 86,0.5)",
                },
              ];
            var normalColor = "rgba(255,255,255,0.9)";
            let seriesData = [];
            var borderHeight = 4;
            xData.forEach((element) => {
              borderData.push(borderHeight);
            });
            [yData1, yData2].forEach((item, index) => {
              var obj1 = {};
              if (index < 4) {
                obj1 = {
                  name: legend[index],
                  type: "bar",
                  // stack: legend[index],
                  data: item,
                  barWidth: "12%",
                  itemStyle: {
                    normal: {
                      color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                          {
                            offset: 0,
                            color: colorArr[index].start + "0.7)",
                          },
                          {
                            offset: 0.5,
                            color: colorArr[index].start + "0.3)",
                          },
                          {
                            offset: 1,
                            color: colorArr[index].end,
                          },
                        ],
                        globalCoord: false,
                      },
                    },
                  },
                };
                seriesData.push(obj1);
              }
            });
            let option = {
              grid: {
                top: "20%",
                right: 0,
                left: "2%",
                bottom: 0,
                containLabel: true,
              },
              legend: {
                show: true,
                itemWidth: 30,
                icon: "square",
                itemGap: 100,
                itemHeight: 30,
                top: "2%",
                textStyle: {
                  color: "#fff",
                  fontSize: 30,
                },
                data: legend,
              },
              tooltip: {
                trigger: "axis",
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "30",
                },
                formatter: function (params) {
                  var str = "";
                  for (var i = 0; i < params.length; i++) {
                    if (params[i].seriesName !== "") {
                      str +=
                        params[i].name +
                        ":" +
                        params[i].seriesName +
                        params[i].value +
                        "<br/>";
                    }
                  }
                  return str;
                },
              },
              xAxis: [
                {
                  type: "category",
                  data: xData,
                  axisPointer: {
                    type: "shadow",
                  },
                  axisLabel: {
                    textStyle: {
                      color: normalColor,
                      fontSize: 30,
                    },
                  },
                  axisLine: {
                    lineStyle: {
                      color: "#0e3a63",
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  splitLine: {
                    show: false,
                  },
                },
              ],
              yAxis: [
                {
                  type: "value",
                  name: "数量 个",
                  nameTextStyle: {
                    color: normalColor,
                    fontSize: 26,
                    padding: 10,
                  },
                  interval: 400,
                  axisLabel: {
                    formatter: "{value}",
                    textStyle: {
                      color: normalColor,
                      fontSize: 30,
                    },
                  },
                  axisLine: {
                    show: false,
                  },
                  axisTick: {
                    show: false,
                  },
                  splitLine: {
                    show: true,
                    lineStyle: {
                      color: "#0e3a63",
                    },
                  },
                },
              ],
              series: seriesData,
            };
            echarts0.setOption(option);
          },
          ldgxClick(index) {
            this.click_index=index;
            top.emiter.emit('ldgx',index);
            this.closeDialog();
          },
        },
      });
    </script>
  </body>
</html>
