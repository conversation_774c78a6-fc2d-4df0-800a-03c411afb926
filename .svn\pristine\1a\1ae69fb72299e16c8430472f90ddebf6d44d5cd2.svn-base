[v-cloak] {
  display: none;
}
html,
body,
ul,
p {
  padding: 0;
  margin: 0;
  list-style: none;
}
.container {
  width: 2070px;
  height: 1850px;
  background: url('/img/right-bg.png') no-repeat;
  background-size: 100% 100%;
  border-radius: 10px;
  padding: 30px 40px;
  box-sizing: border-box;
}
.first_content {
  display: flex;
  height: 750px;
  /* background-color: aqua; */
}
.first_content > div {
  height: 100%;
  width: 30%;
}
.right_part {
  width: 40% !important;
}
/* 表格 */
.table {
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
  overflow-y: auto;
  margin: 0 auto;
}

.table .th {
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-style: italic;
  font-weight: 700;
  font-size: 28px;
  line-height: 80px;
  background: #00396f;
  color: #ffffff;
}

.table .th_td {
  letter-spacing: 0px;
  text-align: center;
  flex: 0.25;
}

.table .tbody {
  width: 100%;
  height: calc(100% - 80px);
  overflow: hidden;
}

.table .tbody:hover {
  overflow-y: auto;
}

.table .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table .tr {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  line-height: 80px;
  font-size: 28px;
  color: #ffffff;
  cursor: pointer;
}

.table .tr:nth-child(2n) {
  background: #00396f;
}

.table .tr:nth-child(2n + 1) {
  background: #035b86;
}

.table .tr:hover {
  background-color: #6990b6;
}

.table .tr_td {
  letter-spacing: 0px;
  text-align: center;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.table_tabbar {
  display: flex;
  font-size: 28px;
  color: #fff;
  width: 100%;
  border-bottom: 2px solid #7c7979;
  justify-content: space-around;
}
.table_tabbar li {
  cursor: pointer;
  opacity: 0.6;
  padding-bottom: 10px;
}
.activeTab {
  opacity: 1 !important;
  color: #fff;
  border-bottom: 5px solid #fff;
}
.middle_part_content {
  /* margin-top: 20px; */
  padding: 50px 30px;
  display: flex;
  height: 85%;

  flex-wrap: wrap;
  justify-content: space-around;
  align-content: space-between;
}
.middle_part_content li {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33%;
  text-align: center;
  line-height: 54px;
  justify-content: center;
}
.small_title {
  color: #fff;
  font-size: 28px;
  margin-left: 50px;
  margin-top: 18px;
  margin-bottom: 18px;
}
.right_part_top {
  display: flex;
  flex-wrap: wrap;
  align-content: space-around;
}
.right_part_top li {
  width: 25%;
  text-align: center;
}
.right_part {
  width: 40%;
}
.second_con {
  height: 860px !important;
  display: flex;
  margin-top: 80px;
}
.second_con > div {
  width: 25%;
}
.item-num {
  margin-left: 20px;
  display: flex;
  align-items: flex-end;
  margin: 10px 0;
}
.item-num > span {
  margin-left: 10px;
  font-size: 28px;
}
.item-num > div {
  width: 44px;
  height: 64px;
  line-height: 52px;
  font-size: 50px;
  margin: 0 5px;
  padding: 5px;
  text-align: center;
  background-image: url('/static/citybrain/csdn/img/ywt/num-bg.png');
  background-size: 100% 100%;
}
.yel-color {
  background: linear-gradient(to bottom, #ffeccb, #f4f1ff, #ffe2b0, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}
.left-con::-webkit-scrollbar {
  width: 10px; /* 横向滚动条 */
  height: 20px; /* 纵向滚动条 必写 */
}
/* 滚动条的滑块 */
.left-con::-webkit-scrollbar-thumb {
  background-color: rgba(27, 146, 215, 1);
  border-radius: 10px;
}

.left-con {
  width: 95%;
  height: 610px;
  /* overflow: hidden; */
  overflow-y: scroll;
}

.left-con li {
  width: 100%;
  height: 45px;
  background-color: #2391ff;
  line-height: 45px;
  font-size: 28px;
  margin-bottom: 20px;
  color: #fff;
  padding: 0 10px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.right-con {
  width: 70%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
}

.btn_ul {
  display: flex;
  color: #2391ff;
  font-size: 28px;
  border: 1px solid #2391ff;
  border-radius: 8px;
  width: 200px;
}
.btn_ul li {
  width: 50%;
  height: 50px;
  text-align: center;
  line-height: 50px;
  cursor: pointer;
}

.btn_active {
  color: #fff;
  background-color: #20aeff;
}
.el-input__inner {
  background-color: unset;
  font-size: 28px;
  width: 200px;
  height: 50px;
  color: #fff;
}
.el-select-dropdown__list {
  background-color: #2e4562 !important;
}
.el-select-dropdown__item.selected,
.el-select-dropdown__item:hover,
.el-select-dropdown__item.hover {
  color: #fff;
  background: #409eff;
}
.el-select-dropdown__item {
  background-color: transparent;
  font-size: 28px;
  color: #20aeff;
}
.el-select .el-input .el-select__caret {
  font-size: 27px;
}
