/*
 * @Author: CK
 * @email: <EMAIL>
 * @Date: 2022-08-03 17:03:48
 * @LastEditTime: 2022-08-03 19:52:42
 * @FilePath: \jinhua\js\work\drawLine.js
 * @Description: 绘制线段
 */

import { creatUuid } from '../utils/index.js';
import { lineVariable } from '../globalVariable/mapFor2D.js'

let drawLinePointArray = []; // 点经纬度集合
let drwaLineArray = []; // 线经纬度集合 
let drawLineIsIcon = false; // 是否添加点击图标
let drawLineId = ''; // 线id
let drwaLineFristClick = true; // 是否第一次点击
let clickNum = 0; // 鼠标点击次数
let call = false;
let isMessage = false

function setPointLayer (e) {
    // 判断图标是否要加
    if (drawLineIsIcon) {
        drawLinePointArray.push(turf.point([e.lngLat.lng, e.lngLat.lat]))
        egis.getSource(`${drawLineId}-point`).setData({
            "type": "FeatureCollection",
            "features": drawLinePointArray
        });
    }
}

// 线点击事件
function lineClick (e) {
    clickNum++;
    // 判断是否第一次点击
    if (drwaLineFristClick) {
        setPointLayer(e)
        drwaLineArray.push([e.lngLat.lng, e.lngLat.lat]);
        drwaLineFristClick = false;
    } else {
        setPointLayer(e)
        drwaLineArray.pop();
        drwaLineArray.push([e.lngLat.lng, e.lngLat.lat]);
        const lineFeatures = turf.lineString(drwaLineArray);
        egis.getSource(`${drawLineId}-line`).setData(lineFeatures);
    }
}

// 线鼠标移动事件
function linMousemove (e) {
    if (!drwaLineFristClick) {
        if (drwaLineArray.length > clickNum) {
            drwaLineArray.pop();
        }
        drwaLineArray.push([e.lngLat.lng, e.lngLat.lat]);
        const lineFeatures = turf.lineString(drwaLineArray);
        egis.getSource(`${drawLineId}-line`).setData(lineFeatures);
    }
}

// 线鼠标右击事件
function contextmenu (e) {
    setPointLayer(e); // 添加图标

    egis.unce('click',lineClick);
    egis.unce('mousemove',linMousemove);
    egis.unce('contextmenu',contextmenu);

    const lineGeojson = turf.lineString(drwaLineArray);

    const obj = {
        linePos: drwaLineArray, // 所绘制线数据的经纬度
        id: drawLineId, // 线图层id
        length: turf.length(lineGeojson, {units: 'kilometers'})*1000, // 线的长度
    }

    drawLinePointArray = []; // 重置经纬度集合
    drwaLineArray = []; // 重置线经纬度集合 
    drawLineIsIcon = false; // 重置是否添加点击图标
    drawLineId = ''; // 重置线id
    drwaLineFristClick = true; // 重置是否第一次点击
    clickNum = 0; // 重置鼠标点击次数

    call && call(obj);
    call = false;

    isMessage && window.parent.postMessage({
        type: "drawLine",
        data: obj,
    },'*');
    isMessage = false;
}

function drawLine (data) {
    const id = data.id ? data.id : creatUuid(8,16);
    drawLineId = id;
    data.call && (call = data.call);
    data.isMessage && (isMessage = data.isMessage);
    lineVariable.drawLineLayerId.push(id)
    // 判断是否需要图标
    if (data.isIcon) {
        drawLineIsIcon = true;
        // 图标是否存在
        if (!egis.hasImage('linePointIcon')) {
            egis.loadImage('./asset/images/linePointIcon.png').then((image) => {
                egis.addImage('linePointIcon',image)
            })
        }
        // 创建点位图层
        const pointId = `${id}-point`
        const iconSize = data.iconSize ? data.iconSize : 0.5; // 设置图标大小
        const iconOffset = data.iconOffset ? data.iconOffset : [0,-90]; // 设置偏移量
        egis.addLayer({
            'id': pointId,
            'type': 'symbol',
            'source': {
                'type': 'geojson',
                'data': {
                  'type': 'FeatureCollection',
                  'features': {}
                }
            },
            'layout': {
              'icon-image': 'linePointIcon', // reference the image
              'icon-size': iconSize,
              'icon-offset': iconOffset
            }
        })
    }

    // 创建线图层
    const lineId = `${id}-line`;
    const lineColor = data.lineColor ? data.lineColor : '#fff'; // 线的颜色
    const lineWidth = data.lineWidth ? data.lineWidth : 5;
    egis.addLayer({
        'id': lineId,
        'type': 'line',
        'source': {
            'type': 'geojson',
            'data': {
              'type': 'FeatureCollection',
              'features': {}
            } 
        },
        "layout": {
            "line-join": "round",
            "line-cap": "round"
        },
        "paint": {
            "line-color": lineColor,
            "line-width": lineWidth
        }
    })
    
    egis.once('click',lineClick);
    egis.once('mousemove',linMousemove);
    egis.once('contextmenu',contextmenu);
}

export default drawLine
