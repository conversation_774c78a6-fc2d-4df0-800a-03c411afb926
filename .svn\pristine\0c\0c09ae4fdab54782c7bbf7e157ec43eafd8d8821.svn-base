<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>数字经济指标分析左侧面板</title>
        <script src="/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain3840/szhgg/css/szjjzbfx-right.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/hjbh/js/echarts-liquidfill.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <!-- 轮播toolTip -->
    </head>

    <body>
        <div id="app" class="container" v-cloak>
            <nav>
                <s-header-title-2 htype="1" title="未来工厂建设情况"></s-header-title-2>
            </nav>
            <div class="tj-con">
                <li v-for="(item,index) in tjData" :key="index">
                    <div class="title1">{{item.value}}{{item.unit}}</div>
                    <div class="title2">{{item.name}}</div>
                </li>
            </div>
            <nav>
                <s-header-title-2 htype="1" title="数字经济多跨场景建设推进情况"></s-header-title-2>
            </nav>
            <div class="btn">全省排名:第二名</div>
            <div class="szjj-con">
                <div id="szjj-chart1"></div>
                <div id="szjj-chart2"></div>
            </div>
            <el-select v-model="value" placeholder="月份" @change="change">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
            <div id="szjj-chart3"></div>
        </div>
    </body>
    <script type="module">
        new Vue({
            el: "#app",
            data: {
                tjData: [],
                value: "7月",
                options: [
                    {
                        value: "7月",
                        label: "7月",
                    },
                    {
                        value: "8月",
                        label: "8月",
                    },
                    {
                        value: "9月",
                        label: "9月",
                    },
                    {
                        value: "10月",
                        label: "10月",
                    },
                ],
            },
            methods: {
                init() {
                    $api("ldst_szhgg_szjj", { type: 8 }).then((res) => {
                        this.tjData = res;
                    });
                    $api("ldst_szhgg_szjj", { type: 9 }).then((res) => {
                        this.progresschartsShow(res);
                    });
                    $api("ldst_szhgg_szjj", { type: 10 }).then((res) => {
                        this.PiechartsShow(res);
                    });
                    $api("ldst_szhgg_szjj", { type: 11 }).then((res) => {
                        this.BarchartsShow(res);
                    });
                },
                change(item) {
                    if (item === "7月") {
                        $api("ldst_szhgg_szjj", { type: 11 }).then((res) => {
                            this.BarchartsShow(res);
                        });
                    } else if (item === "8月") {
                        $api("ldst_szhgg_szjj", { type: "11-1" }).then((res) => {
                            this.BarchartsShow(res);
                        });
                    } else if (item === "9月") {
                        $api("ldst_szhgg_szjj", { type: "11-2" }).then((res) => {
                            this.BarchartsShow(res);
                        });
                    } else if (item === "10月") {
                        $api("ldst_szhgg_szjj", { type: "11-3" }).then((res) => {
                            this.BarchartsShow(res);
                        });
                    }
                },
                //绘制柱图
                BarchartsShow(data) {
                    const myChartsState = echarts.init(document.getElementById("szjj-chart3"));
                    var fontColor = "#30eee9";
                    let x = data.map((item) => {
                        return item.name;
                    });
                    let y1 = data.map((item) => {
                        return item.sheng;
                    });
                    let y2 = data.map((item) => {
                        return item.shi;
                    });
                    let y3 = data.map((item) => {
                        return item.zzl;
                    });
                    let option = {
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        legend: {
                            // align: "right",
                            top: 0,
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                        },
                        grid: {
                            left: "3%",
                            right: "4%",
                            bottom: "3%",
                            containLabel: true,
                        },
                        xAxis: {
                            data: x,
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: "#aaa",
                                    width: 1,
                                },
                            },
                            axisTick: {
                                show: true, //隐藏X轴刻度
                                alignWithLabel: true,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff", //X轴文字颜色
                                    fontSize: 28,
                                },
                                interval: 0,
                                // rotate: 30,
                            },
                        },
                        yAxis: [
                            {
                                type: "value",
                                name: "",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        width: 1,
                                        color: "#3d5269",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                            {
                                type: "value",
                                name: "单位(%)",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                position: "right",
                                splitLine: {
                                    show: false,
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#fff",
                                        width: 2,
                                    },
                                },
                                axisLabel: {
                                    show: true,
                                    formatter: "{value} ", //右侧Y轴文字显示
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "全省最佳应用",
                                type: "bar",
                                data: y1,
                                itemStyle: {
                                    normal: {
                                        color: "#2391ff",
                                    },
                                },
                            },
                            {
                                name: "全市最佳应用",
                                type: "bar",
                                data: y2,
                                itemStyle: {
                                    normal: {
                                        color: "#ffc328",
                                    },
                                },
                            },
                            {
                                name: "理论和制度成果",
                                type: "line",
                                yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                                showAllSymbol: true, //显示所有图形。
                                // symbol: "circle", //标记的图形为实心圆
                                symbolSize: 10, //标记的大小
                                itemStyle: {
                                    normal: {
                                        color: "#26D9FF",
                                        lineStyle: {
                                            color: "#26D9FF",
                                            width: 4,
                                        },
                                    },
                                },
                                data: y2,
                            },
                        ],
                    };
                    myChartsState.setOption(option);
                    tools.loopShowTooltip(myChartsState, option, {
                        loopSeries: true,
                    }); //轮播
                },
                //绘制进度条
                progresschartsShow(data) {
                    const myChartsState = echarts.init(document.getElementById("szjj-chart1"));
                    var fontColor = "#30eee9";
                    let x = data.map((item) => {
                        return item.name;
                    });
                    let y1 = data.map((item) => {
                        return item.value;
                    });
                    var myColor = ["#23e4ab"];
                    let positionLeft = 0.4,
                        max = 100 + 2 * positionLeft;
                    var option = {
                        // backgroundColor: "#101E44",
                        grid: {
                            left: "4%",
                            top: "12%",
                            right: "5%",
                            bottom: "8%",
                            containLabel: true,
                        },
                        xAxis: [
                            {
                                max: max,
                                show: false,
                            },
                        ],
                        yAxis: [
                            {
                                type: "category",
                                inverse: true,
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: false,
                                    inside: false,
                                },
                                data: x,
                            },

                            {
                                axisLine: {
                                    lineStyle: {
                                        color: "rgba(0,0,0,0)",
                                    },
                                },
                                data: [],
                            },
                        ],
                        series: [
                            {
                                name: "数据内框",
                                type: "bar",
                                itemStyle: {
                                    normal: {
                                        barBorderRadius: 30,
                                        color: "#00b5eb",
                                    },
                                },
                                label: {
                                    normal: {
                                        show: true,
                                        //   position: 'right',
                                        color: "#fff",
                                        fontSize: 28,
                                        formatter: "{b}:{c}",
                                        position: [0, "-35px"],
                                    },
                                },
                                barWidth: 30,
                                data: y1,
                            },
                            {
                                name: "外框",
                                type: "bar",
                                itemStyle: {
                                    normal: {
                                        barBorderRadius: 30,
                                        color: "#fff",
                                        fontSize: 28,
                                        color: "rgba(255, 255, 255, 0.14)", //rgba设置透明度0.14
                                    },
                                },
                                barGap: "-100%",
                                z: 0,
                                barWidth: 30,
                                data: [100, 100, 100, 100, 100],
                            },
                        ],
                    };
                    myChartsState.setOption(option);
                    tools.loopShowTooltip(myChartsState, option, {
                        loopSeries: true,
                    }); //轮播
                },
                //绘制饼图
                PiechartsShow(data) {
                    const myChartsPerson = echarts.init(document.getElementById("szjj-chart2"));
                    var fontColor = "#30eee9";
                    let option = {
                        grid: {
                            // left: "15%",
                            right: "2%",
                            // top: "30%",
                            // bottom: "15%",
                            containLabel: true,
                        },
                        legend: {
                            // orient: "vertical",
                            x: "center",
                            y: 50,
                            itemWidth: 24,
                            itemHeight: 14,
                            align: "left",
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                        },
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        color: ["#5087EC", "#F2BD42", "#58A55C", "#F2BD42", "#EE752F"],
                        series: [
                            {
                                name: "党政机关指标分析",
                                type: "pie",
                                radius: "70%",
                                center: ["50%", "50%"],
                                data: data,
                                itemStyle: {
                                    normal: {
                                        label: {
                                            show: true,
                                            color: "#fff",
                                            fontSize: 25,
                                            position: "inside",
                                            formatter: "{b}:\n{d}%",
                                        },
                                    },
                                    labelLine: { show: false },
                                },
                            },
                        ],
                    };

                    myChartsPerson.setOption(option);
                    // tools.loopShowTooltip(myChartsPerson, option, {
                    //     loopSeries: true,
                    // }); //轮播
                },
            },

            //项目生命周期
            mounted() {
                this.init();
            },
        });
    </script>
</html>
