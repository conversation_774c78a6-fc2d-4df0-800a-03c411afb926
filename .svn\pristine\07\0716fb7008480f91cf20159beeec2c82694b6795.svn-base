<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>数字文化-middle</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>

    <style>
      * {
        margin: 0;
        padding: 0;
      }
      .mapImg {
        position: absolute;
        top: 350px;
        left: 600px;
      }
      #szwhmiddle .mainList .main {
        width: 610px;
        height: 293px;
        background: url('/static/citybrain/djtl/img/szwh/box1.png') no-repeat;
        background-size: cover;
      }
      .active {
        width: 610px;
        height: 293px;
        background: url('/static/citybrain/djtl/img/szwh/box1-active.png')
          no-repeat;
        background-size: cover;
      }
      .toptel {
        background: linear-gradient(
          to bottom,
          #d8e3e6,
          #abcaeb,
          #8ed0e6,
          #289de8
        );
        -webkit-background-clip: text;
        color: transparent;
      }
      #szwhmiddle {
        position: relative;
        width: 3480px;
        height: 1930px;
      }
      #szwhmiddle .mainList {
        cursor: pointer;
        position: absolute;
      }
      #szwhmiddle .mainList:nth-child(1) {
        left: 0px;
        top: 184px;
      }
      #szwhmiddle .mainList:nth-child(2) {
        left: 0px;
        top: 552px;
      }
      #szwhmiddle .mainList:nth-child(3) {
        left: 0px;
        top: 920px;
      }
      #szwhmiddle .mainList:nth-child(4) {
        left: 0px;
        top: 1288px;
      }
      #szwhmiddle .mainList:nth-child(5) {
        right: 0px;
        top: 40px;
      }
      #szwhmiddle .mainList:nth-child(6) {
        right: 0px;
        top: 408px;
      }
      #szwhmiddle .mainList:nth-child(7) {
        right: 0px;
        top: 776px;
      }
      #szwhmiddle .mainList:nth-child(8) {
        right: 0px;
        top: 1144px;
      }
      #szwhmiddle .mainList:nth-child(9) {
        right: 0px;
        top: 1512px;
      }
      .line1 {
        height: 297px;
        position: absolute;
        top: 301px;
        left: 604px;
        width: 719px;
        border-top: 10px solid #4d7ea8;
        border-right: 10px solid #4d7ea8;
        border-top-right-radius: 50px;
      }
      .line2 {
        position: absolute;
        top: 660px;
        left: 608px;
        height: 168px;
        width: 396px;
        border-top: 10px solid #4d7ea8;
        border-right: 10px solid #4d7ea8;
        border-top-right-radius: 20px;
      }
      .line3 {
        height: 225px;
        position: absolute;
        top: 748px;
        left: 606px;
        width: 867px;
        border-bottom: 10px solid #4d7ea8;
        border-right: 10px solid #4d7ea8;
        border-bottom-right-radius: 20px;
      }
      .line4 {
        position: absolute;
        top: 1100px;
        left: 606px;
        width: 623px;
        height: 316px;
        border-bottom: 10px solid #4d7ea8;
        border-right: 10px solid #4d7ea8;
        border-bottom-right-radius: 20px;
      }
      .line5 {
        position: absolute;
        right: 610px;
        width: 1225px;
        height: 199px;
        top: 200px;
        border-left: 10px solid #4d7ea8;
        border-top: 10px solid #4d7ea8;
        border-top-left-radius: 40px;
      }
      .line6 {
        position: absolute;
        top: 422px;
        height: 176px;
        width: 1015px;
        right: 610px;
        border-left: 10px solid #4d7ea8;
        border-top: 10px solid #4d7ea8;
        border-top-left-radius: 40px;
      }
      .line7 {
        position: absolute;
        width: 648px;
        height: 230px;
        top: 736px;
        right: 610px;
        border-left: 10px solid #4d7ea8;
        border-bottom: 10px solid #4d7ea8;
        border-bottom-left-radius: 40px;
      }
      .line8 {
        position: absolute;
        right: 610px;
        width: 987px;
        height: 256px;
        top: 985px;
        border-left: 10px solid #4d7ea8;
        border-bottom: 10px solid #4d7ea8;
        border-bottom-left-radius: 40px;
      }
      .line9 {
        position: absolute;
        top: 890px;
        height: 709px;
        right: 610px;
        width: 356px;
        border-left: 10px solid #4d7ea8;
        border-bottom: 10px solid #4d7ea8;
        border-bottom-left-radius: 40px;
      }

      .activeColor1 {
        /* border-image: linear-gradient(90deg, rgb(21, 102, 196), rgb(5, 37, 100)) 1;
          clip-path: inset(0px round 10px); */
        /* animation: huerotate 6s infinite linear;filter: hue-rotate(360deg); */
        height: 50px;
        clip-path: polygon(0 0, 100% 50%, 0 100%);
        offset-path: path('M 10 80 C 80 10, 180 10, 190 80 S 300 300, 300 150');
        background: linear-gradient(
          to bottom,
          rgb(9, 94, 231),
          rgba(208, 12, 120, 0.7)
        );
        width: 50px;
        animation: move 3000ms infinite;
      }
      .activeColor2 {
        height: 50px;
        clip-path: polygon(0 0, 100% 50%, 0 100%);
        offset-path: path(
          'M 10 80 C 250 10, 250 10, 300 100 S 300 450,560 320'
        );
        background: linear-gradient(
          to bottom,
          rgb(9, 94, 231),
          rgba(208, 12, 120, 0.7)
        );
        width: 50px;
        animation: move 3000ms infinite;
      }
      .activeColor3 {
        height: 50px;
        clip-path: polygon(0 0, 100% 50%, 0 100%);
        offset-path: path('M 10 300 C 250 10, 250 10,500 20 S 700 200,810 20');
        background: linear-gradient(
          to bottom,
          rgb(9, 94, 231),
          rgba(208, 12, 120, 0.7)
        );
        width: 50px;
        animation: move 3000ms infinite;
      }
      .activeColor4 {
        height: 50px;
        clip-path: polygon(0 0, 100% 50%, 0 100%);
        offset-path: path(
          'M 10 350 C 100 180, 200 200,300 200 S 300 100,500 20'
        );
        background: linear-gradient(
          to bottom,
          rgb(9, 94, 231),
          rgba(208, 12, 120, 0.7)
        );
        width: 50px;
        animation: move 3000ms infinite;
      }
      .activeColor5 {
        height: 50px;
        clip-path: polygon(100% 100%, 0 0, 0 100%);
        offset-path: path(
          'M 50 20 C -50 50, -80 80,-200 150 S -500 20,-1050 250'
        );
        background: linear-gradient(
          to bottom,
          rgb(9, 94, 231),
          rgba(208, 12, 120, 0.7)
        );
        width: 50px;
        animation: move 3000ms infinite;
      }
      .activeColor6 {
        height: 50px;
        clip-path: polygon(100% 100%, 0 0, 0 100%);
        offset-path: path(
          'M 50 80 C -50 50, -80 80,-200 150 S -500 20,-1000 180'
        );
        background: linear-gradient(
          to bottom,
          rgb(9, 94, 231),
          rgba(208, 12, 120, 0.7)
        );
        width: 50px;
        animation: move 3000ms infinite;
      }
      .activeColor7 {
        height: 50px;
        clip-path: polygon(100% 50%, 0 0, 0 100%);
        offset-path: path(
          'M 50 150 C -50 50, -80 80,-200 150 S -500 20,-600 20'
        );
        background: linear-gradient(
          to bottom,
          rgb(9, 94, 231),
          rgba(208, 12, 120, 0.7)
        );
        width: 50px;
        animation: move 3000ms infinite;
      }
      .activeColor8 {
        height: 50px;
        clip-path: polygon(100% 50%, 0 0, 0 100%);
        offset-path: path(
          'M 50 300 C -520 500, -380 80,-450 100 S -300 0,-800 -100'
        );
        background: linear-gradient(
          to bottom,
          rgb(9, 94, 231),
          rgba(208, 12, 120, 0.7)
        );
        width: 50px;
        animation: move 3000ms infinite;
      }
      .activeColor9 {
        height: 50px;
        clip-path: polygon(100% 50%, 0 0, 0 100%);
        offset-path: path(
          'M 50 800 C -50 50, -80 80,-180 20 S -150 20,-150 -100'
        );
        background: linear-gradient(
          to bottom,
          rgb(9, 94, 231),
          rgba(208, 12, 120, 0.7)
        );
        width: 50px;
        animation: move 3000ms infinite;
      }
      @keyframes huerotate {
        0% {
          filter: hue-rotate(0deg);
        }
        100% {
          filter: hue-rotate(360deg);
        }
      }
      @keyframes move {
        0% {
          offset-distance: 0%;
        }
        100% {
          offset-distance: 100%;
        }
      }
      .showNo {
        display: none;
      }
    </style>
  </head>
  <body>
    <div id="szwhmiddle">
      <!-- 列表 -->
      <!-- <div class="mainList" v-for="(item,i) in list" @click="ckIndex=i">
        <div :class="ckIndex===i?'active':'main'">
          <div
            class="top"
            style="
              height: 47px;
              width: 100%;
              font-size: 34px;
              padding-top: 50px;
              display: flex;
              justify-content: center;
              align-items: center;
              align-content: center;
              justify-items: center;
              background: linear-gradient(
                180deg,
                #aed6ff 0%,
                #74b8ff 47.4853515625%,
                #9ccfff 50%,
                #ddeeff 100%
              );
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            "
          >
            <div class="top-l" style="display: flex; align-items: center">
              <div
                class="line"
                style="width: 102px; height: 2px; background-color: #94b1cb"
              ></div>
              <div
                class="lx"
                style="
                  height: 9px;
                  margin: 4px;
                  display: inline-block;
                  background: #94b1cb;
                  transform: rotate(-45deg);
                  width: 9px;
                "
              ></div>
            </div>
            <div class="toptel" style="margin: 0 8px">{{item.ymbq}}</div>
            <div class="top-l" style="display: flex; align-items: center">
              <div
                class="lx"
                style="
                  height: 9px;
                  margin: 4px;
                  display: inline-block;
                  background: #94b1cb;
                  transform: rotate(-45deg);
                  width: 9px;
                "
              ></div>
              <div
                class="line"
                style="width: 102px; height: 2px; background-color: #94b1cb"
              ></div>
            </div>
          </div>

          <div
            class="maintel"
            style="
              width: 515px;
              height: 139px;
              margin: 11px auto 0;
              font-size: 30px;
              color: #fff;
              font-weight: 600;
              line-height: 50px;
            "
          >
            <i>{{item.VALUE}}</i>
          </div>
        </div>
      </div> -->

      <!-- <div class="mapImg">
        <img src="/static/citybrain/djtl/img/szwh/map.png" width="2200px" />
      </div> -->

      <!-- <div class="line1" :class="ckIndex===0?'activeColor2':'showNo'"></div>
      <div class="line2" :class="ckIndex===1?'activeColor1':'showNo'"></div>
      <div class="line3" :class="ckIndex===2?'activeColor3':'showNo'"></div>
      <div class="line4" :class="ckIndex===3?'activeColor4':'showNo'"></div>
      <div class="line5" :class="ckIndex===4?'activeColor5':'showNo'"></div>
      <div class="line6" :class="ckIndex===5?'activeColor6':'showNo'"></div>
      <div class="line7" :class="ckIndex===6?'activeColor7':'showNo'"></div>
      <div class="line8" :class="ckIndex===7?'activeColor8':'showNo'"></div>
      <div class="line9" :class="ckIndex===8?'activeColor9':'showNo'"></div> -->
    </div>

    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
      var vm_left = new Vue({
        el: '#szwhmiddle',
        data: {
          ckIndex: 0,
          list: [
            {
              name: '兰溪市',
              main: '新媒体、体验交互设计，云演艺、云展览、数字艺术、沉浸式体验',
            },
            {
              name: '婺城区',
              main: '视频直播、网络视听、游戏交易、视频社交、网络文艺、影视娱乐、动漫游戏',
            },
            {
              name: '金义新区',
              main: '沉浸影院、沉浸展演、沉浸夜游，数字文博',
            },
            {
              name: '武义县',
              main: '数字影视、数字娱乐，数字展览、数字阅读、云直播',
            },
            {
              name: '浦江县',
              main: '数字图书馆、数字文化馆、数字美术馆、数字博物馆，云旅游、云演艺、云娱乐',
            },
            { name: '义乌市', main: '电竞、网络游戏' },
            { name: '东阳市', main: '影视文化，游戏、动漫、短视频、直播' },
            {
              name: '永康市',
              main: '智慧景区、智慧酒店、智慧文旅小镇，数字图书馆、数字文化馆、数字博物馆',
            },
            {
              name: '磐安县',
              main: '视频直播、短视频、新媒体，网竞、网影、网书',
            },
          ],
        },
        mounted() {
          $api('/szwh_middle001').then((res) => {
            this.list = res
          })

          // top.document.getElementById('map').contentWindow.Work.funChange(
          //     JSON.stringify({
          //         funcName:"3Dtext", //3D文字功能
          //         textData:[   // pos文字的位置  //text 展示的文字
          //             {pos: [119.94315399169922,29.5630503845215,11000],text:"浦江县",color:[255,255,255,1]},//浦江县
          //             {pos: [119.46214447021484,29.31345558166504,11000],text:"兰溪市",color:[255,255,255,1]},//兰溪市
          //             {pos: [119.5569204711914, 29.00677101135254,11000],text:"婺城区",color:[255,255,255,1]},//婺城区
          //             {pos: [119.8483056640625, 29.18855995178222711000],text:"金义新区",color:[255,255,255,1]},//金东区
          //             {pos: [120.08206787109375,29.322123641967773,11000],text:"义乌市",color:[255,255,255,1]},//义乌市
          //             {pos: [119.7269204711914, 28.79677101135254,11000],text:"武义县",color:[255,255,255,1]},//武义县
          //             {pos: [120.1469204711914, 28.96677101135254,11000],text:"永康市",color:[255,255,255,1]},//永康市
          //             {pos: [120.4169204711914, 29.24677101135254,11000],text:"东阳市",color:[255,255,255,1]},//东阳市
          //             {pos: [120.6299204711914, 29.06677101135254,11000],text:"磐安县",color:[255,255,255,1]}//磐安县
          //         ],
          //         textSize:40
          //     })
          // )
          // // top.commonObj.showMap();
          // top.document.getElementById('map').contentWindow.Work.change3D(9);
          // fetch('/static/data/data.json', {
          //     method: 'GET',
          //     mode: 'cors',
          //     headers: new Headers({
          //         Accept: 'application/json',
          //     }),
          // }).then((res) => res.json()).then((e) => {
          //      e['数字文化_产业'].forEach((e) => {
          //         let obj = {
          //             funcName:"customPop",
          //             html:`
          //                 <div class="main" style="width:750px;height:359px;background:url('/static/citybrain/djtl/img/szwh/box1.png') no-repeat;background-size: cover;">
          //                     <div class="top" style="height:47px;width:100%;font-size:34px;padding-top:50px;display:flex;justify-content:center;align-items: center;align-content: center;justify-items: center;background: linear-gradient(180deg, #AED6FF 0%, #74B8FF 47.4853515625%, #9CCFFF 50%, #DDEEFF 100%);-webkit-background-clip:text;-webkit-text-fill-color: transparent;">
          //                         <div class="top-l" style="display:flex;align-items:center;">
          //                             <div class="line" style="width:102px;height:2px;background-color: #94B1CB;"></div>
          //                             <div class="lx" style="height:9px;margin: 4px;display: inline-block;background:#94B1CB;transform: rotate(-45deg);width: 9px;"></div>
          //                         </div>
          //                         <div class="toptel" style="margin: 0 8px;">${e.name}主导产业</div>
          //                          <div class="top-l" style="display:flex;align-items:center;">
          //                             <div class="line" style="width:102px;height:2px;background-color: #94B1CB;"></div>
          //                             <div class="lx" style="height:9px;margin: 4px;display: inline-block;background:#94B1CB;transform: rotate(-45deg);width: 9px;"></div>
          //                         </div>
          //                     </div>
          //                     <div class="maintel" style="width:650px;height:213px;margin:11px auto 0;font-size:34px;color:#fff;font-weight:600;line-height:80px;">
          //                         <i>${e.center}</i>
          //                     </div>
          //                 </div>
          //             `,
          //             coordinates:e.offset,
          //             closeButton:false,
          //         };
          //         top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(obj))
          //     })
          // })
        },
        methods: {},
      })
    </script>
  </body>
</html>
