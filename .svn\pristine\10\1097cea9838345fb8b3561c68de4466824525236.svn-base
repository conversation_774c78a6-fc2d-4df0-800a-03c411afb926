<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Document</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <link rel="stylesheet" href="../css/sdqfw-right.css" />
    </head>

    <body>
        <div id="sdqfw-left">
            <div class="content">
                <div class="title">
                    <nav style="padding: 20px 45px 0 45px">
                        <s-header-title style="width: 100%" title="气" htype="1"></s-header-title>
                    </nav>
                </div>
                <div class="title">
                    <nav style="padding: 0px 45px">
                        <s-header-title2 style="width: 100%" title="运行分析" htype="1"></s-header-title2>
                    </nav>
                </div>
                <div class="waterBox">
                    <div v-for="item in qData">
                        <div>{{item.name}}</div>
                        <div class="value">
                            <span>{{item.value}}</span>
                            {{item.dw}}
                        </div>
                    </div>
                </div>

                <div class="title">
                    <nav style="padding: 0px 40px">
                        <s-header-title2 style="width: 100%" title="燃气供应分析" htype="1"></s-header-title2>
                    </nav>
                </div>
                <div class="Dbox">
                    <div v-for="item in rqdata" style="width: 800px">
                        <div>{{item.name}}</div>
                        <div class="value valueBox">
                            <span>{{item.value}}</span>
                            {{item.dw}}
                        </div>
                    </div>
                    <el-button
                        type="primary"
                        style="
                            width: 200px;
                            height: 80px;
                            margin-left: 60px;
                            font-size: 40px;
                        "
                        @click="gasPriceDialog"
                    >
                        用气价格
                    </el-button>
                </div>
                <div class="bottomBox" style="height: 420px;">
                    <div>
                        <div class="bottomTitle">
                            <div
                                v-for="(item ,index) in timeList" 
                                @click="changeEcharts(index)"
                                :class="{active:isActive===index}"
                            >
                                {{item}}
                            </div>
                        </div>
                        <div id="lineEcharts003" style="width: 1050px; height: 350px"></div>
                    </div>
                    <div class="yqlBox">
                        <div class="yql"><span>当年累计用气量</span></div>
                        <div id="barEcharts00" style="width: 800px; height: 400px"></div>
                    </div>
                </div>
                <div class="titleRight">
                    <nav>
                        <s-header-title2 title="用气量分析" htype="2"></s-header-title2>
                    </nav>
                    <nav>
                        <s-header-title2 title="事件公告" htype="2"></s-header-title2>
                    </nav>
                </div>
                <div class="zxqbNum">
                    <div class="numBox">
                        <div class="zxLeft">
                            <div class="zxTitle">在线气表数量</div>
                            <div class="qbNum"><span>1205</span>个</div>
                        </div>
                        <div id="pieEcharts00" style="width: 600px; height: 300px"></div>
                    </div>
                    <div class="rightBox">
                        <div><span>供气公司信息公告</span></div>

                        <table class="table">
                            <thead>
                                <tr class="trTop">
                                    <th class="thName" v-for="item in thName">{{item}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="item in gqList">
                                    <td>{{item.sort}}</td>
                                    <td>{{item.sj}}</td>
                                    <td>{{item.lb}}</td>
                                    <td>{{item.time}}</td>
                                    <td>{{item.fw}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#sdqfw-left",
        data: {
            qData: [],
            rqdata: [],
            gqList: [],
            thName: ["序号", "事件", "类别", "时间", "影响范围"],
            timeList: ["年度", "月份", "日期"],
            isActive: 0,
        },
        mounted() {
            this.initFun();
            this.openDialog();
        },
        methods: {
            openDialog() {
                let iframe1 = {
                    type: "openIframe",
                    name: "sdqfw-map",
                    src:
                        baseURL.url +
                        "/static/citybrain/ggfw/commont/sdqfw-map.html",
                    width: "710px",
                    height: "330px",
                    left: "3750px",
                    top: "1220px",
                    argument: {
                        status: "openIframe",
                        // data:planData
                    },
                    zIndex: "10",
                };
                window.parent.postMessage(JSON.stringify(iframe1), "*");
            },
            gasPriceDialog(){
                let diaog = {
                    type: 'openIframe',
                    name: 'gas-price-dialog',
                    src: baseURL.url + '/static/citybrain/ggfw/commont/gas-price-dialog.html',
                    left: "calc(50% - 700px)",
                    top: "25%",
                    width: "1560px",
                    height: "980px",
                    zIndex:"10",
                    argument: {
                        status:""
                    }
                }
                top.window.parent.postMessage(JSON.stringify(diaog), '*')
            },
            initFun() {
                $api("sdqfw_right001").then((res) => {
                    this.qData = res;
                });
                $api("ggfw_sdqfw_right002").then((res) => {
                    this.rqdata = res;
                });
                $api("sdqfw_right003").then((res) => {
                    this.getEcharts04("lineEcharts003", res);
                });
                $api("sdqfw_right006").then((res) => {
                    this.getEcharts05("barEcharts00", res);
                });
                $api("sdqfw_right007").then((res) => {
                    this.getEcharts06("pieEcharts00", res);
                });
                $api("sdqfw_right008").then((res) => {
                    this.gqList = res;
                });
            },

            getEcharts04(dom, echartData) {
                let echarts4 = echarts.init(document.getElementById(dom));

                const xAxisData = echartData.map((item) => {
                    return item.time;
                });
                const yData = echartData.map((item) => {
                    return item.value;
                });

                let option = {
                    textStyle: {
                        fontFamily: "Din-Light",
                    },
                    legend: {
                        data: [
                            {
                                name: "累计用气量",
                                icon: "path://M512 139.81262864a286.42534744 286.42534744 0 1 0 286.42534744 286.42534744 286.42534744 286.42534744 0 0 0-286.42534744-286.42534744z m0 477.3755789a190.95023144 190.95023144 0 1 1 190.95023144-190.95023146 190.95023144 190.95023144 0 0 1-190.95023144 190.95023146z",
                            },
                        ],
                        selected: {
                            累计用气量: true,
                        },
                        itemWidth: 20,
                        itemHeight: 20,
                        itemGap: 30,
                        textStyle: {
                            color: "#fff",
                            lineHeight: 15,
                            fontSize: 30,
                        },
                        type: "scroll",
                    },
                    tooltip: {
                        backgroundColor: "#fff05",
                        trigger: "axis",
                        axisPointer: {
                            type: "none",
                        },
                        textStyle: {
                            color: "#ffff",
                            lineHeight: 28,
                            fontSize: 30,
                        },
                        confine: true,
                        padding: 12,
                    },
                    grid: {
                        // right: 0,
                        bottom: 100,
                    },
                    xAxis: {
                        type: "category",
                        boundaryGap: true,
                        offset: 5,
                        data: xAxisData,
                        axisLabel: {
                            interval: 0,
                            align: "left",
                            color: "#fff",
                            fontSize: 30,
                        },
                        axisLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },
                    },
                    yAxis: {
                        type: "value",
                        min: 0,
                        max: 1600,
                        interval: 400,
                        axisLabel: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        splitLine: {
                            lineStyle: {
                                color: "#19365f",
                            },
                        },
                        axisLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },
                    },
                    series: [
                        {
                            name: "累计用气量",
                            data: yData,
                            type: "line",
                            smooth: true,
                            smoothMonotone: "x",
                            cursor: "pointer",
                            showSymbol: false,
                            lineStyle: {
                                shadowColor: "rgba(18,61,172,0.5)",
                                color: "#0398d1",
                                shadowBlur: 10,
                            },
                        },
                    ],
                };
                echarts4.setOption(option);
            },
            changeEcharts(index) {
                this.isActive = index;
                if (this.isActive === 0) {
                    $api("sdqfw_right003").then((res) => {
                        this.getEcharts04("lineEcharts003", res);
                    });
                } else if (this.isActive === 1) {
                    $api("sdqfw_right004").then((res) => {
                        this.getEcharts04("lineEcharts003", res);
                    });
                } else {
                    $api("sdqfw_right005").then((res) => {
                        this.getEcharts04("lineEcharts003", res);
                    });
                }
            },
            getEcharts05(dom, echartData) {
                let echarts5 = echarts.init(document.getElementById(dom));

                var datas = echartData;
                let maxArr = new Array(datas.length).fill(200);
                let option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: "30",
                        },
                    },
                    legend: {
                        show: false,
                    },
                    grid: {
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        containLabel: true,
                    },
                    xAxis: {
                        show: false,
                        type: "value",
                    },
                    yAxis: [
                        {
                            type: "category",
                            inverse: true,
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisPointer: {
                                label: {
                                    show: true,
                                    margin: 30,
                                },
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#FFF",
                                    fontSize: 30,
                                },
                            },
                            data: datas.map((item) => item.time),
                        },
                        {
                            type: "category",
                            inverse: true,
                            axisTick: "none",
                            axisLine: "none",
                            show: true,
                            data: datas.map((item) => item.value),
                            axisLabel: {
                                show: true,
                                formatter: function (value) {
                                    let a = value + "立方米";
                                    return "{a|" + a + "}";
                                },
                                rich: {
                                    a: {
                                        color: "#35a3d5",
                                        fontSize: 30,
                                        fontWeight: "bold",
                                    },
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            z: 2,
                            type: "bar",
                            barWidth: 25,
                            zlevel: 1,
                            data: datas.map((item, i) => {
                                itemStyle = {
                                    color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                        {
                                            offset: 0,
                                            color: "#1f71a2",
                                        },
                                        {
                                            offset: 1,
                                            color: "#41c1f6",
                                        },
                                    ]),
                                };
                                return {
                                    value: item.value,
                                    itemStyle: itemStyle,
                                };
                            }),
                            label: {
                                show: false,
                                position: "right",
                                color: "#333333",
                                fontSize: 14,
                                offset: [10, 0],
                            },
                        },
                        {
                            type: "bar",
                            barWidth: 25,
                            barGap: "-100%",
                            itemStyle: {
                                normal: {
                                    color: "#052559",
                                },
                            },
                            data: maxArr,
                        },
                    ],
                };
                echarts5.setOption(option);
            },
            getEcharts06(dom, echartData) {
                let echarts5 = echarts.init(document.getElementById(dom));

                const colorList = ["#47A2FF ", "#53C8D1", "#59CB74", "#FBD444", "#7F6AAD", "#585247"];

                let option = {
                    legend: {
                        type: "scroll",
                        orient: "vertical",
                        right: "3%",
                        top: "center",
                        itemGap: 30,
                        icon: "square",
                        data: ["智能气表", "传统气表数量"],
                        textStyle: {
                            color: "#fff",
                            fontSize: 30,
                        },
                    },
                    color: colorList,
                    series: [
                        {
                            type: "pie",
                            radius: ['0%', '50%'],
                            center: ["40%", "50%"],
                            label: {
                                show: false,
                            },
                            labelLine: {
                                show: false,
                            },
                            itemStyle: {
                                borderWidth: 3,
                                borderColor: "#0b3054",
                            },
                            data: echartData,
                        },
                    ],
                };
                echarts5.setOption(option);
            },
        },
    });
</script>
