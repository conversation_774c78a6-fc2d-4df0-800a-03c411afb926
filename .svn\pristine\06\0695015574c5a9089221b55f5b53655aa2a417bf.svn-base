[v-cloak] {
    display: none;
}
* {
    padding: 0;
    margin: 0;
}
#zwfw-right {
    width: 2045px;
    height: 1890px;
    background: url("/img/right-bg.png") no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
}
.bmyTitle{
    display: flex;
    font-size: 30px;
    color: #fff;

}
.fplyTitle{
    display: flex;
    justify-content: space-between;
}
.fply{
    font-size: 30px;
    width: 320px;
    height: 90px;
    color: #fff;
    text-align: center;
    background:url('../img/benefit-block-bg.png')no-repeat;
    background-size: 100% 100%;
}
.gjTitle span{
    background-image: linear-gradient(180deg, rgb(249, 253, 255), #bbe6ff, #007ac0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 30px;
    font-weight: bold;
}
.mydTitle span{
    background-image: linear-gradient(180deg, rgb(249, 253, 255), #cbaeb0, #f06e80);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 30px;
    font-weight: bold;
}
.jrgkTitle{
    font-size: 30px;
    color: #fff;
    line-height: 60px;
}
.jrgkTitle span{
    background-image: linear-gradient(180deg, rgb(249, 253, 255), #bbe6ff, #007ac0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 35px;
    font-weight: bold;
}
.wsdtBox{
    font-size: 30px;
    color: #fff;
    display: flex;
    justify-content: space-around;
}
.wdst-box{
    width: 300px;
    text-align: center;
}
.wsdtTitle{
    height: 100px;
    line-height: 100px;
    background:url('../img/title-bg.png') no-repeat;
    background-size: 100% 100%;
    text-align: center;

}
.wsdtTitle span{
    background-image: linear-gradient(180deg, rgb(249, 253, 255), #bbe6ff, #007ac0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 40px;
    font-weight: bold;
}
.dsdtTitle{
    width: 295px;
    background-image: linear-gradient(180deg, rgb(249, 253, 255), #bbe6ff, #007ac0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 30px;
    font-weight: bold;
}
.dsdtrm{
 
    height: 270px;
    overflow-y: auto;
}
.dsdtBox{
    display: flex;
    justify-content: space-between;
}
.top-DS{
    font-size: 30px;
    /* width: 200px; */
    background: url('../img/title-bg2.png')no-repeat;
    background-size: 100% 100%;
    height: 80px;
    margin-bottom: 15px;
    color: #fff;
    line-height: 80px;
    text-align: center;
}
::-webkit-scrollbar{   
    width:0;   
    height:0;
}
.bjyx{
    display: flex;
}
.bjyxBox{
    width: 640px;
    background: url('../img/title-bg2.png')no-repeat;
    background-size: 100% 100%;
    display: flex;
    font-size: 30px;
    color: #fff;
    justify-content: space-around;
    height: 150px;
    align-items: center;
  
}
.jrgkBox{
    width: 1290px;
}
.bjyxTitle span{
    background-image: linear-gradient(180deg, rgb(249, 253, 255), #bbe6ff, #007ac0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 40px;
    font-weight: bold;
}
.thName{
    display: flex;
    font-size: 30px;
    font-weight: bold;
    width: 1934px;
    line-height: 80px;
    justify-content: space-around;
    color: #fff;
    background-color: #105995;
    border: 1px solid #1567af;
}
.tbody{
    font-size: 30px;
    color: #fff;
}
.tbodyTitle{
    cursor: pointer;
    display: flex;
    justify-content: space-around;
    line-height: 80px;
    text-align: center;
}
.tbodyTitle:nth-child(2n){
    background-color: #094471;
}
.changeTab{
    cursor: pointer;
    font-size: 30px;
    background: url('../img/28.png') no-repeat;
    background-size: 100% 100%;
    width: 200px;
    line-height: 50px;
    font-weight: bold;
    text-align: center;
    color: #ffff;
}
.slqdBox{
    font-size: 30px;
    color: #fff;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
.slqdBox>div{
    display: flex;
    align-items: center;
    width: 240px;
}
.slqdBox span{
    display: inline-block;
    width: 30px;
    height: 30px;
    margin-right: 10px;
}
.zbhzTitle{
    background:url('../img/title-bg2.png')no-repeat;
    background-size: 100% 100%;
    line-height: 70px;
}
.zbhzBox{
    font-size: 30px;
    color: #fff;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 620px;
    justify-content: space-between;
}
.zbhzBox>div{
    text-align: center;
    width: 300px;
    margin-bottom: 10px;
}
.dwTitle{
    background-image: linear-gradient(180deg, rgb(249, 253, 255), #bbe6ff, #007ac0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
}
.dwTitle span{
    background-image: linear-gradient(180deg, rgb(249, 253, 255), #bbe6ff, #007ac0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 40px;
    font-weight: bold;
}
.sqrd{
    background: url('../img/title-bg2.png')no-repeat;
    background-size: 100% 100%;
    width: 500px;
    line-height: 90px;
    margin-bottom: 20px;
}
.sqrd img{
    width: 50px;
    height: 50px;
}
.sqrdTitle{
    display: flex;
    font-size: 30px;
    color: #fff;
    align-items: center;
    justify-content: space-around;
}
.el-input__inner{
    font-size:30px;
    width: 250px;
    color:#fff;
    background-color: #144169;
    border: 1px solid #679fd6;
    line-height: 60px!important;
    height: 60px!important;
    border-radius: 10%;
}
.el-select-dropdown__item{
    font-size:30px;
    color:#fff;
}

.el-select-dropdown{
    border: 1px solid #679fd6!important;
}
.el-scrollbar{
    background-color: #144169;
}
.el-select-dropdown__item.hover{
    background: none!important;
    color:#51c5ec;
}
.el-select-dropdown__item.hover, .el-select-dropdown__item:hover{
    background: none!important;
}
.topLine{
    display: flex;
}
.search .el-input__inner{
    font-size:30px;
    width: 1000px;
    color:#fff;
    background-color: #144169;
    border: 1px solid #679fd6;
    line-height: 60px!important;
    height: 60px!important;
    border-radius: 0;
    margin-left: 55px;
}
.ztyxTitleOne{
    display: flex;
}
.bottomONE{
    width: 620px;
}
.el-input__icon{
    font-size: 25px;
    color: #fff;
    width: 80px;
    line-height: 64px;
}
.thOneName{
    display: flex;
    font-size: 30px;
    font-weight: bold;
    width: 1300px;
    line-height: 70px;
    justify-content: space-around;
    color: #fff;
    background-color: #105995;
    border: 1px solid #1567af;
}
.table{
    margin-top: 40px;
}