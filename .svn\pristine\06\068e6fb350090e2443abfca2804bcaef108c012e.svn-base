var commonObj = {
  userId: sessionStorage.getItem('role'),
  ptid: 'PT0001',
  token:  '',
  Authorization:sessionStorage.getItem('Authorization').replaceAll('"','')  ,
  title: '',
  menuBus: [],
  menuLoadSus: true,
  init: function () {
    $.ajax({
      url: 'getAjaxRequestIp',
      dataType: 'json',
      type: 'get',
      async: false,
      success: function (data) {
        commonObj.pageConfig = data.pageConfig
      },
      error: function (e) {
        console.log(e)
      },
    })
  },
  menuGo: function (menu) {
    if (commonObj.menuLoadSus) {
      commonObj.menuLoadSus = false
      menu.length > 0 && commonObj.menuBus.push(menu)
      console.log('当前菜单==========>', commonObj.menuBus)
    }
  },
  //页面加载
  loadHtml: function (url) {
    commonObj.funMenu(url)
  },
  //获取菜单  /screen/menu/getMenuByMenuIdentify
  async funSearchInfo(path) {

    let argument = `?url=${path}`
    let res = await axios({
      method: 'get',
      url: commonObj.pageConfig['menuspa'] + argument,
      headers: {
        Authorization:commonObj.Authorization
      },
    })
    return res.data.data
  },
  //设置当前菜单
  async setMenuTree(path) {

    let arr = []
    let len = commonObj.menuBus.length
    if (
      len === 0 ||
      (len > 0 && commonObj.menuBus[len - 1][0].url != path) //点第一个首页按钮不需要再获取菜单
    ) {
      arr = await commonObj.funSearchInfo(path)
    }
    let temp = []
    for (let i = 0; i < arr.length; i++) {
      commonObj.title = arr[i].name
      let res = {
        name: arr[i].name,
        url: arr[i].url,
        type: arr[i].type,
        active: arr[i].active,
        pageMiddle: arr[i].pageMiddle,
        iframes: arr[i].child,
      }
      temp.push(res)
    }

    commonObj.menuGo(temp)
    //加载页面
    commonObj.loadHtml(path)


  },
  //顶部菜单生成
  funMenu: function (url) {
    let menLen = commonObj.menuBus.length
    let menuArr = commonObj.menuBus[menLen - 1]
    let fir = []
    let lis = []
    menuArr.forEach((item, index) => {
      if (item.active == 1 || item.url == url) {
        commonObj.menuBus[menLen - 1][index].active = 0
        if (item.type != 0)
        
        item.iframes.map((ite) => {
          if (ite.id == 'left') {
            lis.push(
              `<iframe   style="opacity:0" width="${ite.width}" height="${ite.height}" src="${ite.src}" id=${item.url}${ite.id} class="page_${ite.id} animated fadeInLeft" frameborder="0"  scrolling="no"></iframe>`
            )
          } else if (ite.id == 'right') {
            lis.push(
              `<iframe   style="opacity:0" width="${ite.width}" height="${ite.height}" src="${ite.src}" id=${item.url}${ite.id} class="page_${ite.id} animated fadeInRight" frameborder="0"  scrolling="no"></iframe>`
            )
          }
        })
        $('#mainPage').html(lis)
        $('#headerTitle').html(commonObj.title)
        item.pageMiddle !== '' &&
          item.pageMiddle != null &&
          $('#page_middle').load(item.pageMiddle)
      }
    })

  },
  //首页时间
  formatDate: function (day) {
    if (day < 10) {
      return '0' + day
    } else {
      return '' + day
    }
  },
  setDate: function () {
    // cstz_tqjk
    // http://10.45.14.162:8101/jhyjzh-server/screen_api/zhdd/home/<USER>
    $api("http://10.45.14.162:8101/jhyjzh-server/screen_api/zhdd/home/<USER>").then(weaker => {
      let date = new Date();
      let curhour = commonObj.formatDate(date.getHours())
      let curminute = commonObj.formatDate(date.getMinutes())
      let cursecond = commonObj.formatDate(date.getSeconds())
      for (var i = 0; i < weaker.length; i++) {
        if (date.getDate().toString().padStart(2, '0') === weaker[i].day) {
          let imgUrl = weaker[0].img.slice(2)
          var curday = date.getFullYear() + '.' + (date.getMonth() + 1) + '.' + weaker[i].day;
          $('#headerTime').html(`<span><span id="nowTime">${curhour}:${curminute}:${cursecond}</span></b></b><b class="data">${curday}&nbsp;${weaker[i].mon}</b></span>
                        <span class="week"><img style="width:80px" src="${imgUrl}"/>${weaker[i].wd}<b class="data">${weaker[i].fx}&nbsp;${weaker[i].fd}</b></span>`)
        }
      }
    })
  },
  getNewTime() {
    let date = new Date()
    let curhour = commonObj.formatDate(date.getHours())
    let curminute = commonObj.formatDate(date.getMinutes())
    let cursecond = commonObj.formatDate(date.getSeconds())
    $('#nowTime').html(`${curhour}:${curminute}:${cursecond}`)
  },
  getQueryVariable:function(variable)
  {
         var query = window.location.search.substring(1);
         var vars = query.split("&");
         for (var i=0;i<vars.length;i++) {
                 var pair = vars[i].split("=");
                 if(pair[0] == variable){return pair[1];}
         }
         return(false);
  },
  initMain: function () {
    commonObj.init()
    commonObj.setDate()
    setInterval(function () {
      commonObj.getNewTime()
    }, 1000)
    let url = commonObj.getQueryVariable('url')
    commonObj.setMenuTree(url)
  },
}
  ; (function ($) {
    commonObj.initMain()
  })(jQuery)
