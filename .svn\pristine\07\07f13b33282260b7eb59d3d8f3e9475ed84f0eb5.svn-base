import {
    GeometryInstance,
    PrimitiveCollection,
    MaterialAppearance,
    Material,
    Cartesian3,
    Color,
    Transforms,
    Primitive,
    BoxGeometry
} from "../../../lib/Cesium/Source/Cesium.js"
import {
    Layer
} from './Layer.js'

/**
 * 创建圆
 * @alias Box
 * @extends Layer
 * @constructor
 * @param {Array} datas 立方体的数据信息
 * @param {Object} option 立方体的样式参数
 * 
 * @example
 *  let box = new EMap.Box([{
        position:[104.51461636410956, 31.301237021556204],
        height:100,
        userData:{}
    }], {
        color: '#09f',
        size:[10,10]
    })
    map.add(box)
 * 
 */
export class Box extends Layer {
    constructor(data = [], option = {},id) {
        super(option)
        // 创建容器
        this.collection = new PrimitiveCollection()
        if(id){
            this._uuid = id
        }
        this.collection._uuid = this._uuid

        let opt = Object.assign(this.defaultOption, option)
        this._init(opt)

        this.data = JSON.parse(JSON.stringify(data))

        this.addPrimitives(this.data)
    }
 
    // 默认配置
    defaultOption = {
        color: '#fff',
        size: [0, 0],
    }
    _init(option) {
        let {
            color,
            size,
        } = option
        this.appearance = new MaterialAppearance({
            material: new Material({
                fabric: {
                    type: 'Color',
                    uniforms: {
                        color: Color.fromCssColorString(color)
                    }
                }
            })
        })
        this.option = {
            size
        }

 
    }

    addPrimitives(datas){
        const labelData = []
        const {size} = this.option
        const ids = datas.map(item => {
            const {
                id = this._createUUid(),
                userData = {},
                position,
                height,
                text
            } = item
            let h = height / 2 + (position[2] || 0)
            let center = Cartesian3.fromDegrees(position[0], position[1], h);
         
            userData.position = [position[0], position[1], height + (position[2] || 0)]
            let primitive = new Primitive({
                geometryInstances: new GeometryInstance({
                    geometry: BoxGeometry.fromDimensions({
                        dimensions: new Cartesian3(size[0], size[1], height)
                    }),
                    modelMatrix: Transforms.eastNorthUpToFixedFrame(center),
                    id: {
                        id,
                        uuid: this._uuid,
                        userData
                    }
                }),
                appearance: this.appearance
            })
         

            this.primitives[id] = {
                data: item,
                primitive: this.collection.add(primitive)
            }

            // 如果有字，添加文字文字配置
            if (text) {
                labelData.push({
                    text,
                    position: [position[0], position[1], height + (position[2] || 0)]
                })
            }
            return id
        })

        if (labelData.length > 0 && this.labelOption.alwaysShow) {
            this.label.addPrimitives(labelData);
        }
        return ids
    }
   
}