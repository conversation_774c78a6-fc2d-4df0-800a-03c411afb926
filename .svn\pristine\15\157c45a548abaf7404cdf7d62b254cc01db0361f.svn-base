<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>重大危险源分类统计</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <style>
            .content {
                display: flex;
                font-size: 30px;
                color: #fff;
                text-align: center;
                align-items: center;
                width: 5500px;
            }
            .box {
                width: 380px;
                height: 100px;
                line-height: 100px;
                border: 1px solid #fff;
                background-color: #051431;
                margin-right: 20px;
            }
            .point{
                cursor: pointer;
            }
            .not_allowed{
                cursor: not-allowed;
            }
        </style>
    </head>

    <body>
        <div id="shgl-csaq-zdwxyfltj-bottom">
            <div class="content">
                <div class="box" v-for="(item,index) in thName" :class="item.disabled?'point':'not_allowed'" @click="openDialod(index)">
                    {{item.name}}
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#shgl-csaq-zdwxyfltj-bottom",
        data: {
            thName: [
                {
                    name:'风险隐患',
                    disabled:false
                },
                {
                    name:'分布展示',
                    disabled:false
                },
                {
                    name:'距离聚合',
                    disabled:false
                },
                {
                    name:'区划聚合',
                    disabled:false
                },
                {
                    name:'风险评估',
                    disabled:false
                },
                {
                    name:'专题图',
                    disabled:true
                },
                {
                    name:'区划圈',
                    disabled:false
                },
                {
                    name:'专题制图',
                    disabled:false
                },
                {
                    name:'重大危险源事故泄露上图',
                    disabled:true
                },
                {
                    name:'气体泄漏模型',
                    disabled:true
                },
                {
                    name:'液体泄露模型',
                    disabled:true
                },
                {
                    name:'重大危险源事故扩散模型上图',
                    disabled:true
                },
                {
                    name:'拉格朗日烟团模型',
                    disabled:false
                }
            ],
        },
        mounted() {
            this.initFun();
        },
        methods: {
            initFun() {},
            openDialod(index){
                this.rmPoint()
                if(index==5){
                   this.addPoint() 
                   this.closeIframe('zdwxy-dialog1')
                   this.closeIframe('zdwxy-dialog2')
                }else if(index==8 || index==9 || index==10){
                    this.closeIframe('zdwxy-dialog1')
                    this.openIframe('zdwxy-dialog2')
                }else if(index==11){
                    this.closeIframe('zdwxy-dialog2')
                    this.openIframe('zdwxy-dialog1')
                }
            },
            openIframe(name) {
                let Iframe = {
                    type: "openIframe",
                    name: name,
                    src: baseURL.url + "/static/citybrain/shgl/commont/"+name+".html",
                    left: "2200px",
                    top: "850px",
                    width: "1030px",
                    height: "970px",
                    zIndex: "10",
                    argument: {
                        status: "zdwxy-dialog",
                    },
                };
                window.parent.postMessage(JSON.stringify(Iframe), "*");
            },

            closeIframe(name) {
                top.commonObj.funCloseIframe({
                    name: name,
                });
            },

            addPoint(){
                let res = [
                    {
                        title: '浦江县',
                        gps_x: "119.94315399169922",
                        gps_y: "29.5630503845215",
                    },
                    {
                        title: '兰溪市',
                        gps_x: "119.46214447021484",
                        gps_y: "29.31345558166504",
                    },
                    {
                        title: '婺城区',
                        gps_x: "119.5569204711914",
                        gps_y: "29.00677101135254",
                    },
                    {
                        title: '金义新区',
                        gps_x: "119.8483056640625",
                        gps_y: "29.188559951782227",
                    },
                    {
                        title: '义乌市',
                        gps_x: "120.08206787109375",
                        gps_y: "29.322123641967773",
                    },
                    {
                        title: '武义县',
                        gps_x: "119.7269204711914",
                        gps_y: "28.79677101135254",
                    },
                    {
                        title: '永康市',
                        gps_x: "120.1469204711914",
                        gps_y: "28.97677101135254",
                    },
                    {
                        title: '东阳市',
                        gps_x: "120.4169204711914",
                        gps_y: "29.24677101135254",
                    },
                    {
                        title: '磐安县',
                        gps_x: "120.6299204711914",
                        gps_y: "29.06677101135254",
                    },
                    {
                        title: '婺城区',
                        gps_x: "119.64896993689013",
                        gps_y: "29.090742350540673",
                    }
                ];
                let arr = res.map((item) => {
                    return {
                    data: {
                        title:'风险评估报告',
                        key:[
                            '风险隐患基本信息',
                            '风险隐患名称',
                            '所属企业',
                            '企业地址',
                            '风险等级',
                            '风险描述',
                            '风险承受能力与控制能力分析',
                            '风险可能性分析',
                            '风险后果分析',
                            '分险等级确定'
                        ],
                        value:[
                            ' ',
                            '化学品泄漏',
                            '金华市化工有限公司',
                            '金东区阳光路109号',
                            '一级',
                            '遇明火引发爆炸',
                            '承受能力稍弱，控制能力一般',
                            '可能影响',
                            '造成2000人受伤、12栋楼坍塌',
                            '一级'
                        ]
                    },
                    point: item.gps_x + "," + item.gps_y,
                    };
                });
                console.log(arr);
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                    funcName: "pointLoad",
                    pointType: "digital-blue", // 点位类型（图标名称）
                    pointId: "point1", // 点位唯一id
                    setClick: true,
                    pointData: arr,
                    imageConfig: { iconSize: 1 },
                    popup:{
                        offset:[50,-100]
                    }
                    })
                );
            },

            //清除点位
            rmPoint(){
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                    funcName:"rmPoint" ,
                    pointId: "",
                    })
                )
            },
        },
    });
</script>
