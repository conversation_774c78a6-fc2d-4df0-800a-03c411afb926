<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>领域6左侧面板</title>
        <script src="/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain3840/shgl/css/ly6-left.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <!-- 轮播toolTip -->
    </head>

    <body>
        <div id="app" class="container" v-cloak>
            <nav>
                <s-header-title-2 htype="1" title="交通管理汇聚展示"></s-header-title-2>
            </nav>
            <!-- <div class="btn">详情</div> -->
            <div id="jtgl-chart"></div>
            <div class="table table1">
                <div class="th">
                    <div class="th_td" style="flex: 0.25" v-for="(item,index) in theadList" :key="index">{{item}}</div>
                </div>
                <div class="tbody" id="box0" @mouseover="mouseenterEvent()" @mouseleave="mouseleaveEvent()">
                    <div class="tr" v-for="(item ,i) in tbodyList" :key="i">
                        <div class="tr_td" style="flex: 0.25">{{i+1}}</div>
                        <div class="tr_td" style="flex: 0.25">{{item.name}}</div>
                        <div class="tr_td" style="flex: 0.25">{{item.value}}</div>
                        <div class="tr_td" style="flex: 0.25">{{item.speed}}</div>
                    </div>
                </div>
            </div>

            <div id="rjyd-chart"></div>
            <nav>
                <s-header-title-2 htype="1" title="环境保护汇聚展示"></s-header-title-2>
            </nav>
            <div class="top-con">
                <li
                    v-for="(item,index) in tabList"
                    :key="index"
                    :class="currentIndex===index?'active':''"
                    @click="changeTab(index,item)"
                >
                    {{item.name}}
                </li>
            </div>
            <div
                class="table table1"
                style="height: 520px"
                v-show="currentIndex===0||currentIndex===1||currentIndex===4"
            >
                <div class="th">
                    <div class="th_td" style="flex: 0.16" v-for="(item,index) in theadList1" :key="index">{{item}}</div>
                </div>
                <div class="tbody" id="box1">
                    <div class="tr" v-for="(item ,i) in tbodyList1" :key="i" v-if="currentIndex===0">
                        <div class="tr_td" style="flex: 0.16">{{item.name}}</div>
                        <div class="tr_td" style="flex: 0.26">{{item.pm}}</div>
                        <div class="tr_td" style="flex: 0.16">{{item.yll}}</div>
                        <div class="tr_td" style="flex: 0.16">{{item.you}}</div>
                        <div class="tr_td" style="flex: 0.16">{{item.liang}}</div>
                        <div class="tr_td" style="flex: 0.16">{{item.day}}</div>
                    </div>
                    <div class="tr" v-for="(item ,i) in tbodyList1" :key="i" v-if="currentIndex===1||currentIndex===4">
                        <div class="tr_td" style="flex: 0.16">{{item.name}}</div>
                        <div class="tr_td" style="flex: 0.26">{{item.jb}}</div>
                        <div class="tr_td" style="flex: 0.16">{{item.area}}</div>
                        <div class="tr_td" style="flex: 0.16">{{item.gnqszyq}}</div>
                        <div class="tr_td" style="flex: 0.16">{{item.type}}</div>
                        <div class="tr_td" style="flex: 0.16">{{item.bh}}</div>
                    </div>
                </div>
            </div>
            <div id="chart1" v-show="currentIndex===2"></div>
            <div id="chart2" v-show="currentIndex===3"></div>
        </div>
    </body>
    <script type="module">
        new Vue({
            el: "#app",
            data: {
                nowTime: "", //当前时间
                time: null,
                dom: null,
                scpDom: null,
                tabList: [
                    {
                        name: "生态环境质量",
                        id: "1",
                    },
                    {
                        name: "水质与水域变化",
                        id: "2",
                    },
                    {
                        name: "污染物排放",
                        id: "3",
                    },
                    {
                        name: "温室气体排放",
                        id: "4",
                    },
                    {
                        name: "环境承载力",
                        id: "5",
                    },
                ],
                currentIndex: 0,
                theadList: ["排名", "道路名称", "拥堵延时指数", "速度"],
                tbodyList: [],
                theadList1: ["县市名称", "PM2.5浓度(ug/m3)", "优良率(%)", "优/天", "良/天", "总有效天数"],
                tbodyList1: [],
            },
            methods: {
                changeTab(index, data) {
                    this.currentIndex = index;
                    if (index === 0 || index === 1 || index === 4) {
                        this.getTableData(index);
                    } else if (index === 2 || index === 3) {
                        this.getPieData(index);
                    }
                },
                mouseenterEvent() {
                    clearInterval(this.time);
                },
                mouseleaveEvent() {
                    this.time = setInterval(() => {
                        this.dom.scrollTop += 2;
                        if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                            this.dom.scrollTop = 0;
                        }
                    }, 20);
                },
                autoScroll() {
                    this.dom = document.getElementById("box0");
                    // this.scpDom = document.getElementsByClassName('text')
                    this.time = setInterval(() => {
                        this.dom.scrollTop += 2;
                        if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                            this.dom.scrollTop = 0;
                        }
                    }, 20);
                },
                init() {
                    $api("ldst_shgl_ly6", { type1: 1 }).then((res) => {
                        this.BarchartsShow(res);
                    });
                    $api("ldst_shgl_ly6", { type1: 2 }).then((res) => {
                        this.tbodyList = res;
                    });
                    $api("ldst_shgl_ly6", { type1: 3 }).then((res) => {
                        this.LinechartsShow("rjyd-chart", res);
                    });

                    this.getTableData(0);
                },
                //获取表格数据
                getTableData(index) {
                    if (index === 0) {
                        $api("ldst_shgl_ly6", { type1: 4 }).then((res) => {
                            this.tbodyList1 = res;
                        });
                        this.theadList1 = ["县市名称", "PM2.5浓度(ug/m3)", "优良率(%)", "优/天", "良/天", "总有效天数"];
                    } else if (index === 1) {
                        $api("ldst_shgl_ly6", { type1: 5 }).then((res) => {
                            this.tbodyList1 = res;
                        });
                        this.theadList1 = [
                            "段面名称",
                            "断面级别",
                            "所属县市",
                            "功能区水质要求",
                            "实质水质类别",
                            "与去年同期对比水质变化",
                        ];
                    } else if (index === 4) {
                        $api("ldst_shgl_ly6", { type1: 6 }).then((res) => {
                            this.tbodyList1 = res;
                        });
                        this.theadList1 = [
                            "段面名称",
                            "断面级别",
                            "所属县市",
                            "功能区水质要求",
                            "实质水质类别",
                            "与去年同期对比水质变化",
                        ];
                    }
                },
                //绘制饼图
                getPieData(index) {
                    if (index === 2) {
                        $api("ldst_shgl_ly6", { type1: 7 }).then((res) => {
                            this.PiechartsShow("chart1", res);
                        });
                    } else if (index === 3) {
                        $api("ldst_shgl_ly6", { type1: 8 }).then((res) => {
                            this.PiechartsShow("chart2", res);
                        });
                    }
                },
                //绘制折线图
                LinechartsShow(id, data) {
                    const myChartsRun = echarts.init(document.getElementById(id));
                    var fontColor = "#30eee9";
                    let option = {
                        grid: {
                            left: "5%",
                            right: "10%",
                            top: "25%",
                            bottom: "5%",
                            containLabel: true,
                        },
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        legend: {
                            show: true,
                            x: "center",
                            y: "5",
                            itemWidth: 20,
                            itemHeight: 20,
                            textStyle: {
                                color: "#fff",
                                fontSize: "28px",
                            },
                            // data: [legend],
                        },
                        xAxis: [
                            {
                                type: "category",
                                boundaryGap: false,
                                axisLabel: {
                                    color: "#fff",
                                    rotate: 45,
                                    fontSize: "28px",
                                },
                                axisLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#bbb",
                                    },
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#195384",
                                    },
                                },
                                data: data.map((item) => {
                                    return item.name;
                                }),
                            },
                        ],
                        yAxis: [
                            {
                                type: "value",
                                name: "",
                                min: 0,
                                // max: 1000,
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                axisLabel: {
                                    formatter: "{value}",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: "28px",
                                    },
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: "#fff",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#5087EC",
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "航运交通运输量",
                                type: "line",
                                stack: "总量",
                                // symbol: "circle",
                                symbolSize: 10,
                                itemStyle: {
                                    normal: {
                                        color: "#0092f6",
                                        lineStyle: {
                                            color: "#5087EC",
                                            width: 4,
                                        },
                                    },
                                },
                                // areaStyle: {
                                //     normal: {
                                //         color: new echarts.graphic.LinearGradient(
                                //             0,
                                //             0,
                                //             0,
                                //             1,
                                //             [
                                //                 {
                                //                     offset: 0,
                                //                     color: "rgba(71,121,213,1)",
                                //                 },
                                //                 {
                                //                     offset: 1,
                                //                     color: "rgba(71,121,213,0)",
                                //                 },
                                //             ],
                                //             false
                                //         ),
                                //     }
                                // },
                                data: data.map((item) => {
                                    return item.value;
                                }),
                            },
                        ],
                    };
                    myChartsRun.setOption(option);
                    tools.loopShowTooltip(myChartsRun, option, {
                        loopSeries: true,
                    }); //轮播
                },
                //绘制柱图
                BarchartsShow(data) {
                    const myChartsState = echarts.init(document.getElementById("jtgl-chart"));
                    var fontColor = "#30eee9";
                    let x = data.map((item) => {
                        return item.name;
                    });
                    let y1 = data.map((item) => {
                        return item.value;
                    });
                    let y2 = data.map((item) => {
                        return item.value1;
                    });
                    let option = {
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        legend: {
                            data: ["机车保有量", "驾驶人保有量"],
                            // align: "right",
                            top: 0,
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                        },
                        grid: {
                            left: "3%",
                            right: "4%",
                            bottom: "3%",
                            containLabel: true,
                        },
                        xAxis: {
                            data: x,
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: "#aaa",
                                    width: 1,
                                },
                            },
                            axisTick: {
                                show: true, //隐藏X轴刻度
                                alignWithLabel: true,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff", //X轴文字颜色
                                    fontSize: 28,
                                },
                                interval: 0,
                                // rotate: 30,
                            },
                        },
                        yAxis: [
                            {
                                type: "value",
                                name: "",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        width: 1,
                                        color: "#3d5269",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "机车保有量",
                                type: "bar",
                                data: y1,
                                itemStyle: {
                                    normal: {
                                        color: "#5087EC",
                                    },
                                },
                            },
                            {
                                name: "驾驶人保有量",
                                type: "bar",
                                data: y2,
                                itemStyle: {
                                    normal: {
                                        color: "#68BBC4",
                                    },
                                },
                            },
                        ],
                    };
                    myChartsState.setOption(option);
                    tools.loopShowTooltip(myChartsState, option, {
                        loopSeries: true,
                    }); //轮播
                },
                //绘制饼图
                PiechartsShow(id, data) {
                    const myChartsPerson = echarts.init(document.getElementById(id));
                    var fontColor = "#30eee9";
                    let option = {
                        grid: {
                            left: "3",
                            right: "22%",
                            // top: "30%",
                            // bottom: "15%",
                            containLabel: true,
                        },
                        legend: {
                            orient: "vartical",
                            x: "left",
                            top: "center",
                            left: "80%",
                            bottom: "0%",
                            // data: xdata,
                            itemWidth: 38,
                            itemHeight: 18,
                            itemGap: 16,
                            textStyle: {
                                color: "#fff",
                                fontSize: "28",
                            },
                            formatter: function (name) {
                                return "" + name;
                            },
                        },
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        color: ["#5087EC", "#68BBC4", "#58A55C", "#F2BD42", "#EE752F"],
                        series: [
                            {
                                name: name,
                                type: "pie",
                                radius: "75%",
                                center: ["40%", "50%"],
                                data: data,
                                itemStyle: {
                                    color: "#fff",
                                    emphasis: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: "rgba(0, 0, 0, 0.5)",
                                    },
                                },
                                itemStyle: {
                                    normal: {
                                        label: {
                                            show: true,
                                            color: "#fff",
                                            fontSize: 28,
                                            //	                            position:'inside',
                                            // formatter: "{b}:\n{d}%",
                                            formatter: "{b}:\n{c}",
                                        },
                                    },
                                    labelLine: { show: true },
                                },
                            },
                        ],
                    };

                    myChartsPerson.setOption(option);
                    tools.loopShowTooltip(myChartsPerson, option, {
                        loopSeries: true,
                    }); //轮播
                },
                
                openIframe() {
                    let Iframe = {
                        type: "openIframe",
                        name: 'ly6-select',
                        src: baseURL.url + "/static/citybrain3840/shgl/commont/ly6-select.html",
                        left: "1070px",
                        top: "230px",
                        width: "240px",
                        height: "280px",
                        zIndex: "10",
                        argument: {
                            status: "rkdt-select",
                        },
                    };
                    window.parent.postMessage(JSON.stringify(Iframe), "*");
                },

                closeIframe() {
                    top.commonObj.funCloseIframe({
                        name: "ly6-select",
                    });
                },
            },
            //项目生命周期
            mounted() {
                this.init();
                this.autoScroll();
                this.openIframe()
            },
        });
    </script>
</html>
