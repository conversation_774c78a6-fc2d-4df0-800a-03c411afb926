<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>电子政务项目情况-左</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/static/js/jslib/echarts-wordcloud.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <style scoped>
            * {
                margin: 0;
                padding: 0;
            }

            #app {
                width: 1050px;
                height: 1930px;
                background: url("/img/right-bg.png") no-repeat;
                background-size: 100% 100%;
                overflow: hidden;
            }

            .select {
                /* position: absolute; */
                z-index: 2;
                /* top: -15px; */
                margin-top: 10px;
                right: 20px;
                /* display: flex;
            align-items: center;
            justify-content: center; */
                padding-left: 45px;
            }

            .el-input__inner {
                font-size: 30px;
                /* width: 130px; */
                height: 50px;
                line-height: 50px;
                color: #fff;
                background-color: #011040b3;
            }

            .el-select-dropdown__item.hover,
            .el-select-dropdown__item:hover {
                background-color: #011040b3;
            }

            .el-input__icon {
                line-height: 48px;
            }

            .el-select-dropdown {
                background-color: #011040b3;
            }

            .el-select-dropdown__item {
                font-size: 30px;
                color: #fff;
            }

            .el-select .el-input .el-select__caret {
                font-size: 30px;
            }

            /* 表格 */
            .table {
                width: 95%;
                /* height: 100%; */
                padding: 10px;
                margin-left: 2.5%;
                box-sizing: border-box;
                overflow-y: auto;
            }

            .table .th {
                width: 100%;
                height: 80px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-style: italic;
                font-weight: 700;
                font-size: 20px;
                line-height: 80px;
                background: #00396f;
                color: #ffffff;
            }

            .table .th_td {
                letter-spacing: 0px;
                text-align: center;
                flex: 0.25;
            }

            .table .tbody {
                width: 100%;
                height: 1000px;
                overflow: hidden;
            }

            .table .tbody:hover {
                overflow-y: auto;
            }

            .table .tbody::-webkit-scrollbar {
                width: 4px;
                /*滚动条整体样式*/
                height: 4px;
                /*高宽分别对应横竖滚动条的尺寸*/
            }

            .table .tbody::-webkit-scrollbar-thumb {
                border-radius: 10px;
                background: #20aeff;
                height: 8px;
            }

            .table .tr:nth-child(2n) {
                background: rgb(0 57 111 / 30%);
            }

            .table .tr:nth-child(2n + 1) {
                /* background: #035b86; */
            }

            .table .tr {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100px;
                line-height: 100px;
                font-size: 28px;
                color: #ffffff;
                cursor: pointer;
                text-align: center;
            }

            /* .table .tr:nth-child(2n) {
            background: #00396f;
        }
  
            .table .tr:nth-child(2n+1) {
            background: #035b86;
        }
        */
            .table .tr:hover {
                background-color: #6990b6;
            }

            .table .tr_td {
                letter-spacing: 0px;
                text-align: center;
                box-sizing: border-box;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .left-bottom {
                display: flex;
                margin-top: 40px;
                margin-left: 40px;
            }

            .left-bottom-item {
                display: flex;
                align-items: center;
                flex-direction: column;
                margin-right: 30px;
                cursor: pointer;
            }

            .left-bottom-item > div:first-child {
                font-size: 32px;
                color: #fff;
            }

            .left-bottom-item > div:nth-child(2) {
                font-size: 44px;
                color: #02c1d7;
            }

            .el-pager li {
                font-size: 22px;
            }
        </style>
    </head>

    <body>
        <div id="app">
            <nav style="padding: 20px 45px">
                <s-header-title
                    style="width: 100%"
                    title="项目申报情况汇总展示"
                    htype="2"
                    :click-flag="true"
                    @click="addMap('sbqk')"
                ></s-header-title>
            </nav>
            <div class="select">
                <el-select v-model="value0" placeholder="请选择" @change="change0()">
                    <el-option
                        v-for="item in options0"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    ></el-option>
                </el-select>
            </div>
            <div class="table table1">
                <div class="th">
                    <div class="th_td" style="flex: 0.25" v-for="(item,index) in theadList" :key="index">{{item}}</div>
                </div>
                <div class="tbody" style="overflow: hidden">
                    <div
                        class="tr"
                        style="display: flex; justify-content: unset"
                        v-for="(item ,i) in tbodyList"
                        :key="i"
                        @click="leftClick1(i)"
                    >
                        <div style="flex: 0.25">{{item.xmmc}}</div>
                        <div style="flex: 0.25">{{item.sbbm}}</div>
                        <div style="flex: 0.25">{{item.sbzje}}</div>
                        <div style="flex: 0.25">{{item.xmdd}}</div>
                    </div>
                </div>
            </div>
            <div
                style="display: flex; justify-content: right; margin-right: 40px; margin-top: 20px; margin-bottom: 20px"
            >
                <el-pagination
                    background
                    layout="prev, pager, next"
                    :page-size="pageSize"
                    :current-page="currentPage"
                    @current-change="changePage"
                    :total="total"
                ></el-pagination>
            </div>
            <nav style="padding: 20px 45px">
                <s-header-title
                    style="width: 100%"
                    title="项目评审情况汇总展示"
                    htype="2"
                    @click="addMap('psqk')"
                    :click-flag="true"
                ></s-header-title>
            </nav>
            <div class="left-bottom">
                <div class="left-bottom-item" v-for="(item,i) in data1" :key="i" @click="leftClick2(i)">
                    <div>{{item.name}}</div>
                    <div>{{item.value}}</div>
                    <img src="../../img/Base4.png" alt="" />
                </div>
            </div>
        </div>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script>
            let vm = new Vue({
                el: "#app",
                data: {
                    map1: false,
                    map2: false,
                    value0: 0,
                    options0: [
                        { label: "项目新申报项目", value: 0 },
                        { label: "运维项目", value: 1 },
                        { label: "数字化改革项目", value: 2 },
                        { label: "多跨协同项目", value: 3 },
                        { label: "待办任务", value: 4 },
                        { label: "待办申报", value: 5 },
                    ],
                    theadList: ["项目名称", "申报部门", "申报总金额", "项目地点"],
                    tbodyList: [],
                    data1: [],
                    currentPage: 1,
                    pageSize: 10,
                    total: 100,
                },
                mounted() {
                    //项目申报情况汇总展示
                    $api("ldst_sdsjjldst_dzzwxm-001", { type: "1-1" }).then((res) => {
                        this.tbodyList = res;
                        this.total = this.tbodyList.length;
                    });

                    //项目评审情况汇总展示
                    $api("ldst_sdsjjldst_dzzwxm-002").then((res) => {
                        this.data1 = res;
                    });
                },
                methods: {
                    addMap(name) {
                        let code = name == "sbqk" ? "景区" : "博物馆";
                        if (name == "sbqk") {
                            this.map1 = !this.map1;
                            if (this.map1) {
                                this.getPoint(code);
                            } else {
                                this.rmPoint(code);
                            }
                        } else {
                            this.map2 = !this.map2;
                            if (this.map2) {
                                this.getPoint(code);
                            } else {
                                this.rmPoint(code);
                            }
                        }
                    },
                    getPoint(code) {
                        $api("yxzl_szwh_center011", { code }).then((res) => {
                            let pointData = [];
                            let icon = "szwh-" + code;
                            let key = [];
                            let value = [];
                            let title = "";
                            if (code == "景区") {
                                key = ["项目名称", "申报部门", "申报总金额", "项目地点"];
                                value = ["多跨协同项目", "金华市卫健委", "1856万元", "金华市婺城区阳光路33号"];
                                title = "项目申报详情";
                            } else {
                                key = ["项目名称", "评审部门", "评审状态", "项目地点"];
                                value = ["乡商会项目", "金华市建设局", "已评审", "金华市磐安县磐安路63号"];
                                title = "项目评审详情";
                            }
                            res.forEach((obj, index) => {
                                if (
                                    obj.lng.split(",")[0].indexOf("无") < 0 ||
                                    obj.lng.split(",")[1].indexOf("无") < 0 ||
                                    obj.lng.split(",")[0] == 0 ||
                                    obj.lng.split(",")[1] == 0
                                ) {
                                    let str = {
                                        data: {
                                            pointId: "dzzw",
                                            title: title,
                                            key: key,
                                            value: value,
                                            obj,
                                        },
                                        point: obj.lng,
                                    };
                                    pointData.push(str);
                                }
                            });
                            top.document.getElementById("map").contentWindow.Work.funChange(
                                JSON.stringify({
                                    funcName: "pointLoad", //功能名称
                                    pointType: icon, //点位类型图标
                                    pointId: "dzzw-" + code,
                                    setClick: false,
                                    pointData: pointData,
                                    imageConfig: { iconSize: 0.9 },
                                    size: [0.01, 0.01, 0.01, 0.01],
                                    popup: {
                                        offset: [50, 30],
                                    },
                                })
                            );
                        });
                    },
                    rmPoint(code) {
                        top.document.getElementById("map").contentWindow.Work.funChange(
                            JSON.stringify({
                                funcName: "rmPoint",
                                pointId: "dzzw-" + code, //传id清除单类，不传清除所有
                            })
                        );
                    },
                    change0() {
                        if (this.value0 == 0) {
                            $api("ldst_sdsjjldst_dzzwxm-001", { type: "1-1" }).then((res) => {
                                this.tbodyList = res;
                                this.total = this.tbodyList.length;
                            });
                        } else if (this.value0 == 1) {
                            $api("ldst_sdsjjldst_dzzwxm-001", { type: "1-2" }).then((res) => {
                                this.tbodyList = res;
                                this.total = this.tbodyList.length;
                            });
                        } else if (this.value0 == 2) {
                            $api("ldst_sdsjjldst_dzzwxm-001", { type: "1-3" }).then((res) => {
                                this.tbodyList = res;
                                this.total = this.tbodyList.length;
                            });
                        } else if (this.value0 == 3) {
                            $api("ldst_sdsjjldst_dzzwxm-001", { type: "1-4" }).then((res) => {
                                this.tbodyList = res;
                                this.total = this.tbodyList.length;
                            });
                        } else if (this.value0 == 4) {
                            $api("ldst_sdsjjldst_dzzwxm-001", { type: "1-5" }).then((res) => {
                                this.tbodyList = res;
                                this.total = this.tbodyList.length;
                            });
                        } else if (this.value0 == 5) {
                            $api("ldst_sdsjjldst_dzzwxm-001", { type: "1-6" }).then((res) => {
                                this.tbodyList = res;
                                this.total = this.tbodyList.length;
                            });
                        }
                    },
                    leftClick1(i) {
                        top.commonObj.openWinHtml(
                            "1800",
                            "1630",
                            "static/citybrain3840/sdsjldst/pages/dzzwxmqk/xmsb.html"
                        );
                    },
                    leftClick2(i) {
                        top.commonObj.openWinHtml(
                            "1800",
                            "1630",
                            "static/citybrain3840/sdsjldst/pages/dzzwxmqk/xmps.html"
                        );
                    },
                    changePage(val) {},
                },
            });
        </script>
    </body>
</html>
