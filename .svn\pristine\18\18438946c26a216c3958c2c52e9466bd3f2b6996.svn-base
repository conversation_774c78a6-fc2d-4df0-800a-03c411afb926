<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title></title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/elementui/css/index.css" />
  <link rel="stylesheet" href="/static/citybrain/scjg/css/hjbh-right/common.css" />
  <script src="/Vue/vue.js"></script>
  <script src="/elementui/js/index.js"></script>
  <script src="/static/citybrain/hjbh/js/echarts.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/static/js/jslib/Emiter.js"></script>
  <script src="../../scjg/js/lib/auto-tooltip.js"></script>
  <style>
    [v-cloak] {
    display: none;
}
    * {
      margin: 0;
      padding: 0;
    }

    #szfz-right {
      /* background:url("/img/right-bg.png") no-repeat;
        background-size: 100%; */
      /* width: 2100px;
    height: 1930px; */
      background: linear-gradient(180deg, #0E1A40, #064069);
      opacity: 0.88;
      display: flex;
    }

    .container {
      width: 2100px;
      height: 1930px;
      opacity: 0.88;
      display: flex;
    }

    .part {
      width: 50%;
      height: 100%;
      /* display: flex;
      align-items: center;
      flex-direction: column; */
    }

    .part-title {
      width: 100%;
      height: 84px;
      font-size: 54px;
      text-align: center;
      margin-top: 50px;
      /* background: url('../img/title-back.png'); */
      background-size: 100% 100%;
    }

    .part-con {
      width: 100%;
      height: calc(100% - 124px);
    }

    .hlw_zf {
      position: relative;
    }

    .hlw_zf_part {
      position: absolute;
      width: 369px;
      height: 334px;
      text-align: center;
      align-items: center;
    }

    .hlw_zf>div:nth-child(1) {
      width: 616px;
      height: 246px;
      top: 80px;
      left: 208px;
      background: url('../img/hlw_zf_3.png') no-repeat;
      background-size: cover;
    }

    .hlw_zf>div:nth-child(2) {
      top: 225px;
      left: 88px;
      width: 269px;
      height: 180px;
      background: url('../img/hlw_zf_4.png') no-repeat;
      background-size: 100% 100%;
    }

    .hlw_zf>div:nth-child(3) {
      top: 225px;
      left: 650px;
      width: 269px;
      height: 180px;
      background: url('../img/hlw_zf_4.png') no-repeat;
      background-size: 100% 100%;
    }

    .zfxn_1 {
      display: flex;
      align-items: center;
    }

    .zfxn_1_item {
      display: flex;
    }

    .zfxn_1_item>div:nth-child(2)>div:first-child {
      font-size: 32px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: #D6E7F9;
      margin-left: 16px;
    }

    .zfxn_1_item>div:nth-child(2)>div:nth-child(2) {
      font-size: 60px;
      font-family: Bebas Neue;
      font-weight: 400;
      margin-left: 16px;
    }

    .zfxn_1_item>div:nth-child(2)>div:nth-child(2)>span {
      font-size: 30px;
      margin-left: -20px;
    }

    .hlw_jg {
      display: flex;
      flex-wrap: wrap;
      marign-top: 20px;
    }

    .hlw_jg_chart {
      width: 50%;
      height: 255px;
      padding: 50px 80px;
      box-sizing: border-box;
      align-items: center;
    }

    .hlw_jg_title {
      width: 40%;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 47px;
      margin-left: 10px;
      text-align: center;
    }

    .fxczf {
      width: 100%;
      height: 510px;
      /* height:410px;
        background: url('../img/fxczf-back.png');
        background-size: 100% 100%;
        margin-top:70px; */
      padding: 70px;
      margin-top: -30px;
      box-sizing: border-box;
    }

    .fxczf-box {
      width: 100%;
      height: 100%;
      padding: 10px;
      box-sizing: border-box;
      background: url('../img/fzfxc-border-1.png');
      background-size: 100% 100%;
      border-radius: 20px;
    }

    .fxczf-title {
      text-align: center;
    }

    .yfxz {
      width: 100%;
      height: 100%;
    }

    .yfxz-part {
      margin: 30px auto;
      width: 901px;
      height: 66px;
      background: url('../img/yfxz-back.png') no-repeat;
      background-size: 100% 100%;
    }

    .yfxz-part-img {
      position: relative;
      top: -15px;
      left: 200px;
    }

    .yfxz-part-name {
      font-size: 32px;
      color: #FFFFFF;
      margin: 0 30px 0 30px;
      position: relative;
      top: -28px;
      left: 200px;
    }

    .yfxz-part-value {
      font-size: 60px;
      position: relative;
      top: -28px;
      left: 200px;
    }

    .yfss {
      width: 1060px;
      height: 449px;
      background: url('../img/yfss-back.png');
      background-size: 100% 100%;
      margin-top: 70px;
      position: relative;
    }

    .yfss-part {
      position: absolute;
      text-align: center;
    }

    .yfss :nth-child(1) {
      top: -25px;
      left: 84px;
    }

    .yfss :nth-child(2) {
      top: -25px;
      left: 880px;
    }

    .yfss :nth-child(3) {
      top: 144px;
      left: 486px;
    }

    .yfss :nth-child(4) {
      top: 200px;
      left: 84px;
    }

    .yfss :nth-child(5) {
      top: 200px;
      left: 880px;
    }

    .el-progress__text {
      font-size: 45px !important;
      /* color: #fff; */
      background: linear-gradient(to bottom, #ccf4ff, #ffffff, #00baf8, #ffffff);
      -webkit-background-clip: text;
      color: transparent;
    }

    .el-carousel__item h3 {
      color: #475669;
      font-size: 14px;
      opacity: 0.75;
      line-height: 200px;
      margin: 0;
    }

    /* .el-carousel__item:nth-child(3n) {
        background-color: #0b2747;
        left:-140px;
      }

      .el-carousel__item:nth-child(3n+1) {
        background-color: #0b2747;
        left:-140px;
      }
      .el-carousel__item:nth-child(3n+2) {
        background-color: #0b2747;
        left:-140px;
      } */
    .el-carousel__item--card {
      width: 80%;
    }

    .el-carousel__item {
      left: -140px;
    }

    .el-carousel__button {
      width: 15px;
      height: 15px;
      border-radius: 50%;
    }

    .r-r-1 {
      display: flex;
      align-items: center;
    }

    .r-r-1-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .r-r-1-item>div:first-child {
      background-image: url('../img/new-r-3.png');
      background-size: cover;
      width: 235px;
      height: 256px;
      margin-top: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .r-r-1-item>div:nth-child(2) {
      font-size: 32px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #FFFFFF;

    }

    .r-r-1-item>div:nth-child(3) {
      display: flex;
      align-items: center;
      margin-top: 10px;
    }

    .r-r-1-item>div:nth-child(4) {
      display: flex;
      align-items: center;
      margin-top: 10px;
    }

    .r-r-2 {
      display: flex;
      flex-wrap: wrap;
    }

    .r-r-2-item {
      width: 50%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 10px;
    }

    .r-r-2-item>div:first-child {
      background-image: url('../img/new-r-4.png');
      width: 281px;
      height: 239px;
      line-height: 229px;
      text-align: center;
      font-size: 70px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #FFFFFF;
    }

    .r-r-2-item-word {
      font-size: 32px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #FFFFFF;
    }

    .r-r-3 {
      display: flex;
      /* align-items: flex-end; */
      padding:0 80px;
      padding-top: 40px;
    }

    .r-r-3>div {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      word-break: break-all;
    }

    .r-r-3>div>div:nth-child(1) {
      font-size: 30px;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      ;
      text-shadow: 0px 2px 27px rgba(0, 0, 0, 0.6700);
    }

    .r-r-3-4 {
      font-size: 30px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #D6E7F9;
      line-height: 39px;
      width: 150px;
      text-align: center;
    }

    /* 蓝白色渐变 */
    .lanbailine {
      background: linear-gradient(to bottom, #FFFFFF, #CAFFFF, #00C0FF);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 40px;
      letter-spacing: 2px;
      font-family: "思源黑体 CNBOLD";
      font-weight: bold;

    }

    /* 青色渐变 */
    .qingline {
      font-size: 30px;
      font-family: Source Han Sans SC;
      background: linear-gradient(180deg, #f0ffd7, #ffffff, #a9db52, #f4f1ff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    /* 金色渐变 */
    .goldline {
      background: linear-gradient(180deg, #ffeccb, #ffffff, #ffc460, #ffe2b0);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: 700;
      font-size: 30px;
    }

    .el-progress-circle__track {
      stroke: transparent;
    }

    .goldsize1 {
      font-size: 30px;
      font-family: PangMenZhengDao;
      font-weight: 400;
    }
    .header-title3 > span[data-v-26e97dda] {
      cursor: not-allowed !important;
    }
    .header-title3 > span[data-v-26e97dda] {
      cursor: not-allowed !important;
    }
  </style>
</head>

<body>
  <div id="szfz-right" v-cloak class="container">
    <div class="part fxsb_part">
      <!--  @click="click('风险识别与管控')" -->
      <div class="part-title">
        <s-header-title-3  title="执法与司法" />
      </div>
      <div class="part-con">
        <div style="width:100%;height:30%;">
        <nav style="margin: 10px 0 0">
          <s-header-title2 style="width: 100%;" title="互联网+执法" />
        </nav>
          <div class="hlw_zf">
            <div class="hlw_zf_part">
              <div style="margin: -40px;">
                <p class="s-c-white s-font-30">{{wifi_top.nameAll}}</p>
                <p class="s-c-yellow-gradient s-w7 s-font-40">{{wifi_top.valueAll}}</p>
              </div>
            </div>
            <div class="hlw_zf_part">
              <div style="width: 100%;line-height: 45px;margin-top: 20px;">
                <p class="s-c-red-gradient s-font-30">立案数 {{wifi_top.onlineli}}</p>
                <p class="s-c-green-gradient s-font-30">结案数　{{wifi_top.onlinejie}}</span>
                <p class="s-c-white s-font-30" style="margin-top:40px">线下办案</p>
              </div>
            </div>
            <div class="hlw_zf_part">
              <div style="width: 100%;line-height: 45px;margin-top: 20px;">
                <p class="s-c-red-gradient s-font-30">立案数 {{wifi_top.wifili}}</p>
                <p class="s-c-green-gradient s-font-30">结案数　{{wifi_top.wifijie}}</span>
                <p class="s-c-white s-font-30" style="margin-top:40px">网上办案</p>
              </div>
            </div>
          </div>
        </div>
        <div style="width:100%;height:38%">
            <nav style="margin: 10px 0 0">
          <s-header-title2 style="width: 100%;" title="执法效能" />
        </nav>
          <!-- <s-header-title2 title="互联网+监管"></s-header-title2> -->
          <div class="zfxn_1">
            <div class="zfxn_1_item" v-for="(item,i) in zfxnList" style="margin-left: 120px;margin-top: 30px;">
              <img :src="'../img/new-r-'+(i+1)+'.png'" alt="">
              <div>
                <div>{{item.name}}</div>
                <div class="s-c-yellow-gradient">{{item.value}} <span>{{item.unit}}</span></div>
              </div>
            </div>
          </div>

          <div id="rightecharts1" style="width:100%;height:500px"></div>
        </div>
        <div style="width:100%;height:33%">
            <nav style="margin: 10px 0 0">
          <s-header-title2 style="width: 100%;" title="执法案例" />
        </nav>
          <div class="fxczf">
            <el-carousel :interval="4000" type="card" height="340px">
              <el-carousel-item v-for="item in 6" :key="item">
                <div class="fxczf-box">
                  <div class="fxczf-title s-c-red-gradient s-font-35 s-w7">机动车违停</div>
                  <div style="display: flex;justify-content: space-evenly;margin-top:20px">
                    <img src="../img/lunbo_cs.jpg" alt="" width="280" height="210">
                    <div class="s-font-30 s-c-white" style="line-height: 45px;">
                      <p><span class="s-c-yellow-gradient">地址：</span>**区**路附近</p>
                      <p><span class="s-c-yellow-gradient">时间：</span>2022.5.17 17:17:08</p>
                      <p><span class="s-c-yellow-gradient">案件类型：</span>出店经营</p>
                      <p><span class="s-c-yellow-gradient">案由：</span>店家出店经营,执法人<br>员让店主将桌子移除该区域...</p>
                      <!--<p><span class="s-c-yellow-gradient">历史违章次数：</span>2次</p>-->
                    </div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
      </div>
    </div>
    <div class="part jdzy_part">
      <!--  @click="click('监督制约与服务')" -->
      <div class="part-title">
        <s-header-title-3  title="监督制约与服务" />
      </div>
      <div class="part-con">
        <div style="width:100%;height:30%">
           <nav style="margin: 10px 0 0">
          <s-header-title2 style="width: 100%;" title="司法监督" />
        </nav>
          <!-- <s-header-title2 title="依法行政"></s-header-title2> -->
          <div class="r-r-1">
            <div class="r-r-1-item">
              <div>
                <el-progress type="circle" :percentage="sfjdList.zb1" :width="170" :color="'#5180F8'" :stroke-width="18"
                  :show-text="true" class="progress2">
                </el-progress>
              </div>
              <div>{{sfjdList.name1}}</div>
              <div>
                <div class="qingline">同比{{sfjdList.falg1?'下降':'增加'}} {{sfjdList.tb1}}%</div>
                <img :src="sfjdList.falg1?'../img/del1.png':'../img/zz1.png'" alt="">
              </div>
              <div>
                <img src="../img/jin.png" style="margin-right:10px ;" alt="">
                <div class="goldline goldsize1">降幅全省最大</div>
              </div>
            </div>
            <div class="r-r-1-item">
              <div>
                <el-progress type="circle" :percentage="sfjdList.zb2" :width="170" :color="'#5180F8'" :stroke-width="18"
                  :show-text="true" class="progress2">
                </el-progress>
              </div>
              <div>{{sfjdList.name2}}</div>
              <div>
                <div class="qingline">同比{{sfjdList.falg2?'下降':'增加'}} {{sfjdList.tb2}}%</div>
                <img :src="sfjdList.falg2?'../img/del1.png':'../img/zz1.png'" alt="">
              </div>
              <div>
                <img src="../img/jin.png" style="margin-right:10px ;" alt="">
                <div class="goldline goldsize1">位列全省最低</div>
              </div>
            </div>
            <div class="r-r-1-item">
              <div>
                <el-progress type="circle" :percentage="sfjdList.zb3" :width="170" :color="'#5180F8'" :stroke-width="18"
                  :show-text="true" class="progress2">
                </el-progress>
              </div>
              <div>{{sfjdList.name3}}</div>
              <div>
                <div class="qingline">同比{{sfjdList.falg3?'下降':'增加'}} {{sfjdList.tb3}}%</div>
                <img :src="sfjdList.falg3?'../img/del1.png':'../img/zz1.png'" alt="">
              </div>
              <div>
                <img src="../img/jin.png" style="margin-right:10px ;" alt="">
                <div class="goldline goldsize1">降幅全省第一</div>
              </div>
            </div>
          </div>
        </div>
        <div style="width:100%;height:38%">
           <nav style="margin: 10px 0 0">
          <s-header-title2 style="width: 100%;" title="监察监督" />
        </nav>
          <!-- <s-header-title2 title="依法诉讼"></s-header-title2> -->
          <div class="r-r-2">
            <div class="r-r-2-item" v-for="(item,i) in jcjdList">
              <div>
                {{item.value}}
              </div>
              <div class="r-r-2-item-word">{{item.name}}</div>
            </div>
          </div>
        </div>
        <div style="width:100%;height:33%">
           <nav style="margin: 0px 0 -10px 0">
          <s-header-title2 style="width: 100%;" title="法律援助" />
        </nav>
          <!-- <header class="subTitle s-rela" style="left: -20px;top:20px;">
            <img src="/static/citybrain/scjg/img/scztjg/subTitleLeft.png" alt="subTitleLeft" />
            <div class="lanbailine" style="margin: 0 20px;">
              法律援助
            </div>
            <img src="/static/citybrain/scjg/img/scztjg/subTitleRight.png" alt="subTitleRight" />
          </header> -->
          <div class="r-r-3">
            <div v-for="(item,i) in rightdata3" :key="i">
              <div class="lanbailine"  style="font-size:30px">{{item.y_value+item.unit}}</div>
              <img style="margin-bottom: -20px;"
                :style="{'height':280*(1-(i/10))+'px','marginTop':(280-280*(1-(i/10)))+'px'}" src="../img/new-r-6.png"
                alt="">
              <img src="../img/new-r-5.png" alt="">
              <div class="r-r-3-4">{{item.x_value}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>

</html>


<script src="/static/js/jslib/axios.min.js"></script>
<!-- <script src="/static/js/jslib/http.interceptor.js"></script> -->
<script>
  var vm = new Vue({
    el: '#szfz-right',
    data: {
      wifi_top:{},
      zfxnList:[],
      sfjdList:[],
      jcjdList:[],
      progressList: [
        {
          name: "抽查事项覆盖率",
          value: 100,
          color: "#FFC460",
        },
        {
          name: "任务完成率",
          value: 100,
          color: "#A9DB52",
        },
        {
          name: "应用信用规则率",
          value: 50,
          color: "#00C0FF",
        },
        {
          name: "跨部门监管率",
          value: 5,
          color: "#22E8E8",
        }
      ],
      yfxzList: [
        {
          img: "../img/yfxz-1.png",
          name: "区政府规范文件",
          value: 25,
        },
        {
          img: "../img/yfxz-2.png",
          name: "区政府行政合同",
          value: 5,
        },
        {
          img: "../img/yfxz-3.png",
          name: "街镇重大行政决策",
          value: 2,
        },
        {
          img: "../img/yfxz-4.png",
          name: "区政府重大行政决策",
          value: 16,
        },
        {
          img: "../img/yfxz-5.png",
          name: "区政府重大行政",
          value: 15,
        }
      ],
      rightdata3: [
        { a: "827", b: "法律援助案件数", c: '件' },
      ]
    },
    mounted() {
      this.initApi()
    },
    methods: {
      async initApi(){
        let that=this;
        $api('/szfzRight11').then(res=>{
          that.wifi_top={
            nameAll:res[0].name,
            valueAll:res[0].value,
            onlineli:res[1].value,
            onlinejie:res[2].value,
            wifili:res[3].value,
            wifijie:res[4].value,
          }
        })
        that.zfxnList=await $api('/szfzRight12')
        $api('/szfzRight21').then(res=>{
          that.sfjdList={
            name1:res[0].name,
            zb1:Number(res[0].value),
            tb1:res[3].value,
            falg1:res[3].value.split('')[0]=='-',
            name2:res[1].name,
            zb2:Number(res[1].value),
            tb2:res[4].value,
            falg2:res[4].value.split('')[0]=='-',
            name3:res[2].name,
            zb3:Number(res[2].value),
            tb3:res[5].value,
            falg3:res[5].value.split('')[0]=='-',
          }
        })
        that.jcjdList= await $api('/szfzRight22')
        that.rightdata3=await $api('/szfz_flyz')
        $api('/szfz_zfxn').then(res=>{
          let xdata=res.filter((ele) => ele.type_name == "立案数").map((ele) => ele.x_value);
          let lidata=res.filter((ele) => ele.type_name == "立案数").map((ele) =>Number( ele.y_value));
          let jiedata=res.filter((ele) => ele.type_name == "结案数").map((ele) => Number(ele.y_value));
          that.getEcharts02('rightecharts1',xdata,lidata,jiedata)
        })
      },
      // click(name) {
      //   top.window.emiter.emit(top.EventType.szfzEmit, name)
      // }, 
      getEcharts02(dom,xdata,lidata,jiedata) {
        let myChart = echarts.init(document.getElementById(dom));
        // 指定图表的配置项和数据
        let option = {
          color: ["#00C0FF","#A9DB52","#FF4949", "#FFC665"],
          title: {
            text: "",
            top: "5px",
            left: "10px",
            textStyle: {
              color: "#fff",
              fontSize: 28,
              fontFamily: "SourceHanSansCN-Medium",
            },
          },
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            backgroundColor: "#000000",
            textStyle: {
              color: "#fff",
              fontSize: 16,
            },
          },
          legend: {
            selectedMode: true,
            top: "30px",
            right: "24%",
            textStyle: {
              color: "#fff",
              fontSize: 28,
              fontFamily: "SourceHanSansCN-Medium",
            },
            itemWidth: 18,
            itemHeight: 18,
          },
          grid: {
            top: "18%",
            left: "8%",
            right: "8%",
            bottom: "2%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: xdata,
              splitLine: { show: false },
              axisTick: {
                //y轴刻度线
                show: false,
              },
              axisLine: {
                lineStyle: {
                  color: "#0f2944", // 颜色
                  width: 1, // 粗细
                },
              },
              axisLabel: {
                interval: 0,
                rotate: 40,
                textStyle: {
                  color: "#fff",
                  fontSize: 28,
                  fontFamily: "SourceHanSansCN-Medium",
                },
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              splitLine: {
                lineStyle: {
                  color: "#0F3D60",
                },
              },
              axisLabel: {
                textStyle: {
                  color: "#fff",
                  fontSize: 20,
                  fontFamily: "SourceHanSansCN-Medium",
                },
              },
            },

            /*{
              type: "value",
              name: "",
              min: 0,
              max: 100,
              interval: 20,
              nameTextStyle: {
                color: "#d6e7f9",
                fontSize: 28,
              },
              axisLine: {
                //纵轴线
                show: false,
                lineStyle: {
                  color: "rgba(119,179,241,0.5)",
                  width: 1,
                },
              },
              axisLabel: {
                //纵轴线标签
                formatter: "{value} %",
                textStyle: {
                  color: "#d6e7f9",
                  fontSize: 20,
                },
                margin: 20,
              },
              // splitNumber: 4,
              splitLine: {
                //分隔线
                show: false,
                lineStyle: {
                  color: "#114c93",
                  width: 1,
                },
              },
            },*/
          ],
          series: [
            {
              cursor: "auto",
              name: "立案数",
              type: "bar",
              // data: ['婺城区', '金东区', '兰溪市', '义乌市', '永康市', '东阳市', '浦江县', '磐安县', '开发区'],
              data:lidata,
              // itemStyle: {
              //   normal: {
              //     color: "#e86056",
              //     label: {
              //       show: true, //开启显示
              //       position: "top", //在上方显示
              //       textStyle: {
              //         //数值样式
              //         color: "#fff",
              //         fontSize: 16,
              //       },
              //     },
              //   },
              // },
              barWidth: 16,
              itemStyle: {
                normal: {
                  // lineStyle: {
                  //   color: '#00C0FF'
                  // },
                  // color: "#00C0FF",
                  // barBorderRadius: [10, 10, 10, 10,6],
                  // color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  //   {
                  //     // 四个数字分别对应 数组中颜色的开始位置，分别为 右，下，左，上。例如（1,0,0,0 ）代表从右边开始渐
                  //     // 变。offset取值为0~1，0代表开始时的颜色，1代表结束时的颜色，柱子表现为这两种颜色的渐变。
                  //     offset: 0,
                  //     color: "rgb(0, 192, 255,0)",
                  //   },
                  //   {
                  //     offset: 1,
                  //     color: "rgb(0, 191, 255,0.6)",
                  //   },
                  // ]),
                },
              },
              label: {
                show: false, //开启显示
                position: "top", //在上方显示
                textStyle: {
                  //数值样式
                  color: "#FFFFFF",
                  fontFamily: "SourceHanSansCN-Regular",
                  fontSize: 28,
                },
              },
            },
            {
              name: "结案数",
              data:jiedata,
              type: "bar",
              // data: [5, 5, 5, 5],
              // itemStyle: {
              //   normal: {
              //     color: "#F5CC53",
              //     label: {
              //       show: true, //开启显示
              //       position: "top", //在上方显示
              //       textStyle: {
              //         //数值样式
              //         color: "#fff",
              //         fontSize: 16,
              //       },
              //     },
              //   },
              // },
              barWidth: 16,
              itemStyle: {
                normal: {
                  // barBorderRadius: [10, 10, 0, 0,6],
                  // color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  //   {
                  //     // 四个数字分别对应 数组中颜色的开始位置，分别为 右，下，左，上。例如（1,0,0,0 ）代表从右边开始渐
                  //     // 变。offset取值为0~1，0代表开始时的颜色，1代表结束时的颜色，柱子表现为这两种颜色的渐变。
                  //     offset: 0,
                  //     color: "rgb(255, 196, 96,0)",
                  //   },
                  //   {
                  //     offset: 0.46,
                  //     color: "rgb(169, 219, 82,0.6)",
                  //   },
                  // ]),
                },
              },
              label: {
                show: false, //开启显示
                position: "top", //在上方显示
                textStyle: {
                  //数值样式
                  color: "#FFFFFF",
                  fontFamily: "SourceHanSansCN-Regular",
                  fontSize: 28,
                },
              },
            },
            // {
            //   name: "立案率",
            //   type: "line",
            //   data: [11.8, 17.9, 22.1, 16.8, 23.7, 18.7, 22.6, 19.2, 57.2, ],
            //   // itemStyle: {
            //   //   normal: {
            //   //     color: "#6AE4B2",
            //   //     label: {
            //   //       show: true, //开启显示
            //   //       position: "top", //在上方显示
            //   //       textStyle: {
            //   //         //数值样式
            //   //         color: "#fff",
            //   //         fontSize: 16,
            //   //       },
            //   //     },
            //   //   },
            //   // },
            //   barWidth: 14,
            //   yAxisIndex: 1,
            //   itemStyle: {
            //     // normal: {
            //     //   // barBorderRadius: [10, 10, 0, 0],
            //     //   color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            //     //     {
            //     //       // 四个数字分别对应 数组中颜色的开始位置，分别为 右，下，左，上。例如（1,0,0,0 ）代表从右边开始渐
            //     //       // 变。offset取值为0~1，0代表开始时的颜色，1代表结束时的颜色，柱子表现为这两种颜色的渐变。
            //     //       offset: 0,
            //     //       color: "rgb(169, 219, 82,0)",
            //     //     },
            //     //     {
            //     //       offset: 1,
            //     //       color: "rgb(169, 219, 82,0.46)",
            //     //     },
            //     //   ]),
            //     // },
            //   },
            //   label: {
            //     show: false, //开启显示
            //     position: "top", //在上方显示
            //     textStyle: {
            //       //数值样式
            //       color: "#FFFFFF",
            //       fontFamily: "SourceHanSansCN-Regular",
            //       fontSize: 28,
            //     },
            //   },
            // },
            // {
            //   name: "结案率",
            //   type: "line",
            //   data: [8.8, 15.9, 19.1, 18.8, 31.7, 22.7, 62.6, 19.2, 47.2,],
            //   // itemStyle: {
            //   //   normal: {
            //   //     color: "#6AE4B2",
            //   //     label: {
            //   //       show: true, //开启显示
            //   //       position: "top", //在上方显示
            //   //       textStyle: {
            //   //         //数值样式
            //   //         color: "#fff",
            //   //         fontSize: 16,
            //   //       },
            //   //     },
            //   //   },
            //   // },
            //   barWidth: 14,
            //   yAxisIndex: 1,
            //   itemStyle: {
            //     // normal: {
            //     //   // barBorderRadius: [10, 10, 0, 0],
            //     //   color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            //     //     {
            //     //       // 四个数字分别对应 数组中颜色的开始位置，分别为 右，下，左，上。例如（1,0,0,0 ）代表从右边开始渐
            //     //       // 变。offset取值为0~1，0代表开始时的颜色，1代表结束时的颜色，柱子表现为这两种颜色的渐变。
            //     //       offset: 0,
            //     //       color: "rgb(169, 219, 82,0)",
            //     //     },
            //     //     {
            //     //       offset: 1,
            //     //       color: "rgb(169, 219, 82,0.46)",
            //     //     },
            //     //   ]),
            //     // },
            //   },
            //   label: {
            //     show: false, //开启显示
            //     position: "top", //在上方显示
            //     textStyle: {
            //       //数值样式
            //       color: "#FFFFFF",
            //       fontFamily: "SourceHanSansCN-Regular",
            //       fontSize: 28,
            //     },
            //   },
            // },
          ],
        };
        myChart.setOption(option, true);
        tools.loopShowTooltip(myChart, option, {
          loopSeries: true,
        }) //轮播
      },
    },
  })
</script>
