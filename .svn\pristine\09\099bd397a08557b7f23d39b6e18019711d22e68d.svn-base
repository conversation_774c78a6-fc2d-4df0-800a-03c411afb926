<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>市场主体年度统计弹窗</title>
        <script src="/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain/ggfw/css/zjfw-dialog.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
    </head>
    <style>
        /* 教育服务弹窗 */
        .container {
            width: 1958px;
            height: 1024px;
            background-color: #031827;
            box-shadow: -3px 2px 35px 0px #000000;
            border: 1px solid #359cf8;
            border-radius: 60px;
        }

        .container .head {
            width: 100%;
            height: 100px;
            line-height: 100px;
            background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%), linear-gradient(#ffffff, #ffffff);
            background-blend-mode: normal, normal;
            padding: 10px 50px;
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: center;
            justify-content: space-between;
            border-top-left-radius: 60px;
            border-top-right-radius: 60px;
        }

        .head span {
            font-size: 48px !important;
            font-weight: 500;
            color: #fff;
            font-weight: bold;
        }

        .head .img {
            display: inline-block;
            margin: 20px;
            float: right;
            width: 34px;
            height: 34px;
            background-image: url(/static/citybrain/csdn/img/cstz2-middle/close-hover.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
        }

        .content {
            width: 100%;
            height: calc(100% - 100px);
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            padding: 70px 20px 0 20px;
            box-sizing: border-box;
            position: relative;
        }

        .province {
            /* display: flex;
    flex-direction: column; */
            /* justify-content: start; */
            padding: 0 5px 0 5px;
            box-sizing: border-box;
        }

        .provincetable {
            width: 100%;
            height: 100%;
            text-align: center;
            margin-top: 20px;
        }

        .thead {
            display: flex;
            align-items: center;
            height: 70px;
            font-size: 30px;
            font-weight: 600;
            background-color: #003473;
            color: #417ab7;
        }

        .tbody {
            height: 565px;
            overflow-y: scroll;
        }

        .tbody::-webkit-scrollbar {
            display: none;
        }

        .tbody .line {
            display: flex;
            align-items: center;
            height: 55px;
            margin-top: 7px;
            background-color: #0a2850;
            font-size: 24px;
            font-weight: 600;
            color: #91a2b7;
        }

        .tbody .line div {
            position: relative;
        }

        .updown0 {
            background: linear-gradient(to bottom, #ffffff, tomato);
            -webkit-background-clip: text;
            color: transparent;
        }

        .updown0::after {
            content: "↑";
            position: absolute;
            top: 0px;
            right: 50px;
            font-size: 25px;
            background: linear-gradient(to bottom, #ffffff, tomato);
            -webkit-background-clip: text;
            color: transparent;
        }

        .updown1 {
            background: linear-gradient(to bottom, #ffffff, green);
            -webkit-background-clip: text;
            color: transparent;
        }

        .updown1::after {
            content: "↓";
            position: absolute;
            top: 0px;
            right: 50px;
            font-size: 25px;
            background: linear-gradient(to bottom, #ffffff, green);
            -webkit-background-clip: text;
            color: transparent;
        }

        .select {
            position: absolute;
            width: 150px;
            right: 85px;
            top: 40px;
        }

        .el-input {
            width: 150px;
        }

        .el-scrollbar {
            width: 150px !important;
        }

        .el-select-dropdown__list {
            background-color: #0a2850;
        }

        .el-input__inner {
            width: 150px !important;
        }
    </style>

    <body>
        <div id="app" class="container">
            <div class="head">
                <span>市场主体年度统计</span>
                <div class="img" @click="closeDialog"></div>
            </div>
            <div class="content">
                <div class="select" style="display: flex">
                    <el-select v-model="value" placeholder="请选择" @change="changeData()">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
                <div class="province">
                    <nav style="padding: 20px 0 0px 0; position: relative">
                        <s-header-title title="省内" htype="2"></s-header-title>
                    </nav>
                    <div class="provincetable">
                        <div class="thead">
                            <div style="flex: 0.7">地区</div>
                            <div style="flex: 1.5">市场主体总量</div>
                            <div style="flex: 1.5">放心消费</div>
                            <div style="flex: 1.5">营商环境全省排名</div>
                        </div>
                        <div class="tbody">
                            <div class="line" v-for="item in data1">
                                <div style="flex: 0.7">{{item.region}}</div>
                                <div style="flex: 1.5" :class="[item.updown==0?updown0:updown1]">{{item.total}}</div>
                                <div style="flex: 1.5" :class="[item.updown==0?updown0:updown1]">{{item.consume}}</div>
                                <div style="flex: 1.5" :class="[item.updown==0?updown0:updown1]">{{item.yyhjRank}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="city">
                    <nav style="padding: 20px 0 0px 0; position: relative">
                        <s-header-title title="市内" htype="2"></s-header-title>
                    </nav>
                    <div class="provincetable">
                        <div class="thead">
                            <div style="flex: 0.7">地区</div>
                            <div style="flex: 1.5">市场主体总量</div>
                            <div style="flex: 1.5">放心消费</div>
                            <div style="flex: 1.5">营商环境全省排名</div>
                        </div>
                        <div class="tbody">
                            <div class="line" v-for="item in data1">
                                <div style="flex: 0.7">{{item.region}}</div>
                                <div style="flex: 1.5" :class="[item.updown==0?updown0:updown1]">{{item.total}}</div>
                                <div style="flex: 1.5" :class="[item.updown==0?updown0:updown1]">{{item.consume}}</div>
                                <div style="flex: 1.5" :class="[item.updown==0?updown0:updown1]">{{item.yyhjRank}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    <script type="module">
        new Vue({
            el: "#app",
            data: {
                updown0: "updown0",
                updown1: "updown1",
                data1: [],
                data2: [],
                value: "",
                options: [
                    { value: 2021, label: 2021 },
                    { value: 2020, label: 2020 },
                    { value: 2019, label: 2019 },
                    { value: 2018, label: 2018 },
                    { value: 2017, label: 2017 },
                ],
                data12: [],
                data13: [],
                data14: [],
                data15: [],
                data16: [],
            },
            methods: {
                closeDialog() {
                    top.commonObj.funCloseIframe({
                        name: "sczttj_dialog",
                    });
                },
                changeData() {
                    if (this.value == 2021) {
                        this.data1 = this.data12;
                        this.data2 = this.data12;
                    } else if (this.value == 2020) {
                        this.data1 = this.data13;
                        this.data2 = this.data13;
                    } else if (this.value == 2019) {
                        this.data1 = this.data14;
                        this.data2 = this.data14;
                    } else if (this.value == 2018) {
                        this.data1 = this.data15;
                        this.data2 = this.data15;
                    } else if (this.value == 2017) {
                        this.data1 = this.data16;
                        this.data2 = this.data16;
                    }
                },
            },
            //项目生命周期
            mounted() {
                $api("scjg_dialog_sczttj_1").then((res) => {
                    this.data1 = res;
                });
                $api("scjg_dialog_sczttj_2").then((res) => {
                    this.data12 = res;
                });
                $api("scjg_dialog_sczttj_3").then((res) => {
                    this.data13 = res;
                });
                $api("scjg_dialog_sczttj_4").then((res) => {
                    this.data14 = res;
                });
                $api("scjg_dialog_sczttj_5").then((res) => {
                    this.data15 = res;
                });
                $api("scjg_dialog_sczttj_6").then((res) => {
                    this.data16 = res;
                });
            },
        });
    </script>
</html>
