/* 和谐劳动关系样式 */
.container{
    width: 100%;
    height: 1715px;
}
.top-con{
    width: 100%;
    height: 13%;
}

.mfhx-con{
    width: 100%;
    height: 70%;
    display: flex;
    flex-wrap: wrap;
    padding-left: 65px;
    box-sizing: border-box;
  }
  .mfhx-con li{
      list-style: none;
      width: 18%;
      height: 60px;
      margin-right: 25px;
      display: flex;
  }
  .mfhx-con .img-right{
    margin-left: 20px;
  }
  .img-right .name{
      font-size: 24px;
      /* background: linear-gradient(to top, #83b8ff, #74b4f4, #f0ffff);
      -webkit-background-clip: text; */
      color: #fff;
      font-weight:bold;
  }
  .img-right .value{
      font-size: 28px;
      background: linear-gradient(to top, #83b8ff, #74b4f4, #f0ffff);
      -webkit-background-clip: text;
      color: transparent;
      font-weight: 800;
      text-align: center;
  }
  .img-right .value span{
      font-size: 20px;
      font-weight: 500;
      color: #74b4f4;
      margin-left: 10px;
  }
  
.left-part,.right-part{
    width: 50%;
    height: 100%;
}

.second-con{
    width: 100%;
    height: 30%;
    display: flex;
}

.ldjf-tj li{
    list-style: none;
    width: 27%;
  }
.ldjf-tj{
      width: 100%;
      display: flex;
      padding: 0 40px;
      box-sizing: border-box;
      justify-content: space-around;
  }
.ldjf-tj .title{
    font-size: 30px;
    color: #fff;
    display: inline-block;
    width: 100%;
    text-align: center;
    margin-bottom: 15px;
  }
  .num-bg{
    width: 245px;
    height: 57px;
    background-image: url(/static/citybrain/ggfw/img/title-bg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    /* margin-left: 20px; */
    text-align: center;
    margin-top: -4px;
  }
  .num-bg .num{
    font-size: 32px;
    background: linear-gradient(to top, #83b8ff, #74b4f4, #f0ffff);
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 800;
  }
  .num-bg .unit{
    font-size: 20px;
    background: linear-gradient(to top, #83b8ff, #74b4f4, #f0ffff);
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 500;
    margin-left: 10px;
  }
  #ldjf-chart{
    width: 100%;
    height: 65%;
  }

.gxhx-tj{
    width: 100%;
    height: 95px;
    display: flex;
    justify-content: space-between;
}
.gxhx-tj li{
   width: 18%;
   height: 100%;
   /* background-color: deepskyblue; */
   border: 1px solid #116297;
   list-style: none;
   border-radius: 10px;
   text-align: center;
   font-size: 28px;
   color: #fff;
   padding: 8px 0px;
   box-sizing: border-box;
   cursor: pointer;
}
.active{
    background-color: #2466a5;
}

.table1{
   width: 100%;
   height: 65%;
   margin-top: 20px;
}

.table1 .th {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-style: italic;
    font-weight: 700;
    font-size: 32px;
    line-height: 60px;
    background: #00396f;
    color: #FFFFFF;
  }

  .table1 .th_td {
    letter-spacing: 0px;
    text-align: center;
  }

  .table1 .tbody {
    width: 100%;
    height: calc(100% - 90px);
    /* overflow-y: auto; */
    overflow: hidden;
  }

  .table1 .tbody:hover {
    overflow-y: auto;
  }

  .table1 .tbody::-webkit-scrollbar {
    width: 4px;
    /*滚动条整体样式*/
    height: 4px;
    /*高宽分别对应横竖滚动条的尺寸*/
  }

  .table1 .tbody::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #20aeff;
    height: 8px;
  }

  .table1 .tr {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;
    line-height: 60px;
    font-size: 28px;
    color: #FFFFFF;
    cursor: pointer;
  }

  .table1 .tr:nth-child(2n) {
    background: rgb(0 57 111 / 30%);
  }

  .table1 .tr:nth-child(2n+1) {
    /* background: #035b86; */
  }

  .table1 .tr:hover {
    background-color: #6990b6;
  }

  .table1 .tr_td {
    letter-spacing: 0px;
    text-align: center;
    box-sizing: border-box;
  }
.third-con,.bottom-con{
   width: 100%;
   height: 29%;
   display: flex;
}

.select{
  width: 100%;
  height: 60px;
  display: flex;
  /* justify-content: space-around; */
  padding: 5px 0px;
  box-sizing: border-box;
  position: relative;
  z-index: 99;
}
#ldzc-chart,#ajlx-chart,#ldwq-chart{
  width: 100%;
  height:70%;
 
}
.jyzjzc-tab{
  width: 25%;
  height: 50px;
  display:flex;
  justify-content: space-between;
  color: #fff;
  font-size: 28px;
  list-style: none;
  padding: 3px 50px;
  box-sizing: border-box;
  position: absolute;
  top: 60px;
}
.djsy-item{
  cursor: pointer;
  text-align: center;
}
.active1{
  border-bottom: 2px solid #81bcf5;
  background: linear-gradient(to top, #83b8ff, #74b4f4, #f0ffff);
  -webkit-background-clip: text;
  color: transparent;
}
.spa-label{
  font-size: 28px;
  color: #fff;
  line-height: 47px;
  margin-right: 20px;
}




/* 下拉框 */
.el-select {
  width: 150px;
  margin-right:170px;
}
.el-input__inner {
  height: 50px !important;
  width: 300px !important;
  background-color: #00487f;
  color: #fff;
  font-size: 28px;
}
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: #00487f;
}
.el-select-dropdown__item {
  color: #fff;
  background-color: #00487f;
  font-size: 28px;
}
.el-select-dropdown__list {
  background-color: #00487f;
}
.el-select .el-input .el-select__caret {
  position: relative;
  left: 140px;
  font-size: 28px;
  color: #fff;
}
.box2-content {
  display: unset;
}
.el-select .el-input__inner{
  /* border-radius: 30px !important; */
}
.el-scrollbar{
  width: 300px;
}
.el-input.is-disabled .el-input__inner{
  background-color: #2d4a67;
}
