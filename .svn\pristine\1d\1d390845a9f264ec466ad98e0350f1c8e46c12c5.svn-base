window.grid = function () {
    fetch("./data/grid.geojson").then(res => {
        return res.json()
    }).then(data => {
        let result = [];
        let features = data.features;

        for (let i = 0; i < features.length; i++) {
            let feature = features[i];
            let coords = feature.geometry.coordinates;
            let locs = _dealGeom(feature.geometry.type, coords);
            result.push({ data: locs, prop: feature.properties });
        }
        createModel(result);
    })
    // $.ajax({
    //     url: "./data/grid.geojson",
    //     type: 'get',
    //     dataType: 'json',
    //     async: false,
    //     success: function (data) {
    //         let result = [];
    //         let features = data.features;

    //         for (let i = 0; i < features.length; i++) {
    //             let feature = features[i];
    //             let coords = feature.geometry.coordinates;
    //             let locs = _dealGeom(feature.geometry.type, coords);
    //             result.push({data:locs,prop:feature.properties});
    //         }
    //         createModel(result);
    //     }
    // });

}

let gridObj = null;
let _cache = {};
let colors = [];
window.updateColor = function (obj) {

    //
    for (let i in obj) {
        if (_cache[i]) {
            let start = _cache[i].startIndex;
            let _len = _cache[i]._len;
            for (let x = 0; x < _len/4; x++) {
                let step=x*4;
                colors[start+step+0]=obj[i][0];
                colors[start+step+1]=obj[i][1];
                colors[start+step+2]=obj[i][2];
                colors[start+step+3]=obj[i][3];
            }
        }
    }

    console.log(colors.length);
    gridObj.updateVexterColor(colors);
}


function createModel(result) {

    //进行数据调用
    let poss = [];
    let indexs = [];
    for (let i = 0; i < result.length; i++) {

        let geoms = result[i].data;
        let prop = result[i].prop;
        let key = prop._column + "_" + prop._row;
        let len = geoms.length;
        let color = [7, 33, 79, 0];//同一个面的顶点都用同一个颜色
        let start = poss.length / 3;

        for (let x = 0; x < len; x++) {
            let child = geoms[x];
            let startIndex = poss.length / 3;
            let geometry = createTrix(child);
            //不是三角网
            let indexArray = geometry.index.array;
            for (let i = 0; i < indexArray.length; i += 3) {
                indexs.push(startIndex + indexArray[i]);
                indexs.push(startIndex + indexArray[i + 1]);
                indexs.push(startIndex + indexArray[i + 2]);
            }
            let positionArray = geometry.attributes.position.array;
            for (let i = 0; i < positionArray.length; i += 3) {
                poss.push(positionArray[i]);
                poss.push(positionArray[i + 1]);
                poss.push(positionArray[i + 2]);
                colors.push(...color);
            }
        }
        let end = poss.length / 3;
        _cache[key] = {
            startIndex: start * 4,
            _len: (end - start) * 4,
            color: color
        };

    }
    console.log(colors.length);
    // console.log(_cache);
    // console.log(colors);

    Vex.resourceManager.loadMaterial("./data/col.mats").then(objs => {
        let mat = objs.mats[0];
        let obj = Vex.geometryManager.createGeometry("123456", poss, indexs, colors, mat, 0);
        let loc = obj.getLocalPostion(() => { });
        loc.postion.y = 5;
        obj.updatePostion(loc)
        obj.setVisible(true);
        console.log(obj)
        gridObj = obj;
    });
}

function _dealGeom(type, geoms) {
    let ret = [];
    switch (type) {
        case 'MultiPolygon':
            for (let i = 0; i < geoms.length; i++) {

                let cods = geoms[i];
                let item = [];
                for (let c = 0; c < cods.length; ++c) {
                    let cod = cods[c];
                    for (let x = 0; x < cod.length; x++) {
                        let _cod = cod[x];
                        item.push(_cod[0], _cod[1]);
                    }
                }
                ret.push(item);
            }
            break;
        case 'Polygon':
            let item = [];
            geoms = geoms[0];
            for (let c = 0; c < geoms.length; ++c) {
                let cod = geoms[c];
                item.push(cod[0], cod[1]);
            }
            ret.push(item);
            break;
        default:
            break;
    }
    return ret;

};
createTrix = function (child) {

    let shape = new Vex.lib.Shape();
    shape.moveTo(child[0], child[1]);

    for (let j = 2; j < child.length; j += 2) {
        let x = child[j];
        let y = child[j + 1];
        shape.lineTo(x, y);
    }
    let geometry = new Vex.lib.ShapeBufferGeometry(shape);
    let indexArray = geometry.index.array;
    for (let i = 0; i < indexArray.length; i += 3) {
        let temp = indexArray[i + 2];
        indexArray[i + 2] = indexArray[i + 1];
        indexArray[i + 1] = temp;
    }
    let positionArray = geometry.attributes.position.array;
    positionArray = Vex.scene.camera().pickLonLatToScenePos(positionArray, () => { })
    geometry.attributes.position.array = positionArray;
    return geometry;
}
