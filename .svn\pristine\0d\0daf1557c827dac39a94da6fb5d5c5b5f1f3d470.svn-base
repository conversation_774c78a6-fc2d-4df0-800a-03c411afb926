import {
  BaseLayer
} from "./BaseLayer.js";
import {
  WebMercatorProjection,
  Rectangle,
  Cartographic,
  Cartesian3,
  Material,
  Primitive,
  PrimitiveCollection,
  RectangleGeometry,
  Ellipsoid,
  GeometryInstance,
  MaterialAppearance,
  ColorGeometryInstanceAttribute
} from "../../../lib/Cesium/Source/Cesium.js";
import heatmapFactory from "../../Util/heatmaps.js";
/**
 * 创建热力图
 * @alias HeatMap
 * @extends BaseLayer
 * @constructor
 * @param {Object} bound 热力图的坐标信息和id，[{coord: [lon, lat, height], id: ''}]
 * @param {Object} option 点位的样式参数
 * 
 * @example
 *  const map = EMap.createMap({
            id: 'mapContainer'
        })

        let bounds = {
            west: 104.543911,
            east: 104.570811,
            south: 30.644545,
            north: 30.689995,
        };
        let heatmap = new EMap.HeatMap(bounds)
        // 添加到地图上 
        map.add(heatmap)
        heatmap.createHeat()
        let data = [
            { x: 104.554153, y: 30.6883939, value: 76 },
            { x: 104.5654284, y: 30.6854935, value: 63 },
            { x: 104.565005, y: 30.684573, value: 1 },
        ];
        let valueMin = 0;
        let valueMax = 50;
        heatmap.setWGS84Data(valueMin, valueMax, data)
 * 
 */
export class HeatMap extends BaseLayer {
  constructor(data, option, id) {
    super();

    if (id) {
      this._uuid = id
    }
    this.collection = new PrimitiveCollection();
    this.collection._uuid = this._uuid
    this.option = Object.assign({}, this.defaultOption, option)
    this.addPrimitive(data, option)
  }

  // 默认配置
  defaultOption = {
    
    minCanvasSize: 700, // 热图画布的最小尺寸(像素)
    maxCanvasSize: 2000, // 热图画布的最大尺寸(像素)
    radius: 100, // 如果没有给出半径，则使用的数据点大小因子(较高的高度和宽度除以这个数字将产生使用的半径)
    spacingFactor: 1.5, // 边界周围的额外空间(点半径乘以这个数字产生间距)
    maxOpacity: 1, // 如果没有在热图选项对象中给出，则使用的最大不透明度
    minOpacity: 0.1, // 如果没有在热图选项对象中给出，则使用的最小不透明度
    blur: 0.85, // 如果没有在热图选项对象中给出，则使用模糊
    gradient: {
      // 如果没有在热图选项对象中给出渐变，则使用渐变
      ".3": "blue",
      ".65": "yellow",
      ".8": "orange",
      ".95": "red",
    },
  };


  /**
   * 创建热力图
   */
  _createHeatmap(bounds) {

    // 生成随机id
    this._id = this._getID();

    this._WMP = new WebMercatorProjection();

    // 范围转换坐标
    let _mbound = this._wgs84ToMercatorBound(bounds);
    // 计算宽高
    this._setWidthAndHeight(_mbound);

    // 计算设置的边缘空间
    this._spacing =
      this.option.radius * this.option.spacingFactor;
    // 保存偏移的位置（西、南）
    this._xoffset = _mbound.west;
    this._yoffset = _mbound.south;

    // 宽高添加向外延伸空间
    this.width = Math.round(this.width + this._spacing * 2);
    this.height = Math.round(this.height + this._spacing * 2);

    // 范围向外延伸
    _mbound.west -= this._spacing * this._factor;
    _mbound.east += this._spacing * this._factor;
    _mbound.south -= this._spacing * this._factor;
    _mbound.north += this._spacing * this._factor;



    // 创建热力图dom容器
    this._container = this._getContainer(this._id, this.width, this.height);

    // option添加container属性
    this.option.container = this._container;

    // 创建热力图
    this._heatmap = heatmapFactory.create(this.option);

    this._container.children[0].setAttribute("id", this._id + "-hm");
    return this._mercatorToWgs84Bound(_mbound)
  }

  _mercatorToWgs84Bound(bounds) {
    let sw = this._WMP.unproject(new Cartesian3(bounds.west, bounds.south));
    let ne = this._WMP.unproject(new Cartesian3(bounds.east, bounds.north));

    return [
      Cesium.Math.toDegrees(sw.longitude),
      Cesium.Math.toDegrees(sw.latitude),
      Cesium.Math.toDegrees(ne.longitude),
      Cesium.Math.toDegrees(ne.latitude)
    ]
  }
  _getID(len) {
    let text = "";
    let possible =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    for (let i = 0; i < (len ? len : 8); i++)
      text += possible.charAt(Math.floor(Math.random() * possible.length));

    return text;
  }

  _wgs84ToMercatorBound(bound) {
    let sw = this._WMP.project(
      Cartographic.fromDegrees(bound[0], bound[1])
    );
    let ne = this._WMP.project(
      Cartographic.fromDegrees(bound[2], bound[3])
    );
    return {
      north: ne.y,
      east: ne.x,
      south: sw.y,
      west: sw.x,
    };
  }

  _getContainer(id, width, height) {
    let div = document.createElement("div");
    if (id) {
      div.setAttribute("id", id);
    }
    div.setAttribute(
      "style",
      "width: " +
      width +
      "px; height: " +
      height +
      "px; margin: 0px; display: none;"
    );
    document.body.appendChild(div);
    return div;
  }

  // 84转墨卡托工具
  _wgs84ToMercator(p) {
    let mp = this._WMP.project(Cartographic.fromDegrees(p.x, p.y));
    return {
      x: mp.x,
      y: mp.y,
    };
  }

  // 墨卡托转84
  _mercatorToWgs84(p) {
    let wp = this._WMP.unproject(new Cartesian3(p.x, p.y));
    return {
      x: wp.longitude,
      y: wp.latitude,
    };
  }

  _rad2deg(d) {
    let r = d * (Math.PI / 180.0);
    return r;
  }

  // 计算宽高
  _setWidthAndHeight(mbound) {
    this.width =
      mbound.east > 0 && mbound.west < 0 ?
      mbound.east + Math.abs(mbound.west) :
      Math.abs(mbound.east - mbound.west);
    this.height =
      mbound.north > 0 && mbound.south < 0 ?
      mbound.north + Math.abs(mbound.south) :
      Math.abs(mbound.north - mbound.south);
    this._factor = 1;

    if (
      this.width > this.height &&
      this.width > this.option.maxCanvasSize
    ) {
      this._factor = this.width / this.option.maxCanvasSize;

      if (this.height / this._factor < this.option.minCanvasSize) {
        this._factor = this.height / this.option.minCanvasSize;
      }
    } else if (
      this.height > this.width &&
      this.height > this.option.maxCanvasSize
    ) {
      this._factor = this.height / this.option.maxCanvasSize;

      if (this.width / this._factor < this.option.minCanvasSize) {
        this._factor = this.width / this.option.minCanvasSize;
      }
    } else if (
      this.width < this.height &&
      this.width < this.option.minCanvasSize
    ) {
      this._factor = this.width / this.option.minCanvasSize;

      if (this.height / this._factor > this.option.maxCanvasSize) {
        this._factor = this.height / this.option.maxCanvasSize;
      }
    } else if (
      this.height < this.width &&
      this.height < this.option.minCanvasSize
    ) {
      this._factor = this.height / this.option.minCanvasSize;

      if (this.width / this._factor > this.option.maxCanvasSize) {
        this._factor = this.width / this.option.maxCanvasSize;
      }
    }

    this.width = this.width / this._factor;
    this.height = this.height / this._factor;
  }

  addPrimitive(data, {
    minValue,
    maxValue
  }) {
    
    let bounds = this._getBounds(data)

    let newBounds = this._createHeatmap(bounds)

    let convdata = [];
    if (data && data.length && minValue != null && maxValue != null) {
      for (let i = 0; i < data.length; i++) {
        let point = data[i];

        let hotMapPoint = this._wgs84PointToHeatmapPoint(point);
        if (point.value || point.value === 0) {
          hotMapPoint.value = point.value;
        }
        convdata.push(hotMapPoint);
      }
    }

    this._heatmap.setData({
      min: minValue,
      max: maxValue,
      data: convdata,
    });

    const rectangle = Rectangle.fromDegrees(
      ...newBounds
    )
    
    const primitive = new Primitive({
      geometryInstances: new GeometryInstance({
        geometry: new RectangleGeometry({
          ellipsoid: Ellipsoid.WGS84,
          rectangle,
        }),
        id: 'rectangle',
        attributes: {
          color: new ColorGeometryInstanceAttribute(0.0, 1.0, 1.0, 0.5)
        }
      }),
      appearance: new MaterialAppearance({
        material: new Material({
          fabric: {
            type: "Image",
            uniforms: {
              image: this._heatmap._renderer.canvas,
            },
          },
        }),
      }),
      asynchronous: false,
    });
    this.collection.add(primitive);

  }
  // 计算范围
  _getBounds(data) {

    let minX = 999,
      minY = 999,
      maxX = 0,
      maxY = 0;
    data.forEach(item => {
      if (item.x < minX) {
        minX = item.x
      }
      if (item.y < minY) {
        minY = item.y
      }
      if (item.x > maxX) {
        maxX = item.x
      }
      if (item.y > maxY) {
        maxY = item.y
      }
    })
    return [minX, minY, maxX, maxY]
  }

  _wgs84PointToHeatmapPoint(p) {
    // 经纬度转墨卡托
    const mPoint = this._wgs84ToMercator(p);

    let pn = {};

    pn.x = Math.round(
      (mPoint.x - this._xoffset) / this._factor + this._spacing
    );
    pn.y = Math.round(
      (mPoint.y - this._yoffset) / this._factor + this._spacing
    );
    pn.y = this.height - pn.y;

    return pn;
  }
  destroy() {
    this._container = this._container.remove();
    delete this.map.layers[this._uuid];
    this.map = null;


  }
}