<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>事件中心-右</title>
    <script src="/static/citybrain/hjbh/js/vue.js"></script>
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
      }
      #rightApp {
        width: 2045px;
        height: 1890px;
        background: url("/img/right-bg.png") no-repeat;
        background-size: 100%;
      }
      @font-face {
        font-family: LED;
        src: url("/static/css/fonts/Digital-Regular.ttf");
        font-weight: normal;
        font-style: normal;
      }
      .cznx-left {
        width: 40%;
        height: 100%;
        padding: 0 30px;
        box-sizing: border-box;
      }
      .cznx-right {
        width: 60%;
        height: 100%;
        padding: 0 30px;
        box-sizing: border-box;
      }
      .title_icon {
        width: 10px;
        height: 30px;
        background-color: #01faf8;
        border-radius: 4px;
        margin-right: 15px;
      }
      .title_name {
        color: #fff;
        font-size: 38px;
      }
      .part {
        margin-top: 30px;
      }
      .cznx-left-ascl {
        width: 100%;
        height: 182px;
        justify-content: space-evenly;
      }
      .ascl-item {
        width: 349px;
        height: 182px;
        background: url("/static/citybrain/djtl/img/sjzx/r-001.png");
        background-size: 100% 100%;
        padding: 30px;
        box-sizing: border-box;
      }
      .ascl-item .icon {
        width: 11px;
        height: 28px;
        background-color: #01faf8;
        border-radius: 4px;
        margin-right: 15px;
      }
      .ascl-item .name {
        font-size: 36px;
      }
      .ascl-item .value {
        font-size: 60px;
        font-family: LED;
        color: #0ef5fd;
        text-align: center;
        line-height: 80px;
      }
      .cznx-left-sjlxfx {
        width: 100%;
        padding: 0 50px;
        box-sizing: border-box;
      }
      .sjlxfx-con {
        width: 100%;
        height: 400px;
      }
      .cznx-left-sjfb {
        width: 100%;
        padding: 0 50px;
        box-sizing: border-box;
      }
      .sjfb-con {
        width: 100%;
        height: 350px;
      }
      .hssc-con {
        width: 100%;
        height: 135px;
        padding: 10px 50px 10px 0;
        box-sizing: border-box;
      }
      .hssc-con > ul {
        height: 100%;
        overflow-y: scroll;
        overflow-x: hidden;
      }
      /* 设置滚动条的样式 */
      .hssc-ul::-webkit-scrollbar {
        width: 10px;
      }
      /* 滚动槽 */
      .hssc-ul::-webkit-scrollbar-track {
        border-radius: 5px;
      }
      /* 滚动条滑块 */
      .hssc-ul::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: rgba(35, 144, 207, 0.5);
      }
      .hssc-ul::-webkit-scrollbar-thumb:window-inactive {
        background: rgba(27, 146, 215, 0.8);
      }
      .hssc-con > ul > li {
        font-size: 30px;
        color: #0be2fb;
        justify-content: space-between;
        line-height: 60px;
      }
      .hssc-point {
        width: 24px;
        height: 24px;
        background-color: #0be2fb;
        border-radius: 50%;
      }
      .sjlxyx-con {
        width: 100%;
        height: 400px;
      }
      .czbm-con {
        width: 100%;
        height: 350px;
        padding: 0 30px;
        box-sizing: border-box;
      }
      .czbm-top {
        width: 100%;
        justify-content: space-evenly;
        border-bottom: 1px #356596 solid;
        height: 60px;
        line-height: 60px;
      }
      .czbm-top-item {
        padding: 0 50px;
      }
      .czbm-top-item-active {
        color: #f9c802 !important;
        font-weight: 700;
        border-bottom: 2px white solid;
      }
      .czbm-list {
        width: 976px;
        height: 75%;
        margin: 0 auto;
        display: grid;
        margin-top: 20px;
      }
      .czbm-items {
        width: 976px;
        height: 46px;
        background: url("/static/citybrain/djtl/img/sjzx/r010.png");
        background-size: 100% 100%;
        position: relative;
      }
      .czbm-items-0 {
        background: url("/static/citybrain/djtl/img/sjzx/r010.png");
        background-size: 100% 100%;
      }
      .czbm-items-1 {
        background: url("/static/citybrain/djtl/img/sjzx/r011.png");
        background-size: 100% 100%;
      }
      .czbm-items-2 {
        background: url("/static/citybrain/djtl/img/sjzx/r012.png");
        background-size: 100% 100%;
      }
      .czbm-items-3 {
        background: url("/static/citybrain/djtl/img/sjzx/r013.png");
        background-size: 100% 100%;
      }
      .czbm-items-4 {
        background: url("/static/citybrain/djtl/img/sjzx/r014.png");
        background-size: 100% 100%;
      }
      .czbm-items span:nth-child(1) {
        font-size: 32px;
        font-style: italic;
        color: #ffffff;
        line-height: 46px;
        margin-left: 25px;
      }
      .czbm-items span:nth-child(2) {
        font-size: 30px;
        color: #fefefe;
        line-height: 46px;
        margin-left: 50px;
      }
      .czbm-items span:nth-child(3) {
        font-size: 30px;
        color: #fefefe;
        line-height: 46px;
        position: absolute;
        right: 200px;
      }
      .yjsjc-left {
        width: 40%;
        height: 100%;
        padding: 0px 45px;
        box-sizing: border-box;
        justify-content: space-between;
      }
      .yjsjc-left > img {
        cursor: pointer;
        opacity: 0.7;
      }
      .yjsjc-left > img:active {
        opacity: 1;
      }
      .left-con {
        width: 100%;
        justify-content: space-evenly;
      }
      .left-con-item {
        width: 98px;
        height: 356px;
        background: url("/static/citybrain/djtl/img/sjzx/r-back.png");
        background-size: 100% 100%;
        writing-mode: tb;
        line-height: 98px;
        text-align: center;
      }
      .yjsjc-right {
        width: 60%;
        height: 100%;
      }

      .el-carousel__indicators {
        display: none;
      }
      .el-carousel__container {
        height: 356px;
      }
      .el-carousel__item {
        display: flex;
        padding: 0 80px;
        justify-content: space-evenly;
        box-sizing: border-box;
      }
      .el-carousel__arrow--left {
        width: 69px;
        height: 74px;
        background: url("/static/citybrain/djtl/img/sjzx/r-left.png");
        background-size: 100% 100%;
        left: 0;
        opacity: 0.5;
      }
      .el-carousel__arrow--left:hover {
        opacity: 1;
      }
      .el-carousel__arrow--left:active {
        opacity: 1;
      }
      .el-carousel__arrow--right {
        width: 69px;
        height: 74px;
        background: url("/static/citybrain/djtl/img/sjzx/r-right.png");
        background-size: 100% 100%;
        right: 0;
        opacity: 0.5;
      }
      .el-carousel__arrow--right:hover {
        opacity: 1;
      }
      .el-carousel__arrow--right:active {
        opacity: 1;
      }
      .el-carousel__arrow i {
        display: none;
      }
      /* 表格自动滚动 */
      @keyframes rowUp {
        0% {
          -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
        }

        100% {
          transform: translate3d(0, -200%, 0);
          -webkit-transform: translate3d(0, -200%, 0);
        }
      }
      .hssc-li {
        animation: 10s rowUp linear infinite normal;
        -webkit-animation: 10s rowUp linear infinite normal;
      }
    </style>
  </head>
  <body>
    <div id="rightApp">
      <!-- 处置效能 -->
      <nav style="padding: 20px 40px">
        <s-header-title
          title="处置效能"
          :data-time="timeStr"
        ></s-header-title>
      </nav>
      <div class="s-flex" style="height: 1080px">
        <!-- 左侧 -->
        <div class="cznx-left">
          <!-- 按时处理 -->
          <div class="s-flex cznx-left-ascl part">
            <div class="ascl-item">
              <div class="s-flex">
                <div class="icon breath-light"></div>
                <div class="s-c-white name">按时处理事件数</div>
              </div>
              <p class="value">
                <count-to :start-val="0" :end-val="clsjNum" :duration="3000"></count-to>
                <span class="s-c-white s-font-30" style="margin-left: 10px">件</span>
              </p>
            </div>
            <div class="ascl-item">
              <div class="s-flex">
                <div class="icon breath-light"></div>
                <div class="s-c-white name">按时处理率</div>
              </div>
              <p class="value">
                <count-to :start-val="0" :end-val="cllNum" :duration="3000"></count-to>%
              </p>
            </div>
          </div>
          <!-- 事件类型分析 -->
          <div class="cznx-left-sjlxfx part">
            <div class="s-flex">
              <div class="title_icon"></div>
              <div class="s-c-white title_name">超时事件类型分析</div>
            </div>
            <div class="sjlxfx-con">
              <div
                id="echarts001"
                style="width: 650px; height: 400px"
              ></div>
            </div>
          </div>
          <!-- 事件分布 -->
          <div class="cznx-left-sjfb part">
            <div class="s-flex">
              <div class="title_icon"></div>
              <div class="s-c-white title_name">超时事件分布</div>
            </div>
            <div class="sjfb-con">
              <div
                id="echarts002"
                style="width: 650px; height: 350px"
              ></div>
            </div>
          </div>
        </div>
        <!-- 右侧 -->
        <div class="cznx-right">
          <!-- 耗时最长/最短事件 -->
          <div class="cznx-left-hssc part">
            <div class="s-flex">
              <div class="title_icon"></div>
              <div class="s-c-white title_name">耗时最长/最短事件</div>
            </div>
            <div class="hssc-con">
              <!-- <dv-scroll-board :config="config" style="width:500px;height:220px" /> -->
              <ul class="hssc-ul">
                <li
                  class="s-flex hssc-li"
                  :style="'color:'+item.color"
                  v-for="(item,index) in hsscList"
                  :key="index"
                >
                  <div
                    class="hssc-point"
                    :style="'background-color:'+item.color"
                  ></div>
                  <span>{{item.a}}</span>
                  <span>{{item.b}}</span>
                  <span>{{item.c}}</span>
                  <span>{{item.d}}</span>
                  <span>{{item.e}}</span>
                </li>
              </ul>
            </div>
          </div>
          <!-- 超时事件来源分析 -->
          <div class="cznx-left-sjlyfx part">
            <div class="s-flex">
              <div class="title_icon"></div>
              <div class="s-c-white title_name">超时事件来源分析</div>
            </div>
            <div class="sjlxyx-con">
              <div
                id="echarts003"
                style="width: 1150px; height: 400px"
              ></div>
            </div>
          </div>
          <!-- 超时处置部门 -->
          <div class="cznx-left-czbm part">
            <div class="s-flex">
              <div class="title_icon"></div>
              <div class="s-c-white title_name">超时处置部门</div>
            </div>
            <div class="czbm-con">
              <div class="czbm-top s-flex">
                <div
                  v-for="(item,index) in tabList"
                  :key="index"
                  @click="changeTab(index)"
                  style="cursor: pointer"
                  class="s-c-white s-font-30 czbm-top-item"
                  :class="tabIndex===index?'czbm-top-item-active':''"
                >
                  {{item}}
                </div>
              </div>
              <div class="czbm-list">
                <div
                  v-for="(item,index) in czbmList"
                  :key="index"
                  class="czbm-items"
                  :class="'czbm-items-'+index"
                >
                  <span>{{'NO.'+(index+1)}}</span>
                  <span>{{item.name}}</span>
                  <span>{{item.value}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 一件事集成 -->
      <nav style="margin: 50px">
        <s-header-title
          title="一件事集成"
          :data-time="timeStr"
        ></s-header-title>
      </nav>
      <div class="s-flex" style="height: 410px">
        <!-- 左侧 -->
        <div class="yjsjc-left s-flex">
          <el-carousel
            arrow="always"
            :autoplay="false"
            style="width: 100%; overflow: hidden"
          >
            <el-carousel-item v-for="(item,i) in jsjcList" :key="i">
              <div
                v-for="(item,index) in jsjcList[i]"
                :key="index"
                class="left-con-item"
              >
                <span class="s-c-white s-font-30">{{item}}</span>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
        <!-- 右侧 -->
        <div class="yjsjc-right">
          <div id="echarts004" style="width: 1150px; height: 400px"></div>
        </div>
      </div>
    </div>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
      var vm = new Vue({
        el: "#rightApp",
        data: {
        timeStr:"2022年7月22日",
          hsscList: [
            {
              color: "#0BE2FB",
              a: "城市管理纠纷",
              b: "义乌市",
              c: "事件描述事件",
              d: "耗时1小时3分",
              e: "2022年8月12日",
            },
            {
              color: "#F99609",
              a: "城市管理纠纷",
              b: "义乌市",
              c: "事件描述事件",
              d: "耗时1小时4分",
              e: "2022年8月12日",
            },
            {
              color: "#0BE2FB",
              a: "城市管理纠纷",
              b: "义乌市",
              c: "事件描述事件",
              d: "耗时1小时5分",
              e: "2022年8月12日",
            },
            {
              color: "#F99609",
              a: "城市管理纠纷",
              b: "义乌市",
              c: "事件描述事件",
              d: "耗时1小时6分",
              e: "2022年8月12日",
            },
          ],
          tabList: ["超时率", "超时次数", "超24小时次数"],
          tabIndex: 0,
          czbmList: [],
          czbmList0: [
            {
              name: "磐安县社会治理中心",
              value: "8%",
            },
            {
              name: "婺城区行政执法局",
              value: "7%",
            },
            {
              name: "义乌市交通运输局",
              value: "6%",
            },
            {
              name: "东阳市交警大队",
              value: "5%",
            },
            {
              name: "武义县建设局",
              value: "4%",
            },
          ],
          czbmList1: [
            {
              name: "磐安县社会治理中心",
              value: "9%",
            },
            {
              name: "婺城区行政执法局",
              value: "7%",
            },
            {
              name: "义乌市交通运输局",
              value: "6%",
            },
            {
              name: "东阳市交警大队",
              value: "5%",
            },
            {
              name: "武义县建设局",
              value: "4%",
            },
          ],
          czbmList2: [
            {
              name: "磐安县社会治理中心",
              value: "10%",
            },
            {
              name: "婺城区行政执法局",
              value: "7%",
            },
            {
              name: "义乌市交通运输局",
              value: "6%",
            },
            {
              name: "东阳市交警大队",
              value: "5%",
            },
            {
              name: "武义县建设局",
              value: "4%",
            },
          ],
          jsjcList: [
            [
              "校园及周边安全防制",
              "殡葬管理",
              "农村房屋安全使用",
              "非法捕捞监管执法",
              "非法采伐林木监管",
            ],
            [
              "校园及周边安全防制",
              "殡葬管理",
              "农村房屋安全使用",
              "非法捕捞监管执法",
              "非法采伐林木监管",
            ],
          ],
          clsjNum:9945,
          cllNum:99.10,
          setInterval:null,
        },
        created() {
          this.openMiddleIframe();
        },
        mounted() {
          this.initFun()
          this.changeTab(0);
          this.getEcharts001("echarts001");
          this.getEcharts002("echarts002");
          this.getEcharts003("echarts003");
          this.getEcharts004("echarts004");
          //定时器
          this.setInterval = setInterval(() => {
            this.clsjNum=0
            this.cllNum=0
            setTimeout(()=>{
              this.clsjNum=9945
              this.cllNum=99.10
            },)
          }, 30000);
        },
        methods: {
          initFun(){
            let that=this            
            // 时间
            $api("/csdnsjrw_left43").then((res) => {
              that.timeStr = res[0].date;
            });
          },
          // 打开中间弹窗
          openMiddleIframe() {
            let iframe1 = {
              type: "openIframe",
              name: "sjzx_middle_list",
              src:
                baseURL.url +
                "/static/citybrain/djtl/commont/sjzx_middle_list.html",
              width: "510px",
              height: "660px",
              left: "2190px",
              top: "215px",
              zIndex: "555",
            };
            window.parent.postMessage(JSON.stringify(iframe1), "*");
            let iframe2 = {
              type: "openIframe",
              name: "sjzx_middle_tab",
              src:
                baseURL.url +
                "/static/citybrain/djtl/commont/sjzx_middle_tab.html",
              width: "3380px",
              height: "520px",
              left: "2130px",
              top: "1600px",
              zIndex: "555",
            };
            window.parent.postMessage(JSON.stringify(iframe2), "*");
            let iframe3 = {
              type: "openIframe",
              name: "IndexMapBtn",
              src:
                baseURL.url +
                "/static/citybrain/tckz/IndexMapBtn.html",
              width: "90px",
              height: "350px",
              left: "5450px",
              top: "740px",
              zIndex: "555",
            };
            window.parent.postMessage(JSON.stringify(iframe3), "*");
          },
          changeTab(index) {
            this.tabIndex = index;
            switch (this.tabIndex) {
              case 0:
                this.czbmList = this.czbmList0;
                break;
              case 1:
                this.czbmList = this.czbmList1;
                break;
              case 2:
                this.czbmList = this.czbmList2;
                break;
            }
          },
          // 事件类型分析-绘图
          getEcharts001(id) {
            let myChart = echarts.init(document.getElementById(id));
            let imgUrl = "/static/citybrain/djtl/img/djtl-left/echarts-bg.png";
            let colorList = [
              "#41C8B0",
              "#DB7763",
              "#2D70E9",
              "#08A561",
              "#A9DB52",
              "#B76FD8",
              "#FD852E",
              "#FF4949",
              "#FFC460",
              "#B76FD8",
              "#F99609",
            ];
            let data = [
              {
                value: 10,
                name: "卫生健康",
                label: {
                  color: "#fff",
                  fontSize: 23,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 10,
                name: "平安综治",
                label: {
                  color: "#fff",
                  fontSize: 23,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 10,
                name: "安全应急",
                label: {
                  color: "#fff",
                  fontSize: 23,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 10,
                name: "生态环境",
                label: {
                  color: "#fff",
                  fontSize: 23,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 4,
                name: "市政建设",
                label: {
                  color: "#fff",
                  fontSize: 23,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 4,
                name: "市容市貌",
                label: {
                  color: "#fff",
                  fontSize: 23,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 9,
                name: "园林绿化",
                label: {
                  color: "#fff",
                  fontSize: 23,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 8,
                name: "建筑规划",
                label: {
                  color: "#fff",
                  fontSize: 23,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 10,
                name: "农林水利",
                label: {
                  color: "#fff",
                  fontSize: 23,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 10,
                name: "社会服务",
                label: {
                  color: "#fff",
                  fontSize: 23,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
              {
                value: 10,
                name: "教育文旅",
                label: {
                  color: "#fff",
                  fontSize: 23,
                },
                itemStyle: {},
                emphasis: {
                  itemStyle: {},
                },
              },
            ];
            function angleText(i, num) {
              //每个元素的角度
              var everyAngle = 360 / num;
              //文字现在所在的角度
              var currentAngle = i * everyAngle + everyAngle / 2;
              //文字所在模块的所占角度
              var currentArea = (i + 1) * everyAngle;

              if (currentAngle <= 90) {
                return -currentAngle;
              } else if (currentAngle <= 180 && currentAngle > 90) {
                return 180 - currentAngle;
              } else if (currentAngle < 270 && currentAngle > 180) {
                return 180 - currentAngle;
              } else if (currentAngle < 360 && currentAngle >= 270) {
                return 360 - currentAngle;
              }
            }

            //有值的色图的正切处理
            let data3 = [];
            data3 = JSON.parse(JSON.stringify(data));
            for (var i = 0; i < data3.length; i++) {
              if (i === 0) {
                data3[i]["label"]["color"] = "#333";
                data3[i]["itemStyle"]["color"] = "rgba(25, 255, 224)";
                data3[i]["emphasis"]["itemStyle"]["color"] =
                  "rgba(25, 255, 224)";
                data3[i]["label"]["rotate"] = angleText(i, data3.length);
              } else {
                data3[i]["label"]["color"] = "#fff";
                // data3[i]["itemStyle"]["color"] = "#4169E1"; //颜色配置
                data3[i]["itemStyle"]["color"] = colorList[i]; //颜色配置
                data3[i]["emphasis"]["itemStyle"]["color"] = "#6A5ACD";
                data3[i]["label"]["rotate"] = angleText(i, data3.length);
              }
            }

            //最外层大圈的数据
            let data1 = [];

            data1 = JSON.parse(JSON.stringify(data));
            for (var i = 0; i < data1.length; i++) {
              data1[i].value = 1;
              data1[i]["label"]["rotate"] = angleText(i, data1.length);
              if (i === 0) {
                data1[i]["label"]["color"] = "rgba(25, 255, 224)";
              }
            }

            //透明饼图的数据
            let data2 = [];

            for (var i = 0; i < data.length; i++) {
              if (i === 0) {
                data2.push({
                  value: 1,
                  itemStyle: {
                    color: "rgba(25, 255, 224,0.05)",
                  },
                });
              } else {
                data2.push({
                  value: 1,
                  itemStyle: {
                    color: "transparent",
                  },
                });
              }
            }

            let option = {
              grid: {},
              polar: {},
              angleAxis: {
                show: false,
                interval: 1,
                type: "category",
                data: [],
              },
              graphic: [
                {
                  z: 4,
                  type: "image",
                  id: "logo",
                  left: "35.6%",
                  top: "26.6%",
                  z: -10,
                  bounding: "raw",
                  rotation: 0, //旋转
                  origin: [50, 50], //中心点
                  scale: [0.4, 0.4], //缩放
                  style: {
                    image: imgUrl,
                    opacity: 1,
                  },
                },
              ],
              //中间画圈圈的坐标轴
              radiusAxis: {
                show: false,
              },
              series: [
                {
                  type: "pie",
                  radius: ["68%", "100%"],
                  hoverAnimation: false,
                  itemStyle: {
                    color: "transparent",
                  },
                  labelLine: {
                    normal: {
                      show: false,
                      length: 30,
                      length2: 55,
                    },
                  },
                  label: {
                    normal: {
                      position: "inside",
                      align: "right",
                    },
                  },
                  name: "",
                  data: data1,
                },
                {
                  stack: "a",
                  type: "pie",
                  radius: ["20%", "75%"],
                  roseType: "area",
                  zlevel: 10,
                  itemStyle: {
                    // color: "#4169E1",
                    normal: {
                      borderColor: "rgb(46 64 93)",
                      borderWidth: 1,
                    },
                  },
                  emphasis: {
                    itemStyle: {
                      color: "#6A5ACD",
                    },
                  },
                  label: {
                    normal: {
                      show: true,
                      textStyle: {
                        fontSize: 28,
                        color: "#fff",
                      },
                      position: "inside",
                      rotate: 30,
                      align: "right",
                      fontWeight: "bold",
                      formatter: "{c}",
                    },
                    emphasis: {
                      show: true,
                    },
                  },
                  animation: false,
                  data: data3,
                },
                {
                  type: "pie",
                  zlevel: 99,
                  radius: ["15%", "90%"],
                  selectedOffset: 0,
                  animation: false,
                  hoverAnimation: false,
                  label: {
                    normal: {
                      show: false,
                    },
                  },
                  data: data2,
                },
              ],
            };

            myChart.setOption(option);
            myChart.getZr().on('mousemove', param => {
              myChart.getZr().setCursorStyle('default')
            })

            myChart.on("click", function (a) {
              //最外层的字体颜色重置变色
              for (var da1 = 0; da1 < option.series[0].data.length; da1++) {
                option.series[0].data[da1].label.color = "#fff";
              }

              //色圈的字体颜色和选中颜色重置
              for (var da2 = 0; da2 < option.series[1].data.length; da2++) {
                // option.series[1].data[da2].itemStyle.color = "#4169E1";
                option.series[1].data[da2].itemStyle.color = colorList[da2];
                option.series[1].data[da2].label.color = "#fff";
                //hover颜色重置
                option.series[1].data[da2].emphasis.itemStyle.color = "#6A5ACD";
              }

              //背景的透明饼图的重置
              for (var da3 = 0; da3 < option.series[2].data.length; da3++) {
                option.series[2].data[da3].itemStyle.color = "transparent";
              }

              option.series[1].data[a.dataIndex].itemStyle.color =
                "rgba(25, 255, 224)";
              option.series[1].data[a.dataIndex].label.color = "#333";
              //hover 颜色改变
              option.series[1].data[a.dataIndex].emphasis.itemStyle.color =
                "rgba(25, 255, 224)";
              option.series[0].data[a.dataIndex].label.color =
                "rgba(25, 255, 224)";
              option.series[2].data[a.dataIndex].itemStyle.color =
                "rgba(25, 255, 224,0.1)";
              //console.log(option)
              myChart.setOption(option);
            });

            myChart.on("mouseover", function (a) {
              myChart.dispatchAction({
                type: "highlight",
                seriesIndex: 1,
                dataIndex: a.dataIndex,
              });
            });

            myChart.on("mouseout", function (a) {
              myChart.dispatchAction({
                type: "downplay",
                seriesIndex: 1,
                dataIndex: a.dataIndex,
              });
            });
          },

          //事件分布-绘图
          getEcharts002(id) {
            let myChart = echarts.init(document.getElementById(id));
            let echartData = [
              { value: 30, name: "超时1小时以上" },
              { value: 20, name: "超时1-6小时" },
              { value: 18, name: "超时6-12小时" },
              { value: 12, name: "超时12-24小时" },
              { value: 12, name: "超时24-48小时" },
              { value: 8, name: "超时48小时以上" },
            ];
            const option = {
              tooltip: {
                trigger: "item",
                formatter: "{b}: <br/>占比：{d}%",
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "28",
                },
              },
              legend: {
                orient: "vertical",
                itemWidth: 10,
                itemHeight: 10,
                left: "50%",
                top: "15%",
                icon: "circle",
                itemGap: 16,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                  padding: [0, 0, 0, 20],
                },
                formatter: function (name) {
                  var data = option.series[0].data; //获取series中的data
                  var total = 0;
                  var tarValue;
                  for (var i = 0, l = data.length; i < l; i++) {
                    total += data[i].value;
                    if (data[i].name == name) {
                      tarValue = data[i].value;
                    }
                  }
                  var p = (tarValue / total) * 100;
                  // return name + '  '  +tarValue+'件'
                  return name + "  " + p + "%";
                },
              },
              series: [
                {
                  name: "",
                  type: "pie",
                  radius: ["0%", "60%"],
                  center: ["25%", "50%"],
                  itemStyle: {
                    normal: {
                      borderColor: "#0A1934",
                      borderWidth: 0,
                    },
                  },
                  label: {
                    show: false,
                  },
                  data: echartData,
                },
              ],
            };
            myChart.setOption(option);
            tools.loopShowTooltip(myChart, option, { loopSeries: true });
            myChart.getZr().on('mousemove', param => {
              myChart.getZr().setCursorStyle('default')
            })
          },

          //超时事件来源分析-绘图
          getEcharts003(id) {
            let myChart = echarts.init(document.getElementById(id));
            var colors = [
              "#80E6FF",
              "#97FBD2",
              "#FBEE83",
              "#D485F9",
              "#F27575",
              "#FFBA69",
            ];
            var url = "https://q.cnblogs.com/Images/qdigg.gif";
            option = {
              color: colors,
              tooltip: {
                trigger: "axis",
                axisPointer: {
                  type: "cross",
                  label: {
                    backgroundColor: "red",
                  },
                  lineStyle: {
                    color: "#fff",
                  },
                },
              },
              series: [
                {
                  top: "10%",
                  type: "funnel",
                  left: "0%",
                  width: "60%",
                  height: "90%",
                  gap: 16,
                  minSize: 150,
                  maxSize: 410,
                  label: {
                    normal: {
                      position: "inside",
                      formatter: "{c}",
                      fontSize: 28,
                    },
                    emphasis: {
                      formatter: "{b}: {c}",
                    },
                  },
                  itemStyle: {
                    borderWidth: 0,
                    shadowColor: "rgba(255, 255, 255, 0.5)",
                    shadowBlur: 10,
                  },
                  data: [
                    { value: 100, name: "大厅来访" },
                    { value: 70, name: "领导交办" },
                    { value: 60, name: "基层四平台" },
                    { value: 40, name: "线上来访" },
                    { value: 20, name: "线上预约" },
                    { value: 10, name: "下级上报" },
                  ],
                },
                {
                  top: "10%",
                  type: "funnel",
                  left: "0%",
                  width: "80%",
                  height: "90%",
                  gap: 16,
                  z: 1,
                  minSize: 150,
                  maxSize: 150,
                  label: {
                    normal: {
                      color: "#fff",
                      position: "right",
                      padding: [11, 25, 11, 25],
                      width: 50,
                      formatter: function (d) {
                        var ins =
                          "{aa|" + d.name + "   " + "}{bb|" + d.value + "%}";
                        return ins;
                      },
                      rich: {
                        aa: {
                          fontSize: 28,
                        },
                        bb: {
                          fontSize: 28,
                        },
                      },
                    },
                  },
                  //右侧的百分比显示的地方
                  labelLine: {
                    show: true,
                    normal: {
                      length: 200,
                      position: "right",
                      lineStyle: {
                        width: 1,
                        color: "#e8e9f1",
                        type: "solid",
                      },
                    },
                  },
                  //主体是透明的
                  itemStyle: {
                    normal: {
                      color: "transparent",
                      borderWidth: 0,
                      opacity: 1,
                    },
                  },
                  data: [
                    { value: 33.3, name: "大厅来访" },
                    { value: 23.3, name: "领导交办" },
                    { value: 20, name: "基层四平台" },
                    { value: 13.3, name: "线上来访" },
                    { value: 0.6, name: "线上预约" },
                    { value: 0.3, name: "下级上报" },
                  ],
                },
              ],
            };

            myChart.setOption(option);
            myChart.getZr().on('mousemove', param => {
              myChart.getZr().setCursorStyle('default')
            })
          },

          //一事件集成-绘图
          getEcharts004(id) {
            let myChart = echarts.init(document.getElementById(id));
            //数据
            var XName = [
              "金华市",
              "义乌市",
              "兰溪市",
              "永康市",
              "蒲江县",
              "武义县",
              "磐安县",
            ];
            var data1_1 = [500, 2000, 1000, 1800, 2200, 1200, 1500];
            var data2_1 = [200, 800, 900, 1500, 1800, 2200, 3000];
            var img = [
              "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABRCAYAAABFTSEIAAAACXBIWXMAAAsSAAALEgHS3X78AAAEp0lEQVR42u3cz4sjRRTA8W9Vd3Vn8mMmjj9WQWSRZQ+CsH+B7MnDIgiCd0E8CYJ/gOAIelo8ehUP/gF6WLw5/gMueFP2sIcF0dHd2Z1kknR11fOQZJJJMtlZd03H7HtQpNOTnpn+8Lrm1etmjIig8e/DKoECKqACKqCGAiqgAiqghgIqoAIqoIYCKqACKqCGAiqgAiqghgIqoAJudKTr+osZMNPvBUQBHwHsPF9fB9R0DeHMOQ6T6WOrhEzXBM4swDOL0M6CrArRVoq3t2dGUIb9fTvatg8ZZup1PDBgzPmy98mey6qfzjLz2WaWjEUZKEvGyi9nWyneMOvGIyFQo2Sbg4MUSChpU9IeTTUpJdsEajPZOJeJG5uBZj7rLLduWS5dGm6XNLEELOFUFj54ACJCaychkpDSASK3bwsXL0YgVpWJKwM0iy9Zy8HdGru7jvt3Pbu7w0wES7drTwAbjTHMGCsQcIAnYTC1/wRx0wEnl27JNgZI8HQ6Kc1mQq83RNzaMjPzXqDbjTQaJRFLxIyyMSxAXEkWrhrQzAAmo5HOjCQf7jflILxOkohL+aUPgV4vEGNJo+E5PAy02+UIMEwBxo0CPDP7Dg5SnEtpt1PA0e87XO25FOoh8IYIH2Y5b45RzGAQBiIltZoHxqMcjbksXAVgdc2EQMYzzzdotyeZWKuleULXJtwT4SODfC2QCWR+IF9KnjuX1Xbo99Op7LVE8iXlz0YBTk5SyLEEjo5OLuccEoFUvHfO+reuUPx4zftXAIcx1hdcF+/TvFab4A0Bs0VwqyhpVnkJT89/Q4DDQ0e77YCMwIUsFMeFZD856699URRvX4nxE4A/jbnxXp7v4Zw3ReGNSDHI8wFQjIafuoyn58L/fB6sth/Ybg9fez2TRC6QZcZYvgHsazF+MP7YCyLXcM7gvSXLDGBqYDg+NhwdmSpPoTrAkub0W+f4FSB1fDucIunMHSLpO8WAH0rSy8u+19MBCHB4OHzd2pI+CEUhpigEiN+l6WcdY252jLn5s7Wf472ImPcN8pUl/tEHoV4XWq1Ke4KrLmPsTA3oODpytFoOyJKSyzHyMSIxteWngMW5cSEdDJQUhTdZVgxOz3/+jFJm4+bA2e5JpNU6WZ4Fw99JwnWMKccwpeddP+B7GZTNUPKqybJy0O+Hs1YfMz9swwvpB8fbGDG0GuGkkK7V0hxSmZQpABI8l2z0v3sJf50qpAMJCd2qCulql3LD1lRGQjm7lEsDz0rkxTQOfiPPxUBcuJTbbhss/Y1eyi3NwsmKInmkZsKk5gtPUzNhvp11507CSy/X6XYStpvFudpZw1ZWIOF4Cq6SdtbKbioJyAhRTu3u9yMJXerN+ugvaQQsjcZ8Q3VnZwxlSDhe1lB9GjrSw5b+1avT8+Jw+979nNaOI6U3KpTrWAosxVQmygK4ld8X0ZtK/7eViExD7O1NQPb3T7fsl4/4sBpwYzPwjFbTo95Yl9l9Vd1YN1X/147HebSjary1AHyc5qc+XLQEQx9ve8Kg6xr6hKoCKqACKqCGAiqgAiqghgIqoAIqoIYCKqACKqCGAiqgAiqghgIq4JrHP8fEWV8FMTmOAAAAAElFTkSuQmCC",
              "image://data:image/png;base64,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",
              "image://data:image/png;base64,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",
              "image://data:image/png;base64,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",
            ];

            var data1_2 = [{ coords: [] }];
            var data2_2 = [{ coords: [] }];
            for (let i = 0; i < XName.length; i++) {
              data1_2[0].coords.push([XName[i], data1_1[i]]);
              data2_2[0].coords.push([XName[i], data2_1[i]]);
            }

            option = {
              grid: {
                top: "20%",
                left: "10%",
                bottom: "20%",
                right: "5%",
              },
              tooltip: {
                trigger: "axis",
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "28",
                },
              },
              legend: {
                // orient: 'vertical',
                itemWidth: 18,
                itemHeight: 18,
                right: "10%",
                top: "0%",
                icon: "circle",
                itemGap: 45,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                  padding: [0, 0, 0, 20],
                },
                data: ["当年", "上年"],
              },
              yAxis: [
                {
                  name: "单位：件",
                  type: "value",
                  position: "left",
                  nameTextStyle: {
                    color: "#D6E7F9",
                    fontSize: 28,
                    padding: [0, 0, 20, 0],
                  },
                  splitLine: {
                    lineStyle: {
                      type: "dashed",
                      color: "rgba(135,140,147,0.8)",
                    },
                  },
                  axisLine: {
                    show: false,
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.8,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 28,
                      color: "#D6E7F9",
                    },
                  },
                },
              ],
              xAxis: [
                {
                  type: "category",
                  axisTick: {
                    show: false,
                  },
                  offset: 20,
                  axisLine: {
                    show: false,
                    lineStyle: {
                      color: "#0696f9",
                    },
                  },
                  axisLabel: {
                    inside: false,
                    textStyle: {
                      fontSize: 28,
                      color: "#D6E7F9",
                      lineHeight: 22,
                    },
                  },
                  data: XName,
                },
              ],
              series: [
                {
                  symbolSize: 150,
                  symbol: img[2],
                  name: "当年",
                  type: "line",
                  data: data1_1,
                  itemStyle: {
                    normal: {
                      borderWidth: 5,
                      color: "#0696f9",
                    },
                  },
                },
                {
                  symbolSize: 150,
                  symbol: img[3],
                  name: "上年",
                  type: "line",
                  data: data2_1,
                  itemStyle: {
                    normal: {
                      borderWidth: 5,
                      color: "#d4dd48",
                    },
                  },
                },
                {
                  name: "滑行的光点",
                  type: "lines",
                  coordinateSystem: "cartesian2d",
                  symbolSize: 30,
                  polyline: true,
                  effect: {
                    show: true,
                    trailLength: 0,
                    symbol: "arrow",
                    period: 10, //光点滑动速度
                    symbolSize: 150,
                    symbol: img[0],
                  },
                  lineStyle: {
                    normal: {
                      width: 1,
                      opacity: 0.6,
                      curveness: 0.2,
                    },
                  },
                  data: data1_2,
                },
                {
                  name: "滑行的光点",
                  type: "lines",
                  coordinateSystem: "cartesian2d",
                  symbolSize: 30,
                  polyline: true,
                  effect: {
                    show: true,
                    trailLength: 0,
                    symbol: "arrow",
                    period: 10, //光点滑动速度
                    symbolSize: 150,
                    symbol: img[1],
                  },
                  lineStyle: {
                    normal: {
                      width: 1,
                      opacity: 0.6,
                      curveness: 0.2,
                    },
                  },
                  data: data2_2,
                },
              ],
            };
            myChart.setOption(option);
            myChart.getZr().on('mousemove', param => {
              myChart.getZr().setCursorStyle('default')
            })
          },
        },
        beforeDestroy() {
          clearInterval(this.setInterval)
        },
      });
    </script>
  </body>
</html>
