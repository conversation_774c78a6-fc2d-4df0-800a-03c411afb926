<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <link rel="stylesheet" href="icon/iconfont.css">
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/elementui/js/index.js"></script>
    <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
    <style>
        body,
        html {
            margin: 0;
            font-size: 75px;
        }

        li,
        ul {
            list-style: none;
        }

        /* 设置滚动条的样式 */
        ::-webkit-scrollbar {
            width: 0.2rem;
            height: 0.2rem;
        }

        /* 滚动槽 */
        ::-webkit-scrollbar-track {
            border-radius: 0.1rem;
        }

        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            border-radius: 0.1rem;
            background: rgba(35, 144, 207, 0.3);
        }

        ::-webkit-scrollbar-thumb:window-inactive {
            background: rgba(27, 146, 215, 0.5);
        }

        #toolbar-tckz {
            /* width: 7680px;
            height: 2160px;
            background: #333; */
            position: relative;
        }

        .control-box {
            position: absolute;
            right: 1rem;
            /* top: 1rem; */
            z-index: 11;
        }

        .boxshadow {
            box-shadow: 0 0 0.1rem #c1c1c1;
        }

        .instrument-box {
            display: flex;
            align-items: center;
            border-radius: 0.2rem;
            background: #333;
            padding: 0.3rem 0;
            opacity: .9;
        }

        .instrument-item {
            border-right: 1px solid #7d7d7d;
            display: flex;
            align-items: center;
            font-size: 0.6rem;
            font-weight: 700;
            padding: 0.1rem 0.6rem;
            position: relative;
            cursor: pointer;
            color: #fff;
            white-space: nowrap;
        }

        .instrument-item:hover {
            color: #fb8e03;
        }

        .instrument-item-icon {
            padding-right: 5px;
            font-size: 0.6rem;
        }

        .pull-down {
            width: 100%;
            position: absolute;
            left: 0;
            top: 1.5rem;
            background-color: #333;
            box-shadow: 0 0 0.1rem #c1c1c1;
            border-radius: 0.1rem;
            display: block;
            padding: 0;
        }

        .pull-down-more {
            width: 100%;
            margin-left: 0.2rem;
            position: absolute;
            top: 1.5rem;
            background-color: #333;
            box-shadow: 0 0 0.1rem #c1c1c1;
            border-radius: 0.1rem;
            display: block;
            padding: 0;
            max-height: 4.2rem;
            overflow-y: scroll;
            color: #fff;
        }

        .pull-down-item {
            width: 100%;
            box-sizing: border-box;
            text-align: center;
            font-size: 0.6rem;
            padding: 0.2rem 0.2rem 0.4rem 0.2rem;
            font-weight: 400;
            cursor: pointer;
            color: #fff;
        }

        .active-item {
            color: #fb8e03 !important;
        }

        .checkedBox {
            width: 0.3rem;
            height: 0.3rem;
            margin-right: 0.3rem;
            position: relative;
            top: -0.08rem;
        }

        /* .point-box {
            width: 600px;
            min-height: 100px;
            position: relative;
            font-size: 16px;
            color: white;
            box-shadow: inset 0px 2px 4px 0px rgb(43 154 208 / 20%);
            border-style: solid;
            border-width: 4px;
            border-image-source: linear-gradient(0deg, #32abe4 0%, #0b5aa4 100%);
            border-image-slice: 1;
            background: rgba(0, 32, 52, 0.9);
        }

        .point-title {
            height: 50px;
            padding: 0 10px;
            display: flex;
            justify-content: space-between;
            background-color: blue;
            box-sizing: border-box;
            line-height: 50px;
            font-size: 18px;
            background-image: linear-gradient(0deg, rgba(0, 89, 147, 0.9) 0%, rgba(0, 32, 52, 0.9) 100%), linear-gradient(0deg, rgba(103, 200, 255, 0.2) 0%, rgba(110, 176, 231, 0.1) 100%);
        }

        .point-body {
            padding: 10px;
            box-sizing: border-box;
        } */
    </style>
</head>

<body>
    <div id="toolbar-tckz">
        <div class="control-box">
            <ul class="boxshadow instrument-box">
                <li class="instrument-item" :class="instrumentIndex===index?'active-item':''"
                    :style="{ display: item.visit ? 'block' : 'none' }" v-for="(item,index) in instrumentList"
                    :key="`instrument`+index" @click="changeInstrument(index)">
                    <i class="iconfont instrument-item-icon" :class="item.icon"></i>
                    <span class="instrument-item-text">{{item.name}}</span>
                    <ul class="pull-down boxshadow" v-show="instrumentIndex===index && pullDownShow">
                        <li class="pull-down-item" :class="pullDownIndex===index1?'active-item':''"
                            :style="{cursor:item.visit?'pointer':'not-allowed'}" :disabled="true"
                            v-for="(item,index1) in pullDownList" :key="`pullDown`+index1"
                            @click.stop="changePullDown(index1)">
                            <i class="iconfont instrument-item-icon" :class="item.icon"></i>
                            <span class="instrument-item-text">{{item.name}}</span>
                        </li>
                    </ul>
                    <div class="pull-down-more boxshadow" style="left:4.3rem;line-height: 1rem;"
                        v-show="instrumentIndex===index && pullDownItem_moreShow">
                        <form @change="change" @click.stop="" style="padding:0.2rem;font-weight: 400;">
                            <div v-for="(item,index2) in labelList" :key="index2">
                                <input type="checkbox" v-model="checkList" :value="item.count+','+item.key"
                                    class="checkedBox">{{item.key}}
                            </div>
                        </form>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</body>

</html>

<script>
    window.wss = DHWs.getInstance({
        reConnectCount: 2,
        connectionTimeout: 30 * 1000,
        messageEvents: {
            loginState() {
                console.log('aaaa');
            }
        }
    })

    var vm = new Vue({
        el: '#toolbar-tckz',
        data: {
            instrumentIndex: null,
            instrumentList: [
                {
                    icon: 'icon-celiang1',
                    name: '面积检测',
                    visit: false,
                },
                {
                    icon: 'icon-biaohui',
                    name: '地图标绘',
                    visit: false,
                },
                {
                    icon: 'icon-kongjianfenxi',
                    name: '空间查询',
                    visit: true,
                },
                {
                    icon: 'icon-dingwei1',
                    name: '定位拾取',
                    visit: false,
                },
                {
                    icon: 'icon-dingwei',
                    name: '通视性分析',
                    visit: false,
                },
                {
                    icon: 'icon-qingchu',
                    name: '地图清除',
                    visit: true,
                }
            ],
            pullDownIndex: null,
            pullDownList: [],
            pullDownList1: [
                {
                    icon: 'icon-mianjiceliang',
                    name: '面积测量',
                    visit: false,
                },
                {
                    icon: 'icon-juliceliang',
                    name: '距离测量',
                    visit: false,
                },
                {
                    icon: 'icon-colum-height',
                    name: '高度测量',
                    visit: false,
                },
            ],
            pullDownList2: [
                {
                    icon: 'icon-icon-dian',
                    name: '点标绘',
                    visit: true,
                },
                {
                    icon: 'icon-icon-xian',
                    name: '线标绘',
                    visit: true,
                },
                {
                    icon: 'icon-icon-mian',
                    name: '面标绘',
                    visit: true,
                },
            ],
            pullDownList3: [
                {
                    icon: 'icon-duobianxing',
                    name: '多边形查询',
                    visit: true,
                },
                {
                    icon: 'icon-icon-dian',
                    name: '点缓冲查询',
                    visit: false,
                },
                {
                    icon: 'icon-jianhaoyuan',
                    name: '线缓冲查询',
                    visit: false,
                },
            ],
            pullDownList4: [
                {
                    icon: 'icon-dingwei1',
                    name: '定位拾取',
                    visit: false,
                },
                {
                    icon: 'icon-dingweixiao',
                    name: '地图定位',
                    visit: false,
                }
            ],
            pullDownList5: [
                {
                    icon: 'icon-dingwei',
                    name: '通视分析',
                    visit: false,
                },
                {
                    icon: 'icon-qingchu',
                    name: '可视域分析',
                    visit: false,
                }
            ],
            pullDownShow: false,
            pullDownItem_moreShow: false,

            //类型标签
            labelList: [
                {
                    count: 4840,
                    key: "企事业单位",
                },
                {
                    count: 4840,
                    key: "其他兴趣点",
                },
                {
                    count: 4840,
                    key: "中餐馆",
                },
                {
                    count: 4840,
                    key: "视频点位",
                },
                {
                    count: 4840,
                    key: "机关单位",
                },
                {
                    count: 4840,
                    key: "机关单位1",
                },
                {
                    count: 4840,
                    key: "机关单位2",
                },
                {
                    count: 4840,
                    key: "机关单位3",
                }
            ],
            label: [],
            checkList: [],
            tckg_toolbar_obj: {},
        },
        created() {

        },
        mounted() {
            var that = this
            //that.addVideo()
            window.addEventListener('message', function (e) {
                let info = e.data
                // console.log(info);
                if (info.type == 'getPolygonResult') {   //三维-多边形绘制完成返回参数'getPolygonResult'
                    that.pullDownItem_moreShow = true
                    that.tckg_toolbar_obj = {}
                    that.labelList = info.data.dataList
                    that.labelList.forEach((item, index) => {
                        that.tckg_toolbar_obj[item.key] = index
                    })
                } else if (info.type == 'getDatas') {      //二维-多边形绘制完成返回参数'getDatas'
                    that.pullDownItem_moreShow = true
                    that.tckg_toolbar_obj = {}
                    that.labelList = info.data.dataList
                    that.labelList.forEach((item, index) => {
                        that.tckg_toolbar_obj[item.key] = index
                    })
                } else if (info.type == 'getDataByType') {  //获取点位数据返回参数'getDataByType'
                    let list = []
                    info.data.dataList.forEach((item) => {
                        let data = {
                            data: {
                                title: item.name,
                                key: ['类型','地址', '全称'],
                                value: [item.esType1,item.address, item.name],
                                code: item.region,
                            },
                            point: item.esX + "," + item.esY,
                            // code:'33079953001320081919'  //视频弹出
                        }
                        list.push(data)
                    });
                    let index = that.tckg_toolbar_obj[info.data.dataList[0].esType1]
                    let pointType = info.data.dataList[0].esType1

                    //加载点位
                    top.document.getElementById('map').contentWindow.Work.funChange(
                        JSON.stringify({
                            "funcName": "pointLoad",
                            "pointType": 'point-' + pointType, // 点位类型（图标名称）
                            "pointId": "tckz_toolbar_" + index, // 点位唯一id
                            "height": "0",
                            "size": [0.1, 0.1, 0.1, 0.1],
                            "popup": {
                                offset: [0, 50],
                            },
                            "pointData": list,
                            popup:{
                              offset:[50,-100]
                              }
                            //"setClick": false,
                        })
                    )
                }
                // else if (info.type == 'pointClick') {     //二维-点位点击返回参数'pointClick'
                //     let data = info.data;
                //     let point = [data.point.split(",")[0], data.point.split(",")[1]]
                //     let pointInfo = JSON.parse(data.data)

                //     let display = 'block'
                //     if(pointInfo.value[2]==="金华市"){
                //         display = "none"
                //     }
                //     if(pointInfo.value[1]==="" || pointInfo.value[1]===null){
                //         pointInfo.value[1] = '-'
                //     }
                //     //绘制自定义弹框
                //     top.document.getElementById('map').contentWindow.Work.funChange(
                //         JSON.stringify({
                //             funcName: 'customPop',
                //             coordinates: [point[0], point[1]],
                //             popup: {
                //                 offset: [100, 300],  //默认偏移量[0,20]
                //             },
                //             html: `<div class="point-box"
                //                         style="min-width: 400px;
                //                                 min-height: 100px;
                //                                 position: relative;
                //                                 font-size: 25px;
                //                                 color: white;
                //                                 box-shadow: inset 0px 2px 4px 0px rgb(43 154 208 / 20%);
                //                                 border-style: solid;
                //                                 border-width: 4px;
                //                                 border-image-source: linear-gradient(0deg, #32abe4 0%, #0b5aa4 100%);
                //                                 border-image-slice: 1;
                //                                 background: rgba(0, 32, 52, 0.9);">
                //                         <div class="point-title"
                //                             style="height: 50px;
                //                                     padding: 0 10px;
                //                                     display: flex;
                //                                     justify-content: space-between;
                //                                     background-color: blue;
                //                                     box-sizing: border-box;
                //                                     line-height: 50px;
                //                                     font-size: 30px;
                //                                     background-image: linear-gradient(0deg, rgba(0, 89, 147, 0.9) 0%, rgba(0, 32, 52, 0.9) 100%), linear-gradient(0deg, rgba(103, 200, 255, 0.2) 0%, rgba(110, 176, 231, 0.1) 100%);">
                //                             <div style="width: 280px;white-space: nowrap;overflow: hidden;text-overflow:ellipsis;">`+pointInfo.title+`</div>
                //                             <div style="display:flex;">
                //                                 <img src="/static/citybrain/tckz/img/rckz/icon/video.png" alt=""
                //                                     width="30" height="30" style="margin:10px 10px 0 0;display:`+display+`;"
                //                                     id="point_video" info=${pointInfo.value[2]} >
                //                                 <img src="/static/citybrain/tckz/img/rckz/icon/close.png" alt=""
                //                                     width="30" height="30" style="margin:10px 10px 0 0;"
                //                                     id="point_close">
                //                             </div>
                //                         </div>
                //                         <div class="point-body"
                //                                 style="padding: 10px;
                //                                     box-sizing: border-box;">
                //                             <p style="line-height:50px">类型：`+pointInfo.value[0]+`</p>
                //                             <p style="line-height:50px">地址：`+pointInfo.value[1]+`</p>
                //                             <p style="line-height:50px">全称：`+pointInfo.title+`</p>
                //                         </div>
                //                     </div>`
                //         })
                //     )

                //     //弹框内关闭按钮
                //     top.document.getElementById('map').contentWindow.document.querySelector('#point_close').onclick = function () {
                //         top.document.getElementById('map').contentWindow.Work.funChange(
                //             JSON.stringify({
                //                 funcName: 'rmPop',
                //             })
                //         )
                //     }
                //     //弹框内加载视频按钮
                //     top.document.getElementById('map').contentWindow.document.querySelector('#point_video').onclick = function () {
                //         let code = top.document.getElementById('map').contentWindow.document.querySelector('#point_video').getAttribute('info');
                //         if(code){
                //             window.wss.openVideo([code])
                //         }
                //     }
                // }
            });

        },
        methods: {
            //切换tab按钮
            changeInstrument(index) {
                if (this.instrumentIndex === index) {
                    this.pullDownShow = false
                    this.instrumentIndex = null
                } else {
                    this.instrumentIndex = index
                    this.pullDownShow = true
                    this.pullDownIndex = null
                    this.pullDownList = []
                    this.pullDownItem_moreShow = false

                    switch (index) {
                        case 0:
                            this.pullDownList = this.pullDownList1;
                            break;
                        case 1:
                            this.pullDownList = this.pullDownList2;
                            break;
                        case 2:
                            this.pullDownList = this.pullDownList3;
                            break;
                        case 3:
                            this.pullDownList = this.pullDownList4;
                            break;
                        case 4:
                            this.pullDownList = this.pullDownList5;
                            break;
                        case 5:
                            this.cleanMap()
                            break;
                    }
                }

            },
            //选择标签
            changePullDown(index1) {
                if (this.pullDownList[index1].visit) {
                    this.pullDownIndex = index1
                    this.pullDownItem_moreShow = false
                    this.handleMap(this.instrumentIndex, this.pullDownIndex)
                }
            },
            //操作地图
            handleMap(index_1, index_2) {
                // console.log(index_1,index_2);
                if (index_1 === 0) {
                    if (index_2 === 0) {

                    } else if (index_2 === 1) {

                    } else if (index_2 === 2) {

                    }
                } else if (index_1 === 1) {
                    if (index_2 === 0) {
                        this.setPoint()
                    } else if (index_2 === 1) {

                    } else if (index_2 === 2) {

                    }
                } else if (index_1 === 2) {
                    if (index_2 === 0) {
                        this.queryPolygon()
                    } else if (index_2 === 1) {
                        
                    } else if (index_2 === 2) {

                    }
                } else if (index_1 === 3) {
                    if (index_2 === 0) {

                    } else if (index_2 === 1) {

                    }
                } else if (index_1 === 4) {
                    if (index_2 === 0) {

                    } else if (index_2 === 1) {

                    }
                }
            },
            // 面积测量

            // 距离测量

            // 高度测量

            // 点标绘
            setPoint() {
                top.document.getElementById('map').contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: 'setPoint',
                        pointType: 'gnqPoint',
                        pointId: 'aab',
                        data: { text: 'aaa', color: [255, 0, 0, 1] }, // 信息
                        textStatus: false // 是否展示文字
                    })
                )
            },

            // 线标绘

            // 面标绘

            // 多边形查询
            queryPolygon() {

                // 三维方法-绘制多边形
                top.document.getElementById('map').contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: 'startDrawPolygon',
                        color: [255, 0, 0, 0.5],//绘制区域颜色
                        pointAndTextStatus: false, // 是否展示文字和点位
                        getSectionDatas: true,//是否查询区域数据，默认false
                    })
                )

                // 二维方法-绘制多边形
                // top.document.getElementById('map').contentWindow.Work.funChange(
                //     JSON.stringify({
                //         funcName: 'measureAnalysisPolygon',
                //     })
                // )
            },
            //多边形查询-类型选择
            change(e) {
                let id = 0
                let index = this.tckg_toolbar_obj[e.target._value.split(',')[1]]
                if (e.target.checked) {
                    // 三维
                    top.document.getElementById('map').contentWindow.Work.funChange(
                        JSON.stringify({
                            "funcName": "getDataByType",
                            "type": e.target._value.split(',')[1], //数据类型（类型根据多边形返回数据获取）
                            "pageIndex": 1,//当前页数
                            "pageSize": e.target._value.split(',')[0],//每页条数
                        })
                    )

                    // 二维 - 根据类型查询点位数据
                    // top.document.getElementById('map').contentWindow.Work.funChange(
                    //     JSON.stringify({
                    //         "funcName": "getDataByType",
                    //         "esType1": e.target._value.split(',')[1], //数据类型（类型根据多边形返回数据获取）
                    //         "pageIndex": 1,//当前页数
                    //         "pageSize": e.target._value.split(',')[0],//每页条数
                    //     })
                    // )
                } else {
                    top.document.getElementById('map').contentWindow.Work.funChange(
                        JSON.stringify({
                            funcName: 'rmPoint',
                            "pointId": "tckz_toolbar_" + index,//传id清除单类，不传清除所有
                        })
                    )
                }
            },
            // 点缓冲查询

            // 线缓冲查询

            // 定位拾取

            // 地图定位

            // 通视分析

            // 可视域分析

            // 地图清除
            cleanMap() {
                //重置多边形
                top.document.getElementById('map').contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: 'rmDrawPolygon',
                    })
                )
                //清除点位
                top.document.getElementById('map').contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: 'rmPoint',
                        "pointId": "",//传id清除单类，不传清除所有
                    })
                )
            },

            //登陆视频插件
            addVideo() {
                let DHWsInstance = window.wss;
                DHWsInstance.detectConnectQt().then((res) => {
                    if (res) {
                        // 连接客户端成功
                        window.wss.login({
                            loginIp: "*************",
                            loginPort: "8001",// 本地
                            userName: "system",
                            userPwd: "JHcsdn@123456",
                            token: "",
                        });
                        window.wss.on("loginState", (res) => {
                            if (res) {
                                console.log("登录成功");
                                this.isLogin = true
                            } else {
                                console.log("登录失败");
                            }
                        });
                    } else {
                        // 连接客户端失败
                        console.log("连接客户端失败");
                    }
                });
            },
        },
    })
</script>
