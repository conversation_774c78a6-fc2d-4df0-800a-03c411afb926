<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>领域4-中间地图</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <link
      rel="stylesheet"
      href="/static/citybrain3840/shgl/css/map-dialog.css"
    />
    <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
  </head>
  <style>
    #map-middle {
      position: relative;
      left: 0px;
      top: 0px;
    }
    .tree {
      height: 410px;
    }
  </style>

  <body>
    <div id="map-middle" v-cloak>
      <div class="tree">
        <!-- :default-checked-keys="[1]" -->
        <el-tree
          :data="treeData"
          show-checkbox
          node-key="id"
          ref="tree"
          highlight-current
          @check-change="checkChange"
          @node-click="handleNodeClick"
          class="auth-tree"
          :render-after-expand="false"
          icon-class="el-icon-caret-left"
        >
          <div
            style="display: flex; align-items: center"
            slot-scope="{ node, data }"
          >
            <div class="node-lable">
              {{ node.label }}
              <span v-if="data.children">({{data.children.length}})</span>
              <img
                v-if="data.parentid!='szwh'"
                style="width: 30px; margin-right: 15px"
                :src="`/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/${data.icon}.png`"
                alt=""
              />
            </div>
          </div>
        </el-tree>
      </div>
    </div>
  </body>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>

  <script>
    var vm = new Vue({
      el: "#map-middle",
      data() {
        return {
          click_item: {},
          treeData: [
            {
              id: 1,
              label: "重大危险源",
              icon: "szwh-景区",
              code: "景区",
            },
            {
              id: 2,
              label: "救援资源",
              icon: "szwh-等级民宿",
              code: "等级民宿",
            },
            {
              id: 3,
              label: "预警",
              icon: "szwh-文化馆",
              code: "文化馆",
            },
            {
              id: 4,
              label: "监控",
              icon: "videoCon",
              code: "景区",
            },
            {
              id: 5,
              label: "危险预警",
              icon: "warning_red",
              code: "旅行社",
            },
            {
              id: 6,
              label: "隐患排查",
              children: [
                {
                  id: 61,
                  label: "一般隐患",
                  code: "一般隐患",
                  disabled: true,
                },
                {
                  id: 62,
                  label: "重大隐患",
                  code: "重大隐患",
                  disabled: true,
                },
                {
                  id: 63,
                  label: "企业自查隐患",
                  code: "企业自查隐患",
                  disabled: true,
                },
                {
                  id: 64,
                  label: "政府巡查隐患",
                  code: "政府巡查隐患",
                  disabled: true,
                },
                {
                  id: 65,
                  label: "公众举报隐患",
                  code: "公众举报隐患",
                  disabled: true,
                },
              ],
            },
            {
              id: 7,
              label: "风险诊断",
              code: "文化馆",
              children: [
                {
                  id: 71,
                  label: "一级",
                  code: "一级",
                  disabled: true,
                },
                {
                  id: 72,
                  label: "二级",
                  code: "二级",
                  disabled: true,
                },
                {
                  id: 73,
                  label: "三级",
                  code: "三级",
                  disabled: true,
                },
                {
                  id: 74,
                  label: "四级",
                  code: "四级",
                  disabled: true,
                },
              ],
            },
          ],
        };
      },
      mounted() {
        this.initMap();
        this.popFun();
        var that = this;
        window.addEventListener("message", function (e) {
          let info = e.data;
          if (
            info.type == "bankuaiClick" &&
            that.click_item.label == "危险预警"
          ) {
            that.clearText();
            that.getPoint(that.click_item);
          }
          console.log(info);
          if (
            info.type == "pointClick" &&
            that.click_item.label == "危险预警"
          ) {
            window.parent.postMessage(
              JSON.stringify({
                type: "openIframe",
                name: "echart_wxyj",
                src: "/static/citybrain3840/shgl/pages/yjgl/echart_wxyj.html",
                width: "850px",
                height: "530px",
                left: "1912px",
                top: "1357px",
                zIndex: "10",
              }),
              "*"
            );
          }
        });
      },
      methods: {
        initMap() {
          top.document.getElementById("map").contentWindow.Work.change3D(9);
          $get("/textCity.json").then((res) => {
            let textData = [];
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "3Dtext", //功能名称
                textData: res,
                textSize: 35,
              })
            );
          });
        },
        handleNodeClick(data, node) {
          this.$refs.tree.setChecked(node, !node.checked);
        },
        checkChange(item, flag) {
          let click_true_list = this.$refs.tree.getCheckedNodes();
          if (click_true_list.length == 0) {
            this.popFun();
            this.rmPoint(item);
          } else {
            this.clearPop();
            if (flag) {
              if (item.label == "危险预警") {
                this.click_item = item;
                this.setTextandNum();
                return;
              }
              this.getPoint(item);
            } else {
              this.rmPoint(item);
            }
          }
        },
        async popFun() {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "flyto", //功能名称
              flyData: {
                center: [119.95478050597587, 29.11613226366889],
                zoom: 9,
                pitch: 50,
                bearing: 0,
              },
            })
          );
          console.log("地图弹窗=================================");

          var textData = await $get("personInTime");
          let maxValueArr = [];
          let maxValue = "";
          textData.map((item) => {
            // 所有值中的最大值
            let a = +item.value[0];
            let b = +item.value[1];
            let c = +item.value[2];
            maxValueArr.push(a);
            maxValueArr.push(b);
            maxValueArr.push(c);
            // //科学技术
            // item.value[0] = setAct(item.value[0]);
            // item.value[1] = setAct(item.value[1]);
            // item.value[2] = setAct(item.value[2]);
            // item.value[3] = setAct(item.value[3]);
          });
          maxValue = Math.max(...maxValueArr);
          for (let i = 0; i < textData.length; i++) {
            let a1 = parseInt(Number(textData[i].value[0]));
            let a2 = parseInt(Number(textData[i].value[1]));
            let a3 = parseInt(Number(textData[i].value[2]));
            let a4 = parseInt(Number(textData[i].value[3]));
            let a1_res = (a1 / maxValue).toFixed(2);
            let a2_res = (a2 / maxValue).toFixed(2);
            let a3_res = (a3 / maxValue).toFixed(2);
            // console.log("最大值===》", a1_res, a2_res, a3_res);

            const url = `${baseURL.url}/static/citybrain/hjbh/img/rkzt/rkpc_bg.png`;
            let objData = {
              funcName: "customPop",
              coordinates: textData[i].pos,
              closeButton: false,
              html: `
                          <div style="min-width: 440px;overflow: hidden; height: 270px; position: relative;background: url('${url}') no-repeat;background-size: 100% 100%;">
                               <p style="position: absolute;
                               right: 45px;top: 30px;
               font-size: 28px;
               background-image: linear-gradient(180deg, #f7ad47, #fff, #ffd8a1);
               background-clip: text;
               -webkit-background-clip: text;
               color: transparent;
               font-weight: bold;
               font-style: italic;
               width: 160px;
              height: 30px;
               line-height: 30px;
               text-align: center;">${textData[i].name}</p>
                               <div style="width: 345px;overflow: hidden;
               position: absolute;
               top: 79px;
               left: 24px;
               height: 156px;
               padding: 0 20px;
               color: #fff;">
                                   <div style="font-size: 20px;height:33px">
                                       <span>低风险源：</span>
                                       <span style=" font-size: 20px;
               font-weight: 600;
               background-image: linear-gradient(180deg, #f7ad47, #fff, #ffd8a1);
               background-clip: text;
               -webkit-background-clip: text;
               color: transparent;">${textData[i].value[0]}人</span>
                                   </div>
                                   <div style="width: 100%;
               height: 8px;
               background-color: #144363;
               position: relative;
               border-radius: 12px;
               margin-bottom: 15px;">
                                       <div style="height: 8px !important;
               position: absolute;
               top: -1px;
               left: 0;
               border-radius: 12px;
               z-index: 100;background-image: linear-gradient(10deg, #ff4c4c, #ffa1a1);
               width:${
                 // a1 > 310 ? '100%' : a1 + 'px'
                 // a1_res * 310
                 a1_res * 100
               }%
               "></div>
                                   </div>
                                   <div style="font-size: 20px;height:33px">
                                       <span>一般风险源：</span>
                                       <span style=" font-size: 20px;
               font-weight: 600;
               background-image: linear-gradient(180deg, #f7ad47, #fff, #ffd8a1);
               background-clip: text;
               -webkit-background-clip: text;
               color: transparent;">${textData[i].value[1]}人</span>
                                   </div>
                                   <div style="width: 100%;
               height: 8px;
               background-color: #144363;
               position: relative;
               border-radius: 12px;
               margin-bottom: 15px;">
                                       <div style="height: 8px !important;
               position: absolute;
               top: -1px;
               left: 0;
               border-radius: 12px;
               z-index: 100;background-image: linear-gradient(10deg, #e9a53a, #e7aba3);width: ${
                 // a2 > 310 ? '100%' : a2 + 'px'
                 a2_res * 100
               }%"></div>
                                   </div>
                                   <div style="font-size: 20px;height:33px;display: flex;">
                                       <span style="white-space: nowrap;">较大风险源：</span>
                                       <span style=" font-size: 20px;white-space: nowrap;
               font-weight: 600;
               color:#00C0FF">${textData[i].value[2]}人</span>
                                   </div>
                                   <div style="width: 100%;
               height: 8px;
               background-color: #144363;
               position: relative;
               border-radius: 12px;
               margin-bottom: 15px;">
                                       <div style="height: 8px !important;
               position: absolute;
               top: -1px;
               left: 0;
               border-radius: 12px;
               z-index: 100; background-image: linear-gradient(10deg, #bb76db, #dcb4f3);width:${
                 // a3 > 310 ? '100%' : a3 + 'px'
                 // a3_res * 385
                 a3_res * 100
               }%"></div>
                                   </div>
                               </div>
                           </div>
                       `,
            };

            window.parent.document
              .getElementById("map")
              .contentWindow.Work.funChange(JSON.stringify(objData));
          }
        },
        getPoint(item) {
          this.click_item = item;
          $api("yxzl_szwh_center011", { code: item.code }).then((res) => {
            let pointData = [];
            let icon = item.icon;
            let key = [];
            let value = [];

            if (item.label == "重大危险源") {
              key = ["风险级别", "风险源位置", "投用时间", "类型", "R值"];
              value = [
                "一般风险源",
                "化工厂东北角",
                "2019年8月",
                "储存单位",
                "127.04",
              ];
            }else{
              key = ["风险级别", "风险源位置", "投用时间", "类型", "R值"];
              value = [
                "一般风险源",
                "化工厂东北角",
                "2019年8月",
                "储存单位",
                "127.04",
              ];

            }

            res.forEach((obj, index) => {
              if (
                obj.lng.split(",")[0].indexOf("无") < 0 ||
                obj.lng.split(",")[1].indexOf("无") < 0 ||
                obj.lng.split(",")[0] == 0 ||
                obj.lng.split(",")[1] == 0
              ) {
                let str = {
                  data:
                    key.length == 0
                      ? {}
                      : {
                          title: item.label + "详情",
                          key: key,
                          value: value,
                        },
                  point: obj.lng,
                };
                pointData.push(str);
              }
            });
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "pointLoad", //功能名称
                pointType: icon, //点位类型图标
                pointId: "shfx-" + item.id,
                pointData: pointData,
                setClick: true,
                imageConfig: { iconSize: 0.6 },
                popup: {
                  offset: [50, -100],
                },
              })
            );
          });
        },
        setTextandNum() {
          $get("/textCity.json").then((res) => {
            let textData = [];
            res.map((v) => {
              textData.push({
                text: v.text + "\n" + Math.ceil(Math.random() * 15),
                color: v.color,
                pos: v.pos,
              });
            });
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "3Dtext", //功能名称
                textData: textData,
                textSize: 35,
              })
            );
          });
        },
        clearText() {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rm3Dtext",
            })
          );
        },
        clearPop() {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmPop",
            })
          );
        },
        rmPoint(item) {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmPoint",
              pointId: "shfx-" + item.id, //传id清除单类，不传清除所有
            })
          );
        },
        rmAllPoint() {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmPoint",
              pointId: "", //传id清除单类，不传清除所有
            })
          );
        },
      },
      destroyed() {
        this.rmAllPoint();
      },
    });
  </script>
</html>
