<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/citybrain/jhpro/Vue/vue.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/elementui/js/index.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <!-- <script src="/static/js/home_services/iconfont.js"></script> -->
  </head>
  <body>
    <div id="tcgl_app" @click="cli">
      <div v-show="!show" class="btn" style="margin-left: -5px">
        <p>图层</p>
        <img src="/static/images/home/<USER>" alt="" />
      </div>
      <div v-show="show" class="s-flex">
        <div class="btn-active btn">
          <p class="s-p-t-0">图层</p>
          <img
            src="/static/images/home/<USER>"
            alt=""
          />
        </div>
      </div>
    </div>
    <script>
      var vm = new Vue({
        el: "#tcgl_app",
        data: {
          show: false,
        },
        methods: {
          // 清除
          rmAllFun() {
            top.document
              .getElementById("map")
              .contentWindow.Work.funChange(
                JSON.stringify({ funcName: "rmAll" })
              );
          },
          setVisible() {
            // 调用设置控件显隐接口
            let ctrlList = ["ctrl0", "ctrl1", "ctrl2"];
            ctrlList.forEach((item) => {
              const params = [
                {
                  ctrlCode: item,
                  visible: !top.DHWsInstance.ctrls.find((i) => {
                    if (i.ctrlCode === item) {
                      return i;
                    }
                  }).visible,
                },
              ];
              top.DHWsInstance.setCtrlVisible(params);
            });
          },
          cli() {
            if (top.window.frames["videoManage"]) return;
            this.show = !this.show;
            if (this.show) {
              this.open();
              top.vm.righttcShow = false;
              top.vm.middleShow = false;
              // this.setVisible()
              // console.log('88', top.window)
              // console.log('66', top.window[7].vm)
              // top.window
            } else {
              // top.vm.cancel()
              // this.setVisible()
              this.close();
              this.rmAllFun();
              top.vm.righttcShow = true;
              top.vm.middleShow = true;
              top.vm.imgShow = false;
              this.closeYgfx();

              for (var k in top.window) {
                // console.log(top.window[k])
                if (top.window[k]) {
                  if (top.window[k].name === "tckz_tcgl") {
                    // console.log('reobj', top.window[k])
                    var win_obj = top.window[k];
                    // console.log(win_obj.vm.options)

                    win_obj.vm.options.forEach((item) => {
                      item.children.forEach((obj) => {
                        // console.log(obj.label)
                        top.document
                          .getElementById("map")
                          .contentWindow.Work.funChange(
                            JSON.stringify({
                              funcName: "rmAddMap",
                              id: "map" + obj.label,
                            })
                          );
                      });
                    });
                  }
                }
              }
            }
          },
          closeYgfx() {
            top.commonObj.funCloseIframe({
              name: "ygfx_legend",
            });
            top.commonObj.funCloseIframe({
              name: "ygfx_chart",
            });
          },
          open() {
            this.show = true;

            try {
              top.document.getElementById("map").contentWindow.Work.change3D(7);
            } catch (error) {}
            //     <iframe src="./tcgl.html" style="position: absolute; z-index: 10; left: 50px" width="600px" height="1500rem"
            //   frameborder="0"></iframe>
            // <iframe src="./toolbar.html" style="position: absolute; z-index: 9; right: 0; top: 100px" width="100%"
            //   height="800rem" frameborder="0" id="toolbar"></iframe>

            let leftData = {
              type: "openIframe",
              name: "tckz_tcgl",
              src: baseURL.url + "/static/citybrain/tckz/tcgl.html",
              width: "600px",
              height: "920px",
              left: "2200px",
              top: "200px",
              zIndex: 999,
            };
            let rightData = {
              type: "openIframe",
              name: "tckz_toolbar",
              src: baseURL.url + "/static/citybrain/tckz/toolbar.html",
              width: "800px",
              height: "550px",
              left: "4790px",
              top: "160px",
              zIndex: "100",
            };
            window.parent.postMessage(JSON.stringify(leftData), "*");
            // window.parent.postMessage(
            //   JSON.stringify(rightData),
            //   '*'
            // )

            // window.parent.postMessage(
            //     JSON.stringify({
            //       type: 'fadeIframe',
            //       data: ['leftOut'],
            //     }),
            //     '*'
            //   )
          },
          close() {
            this.closeDetail();
            try {
              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  funcName: "rmPoint",
                  pointId: "",
                })
              );

              // top.document.getElementById('map').contentWindow.Work.funChange(
              //   JSON.stringify({
              //     funcName: 'rm3DtextById',
              //     id: '',
              //   })
              // )
              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  funcName: "rmAddMap",
                  id: "TDT_TITLE_ID_route",
                })
              );
              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  funcName: "rmAddMap",
                  id: "TDT_TITLE_ID_point",
                })
              );
              // top.document.getElementById('map').contentWindow.Work.funChange(
              //   JSON.stringify({
              //     funcName: 'rmAddMap',
              //     id: '',
              //   })
              // )
            } catch (error) {}
            window.parent.postMessage(
              JSON.stringify({
                type: "closeIframe",
                name: "tckz_tcgl",
              }),
              "*"
            );
            // window.parent.postMessage(
            //   JSON.stringify({
            //     type: 'closeIframe',
            //     name: 'tckz_toolbar'
            //   }),
            //   '*'
            // )
            // window.parent.postMessage(
            //   JSON.stringify({
            //     type: 'fadeIframe',
            //     data: ['leftIn'],
            //   }),
            //   '*'
            // )
          },
          closeDetail() {
            let data = JSON.stringify({
              type: "closeIframe",
              name: "tcgl-detail-tc",
            });
            window.parent.postMessage(data, "*");
          },
        },
      });
    </script>
    <style>
      #tcgl_app {
        width: 100%;
        height: 100%;
      }
      .btn-active {
        position: relative;
        /* top: 0px !important; */
        color: #ffc460 !important;
        background-image: url("/static/images/home/<USER>") !important;
      }
      .btn {
        width: 54px;
        /* height: 255px; */
        height: 180px;
        background-image: url("/static/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        font-size: 38px;
        color: white;
        line-height: 42px;
        text-align: center;
        cursor: pointer;
      }
      .btn p {
        padding-top: 30px;
      }
      .btn img {
        width: 28px;
        height: 29px;
        position: relative;
        top: -40px;
      }

      .video_box {
        width: 487px;
        height: 1433px;
        background-color: #091e35;
        border-top: 1px solid #359cf8;
        border-left: 1px solid #359cf8;
        border-bottom: 1px solid #359cf8;
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
      }
      .video_box_l2 {
        border-left: 0px;
        border-right: 1px solid #359cf8;
        border-top-left-radius: 0px;
        border-bottom-left-radius: 0px;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
      }

      ::-webkit-scrollbar {
        width: 0;
        height: 0;
        background-color: transparent;
      }

      .video_box input {
        margin-left: 15px;
        margin-top: 20px;
        width: 90%;
        height: 44px;
        background: #132c4e;
        border: 1px solid #359cf8;
        opacity: 0.9;
        border-radius: 10px;
        font-size: 28px;
      }
      .video_box button {
        margin-top: 20px;
        height: 44px;
        line-height: 14px;
        width: 100px;
        border: 1px solid #359cf8;
        background-image: linear-gradient(
          -32deg,
          rgba(0, 32, 52, 0.9),
          rgba(0, 89, 147, 0.9)
        );
        border-radius: 10px;
        font-size: 30px;
        font-weight: 400;
        color: #accbee;
      }

      .el-tree {
        background-color: transparent;
        color: white;
      }
      .el-tree-node__content:hover {
        background-color: #ffc46050;
        color: #ffc460;
      }
      .el-tree-node:focus > .el-tree-node__content {
        background-color: #ffc46050;
        color: #ffc460;
      }

      .video_box_l2 .el-tree-node__content:hover {
        background-color: #00c0ff50;
        color: #00c0ff;
      }
      .video_box_l2 .el-tree-node:focus > .el-tree-node__content {
        background-color: #00c0ff50;
        color: #00c0ff;
      }

      .el-tree-node__content {
        height: 80px;
      }
      .el-tree-node__expand-icon {
        font-size: 22px;
        position: absolute;
        right: 35px;
      }
      .el-radio__inner {
        background-color: #39516a;
        border: 1px solid #90a2bd;
      }
      .el-radio__input.is-checked .el-radio__inner {
        background: #22e8e8;
        border-color: #22e8e8;
      }
      .el-radio__label {
        display: none;
      }
      .el-input__inner {
        color: #fff;
      }
    </style>
  </body>
</html>
