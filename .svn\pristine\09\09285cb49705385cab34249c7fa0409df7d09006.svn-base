<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>部门接入</title>
    <script src="/Vue/vue.js"></script>
    <link rel="stylesheet" href="./static/css/home_services/bmjr.css" />
    <link rel="stylesheet" href="./static/css/home_services/bmjr-2.css" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
  </head>

  <body>
    <div class="head-top-title">
      <div id="topTitle" onclick="location.href = './dnyx.html'">
        <div class="text_linear_white header_text"></div>
      </div>
    </div>
    <div class="head-title-new">
      <!-- <div id="backMind">返回</div> -->

      <!-- <div class="head-tab" id="headTab">
			<div class="active">公共数据平台</div>
		</div> -->
    </div>
    <div class="ggsjpt-page" style="display: none"></div>
    <div class="container" id="bmjr-main">
      <div class="ggsjpt-bk-color"></div>
      <!-- <div> -->
      <!--左边-->
      <div class="panel-l-1 panel panel-top panel-lef" style="display: none">
        <div class="mainpage_l_b mainpage_panel_l">
          <div class="mainpage_title fs-60 color-0ff fw-b mainpage_panel_l">
            <div class="mainpage_btn" id="left-btn">
              <div class="color-fff">全部</div>
              <img src="./img/bmjr/top-title-more.png" alt="" width="43" height="38" />
            </div>
            <div class="mainpage_title_btns fs-40" id="left-tab">
              <div>全部</div>
              <div>驾驶舱</div>
              <div>场景应用</div>
              <div>数改应用</div>
            </div>
          </div>
          <div class="mainpage_content mainpage_panel_l">
            <div
              class="mainpage_list_box mianpage_list_box_l snaps-box scrollbar"
              id="leftSnaps"
            ></div>
          </div>
        </div>
      </div>

      <!-- 左边最新 -->
      <div class="panel-l-1 panel panel-top panel-lef">
        <div class="mainpage_l_b1 mainpage_panel_l">
          <!-- 数据资源 -->
          <div style="padding-top: 50px">
            <s-header-title-3 title="数据资源" />
          </div>
          <div
            class="bmjr-right-left-top sjzy-css"
            style="padding-top: 40px; box-sizing: border-box"
          >
            <div class="sjzy-tab">
              <div
                @click="changeTab(0)"
                :class="tabIndex==0 ? 'rightTab rightTab-left-act' : 'rightTab'"
              >
                {{tabList[0]}}
              </div>
              <div
                @click="changeTab(1)"
                :class="tabIndex==1 ? 'rightTab rightTab-right-act' : 'rightTab'"
              >
                {{tabList[1]}}
              </div>
            </div>
            <div :class="[tabIndex==0 ? 'sjzy-con-bg1' : 'sjzy-con-bg2','sjzy-con']">
              <div class="list1">
                <div v-for="(item,index) in list1Data" :key="index">
                  <div class="s-font-35 s-c-white">{{item.name}}</div>
                  <span class="s-font-40 s-c-yellow-gradient">{{item.value}}</span>
                  <span class="s-font-32 s-c-yellow-gradient">
                    {{item.unit}}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <!-- 基础设施 -->
          <div>
            <s-header-title-3 title="基础设施" />
          </div>
          <div class="bmjr-right-left-bottom">
            <div class="sjzy-tab">
              <div
                @click="changeTabJcss(0)"
                :class="jcssIndex==0 ? 'rightTab rightTab-left-act' : 'rightTab'"
              >
                {{jcssList[0]}}
              </div>
              <div
                @click="changeTabJcss(1)"
                :class="jcssIndex==1 ? 'rightTab rightTab-right-act' : 'rightTab'"
              >
                {{jcssList[1]}}
              </div>
            </div>
            <div :class="[jcssIndex==0 ? 'jcss-bg1' : 'jcss-bg2','jcss-con']">
              <div class="list2">
                <div v-for="(item,index) in list1DataJcss" :key="index">
                  <div class="s-font-35 s-c-white">{{item.name}}</div>
                  <span class="s-font-40 s-c-yellow-gradient">{{item.value}}</span>
                  <span class="s-font-32 s-c-yellow-gradient">{{item.unit}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!--右边-->
      <div class="panel panel-top panel-right panel-r-1" style="display: none">
        <div class="mainpage_r_b mainpage_panel">
          <div class="mainpage_title fs-60 fw-b color-0ff mainpage_panel">
            <div class="mainpage_btn" id="right-btn">
              <div class="color-fff">全部</div>
              <img src="./img/bmjr/top-title-more.png" alt="" width="43" height="38" />
            </div>
            <div class="mainpage_title_btns fs-40" id="right-tab">
              <div>全部</div>
              <div>驾驶舱</div>
              <div>场景应用</div>
              <div>数改应用</div>
            </div>
          </div>
          <div class="mainpage_content mainpage_panel">
            <div
              class="mainpage_list_box mianpage_list_box_r snaps-box scrollbar"
              id="rightSnaps"
            ></div>
          </div>
        </div>
      </div>

      <!-- 右边最新 -->
      <div class="panel panel-top panel-right panel-r-1">
        <div class="mainpage_r_b1 mainpage_panel">
          <!-- 安全运维 -->
          <div style="padding-top: 50px">
            <s-header-title-3 title="安全运维" />
          </div>
          <div class="bmjr-right-left-top aqyw-con" style="height: 640px; margin-top: 30px">
            <div class="aqyw-left">
              <div class="list3">
                <div v-for="(item,index) in aqywData" :key="index">
                  <span class="s-font-35 s-c-white">{{item.name}}</span>
                  <span class="s-font-40 s-c-yellow-gradient">{{item.value}}</span>
                  <span class="s-font-32 s-c-yellow-gradient">{{item.unit}}</span>
                </div>
              </div>
            </div>
            <div class="aqyw-right">
              <div class="list4" style="position: relative">
                <div style="margin-left: 180px">
                  <span class="s-font-35 s-c-white">告警</span>
                  <span class="s-font-40 s-c-red-gradient">3334</span>
                </div>
                <div style="margin-left: 140px; margin-top: 20px">
                  <span class="s-font-35 s-c-white">通过</span>
                  <span class="s-font-40 color1">3334</span>
                </div>
                <div style="margin-left: 40px; margin-top: 40px">
                  <span class="s-font-35 s-c-white">阻断行为</span>
                  <span class="s-font-40 color0">3334</span>
                </div>
                <div style="margin-left: 40px; margin-top: 80px">
                  <span class="s-font-35 s-c-white">模拟阻断</span>
                  <span class="s-font-40 s-c-yellow-gradient">3334</span>
                </div>
                <div style="position: absolute; right: 140px; top: 50px">
                  <span class="s-font-35 s-c-white">脱敏</span>
                  <span class="s-font-40 color2">3334</span>
                </div>
                <div style="position: absolute; right: 20px; top: 230px">
                  <span class="s-font-35 s-c-white">阻断链接</span>
                  <span class="s-font-40 color3">3334</span>
                </div>
              </div>
            </div>
          </div>
          <!-- 项目审批 -->
          <div style="padding-top: 40px">
            <s-header-title-3 title="项目审批" />
          </div>
          <div class="bmjr-right-left-bottom xmsp-con">
            <div class="list5">
              <div v-for="(item,index) in xmspData" :key="index">
                <div class="s-font-70 s-c-blue-gradient" style="margin-bottom: 80px">
                  {{item.value}}
                </div>
                <span class="s-font-35 s-c-white">{{item.name}}</span>
                <span class="s-font-35 s-c-white" v-if="item.unit!=''">({{item.unit}})</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!--上面-->
      <!-- <div class="mainpage_up">
        <ul class="up-left" id="up-left"></ul>
        <ul class="up-left up-right" id="up-right">

        </ul>
      </div> -->
      <!--下面-->
      <div class="mainpage_down_bg">
        <div class="mainpage_down">
          <div class="down-left">
            <ul class="qy_down flex-around" id="qy_down"></ul>
          </div>
          <!-- <div class="down-mid ">
                        <div class="mid-img"></div>
                    </div> -->
          <div class="down-right" id="down-right">
            <ul class="bm_down flex-around" id="bm_down"></ul>
          </div>
        </div>
      </div>

      <!--<div class="dzp_search ">-->
      <!--<div class="lf search_div">-->
      <!--<input id="search_input" class="search_input color-fff fs-1" type="text" placeholder="请输入关键字">-->
      <!--</div>-->
      <!--<div class="lf search_btn fs-1 color-0ff" id="search_btn">搜索</div>-->
      <!--</div>-->
      <!-- <div class="dzp_b "></div> -->
      <div class="bmjr_dzp" id="bmjr_dzp">
        <div class="zp_dcl"></div>
        <div class="bmjr-top-bg"></div>
        <div class="zp_dcl-circle"></div>
        <div class="bmjr-center-bg"></div>
        <div class="dqdh-content">
          <span class="dqdh-icon1"></span>
          <span class="dqdh-icon2"></span>
          <span class="dqdh-icon3"></span>
        </div>
        <!--<div class="zp_n_b"></div>-->
        <!--<div class="zp_w_b"></div>-->
        <div class="zp_img_w" id="zp_img_w"></div>
        <div class="zp_img_n" id="zp_img_n"></div>
        <!--弹窗-->
        <div class="bmjrTc" id="zlzx_tc">
          <!--弹窗点点-->
          <div class="bmjrTc_circle"></div>
          <div class="bmjrTc_line1"></div>
          <!--弹窗第一个斜线-->
          <div class="bmjrTc_line2"></div>
          <!--弹窗第二个直线-->
          <div class="bmjrTc_line3"></div>
          <!--弹窗第三个直线-->
          <ul class="bmjrTc_line4">
            <!--弹窗第四个直线-->
            <li class="lf"></li>
            <li class="lf"></li>
            <li class="lf"></li>
            <li class="lf"></li>
            <li class="lf"></li>
          </ul>
          <div class="bmjrTc_nr" id="bmjrTc_nr">
            <!--弹窗内容部分-->
            <span class="tc_close" id="tc_close"></span>
            <div class="tc_top">
              <div
                class="panel-title tc_span color-0ff fs-3"
                style="background: none"
                id="tc_title"
              ></div>
            </div>
            <!-- <div class="tc_sj">
              <div class="tc_span fs-2 color-36a5ed lf">
                <span class="lf" id="tc_name1"></span>
                <span class="xt_num color-6ae691 fs-2 lf" id="xt_num">3</span>
              </div>
              <div
                class="tc_span fs-2 color-36a5ed lf gnsj_div no-pointer"
                id="gnsj_div"
              >
                <span class="lf" id="tc_name2"></span>
                <span class="xt_num color-6ae691 fs-2 lf" id="sj_num">5</span>
              </div>
            </div> -->
            <p class="tc_p">
              <span class="lf tc_title fs-2 color-0ff">接入系统</span>
              <span class="lf fs-2 color-0ff">连接状态</span>
            </p>
            <ul class="tc_xt" id="tc_xt"></ul>
          </div>
          <!--弹窗内容-->
        </div>
        <div class="bmjrTcSjl" id="bmjrTcSjl">
          <span class="tc_close" id="bmjrTcSjl_close"></span>
          <ul class="bmjrTcSjl_ul">
            <li class="lf fs-2 color-0ff">
              <span class="bmIcon"></span>
              <span id="bmmc"></span>
            </li>
            <li class="lf tcSjl_input fs-1">
              <input
                id="bmjrTcSjl_input"
                class="fs-1 color-fff"
                type="text"
                placeholder="请输入数据类型..."
              />
            </li>
            <li class="lf tcSjl_search fs-1 color-0ff" id="bmjrTcSjl_search">
              <span></span>
              <span>查找</span>
            </li>
          </ul>
          <ul class="tcSjl_tit fs-2 color-0ff">
            <li class="lf">提供部门</li>
            <li class="lf">数据类</li>
            <li class="lf">接入方式</li>
          </ul>
          <ul class="tcSjl_con fs-2 color-fff tcSjl_con_2" id="bmjrTcSjl_con"></ul>
          <div class="tc_page color-fff fs-2" id="bmSjl_page"></div>
        </div>
        <!--<div class="zlzx_tc" id="zlzx_tc">-->
        <!--<span class="tc_close" id="tc_close"></span>-->
        <!--<div class="tc_top">-->
        <!--<div class="tc_span  color-0ff fs-5 " id="tc_title"></div>-->
        <!--</div>-->
        <!--<div class="tc_sj">-->
        <!--<div class="tc_span fs-3 color-36a5ed lf"><span class="lf" id="tc_name1"></span><span class="xt_num color-6ae691 fs-4 lf" id="xt_num">3</span></div>-->
        <!--<div class="tc_span fs-3 color-36a5ed lf" id="gnsj_div"><span class="lf" id="tc_name2"></span><span class="xt_num color-6ae691 fs-4 lf" id="sj_num">5</span></div>-->
        <!--</div>-->
        <!--<p class="tc_p"><span class="lf tc_title fs-3 color-0ff">接入系统</span>-->
        <!--<span class="lf fs-3 color-0ff">连接状态</span>-->
        <!--</p>-->
        <!--<ul class="tc_xt" id="tc_xt">-->
        <!--<li>-->
        <!--<div class="xt_title color-fff fs-3 lf" >111111</div>-->
        <!--<span class="bg-02e117 bg-cicle lf"></span>-->
        <!--</li>-->
        <!--<li>-->
        <!--<div class="xt_title color-fff fs-3 lf" >111111</div>-->
        <!--<span class="bg-02e117 bg-cicle lf"></span>-->
        <!--</li>-->
        <!--<li>-->
        <!--<div class="xt_title color-fff fs-3 lf" >111111</div>-->
        <!--<span class="bg-02e117 bg-cicle lf"></span>-->
        <!--</li>-->
        <!--<li>-->
        <!--<div class="xt_title color-fff fs-3 lf" >111111</div>-->
        <!--<span class="bg-02e117 bg-cicle lf"></span>-->
        <!--</li>-->
        <!--</ul>-->
        <!--</div>-->
      </div>

      <!--接入数据类弹窗-->
      <!-- <div class="tcSjl " id="tcSjl">
        <span class="tc_close" id="sjl_close"></span>
        <ul class="tcSjl_bmList" id="tcSjl_bmList"></ul>
        <ul class="tcSjl_ul">
            <li class="lf color-0ff fs-2">部门</li>
            <li class="lf tcSjl_bm fs-1 color-0ff" id="sjlSelect"><span id="sjlbm">请选择</span><span></span></li>
            <li class="lf tcSjl_input fs-1">
                <input id="sjlInput" class="fs-1 color-fff" type="text" placeholder="请输入数据类型...">
            </li>
            <li class="lf tcSjl_search fs-1 color-0ff" id="tcSjl_search"><span></span><span>查找</span></li>
        </ul>
        <ul class="tcSjl_tit fs-2 color-0ff">
            <li class="lf">提供部门</li>
            <li class="lf">数据类</li>
            <li class="lf">接入方式</li>
        </ul>
        <ul class="tcSjl_con fs-2 color-fff" id="tcSjl_con">
        </ul>
        <div class="tc_page color-fff fs-2" id="tcSjl_page"></div>
    </div> -->
      <!-- </div> -->
    </div>
    <script src="/jquery/jquery-3.4.1.min.js"></script>
    <script>
      window.onload = function () {
        // $('#topTitle').click(function () {
        //   console.log('2222')
        //   window.location.href = './main.html'
        // })
      };
    </script>
    <script src="./static/js/home_services/bmjr.js"></script>
  </body>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>
    new Vue({
      el: '#bmjr-main',
      data() {
        return {
          list1Data: [
            {
              name: '编目',
              value: 25441,
              unit: '个',
            },
            {
              name: '归集',
              value: 9573,
              unit: '条',
            },
            {
              name: '治理',
              value: 55819.91,
              unit: '万条',
            },
            {
              name: '共享',
              value: 1014,
              unit: '条',
            },
            {
              name: '开放',
              value: 23765.24,
              unit: '万条',
            },
          ],
          list1DataJcss: [
            {
              name: '物理机',
              value: 506,
              unit: '台',
            },
            {
              name: '综合算力',
              value: 23264,
              unit: '核',
            },
            {
              name: '总存储',
              value: 2265176,
              unit: 'GB',
            },
            {
              name: '总内存',
              value: 96256,
              unit: 'GB',
            },
          ],
          bmjrList1: [
            {
              name: '编目',
              value: 25441,
              unit: '个',
            },
            {
              name: '归集',
              value: 9573,
              unit: '条',
            },
            {
              name: '治理',
              value: 55819.91,
              unit: '万条',
            },
            {
              name: '共享',
              value: 1014,
              unit: '条',
            },
            {
              name: '开放',
              value: 23765.24,
              unit: '万条',
            },
          ],
          bmjrList2: [
            {
              name: '算法',
              value: 158,
              unit: '个',
            },
            {
              name: '模型',
              value: 210,
              unit: '个',
            },
            {
              name: '知识',
              value: 265,
              unit: '个',
            },
            {
              name: '智能模块',
              value: 25,
              unit: '个',
            },
            {
              name: '组件',
              value: 358,
              unit: '个',
            },
          ],
          bmjrList3: [
            {
              name: '物理机',
              value: 506,
              unit: '台',
            },
            {
              name: '综合算力',
              value: 23264,
              unit: '核',
            },
            {
              name: '总存储',
              value: 2265176,
              unit: 'GB',
            },
            {
              name: '总内存',
              value: 96256,
              unit: 'GB',
            },
          ],
          bmjrList4: [
            {
              name: '云主机',
              value: 2765,
              unit: '个',
            },
            {
              name: 'VPC',
              value: 58,
              unit: '个',
            },
            {
              name: '云数据库',
              value: 505,
              unit: '个',
            },
            {
              name: 'OSS',
              value: 210,
              unit: '个',
            },
            {
              name: 'SLB',
              value: 1388,
              unit: '个',
            },
          ],
          tabIndex: 0,
          tabList: ['数据接入', '智能要素情况'],
          jcssIndex: 0,
          jcssList: ['政务云平台资源', '政务云云资源'],
          // 安全运维
          aqywData: [
            {
              name: '数据库量',
              value: '1888',
              unit: '个',
            },
            {
              name: '事件总数',
              value: '16.69',
              unit: '万条',
            },
            {
              name: '访问数量',
              value: '1.65',
              unit: '万条',
            },
            {
              name: '风险数量',
              value: '1.68',
              unit: '万条',
            },
          ],
          // 项目审批
          xmspData: [
            {
              name: '项目总数',
              value: '98',
              unit: '个',
            },
            {
              name: '项目评审数',
              value: '58',
              unit: '个',
            },
            {
              name: '项目总金额',
              value: '125',
              unit: '亿元',
            },
            {
              name: '项目核减总金额',
              value: '58',
              unit: '亿元',
            },
            {
              name: '指标名称',
              value: '1258',
              unit: '',
            },
            {
              name: '指标名称',
              value: '1258',
              unit: '',
            },
            {
              name: '指标名称',
              value: '1258',
              unit: '',
            },
            {
              name: '指标名称',
              value: '1258',
              unit: '',
            },
          ],
        };
      },
      mounted() {},
      methods: {
        init1() {},
        changeTab(index) {
          this.tabIndex = index;
          index == 0 ? (this.list1Data = this.bmjrList1) : (this.list1Data = this.bmjrList2);
        },
        changeTabJcss(index) {
          this.jcssIndex = index;
          index == 0
            ? (this.list1DataJcss = this.bmjrList3)
            : (this.list1DataJcss = this.bmjrList4);
        },
      },
    });
  </script>
</html>
