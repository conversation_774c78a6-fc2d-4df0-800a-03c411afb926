<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>公司/机构名称</title>
        <script src="/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain/ggfw/css/ggfw-dialog copy.css" />
        <link rel="stylesheet" href="/static/citybrain/ggfw/css/sdqfw-dialog.css" />
        <link rel="stylesheet" href="/static/citybrain/ggfw/css/sdqfw-dialog1.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <!-- 轮播toolTip -->
    </head>

    <body>
        <div id="app" class="container">
            <div class="head">
                <span>公司/机构名称</span>
                <div class="img" @click="closeDialog"></div>
            </div>
            <div class="content">
                <div style="width: 100%; padding: 15px">
                    <ul class="ulTitle">
                        <li v-for="item in gsjgList">
                            <div>{{item.name}}：{{item.value}}{{item.dw}}</div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </body>
    <script type="module">
        new Vue({
            el: "#app",
            data: {
                gsjgList: [],
            },
            //项目生命周期
            mounted() {
                this.init();
            },
            methods: {
                closeDialog() {
                    let data = JSON.stringify({
                        type: "closeIframe",
                        name: "sdqfw-dialog1",
                    });
                    window.parent.postMessage(data, "*");
                },
                init() {
                    $get("/ggfw/sdqfw/sdqfw-dialog003").then((res) => {
                        this.gsjgList = res;
                    });
                },
            },
        });
    </script>
</html>
