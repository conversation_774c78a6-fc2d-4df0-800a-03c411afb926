<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>交通管理右侧</title>
        <script src="/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain/shgl/css/jtgl-right.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <!-- 轮播toolTip -->
    </head>

    <style>
        .qsajhjzs {
            display: flex;
            justify-content: right;
            margin-right: 40px;
        }

        .qsajhjzs .el-radio-button__inner {
            padding: 0px 10px;
            height: 48px;
            line-height: 48px;
            background-color: hsl(0, 0%, 100%, 0.8);
            font-size: 32px;
        }

        .qsajhjzs .is-active .el-radio-button__inner {
            background-color: #83b8ff;
            border-color: #83b8ff;
            color: #fff;
        }

        /* 下拉 */
        .select {
            display: inline-block;
            width: 280px;
            height: 40px;
            position: relative;
            left: 30px;
            z-index: 999;
        }

        .flow-icon {
            width: 25px;
            position: absolute;
            top: 10px;
            right: 10px;
        }

        .flow-icon1 {
            margin-top: -5px;
            transform: rotateX(180deg);
        }

        .select ul {
            width: 90%;
            height: 240px;
            text-align: center;
            font-size: 24px;
            color: #fefefe;
            overflow-y: auto;
            display: none;
            list-style: none;
            margin: 0 15px;
            padding: 0;
            position: absolute;
        }

        .select > span {
            display: block;
            font-size: 26px;
            color: #fff;
            position: absolute;
            top: -40px;
            left: 65px;
        }

        .ul {
            width: 100%;
            height: 40px;
            text-align: center;
            font-size: 26px;
            color: #fefefe;
            background-color: #132c4e;
            border: 1px solid #359cf8;
            border-radius: 40px;
            margin-top: 25px;
            z-index: 9999;
        }

        .select ul > li {
            width: 100%;
            height: 40px;
            line-height: 40px;
            background-color: #132c4ec2;
            box-sizing: border-box;
            z-index: 9999;
        }

        .select ul > li:hover {
            background-color: #359cf8;
        }

        .ul-active {
            display: block !important;
        }

        .ul-active > li:last-of-type {
            border-radius: 0 0 20px 20px;
            z-index: 9999;
        }

        .select ul::-webkit-scrollbar {
            /*滚动条整体样式*/
            width: 6px;
            /*高宽分别对应横竖滚动条的尺寸*/
            height: 1px;
            /* scrollbar-arrow-color: red; */
        }

        .select ul::-webkit-scrollbar-thumb {
            border-radius: 6px;
            /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
            background: #20aeff;
            height: 10px;
        }
    </style>

    <body>
        <div id="app">
            <div class="main">
                <div class="top-title">
                    <s-header-title title="交通态势" :data-time="nowTime"></s-header-title>
                </div>
                <div class="echarts-show-content">
                    <div class="top-part">
                        <!-- 交通拥堵指数分析 -->
                        <div style="width: 50%">
                            <nav>
                                <s-header-title-2 title="交通拥堵指标分析"></s-header-title-2>
                            </nav>
                            <div class="index-analysis" id="index-analysis"></div>
                        </div>
                        <!-- 道路拥堵TOP3 -->
                        <div class="road">
                            <div class="road-legend">
                                <!-- <div class="legend">道路拥堵TOP3</div> -->
                                <nav>
                                    <s-header-title-2 title="道路拥堵TOP3"></s-header-title-2>
                                </nav>
                                <!-- 速度切换 -->
                                <div class="legend">
                                    <span @click="clickSpeed(0)" :class="currentSpeed===0?'select':''">慢</span>
                                    <span @click="clickSpeed(1)" :class="currentSpeed===1?'select':''">快</span>
                                </div>
                            </div>
                            <div class="road-top">
                                <li id="chart1"></li>
                                <li id="chart2"></li>
                                <li id="chart3"></li>
                            </div>
                        </div>
                    </div>
                    <!-- 历史拥堵道路 -->
                    <div class="midle-part">
                        <div style="display: flex; width: 100%; height: 100%">
                            <div style="flex: 1">
                                <nav>
                                    <s-header-title-2 title="历史拥堵道路" htype="1"></s-header-title-2>
                                </nav>
                                <div class="qsajhjzs">
                                    <el-radio-group v-model="tabPosition" style="margin-bottom: 0px">
                                        <el-radio-button label="年"></el-radio-button>
                                        <el-radio-button label="月"></el-radio-button>
                                        <el-radio-button label="周"></el-radio-button>
                                    </el-radio-group>
                                </div>
                                <div class="history-road" id="history-road"></div>
                            </div>
                            <div style="flex: 1">
                                <nav>
                                    <s-header-title-2 title="历史拥堵道路" htype="1"></s-header-title-2>
                                </nav>
                                <div class="select" @click="showSelct=!showSelct">
                                    <div class="flow-icon" :class="showSelct?'flow-icon1':''">
                                        <img src="/static/citybrain/hjbh/img/rkzt/up.png" alt="" width="25" />
                                    </div>
                                    <div class="ul" style="margin-top: 0">
                                        <div style="cursor: pointer">{{startName}}</div>
                                        <ul :class="[showSelct?'ul-active':'']">
                                            <li
                                                style="cursor: pointer"
                                                v-for="(item,index) in flowList"
                                                @click="change(item)"
                                            >
                                                {{item}}
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="history-road" id="history-road1"></div>
                            </div>
                        </div>
                        <div class="bottom-btn">
                            <div
                                :class="[currentTab===index?'active':'','bottom-btn-item']"
                                v-for="(item,index) in tabList"
                                @click="changeTab(index,item)"
                                :key="index"
                            >
                                {{item}}
                            </div>
                        </div>
                    </div>

                    <div class="bottom-part">
                        <div class="total">{{names}}&nbsp;3980起</div>
                        <!-- 年月日切换 -->

                        <!-- 交通事故分析 -->
                        <div class="box">
                            <nav>
                                <s-header-title-2 :title="typeTitleArr[currentTab]"></s-header-title-2>
                            </nav>
                            <div class="qsajhjzs">
                                <!-- <span @click="clickTime(0)" :class="currentTime===0?'select':''">年</span>
                        <span @click="clickTime(1)" :class="currentTime===1?'select':''">季度</span>
                        <span @click="clickTime(2)" :class="currentTime===2?'select':''">月</span>
                        <span @click="clickTime(3)" :class="currentTime===3?'select':''">周</span> -->
                                <el-radio-group v-model="tabPosition1" style="margin-bottom: 0px">
                                    <el-radio-button label="年"></el-radio-button>
                                    <el-radio-button label="季度"></el-radio-button>
                                    <el-radio-button label="月"></el-radio-button>
                                    <el-radio-button label="周"></el-radio-button>
                                </el-radio-group>
                            </div>
                            <div class="accident-analysis" id="accident-analysis"></div>
                        </div>
                        <div class="box">
                            <!-- 交通事故成因 -->
                            <nav>
                                <s-header-title-2 :title="chartTitleArr[currentTab]"></s-header-title-2>
                            </nav>
                            <div class="accident-origin" id="accident-origin"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script type="module">
            new Vue({
                el: "#app",
                data: {
                    nowTime: "", //当前时间
                    tabList: ["事故", "违法"], //切换tab
                    currentTab: 0, //当前切换下标
                    typeTitleArr: ["交通事故分析", "交通违法分析"], //
                    chartTitleArr: ["交通事故成因", "交通违法类型"], //
                    currentSpeed: 0, //当前切换速度下标
                    currentTime: 0, //当前切换时间下标
                    originData: [], //事故成因列表数据
                    indexData: [], //交通拥堵指标数据
                    accidenteData: [], //交通事故分析
                    topThreeData: [], //事故拥堵top3
                    historyRoadData: [], //历史道路数据
                    tabPosition: "年",
                    tabPosition1: "年",
                    flowList: ["环城西路", "人民西路", "回溪街"], // 历史拥堵道路区域下拉框
                    showSelct: false,
                    startName: "环城西路",
                    names: "交通事故总数",
                },
                watch: {
                    tabPosition(newName, oldName) {
                        if (this.tabPosition == "年") {
                            $api("shgl_jtgl-right_history-road", { type: "年" }).then((res) => {
                                this.historyRoadData = res;
                                this.historychartsShow();
                            });
                        } else if (this.tabPosition == "月") {
                            $api("shgl_jtgl-right_history-road", { type: "月" }).then((res) => {
                                this.historyRoadData = res;
                                this.historychartsShow();
                            });
                        } else {
                            $api("shgl_jtgl-right_history-road", { type: "周" }).then((res) => {
                                this.historyRoadData = res;
                                this.historychartsShow();
                            });
                        }
                    },
                    tabPosition1(newName, oldName) {
                        if (this.tabPosition1 == "年") {
                            $api("shgl_jtgl_accidente-analysis", { type: "年" }).then((res) => {
                                this.accidenteData = res;
                                this.accidentechartsShow(this.names);
                            });
                        } else if (this.tabPosition1 == "季度") {
                            $api("shgl_jtgl_accidente-analysis", { type: "季" }).then((res) => {
                                this.accidenteData = res;
                                this.accidentechartsShow(this.names);
                            });
                        } else if (this.tabPosition1 == "月") {
                            $api("shgl_jtgl_accidente-analysis", { type: "月" }).then((res) => {
                                this.accidenteData = res;
                                this.accidentechartsShow(this.names);
                            });
                        } else {
                            $api("shgl_jtgl_accidente-analysis", { type: "周" }).then((res) => {
                                this.accidenteData = res;
                                this.accidentechartsShow(this.names);
                            });
                        }
                    },
                },
                methods: {
                    //获取当前时间
                    getTime() {
                        var data = new Date();
                        var yesterday = new Date(data.setDate(data.getDate() - 1));
                        this.nowTime =
                            yesterday.getFullYear() +
                            "年" +
                            (yesterday.getMonth() + 1) +
                            "月" +
                            yesterday.getDate() +
                            "日";
                    },
                    //切换tab
                    changeTab(index, item) {
                        this.currentTab = index;
                        console.log(item);
                        if (item === "违法") {
                            $api("shgl_jtgl-right_accident1").then((res) => {
                                this.originData = res;
                                this.originechartsShow();
                            });
                            $api("shgl_jtgl_accidente-analysis1", { type: "年" }).then((res) => {
                                this.accidenteData = res;
                                this.names = "交通违法数";
                                this.accidentechartsShow(this.names);
                            });
                        } else {
                            this.getPieData();
                            this.getBarAccidenteData();
                        }
                    },
                    // 下拉框
                    change(item) {
                        this.startName = item;
                        $api("shgl_csjt_add1", { type: item }).then((res) => {
                            this.Showeee("history-road1", res);
                        });
                    },
                    //切换速度指标
                    clickSpeed(index) {
                        this.currentSpeed = index;
                    },
                    //切换年月日
                    clickTime(index) {
                        this.currentTime = index;
                    },
                    //初始化数据
                    initData() {
                        this.getPieData();
                        this.getBarIndexData();
                        this.getBarAccidenteData();
                        this.getRoadData();
                        this.getBarHistoryData();
                    },
                    //获取交通事故成因数据
                    getPieData() {
                        $api("shgl_jtgl-right_accident").then((res) => {
                            this.originData = res;
                            this.originechartsShow();
                        });
                    },
                    //获取交通指标折现图数据
                    getBarIndexData() {
                        $api("shgl_jtgl-right_index-analysis").then((res) => {
                            this.indexData = res;
                            this.indexechartsShow();
                        });
                    },
                    //获取交通事故分析折现图数据
                    getBarAccidenteData() {
                        $api("shgl_jtgl_accidente-analysis", { type: "年" }).then((res) => {
                            this.names = "交通事故数";
                            this.accidenteData = res;
                            this.accidentechartsShow(this.names);
                        });
                    },
                    //获取指标刻度
                    getRoadData() {
                        $api("shgl_jtgl-right_top").then((res) => {
                            this.topThreeData = res;
                            console.log(this.topThreeData);
                            this.getChart("chart1", this.topThreeData[0].name, this.topThreeData[0].value);
                            this.getChart("chart2", this.topThreeData[1].name, this.topThreeData[1].value);
                            this.getChart("chart3", this.topThreeData[2].name, this.topThreeData[2].value);
                        });
                    },
                    //获取历史拥堵道路折现图数据
                    getBarHistoryData() {
                        $api("shgl_jtgl-right_history-road", { type: "年" }).then((res) => {
                            this.historyRoadData = res;
                            this.historychartsShow();
                        });
                    },
                    //绘制交通拥堵指标分析折现图
                    indexechartsShow() {
                        const myChartsIndex = echarts.init(document.getElementById("index-analysis"));
                        var fontColor = "#30eee9";
                        let x = this.indexData.map((item) => {
                            return item.name;
                        });
                        let y = this.indexData.map((item) => {
                            return item.value;
                        });
                        let option = {
                            // backgroundColor: "#11183c",
                            // title: {
                            //     text: "交通拥堵指标分析",
                            //     x: "left",
                            //     top: "0",
                            //     textStyle: { color: "#fff", fontSize: "40" },
                            // },
                            grid: {
                                left: "5%",
                                right: "10%",
                                top: "10%",
                                bottom: "20%",
                                containLabel: true,
                            },
                            tooltip: {
                                trigger: "item",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                textStyle: {
                                    color: "white",
                                    fontSize: "30",
                                },
                            },
                            legend: {
                                show: true,
                                x: "center",
                                y: "35",
                                itemWidth: 20,
                                itemHeight: 20,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                data: ["拥堵指数"],
                            },
                            xAxis: [
                                {
                                    type: "category",
                                    boundaryGap: false,
                                    axisLabel: {
                                        color: "#fff",
                                        fontSize: "28px",
                                    },
                                    axisLine: {
                                        show: true,
                                        lineStyle: {
                                            color: "#bbb",
                                        },
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#195384",
                                        },
                                    },
                                    data: x,
                                },
                            ],
                            yAxis: [
                                {
                                    type: "value",
                                    name: "",
                                    min: 0,
                                    max: 1000,
                                    axisLabel: {
                                        formatter: "{value}",
                                        textStyle: {
                                            color: "#fff",
                                            fontSize: "28px",
                                        },
                                    },
                                    axisLine: {
                                        lineStyle: {
                                            color: "#fff",
                                        },
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#11366e",
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    name: "拥堵指数",
                                    type: "line",
                                    stack: "总量",
                                    // symbol: "circle",
                                    symbolSize: 10,
                                    itemStyle: {
                                        normal: {
                                            color: "#5087EC",
                                            lineStyle: {
                                                color: "#5087EC",
                                                width: 4,
                                            },
                                        },
                                    },
                                    markPoint: {
                                        itemStyle: {
                                            normal: {
                                                color: "red",
                                                title: {
                                                    text: "历史拥堵道路",
                                                    x: "left",
                                                    top: "0",
                                                    textStyle: { color: "#fff", fontSize: "26" },
                                                },
                                            },
                                        },
                                    },
                                    data: y,
                                },
                            ],
                        };
                        myChartsIndex.setOption(option);
                        // tools.loopShowTooltip(myChartsIndex, option, {
                        //     loopSeries: true,
                        // }); //轮播
                    },
                    //绘制仪表盘
                    getChart(id, text, num) {
                        let myChart1 = echarts.init(document.getElementById(id));
                        const GuageSVG = "/static/citybrain/szzf/img/yshj/gauge-bg.png";
                        let option = {
                            title: {
                                bottom: "15%",
                                left: "center",
                                text: text,
                                textStyle: {
                                    fontSize: 24,
                                    color: "#fff",
                                },
                            },
                            graphic: {
                                elements: [
                                    {
                                        type: "image",
                                        style: {
                                            image: GuageSVG,
                                        },
                                        left: "0%",
                                        bottom: "20%",
                                    },
                                ],
                            },
                            series: [
                                {
                                    name: "刻度",
                                    type: "gauge",
                                    center: ["50%", "65%"],
                                    radius: "90%",
                                    min: 0,
                                    max: 100,
                                    splitNumber: 10, //刻度数量
                                    startAngle: 200,
                                    endAngle: -20,
                                    axisLine: {
                                        show: true,
                                        lineStyle: {
                                            width: 1,
                                            color: [[1, "rgba(0,0,0,0)"]],
                                        },
                                    }, //仪表盘轴线
                                    axisLabel: {
                                        show: true,
                                        color: "#00C0FF",
                                        distance: -30,
                                        fontSize: 20,
                                    }, //刻度标签。
                                    axisTick: {
                                        show: true,
                                        lineStyle: {
                                            color: "#00C0FF",
                                            width: 1,
                                        },
                                        length: -8,
                                    }, //刻度样式
                                    splitLine: {
                                        show: true,
                                        length: -12,
                                        lineStyle: {
                                            color: "#00C0FF",
                                        },
                                    }, //分隔线样式
                                    detail: {
                                        show: false,
                                    },
                                    pointer: {
                                        show: false,
                                    },
                                },
                                // 主图
                                {
                                    type: "gauge",
                                    center: ["50%", "65%"],
                                    startAngle: 200,
                                    endAngle: -20,
                                    min: 0,
                                    max: 100,
                                    splitNumber: 12,
                                    itemStyle: {
                                        normal: {
                                            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                                                {
                                                    offset: 0,
                                                    color: "#3a79ee",
                                                },
                                                {
                                                    offset: 0.5,
                                                    color: "#30aaf5",
                                                },
                                                {
                                                    offset: 1,
                                                    color: "#2ad9fc",
                                                },
                                            ]),
                                        },
                                    },
                                    progress: {
                                        show: true,
                                        roundCap: true,
                                        width: 12,
                                    },
                                    pointer: {
                                        show: false,
                                    },
                                    axisLine: {
                                        roundCap: true,
                                        lineStyle: {
                                            width: 12,
                                        },
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    splitLine: {
                                        show: false,
                                    },
                                    axisLabel: {
                                        show: false,
                                    },
                                    detail: {
                                        show: true,
                                        offsetCenter: [0, 0],
                                        formatter: function (params) {
                                            return params + "\n" + "车速";
                                        },
                                        textStyle: {
                                            fontSize: 30,
                                            color: "#fff",
                                        },
                                    },
                                    data: [
                                        {
                                            value: num,
                                        },
                                    ],
                                },
                            ],
                        };
                        myChart1.setOption(option);
                        // tools.loopShowTooltip(myChart, option, { loopSeries: true });
                        myChart1.getZr().on("mousemove", (param) => {
                            myChart1.getZr().setCursorStyle("default");
                        });
                    },
                    //绘制历史拥堵道路折线图
                    historychartsShow() {
                        const myChartshistory = echarts.init(document.getElementById("history-road"));
                        const chartData = [...this.historyRoadData];
                        chartData.sort((prev, next) => {
                            if (Number(prev.index) < Number(next.index)) {
                                return 1;
                            }
                            if (Number(prev.index) > Number(next.index)) {
                                return -1;
                            }
                            return 0;
                        });
                        let x = chartData.map((item) => {
                            return item.name;
                        });
                        let y = chartData.map((item) => {
                            return item.value;
                        });
                        let index = chartData.map((item) => {
                            return item.index;
                        });
                        let option = {
                            // title: {
                            //     text: "历史拥堵道路",
                            //     x: "left",
                            //     top: "0",
                            //     textStyle: { color: "#fff", fontSize: "40" },
                            // },
                            tooltip: {
                                trigger: "item",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                textStyle: {
                                    color: "white",
                                    fontSize: "30",
                                },
                            },
                            grid: {
                                left: "4%",
                                top: "18%",
                                right: "5%",
                                bottom: "22%",
                            },
                            legend: {
                                orient: "horizontal",
                                // icon: "circle",
                                top: 20,
                                itemGap: 15,
                                textStyle: {
                                    color: "#D6E7F9",
                                    fontSize: 28,
                                },
                            },
                            xAxis: {
                                data: x,
                                offset: 10,
                                axisLine: {
                                    show: true, //隐藏X轴轴线
                                    lineStyle: {
                                        color: "#aaa",
                                        width: 1,
                                    },
                                },
                                axisTick: {
                                    show: true, //隐藏X轴刻度
                                    alignWithLabel: true,
                                },
                                axisLabel: {
                                    show: true,

                                    textStyle: {
                                        color: "#fff", //X轴文字颜色
                                        fontSize: 28,
                                    },
                                    interval: 0,
                                    rotate: 15,
                                },
                            },
                            yAxis: [
                                {
                                    type: "value",
                                    // name: "人数",
                                    nameTextStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            width: 1,
                                            color: "#3d5269",
                                        },
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    axisLine: {
                                        show: false,
                                    },
                                    axisLabel: {
                                        show: true,
                                        textStyle: {
                                            color: "#fff",
                                            fontSize: 28,
                                        },
                                    },
                                },
                                {
                                    type: "value",
                                    name: "单位:次",
                                    nameTextStyle: {
                                        color: "#fff",
                                        fontSize: 22,
                                    },
                                    position: "right",
                                    splitLine: {
                                        show: false,
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    axisLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#fff",
                                            width: 2,
                                        },
                                    },
                                    axisLabel: {
                                        show: true,
                                        formatter: "{value} ", //右侧Y轴文字显示
                                        textStyle: {
                                            color: "#fff",
                                            fontSize: 28,
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    name: "拥堵指数",
                                    type: "bar",
                                    itemStyle: {
                                        normal: {
                                            color: "#5087EC",
                                        },
                                    },
                                    data: index,
                                },

                                {
                                    name: "次数",
                                    type: "line",
                                    yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                                    showAllSymbol: true, //显示所有图形。
                                    // symbol: "circle", //标记的图形为实心圆
                                    symbolSize: 10, //标记的大小
                                    itemStyle: {
                                        normal: {
                                            color: "#26D9FF",
                                            lineStyle: {
                                                color: "#26D9FF",
                                                width: 4,
                                            },
                                        },
                                    },
                                    data: y,
                                },
                            ],
                        };

                        myChartshistory.setOption(option);
                        tools.loopShowTooltip(myChartshistory, option, {
                            loopSeries: true,
                        }); //轮播
                    },
                    //绘制交通事故分析折线图
                    accidentechartsShow(names) {
                        const myChartsAccident = echarts.init(document.getElementById("accident-analysis"));
                        var fontColor = "#30eee9";
                        let x = this.accidenteData.map((item) => {
                            return item.name;
                        });
                        let y = this.accidenteData.map((item) => {
                            return item.value;
                        });
                        let option = {
                            // backgroundColor: "#11183c",
                            // title: {
                            //     text: "交通事故分析",
                            //     x: "left",
                            //     top: "0",
                            //     textStyle: { color: "#fff", fontSize: "40" },
                            // },
                            grid: {
                                left: "5%",
                                right: "10%",
                                top: "20%",
                                bottom: "10%",
                                containLabel: true,
                            },
                            tooltip: {
                                trigger: "item",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                textStyle: {
                                    color: "white",
                                    fontSize: "30",
                                },
                            },
                            legend: {
                                show: true,
                                x: "center",
                                y: "65",
                                itemWidth: 20,
                                itemHeight: 20,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: "28px",
                                },
                            },
                            xAxis: [
                                {
                                    type: "category",
                                    boundaryGap: false,
                                    axisLabel: {
                                        color: "#fff",
                                        fontSize: "28px",
                                    },
                                    axisLine: {
                                        show: true,
                                        lineStyle: {
                                            color: "#bbb",
                                        },
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#195384",
                                        },
                                    },
                                    data: x,
                                },
                            ],
                            yAxis: [
                                {
                                    type: "value",
                                    name: "单位:起",
                                    nameTextStyle: {
                                        color: "#fff",
                                        fontSize: 22,
                                    },
                                    min: 0,
                                    max: 1000,
                                    fontSize: 20,
                                    axisLabel: {
                                        formatter: "{value}",
                                        textStyle: {
                                            color: "#fff",
                                            fontSize: "28px",
                                        },
                                    },
                                    axisLine: {
                                        lineStyle: {
                                            color: "#fff",
                                        },
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#11366e",
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    name: names,
                                    type: "line",
                                    stack: "总量",
                                    // symbol: "circle",
                                    symbolSize: 10,
                                    itemStyle: {
                                        normal: {
                                            color: "#5087EC",
                                            lineStyle: {
                                                color: "#5087EC",
                                                width: 4,
                                            },
                                        },
                                    },
                                    markPoint: {
                                        itemStyle: {
                                            normal: {
                                                color: "red",
                                                title: {
                                                    text: "历史拥堵道路",
                                                    x: "left",
                                                    top: "0",
                                                    textStyle: { color: "#fff", fontSize: "40" },
                                                },
                                            },
                                        },
                                    },
                                    data: y,
                                },
                            ],
                        };
                        myChartsAccident.setOption(option);
                        // tools.loopShowTooltip(myChartsAccident, option, {
                        //     loopSeries: true,
                        // }); //轮播
                    },
                    //绘制交通事故成因饼图
                    originechartsShow() {
                        const myChartsOrigin = echarts.init(document.getElementById("accident-origin"));
                        var fontColor = "#30eee9";
                        let option = {
                            // title: {
                            //     text: "交通事故成因",
                            //     x: "left",
                            //     top: "0",
                            //     textStyle: { color: "#fff", fontSize: "40" },
                            // },
                            grid: {
                                // left: "15%",
                                right: 22,
                                // top: "30%",
                                bottom: "20%",
                                containLabel: true,
                            },
                            tooltip: {
                                trigger: "item",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                textStyle: {
                                    color: "white",
                                    fontSize: "30",
                                },
                            },
                            color: ["#EE752F", "#5087EC", "#68BBC4", "#58A55C", "#F2BD42"],
                            legend: {
                                // orient: "vertical",
                                x: "center",
                                y: "bottom",
                                textStyle: {
                                    color: "#fff",
                                    fontSize: "28px",
                                },
                                // formatter: function (name) {
                                //     var oa = option.series[0].data;
                                //     var num = oa[0].value + oa[1].value + oa[2].value + oa[3].value;
                                //     for (var i = 0; i < option.series[0].data.length; i++) {
                                //         if (name == oa[i].name) {
                                //             return (
                                //                 name +
                                //                 "     " +
                                //                 oa[i].value +
                                //                 "     " +
                                //                 ((oa[i].value / num) * 100).toFixed(2) +
                                //                 "%"
                                //             );
                                //         }
                                //     }
                                // },
                            },
                            series: [
                                {
                                    name: "交通事故成因",
                                    type: "pie",
                                    radius: "65%",
                                    center: ["50%", "45%"],
                                    data: this.originData,
                                    itemStyle: {
                                        color: "#fff",
                                        emphasis: {
                                            shadowBlur: 10,
                                            shadowOffsetX: 0,
                                            shadowColor: "rgba(0, 0, 0, 0.5)",
                                        },
                                    },
                                    itemStyle: {
                                        normal: {
                                            label: {
                                                show: true,
                                                color: "#fff",
                                                fontSize: "28px",
                                                //position:'inside',
                                                formatter: "{b}:\n{d}%\n",
                                            },
                                        },
                                        labelLine: { show: true },
                                    },
                                },
                            ],
                        };

                        myChartsOrigin.setOption(option);
                        tools.loopShowTooltip(myChartsOrigin, option, {
                            loopSeries: true,
                        }); //轮播
                    },

                    openIframe2() {
                        let right = {
                            type: "openIframe",
                            name: "jtgl-doing2",
                            src: baseURL.url + "/static/citybrain/shgl/commont/jtgl-doing2.html",
                            left: "5030px",
                            top: "230px",
                            width: "500px",
                            height: "400px",
                            zIndex: "10",
                            argument: {
                                status: "",
                            },
                        };
                        window.parent.postMessage(JSON.stringify(right), "*");
                    },
                    Showeee(dom, echartData) {
                        const myChartsIndex = echarts.init(document.getElementById(dom));
                        var fontColor = "#30eee9";
                        let x = echartData.map((item) => {
                            return item.name;
                        });
                        let y = echartData.map((item) => {
                            return item.value;
                        });
                        let option = {
                            // backgroundColor: "#11183c",
                            // title: {
                            //     text: "交通拥堵指标分析",
                            //     x: "left",
                            //     top: "0",
                            //     textStyle: { color: "#fff", fontSize: "40" },
                            // },
                            grid: {
                                left: "5%",
                                right: "10%",
                                top: "10%",
                                bottom: "20%",
                                containLabel: true,
                            },
                            tooltip: {
                                trigger: "item",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                textStyle: {
                                    color: "white",
                                    fontSize: "30",
                                },
                            },
                            legend: {
                                show: true,
                                x: "center",
                                y: "35",
                                itemWidth: 20,
                                itemHeight: 20,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                data: ["拥堵次数"],
                            },
                            xAxis: [
                                {
                                    type: "category",
                                    boundaryGap: false,
                                    axisLabel: {
                                        color: "#fff",
                                        fontSize: "28px",
                                    },
                                    axisLine: {
                                        show: true,
                                        lineStyle: {
                                            color: "#bbb",
                                        },
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#195384",
                                        },
                                    },
                                    data: x,
                                },
                            ],
                            yAxis: [
                                {
                                    type: "value",
                                    name: "单位：次",
                                    nameTextStyle: {
                                        fontSize: 30,
                                        color: "#ffffff",
                                    },
                                    axisLabel: {
                                        formatter: "{value}",
                                        textStyle: {
                                            color: "#fff",
                                            fontSize: "28px",
                                        },
                                    },
                                    axisLine: {
                                        lineStyle: {
                                            color: "#fff",
                                        },
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#11366e",
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    name: "拥堵次数",
                                    type: "line",
                                    stack: "总量",
                                    // symbol: "circle",
                                    symbolSize: 10,
                                    itemStyle: {
                                        normal: {
                                            color: "#5087EC",
                                            lineStyle: {
                                                color: "#5087EC",
                                                width: 4,
                                            },
                                        },
                                    },

                                    data: y,
                                },
                            ],
                        };
                        myChartsIndex.setOption(option);
                        tools.loopShowTooltip(myChartsIndex, option, {
                            loopSeries: true,
                        }); //轮播
                    },
                },
                //项目生命周期
                mounted() {
                    this.getTime();
                    this.initData();
                    this.openIframe2();
                    $api("shgl_csjt_add1", { type: "环城西路" }).then((res) => {
                        this.Showeee("history-road1", res);
                    });
                },
            });
        </script>
    </body>
</html>
