<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>首页</title>
    <link rel="stylesheet" href="static/css/common3840.css" />
    <link rel="stylesheet" href="/static/css/animate.css" />

    <!-- <script src="./static/citybrain/jhpro/Vue/vue.js"></script> -->
    <!-- <script src="./static/citybrain/jhpro/echarts/echarts.js"></script> -->

    <style>
        button {
            width: 500px;
            height: 200px;
            font-size: 50px;
        }

        .btn {
            width: 150px;
            height: 150px;
            left:1185px;
            top: 1580px;
            position: absolute;
            background-image: url(./img/btn_click.png);
            background-size: 100% 100%;
            z-index: 9999;
            display: none !important;
            /* z-index: 80; */

        }

        .btn:hover {
            background-image: url(./img/btn_clicked_hover.png);
        }

        .btn-active {
            background-image: url(./img/btn_clicked_hover.png);
        }

        .switch_area {
            position: absolute;
            left:56.5%;
            top: 45px;
            z-index: 998;
        }

        .triangle {
            cursor: pointer;
            position: relative;
            z-index: 998;
        }

        .switch_menu {
            width: 100%;
            margin-left: 10%;
            height: auto;
            /* height: auto; */
            background: linear-gradient(180deg, #0E1A4095, #07487595);
            /* left: 90px; */
            /* top: 0; */
            z-index: 14;
            position: absolute;
        }
        .menu_2{
          /* margin-top: 80px; */
        }
        .menu_3{
          position: relative;
          /* left: 346px; */
        }
        ul.switch_menu li {
            font-size: 40px;
            color: white;
            font-family: FZZhengHeiS-DB-GB;
            font-weight: 400;
            width: 100%;
            height: 76px;
            line-height: 70px;
            text-align: center;
            background: linear-gradient(180deg, #FFFFFF 53%, #94AADB 1.8%, #C3D8EE 50%, #EBF2FF 100%);
            -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        /* ul.switch_menu li:first-of-type {
            border-radius: 16px 16px 0 0;
        }

        ul.switch_menu li:last-of-type {
            border-radius: 0 0 16px 16px;
        } */

        ul.switch_menu li:hover {
            background-image: linear-gradient(180deg, #FFFFFF 0%,#FFC460 50%, #FFECCB 100%);;
            background-blend-mode: normal, normal;
            cursor: pointer;
        }

        .header_time {
            display: flex;
            align-items: center;
        }

        .header_time .week {
            height: 100px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: flex-start;
            /* padding-right: 10px; */
        }

        .header_time .week img {
            margin-top: -15px;
        }
        .headNowtitle{
            /* padding: 0px 20px;
            border-top: 5px solid #1f85c8;
            background-image: linear-gradient(to bottom, rgb(34 89 125), rgb(12 107 172 / 10%)); */
          background-image: url('/static/images/home/<USER>');
          background-size: 100% 100%;
          background-repeat: no-repeat;
        }



        .top-close {
        width: 150px;
        height: 150px;
        cursor: pointer;
        background-image: url('/static/images/common/components/close-1.png');
        background-size: 100%;
      }
      .details-modal-overlay {
          background: rgba(3, 24, 39,0.6);
          box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
          backdrop-filter: blur(4px);
          position: fixed;
          bottom: 0;
          right: 0;
          left: 0;
          top: 0;
          width: 3840px;
          height: 2160px;
          z-index: 0;
        }
        /* 退出弹窗 */
        .exit-system-css{
          position: absolute;
          left: 1120px;
          top: 750px;
          z-index: 10000;
          width: 1440px;
          height: 420px;
          background: #025E9A;
          box-shadow: inset 0px -3px 0px 0px rgba(255,255,255,0.12);
          border-radius: 0px 0px 0px 0px;
          border: 3px solid;
          border-image: linear-gradient(270deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 3 3;
        }
        .exit-top{
          width: 100%;
          height: 132px;
          font-size: 48px;
          color: #fff;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 50px;
          box-sizing: border-box;
        }
        .exit-bottom{
          width: 100%;
          height: calc(100% - 132px);
          font-size: 42px;
          color: #fff;
          border-top: 3px solid #2B76AC;
          padding: 50px;
          box-sizing: border-box;

        }
        .exit-system-text{
          width: 100%;
          color: rgba(255, 255, 255, 0.49);
          margin-bottom: 40px;
        }
        .close-btn{
          width:48px;
          height: 48px;
          background-image: url("/static/images/common/components/close.png");
          background-size: 100%;
          background-position: center;
          background-repeat: no-repeat;
        }
        .close-btn:hover{
          background-image: url("/static/images/common/components/close-hover.png");
        }
        .exit-btns{
          width: 100%;
          display: flex;
          justify-content: flex-end;
        }
        .exit-btns>div{
          width: 222px;
          height: 96px;
          text-align: center;
          line-height: 96px;
          border-radius: 6px;
          border: 3px solid rgba(255,255,255,0.2);
        }
        .exit-btns>div:first-child{
          margin-right: 30px;
        }
        .exit-btn-active{
          background-color: #177EDD;
          border-color: transparent;
        }
        .exit-btn-active:hover{
          background-color: #1785eb;
        }
        #cancel:hover{
          background-color: #016bb1;
        }
    </style>

    <script src="/static/citybrain/csdn/lib/components/core-min.js"></script>
    <script src="/static/citybrain/csdn/lib/components/sha256.js"></script>
    <script src="/static/citybrain/csdn/lib/components/md5.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>

</head>

<body>
    <div class="container">
        <!-- 点击三角形，六大板块切换 -->
        <!-- <div class="switch_area">
            <img class="triangle" src="./img/triangle.png" alt="" />
            <ul class="switch_menu">
            </ul>
        </div> -->
        
        <div class="header">
            <div class="headNowtitle">
                <div style="height: 80px;line-height: 90px;">
                  <i class="text_linear_white" style="margin-right: 60px;">当前位置</i>
                </div>
                <div style="height: 100px;">
                  <span class="lg-yellow nowTitle" style="margin-right: 60px;"></span>
                </div>
              </div>
            <div class="header_time" id="headerTime">
                <!-- <span></span>
                <span class="week"></span> -->
            </div>

            <img src="/static/images/common/header/tuichu.png" 
            style="position: absolute;
            z-index: 99;
            right: 25px;
            width: 40px;
            height: 40px;
            top: 35px;
            cursor: pointer;" onclick="commonObj.logOut()" alt="">

            <div class="header_img">
                
            </div>
            <div class="header_title_text">
              <span class="text_linear_white header_text"  id="headerTitle" onclick="commonObj.openMenu('home')"
                    parentUrl="home">金华城市大脑</span>
            </div>
            <ul class="nav_ul" id="navUl">
              <!-- <li class="nav_li_lib">
                <div class="nav_li">
                  <tag class="tag_jjtj"></tag>
                  <span class="font_white">经济调节</span>
                </div>

                <div>
                    <ul class="switch_menu menu_2">
                      <li>首页</li>
                      <li>
                        城市设施
                        <div class="menu_2_tag jiantou"></div>
                      </li>
                      <li>城市综治</li>
                    </ul>
                 
                    <ul class="switch_menu menu_3">
                      <li>
                        
                      </li>
                    </ul>
                </div>
              </li> -->
            </ul>
            <!-- <ul class="nav_ul_home  " id="navUlLeft">
            </ul>
            <ul class="nav_ul_home " style="left: 4850px;" id="navUlRight" >
            </ul> -->
            <div class="weather" id="weather" style="z-index: 10"></div>
        </div>

        <!-- 图层管理 -->
        <!-- <iframe src="/static/citybrain/tckz/IndexTcglBtn.html" name="indexTcglBtn" width="100px" height="230px" style="position: absolute;z-index: 999;left: 2100px;top: 200px"
                frameborder="0"></iframe> -->
        <!-- 全局搜索 -->
        <!-- <iframe src="/static/citybrain/tckz/IndexVideoBtn.html" name="indexVideoBtn"  width="100px" height="230px" style="position: absolute;z-index: 999;top: 414px;left: 2100px;"
            frameborder="0"></iframe>  -->
        <!-- 搜索视频 -->
        <!-- <iframe src="/static/citybrain/tckz/IndexVideoBtnall.html" name="indexVideoBtnAll"  width="100px" height="230px" style="position: absolute;z-index: 999;top: 630px;left: 2100px;"
            frameborder="0"></iframe>  -->
                <!-- 全局搜索 -->
        <!-- <iframe src="/static/citybrain/csdn/commont/index_name_sou.html" name="souMainBox" width="685px" height="360px" style="position: absolute;z-index: 999;left: 3513px;top: 200px" frameborder="0"></iframe> -->
      <!-- <iframe style="position: absolute;z-index: 996;left: 5200px;top: 200px"
      name="indexMapIcon"
                src="/static/citybrain/csdn/commont/main_mapIcon.html"
                width="415px"
                height="500px"
                frameborder="0"
              ></iframe> 
                 -->

        <!--  <div class="main-page" id="mainPage">
        <iframe class="page_center" src="./static/2Dmap/index.html" frameborder="0"></iframe>
    </div> -->

      <div class="main-page" id="mainPage">
      </div>
        <!-- <div class="page_center" id="map" style="background-color: rgb(66, 225, 249);"></div> -->

        <iframe class="page_center" src="/static/EGS(v1.0.0)/index.html" id="map" name="map" frameborder="0"></iframe>
        
        <div id="main-bg" style="background:url('/static/images/common/bg-main.png') no-repeat 100%; width: 3840px;
            height: 2160px;
            top: 0;
            z-index: 1;
            position: absolute;">
        </div>

        <!-- 模态框 -->
        <div id="modal-overlay" class="details-modal-overlay" > </div>
        <div id="page_middle" class="page_middle" style="position: absolute; z-index: 99"></div>
        <div class="btn" id="btnClick"></div>
        <!-- <div id="page_middle1" class="page_middle1" style="position: absolute; z-index: 99"> -->


    </div>
    </div>

 
    <script src="/static/js/jslib/jquery-3.4.1.min.js"></script>
    <script src="/static/js/jslib/iframeResizer.contentWindow.min.js"></script>
    <script src="/static/js/jslib/common.2.0.3840.js"></script>
    <script src="/static/js/comjs/scale.js"></script>
    <script src="/static/js/jslib/Emiter.js"></script>
    <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
    <script src="/static/js/jslib/listeners.js"></script>
</body>

</html>
