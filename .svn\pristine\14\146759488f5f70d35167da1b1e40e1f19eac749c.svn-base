<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>城市安全运行指标分析-左</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
    <!-- 轮播toolTip -->
    <style>
      * {
        margin: 0;
        padding: 0;
      }
      #app {
        position: relative;
        width: 1050px;
        height: 1930px;
        background: url("/img/left-bg.png") no-repeat;
        background-size: 100% 100%;
        padding: 30px;
        box-sizing: border-box;
        overflow: hidden;
      }
      .header-title2[data-v-4d0d1712] {
        width: 100% !important;
      }
      .main_list {
        width: 420px !important;
        height: 130px !important;
        margin: 10px 30px;
        background: linear-gradient(
          to right,
          rgba(5, 28, 104, 0.527),
          rgba(33, 62, 143, 0.61),
          rgba(37, 95, 160, 0.541)
        );
        text-align: center;
        padding-top: 40px;
      }
      /* 下拉框 */
      .select {
        position: absolute;
        left: 820px;
        top: 420px;
      }
      .el-select {
        width: 150px;
      }
      .el-input__inner {
        height: 50px !important;
        width: 200px !important;
        background-color: #00487f;
        color: #fff;
        font-size: 28px;
      }
      .el-select-dropdown {
        border: 1px solid #2578a6;
        background-color: #032f46d3;
      }
      .el-select-dropdown__item.hover,
      .el-select-dropdown__item:hover {
        background-color: #00487f;
      }
      .el-select-dropdown__item {
        color: #fff;
        background-color: #00487f;
        font-size: 28px;
        height: 50px;
        line-height: 50px;
      }
      .el-select-dropdown__list {
        background-color: #00487f;
      }
      .el-select .el-input .el-select__caret {
        position: relative;
        left: 40px;
        font-size: 28px;
        color: #fff;
      }
      .el-select .el-input__inner {
        /* border-radius: 30px !important; */
      }
      .el-scrollbar {
        width: 200px;
      }
      .el-input.is-disabled .el-input__inner {
        background-color: #2d4a67;
      }
      /* 表格 */
      .el-table {
        font-size: 24px !important;
        color: #fff !important;
        max-height: 252px !important;
        overflow: hidden;
        overflow-y: auto;
        background-color: transparent !important;
      }
      .el-table th,
      .el-table tr {
        text-align: center;
        background-color: #1a457be3 !important;
        border: 0 !important;
      }
      .el-table td,
      .el-table th {
        text-align: center;
        border: 0 !important;
      }
      .el-table--border::after,
      .el-table--group::after,
      .el-table::before {
        background-color: transparent !important;
      }
      .el-table--enable-row-transition .el-table__body td {
        border-top: 1px solid rgba(10, 25, 109, 0.644) !important;
      }
      .el-table tbody tr:hover > td {
        background: #164a83e8 !important;
      }
      .el-table td,
      .el-table th.is-leaf {
        border-bottom: 0 !important;
      }
      .el-table::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 2px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
        /* scrollbar-arrow-color: red; */
      }

      .el-table::-webkit-scrollbar-thumb {
        border-radius: 2px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
        background: #20aeff;
        height: 10px;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="s-flex s-flex-wrap s-row-between">
        <dv-border-box-8
          class="main_list"
          v-for="(item,index) in list"
          :key="index"
        >
          <p class="s-font-30 s-c-white">{{item.name}}</p>
          <p class="s-font-30 s-c-white">
            <count-to
              :start-val="0"
              :end-val="Number(item.value)"
              :duration="3000"
              class="s-c-yellow-gradient s-font-40"
            ></count-to>
            {{item.unit}}
          </p>
        </dv-border-box-8>
      </div>

      <nav>
        <s-header-title title="区域分类统计分析" htype="2"></s-header-title>
      </nav>
      <div class="select">
        <el-select v-model="value" @change="selectMothFun" placeholder="月份">
          <el-option
            v-for="item,index in options"
            :key="index"
            :label="item"
            :value="item"
          >
          </el-option>
        </el-select>
      </div>
      <div id="bar_eh" style="width: 100%; height: 500px"></div>

      <nav class="s-m-b-10">
        <s-header-title-2 title="事件详情" htype="1"></s-header-title-2>
      </nav>
      <div class="info_box">
        <template>
          <el-table :data="infoList" style="width: 100%">
            <el-table-column prop="name" label="事件名称"> </el-table-column>
            <el-table-column prop="type" label="事件分类"> </el-table-column>
            <el-table-column prop="main" label="事件描述"> </el-table-column>
          </el-table>
        </template>
      </div>

      <nav class="s-m-t-30">
        <s-header-title title="安全生产态势月度分析" htype="2"></s-header-title>
      </nav>
      <div id="line_eh" style="width: 100%; height: 410px"></div>
    </div>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
      window.addEventListener("message", async (e) => {
        if (e.data && e.data.type == "pointClick" && e.data.data.data) {
          let data = JSON.parse(e.data.data.data);
          if (data.csaqyx == "运行") {
            let countStr = "";
            for (let i = 0; i < data.obj.main.length; i++) {
              countStr += `
                <div class="item" style="display: flex; font-size: 32px; color: #2299e2; line-height: 70px">
                  <span style="margin-left:30px;white-space: nowrap; ">${data.obj.main[i].key}  :</span>
                  <span style="color: #fff; margin-left:30px;">${data.obj.main[i].value}</span>
                </div>`;
            }
            let str = `<div onclick=" this.style.display = 'none'"
          style="
            width: 800px;
            position: absolute;

            border-radius: 5px;
            background-color: rgba(10, 31, 53, 0.8);
            z-index: 999999;
            -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
            box-shadow: inset 0 0 40px 0 #5ba3fa;
            padding: 24px;
          " >
         <div class="container">${countStr}</div>
        </div>`;

            let objData = {
              funcName: "customPop",
              coordinates: data.obj.pos.split(","),
              closeButton: true,
              html: str,
            };

            top.document
              .getElementById("map")
              .contentWindow.Work.funChange(JSON.stringify(objData));
          }
        }
      });
      let vm = new Vue({
        el: "#app",
        data: {
          list: [],
          value: "",
          options: ["1月","2月","3月"],
          infoList: [],
        },

        created() {
          this.initApi();
          this.initMap();
        },
        mounted() {},
        methods: {
          initMap() {
            top.document.getElementById("map").contentWindow.Work.change3D(9);
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "flyto", //功能名称
                flyData: {
                  center: [119.98478050597587, 29.18613226366889],
                  zoom: 9,
                  pitch: 28,
                  bearing: 0,
                  duration: 4000, //飞行时间（建议加上）
                },
              })
            );
            $get("/textCity.json").then((res) => {
              let textData = [];
              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  funcName: "3Dtext", //功能名称
                  textData: res,
                  textSize: 35,
                })
              );
            });
          },
          initApi() {
            $get("/3840/shgl/csaqyx/csaqyx01.json", { type1: 1 }).then((res) => {
              this.list = res.slice(0, 4);
            });
            $get("/3840/shgl/csaqyx/csaqyx02.json", { month: "一月" }).then((res) => {
              this.getBar("bar_eh", res);
            });
            $get("/3840/shgl/csaqyx/csaqyx03.json", { type1: 3 }).then((res) => {
              this.getLine("line_eh", res);
            });
            // $api("ldst_shgl_csaqyx", { type1: 1 }).then((res) => {
            //   this.list = res.slice(0, 4);
            // });
            // $api("ldst_shgl_csaqyx2", { month: "一月" }).then((res) => {
            //   this.getBar("bar_eh", res);
            // });
            // $api("ldst_shgl_csaqyx", { type1: 3 }).then((res) => {
            //   this.getLine("line_eh", res);
            // });
          },
          selectMothFun(e) {
            $get("", { month: e }).then((res) => {
              this.getBar("bar_eh", res);
            });
            // $api("ldst_shgl_csaqyx2", { month: e }).then((res) => {
            //   this.getBar("bar_eh", res);
            // });
          },
          // 区域分类柱状图
          getBar(id, echartsData) {
            const myChartsRun = echarts.init(document.getElementById(id));
            let xdata = [],
              ydata1 = [],
              ydata2 = [];
            echartsData.map((ele) => {
              xdata.push(ele.name);
              ydata1.push(ele.gfq);
              ydata2.push(ele.dfq);
            });

            var option = {
              tooltip: {
                //提示框组件
                trigger: "axis",
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "24",
                },
              },
              grid: {
                left: "1%",
                right: "2%",
                bottom: "5%",
                top: "12%",
                containLabel: true,
              },
              legend: {
                itemGap: 16,
                itemWidth: 20,
                itemHeight: 20,
                textStyle: {
                  color: "#fff",
                  fontStyle: "normal",
                  fontFamily: "微软雅黑",
                  fontSize: 24,
                },
              },
              xAxis: [
                {
                  type: "category",
                  boundaryGap: true, //坐标轴两边留白
                  data: xdata,
                  axisLabel: {
                    color: "#fff",
                    fontSize: "26px",
                  },
                  axisLine: {
                    show: true,
                    lineStyle: {
                      color: "#bbb",
                    },
                  },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      color: "#195384",
                    },
                  },
                },
              ],
              yAxis: [
                {
                  type: "value",
                  name: "单位：起",
                  splitNumber: 5,
                  nameTextStyle: {
                    color: "#fff",
                    fontSize: 24,
                    padding: [10, 0, 20, 0],
                  },
                  axisLabel: {
                    formatter: "{value}",
                    textStyle: {
                      color: "#fff",
                      fontSize: "24px",
                    },
                  },
                  axisLine: {
                    lineStyle: {
                      color: "#fff",
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      color: "#5087EC",
                    },
                  },
                },
              ],
              series: [
                {
                  name: "高发区",
                  type: "bar",
                  data: ydata1,
                  barWidth: 20,
                  itemStyle: {
                    //图形样式
                    normal: {
                      barBorderRadius: [5, 5, 0, 0],
                      color: new echarts.graphic.LinearGradient(
                        1,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 1,
                            color: "rgba(50, 150, 250, 0.1)",
                          },
                          {
                            offset: 0.5,
                            color: "rgba(50, 150, 250, 0.5)",
                          },
                          {
                            offset: 0,
                            color: "rgba(50, 150, 250, 1)",
                          },
                        ],
                        false
                      ),
                    },
                  },
                },
                {
                  name: "底发区",
                  type: "bar",
                  data: ydata2,
                  barWidth: 20,
                  itemStyle: {
                    //图形样式
                    normal: {
                      barBorderRadius: [5, 5, 0, 0],
                      color: new echarts.graphic.LinearGradient(
                        1,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 1,
                            color: "rgba(62, 208, 178, 0.1)",
                          },
                          {
                            offset: 0.5,
                            color: "rgba(62, 208, 178, 0.5)",
                          },
                          {
                            offset: 0,
                            color: "rgba(62, 208, 178, 1)",
                          },
                        ],
                        false
                      ),
                    },
                  },
                },
              ],
            };

            myChartsRun.setOption(option);
            tools.loopShowTooltip(myChartsRun, option, {
              loopSeries: true,
            }); //轮播
          },
          // 安全生产态势月度分析--折线图
          getLine(id, echartsData) {
            const myChartsRun = echarts.init(document.getElementById(id));

            let color = [
              "#0090FF",
              "#36CE9E",
              "#FFC005",
              "#FF515A",
              "#8B5CFF",
              "#00CA69",
            ];

            let xAxisData = echartsData.map((v) => v.name);
            let yAxisData1 = echartsData.map((v) => v.value);
            let yAxisData2 = echartsData.map((v) => v.value2);
            const hexToRgba = (hex, opacity) => {
              let rgbaColor = "";
              let reg = /^#[\da-f]{6}$/i;
              if (reg.test(hex)) {
                rgbaColor = `rgba(${parseInt(
                  "0x" + hex.slice(1, 3)
                )},${parseInt("0x" + hex.slice(3, 5))},${parseInt(
                  "0x" + hex.slice(5, 7)
                )},${opacity})`;
              }
              return rgbaColor;
            };

            let option = {
              color: color,
              legend: {
                itemGap: 16,
                itemWidth: 20,
                itemHeight: 20,
                textStyle: {
                  color: "#fff",
                  fontStyle: "normal",
                  fontFamily: "微软雅黑",
                  fontSize: 24,
                },
              },
              tooltip: {
                trigger: "axis",
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                formatter: function (params) {
                  let html = "";
                  params.forEach((v) => {
                    html += `<div style="color: #fff;font-size: 20px;line-height: 24px">
                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                  color[v.componentIndex]
                };"></span>
                ${v.seriesName}.${v.name}
                <span style="color:${
                  color[v.componentIndex]
                };font-weight:700;font-size: 24px">${v.value}</span>
                万元`;
                  });

                  return html;
                },
                // extraCssText:
                //   "background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;",
                // axisPointer: {
                //   type: "shadow",
                //   shadowStyle: {
                //     color: "#ffffff",
                //     shadowColor: "rgba(225,225,225,1)",
                //     shadowBlur: 5,
                //   },
                // },
              },
              grid: {
                top: "20%",
                left: "1%",
                right: "1%",
                bottom: "1%",
                containLabel: true,
              },
              xAxis: [
                {
                  type: "category",
                  boundaryGap: true, //坐标轴两边留白
                  data: xAxisData,
                  axisLabel: {
                    color: "#fff",
                    fontSize: "26px",
                  },
                  axisLine: {
                    show: true,
                    lineStyle: {
                      color: "#bbb",
                    },
                  },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      color: "#195384",
                    },
                  },
                },
              ],
              yAxis: [
                {
                  type: "value",
                  splitNumber: 5,
                  name: "单位：起",
                  nameTextStyle: {
                    color: "#fff",
                    fontSize: 24,
                    padding: [10, 0, 20, 0],
                  },
                  axisLabel: {
                    formatter: "{value}",
                    textStyle: {
                      color: "#fff",
                      fontSize: "24px",
                    },
                  },
                  axisLine: {
                    lineStyle: {
                      color: "#fff",
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      color: "#5087EC",
                    },
                  },
                },
              ],
              series: [
                {
                  name: "本年",
                  type: "line",
                  smooth: true,
                  // showSymbol: false,/
                  symbolSize: 12,
                  zlevel: 3,
                  lineStyle: {
                    normal: {
                      color: color[0],
                      shadowBlur: 3,
                      shadowColor: hexToRgba(color[0], 0.5),
                      shadowOffsetY: 8,
                    },
                  },
                  areaStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 0,
                            color: hexToRgba(color[0], 0.3),
                          },
                          {
                            offset: 1,
                            color: hexToRgba(color[0], 0.1),
                          },
                        ],
                        false
                      ),
                      shadowColor: hexToRgba(color[0], 0.1),
                      shadowBlur: 10,
                    },
                  },
                  data: yAxisData1,
                },
                {
                  name: "上年",
                  type: "line",
                  smooth: true,
                  symbolSize: 12,
                  lineStyle: {
                    normal: {
                      color: color[2],
                      shadowBlur: 3,
                      shadowColor: hexToRgba(color[2], 0.5),
                      shadowOffsetY: 8,
                    },
                  },
                  areaStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 0,
                            color: hexToRgba(color[2], 0.3),
                          },
                          {
                            offset: 1,
                            color: hexToRgba(color[2], 0.1),
                          },
                        ],
                        false
                      ),
                      shadowColor: hexToRgba(color[2], 0.1),
                      shadowBlur: 10,
                    },
                  },
                  data: yAxisData2,
                },
              ],
            };

            myChartsRun.setOption(option);
            tools.loopShowTooltip(myChartsRun, option, {
              loopSeries: true,
            }); //轮播
          },
        },
      });
    </script>
  </body>
</html>
