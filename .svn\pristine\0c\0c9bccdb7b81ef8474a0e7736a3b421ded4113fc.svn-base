<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title></title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <script src="/elementui/js/index.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <link rel="stylesheet" href="./css/scjg/scjg-right.css" />
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
  </head>

  <body>
    <div id="scjg-right">
      <div class="content">
        <div class="titleBox">
          <div class="title" style="flex: 1">
            <nav style="padding: 20px 0 20px 0">
              <s-header-title title="各媒介采集情况" htype="2"></s-header-title>
            </nav>
            <div id="barEcharts002" style="height: 300px"></div>
            <nav style="padding: 0px 0 0px 0; position: relative">
              <s-header-title title="12315维权" htype="2"></s-header-title>
              <div
                class="xq-btn"
                @click="clickDetail('12315wq_dialog','960px','1020px','45%')"
              >
                查看详情
              </div>
            </nav>
            <div class="wqBox">
              <div class="wqList" v-for="item in wqList">
                <div>{{item.name}}</div>
                <div class="value1">{{item.value}}{{item.dw}}</div>
              </div>
            </div>
            <nav style="padding: 0px 0 0px 0; position: relative">
              <s-header-title2 title="放心消费情况" htype="2"></s-header-title2>
              <div
                class="xq-btn"
                @click="clickDetail('fxxf_dialog','910px','1020px','45%')"
              >
                查看详情
              </div>
            </nav>
            <div class="fxxf">
              <img class="fxxfImg" src="./img/sy/sxyt_1.png" alt="" />
              <div class="xfqk">
                全市放心消费单位<span class="value value_one"
                  >{{data1.value}}{{data1.dw}}</span
                >
              </div>
            </div>
            <div class="fxxf">
              <img class="fxxfImg" src="./img/sy/sxyt_2.png" alt="" />
              <div class="xfqk">
                无理由退换货承若单位<span class="value value_one"
                  >{{data2.value}}{{data2.dw}}</span
                >
              </div>
            </div>
            <div class="fxxf">
              <img class="fxxfImg" src="./img/sy/sxyt_3.png" alt="" />
              <div class="xfqk">
                放心工厂<span class="value value_one"
                  >{{data3.value}}{{data3.dw}}</span
                >
              </div>
            </div>
            <nav style="padding: 0px 0 0px 0; position: relative">
              <s-header-title title="三小一摊" htype="2"></s-header-title>
              <div
                class="xq-btn"
                @click="clickDetail('31_dialog','1390px','724px','41%')"
              >
                查看详情
              </div>
            </nav>
            <div class="sxytBox">
              <div class="sxytC" v-for="(item ,index) in sxytList">
                <img
                  class="imgSx"
                  :src="`./img/sy/sxyt_${index+1}.png`"
                  alt=""
                />
                <div>
                  <div>{{item.name}}</div>
                  <div class="value">{{item.value}}{{item.dw}}</div>
                </div>
              </div>
            </div>
            <nav style="padding: 0px 0 0px 0">
              <s-header-title
                title="工业企业固废"
                htype="2"
                :click-flag="true"
                @click="open1"
              ></s-header-title>
            </nav>

            <table class="table table-hover">
              <thead>
                <tr>
                  <th v-for="item in thNameOne">{{item}}</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in tableList">
                  <td>{{item.name}}</td>
                  <td>{{item.value}}{{item.dw}}</td>
                  <td>{{item.name1}}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="title">
            <nav style="padding: 20px 0 20px 0; position: relative">
              <s-header-title title="双随机一公开" htype="2"></s-header-title>
              <div
                class="xq-btn"
                @click="clickDetail('ssj_dialog','1400px','980px','40%')"
              >
                查看详情
              </div>
            </nav>
            <div>
              <div class="ssjList">
                <div class="ssjContent" v-for="item in ssjList">
                  <img src="./img/sy/ssj_left.png" alt="" />
                  <div>
                    <div>{{item.name}}</div>
                    <div class="value">{{item.value}}{{item.dw}}</div>
                  </div>
                </div>
              </div>
              <div class="jhzx">
                <dv-border-box-8 style="width: 148px; height: 98px">
                  <div style="margin-left: 55px; padding-top: 10px">
                    <img src="./img/sy/plan.png" alt="" />
                  </div>
                  <div class="text3">计划执行</div>
                </dv-border-box-8>
                <div>
                  <div class="thName">
                    <div v-for="item in thName">{{item}}</div>
                  </div>
                  <div class="thName">
                    <div class="value1">{{jhzxList.value}}</div>
                    <div class="value value2">
                      {{jhzxList.value1}}{{jhzxList.dw}}
                    </div>
                  </div>
                </div>
              </div>
              <div class="jhzx">
                <dv-border-box-8 style="width: 148px; height: 98px">
                  <div style="margin-left: 55px; padding-top: 10px">
                    <img src="./img/sy/plan.png" alt="" />
                  </div>
                  <div class="text3">任务执行</div>
                </dv-border-box-8>
                <div>
                  <div class="thName">
                    <div v-for="item in thName">{{item}}</div>
                  </div>
                  <div class="thName">
                    <div class="value1">{{rwzxList.value}}</div>
                    <div class="value value2">
                      {{rwzxList.value1}}{{rwzxList.dw}}
                    </div>
                  </div>
                </div>
              </div>
              <div class="jhzx">
                <dv-border-box-8 style="width: 148px; height: 98px">
                  <div style="margin-left: 55px; padding-top: 10px">
                    <img src="./img/sy/plan.png" alt="" />
                  </div>
                  <div class="text3">结果公示</div>
                </dv-border-box-8>
                <div>
                  <div class="thName">
                    <div v-for="item in thName">{{item}}</div>
                  </div>
                  <div class="thName">
                    <div class="value1">{{jggsList.value}}</div>
                    <div class="value value2">
                      {{jggsList.value1}}{{jggsList.dw}}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <nav style="padding: 20px 0 0px 0">
              <s-header-title
                title="特种设备监管"
                htype="2"
                :click-flag="true"
                @click="open"
              ></s-header-title>
            </nav>
            <nav style="padding: 0px 0 0px 0">
              <s-header-title2
                title="历年设备数量情况"
                htype="2"
              ></s-header-title2>
            </nav>
            <div id="charts1" style="width: 1000px; height: 432px"></div>
            <div>
              <div class="tab">
                <div
                  v-for="(item ,index) in tabName"
                  :class="{active:isActive===index}"
                  @click="change(index)"
                >
                  {{item}}
                </div>
              </div>
              <div
                class="animated fadeInUp"
                v-if="this.isActive===0"
                id="pie1"
                style="
                  width: 1000px;
                  height: 300px;
                  position: relative;
                  right: 140px;
                "
              ></div>
              <div
                v-if="this.isActive===1"
                id="pie2"
                class="animated fadeInUp"
                style="
                  width: 550px;
                  height: 300px;
                  position: relative;
                  right: 150px;
                "
              ></div>
            </div>
            <nav style="padding: 0px 0 0px 0; position: relative">
              <s-header-title title="营商环境" htype="2"></s-header-title>
              <div
                class="xq-btn"
                @click="clickDetail('table_yshj_dialog','2515px','1810px','32%')"
              >
                查看详情
              </div>
            </nav>
            <div class="yshj">
              <div class="yshjList" v-for="(item ,index) in yshjList">
                <img :src="`./img/sy/sczt_${index+1}.png`" alt="" />
                <div class="yshjContent">
                  <div class="value">{{item.dw1}}{{item.value}}{{item.dw}}</div>
                  <div>{{item.name}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  var vm = new Vue({
    el: "#scjg-right",
    data: {
      ssjList: [],
      thName: ["抽查计划总数", "计划执行率"],
      thNameOne: ["企业名称", "经营能力", "经营方式"],
      jhzxList: [],
      rwzxList: [],
      jggsList: [],
      wqList: [],
      data1: [],
      data2: [],
      data3: [],
      sxytList: [],
      tableList: [],
      ydata3: [],
      tabName: ["区、县、市设备数量", "设备数量类型"],
      isActive: 0,
      yshjList: [],
      barShow1: false,
      barShow: false,
    },
    mounted() {
      this.initFun();
    },
    methods: {
      open1() {
        top.mapUtil.removeAllLayers();
        this.upPopFun();
      },
      // 自定义经济弹窗
      async upPopFun() {
        this.bankuai()
        var textData = await $get("personInTime");
        let maxValueArr = [];
        let maxValue = "";
        textData.map((item) => {
          item.value[0] = Math.ceil(Math.random() * 300);
          item.value[1] = Math.ceil(Math.random() * 300);
          item.value[2] = Math.ceil(Math.random() * 300);
          item.value[3] = Math.ceil(Math.random() * 300);
          item.value[4] = Math.ceil(Math.random() * 300);
          item.value[5] = Math.ceil(Math.random() * 300);
          // 所有值中的最大值
          let a = +item.value[0];
          let b = +item.value[1];
          let c = +item.value[2];
          let d = +item.value[3];
          let e = +item.value[4];
          let f = +item.value[5];
          maxValueArr.push(a);
          maxValueArr.push(b);
          maxValueArr.push(c);
          maxValueArr.push(d);
          maxValueArr.push(e);
          maxValueArr.push(f);
        });
        maxValue = Math.max(...maxValueArr);
        let popArr = [];
        for (let i = 0; i < textData.length; i++) {
          let a1 = parseInt(Number(textData[i].value[0]));
          let a2 = parseInt(Number(textData[i].value[1]));
          let a3 = parseInt(Number(textData[i].value[2]));
          let a4 = parseInt(Number(textData[i].value[3]));
          let a5 = parseInt(Number(textData[i].value[4]));
          let a6 = parseInt(Number(textData[i].value[5]));
          let a1_res = (a1 / maxValue).toFixed(2);
          let a2_res = (a2 / maxValue).toFixed(2);
          let a3_res = (a3 / maxValue).toFixed(2);
          let a4_res = (a4 / maxValue).toFixed(2);
          let a5_res = (a5 / maxValue).toFixed(2);
          let a6_res = (a6 / maxValue).toFixed(2);
          // console.log("最大值===》", a1_res, a2_res, a3_res, a4_res);
          const url = `${baseURL.url}/static/citybrain/hjbh/img/rkzt/rkpc_bg.png`;
          let objData = {
            position: textData[i].pos,
            offset: [170, 20],
            content: `
                              <div style="min-width: 440px;overflow: hidden; height: 460px; position: relative;background: url('${url}') no-repeat;background-size: 100% 100%;cursor: pointer;"
                                      onclick="console.log("111111")">
                                      <p style="position: absolute;
                                          right: 45px;top: 30px;
                                          font-size: 28px;
                                          background-image: linear-gradient(180deg, #f7ad47, #fff, #ffd8a1);
                                          background-clip: text;
                                          -webkit-background-clip: text;
                                          color: transparent;
                                          font-weight: bold;
                                          font-style: italic;
                                          width: 160px;
                                          height: 40px;
                                          line-height: 50px;
                                          text-align: center;">
                                      ${textData[i].name}
                                      </p>
                                      <div style="width: 345px;overflow: hidden;
                                              position: absolute;
                                              top: 79px;
                                              left: 24px;
                                              height: 348px;
                                              padding: 0 20px;
                                              color: #fff;">
                                      <div style="font-size: 20px;height:33px">
                                          <span>建立应用总数：</span>
                                          <span style=" font-size: 20px;
                                                  font-weight: 600;
                                                  background-image: linear-gradient(180deg, #f7ad47, #fff, #ffd8a1);
                                                  background-clip: text;
                                                  -webkit-background-clip: text;
                                                  color: transparent;">
                                              ${
                                                textData[i].value[0] > 100
                                                  ? textData[i].value[0]
                                                  : "★" + textData[i].value[0]
                                              }
                                          </span>
                                      </div>
                                      <div style="width: 100%;
                                              height: 8px;
                                              background-color: #144363;
                                              position: relative;
                                              border-radius: 12px;
                                              margin-bottom: 15px;">
                                          <div style="height: 8px !important;
                                              position: absolute;
                                              top: -1px;
                                              left: 0;
                                              border-radius: 12px;
                                              z-index: 100;background-image: linear-gradient(10deg, #ff4c4c, #ffa1a1);
                                              width:${a1_res * 100}%">
                                          </div>
                                          </div>
                                          <div style="font-size: 20px;height:33px">
                                              <span>产废在线数：</span>
                                              <span style=" font-size: 20px;
                      font-weight: 600;
                      background-image: linear-gradient(180deg, #f7ad47, #fff, #ffd8a1);
                      background-clip: text;
                      -webkit-background-clip: text;
                      color: transparent;">${
                        textData[i].value[1] > 100
                          ? textData[i].value[1]
                          : "★" + textData[i].value[1]
                      }

                      </span>
                                          </div>
                                          <div style="width: 100%;
                      height: 8px;
                      background-color: #144363;
                      position: relative;
                      border-radius: 12px;
                      margin-bottom: 15px;">
                                              <div style="height: 8px !important;
                      position: absolute;
                      top: -1px;
                      left: 0;
                      border-radius: 12px;
                      z-index: 100;background-image: linear-gradient(10deg, #e9a53a, #e7aba3);width: ${
                        a2_res * 100
                      }%"></div>
                                          </div>
                                          <div style="font-size: 20px;height:33px;display: flex;">
                                              <span style="white-space: nowrap;">
                                                  运输在线数：</span>
                                              <span style=" font-size: 20px;white-space: nowrap;
                      font-weight: 600;
                      color:#00C0FF">${
                        textData[i].value[2] > 100
                          ? textData[i].value[2]
                          : "★" + textData[i].value[2]
                      }
                      </span>
                                          </div>
                                          <div style="width: 100%;
                      height: 8px;
                      background-color: #144363;
                      position: relative;
                      border-radius: 12px;
                      margin-bottom: 15px;">
                                              <div style="height: 8px !important;
                      position: absolute;
                      top: -1px;
                      left: 0;
                      border-radius: 12px;
                      z-index: 100; background-image: linear-gradient(10deg, #bb76db, #dcb4f3);width:${
                        a3_res * 100
                      }%"></div>
                                          </div>
                                          <div style="font-size: 20px;height:33px;display: flex;">
                                              <span style="white-space: nowrap;">
                                                  处置在线数：</span>
                                              <span style=" font-size: 20px;white-space: nowrap;
                      font-weight: 600;
                      color:#00C0FF">${
                        textData[i].value[3] > 100
                          ? textData[i].value[3]
                          : "★" + textData[i].value[3]
                      }
                      </span>
                                          </div>
                                          <div style="width: 100%;
                      height: 8px;
                      background-color: #144363;
                      position: relative;
                      border-radius: 12px;
                      margin-bottom: 15px;">
                                              <div style="height: 8px !important;
                      position: absolute;
                      top: -1px;
                      left: 0;
                      border-radius: 12px;
                      z-index: 100; background-image: linear-gradient(10deg, #bb76db, #dcb4f3);width:${
                        a4_res * 100
                      }%"></div>
                                          </div>
                                          <div style="font-size: 20px;height:33px;display: flex;">
                                              <span style="white-space: nowrap;">
                                                  依法查处无照经营企业数：</span>
                                              <span style=" font-size: 20px;white-space: nowrap;
                      font-weight: 600;
                      color:#00C0FF">${
                        textData[i].value[4] > 100
                          ? textData[i].value[4]
                          : "★" + textData[i].value[4]
                      }
                      </span>
                                          </div>
                                          <div style="width: 100%;
                      height: 8px;
                      background-color: #144363;
                      position: relative;
                      border-radius: 12px;
                      margin-bottom: 15px;">
                                              <div style="height: 8px !important;
                      position: absolute;
                      top: -1px;
                      left: 0;
                      border-radius: 12px;
                      z-index: 100; background-image: linear-gradient(10deg, #bb76db, #dcb4f3);width:${
                        a5_res * 100
                      }%"></div>
                                          </div>
                                          <div style="font-size: 20px;height:33px;display: flex;">
                                              <span style="white-space: nowrap;">
                                                  超范围经营行为案件：</span>
                                              <span style=" font-size: 20px;white-space: nowrap;
                      font-weight: 600;
                      color:#00C0FF">${
                        textData[i].value[5] > 100
                          ? textData[i].value[5]
                          : "★" + textData[i].value[5]
                      }
                      </span>
                                          </div>
                                          <div style="width: 100%;
                      height: 8px;
                      background-color: #144363;
                      position: relative;
                      border-radius: 12px;
                      margin-bottom: 15px;">
                                              <div style="height: 8px !important;
                      position: absolute;
                      top: -1px;
                      left: 0;
                      border-radius: 12px;
                      z-index: 100; background-image: linear-gradient(10deg, #bb76db, #dcb4f3);width:${
                        a6_res * 100
                      }%"></div>
                                          </div>


                                      </div>
                                  </div>
                              `,
          };
          popArr.push(objData);
        }
        top.mapUtil.loadPopupLayer({
          layerid: "mapPop",
          data: popArr,
        });
      },

      open(index) {
        top.mapUtil.removeAllLayers();
        this.Histogram();
      },
      bankuai() {
        top.mapUtil.flyTo({
          x: 120.**************,
          y: 26.************,
          z: 326456.**********,
          heading: 350.**************,
          tilt: 38.**************,
        });
        top.mapUtil.loadRegionLayer({
          layerid: "szjjzbfx_bk",
          data: [
            { name: "婺城区", color: [78, 107, 221, 1], height: 2800 },
            { name: "开发区", color: [78, 107, 221, 1], height: 2600 },
            { name: "金东区", color: [46, 81, 221, 1], height: 2400 },
            { name: "兰溪市", color: [78, 107, 221, 1], height: 2200 },
            { name: "浦江县", color: [110, 133, 221, 1], height: 2000 },
            { name: "义乌市", color: [110, 133, 221, 1], height: 1800 },
            { name: "东阳市", color: [78, 107, 221, 1], height: 1600 },
            { name: "磐安县", color: [110, 133, 221, 1], height: 1400 },
            { name: "永康市", color: [46, 81, 221, 1], height: 1200 },
            { name: "武义县", color: [110, 133, 221, 1], height: 1000 },
          ],
        });
      },
      //区划地图柱状图
      Histogram() {
        this.bankuai()
        let data = [
          {
            pos: [119.*************, 29.***************, 0],
            text: "浦江县\n50个",
            color: [255, 255, 255, 1],
            type: "",
          },
          {
            pos: [119.**************, 29.***************, 0],
            text: "兰溪市\n56个",
            color: [255, 255, 255, 1],
            type: "",
          },
          {
            pos: [119.**************, 28.***************, 0],
            text: "婺城区\n40个",
            color: [255, 255, 255, 1],
            type: "",
          },
          {
            pos: [119.**************, 29.**************, 0],
            text: "金义新区\n78个",
            color: [255, 255, 255, 1],
            type: "",
          },
          {
            pos: [120.**************, 29.**************, 0],
            text: "义乌市\n77个",
            color: [255, 255, 255, 1],
            type: "",
          },
          {
            pos: [119.67587615578625, 28.777011171587162, 0],
            text: "武义县\n65个",
            color: [255, 255, 255, 1],
            type: "",
          },
          {
            pos: [120.04986352478184, 28.987053762616034, 0],
            text: "永康市\n24个 ★",
            color: [255, 255, 255, 1],
            type: "",
          },
          {
            pos: [120.34254935807531, 29.305565856200392, 0],
            text: "东阳市\n37个 ★",
            color: [255, 255, 255, 1],
            type: "",
          },
          {
            pos: [120.50638710113705, 29.009431602110855, 0],
            text: "磐安县\n32个 ★",
            color: [255, 255, 255, 1],
            type: "",
          },
        ];
        top.mapUtil.loadTextLayer({
          layerid: "3Dtext",
          data: data,
          style: {
            size: 35,
          },
        });
        top.mapUtil.loadHistogram({
          data: [
            { name: "浦江县", num: 50, unit: "个" },
            { name: "兰溪市", num: 56, unit: "个" },
            { name: "婺城区", num: 40, unit: "个" },
            { name: "金义新区", num: 78, unit: "个" },
            { name: "义乌市", num: 77, unit: "个" },
            { name: "武义县", num: 65, unit: "个" },
            { name: "永康市", num: 24, unit: "个 ★" },
            { name: "东阳市", num: 37, unit: "个 ★" },
            { name: "磐安县", num: 32, unit: "个 ★" },
          ],
        });
      },

      initFun() {
        $api("scjg_scjg-right01").then((res) => {
          this.getEcharts02("barEcharts002", res,'条');
        });
        $api("scjg_scjg-right02").then((res) => {
          this.ssjList = res;
        });
        $api("scjg_scjg-right03").then((res) => {
          this.jhzxList = res[0];
          this.rwzxList = res[1];
          this.jggsList = res[2];
        });
        $api("scjg_scjg-right", { type: "4" }).then((res) => {
          this.wqList = res;
        });
        $api("scjg_scjg-right", { type: "5" }).then((res) => {
          this.data1 = res[0];
          this.data2 = res[1];
          this.data3 = res[2];
        });
        $api("scjg_scjg-right", { type: "6" }).then((res) => {
          this.sxytList = res;
        });
        $api("scjg_scjg-right", { type: "7" }).then((res) => {
          this.tableList = res;
        });
        $api("scjg_scjg-right08").then((res) => {
          this.yshjList = res;
        });
        $api("/scjg/scjg009").then((res) => {
          this.getEcharts03(res);
        });
        $api("/scjg/scjg015", { code: 1 }).then((res) => {
          this.getEcharts04(res);
        });
      },
      clickDetail(url, width, height, left) {
        this.openDiaog(url, width, height, left);
      },
      openDiaog(name, width, height, left) {
        let top = "25%";
        if (name == "table_yshj_dialog") {
          top = "12%";
        }
        let diaog = {
          type: "openIframe",
          name: name,
          src:
            baseURL.url +
            "/static/citybrain/scjg/commont/scjgyzt/" +
            name +
            ".html",
          left: left,
          top: top,
          width: width,
          height: height,
          zIndex: "999",
          argument: {
            status: "",
          },
        };
        window.parent.postMessage(JSON.stringify(diaog), "*");
      },

      getEcharts02(id, echartsData,unit) {
        const myChartsRun = echarts.init(document.getElementById(id));
        let option = {
          legend: {
            textStyle: {
              fontSize: 30,
              color: "#fff",
            },
          },
          tooltip: {
            trigger: "item",
            backgroundColor: "rgba(50,50,50,0.7)",
            formatter: "{b} : {c}"+unit,
            borderColor: "rgba(50,50,50,0.7)",
            textStyle: {
              fontSize: 30,
              color: "#fff",
            },
          },
          xAxis: {
            type: "category",
            data: echartsData.map((item) => item.time),
            offset: 10,
            axisLabel: {
              textStyle: {
                fontSize: 30,
                color: "#fff",
              },
            },
          },
          yAxis: {
            type: "value",

            splitLine: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                fontSize: 30,
                color: "#fff",
              },
            },
          },
          series: [
            {
              name: "APP",
              data: echartsData.map((item) => item.value),
              type: "bar",
            },
            {
              name: "公众号",
              data: echartsData.map((item) => item.value1),
              type: "bar",
            },
            {
              name: "小程序",
              data: echartsData.map((item) => item.value2),
              type: "bar",
            },
          ],
        };
        myChartsRun.setOption(option);
        tools.loopShowTooltip(myChartsRun, option, {
          loopSeries: true,
        }); //轮播
      },
      getEcharts03(res) {
        let charts1 = echarts.init(document.getElementById("charts1"));

        let option = {
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            backgroundColor: "#000000",
            textStyle: {
              color: "white",
              fontSize: "27",
            },
          },
          xAxis: {
            type: "category",
            data: res.map((o) => {
              return o.particular_year;
            }),
            axisLine: {
              lineStyle: {
                color: "#fff",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 30,
              },
            },
          },
          yAxis: {
            type: "value",
            scale: true, //防止数值接近 使折线图呈现一条直线 脱离0值比例
            name: "单位: 台",
            nameTextStyle: {
              fontSize: 27,
              padding: [0, 80, 10, 0],
            },
            axisLine: {
              lineStyle: {
                color: "#fff",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 22,
              },
            },
          },
          legend: {
            left: "30%",
            top: "1%",
            itemGap: 27,
            textStyle: {
              fontSize: 30,
              color: "white",
            },
          },
          series: [
            {
              name: "总数",
              data: res.map((o) => {
                return o.total_number;
              }), //总数,
              type: "line",
              stack: "a", //相同则设置为堆叠图 另一条数据为前一条数据的累加 从而实现不交错
              symbolSize: 7,
              smooth: true,
              showSymbol: false,
              areaStyle: {
                opacity: 0.8,
                color: "#0197f3",
              },
              emphasis: {
                focus: "series",
              },
              symbol: "circle",
              itemStyle: {
                normal: {
                  color: "#0197f3",
                  lineStyle: {
                    color: "#0197f3",
                  },
                },
              },
            },
            {
              name: "在用",
              data: res.map((o) => {
                return o.start_number;
              }), //在用
              type: "line",
              stack: "a",
              smooth: true,
              showSymbol: false,
              areaStyle: {
                opacity: 0.8,
                color: "#2fc9b0",
              },
              emphasis: {
                focus: "series",
              },
              symbolSize: 7,
              symbol: "circle",
              itemStyle: {
                normal: {
                  color: "#2fc9b0",
                  lineStyle: {
                    color: "#2fc9b0",
                  },
                },
              },
            },
            {
              name: "停用",
              data: res.map((o) => {
                return o.stop_number;
              }), //停用
              type: "line",
              stack: "a",
              smooth: true,
              showSymbol: false,
              areaStyle: {
                opacity: 0.8,
                color: "#fdb472",
              },
              emphasis: {
                focus: "series",
              },
              symbolSize: 7,
              symbol: "circle",
              itemStyle: {
                normal: {
                  color: "#fdb472",
                  lineStyle: {
                    color: "#fdb472",
                  },
                },
              },
            },
          ],
        };
        charts1.setOption(option);
        tools.loopShowTooltip(charts1, option, {
          loopSeries: true,
        }); //轮播
      },
      getEcharts04(res) {
        let charts2 = echarts.init(document.getElementById("pie1"));

        let option = {
          color: ["#00c0fe", "#af75cb", "#fe8630", "#fec460", "#298ace"],
          tooltip: {
            trigger: "item",
            borderWidth: 0,
            backgroundColor: "#000000",
            textStyle: {
              color: "white",
              fontSize: "32",
            },
          },
          legend: {
            left: "64%",
            top: "23%",
            orient: "vertical",
            // itemGap: 27,
            textStyle: {
              fontSize: 30,
              color: "white",
            },
          },
          // graphic: [
          //     {
          //         type: 'image',
          //         left: '305',
          //         top: 40,
          //         z: -10,
          //         bounding: 'raw',
          //         style: {
          //             image: 'img/sy/Base2.png',
          //         }
          //     }
          // ],
          series: [
            {
              name: "数量(个)",
              type: "pie",
              barWidth: 20,
              radius: ["60%", "70%"],
              center: ["50%", "50%"],
              avoidLabelOverlap: false,
              itemStyle: {
                borderColor: "rgba(8,27,50,0)",
                borderWidth: 20,
              },
              label: {
                normal: {
                  show: false,
                },
              },
              labelLine: {
                show: false,
              },
              data: res.map((o) => {
                return { name: o.type_name, value: o.type_number };
              }),
            },
          ],
        };
        charts2.setOption(option);
        tools.loopShowTooltip(charts2, option, {
          loopSeries: true,
        }); //轮播
      },
      getEcharts05(res) {
        let charts3 = echarts.init(document.getElementById("pie2"));
        let option = {
          color: ["#00c0fe", "#af75cb", "#fe8630", "#fec460", "#298ace"],
          tooltip: {
            trigger: "item",
            borderWidth: 0,
            backgroundColor: "#000000",
            textStyle: {
              color: "white",
              fontSize: "32",
            },
          },

          legend: {
            left: "70%",
            top: "24%",
            orient: "vertical",
            // itemGap: 27,
            textStyle: {
              fontSize: 30,
              color: "white",
            },
          },
          series: [
            {
              name: "设备数量(个)",
              type: "pie",
              barWidth: 20,
              radius: ["60%", "70%"],
              avoidLabelOverlap: false,
              itemStyle: {
                borderColor: "rgba(8,27,50,0)",
                borderWidth: 20,
              },
              label: {
                normal: {
                  show: false,
                },
              },
              labelLine: {
                show: false,
              },
              data: res.map((o) => {
                return { name: o.type_name, value: o.type_number };
              }),
            },
          ],
        };
        charts3.setOption(option);
        tools.loopShowTooltip(charts3, option, {
          loopSeries: true,
        }); //轮播
      },
      change(index) {
        this.isActive = index;
        if (this.isActive === 0) {
          $api("/scjg/scjg015", { code: 1 }).then((res) => {
            this.getEcharts04(res);
          });
        } else {
          $api("/scjg/scjg015", { code: 2 }).then((res) => {
            this.getEcharts05(res);
          });
        }
      },
    },
  });
</script>
