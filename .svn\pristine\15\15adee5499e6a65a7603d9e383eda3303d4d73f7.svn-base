<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>领域6右侧面板</title>
        <script src="/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain3840/shgl/css/ly6-right.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <!-- 轮播toolTip -->
    </head>

    <body>
        <div id="app" class="container" v-cloak>
            <div class="btn1" v-show="isVisible==false" @click="clickBtn">关闭</div>
            <div v-show="isVisible">
                <nav>
                    <s-header-title-2 htype="1" title="自然资源汇聚展示"></s-header-title-2>
                </nav>
                <div class="title">社会管理问题</div>
                <div class="table table1">
                    <div class="th">
                        <div class="th_td" style="flex: 0.45" v-for="(item,index) in theadList" :key="index">
                            {{item}}
                        </div>
                    </div>
                    <div class="tbody" id="tbody1">
                        <div class="tr" v-for="(item ,i) in tbodyList" :key="i">
                            <div class="tr_td" style="flex: 0.3" :title="item.name">{{item.name}}</div>
                            <div class="tr_td" style="flex: 0.6" :title="item.detail">{{item.detail}}</div>
                            <div class="tr_td" style="flex: 0.15">{{item.state}}</div>
                        </div>
                    </div>
                </div>
                <nav>
                    <s-header-title-2 htype="1" title="住房服务汇聚展示"></s-header-title-2>
                </nav>
                <div style="display: flex">
                    <div class="title1">故障房筹集及分配信息：25万套</div>
                    <div class="title1">物业服务管理信息：30万套</div>
                    <div class="title1">人才安居工程建设：80%</div>
                </div>
                <div style="display: flex">
                    <div class="title1">人才安居资格确认信息：0.9万套</div>
                    <div class="title1">房产交易备案信息：23万套</div>
                </div>
                <div class="title">住房保障</div>
                <div id="zfbz-chart"></div>
                <div class="title">人才安居</div>
                <div id="rcaj-chart"></div>
                <div class="title">交易市场</div>
                <div id="jysc-chart"></div>
            </div>
            <iframe
                v-show="isVisible==false"
                src="/static/citybrain3840/shgl/pages/ly6-right-detail.html"
                frameborder="0"
                width="1020px"
                height="1930px"
            ></iframe>
        </div>
    </body>
    <script type="module">
        new Vue({
            el: "#app",
            data: {
                nowTime: "", //当前时间
                tjList: [],
                theadList: ["事件名称", "详情", "状态"],
                tbodyList: [],
                isVisible: true,
                btnTitle: "详情",
            },
            methods: {
                clickBtn() {
                    this.isVisible = !this.isVisible;
                    if (this.isVisible) {
                        this.btnTitle = "详情";
                    } else {
                        this.btnTitle = "关闭";
                    }
                },
                init() {
                    $api("ldst_shgl_ly6", { type1: 11 }).then((res) => {
                        res.forEach((item) => {
                            item.detail = item.detail.replace('2024', '2025')
                            item.detail = item.detail.replace('6月', '1月')
                            item.detail = item.detail.replace('8月', '2月')
                            item.detail = item.detail.replace('9月', '3月')
                            item.detail = item.detail.replace('10月', '4月')
                        })
                        this.tbodyList = res;
                    });
                    $api("ldst_shgl_ly6", { type1: 12 }).then((res) => {
                        this.BarchartsShow("zfbz-chart", "保障住房", "单位:万套", res);
                    });
                    $api("ldst_shgl_ly6", { type1: 13 }).then((res) => {
                        this.LinechartsShow(res);
                    });
                    $api("ldst_shgl_ly6", { type1: 14 }).then((res) => {
                        this.BarchartsShow("jysc-chart", "新房成交量", "单位:套", res);
                    });
                },
                //绘制柱图
                BarchartsShow(id, name, unit, data) {
                    const myChartsDivine = echarts.init(document.getElementById(id));
                    let x = data.map((item) => {
                        return item.name;
                    });
                    if(name=='新房成交量'){
                         x= ['2024/05','2024/06','2024/07','2024/08','2024/09','2024/10','2024/11','2024/12','2025/01','2025/02','2025/03','2025/04']
                    }
                    let y = data.map((item) => {
                        return item.value;
                    });
                    let option = {
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },

                        grid: {
                            left: "8%",
                            top: "18%",
                            right: "5%",
                            bottom: "20%",
                        },
                        legend: {
                            top: "3%",
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                        },
                        xAxis: {
                            data: x,
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: "#aaa",
                                    width: 1,
                                },
                            },
                            axisTick: {
                                show: true, //隐藏X轴刻度
                                alignWithLabel: true,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff", //X轴文字颜色
                                    fontSize: 28,
                                },
                                interval: 0,
                                rotate: 30,
                            },
                        },
                        yAxis: [
                            {
                                type: "value",
                                name: unit,
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        width: 1,
                                        color: "#3d5269",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: name,
                                type: "bar",
                                barWidth: 70,
                                color: "#5087EC",
                                label: {
                                    show: true,
                                    position: "top",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                                itemStyle: {
                                    normal: {
                                        color: "#5087EC",
                                    },
                                },
                                data: y,
                            },
                        ],
                    };

                    myChartsDivine.setOption(option);
                    tools.loopShowTooltip(myChartsDivine, option, {
                        loopSeries: true,
                    }); //轮播
                },
                //绘制折线图
                LinechartsShow(data) {
                    const myChartsDivine = echarts.init(document.getElementById("rcaj-chart"));
                    // let x = data.map((item) => {
                    //     return item.name;
                    // }); 
                    let x=['2023/12','2024/01','2024/02','2024/03','2024/04','2024/05','2024/06','2024/07','2024/08','2024/09','2024/10','2024/11','2024/12','2025/01','2025/02','2025/03','2025/04']
                    let y = data.map((item) => {
                        return item.value;
                    });
                    let option = {
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },

                        grid: {
                            left: "8%",
                            top: "18%",
                            right: "8%",
                            bottom: "20%",
                        },
                        legend: {
                            data: ["人才房"],
                            top: "3%",
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                        },
                        xAxis: {
                            data: x,
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: "#aaa",
                                    width: 1,
                                },
                            },
                            axisTick: {
                                show: true, //隐藏X轴刻度
                                alignWithLabel: true,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff", //X轴文字颜色
                                    fontSize: 28,
                                },
                                interval: 0,
                                rotate: 30,
                            },
                        },
                        yAxis: [
                            {
                                type: "value",
                                name: "单位:套",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        width: 1,
                                        color: "#3d5269",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "人才房",
                                type: "line",
                                stack: "总量",
                                // symbol: "circle",
                                symbolSize: 10,
                                itemStyle: {
                                    normal: {
                                        color: "#0092f6",
                                        lineStyle: {
                                            color: "#5087EC",
                                            width: 4,
                                        },
                                    },
                                },
                                areaStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "rgba(71,121,213,1)",
                                                },
                                                {
                                                    offset: 1,
                                                    color: "rgba(71,121,213,0)",
                                                },
                                            ],
                                            false
                                        ),
                                    },
                                },
                                data: y,
                            },
                        ],
                    };

                    myChartsDivine.setOption(option);
                    tools.loopShowTooltip(myChartsDivine, option, {
                        loopSeries: true,
                    }); //轮播
                },
            },
            //项目生命周期
            mounted() {
                this.init();
                window.addEventListener("message", (event) => {
                    console.log(event);
                    const data = JSON.parse(event.data);
                    if (data && data.name === "ly6-left-detail-dialog") {
                        this.isVisible = false;
                    }
                });
            },
        });
    </script>
</html>
