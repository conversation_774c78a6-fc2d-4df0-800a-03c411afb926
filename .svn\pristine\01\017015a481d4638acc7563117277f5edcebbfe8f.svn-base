<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>领域2</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <style>
            #ly2 {
                width: 3846px;
                height: 1930px;
                background: url("/img/left-bg.png") no-repeat;
                background-size: 100% 100%;
                overflow: hidden;
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                /* display: flex;
      justify-content: space-evenly;
      flex-direction: column;
      align-items: center; */
            }

            .lbCon {
                width: 1234px;
                height: 919px;
                /* background-image: url("../img/lbbg.png"); */
                background-color: rgb(0, 57, 111, 0.5);
                border: 1px solid #20aeff;
                border-radius: 20px;
                background-size: cover;
                margin: 0 20px;
            }

            .title {
                font-size: 50px;
                margin-top: 50px;
                margin-left: 50px;
                font-family: Arial;
                color: rgba(2, 193, 215, 1);
                font-style: normal;
                letter-spacing: 0px;
                text-decoration: none;
                font-weight: 600;
            }

            /* 轮播样式 */
            .block {
                width: 94%;
                margin-left: 4%;
                margin-top: 20px;
            }

            .title1 {
                font-size: 40px;
                color: #fff;
                margin-top: 20px;
                margin-left: 20px;
                font-family: Arial;
                color: rgba(2, 193, 215, 1);
                font-style: normal;
                letter-spacing: 0px;
                text-decoration: none;
            }

            .el-carousel__container {
                height: 680px;
            }

            .el-carousel__item h3 {
                color: #254089;
                font-size: 14px;
                opacity: 0.75;
                line-height: 150px;
                margin: 0;
            }

            .el-carousel__item {
                background-color: rgb(34, 81, 146, 0.7);
            }

            .el-carousel__item.is-animating {
                -webkit-transition: -webkit-transform 2s ease-in-out;
                transition: -webkit-transform 2s ease-in-out;
                transition: transform 2s ease-in-out;
                transition: transform 2s ease-in-out, -webkit-transform 2s ease-in-out;
            }

            .el-carousel__indicators--outside button {
                opacity: 1;
            }

            .el-carousel__button {
                width: 50px;
                height: 5px;
            }

            .is-active .el-carousel__button {
                background-color: #20aeff;
                width: 50px;
                height: 5px;
            }

            .pmt {
                display: flex;
                align-items: center;
            }

            .pmt > div {
                width: 50%;
            }

            .ldtmsg {
                display: flex;
                align-items: center;
                color: #fff;
                font-size: 26px;
                padding-left: 20px;
                box-sizing: border-box;
            }

            .ldtmsg > div {
                margin-right: 15px;
            }

            .select {
                /* position: absolute; */
                z-index: 2;
                /* top: -15px; */
                margin-top: 10px;
                right: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .el-input__inner {
                font-size: 34px;
                /* width: 500px; */
                height: 50px;
                line-height: 50px;
                color: #fff;
                background-color: #011040b3;
            }

            .el-select-dropdown__item.hover,
            .el-select-dropdown__item:hover {
                background-color: #011040b3;
            }

            .el-input__icon {
                line-height: 48px;
            }

            .el-select-dropdown {
                background-color: #011040b3;
            }

            .el-select-dropdown__item {
                font-size: 30px;
                color: #fff;
            }

            .el-select .el-input .el-select__caret {
                font-size: 30px;
            }
        </style>
    </head>

    <body>
        <div id="ly2">
            <div class="lbCon">
                <div class="title">医疗资源</div>
                <div class="block">
                    <el-carousel ref="carousel0" indicator-position="outside" :interval="5000">
                        <el-carousel-item v-for="(item,i) in data0" :key="i">
                            <div class="title1">{{item[0].name0}}</div>
                            <div :id="'ylzyEcharts'+i" style="height: 660px"></div>
                        </el-carousel-item>
                    </el-carousel>
                    <div class="select">
                        <el-select v-model="value0" placeholder="请选择" @change="setActiveItem0(value0)">
                            <el-option
                                v-for="item in options0"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div class="lbCon">
                <div class="title">医疗服务</div>
                <div class="block">
                    <el-carousel ref="carousel1" indicator-position="outside" :interval="5000">
                        <el-carousel-item v-for="(item,i) in data1" :key="i">
                            <div class="title1">{{item[0].name0}}</div>
                            <div :id="'ylfwEcharts'+i" style="height: 660px"></div>
                        </el-carousel-item>
                    </el-carousel>
                    <div class="select">
                        <el-select v-model="value1" placeholder="请选择" @change="setActiveItem1(value1)">
                            <el-option
                                v-for="item in options1"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div class="lbCon">
                <div class="title">旅游指标</div>
                <div class="block">
                    <el-carousel ref="carousel2" indicator-position="outside" :interval="5000">
                        <el-carousel-item v-for="(item,i) in data2" :key="i">
                            <div class="title1">{{item[0].name0}}</div>
                            <div :id="'lyzbEcharts'+i" style="height: 660px"></div>
                        </el-carousel-item>
                    </el-carousel>
                    <div class="select">
                        <el-select v-model="value2" placeholder="请选择" @change="setActiveItem2(value2)">
                            <el-option
                                v-for="item in options2"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div class="lbCon">
                <div class="title">教育指标分析展示</div>
                <div class="block">
                    <el-carousel ref="carousel3" indicator-position="outside" :interval="5000">
                        <el-carousel-item v-for="(item,i) in data3" :key="i">
                            <div class="title1">{{item[0].name0}}</div>
                            <div :id="'jyzbEcharts'+i" style="height: 660px"></div>
                        </el-carousel-item>
                    </el-carousel>
                    <div class="select">
                        <el-select v-model="value3" placeholder="请选择" @change="setActiveItem3(value3)">
                            <el-option
                                v-for="item in options3"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div class="lbCon">
                <div class="title">人社指标分析展示</div>
                <div class="block">
                    <el-carousel ref="carousel4" indicator-position="outside" :interval="5000">
                        <el-carousel-item v-for="(item,i) in data4" :key="i">
                            <div class="title1">{{item[0].name0}}</div>
                            <div v-show="i!=2" :id="'rszbEcharts'+i" style="height: 660px"></div>
                            <div v-show="i==2" class="pmt">
                                <div :id="'rszbEcharts'+i+'l'" style="height: 660px"></div>
                                <div :id="'rszbEcharts'+i+'r'" style="height: 660px"></div>
                            </div>
                        </el-carousel-item>
                    </el-carousel>
                    <div class="select">
                        <el-select v-model="value4" placeholder="请选择" @change="setActiveItem4(value4)">
                            <el-option
                                v-for="item in options4"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div class="lbCon">
                <div class="title">卫生健康分析展示</div>
                <div class="block">
                    <el-carousel ref="carousel5" indicator-position="outside" :interval="5000">
                        <el-carousel-item v-for="(item,i) in data5" :key="i">
                            <div class="title1">{{item[0].name0}}</div>
                            <div v-show="i==2" class="ldtmsg">
                                <div v-for="(item,i) in data6" :key="i">
                                    {{item.name+"："+item.value+" "+item.value1}}
                                </div>
                            </div>
                            <div :id="'wsjkEcharts'+i" style="height: 660px"></div>
                        </el-carousel-item>
                    </el-carousel>
                    <div class="select">
                        <el-select v-model="value5" placeholder="请选择" @change="setActiveItem5(value5)">
                            <el-option
                                v-for="item in options5"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#ly2",
        data: {
            data0: [],
            data1: [],
            data2: [],
            data3: [],
            data4: [],
            data5: [],
            data6: [],
            options0: [],
            value0: 0,
            options1: [],
            value1: 0,
            options2: [],
            value2: 0,
            options3: [],
            value3: 0,
            options4: [],
            value4: 0,
            options5: [],
            value5: 0,
        },
        created() {},
        mounted() {
            this.init0();
            this.init1();
            this.init2();
            this.init3();
            this.init4();
            this.init5();
        },
        methods: {
            async init0() {
                await $api("ldst_shgl_ly2", { type: 1 }).then((res) => {
                    this.data0.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 2 }).then((res) => {
                    this.data0.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 3 }).then((res) => {
                    this.data0.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 4 }).then((res) => {
                    this.data0.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 5 }).then((res) => {
                    this.data0.push(res);
                    this.data0.forEach((item, i) => {
                        // this.options0.push({label:this.data6[0].name0,value:4})
                        setTimeout(() => {
                            console.log(item[0].name0);
                            this.options0.push({
                                label: item[0].name0.slice(0, item[0].name0.indexOf("数量")) + "统计",
                                value: i,
                            });
                            this.barEcgarts0(
                                item.map((x) => {
                                    return x.name;
                                }),
                                item.map((x) => {
                                    return x.value;
                                }),
                                item.map((x) => {
                                    return x.value1;
                                }),
                                "2021",
                                "2022",
                                "bar",
                                "bar",
                                "ylzyEcharts" + i,
                                "单位：个"
                            );
                        });
                    });
                });
            },
            async init1() {
                // 医疗服务
                await $api("ldst_shgl_ly2", { type: 6 }).then((res) => {
                    this.data1.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 7 }).then((res) => {
                    this.data1.push(res);

                    setTimeout(() => {
                        console.log("adsasdasdasdasd", this.data1);
                        this.data1.forEach((item, i) => {
                            this.options1.push({ label: item[0].name0, value: i });
                            console.log("adsasdasdasdasd", this.options1);
                            this.barEcgarts1(
                                item.map((x) => {
                                    return x.name;
                                }),
                                item.map((x) => {
                                    return x.value;
                                }),
                                item.map((x) => {
                                    return x.value1;
                                }),
                                "2021",
                                "2022",
                                "bar",
                                "bar",
                                "ylfwEcharts" + i
                            );
                        });
                    }, 0);
                });
            },
            async init2() {
                // 旅游指标
                await $api("ldst_shgl_ly2", { type: 8 }).then((res) => {
                    this.data2.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 9 }).then((res) => {
                    this.data2.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 10 }).then((res) => {
                    this.data2.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 11 }).then((res) => {
                    this.data2.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 12 }).then((res) => {
                    this.data2.push(res);

                    setTimeout(() => {
                        this.data2.forEach((item, i) => {
                            console.log("1111111111111111", item);
                            var optionsItem = item[0].name0;
                            if (optionsItem.indexOf("接待人数") != -1) {
                                optionsItem = optionsItem.slice(0, optionsItem.indexOf("接待人数"));
                            }
                            if (optionsItem.indexOf("分析") != -1) {
                                optionsItem = optionsItem.slice(0, optionsItem.indexOf("分析"));
                            }
                            this.options2.push({ label: optionsItem, value: i });
                            if (i == 0) {
                                this.barEcgarts2(
                                    item.map((x) => {
                                        return x.name;
                                    }),
                                    item.map((x) => {
                                        return x.value;
                                    }),
                                    item.map((x) => {
                                        return x.value1;
                                    }),
                                    "销售",
                                    "维护",
                                    "bar",
                                    "bar",
                                    "lyzbEcharts" + i
                                );
                            } else if (i == 1 || i == 2) {
                                this.barEcgarts0(
                                    item.map((x) => {
                                        return x.name;
                                    }),
                                    item.map((x) => {
                                        return x.value;
                                    }),
                                    [],
                                    "",
                                    "",
                                    "bar",
                                    "bar",
                                    "lyzbEcharts" + i,
                                    "单位：人"
                                );
                            } else if (i == 3) {
                                this.barEcgarts7(
                                    item,
                                    [
                                        { text: "门票价格", max: 100 },
                                        { text: "交通便利度", max: 100 },
                                        { text: "景区星级", max: 100 },
                                        { text: "景区类型", max: 100 },
                                        { text: "网络评价等级", max: 100 },
                                    ],
                                    "lyzbEcharts" + i
                                );
                            } else {
                                this.barEcgarts6(item, "lyzbEcharts" + i);
                            }
                        });
                    }, 0);
                });
            },
            async init3() {
                // 教育指标分析展示
                await $api("ldst_shgl_ly2", { type: 13 }).then((res) => {
                    this.data3.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 14 }).then((res) => {
                    this.data3.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 15 }).then((res) => {
                    this.data3.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 16 }).then((res) => {
                    this.data3.push(res);
                    setTimeout(() => {
                        this.data3.forEach((item, i) => {
                            var temp = item;
                            this.options3.push({ label: item[0].name0, value: i });
                            this.barEcgarts3(
                                temp.map((x) => {
                                    return x.name;
                                }),
                                temp.map((x) => {
                                    return x.value;
                                }),
                                temp.map((x) => {
                                    return x.value1;
                                }),
                                i == 0 ? "教育经费投入2021" : "",
                                i == 0 ? "教育经费投入2022" : "",
                                "bar",
                                "line",
                                "jyzbEcharts" + i,
                                i == 1 ? 15 : 0,
                                i <= 1 ? "单位：万元" : "单位：人"
                            );
                        });
                    }, 0);
                });
            },
            async init4() {
                // 人社指标分析展示
                await $api("ldst_shgl_ly2", { type: 17 }).then((res) => {
                    this.data4.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 18 }).then((res) => {
                    this.data4.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 19 }).then((res) => {
                    this.data4.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 20 }).then((res) => {
                    this.data4.push(res);
                    setTimeout(() => {
                        this.data4.forEach((item, i) => {
                            var temp = item;
                            var optionsItem = item[0].name0;
                            if (optionsItem.indexOf("统计") != -1) {
                                optionsItem = optionsItem.slice(0, optionsItem.indexOf("统计"));
                            }
                            this.options4.push({ label: optionsItem, value: i });
                            if (i == 0) {
                                this.barEcgarts4(
                                    temp.map((x) => {
                                        return x.name;
                                    }),
                                    temp.map((x) => {
                                        return x.value;
                                    }),
                                    temp.map((x) => {
                                        return x.value1;
                                    }),
                                    "就业人数",
                                    "创业人数",
                                    "bar",
                                    "bar",
                                    "rszbEcharts" + i
                                );
                            } else if (i == 1) {
                                this.barEcgarts8(
                                    temp.map((x) => {
                                        return x.name;
                                    }),
                                    temp.map((x) => {
                                        return x.value;
                                    }),
                                    temp.map((x) => {
                                        return x.value1;
                                    }),
                                    temp.map((x) => {
                                        return x.value2;
                                    }),
                                    "2021年参保人数",
                                    "2022年参保人数",
                                    "参保率",
                                    "bar",
                                    "bar",
                                    "line",
                                    "rszbEcharts" + i,
                                    i == 1 ? 15 : 0,
                                    i <= 1 ? "单位：万元" : "单位：人"
                                );
                            } else if (i == 2) {
                                this.barEcgarts9(temp, "rszbEcharts" + i + "l");
                                this.barEcgarts9(temp, "rszbEcharts" + i + "r");
                            } else {
                                this.barEcgarts0(
                                    temp.map((x) => {
                                        return x.name;
                                    }),
                                    temp.map((x) => {
                                        return x.value;
                                    }),
                                    [],
                                    "",
                                    "",
                                    "bar",
                                    "bar",
                                    "rszbEcharts" + i,
                                    "单位：人"
                                );
                            }
                        });
                    }, 0);
                });
            },
            async init5() {
                // 卫生健康分析展示
                await $api("ldst_shgl_ly2", { type: 21 }).then((res) => {
                    this.data5.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 22 }).then((res) => {
                    this.data5.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 23 }).then((res) => {
                    this.data5.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 24 }).then((res) => {
                    this.data5.push(res);
                });
                await $api("ldst_shgl_ly2", { type: 25 }).then((res) => {
                    this.data5.push(res);
                    setTimeout(() => {
                        this.data5.forEach((item, i) => {
                            var temp = item;
                            this.options5.push({ label: item[0].name0, value: i });
                            if (i == 0) {
                                this.barEcgarts5(
                                    temp.map((x) => {
                                        return x.name;
                                    }),
                                    temp.map((x) => {
                                        return x.value;
                                    }),
                                    temp.map((x) => {
                                        return x.value1;
                                    }),
                                    "男性健康比例",
                                    "女性健康比例",
                                    "bar",
                                    "bar",
                                    "wsjkEcharts" + i
                                );
                            } else if (i == 1) {
                                this.barEcgarts0(
                                    temp.map((x) => {
                                        return x.name;
                                    }),
                                    temp.map((x) => {
                                        return x.value;
                                    }),
                                    [],
                                    "",
                                    "",
                                    "bar",
                                    "bar",
                                    "wsjkEcharts" + i,
                                    "单位：间"
                                );
                            } else if (i == 2) {
                                this.data6 = temp.filter((a, i) => {
                                    return i > 4;
                                });
                                this.barEcgarts7(
                                    temp.filter((a, i) => {
                                        return i <= 4;
                                    }),
                                    [
                                        { text: "妇幼保健机构新增", max: 100 },
                                        { text: "医务人员薪酬增加", max: 100 },
                                        { text: "药物零售指导价格落实度", max: 100 },
                                        { text: "人均预期寿命", max: 100 },
                                        { text: "个人卫生支出占比下降", max: 100 },
                                    ],
                                    "wsjkEcharts" + i
                                );
                            } else if (i == 3) {
                                this.barEcgarts0(
                                    temp.map((x) => {
                                        return x.name;
                                    }),
                                    temp.map((x) => {
                                        return x.value;
                                    }),
                                    [],
                                    "",
                                    "",
                                    "bar",
                                    "bar",
                                    "wsjkEcharts" + i,
                                    "单位：万人"
                                );
                            } else if (i == 4) {
                                this.barEcgarts0(
                                    temp.map((x) => {
                                        return x.name;
                                    }),
                                    temp.map((x) => {
                                        return x.value;
                                    }),
                                    temp.map((x) => {
                                        return x.value1;
                                    }),
                                    "中成药销售额",
                                    "同比增速",
                                    "bar",
                                    "line",
                                    "wsjkEcharts" + i,
                                    "单位：万元"
                                );
                            }
                        });
                    }, 0);
                });
                // axios.get("/static/data/3840/xsqstjc/ly2/ly2006.json").then((res) => {
                //   this.data5 = res.data.data
                //  })
            },
            setActiveItem0(value) {
                this.$refs.carousel0.setActiveItem(value);
            },
            setActiveItem1(value) {
                this.$refs.carousel1.setActiveItem(value);
            },
            setActiveItem2(value) {
                this.$refs.carousel2.setActiveItem(value);
            },
            setActiveItem3(value) {
                this.$refs.carousel3.setActiveItem(value);
            },
            setActiveItem4(value) {
                this.$refs.carousel4.setActiveItem(value);
            },
            setActiveItem5(value) {
                this.$refs.carousel5.setActiveItem(value);
            },
            barEcgarts0(xdata, ydata1, ydata2, name1, name2, type1, type2, dom, dw) {
                let myChart = echarts.init(document.getElementById(dom));
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                        // formatter: function (params) {
                        //     console.log(params);
                        //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
                        //     for (var i = 0; i < params.length; i++) {
                        //       if (params[i].data != undefined) {
                        //         res +=
                        //           "<p>" +
                        //           params[i].marker +
                        //           params[i].seriesName+" "+
                        //           params[i].data +
                        //           "万件" +
                        //           "</p>";
                        //       }
                        //     }
                        //     return res;
                        //   },
                    },
                    legend: {
                        orient: "horizontal",
                        // icon: "circle",
                        top: 40,
                        itemGap: 45,
                        textStyle: {
                            color: "#D6E7F9",
                            fontSize: 36,
                        },
                    },
                    grid: {
                        left: "5%",
                        right: "5%",
                        top: "20%",
                        bottom: "10%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: "category",
                            data: xdata,
                            axisLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)", // 颜色
                                    width: 1, // 粗细
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                // rotate: 30,
                                textStyle: {
                                    color: "#D6E7F9",
                                    fontSize: 36,
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: dw,
                            type: "value",
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                        {
                            name: "",
                            type: "value",
                            max: 10,
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                formatter: "{value}%",
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: name1,
                            type: type1,
                            barWidth: "20%",
                            // smooth: true,
                            yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#00C0FF",
                                        },
                                        {
                                            offset: 1,
                                            color: "rgba(0,192,255,0)",
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata1,
                            areaStyle: {
                                //填充
                                color: "#00C0FF",
                                opacity: 1,
                            },
                        },
                        {
                            name: name2,
                            type: type2,
                            barWidth: "20%",
                            // smooth: true,
                            yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#2DF09F",
                                        },
                                        {
                                            offset: 1,
                                            color: "rgba(0,192,255,0)",
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata2,
                            // areaStyle:"",
                        },
                    ],
                };
                if (ydata2.length <= 0) {
                    delete option.series[1];
                }
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },
            barEcgarts1(xdata, ydata1, ydata2, name1, name2, type1, type2, dom) {
                let myChart = echarts.init(document.getElementById(dom));
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                        // formatter: function (params) {
                        //     console.log(params);
                        //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
                        //     for (var i = 0; i < params.length; i++) {
                        //       if (params[i].data != undefined) {
                        //         res +=
                        //           "<p>" +
                        //           params[i].marker +
                        //           params[i].seriesName+" "+
                        //           params[i].data +
                        //           "万件" +
                        //           "</p>";
                        //       }
                        //     }
                        //     return res;
                        //   },
                    },
                    legend: {
                        orient: "horizontal",
                        // icon: "circle",
                        top: 40,
                        itemGap: 45,
                        textStyle: {
                            color: "#D6E7F9",
                            fontSize: 36,
                        },
                    },
                    grid: {
                        left: "5%",
                        right: "5%",
                        top: "20%",
                        bottom: "10%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: "category",
                            data: xdata,
                            axisLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)", // 颜色
                                    width: 1, // 粗细
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                // rotate: 30,
                                textStyle: {
                                    color: "#D6E7F9",
                                    fontSize: 36,
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: "单位：个",
                            type: "value",
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                        {
                            name: "",
                            type: "value",
                            max: 10,
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                formatter: "{value}%",
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: name1,
                            type: type1,
                            barWidth: "20%",
                            // smooth: true,
                            yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#00C0FF",
                                        },
                                        {
                                            offset: 1,
                                            color: "rgba(0,192,255,0)",
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata1,
                            areaStyle: {
                                //填充
                                color: "#00C0FF",
                                opacity: 1,
                            },
                        },
                        {
                            name: name2,
                            type: type2,
                            barWidth: "20%",
                            // smooth: true,
                            yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#2DF09F",
                                        },
                                        {
                                            offset: 1,
                                            color: "rgba(0,192,255,0)",
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata2,
                            // areaStyle:"",
                        },
                    ],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },
            barEcgarts2(xdata, ydata1, ydata2, name1, name2, type1, type2, dom) {
                let myChart = echarts.init(document.getElementById(dom));
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                        // formatter: function (params) {
                        //     console.log(params);
                        //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
                        //     for (var i = 0; i < params.length; i++) {
                        //       if (params[i].data != undefined) {
                        //         res +=
                        //           "<p>" +
                        //           params[i].marker +
                        //           params[i].seriesName+" "+
                        //           params[i].data +
                        //           "万件" +
                        //           "</p>";
                        //       }
                        //     }
                        //     return res;
                        //   },
                    },
                    legend: {
                        orient: "horizontal",
                        // icon: "circle",
                        top: 10,
                        itemGap: 45,
                        textStyle: {
                            color: "#D6E7F9",
                            fontSize: 36,
                        },
                    },
                    grid: {
                        left: "5%",
                        right: "18%",
                        top: "10%",
                        bottom: "10%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            name: "   单位：万",
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 12,
                            },
                            type: "value",
                            // data: xdata,
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 10,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            type: "category",
                            data: xdata,
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                        {
                            name: "",
                            type: "category",
                            max: 10,
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                formatter: "{value}%",
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: name1,
                            type: type1,
                            barWidth: "40%",
                            stack: "total",
                            // smooth: true,
                            // yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: "#00C0FF",
                                    barBorderRadius: 0,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata1,
                            areaStyle: {
                                //填充
                                color: "#00C0FF",
                                opacity: 1,
                            },
                        },
                        {
                            name: name2,
                            type: type2,
                            barWidth: "40%",
                            stack: "total",
                            // smooth: true,
                            // yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: "#2DF09F",
                                    barBorderRadius: 0,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata2,
                            // areaStyle:"",
                        },
                    ],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },
            barEcgarts3(xdata, ydata1, ydata2, name1, name2, type1, type2, dom, rotate, dw) {
                let myChart = echarts.init(document.getElementById(dom));
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                        // formatter: function (params) {
                        //     console.log(params);
                        //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
                        //     for (var i = 0; i < params.length; i++) {
                        //       if (params[i].data != undefined) {
                        //         res +=
                        //           "<p>" +
                        //           params[i].marker +
                        //           params[i].seriesName+" "+
                        //           params[i].data +
                        //           "万件" +
                        //           "</p>";
                        //       }
                        //     }
                        //     return res;
                        //   },
                    },
                    legend: {
                        orient: "horizontal",
                        // icon: "circle",
                        top: 40,
                        itemGap: 45,
                        textStyle: {
                            color: "#D6E7F9",
                            fontSize: 36,
                        },
                    },
                    grid: {
                        left: "5%",
                        right: "5%",
                        top: "20%",
                        bottom: "10%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: "category",
                            data: xdata,
                            axisLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)", // 颜色
                                    width: 1, // 粗细
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                rotate: rotate | 0,
                                textStyle: {
                                    color: "#D6E7F9",
                                    fontSize: 36,
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: dw,
                            type: "value",
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                        {
                            name: "",
                            type: "value",
                            max: 10,
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                formatter: "{value}%",
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: name1,
                            type: type1,
                            barWidth: "20%",
                            // smooth: true,
                            yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#00C0FF",
                                        },
                                        {
                                            offset: 1,
                                            color: "rgba(0,192,255,0)",
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata1,
                            areaStyle: {
                                //填充
                                color: "#00C0FF",
                                opacity: 1,
                            },
                        },
                        {
                            name: name2,
                            type: type2,
                            barWidth: "20%",
                            smooth: true,
                            yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color:
                                        type2 == "bar"
                                            ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                                  {
                                                      offset: 0,
                                                      color: "#2DF09F",
                                                  },
                                                  {
                                                      offset: 1,
                                                      color: "rgba(0,192,255,0)",
                                                  },
                                              ])
                                            : "#2DF09F",
                                    barBorderRadius: 4,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata2,
                            // areaStyle:"",
                        },
                    ],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },
            barEcgarts4(xdata, ydata1, ydata2, name1, name2, type1, type2, dom) {
                let myChart = echarts.init(document.getElementById(dom));
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                        // formatter: function (params) {
                        //     console.log(params);
                        //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
                        //     for (var i = 0; i < params.length; i++) {
                        //       if (params[i].data != undefined) {
                        //         res +=
                        //           "<p>" +
                        //           params[i].marker +
                        //           params[i].seriesName+" "+
                        //           params[i].data +
                        //           "万件" +
                        //           "</p>";
                        //       }
                        //     }
                        //     return res;
                        //   },
                    },
                    legend: {
                        orient: "horizontal",
                        // icon: "circle",
                        top: 10,
                        itemGap: 45,
                        textStyle: {
                            color: "#D6E7F9",
                            fontSize: 36,
                        },
                    },
                    grid: {
                        left: "5%",
                        right: "18%",
                        top: "10%",
                        bottom: "10%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            name: "   单位：人",
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 12,
                            },
                            type: "value",
                            // data: xdata,
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 10,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            type: "category",
                            data: xdata,
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                        {
                            name: "",
                            type: "category",
                            max: 10,
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                formatter: "{value}%",
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: name1,
                            type: type1,
                            barWidth: "40%",
                            stack: "total",
                            // smooth: true,
                            // yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: "#00C0FF",
                                    barBorderRadius: 0,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata1,
                            areaStyle: {
                                //填充
                                color: "#00C0FF",
                                opacity: 1,
                            },
                        },
                        {
                            name: name2,
                            type: type2,
                            barWidth: "40%",
                            stack: "total",
                            // smooth: true,
                            // yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: "#2DF09F",
                                    barBorderRadius: 0,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata2,
                            // areaStyle:"",
                        },
                    ],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },
            barEcgarts5(xdata, ydata1, ydata2, name1, name2, type1, type2, dom) {
                let myChart = echarts.init(document.getElementById(dom));
                let option = {
                    color: ["#D6E7F9", "#2DF09F"],
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        formatter: (paramss) => {
                            var htmlStr = "";
                            var seriesName = paramss[0].axisValueLabel; //图例名称
                            htmlStr += seriesName + "<br/>";
                            paramss.forEach((params, i) => {
                                // var params = paramss[0]
                                var color = params.color; //图例颜色

                                var xName = params.seriesName; //x轴的名称
                                var value = Math.abs(params.value); //y轴值
                                // var htmlStr = '<div>';

                                htmlStr +=
                                    '<span style="margin-right: 5px; font-size: 16pt; font-family: Consolas;display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color:' +
                                    color +
                                    ' ;"></span>';
                                htmlStr += "<span style='min-height: 20pt; font-size: 20pt'>";
                                htmlStr += xName + " " + value + "%";
                                htmlStr += "</span>";
                                htmlStr += "<span style='min-height: 20pt; font-size: 20pt; margin-left: 20px'>";
                                // console.log(params.data.length);
                                // if (!value) {
                                //   htmlStr += value + '%';
                                // } else {
                                //   htmlStr += value[params.seriesIndex + 1] + '万元';//选择对应value的坐标
                                // }
                                htmlStr += "</span>";
                                htmlStr += "</div>";
                            });
                            return htmlStr;
                        },

                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                        // formatter: function (params) {
                        //     console.log(params);
                        //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
                        //     for (var i = 0; i < params.length; i++) {
                        //       if (params[i].data != undefined) {
                        //         res +=
                        //           "<p>" +
                        //           params[i].marker +
                        //           params[i].seriesName+" "+
                        //           params[i].data +
                        //           "万件" +
                        //           "</p>";
                        //       }
                        //     }
                        //     return res;
                        //   },
                    },
                    legend: {
                        orient: "horizontal",
                        // icon: "circle",
                        top: 10,
                        itemGap: 45,
                        textStyle: {
                            color: "#D6E7F9",
                            fontSize: 36,
                        },
                    },
                    grid: {
                        left: "5%",
                        right: "18%",
                        top: "10%",
                        bottom: "10%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            name: "   单位：%",
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 12,
                            },
                            type: "value",
                            // data: xdata,
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 10,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                formatter: function (value) {
                                    return Math.abs(value); //负数取绝对值变正数
                                },

                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            type: "category",
                            data: xdata,
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                        {
                            name: "",
                            type: "category",
                            max: 10,
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                formatter: "{value}%",
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: name1,
                            type: type1,
                            barWidth: "40%",
                            stack: "total",
                            label: {
                                show: true,
                                position: "inside",
                                formatter: function (params) {
                                    return Math.abs(params.value) + "%";
                                },
                            },
                            // smooth: true,
                            // yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: "#00C0FF",
                                    barBorderRadius: 0,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata1,
                            areaStyle: {
                                //填充
                                color: "#00C0FF",
                                opacity: 1,
                            },
                        },
                        {
                            name: name2,
                            type: type2,
                            barWidth: "40%",
                            label: {
                                show: true,
                                position: "inside",
                                formatter: function (params) {
                                    return Math.abs(params.value) + "%";
                                },
                            },
                            stack: "total",
                            // smooth: true,
                            // yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: "#2DF09F",
                                    barBorderRadius: 0,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata2,
                            // areaStyle:"",
                        },
                    ],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },

            // 饼图
            barEcgarts6(data, dom) {
                let myChart = echarts.init(document.getElementById(dom));
                let option = {
                    color: [
                        "#00C0FF",
                        "#22E8E8",
                        "#FFD461",
                        "#A9DB52",
                        "#B76FD8",
                        "#FD852E",
                        "#FF4949",
                        "#0594C3",
                        "#009D9D",
                        "#A47905",
                    ],

                    tooltip: {
                        trigger: "item",
                        // formatter: '{b}: <br/> {d}%',
                        formatter: "{b}: <br/> {c}个<br/> {d}%",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "25",
                        },
                    },
                    legend: {
                        orient: "vertical",
                        left: "55%",
                        top: "30%",
                        bottom: "0%",
                        icon: "circle",
                        itemGap: 30,
                        textStyle: {
                            rich: {
                                name: {
                                    fontSize: 25,
                                    color: "#ffffff",
                                    padding: [0, 20, 0, 15],
                                },
                                value: {
                                    fontSize: 25,
                                    color: "#2CC6F9",
                                    // padding: [10, 0, 0, 15]
                                },
                            },
                        },
                        formatter: function (name) {
                            var data = option.series[0].data; //获取series中的data
                            var total = 0;
                            var tarValue;
                            for (var i = 0, l = data.length; i < l; i++) {
                                total += data[i].value;
                                if (data[i].name == name) {
                                    tarValue = data[i].value;
                                }
                            }
                            this.serverNum = total;
                            var p = ((tarValue / total) * 100).toFixed(2);
                            return "{name|" + name + "}{value|" + tarValue + "个  " + p + "%}";
                        },
                    },
                    series: [
                        {
                            name: "",
                            type: "pie",
                            radius: ["40%", "60%"],
                            center: ["30%", "50%"],
                            roseType: "",
                            label: {
                                show: false,
                            },
                            data: data,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: "rgba(0, 0, 0, 0.5)",
                                },
                            },
                            label: {
                                normal: {
                                    formatter: "{b} {d}%  ",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 22,
                                    },
                                },
                            },
                        },
                    ],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },

            // 雷达图
            barEcgarts7(data, maxData, dom) {
                let myChart = echarts.init(document.getElementById(dom));
                let option = {
                    title: {
                        text: "",
                    },
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                    },
                    // legend: {
                    //   // left: 'center',
                    //   data: [
                    //     // 'A Software',
                    //     // 'A Phone',
                    //     // 'Another Phone',
                    //     // 'Precipitation',
                    //     // 'Evaporation'
                    //   ]
                    // },
                    radar: [
                        {
                            indicator: maxData,
                            axisName: {
                                // formatter: '【{value}】',
                                color: "#fff",
                                fontSize: 32,
                            },
                            // radius: 80,
                            // center: ['50%', '60%']
                        },
                    ],
                    series: [
                        {
                            type: "radar",
                            tooltip: {
                                trigger: "item",
                            },
                            areaStyle: {},
                            data: [
                                {
                                    value: data.map((item) => {
                                        return item.value;
                                    }),
                                    name: "游客关注度",
                                },
                            ],
                            areaStyle: {
                                color: new echarts.graphic.RadialGradient(0.1, 0.6, 1, [
                                    {
                                        color: "rgba(255, 145, 124, 0.1)",
                                        offset: 0,
                                    },
                                    {
                                        color: "rgba(255, 145, 124, 0.9)",
                                        offset: 1,
                                    },
                                ]),
                            },
                        },
                    ],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },
            // 两柱一折线
            barEcgarts8(xdata, ydata1, ydata2, ydata3, name1, name2, name3, type1, type2, type3, dom, rotate, dw) {
                let myChart = echarts.init(document.getElementById(dom));
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                        // formatter: function (params) {
                        //     console.log(params);
                        //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
                        //     for (var i = 0; i < params.length; i++) {
                        //       if (params[i].data != undefined) {
                        //         res +=
                        //           "<p>" +
                        //           params[i].marker +
                        //           params[i].seriesName+" "+
                        //           params[i].data +
                        //           "万件" +
                        //           "</p>";
                        //       }
                        //     }
                        //     return res;
                        //   },
                    },
                    legend: {
                        orient: "horizontal",
                        // icon: "circle",
                        top: 40,
                        itemGap: 45,
                        textStyle: {
                            color: "#D6E7F9",
                            fontSize: 36,
                        },
                    },
                    grid: {
                        left: "5%",
                        right: "5%",
                        top: "24%",
                        bottom: "10%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: "category",
                            data: xdata,
                            axisLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)", // 颜色
                                    width: 1, // 粗细
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                rotate: rotate | 0,
                                textStyle: {
                                    color: "#D6E7F9",
                                    fontSize: 36,
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: dw,
                            type: "value",
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                        {
                            name: "",
                            type: "value",
                            max: 100,
                            nameTextStyle: {
                                fontSize: 36,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                formatter: "{value}%",
                                textStyle: {
                                    fontSize: 36,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: name1,
                            type: type1,
                            barWidth: "20%",
                            // smooth: true,
                            yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#00C0FF",
                                        },
                                        {
                                            offset: 1,
                                            color: "rgba(0,192,255,0)",
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata1,
                            areaStyle: {
                                //填充
                                color: "#00C0FF",
                                opacity: 1,
                            },
                        },
                        {
                            name: name2,
                            type: type2,
                            barWidth: "20%",
                            smooth: true,
                            yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color:
                                        type2 == "bar"
                                            ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                                  {
                                                      offset: 0,
                                                      color: "#2DF09F",
                                                  },
                                                  {
                                                      offset: 1,
                                                      color: "rgba(0,192,255,0)",
                                                  },
                                              ])
                                            : "#2DF09F",
                                    barBorderRadius: 4,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata2,
                            // areaStyle:"",
                        },
                        {
                            name: name3,
                            type: type3,
                            barWidth: "20%",
                            smooth: true,
                            yAxisIndex: 1,
                            itemStyle: {
                                normal: {
                                    color:
                                        type3 == "bar"
                                            ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                                  {
                                                      offset: 0,
                                                      color: "#fff849",
                                                  },
                                                  {
                                                      offset: 1,
                                                      color: "rgba(0,192,255,0)",
                                                  },
                                              ])
                                            : "#fff849",
                                    barBorderRadius: 4,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata3,
                            // areaStyle:"",
                        },
                    ],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            }, // 横向柱图

            barEcgarts9(data, dom) {
                let myChart = echarts.init(document.getElementById(dom));
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                    },
                    legend: {
                        selectedMode: false,
                        data: ["人"],
                        right: "4%",
                        textStyle: {
                            fontSize: 16,
                            color: "#fff",
                        },
                    },
                    grid: {
                        top: "0%",
                        left: "5%",
                        right: "5%",
                        bottom: "0",
                        containLabel: true,
                    },
                    xAxis: {
                        type: "value",
                        show: false,
                    },
                    yAxis: {
                        name: "",
                        type: "category",
                        triggerEvent: false,
                        inverse: true,
                        axisLine: {
                            show: false,
                        },
                        axisLabel: {
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                        },
                        axisTick: {
                            show: false,
                            length: 10,
                        },
                        data: data.map((item) => item.name),
                    },
                    series: [
                        {
                            // cursor:"auto",
                            type: "bar",
                            name: "",
                            showBackground: true,
                            itemStyle: {
                                barBorderRadius: [0, 10, 10, 0],
                                color: function (params) {
                                    var colors = [
                                        "#4587E7",
                                        "#35AB33",
                                        "#F5AD1D",
                                        "#ff7f50",
                                        "#da70d6",
                                        "#32cd32",
                                        "#6495ed",
                                    ];
                                    // 通过返回值的下标一一对应将颜色赋给柱子上，return出去什么颜色就是什么颜色，这里可以写判断
                                    if (params.dataIndex == 0) {
                                        return new echarts.graphic.LinearGradient(
                                            1,
                                            0,
                                            0,
                                            0,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "#FF9434", //指0%处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: "#F90808", //指100%处的颜色
                                                },
                                            ],
                                            false
                                        );
                                    } else if (params.dataIndex == 1) {
                                        return new echarts.graphic.LinearGradient(
                                            1,
                                            0,
                                            0,
                                            0,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "#FFF220", //指0%处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: "#F98508", //指100%处的颜色
                                                },
                                            ],
                                            false
                                        );
                                    } else if (params.dataIndex == 2) {
                                        return new echarts.graphic.LinearGradient(
                                            1,
                                            0,
                                            0,
                                            0,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "#2DF09F", //指0%处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: "#0EB1E5", //指100%处的颜色
                                                },
                                            ],
                                            false
                                        );
                                    } else {
                                        return new echarts.graphic.LinearGradient(
                                            1,
                                            0,
                                            0,
                                            0,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "#2BDAFF", //指0%处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: "#078FF7", //指100%处的颜色
                                                },
                                            ],
                                            false
                                        );
                                    }
                                    // return colors[params.dataIndex];
                                },
                            },
                            label: {
                                show: true,
                                position: [540, -2],
                                color: "#fff",
                                formatter: function (params) {
                                    return params.value + " 个";
                                },
                                fontSize: 28,
                            },
                            barWidth: 20,
                            color: "#539FF7",
                            data: data.map((item) => item.value),
                        },
                    ],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },
        },
    });
</script>
