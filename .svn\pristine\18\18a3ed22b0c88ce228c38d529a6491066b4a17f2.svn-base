<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>数字政府-右侧</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <link rel="stylesheet" href="/static/citybrain/szhgg/css/dzjg-right.css" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
    <!-- 轮播toolTip -->
    <script src="/static/js/jslib/echarts-wordcloud.min.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>

</head>
<style>
    .lctdiv {
        position: absolute;
        background: rgb(0, 135, 236);
        color: #fff;
        padding: 8px 30px;
        font-size: 30px;
        right: 0px;
        top: 10px;
        border-radius: 4px;
        cursor: pointer;
    }
</style>

<body>
    <div id="app" class="container" v-cloak>
        <div class="first-con">
            <nav>
                <s-header-title htype="1" title="重点应用情况" data-time="2022年7月22日"></s-header-title>
            </nav>
            <div class="first_content">
                <div class="left_part">
                    <nav>
                        <s-header-title-2 htype="1" title="多跨协同情况">
                            </s-header-title>
                    </nav>
                    <div>
                        <ul class="table_tabbar">
                            <li v-for="(item,index) in tabData" :key="index" @click="tableTab(index)"
                                :class="index==tab?'activeTab':''">
                                {{item}}
                            </li>
                        </ul>
                        <div class="table table1">
                            <div class="th">
                                <div class="th_td" style="flex: 0.45" v-for="(item,index) in dkxttheadList"
                                    :key="index">
                                    {{item}}
                                </div>
                            </div>
                            <div class="tbody" id="tbody">
                                <div class="tr" v-for="(item ,i) in dkxtData" :key="i">
                                    <div class="tr_td" style="flex: 0.45">{{item.depart}}</div>
                                    <div class="tr_td" style="flex: 0.45">{{item.value}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="middle_part" style="position: relative;">
                    <nav>
                        <s-header-title-2 htype="1" title="核心指标情况">
                            </s-header-title>
                    </nav>
                    <div class="lctdiv" @click="openLct()">流程图</div>
                    <ul class="middle_part_content">
                        <li v-for="(item,index) in hxzbData" :key="index">
                            <div style="color: #31a7ff; font-size:35px;font-weight: 500;">{{item.value}}<span
                                    style="color: #31a7ff; font-size:29px">{{item.unit}}</span></div>
                            <div style="color: #fff; font-size:26px" v-if="item.name!=''">{{item.name}}</div>
                        </li>
                    </ul>
                </div>
                <div class="right_part">
                    <nav>
                        <s-header-title-2 htype="1" title="数据共享情况">
                            </s-header-title>
                    </nav>
                    <div class="small_title">共享指标</div>
                    <ul class="right_part_top">
                        <li v-for="(item,index) in gxzbData" :key="index" :style="{'width':index>2?'25%':'33%'}">
                            <div style="color: #31a7ff; font-size:35px;font-weight: 500;">{{item.value}}<span
                                    style="color: #31a7ff; font-size:29px">{{item.unit}}</span></div>
                            <div style="color: #fff; font-size:26px" v-if="item.name!=''">{{item.name}}</div>
                        </li>
                    </ul>
                    <div style="color: #fff;font-size :28px;position:relative;top:40px;left: 40px;">高频数据目录</div>

                    <div style="width: 100%;height:500px;margin-top:-20px;" id="chart01"></div>
                </div>
            </div>

        </div>
        <div class="second_con">

            <div class="one_part">
                <nav>
                    <s-header-title-2 htype="1" title="组件数据共享情况">
                        </s-header-title>
                </nav>
                <div style="color: #fff;font-size :28px;margin-bottom: 20px;margin-left: 50px;">共享指标</div>

                <ul class="right_part_top">
                    <li v-for="(item,index) in zjsjgxData" :key="index">
                        <div style="color: #31a7ff; font-size:35px;font-weight: 500;">{{item.value}}<span
                                style="color: #31a7ff; font-size:29px">{{item.unit}}</span></div>
                        <div style="color: #fff; font-size:26px" v-if="item.name!=''">{{item.name}}</div>
                    </li>
                </ul>
                <div style="color: #fff;font-size :28px;position:relative;top:40px;left: 40px;">高频调用组件</div>

                <div style="width: 100%;height:500px" id="chart02"></div>
            </div>
            <div class="two_part">
                <nav>
                    <s-header-title-2 htype="1" title="实践应用成效情况">
                        </s-header-title>
                </nav>
                <div style="text-align: center;color: #31a7ff;font-size: 28px;margin: 51px 0px;
        ">{{szfzyyData.name}}</div>
                <div class="item-num yel-color" style="justify-content: center; margin: 51px 0px">
                    <div v-for="(item,index) in szfzyyData.value" :key="index">
                        <count-to :start-val="0" :end-val="Number(item)" :duration="3000"
                            class="count-toNum s-c-yellow-gradient"></count-to>
                    </div>
                    <span>个</span>
                </div>

                <div id="chart03" style="width: 100%; height: 400px; margin-top: 90px; display: none"></div>
                <div v-for="(item,index) in szfzyyData.list" :class="index<3?'bar breath-light':''"
                    style="display: flex; padding: 12px 0">
                    <span style="width: 120px; font-size: 24px; color: #ffffff">{{item.name}}</span>
                    <el-progress type="line" :percentage="item.value/70" :show-text="false" :stroke-width="26"
                        color="#23ffe2" style="flex: 1"></el-progress>
                    <span style="width: 140px; font-size: 24px; color: #ffb726">{{item.desc}}</span>
                </div>
            </div>
            <div class="three_part">
                <nav>
                    <s-header-title-2 htype="1" title="制造成果应用成效">
                        </s-header-title>
                </nav>
                <div style="display: flex;justify-content: space-between;padding-right: 20px;margin: 40px 0; ">
                    <ul class="btn_ul">
                        <li v-for="(item,index) in btn" :key="index" @click="btn1Click(index)"
                            :class="index==btn1?'btn_active':''">{{item}}</li>

                    </ul>
                    <div>
                        <el-select v-model="value" placeholder="请选择选项">
                            <el-option v-for="item in options" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div>
                    <div v-show="show0==0" class="left-con">
                        <li v-for="(item,index) in zzcgData" :key="index">
                            <span>{{item.name}}</span><i class="el-icon-arrow-right"></i>
                        </li>
                    </div>
                    <div v-show="show0==1" id="zzcgEcharts" style="width:400px;height:600px">
                    </div>
                    <div v-show="show0==2" id="zzcgEcharts1" style="width:400px;height:600px"></div>
                    <div v-show="show0==3" id="zzcgEcharts2" style="width:400px;height:600px"></div>
                </div>
            </div>
            <div class="four_part">
                <nav>
                    <s-header-title-2 htype="1" title="理论成果应用成效">
                        </s-header-title>
                </nav>
                <div style="display: flex;justify-content: space-between;padding-right: 20px;margin: 40px 0; ">
                    <ul class="btn_ul">
                        <li v-for="(item,index) in btn" :key="index" @click="btn2Click(index)"
                            :class="index==btn2?'btn_active':''">{{item}}</li>

                    </ul>
                    <div>
                        <el-select v-model="value2" placeholder="请选择选项">
                            <el-option v-for="item in options" :key="item.value" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div>
                    <div v-show="show1==0" class="left-con">
                        <li v-for="(item,index) in zzcgData1" :key="index">
                            <span>{{item.name}}</span><i class="el-icon-arrow-right"></i>
                        </li>
                    </div>
                    <div v-show="show1==1" id="zzcgEcharts3" style="width:400px;height:600px">
                    </div>
                    <div v-show="show1==2" id="zzcgEcharts4" style="width:400px;height:600px"></div>
                    <div v-show="show1==3" id="zzcgEcharts5" style="width:400px;height:600px"></div>
                </div>
            </div>
        </div>
    </div>
</body>
<script type="module">
    new Vue({
        el: '#app',
        data() {
            return {
                dkxtData: [],
                dkxttheadList: ['部门', '协同数'],
                tabData: ['跨层级', '跨地域', '跨系统', '跨部门', '跨业务'],
                tab: 0,
                hxzbData: [],
                gxzbData: [],
                gpsjmlData: [],
                zjsjgxData: [],
                szfzyyData: {},
                zzcgData: [],
                zzcgData1: [],
                btn: ['地区', '部门'],
                btn1: 0,
                btn2: 0,
                value2: '条形图',
                options: [],
                value: '条形图',
                show0: 0,
                show1: 0

            }
        },
        watch: {
            value(newName, oldName) {
                this.show0 = this.value
            },
            value2(newName, oldName) {
                this.show1 = this.value2
            },
        },
        mounted() {
            this.init()
            this.queryData1('地区')
            this.queryData2('地区')

        },
        methods: {
            openLct() {
                top.commonObj.funOpenIframe({
                    width: "2130px",
                    height: "1270px",
                    zIndex: "999",
                    src: "/static/citybrain/szhgg/pages/szzf-dialog.html",
                    left: "2945px",
                    top: "275px",
                    name: "szzf-dialog",
                });
            },
            btn2Click(index) {
                this.btn2 = index
                if (index == 0) {
                    this.queryData2('地区')
                } else {
                    this.queryData2('部门')
                }

            },
            btn1Click(index) {
                this.btn1 = index
                if (index == 0) {
                    this.queryData1('地区')
                } else {
                    this.queryData1('部门')
                }
            },
            newline(option, number, axis) {
                /* 此处注意你的json是数组还是对象 */
                option[axis][0].axisLabel = {
                    interval: 0,
                    // margin: 55, 
                    textStyle: {
                        // align: 'left',
                        color: '#fff',
                        fontSize: '24',
                        interval: 0,
                        padding: [50, 0, 0, 0],

                        // 文字左对齐
                    },
                    formatter: (params) => {
                        let newParamsName = '';
                        const paramsNameNumber = params.length;
                        const provideNumber = number;
                        const rowNumber = Math.ceil(paramsNameNumber / provideNumber);
                        if (paramsNameNumber > provideNumber) {
                            for (let p = 0; p < rowNumber; p++) {
                                let tempStr = '';
                                const start = p * provideNumber;
                                const end = start + provideNumber;
                                if (p == rowNumber - 1) {
                                    tempStr = params.substring(start, paramsNameNumber);
                                } else {
                                    tempStr = params.substring(start, end) + '\n';
                                }
                                newParamsName += tempStr;
                            }
                        } else {
                            newParamsName = params;
                        }
                        return newParamsName;
                    }
                };
                return option;
            },
            init() {
                $get("/szggh/szzf/select").then((res) => {

                    // this.options = res
                    this.options = [
                        { label: "条形图", value: 0 },
                        { label: "饼状图", value: 1 },
                        { label: "柱状图", value: 2 },
                        { label: "折线图", value: 3 }
                    ]
                });
                $get('/szggh/dzjg/sjyy').then((res) => {
                    this.szfzyyData.value = res.total.value.toString()
                    this.szfzyyData.name = res.total.name
                    this.szfzyyData.list = res.data

                    console.log(this.szfzyyData)
                    this.initEcharts02('chart03', res.data)
                })
                $get("/szggh/szzf/dkxt", { type: "跨层级" }).then((res) => {
                    this.dkxtData = res
                });
                $get("/szggh/szzf/hxzb").then((res) => {
                    let beforedata = res.filter(item => {
                        return item.type == 'before'
                    })
                    let afterdata = res.filter(item => {
                        return item.type == 'after'
                    })
                    // console.log();
                    this.hxzbData.push({ name: '', value: '改革前', unit: '' })
                    this.hxzbData.push(...beforedata)
                    this.hxzbData.push({ name: '', value: '改革后', unit: '' })

                    this.hxzbData.push(...afterdata)
                });
                $get("/szggh/szzf/gxzb").then((res) => {
                    this.gxzbData = res
                });
                $get("/szggh/szzf/zjsjgx").then((res) => {
                    this.zjsjgxData = res
                });
                $get("/szggh/szzf/gpsjml").then((res) => {
                    this.initEcharts01('chart01', res)

                });
                $get("/szggh/szzf/gpdyzj").then((res) => {
                    this.initEcharts01('chart02', res)
                });


            },
            queryData1(type1) {
                $get("/szggh/szsh/zzcg", { type: "制造成果", type1: type1 }).then((res) => {

                    this.zzcgData = res
                    var edata = res.map((a, i) => {
                        return {
                            name: a.name,
                            value: Math.ceil(Math.random() * 100)
                        }
                    })

                    this.barEcgarts4(edata, "zzcgEcharts")

                    this.barEcgarts8(
                        edata.map((a) => { return a.name }),
                        edata.map((a) => { return a.value }),
                        "",
                        "bar",
                        "zzcgEcharts1",
                        30,
                        ""
                    )
                    this.barEcgarts8(
                        edata.map((a) => { return a.name }),
                        edata.map((a) => { return a.value }),
                        "",
                        "line",
                        "zzcgEcharts2",
                        30,
                        ""
                    )
                    if (type1 === "部门") {

                        let ndata = [
                            {
                                name: "销售部",
                            }, {
                                name: "追求部",
                            }, {
                                name: "营业部",
                            }, {
                                name: "共好部",
                            }, {
                                name: "总宣部",
                            }, {
                                name: "出口部",
                            }, {
                                name: "采购部",
                            }, {
                                name: "研发部",
                            }, {
                                name: "销售推广部",
                            }, {
                                name: "外交部",
                            },
                        ]
                        this.zzcgData = ndata
                        var edata = this.zzcgData.map((a, i) => {
                            return {
                                name: a.name,
                                value: Math.ceil(Math.random() * 100)
                            }
                        })

                        this.barEcgarts4(edata, "zzcgEcharts")

                        this.barEcgarts8(
                            ndata.map(item => item.name),
                            edata.map((a) => { return a.value }),
                            "",
                            "bar",
                            "zzcgEcharts1",
                            30,
                            ""
                        )
                        this.barEcgarts8(
                            ndata.map(item => item.name),
                            edata.map((a) => { return a.value }),
                            "",
                            "line",
                            "zzcgEcharts2",
                            30,
                            ""
                        )
                    }
                });
            },
            queryData2(type1) {
                $get("/szggh/szsh/zzcg", { type: "理论成果", type1: type1 }).then((res) => {
                    console.log(this.zzcgData1)
                    this.zzcgData1 = res
                    var edata = res.map((a, i) => {
                        return {
                            name: a.name,
                            value: Math.ceil(Math.random() * 100)
                        }
                    })
                    // 右边
                    this.barEcgarts4(edata, "zzcgEcharts3")

                    this.barEcgarts8(
                        edata.map((a) => { return a.name }),
                        edata.map((a) => { return a.value }),
                        "",
                        "bar",
                        "zzcgEcharts4",
                        30,
                        ""
                    )
                    this.barEcgarts8(
                        edata.map((a) => { return a.name }),
                        edata.map((a) => { return a.value }),
                        "",
                        "line",
                        "zzcgEcharts5",
                        30,
                        ""
                    )
                    if (type1 === "部门") {

                        let ndata = [
                            {
                                name: "销售部",
                            }, {
                                name: "追求部",
                            }, {
                                name: "营业部",
                            }, {
                                name: "共好部",
                            }, {
                                name: "总宣部",
                            }, {
                                name: "出口部",
                            }, {
                                name: "采购部",
                            }, {
                                name: "研发部",
                            }, {
                                name: "销售推广部",
                            }, {
                                name: "外交部",
                            },
                        ]
                        this.zzcgData1 = ndata
                        var edata = this.zzcgData1.map((a, i) => {
                            return {
                                name: a.name,
                                value: Math.ceil(Math.random() * 100)
                            }
                        })

                        this.barEcgarts4(edata, "zzcgEcharts3")

                        this.barEcgarts8(
                            ndata.map(item => item.name),
                            edata.map((a) => { return a.value }),
                            "",
                            "bar",
                            "zzcgEcharts4",
                            30,
                            ""
                        )
                        this.barEcgarts8(
                            ndata.map(item => item.name),
                            edata.map((a) => { return a.value }),
                            "",
                            "line",
                            "zzcgEcharts5",
                            30,
                            ""
                        )
                    }
                });
            },
            tableTab(index) {
                this.tab = index
                if (this.tab === 0) {
                    $get("/szggh/szzf/dkxt", { type: "跨层级" }).then((res) => {
                        this.dkxtData = res
                    });
                } else if (this.tab === 1) {
                    $get("/szggh/szzf/dkxt", { type: "跨地域" }).then((res) => {
                        this.dkxtData = res
                    });
                } else if (this.tab === 2) {
                    $get("/szggh/szzf/dkxt", { type: "跨系统" }).then((res) => {
                        this.dkxtData = res
                    });
                } else if (this.tab === 3) {
                    $get("/szggh/szzf/dkxt", { type: "跨部门" }).then((res) => {
                        this.dkxtData = res
                    });
                } else if (this.tab === 4) {
                    $get("/szggh/szzf/dkxt", { type: "跨业务" }).then((res) => {
                        this.dkxtData = res
                    });
                }

            },
            initEcharts02(dom, data) {
                const myCharts = echarts.init(document.getElementById(dom))


                let yLabel = [];
                let yData = [];
                let rightLabel = []
                data.forEach(item => {
                    yLabel.push(item.name)
                    yData.push(item.value)
                    rightLabel.push(item.desc)
                })
                let bgData = [];
                for (let i in yData) {
                    bgData.push(8000);
                }
                let option = {
                    grid: {
                        left: "0%",
                        right: "0%",
                        bottom: "5%",
                        top: "10%",
                        containLabel: true,
                    },
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "none",
                        },
                        formatter: function (params) {
                            return (

                                "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:rgba(36,207,233,0.9)'></span>" +
                                params[0].name +
                                " : " +
                                params[0].value +
                                "个 <br/>"
                            );
                        },
                    },
                    //   backgroundColor: "rgb(20,28,52)",
                    xAxis: {
                        show: false,
                        type: "value",
                    },
                    yAxis: [
                        {
                            type: "category",
                            inverse: true,
                            axisLabel: {
                                show: true,
                                margin: 15,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: '24'
                                },
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                            },

                            data: yLabel,
                        },
                        {
                            type: "category",
                            inverse: true,
                            axisTick: "none",
                            axisLine: "none",
                            show: true,
                            axisLabel: {
                                textStyle: {
                                    color: "#ffb726",
                                    fontSize: "25",
                                },

                            },


                            data: rightLabel,
                        },
                    ],
                    series: [
                        {
                            name: "",
                            type: "bar",
                            zlevel: 1,
                            itemStyle: {
                                barBorderRadius: [0, 10, 10, 0],
                                color: function (params) {
                                    var colors = [
                                        "#4587E7",
                                        "#35AB33",
                                        "#F5AD1D",
                                        "#ff7f50",
                                        "#da70d6",
                                        "#32cd32",
                                        "#6495ed",
                                    ];
                                    // 通过返回值的下标一一对应将颜色赋给柱子上，return出去什么颜色就是什么颜色，这里可以写判断
                                    if (params.dataIndex == 0) {
                                        return new echarts.graphic.LinearGradient(
                                            1,
                                            0,
                                            0,
                                            0,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "#FF9434", //指0%处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: "#F90808", //指100%处的颜色
                                                },
                                            ],
                                            false
                                        );
                                    } else if (params.dataIndex == 1) {
                                        return new echarts.graphic.LinearGradient(
                                            1,
                                            0,
                                            0,
                                            0,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "#FFF220", //指0%处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: "#F98508", //指100%处的颜色
                                                },
                                            ],
                                            false
                                        );
                                    } else if (params.dataIndex == 2) {
                                        return new echarts.graphic.LinearGradient(
                                            1,
                                            0,
                                            0,
                                            0,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "#2DF09F", //指0%处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: "#0EB1E5", //指100%处的颜色
                                                },
                                            ],
                                            false
                                        );
                                    } else {
                                        return new echarts.graphic.LinearGradient(
                                            1,
                                            0,
                                            0,
                                            0,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "#2BDAFF", //指0%处的颜色
                                                },
                                                {
                                                    offset: 1,
                                                    color: "#078FF7", //指100%处的颜色
                                                },
                                            ],
                                            false
                                        );
                                    }
                                    // return colors[params.dataIndex];
                                },
                            },

                            barWidth: 20,
                            data: yData,
                        },
                        {
                            name: "背景",
                            type: "bar",
                            barWidth: 20,
                            barGap: "-100%",
                            data: bgData,
                            itemStyle: {
                                normal: {
                                    color: "rgba(24,31,68,1)",
                                    barBorderRadius: [0, 30, 30, 0],
                                },
                            },
                        },
                    ],
                };
                let option1 = this.newline(option, 4, 'yAxis')
                myCharts.setOption(option1);

            },
            initEcharts01(dom, res) {
                const myCharts = echarts.init(document.getElementById(dom))

                var data = [];

                var titlename = [];
                var valdata = [];

                res.forEach((item, index) => {
                    data.push(item.value)
                    titlename.push(index + 1 + '.' + item.name)
                    valdata.push(item.value)
                })

                var myColor = ["#1089E7", "#F57474", "#56D0E3", "#F8B448", "#8B78F6"];
                let option = {
                    grid: {

                        right: "20%",


                        // containLabel: true,
                    },
                    xAxis: {
                        show: false,
                    },
                    yAxis: [
                        {
                            show: false,
                            data: titlename,
                            inverse: true,
                            axisLine: {
                                show: false,
                            },

                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                textStyle: {


                                    color: function (value, index) {
                                        var num = myColor.length;
                                        return myColor[index % num];
                                    },
                                },
                                formatter: function (value, index) {
                                    return ["{title|" + value + "} "].join("\n");
                                },
                                rich: {},

                            },
                        },
                        {
                            show: true,
                            inverse: true,
                            data: valdata,
                            axisLabel: {

                                textStyle: {

                                    fontSize: 30,
                                    color: function (value, index) {
                                        var num = myColor.length;
                                        return myColor[index % num];
                                    },
                                },
                            },
                            axisLine: {
                                show: false,
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                        },
                    ],
                    series: [
                        {
                            name: "条",
                            type: "bar",
                            yAxisIndex: 0,
                            data: data,
                            barWidth: 50,
                            itemStyle: {
                                normal: {
                                    barBorderRadius: 30,

                                    color: function (params) {
                                        var num = myColor.length;
                                        return myColor[params.dataIndex % num];
                                    },
                                },
                            },
                            label: {
                                normal: {
                                    show: true,
                                    position: "insideLeft",
                                    formatter: "{b}",
                                    fontSize: 30,
                                    color: '#fff'
                                },
                            },
                        },
                    ],
                };

                myCharts.setOption(option);
            },
            //饼图
            barEcgarts4(data, dom) {
                let myChart = echarts.init(document.getElementById(dom));
                let option = {
                    color: [
                        "#00C0FF",
                        "#22E8E8",
                        "#FFD461",
                        "#A9DB52",
                        "#B76FD8",
                        "#FD852E",
                        "#FF4949",
                        "#0594C3",
                        "#009D9D",
                        "#A47905",
                    ],

                    tooltip: {
                        trigger: "item",
                        // formatter: '{b}: <br/> {d}%',
                        formatter: "{b}: <br/> {c}个<br/> {d}%",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "25",
                        },
                    },
                    legend: {
                        orient: "vertical",
                        left: "45%",
                        top: "5%",
                        bottom: "0%",
                        icon: "circle",
                        itemGap: 30,
                        textStyle: {
                            rich: {
                                name: {
                                    fontSize: 25,
                                    color: "#ffffff",
                                    padding: [0, 20, 0, 15],
                                },
                                value: {
                                    fontSize: 25,
                                    color: "#2CC6F9",
                                    // padding: [10, 0, 0, 15]
                                },
                            },
                        },
                        formatter: function (name) {
                            var data = option.series[0].data; //获取series中的data
                            var total = 0;
                            var tarValue = 0;
                            for (var i = 0, l = data.length; i < l; i++) {
                                total += Number(data[i].value);
                                if (data[i].name == name) {
                                    tarValue = data[i].value;
                                }
                            }
                            // this.serverNum = total;
                            var p = ((tarValue / total) * 100).toFixed(2);
                            return "{name|" + name + "}{value|" + tarValue + "  " + p + "%}";
                        },
                    },
                    series: [
                        {
                            name: "",
                            type: "pie",
                            radius: ["30%", "40%"],
                            center: ["22%", "50%"],
                            roseType: "",
                            label: {
                                show: false,
                            },
                            data: data,
                            // emphasis: {
                            //   itemStyle: {
                            //     shadowBlur: 10,
                            //     shadowOffsetX: 0,
                            //     shadowColor: 'rgba(0, 0, 0, 0.5)'
                            //   }
                            // },
                            // label: {
                            //   normal: {
                            //     formatter: "{b} {d}%  ",
                            //     textStyle: {
                            //       color: "#fff",
                            //       fontSize: 22,
                            //     }
                            //   }
                            // }
                        },
                    ],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },
            // 柱图/折现
            barEcgarts8(xdata, ydata1, name1, type1, dom, rotate, dw) {
                let myChart = echarts.init(document.getElementById(dom));
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                        // formatter: function (params) {
                        //     console.log(params);
                        //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
                        //     for (var i = 0; i < params.length; i++) {
                        //       if (params[i].data != undefined) {
                        //         res +=
                        //           "<p>" +
                        //           params[i].marker +
                        //           params[i].seriesName+" "+
                        //           params[i].data +
                        //           "万件" +
                        //           "</p>";
                        //       }
                        //     }
                        //     return res;
                        //   },
                    },
                    legend: {
                        orient: "horizontal",
                        // icon: "circle",
                        top: 40,
                        itemGap: 45,
                        textStyle: {
                            color: "#D6E7F9",
                            fontSize: 28,
                        },
                    },
                    grid: {
                        left: "5%",
                        right: "5%",
                        top: "5%",
                        bottom: "10%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: "category",
                            data: xdata,
                            axisLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)", // 颜色
                                    width: 1, // 粗细
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                // interval: 0,
                                // rotate: rotate | 0,
                                textStyle: {
                                    color: "#D6E7F9",
                                    fontSize: 20,
                                },
                                formatter: function (value) {
                                    return value.split("").join("\n");
                                }
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: dw,
                            type: "value",
                            nameTextStyle: {
                                fontSize: 28,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 28,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                        {
                            name: "",
                            type: "value",
                            max: 100,
                            nameTextStyle: {
                                fontSize: 28,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                formatter: "{value}%",
                                textStyle: {
                                    fontSize: 28,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: name1,
                            type: type1,
                            barWidth: "20%",
                            // smooth: true,
                            yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#00C0FF",
                                        },
                                        {
                                            offset: 1,
                                            color: "rgba(0,192,255,0)",
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            // label: {
                            //   show: true,
                            //   fontSize: 18,
                            //   fontWeight: "bold",
                            //   color: "#fff",
                            //   marginTop: 15,
                            //   position: "top",
                            // },
                            data: ydata1,
                            // areaStyle: {
                            //     //填充
                            //     color: "#00C0FF",
                            //     opacity: 1,
                            // },
                        }],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            }, // 横向柱图
        },
    })
</script>

</html>