<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <title>弹窗</title>
  <script src="/Vue/vue.js"></script>
  <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
  <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <!-- <link rel="stylesheet" href="/static/citybrain/ggfw/css/jyfw-diaolog.css" /> -->
  <!-- <link rel="stylesheet" href="../css/city.css" />
  <link rel="stylesheet" href="../css/common.css" /> -->
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
  <script src="/static/js/jslib/echarts-wordcloud.min.js"></script>
  <!-- 轮播toolTip -->
</head>
<style>
  .container {
    width: 2130px;
    margin: 0 auto;
    height: 1270px;
    background-color: #031827;
    box-shadow: -3px 2px 35px 0px #000000;
  
  }

  .container .head {
    width: 100%;
    height: 80px;
    line-height: 100px;
    background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%), linear-gradient(#ffffff, #ffffff);
    background-blend-mode: normal, normal;
    padding: 10px 50px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    justify-content: space-between;
  }

  .head span {
    font-size: 40px !important;
    font-weight: 500;
    color: #fff;
    font-weight: bold;
  }

  .head .img {
    display: inline-block;
    margin: 20px;
    float: right;
    width: 34px;
    height: 34px;
    background-image: url(/static/citybrain/csdn/img/cstz2-middle/close-hover.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

</style>

<body>
  <div id="app" class="szjj-dialog container">
    <div class="head">
      <span>详情图</span>
      <div class="img" @click="closeDialog"></div>
    </div>
    <img style="margin-left:180px" src="../img/szjj.png" alt="">
  </div>
</body>
<script type="module">
  new Vue({
    el: "#app",
    data: {
    },
    //项目生命周期
    mounted() {
    },
    methods: {
      closeDialog() {
        top.commonObj.funCloseIframe({
          name: "szjj-dialog",
        });
      },
    
    },

  });
</script>

</html>