<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>营商环境</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/elementui.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/elementui/js/elementui.js"></script>
    <script src="/static/citybrain/csdn/echarts/china.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
</head>
<style>
    [v-cloak] {
        display: none;
    }
    html,body,ul,p{
        padding:0;
        margin:0;
        list-style: none;
    }
    .container{
        width:2200px;
        height:1470px;
        background-color: #031827;
      box-shadow: -3px 2px 35px 0px #000000;
      border: 1px solid #359cf8;
      border-radius: 60px;

    }
    .tab-con{
      width: 500px;
      height: 80px;
      display: flex;
      margin: 20px auto;
    }
    .tab-con li{
      width: 200px;
      height: 80px;
      background-color: cornsilk;
      border: 1px solid #3d8cdd;
      color: #3d8cdd;
      font-size: 35px;
      line-height: 80px;
      text-align: center;
      cursor: pointer;
    }
    .active{
      background-color: #3d8cdd!important;
      color: #fff!important
    }
    .container .head {
    width: 100%;
    height: 100px;
    line-height: 100px;
    background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%), linear-gradient(#ffffff, #ffffff);
    background-blend-mode: normal, normal;
    padding: 10px 50px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 60px;
    border-top-right-radius: 60px;
  }

  .head span {
    font-size: 48px !important;
    font-weight: 500;
    color: #fff;
    font-weight: bold;
  }

  .head .img {
    display: inline-block;
    margin: 20px;
    float: right;
    width: 34px;
    height: 34px;
    background-image: url(/static/citybrain/csdn/img/cstz2-middle/close-hover.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .content{
      width: 100%;
      height: calc(100% - 100px);
      /* display: flex;
      justify-content: space-between; */
      padding: 20px;
      box-sizing: border-box;
    }
</style>

<body>
    <div id="app" class="container" v-cloak>
      <div class="head">
        <span>营商环境</span>
        <div class="img" @click="closeDialog"></div>
      </div>
      <div class="content">
        <div class="tab-con" >
          <li v-for="(item,index) in tabList" 
          :class="currentIndex===index?'active':''"
          @click="changeTab(index)"
          :key="index">
            {{item.name}}
          </li>
        </div>
        <div id="mainMap1" style="width: 2200px; height:1200px;" v-show="currentIndex===0"></div>
        <div id="mainMap2" style="width: 2200px; height:1200px;" v-show="currentIndex===1"></div>
      </div>
    </div>
</body>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>

<script type="module">

    new Vue({
        el: '#app',
        data: {
            tabList:[
              {
                name:"内资",
                id:1,
              },
              {
                name:"外资",
                id:2,
              }
            ],
            showMap1:false,
            qgMapData_in: [],
            qgMapData_out: [],
            qgMapDataPop_in: [],
            qgMapDataPop_out: [],
            peopleIn: [],
            peopleOut: [],
            tableData_in: [],
            tableData_out: [],
            myChart2: {},
            inToMapArr: [],
            outToMapArr: [],
            inProvince: 0,
            outProvince: 0,
            showMap: true,
            activeName: "first",
            top10Data: [],
            qgMapData: [],
            // 全省的地图
            showQxMap: false,
            qxMapData: [
                {
                coord: [119.393576, 30.057459],
                name: "杭州市",
                num: "",
                pm: "",
                },
                {
                coord: [121.409792, 29.700388],
                name: "宁波市",
                num: "",
                pm: "",
                },
                {
                coord: [121.114181, 28.772005],
                name: "台州市",
                num: "",
                pm: "",
                },
                {
                coord: [120.702112, 29.897117],
                name: "绍兴市",
                num: "",
                pm: "",
                },
                {
                coord: [120.672111, 28.000575],
                name: "温州市",
                num: "",
                pm: "",
                },
                {
                coord: [118.769723, 28.921163],
                name: "衢州市",
                num: "",
                pm: "",
                },
                {
                coord: [119.422711, 28.10005],
                name: "丽水市",
                num: "",
                pm: "",
                },
                {
                coord: [119.653436, 29.084634],
                name: "金华市",
                num: "",
                pm: "",
                },
            ],
            currentIndex:0,
          },
        //项目生命周期
        mounted() {
          this.getInOrOut("mainMap1",false);
            // var that = this
            // window.addEventListener("message", function (e) {
            //     let info = e.data;
            //     if(info.status == 'rkdt-fly'){

            //         if(info.code==1){
            //             that.showMap1=true
            //             that.getInOrOut("mainMap1");
            //         }else{
            //             that.showMap1=false
            //             that.getInOrOut("mainMap2");
            //         }
            //     }
            // });
        },

        methods: {
          changeTab(index){
           this.currentIndex=index;
           if(index==0){
                this.getInOrOut("mainMap1",false);
             }else{
                 this.getInOrOut("mainMap2",true);
            }
          },
          closeDialog() {
          top.commonObj.funCloseIframe({
            name: "yshj_dialog",
          });
        },
            // 加载全国的echarts的地图
          drawQgMap(dom, chartsMapData, titleText,flag) {
            let that = this;
            this.myChart2 = echarts.init(document.getElementById(dom));
            let series = [];
            let outData = JSON.parse(JSON.stringify(this.qxMapData));
            let chinaGeoCoordMap = chartsMapData;
            let len = outData.length - 1;
            let chinaDatas = chartsMapData;
            console.log(chartsMapData)
            let option = {
              tooltip: {
                show: false,
                trigger: "item",
                showDelay: 0,
                hideDelay: 0,
                enterable: true,
                transitionDuration: 0,
                extraCssText: "z-index:100",
                formatter: function (params, ticket, callback) {
                  //根据业务自己拓展要显示的内容
                  var res = "";
                  var name="";
                  var value="";
                  if(params.componentSubType=='effectScatter'){
                    name = params.data.name;
                    value = params.data.num;
                    }else{
                        name="-";
                        value="-"
                    }
                  
                  if (name != "金华") {
                    res = "<span>" + name + "-来金就诊总人数:" + value + "人</br>来金就诊病种:</br>高血压  20人</br>糖尿病  15人</br>冠心病 12人 </span>"  ;
                  } else {
                    res = "<span>" + name + "</span>";
                  }
                  return res;
                },
                textStyle: {
                  fontSize: "40",
                },
              },
              visualMap: {
                show:flag,
                min: 0,
                max: 1000000,
                right: 100,
                seriesIndex: 1,
                type: "piecewise",
                bottom: 100,
                textStyle: {
                  color: "#FFFF",
                  fontSize:"28",
                },
                splitList: [
                  {
                    gt: 50000,
                    color: "#F5222D",
                    label: "大陆981209亿元",
                  }, //大于5万人
                  {
                    gte: 30000,
                    lte: 50000,
                    color: "#FA541C ",
                    label: "日本3646亿元",
                  }, //3-5万人
                  {
                    gte: 10000,
                    lte: 30000,
                    color: "#FA8C16",
                    label: "美国12546亿元",
                  }, //1-3万人
                  {
                    lte: 10000,
                    color: "#fbe1d6",
                    label: "其他33546亿元",
                  },
                ],
              },
              color: ["#e6d85b", "#e6d85b", "#e6d85b", "#e6d85b", "#e6d85b"],
              geo: {
                map: "china",
                zoom: 1.2,
                label: {
                  emphasis: {
                    show: false,
                  },
                },
                roam: true, //是否允许缩放
                itemStyle: {
                  normal: {
                    color: "rgba(51, 69, 89, .5)", //地图背景色
                    borderColor: "#00ffff", //省市边界线00fcff 516a89
                    borderWidth: 2,
                    opacity: 0.8,
                    shadowBlur: 20,
                    shadowColor: "#006dda",
                    shadowOffsetX: 5,
                    shadowOffsetY: 5,
                  },
                  emphasis: {
                    color: "rgba(37, 43, 61, .5)", //悬浮背景
                  },
                },
              },
              series: series,
            };

            [["金华", chinaDatas]].forEach(function (item, i) {
              series.push(
                {
                  type: "lines",
                  zlevel: 2,
                  effect: {
                    show: true,
                    period: 4, //箭头指向速度，值越小速度越快
                    trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
                    symbol: "arrow", //箭头图标
                    symbolSize: 30, //图标大小
                  },
                  lineStyle: {
                    normal: {
                      width: 6, //尾迹线条宽度
                      opacity: 1, //尾迹线条透明度
                      curveness: 0.3, //尾迹线条曲直度
                    },
                  },
                  data: that.convertData(
                    item[1],
                    chinaGeoCoordMap,
                    [119.653436, 29.084634],
                    dom
                  ),
                },
                {
                  type: "effectScatter",
                  coordinateSystem: "geo",
                  zlevel: 2,
                  rippleEffect: {
                    //涟漪特效
                    period: 4, //动画时间，值越小速度越快
                    brushType: "stroke", //波纹绘制方式 stroke, fill
                    scale: 4, //波纹圆环最大限制，值越大波纹越大
                  },
                  label: {
                    normal: {
                      show: true,
                      position: "right", //显示位置
                      // offset: [5, -20], //偏移设置
                      formatter: function (params) {
                        //圆环显示文字
                        // return (
                        //   params.data.pm +
                        //   "" +
                        //   params.data.name +
                        //   "{a| " +
                        //   params.data.num +
                        //   "}人"
                        // );
                        return params.data.name;
                        
                      },
                      color: "#fff",
                      fontSize: 36,
                      rich: {
                        a: {
                          color: "yellow",
                          fontSize: 36,
                        },
                      },
                    },
                    emphasis: {
                      show: true,
                    },
                  },
                  symbol: "circle",
                  symbolSize: function (val) {
                    // return 10 + val[2] * 5; //圆环大小
                    return 20; //圆环大小
                  },
                  data: item[1].map(function (dataItem) {
                    return {
                      name: dataItem.name,
                      value: dataItem.coord,
                      num: dataItem.num,
                      pm: dataItem.pm,
                      // value: chinaGeoCoordMap[dataItem.name].concat([dataItem.num])
                    };
                  }),
                },

                //被攻击点
                {
                  type: "scatter",
                  coordinateSystem: "geo",
                  zlevel: 2,
                  rippleEffect: {
                    period: 4,
                    brushType: "stroke",
                    scale: 4,
                  },
                  label: {
                    normal: {
                      show: true,
                      position: "left",
                      //offset:[5, 0],
                      color: "#fff",
                      formatter: "{b}",
                      textStyle: {
                        color: "#f00",
                        fontSize: 40,
                      },
                    },
                    emphasis: {
                      show: true,
                      color: "#f60",
                    },
                  },
                  symbol: "pin",
                  symbolSize: 60,
                  itemStyle: {
                    normal: {
                      color: "#f44336",
                    },
                  },
                  data: [
                    {
                      name: item[0],
                      value: outData[len].coord,
                      num: outData[len].num,
                      pm: outData[len].pm,
                    },
                  ],
                }
              );
            });
            this.myChart2.setOption(option);
            this.myChart2.getZr().on("mousemove", (param) => {
              this.myChart2.getZr().setCursorStyle("default");
            });
            // this.myChart3.setOption(option)
          },
          getInOrOut(id,flag) {
            // 0流出 1流入
            $get("/ggfw/yb/ydjz_1").then((res) => {
              let arr = [];
              res.map((ele, index) => {
                let obj = {
                  name: ele.cityName,
                  num: ele.countNum,
                  coord: [ele.gps.split(",")[0], ele.gps.split(",")[1]],
                  pm: index + 1,
                };
                arr.push(obj);
              });
              this.qgMapData_in = this.qgMapDataPop_in = arr;
              this.drawQgMap(
                id,
                this.qgMapData_in,
                "昨日省外城市人员流出情况",
                flag
              );
            });
          },
          convertData(data, chinaGeoCoordMap, id, dom) {
            // console.log(data)
            if (dom === "mainMap1") {
              var res = [];
              for (var i = 0; i < data.length; i++) {
                var dataItem = data[i];
                var fromCoord = dataItem.coord;
                var toCoord = id;
                if (fromCoord && toCoord) {
                  res.push([
                    {
                      coord: fromCoord,
                      value: dataItem.num,
                    },
                    {
                      coord: toCoord,
                    }
                  ]);
                }
              }
              return res;
            } else {
              var res = [];
              for (var i = 0; i < data.length; i++) {
                var dataItem = data[i];
                // var fromCoord = dataItem.coord;
                // var toCoord = id;
                var fromCoord = id;
                var toCoord = dataItem.coord;
                if (fromCoord && toCoord) {
                  res.push([
                    {
                      coord: toCoord,
                      value: dataItem.num,
                    },
                    {
                      coord: fromCoord,
                    },
                  ]);
                }
              }
              return res;
            }
          },        
        }
    })


</script>

</html>