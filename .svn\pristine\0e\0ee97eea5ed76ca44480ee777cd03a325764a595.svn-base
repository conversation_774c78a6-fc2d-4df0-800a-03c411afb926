function init(dom, data) {
    let uuid = dom.getAttribute('data-uuid');
    console.log(data);
    document.getElementsByClassName('projectName').innerText = data.name ? data.name : ''; // 项目名称
    // 重点归属
    let level = '';
    switch (data.level) {
        case 0:
            level = '普通项目';
            break;
        case 1:
            level = '市本级重点项目';
            break;
        case 2:
            level = '市级重点项目';
            break;
        case 3:
            level = '省级重点项目';
            break;
    }
    document.getElementsByClassName('projectLevel')[0].innerText = level || '普通项目'; // 责任单位
    document.getElementsByClassName('projectCompany')[0].innerText = data.company ? data.company : ''; // 责任单位
    document.getElementsByClassName('town_belong')[0].innerText = data.town_belong?data.town_belong:'';    // 建设地址
    document.getElementsByClassName('build_year')[0].innerText = data.build_year?data.build_year:'';    // 建设年限
    document.getElementsByClassName('value')[0].innerText = data.value?data.value+'万元':'-万元';   // 总投资
    document.getElementsByClassName('owner')[0].innerText = data.owner?data.owner:'';   // 项目业主
    document.getElementsByClassName('content')[0].innerText = data.content?data.content:'';   // 建设内容及规模
    document.getElementsByClassName('batch')[0].innerText = data.batch?data.batch:'';   // 项目业主
    document.getElementsByClassName('year_value')[0].innerText = data.year_value?data.year_value:'';   // 项目业主

    // console.log(document.querySelector('.videoBtn'));
    let showVideoBox = document.querySelector('.videoBtn')
    showVideoBox.addEventListener('click', () => {
        setTimeout(() => {
            document.querySelector('.videoBox').style.display = 'block'
        });

    })
    // 关闭视频
    document.querySelector('.closeVideoBox').addEventListener('click', () => {
        document.querySelector('.videoBox').style.display = 'none'
    })
    // 关闭弹窗
    dom.getElementsByClassName('close')[0].onclick = () => {
        Vex.Work.sprite.pointOverlay.removePopup(uuid)
    }
}