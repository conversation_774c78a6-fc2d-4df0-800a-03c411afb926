<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>安全生产事故分类统计-左侧</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <link rel="stylesheet" href="/static/citybrain/shgl/css/shgl-csaq-aqscsg-left.css" />
  </head>
  <style></style>

  <body>
    <div id="app" class="container" v-cloak>
      <nav>
        <s-header-title
          htype="1"
          title="事故类型统计展示"
          data-time="2022年2月11日">
        </s-header-title>
      </nav>
      <div class="sglx">
        <div class="sglx-left">
          <nav>
            <s-header-title-2
              htype=""
              title="安全生产事故各类型占比"
            ></s-header-title>
          </nav>
          <div id="chart01" style="width:100%;height:calc(100% - 73px)"></div>
        </div>
        <div class="sglx-right">
          <nav>
            <s-header-title-2
              htype=""
              title="安全生产事故严重程度占比"
            ></s-header-title>
          </nav>
          <div id="chart02" style="width:100%;height:calc(100% - 73px)"></div>
        </div>
      </div>  
      <nav>
        <s-header-title
          htype="1"
          title="时序变化趋势统计"
          data-time="2022年2月11日">
        </s-header-title>
      </nav>
      <div class="sxbh">
        <nav>
          <s-header-title-2
            htype="1"
            title="安全生产事故变化趋势"
          ></s-header-title>
        </nav>
        <div id="chart03" style="width:100%;height:calc(100% - 73px)"></div>
        <div class="tabs">
          <div style="cursor: pointer;" v-for="(item,index) in tabList" :key="index" @click="changeTab(index)"
              class="tab-item" :class="tabIndex===index?'tab-active':''">
            {{item}} 
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="bottom-left">
          <nav>
            <s-header-title
              htype="2"
              title="安全生产事件24小时分布"
            ></s-header-title>
          </nav>
          <div class="aqscsj-xsqs">
            <nav>
              <s-header-title-2
                htype="0"
                title="安全生产事件小时趋势"
              ></s-header-title>
            </nav>
            <div id="chart04" style="width:100%;height:calc(100% - 73px)"></div>
          </div>
        </div>
        <div class="bottom-right">
          <nav>
            <s-header-title
              htype="2"
              title="安全生产事件区域动态分布"
            ></s-header-title>
          </nav>
          <div class="aqscsj-hjysfx">
            <nav>
              <s-header-title-2
                htype="0"
                title="安全生产事件环境因素分析"
              ></s-header-title>
            </nav>
            <div id="chart05" style="width:100%;height:calc(100% - 73px)"></div>
          </div>
        </div>
      </div>
    </div>
  </body>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>

  <script>
    var vm = new Vue({
      el: "#app",
      data() {
        return {
          chartData1:[],
          chartData2:[],
          chartData3:[],
          chartData4:[],
          chartData5:[],
          tabIndex:0,
          tabList:['周度','月度','季度','年度']
        }
      },
      mounted() {
        this.init()
        this.flyTo()
        this.addPoint()
        top.document.getElementById("map").contentWindow.Work.change3D(7)
      },
      methods: {
        init(){
          $api('shgl_aqscsg_left01').then(res=>{
            this.chartData1 = res 
            this.getChart01('chart01',this.chartData1)  
          })
          $api('shgl_aqscsg_left02').then(res=>{
            this.chartData2 = res 
            this.getChart02('chart02',this.chartData2) 
          })
          $api('shgl_aqscsg_left03',{type:"周"}).then(res=>{
            this.chartData3 = res 
            this.getChart03('chart03',this.chartData3)  
          })
          $api('shgl_aqscsg_left04').then(res=>{
            this.chartData4 = res 
            this.getChart04('chart04',this.chartData4)
          })
          $api('shgl_aqscsg_left05').then(res=>{
            this.chartData5 = res 
            this.getChart05('chart05',this.chartData5)   
          })
        },
        changeTab(index){
          this.tabIndex=index
          if(this.tabIndex==0){
            $api('shgl_aqscsg_left03',{type:"周"}).then(res=>{
            this.chartData3 = res 
            this.getChart03('chart03',this.chartData3)  
          })
          }else if(this.tabIndex==1){
            $api('shgl_aqscsg_left03',{type:"月"}).then(res=>{
            this.chartData3 = res 
            this.getChart03('chart03',this.chartData3)  
          })
          }else if(this.tabIndex==2){
            $api('shgl_aqscsg_left03',{type:"季"}).then(res=>{
            this.chartData3 = res 
            this.getChart03('chart03',this.chartData3)  
          })
          }else if(this.tabIndex==3){
            $api('shgl_aqscsg_left03',{type:"年"}).then(res=>{
            this.chartData3 = res 
            this.getChart03('chart03',this.chartData3)  
          })
          }
        },
        getChart01(id,chartData){
          const myCharts = echarts.init(document.getElementById(id))
          let title = "总数";
          let color = ["#0E7CE2", "#FF8352", "#E271DE", "#F8456B", "#00FFFF", "#4AEAB0", "red"];
          let formatNumber = function (num) {
            let reg = /(?=(\B)(\d{3})+$)/g;
            return num.toString().replace(reg, ",");
          };
          let total = chartData.reduce((a, b) => {
            return a + b.value * 1;
          }, 0);
          option = {
            color: color,
            tooltip: {
                trigger: 'item',
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "rgba(212, 232, 254, 1)",
                  fontSize: 28,
                },
            },
            title: [
              {
                text: "{name|" + title + "}\n{val|" + formatNumber(total) + "}",
                top: "center",
                left: "65%",
                textStyle: {
                  rich: {
                    name: {
                      fontSize: 40,
                      fontWeight: "normal",
                      color: "#fff",
                      padding: [10, 0],
                    },
                    val: {
                      fontSize: 28,
                      fontWeight: "bold",
                      color: "#fff",
                      padding: [10, 10],
                    },
                  },
                },
              },
              {
                text: "",
                top: 20,
                left: 20,
                textStyle: {
                  fontSize: 28,
                  color: "#666666",
                  fontWeight: 400,
                },
              },
            ],
            legend: {
              orient: "vartical",
              x: "left",
              // top: "40%",
              left: "10%",
              bottom: "30%",
              itemGap: 40,
              itemWidth: 20,
              itemHeight: 20,
              textStyle: {
                  color: "#fff",
                  fontSize:"24",
              },
            },
            series: [
              {
                type: "pie",
                radius: ["45%", "65%"],
                center: ["70%", "50%"],
                data: chartData,
                hoverAnimation: true,
                itemStyle: {
                  normal: {
                    borderWidth: 0,
                  },
                },
                labelLine: {
                  normal: {
                    show:false,
                  },
                },
                label: {
                  normal: {
                    show:false
                  },
                },
              },
            ],
          };
          myCharts.setOption(option);
          myCharts.getZr().on('mousemove', param => {
            myCharts.getZr().setCursorStyle('default')
          })
        },
        getChart02(id,chartData){
          const myCharts = echarts.init(document.getElementById(id))
          let option = {
              grid: {
                  // left: "15%",
                  right: 22,
                  // top: "30%",
                  bottom: "20%",
                  containLabel: true,
              },
              tooltip: {
                trigger: 'item',
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "rgba(212, 232, 254, 1)",
                  fontSize: 28,
                },
              },
              color: ["#EE752F","#5087EC", "#68BBC4", "#58A55C", "#F2BD42" ],
              legend: {
                orient: "vartical",
                x: "left",
                left: "10%",
                bottom: "30%",
                itemGap: 40,
                itemWidth: 20,
                itemHeight: 20,
                textStyle: {
                    color: "#fff",
                    fontSize:"24",
                },
              },
              series: [
                {
                  name: "",
                  type: "pie",
                  radius: "65%",
                  center: ["60%", "50%"],
                  data: chartData,
                  itemStyle: {
                      color: '#fff',
                      emphasis: {
                          shadowBlur: 10,
                          shadowOffsetX: 0,
                          shadowColor: "rgba(0, 0, 0, 0.5)",
                      },
                  },
                  itemStyle: {
                      normal: {
                          label: {
                              show: true,
                              color: '#fff',
                              fontSize:"28px",
                              formatter: "{b}\n{d}%",
                          },
                      },
                      labelLine: { show: true },
                  },
                },
              ],
          };
          myCharts.setOption(option);
          myCharts.getZr().on('mousemove', param => {
            myCharts.getZr().setCursorStyle('default')
          })
        },
        getChart03(id,chartData){
          const myCharts = echarts.init(document.getElementById(id))
          let datax = [],
              datay = [];
            chartData.map((ele) => {
              datax.push(ele.name);
              datay.push(ele.value);
            });
            let option = {
              title: {
                show: false,
                text: "用电量",
              },
              legend: {
                show: true,
                x: "center",
                y: "10",
                itemWidth: 20,
                itemHeight: 20,
                textStyle: {
                    color: "#fff",
                    fontSize:"24",
                },
              },
              grid: {
                left: "3%",
                right: "4%",
                bottom: "10%",
                containLabel: true,
              },
              tooltip: {
                trigger: "axis",
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                axisPointer: {
                  lineStyle: {
                    color: "rgba(11, 208, 241, 1)",
                    type: "slider",
                  },
                },
                textStyle: {
                  color: "rgba(212, 232, 254, 1)",
                  fontSize: 28,
                },
              },
              xAxis: [
                {
                  type: "category",
                  offset: 20,
                  axisLine: {
                    //坐标轴轴线相关设置。数学上的x轴
                    show: true,
                    lineStyle: {
                      color: "rgba(108, 166, 219, 0.3)",
                    },
                  },
                  axisLabel: {
                    //坐标轴刻度标签的相关设置
                    textStyle: {
                      color: "#fff",
                      fontSize: 24,
                    },
                  },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      color: "#192a44",
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  data: datax,
                },
              ],
              yAxis: [
                {
                  name: "",
                  nameTextStyle: {
                    fontSize: 24,
                    color: "#D6E7F9",
                    padding: [0, 20, 10, 0],
                  },
                  splitLine: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.1,
                      width: 2,
                    },
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.5,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 24,
                      color: "#D6E7F9",
                    },
                  },
                  axisLine: {
                    show: false,
                    lineStyle: {
                      color: "#233653",
                    },
                  },
                },
              ],
              series: [
                {
                  name: "事故",
                  type: "line",
                  itemStyle: {
                    normal: {
                      color: "#3A84FF",
                      lineStyle: {
                        color: "#1b759c",
                        width: 4,
                      },
                      areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                          {
                            offset: 0,
                            color: "rgba(2, 92, 131,0.9)",
                          },
                          {
                            offset: 1,
                            color: "rgba(2, 92, 131,0.2)",
                          },
                        ]),
                      },
                    },
                  },
                  data: datay,
                },
              ],
            };
          myCharts.setOption(option);
          myCharts.getZr().on('mousemove', param => {
            myCharts.getZr().setCursorStyle('default')
          })
        },
        getChart04(id,chartData){
          const myCharts = echarts.init(document.getElementById(id))
          let x=chartData.map((item)=>{
              return item.name;
          })
          let y1=chartData.map((item)=>{
              return item.value1;
          })
          let y2=chartData.map((item)=>{
              return item.value2;
          })
          let option = {
              grid: {
                  left: "5%",
                  right: "10%",
                  top: "20%",
                  bottom: "10%",
                  containLabel: true,
              },
              tooltip: {
              trigger: "axis",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                  color: "white",
                  fontSize: "28",
              },
              },
              legend: {
                  show: true,
                  x: "center",
                  y: "10",
                  itemWidth: 20,
                  itemHeight: 20,
                  textStyle: {
                      color: "#fff",
                      fontSize:"24",
                  },
              },
              xAxis: [
                {
                  type: "category",
                  offset: 20,
                  axisLine: {
                    //坐标轴轴线相关设置。数学上的x轴
                    show: true,
                    lineStyle: {
                      color: "rgba(108, 166, 219, 0.3)",
                    },
                  },
                  axisLabel: {
                    //坐标轴刻度标签的相关设置
                    textStyle: {
                      color: "#fff",
                      fontSize: 28,
                    },
                  },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      color: "#192a44",
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  data: x,
                },
              ],
              yAxis: [
                {
                  name: "单位：件",
                  nameTextStyle: {
                    fontSize: 28,
                    color: "#D6E7F9",
                  },
                  splitLine: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.1,
                      width: 2,
                    },
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.5,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 28,
                      color: "#D6E7F9",
                    },
                  },
                  axisLine: {
                    show: false,
                    lineStyle: {
                      color: "#233653",
                    },
                  },
                },
              ],
              series: [
                  {
                      name: "发生数",
                      type: "line",
                      // symbol: "circle",
                      symbolSize: 10,
                      itemStyle: {
                          normal: {
                              color: "#5087EC",
                              lineStyle: {
                                  color: "#5087EC",
                                  width: 4,
                              },
                          },
                      },
                      data: y1,
                  },{
                      name: "伤亡数",
                      type: "line",
                      stack: "总量",
                      // symbol: "circle",
                      symbolSize: 10,
                      itemStyle: {
                          normal: {
                              color: "#00ca95",
                              lineStyle: {
                                  color: "#00ca95",
                                  width: 4,
                              },
                          },
                      },
                      data: y2,
                  },
              ],
          };
          myCharts.setOption(option);
          myCharts.getZr().on('mousemove', param => {
            myCharts.getZr().setCursorStyle('default')
          })
        },
        getChart05(id,chartData){
          const myCharts = echarts.init(document.getElementById(id))
          var legend = ["政策因素", "经济因素", "规划因素"];
          var colorList = ['#5087EC', '#68BBC4','#58A55C'];
          var data = [];
          let x = chartData.map((item) => {
              return item.name;
          })
          let y1 = chartData.map((item) => {
              return item.value1;
          })
          let y2 = chartData.map((item) => {
              return item.value2;
          })
          let y3 = chartData.map((item) => {
              return item.value3;
          })
          data.push(y1, y2, y3);
          let option = {
              tooltip: {
                  trigger: "item",
                  borderWidth: 0,
                  backgroundColor: "rgba(0, 0, 0, 0.6)",
                  textStyle: {
                      color: "white",
                      fontSize: "28",
                  },
              },
              // color: colors,
              legend: {
                  x: "center",
                  y: "15",
                  itemWidth: 20,
                  itemHeight: 20,
                  textStyle: {
                      color: "#fff",
                      fontSize: 24,
                  },
                  data: legend,
              },
              grid: {
                  left: "3%",
                  right: "4%",
                  bottom: "5%",
                  top:"20%",
                  containLabel: true,
              },
              xAxis: {
                  type: "category",
                  axisLabel: {
                      color: '#fff',
                      fontSize: 28,
                      // rotate: 45,
                  },
                  axisLine: {
                      show:true,
                      lineStyle: {
                          color: "#bbb",
                      },
                  },
                  splitLine: {
                      show: false,
                      lineStyle: {
                          color: "#195384",
                      },
                  },
                  data: x,
              },
              yAxis: {
                  type: "value",
                  name: "单位:件",
                  nameTextStyle: {
                      color: "#fff",
                      fontSize: 28,
                  },
                  axisLabel: {
                      formatter: "{value}",
                      textStyle: {
                          color: "#fff",
                          fontSize: 28
                      },
                  },
                  axisLine: {
                      lineStyle: {
                          color: "#fff",
                      },
                  },
                  axisTick: {
                      show: false,
                  },
                  splitLine: {
                      show: true,
                      lineStyle: {
                          color: "#11366e",
                      },
                  },
              },
              series: [],
            };
            for (var i = 0; i < legend.length; i++) {
              option.series.push({
                  name: legend[i],
                  type: "bar",
                  stack: "总量",
                  barWidth: 40,
                  itemStyle: {
                      normal: {
                          color: colorList[i]
                      },
                  },
                  label: {
                      show: true,
                      position: "inside",
                      textStyle: {
                          color: "#fff",
                          fontSize: 24
                      },
                  },
                  data: data[i],
              });
            }
          myCharts.setOption(option);
          myCharts.getZr().on('mousemove', param => {
            myCharts.getZr().setCursorStyle('default')
          })
        },
      
        //飞入
        flyTo(){
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "flyto", //功能名称
              flyData: {
                center: [119.95478050597587, 29.01613226366889],
                zoom: 10.5,
                pitch: 40,
                bearing: 0,
              },
            })
          )
        },


        addPoint(){
          let res = [
            {
              title: '浦江县',
              gps_x: "119.94315399169922",
              gps_y: "29.5630503845215",
            },
            {
              title: '兰溪市',
              gps_x: "119.46214447021484",
              gps_y: "29.31345558166504",
            },
            {
              title: '婺城区',
              gps_x: "119.5569204711914",
              gps_y: "29.00677101135254",
            },
            {
              title: '金义新区',
              gps_x: "119.8483056640625",
              gps_y: "29.188559951782227",
            },
            {
              title: '义乌市',
              gps_x: "120.08206787109375",
              gps_y: "29.322123641967773",
            },
            {
              title: '武义县',
              gps_x: "119.7269204711914",
              gps_y: "28.79677101135254",
            },
            {
              title: '永康市',
              gps_x: "120.1469204711914",
              gps_y: "28.97677101135254",
            },
            {
              title: '东阳市',
              gps_x: "120.4169204711914",
              gps_y: "29.24677101135254",
            },
            {
              title: '磐安县',
              gps_x: "120.6299204711914",
              gps_y: "29.06677101135254",
            },
            {
              title: '婺城区',
              gps_x: "119.64896993689013",
              gps_y: "29.090742350540673",
            },
            {
              title: '婺城区',
              gps_x: "119.65305962805735",
              gps_y: "29.07810148693506",
            }
          ];
          let arr = res.map((item) => {
            return {
              data: {},
              point: item.gps_x + "," + item.gps_y,
            };
          });
          console.log(arr);
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "pointLoad",
              pointType: "water", // 点位类型（图标名称）
              pointId: "point1", // 点位唯一id
              setClick: true,
              pointData: arr,
              imageConfig: { iconSize: 1 },
              popup:{
                offset:[50,-100]
              }
            })
          );
        },

        //清除点位
        rmPoint(){
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName:"rmPoint" ,
              pointId: "",
            })
          )
        },


      },
      destroyed(){
        this.rmPoint()
      }
    });
  </script>
</html>
