<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>市场监管</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <style>
            * {
                padding: 0;
                margin: 0;
            }
            #scjg-right-dialog01 {
                width: 1500px;
                height: 650px;
                background-color: #0a2443;
                border: 4px solid #17cddd;
                border-radius: 40px;
                box-sizing: border-box;
            }
            .table {
                font-size: 30px;
                width: 700px !important;
                margin: auto;
                color: #fff;
                border-collapse: collapse;
                text-align: center;
                border: 1px solid #0087ec;
            }

            .title {
                position: relative;
            }

            .close {
                position: absolute;
                right: 40px;
                top: 20px;
                font-size: 40px;
                color: #fff;
                font-weight: 700;
                cursor: pointer;
            }
        </style>
    </head>

    <body>
        <div id="scjg-right-dialog01">
            <div class="content">
                <div class="title" style="display: flex">
                    <nav style="padding: 20px 0px; flex: 1">
                        <s-header-title2 style="width: 100%" title="重点企业监管市场情况" htype="2"></s-header-title2>
                    </nav>
                    <nav style="padding: 20px 0px; flex: 1">
                        <s-header-title2 style="width: 100%" title="重点企业监管情况" htype="2"></s-header-title2>
                    </nav>
                    <div class="close" @click="close">X</div>
                </div>
                <div style="display: flex">
                    <div id="barEcharts01" style="height: 520px; width: 100%; flex: 1"></div>
                    <div style="flex: 1">
                        <table border class="table">
                            <thead>
                                <tr>
                                    <th v-for="item in thName">{{item}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="item in tableList">
                                    <td>{{item.name}}</td>
                                    <td>{{item.qymc}}</td>
                                    <td>{{item.jg}}</td>
                                    <td>{{item.value}}</td>
                                    <td>{{item.jyqk}}</td>
                                    <td>{{item.xypj}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#scjg-right-dialog01",
        data: {
            thName: ["企业类型", "企业名称", "双随机一公开检查结果", "消费者维权情况", "企业经营情况", "企业信用评级"],
            tableList: [],
        },
        mounted() {
            this.initFun();
        },
        methods: {
            initFun() {
                // $api("ldst_scjg_scjg", { type: 28 }).then((res) => {
                //     this.getEcharts01(res);
                // });
                // $api("ldst_scjg_scjg", { type: 29 }).then((res) => {
                //     this.tableList = res;
                // });
                $get("3840/scjg/scjg28").then((res) => {
                    this.getEcharts01(res);
                });
                $get("3840/scjg/scjg29").then((res) => {
                    this.tableList = res;
                });
            },
            close() {
                top.commonObj.funCloseIframe({
                    name: "scjg-right-dialog01",
                });
            },

            getEcharts01(res) {
                let myCharts = echarts.init(document.getElementById("barEcharts01"));
                let yData = res.map((item) => {
                    return item.name;
                });
                let value = res.map((item) => {
                    return item.value;
                });

                let option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    grid: {
                        bottom: "16%",
                        left: "20%",
                        right: "0%",
                    },
                    yAxis: [
                        {
                            type: "value",

                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                formatter: "{value}万户",
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                    ],

                    xAxis: {
                        type: "category",
                        data: yData,
                        offset: 15,
                        axisLabel: {
                            fontSize: 30,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            name: "户数",
                            type: "bar",
                            data: value,
                        },
                    ],
                };
                myCharts.setOption(option);
                tools.loopShowTooltip(myCharts, option, { loopSeries: true });
            },
        },
    });
</script>
