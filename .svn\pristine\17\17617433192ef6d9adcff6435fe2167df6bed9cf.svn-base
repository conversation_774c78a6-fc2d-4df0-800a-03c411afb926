<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>一体化智能平台-右</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
      }

      #app {
        width: 1050px;
        height: 1930px;
        background: url('/img/right-bg.png') no-repeat;
        background-size: 100% 100%;
        padding: 30px;
        box-sizing: border-box;
        overflow: hidden;
      }

      .line1 {
        width: 100%;
        height: 500px;
        display: flex;
        justify-content: space-evenly;
        flex-wrap: wrap;
      }

      .line2,
      .line3 {
        width: 100%;
        height: 600px;
      }

      .item1 {
        width: 40%;
        min-height: 150px;
        background-color: #0087ec;
        /* padding: 30px 0; */
        box-sizing: border-box;
        margin-bottom: 20px;
      }

      .item1 > p {
        text-align: center;
        font-size: 30px;
        color: white;
        margin: 15px 0;
      }

      .el-select .el-input .el-input__inner {
        font-size: 24px;
      }

      .el-select-dropdown__item {
        font-size: 24px;
      }

      .line2 .picktime {
        float: right;
        margin-left: 50px;
      }

      .line2 .zhi {
        float: right;
        font-size: 24px;
        color: white;
        margin: 0 20px 0 20px;
        box-sizing: border-box;
      }

      .predictButton {
        width: 250px;
        height: 40px;
        float: right;
        text-align: center;
        margin-left: 50px;
        padding-top: 2px;
        box-sizing: border-box;
        background-color: #0087ec;
        border-radius: 10px;
      }

      .predictButton span {
        color: white;
        font-size: 24px;
      }
      nav {
        margin: 30px 0;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <nav>
        <s-header-title title="信息系统展示" htype="2"></s-header-title>
      </nav>

      <div class="line1">
        <div class="item1" v-for="(item,index) in line1List" :key="index">
          <p>{{item.value + item.unit}}</p>
          <p>{{item.name}}</p>
        </div>
      </div>

      <div class="line2">
        <nav>
          <s-header-title-2
            title="系统访问量变化分析"
            htype="0"
          ></s-header-title-2>
        </nav>
        <div class="picktime">
          <div v-show="value1==='1'">
            <el-select
              v-model="time2"
              style="width: 150px; float: right"
              @change="getlineEchart1()"
            >
              <el-option
                v-for="(item,index) in optionsMonth"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <span class="zhi">至</span>
            <el-select
              v-model="time1"
              style="width: 150px; float: right"
              @change="getlineEchart1()"
            >
              <el-option
                v-for="(item,index) in optionsMonth"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
          <div v-show="value1==='2'">
            <el-select
              v-model="time2"
              style="width: 150px; float: right"
              @change="getlineEchart1()"
            >
              <el-option
                v-for="(item,index) in optionsMonth1"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <span class="zhi">至</span>
            <el-select
              v-model="time1"
              style="width: 150px; float: right"
              @change="getlineEchart1()"
            >
              <el-option
                v-for="(item,index) in optionsMonth1"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
          <div v-show="value1==='3'">
            <el-select
              v-model="time2"
              style="width: 150px; float: right"
              @change="getlineEchart1()"
            >
              <el-option
                v-for="(item,index) in optionsMonth2"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <span class="zhi">至</span>
            <el-select
              v-model="time1"
              style="width: 150px; float: right"
              @change="getlineEchart1()"
            >
              <el-option
                v-for="(item,index) in optionsMonth2"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>
        <el-select
          v-model="value1"
          placeholder="时间范围"
          style="width: 150px; float: right"
          @change="change"
        >
          <el-option
            v-for="(item,index) in options"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <div
          id="chart01"
          style="width: 100%; height: 400px; position: relative; top: 50px"
        ></div>
      </div>

      <div class="line3">
        <nav>
          <s-header-title-2
            title="系统注册人数分析"
            htype="0"
          ></s-header-title-2>
        </nav>
        <div class="predictButton" @click="getlineEchart2()">
          <span>预测注册人数</span>
        </div>
        <el-select
          v-model="value2"
          placeholder="区域"
          style="width: 150px; float: right"
          @change="change1"
        >
          <el-option
            v-for="(item,index) in options1"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <div
          id="chart02"
          style="width: 100%; height: 400px; position: relative; top: 50px"
        ></div>
      </div>
    </div>
  </body>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>
    let vm = new Vue({
      el: '#app',
      data: {
        line1List: [],
        time1: '',
        time2: '',
        optionsIs: '',
        optionsMonth: [
          { value: '1', label: '1月' },
          { value: '2', label: '2月' },
          { value: '3', label: '3月' },
          { value: '4', label: '4月' },
          { value: '5', label: '5月' },
          { value: '6', label: '6月' },
          { value: '7', label: '7月' },
          { value: '8', label: '8月' },
          { value: '9', label: '9月' },
          { value: '10', label: '10月' },
          { value: '11', label: '11月' },
          { value: '12', label: '12月' },
        ],
        optionsMonth1: [
          { value: '1', label: '第一周' },
          { value: '2', label: '第二周' },
          { value: '3', label: '第三周' },
          { value: '4', label: '第四周' },
          { value: '5', label: '第一周' },
          { value: '6', label: '第二周' },
          { value: '7', label: '第三周' },
          { value: '8', label: '第四周' },
        ],
        optionsMonth2: [
          { value: '1', label: '1日' },
          { value: '2', label: '2日' },
          { value: '3', label: '3日' },
          { value: '4', label: '4日' },
          { value: '5', label: '5日' },
          { value: '6', label: '6日' },
          { value: '7', label: '7日' },
          { value: '8', label: '8日' },
          { value: '9', label: '9日' },
        ],
        options: [
          {
            value: '1',
            label: '月',
          },
          {
            value: '2',
            label: '周',
          },
          {
            value: '3',
            label: '日',
          },
        ],
        options1: [
          {
            value: '1',
            label: '月',
          },
          {
            value: '2',
            label: '周',
          },
          {
            value: '3',
            label: '日',
          },
        ],
        value1: '1',
        value2: '1',
        date: '1',
      },
      mounted() {
        this.init()
      },
      methods: {
        change(item) {
          console.log(item)
          if (item === '1') {
            $api('ldst_shgl_ythznpt', { type: '8-1' }).then((res) => {
              this.lineEchart('chart01', res, '次')
            })
          } else if (item === '2') {
            $api('ldst_shgl_ythznpt', { type: '8-2' }).then((res) => {
              this.lineEchart('chart01', res, '次')
            })
          } else if (item === '3') {
            $api('ldst_shgl_ythznpt', { type: '8-3' }).then((res) => {
              this.lineEchart('chart01', res, '次')
            })
          }
        },
        change1(item) {
          if (item === '1') {
            $api('ldst_shgl_ythznpt', { type: '9-1' }).then((res) => {
              this.lineEchart2('chart02', res, '次', '系统用户注册数')
            })
          } else if (item === '2') {
            $api('ldst_shgl_ythznpt', { type: '9-2' }).then((res) => {
              this.lineEchart2('chart02', res, '次', '系统用户注册数')
            })
          } else if (item === '3') {
            $api('ldst_shgl_ythznpt', { type: '9-3' }).then((res) => {
              this.lineEchart2('chart02', res, '次', '系统用户注册数')
            })
          }
        },
        getlineEchart1() {
          if (this.time1 != '' && this.time2 != '') {
            if (this.time1 < this.time2) {
              if (this.value1 === '1') {
                $api('ldst_shgl_ythznpt', { type: '8-1' }).then((res) => {
                  res = res.slice(this.time1 - 1, this.time2)
                  this.lineEchart('chart01', res, '次')
                })
              } else if (this.value1 === '2') {
                $api('ldst_shgl_ythznpt', { type: '8-2' }).then((res) => {
                  res = res.slice(this.time1 - 1, this.time2)
                  this.lineEchart('chart01', res, '次')
                })
              } else if (this.value1 === '3') {
                $api('ldst_shgl_ythznpt', { type: '8-3' }).then((res) => {
                  res = res.slice(this.time1 - 1, this.time2)
                  this.lineEchart('chart01', res, '次')
                })
              }
            } else {
              this.lineEchart('chart01', [], '次')
            }
          }
        },
        getlineEchart2() {
          //   $api("ldst_shgl_ythznpt9", { type1: "月" }).then((res) => {
          //         this.lineEchart2("chart02", res, "次","预测用户注册数");
          //   });
          $get('/3840/shgl/ythznpt/ythznpt09-1').then((res) => {
            this.lineEchart2('chart02', res, '次', '预测用户注册数')
          })
          let res = [
            { name: '11月', value: '120' },
            { name: '12月', value: '100' },
            { name: '1月', value: '104' },
            { name: '2月', value: '80' },
            { name: '3月', value: '103' },
            { name: '4月', value: '110' },
          ]
          this.lineEchart2('chart02', res, '次', '预测用户注册数')
        },
        init() {
          $api('ldst_shgl_ythznpt', { type: 7 }).then((res) => {
            this.line1List = res
          })
          $api('ldst_shgl_ythznpt', { type: '8-1' }).then((res) => {
            this.lineEchart('chart01', res, '次')
          })
          $api('ldst_shgl_ythznpt', { type: '9-1' }).then((res) => {
            this.lineEchart2('chart02', res, '人', '系统用户注册数')
          })
        },
        lineEchart(id, lineData, unit) {
          const myEc = echarts.init(document.getElementById(id))
          let datax = [],
            datay = []
          lineData.map((ele) => {
            datax.push(ele.name)
            datay.push(ele.value)
          })
          let option = {
            legend: {
              show: true,
              x: 'center',
              y: '10',
              itemWidth: 40,
              itemHeight: 20,
              textStyle: {
                color: '#fff',
                fontSize: '28',
              },
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '8%',
              containLabel: true,
            },
            tooltip: {
              trigger: 'axis',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              axisPointer: {
                lineStyle: {
                  color: 'rgba(11, 208, 241, 1)',
                  type: 'slider',
                },
              },
              textStyle: {
                color: 'rgba(212, 232, 254, 1)',
                fontSize: 28,
              },
            },
            xAxis: [
              {
                type: 'category',
                offset: 20,
                axisLine: {
                  //坐标轴轴线相关设置。数学上的x轴
                  show: true,
                  lineStyle: {
                    color: 'rgba(108, 166, 219, 0.3)',
                  },
                },
                axisLabel: {
                  //坐标轴刻度标签的相关设置
                  // rotate: -30,
                  textStyle: {
                    color: '#fff',
                    fontSize: 28,
                  },
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    color: '#192a44',
                  },
                },
                axisTick: {
                  show: false,
                },
                data: datax,
              },
            ],
            yAxis: [
              {
                name: '单位:' + unit,
                min: (value) => {
                  return parseInt(value.min - 1)
                },
                nameTextStyle: {
                  fontSize: 24,
                  color: '#D6E7F9',
                  padding: [0, 20, 10, 0],
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: '#77b3f1',
                    opacity: 0.1,
                    width: 2,
                  },
                },
                axisTick: {
                  show: true,
                  lineStyle: {
                    color: '#77b3f1',
                    opacity: 0.5,
                    width: 2,
                  },
                },
                axisLabel: {
                  textStyle: {
                    fontSize: 28,
                    color: '#D6E7F9',
                  },
                },
                axisLine: {
                  show: false,
                  lineStyle: {
                    color: '#233653',
                  },
                },
              },
            ],
            series: [
              {
                name: '系统访问数',
                type: 'line',
                symbolSize: 10,
                itemStyle: {
                  normal: {
                    // color: "#3A84FF",
                    lineStyle: {
                      // color: "#1b759c",
                      width: 2,
                    },
                  },
                },
                data: datay,
              },
            ],
          }
          myEc.setOption(option)
        },
        lineEchart2(id, lineData, unit, legend) {
          const myEc = echarts.init(document.getElementById(id))
          let datax = [],
            datay = []
          lineData.map((ele) => {
            datax.push(ele.name)
            datay.push(ele.value)
          })
          let option = {
            legend: {
              show: true,
              x: 'center',
              y: '10',
              itemWidth: 40,
              itemHeight: 20,
              textStyle: {
                color: '#fff',
                fontSize: '28',
              },
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '8%',
              containLabel: true,
            },
            tooltip: {
              trigger: 'axis',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              axisPointer: {
                lineStyle: {
                  color: 'rgba(11, 208, 241, 1)',
                  type: 'slider',
                },
              },
              textStyle: {
                color: 'rgba(212, 232, 254, 1)',
                fontSize: 28,
              },
            },
            xAxis: [
              {
                type: 'category',
                offset: 20,
                axisLine: {
                  //坐标轴轴线相关设置。数学上的x轴
                  show: true,
                  lineStyle: {
                    color: 'rgba(108, 166, 219, 0.3)',
                  },
                },
                axisLabel: {
                  //坐标轴刻度标签的相关设置
                  // rotate: -30,
                  textStyle: {
                    color: '#fff',
                    fontSize: 28,
                  },
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    color: '#192a44',
                  },
                },
                axisTick: {
                  show: false,
                },
                data: datax,
              },
            ],
            yAxis: [
              {
                name: '单位:' + unit,
                min: (value) => {
                  return parseInt(value.min - 1)
                },
                nameTextStyle: {
                  fontSize: 24,
                  color: '#D6E7F9',
                  padding: [0, 20, 10, 0],
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: '#77b3f1',
                    opacity: 0.1,
                    width: 2,
                  },
                },
                axisTick: {
                  show: true,
                  lineStyle: {
                    color: '#77b3f1',
                    opacity: 0.5,
                    width: 2,
                  },
                },
                axisLabel: {
                  textStyle: {
                    fontSize: 28,
                    color: '#D6E7F9',
                  },
                },
                axisLine: {
                  show: false,
                  lineStyle: {
                    color: '#233653',
                  },
                },
              },
            ],
            series: [
              {
                name: legend,
                type: 'line',
                symbolSize: 10,
                itemStyle: {
                  normal: {
                    // color: "#3A84FF",
                    lineStyle: {
                      // color: "#1b759c",
                      width: 2,
                    },
                  },
                },
                data: datay,
              },
            ],
          }
          myEc.setOption(option)
        },
      },
    })
  </script>
</html>
