var commonObj = {
  videoList: [],
  userId: '',
  Authorization: '',
  ptid: 'PT0001',
  token: '',
  title: '',
  userInfo: {
    deptName: '',
    nickName: '',
  },
  pageConfig: {},
  menuLoadSus: true,
  lastDestroyed: true,
  menuTopics: null,
  dontDestroyFrameNames: [],
  __MapContentWindow: null,
  //获取接口
  init: function () {
    $.ajax({
      url: 'getAjaxRequestIp',
      dataType: 'json',
      type: 'get',
      async: false,
      success: function (data) {
        commonObj.pageConfig = data.pageConfig
      },
      error: function (e) {
        console.log(e)
      },
    })
  },

  /**
   * * 生成一个不重复的ID
   *  @param { Number } randomLength
   */
  getUUID: (randomLength = 10) => {
    let uuid = Number(
      Math.random().toString().substr(2, randomLength) + Date.now()
    ).toString(36)
    return uuid
  },

  //获取菜单
  async funSearchInfo(path) {
    $('#path').append('path：' + JSON.stringify(path) + '<br/>')

    let argument = `?indexid=index001&url=${path}&userId=${commonObj.userId}`
    let res = await axios({
      method: 'get',
      url: commonObj.pageConfig['menuurl'] + argument,
    })
    return res.data.data
  },
  //自定义布局菜单
  async funLayoutSearchInfo(path) {
    let argument = `?path=${path}`
    let res = await axios({
      method: 'get',
      url: commonObj.pageConfig['vitemenuurl'] + argument,
      headers: {
        Authorization: commonObj.Authorization,
      },
    })
    // if(res.data.code ==401){
    //   await axios({
    //     method: 'get',
    //     url: commonObj.pageConfig['refreshToken'] ,
    //     headers: {
    //       Authorization: commonObj.Authorization,
    //     },
    //   })
    // }
    if (res.data && res.data.rows.length > 0) {
      let row = res.data.rows[0]
      let tmpBoxs = row.tmpBoxs.map((item) => {
        return {
          x: item.boxX,
          y: item.boxY,
          w: item.boxWidht,
          h: item.boxHeight,
          i: item.boxId,
          url: item.url,
        }
      })
      let data = [
        {
          boxs: tmpBoxs,
          colNum: row.colNum,
          htmlH: row.htmlHeight,
          htmlW: row.htmlWidth,
          rowNum: row.rowNum,
          path: row.path,
        },
      ]
      return data
    } else {
      return []
    }
  },

  //菜单get
  async getMenuMap(url) {
    if (!commonObj.menuTopics.has(url)) {
      let arr = await commonObj.funSearchInfo(url)
      // for (let i = 0; i < arr.length; i++) {
      //   let res = arr[i]
      //   commonObj.setMenuMap(res.url, res)
      // }
      commonObj.setMenuMap(url, arr)
      return commonObj.menuTopics.get(url)
    }
    return commonObj.menuTopics.get(url)
  },
  async getMenuObj(mapUrl, url) {
    let arr = await commonObj.getMenuMap(mapUrl)
    let result = null
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].url == url) {
        result = arr[i]
        break
      }
    }
    return result
  },
  //菜单set
  setMenuMap(type, menu) {
    if (!commonObj.menuTopics.has(type)) {
      commonObj.menuTopics.set(type, menu)
    }
  },
  hideMap() {
    $('#main-bg').css('z-index', 5)
  },
  showMap() {
    $('#main-bg').css('z-index', 1)
  },
  async loadHtml(mapUrl, url) {
    let menuObj = await commonObj.getMenuObj(mapUrl, url)
    let arr = []

    commonObj.destroyHtml()
    commonObj.title = menuObj.name || ''

    if (menuObj.isMap == 1) {
      commonObj.hideMap()
    } else {
      commonObj.showMap()
    }

    if (menuObj.isStatus == 1) {
      arr = await commonObj.funLayoutSearchInfo(url)
      commonObj.readType2(arr)
    } else {
      commonObj.readType1(menuObj)
    }
  },
  openWinHtml: function (width, hight, url) {
    width = Number(width)
    hight = Number(hight)
    let left = (3840 - width) / 2
    let top = (2160 - hight) / 2
    let params = {
      left: left + 'px',
      src: url,
      top: top + 'px',
      zIndex: '999',
      width: width + 'px',
      height: hight + 'px',
      name: 'winh',
    }
    commonObj.updateModalOverlayZindex(998)
    commonObj.funOpenIframe(params)

    document.documentElement.style.setProperty('--moveTop', top + 'px')
    document.documentElement.style.setProperty('--moveBottom', hight + 'px')

    let dom = `<div id="opendiv">
    <div class="top-close"  onclick="commonObj.closeWinHtml()"  style="position: absolute;z-index:1000;top:${
      top + 'px'
    };left:${left + width - 150 + 'px'}"  ></div>
    <div class="icon-shine-l" style="top:${top + 'px'};left:${
      left - 412 + 'px'
    }"></div>
    <div class="icon-shine-l icon-shine-r" style="top:${hight + 'px'};left:${
      left + width - 412 + 'px'
    }"></div>
    </div>`
    $('#opendiv').remove()
    $('#mainPage').append(dom)
  },
  closeWinHtml: function () {
    commonObj.updateModalOverlayZindex(0)
    commonObj.funCloseIframe({ name: 'winh' })
    $('#opendiv').remove()
  },
  /**
   * 遮罩层
   */
  updateModalOverlayZindex: function (zIndex = 998) {
    $('#modal-overlay').css('z-index', zIndex)
  },
  /**
   * 创建底部菜单
   * @param {Array}  menuArr 菜单数组  {name:'菜单',path:'zdxm'}
      [{name:'菜单',path:'zdxm'}]
     @param {Array}  isDestroy  点击切换菜单是否销毁 defult true
   */
  createBottomMenubar: function (menuArr, isDestroy = true) {
    let p = {
      type: 'openIframe',
      name: 'navFrame',
      src: '/static/citybrain/tckz/navFrame.html',
      left: '2443px',
      top: '2070px',
      width: '3740px',
      height: '152px',
      zIndex: '100',
      argument: {
        action: 'setNav',
        params: menuArr,
      },
      callback: null,
    }
    commonObj.funOpenIframe(p)
  },
  readType1: function (menuObj) {
    let lis = []

    $('.menu_2').html('')
    $('.menu_3').html('')
    menuObj.child.map((ite) => {
      if (ite.id == 'left') {
        lis.push(
          `<iframe   width="${ite.width}" height="${ite.height}" src="${ite.src}" id=${menuObj.url}${ite.id} class="page_${ite.id} animated fadeInLeft" frameborder="0"  scrolling="no"></iframe>`
        )
      } else if (ite.id == 'right') {
        lis.push(
          `<iframe    width="${ite.width}" height="${ite.height}" src="${ite.src}" id=${menuObj.url}${ite.id} class="page_${ite.id} animated fadeInRight" frameborder="0"  scrolling="no"></iframe>`
        )
      }
      ;`${menuObj.url}${ite.id}`
    })
    $('#mainPage').append(lis)

    $('.nowTitle').html(commonObj.title)
    menuObj.pageMiddle !== '' &&
      menuObj.pageMiddle != null &&
      $('#page_middle').load(menuObj.pageMiddle)
  },
  readType2: function (menuArr) {
    if (menuArr.length == 0) return
    $('.menu_2').html('')
    $('.menu_3').html('')
    let lis = []
    let item = menuArr[0]
    const {
      htmlH,
      htmlW,
      rowNum,
      colNum,
      ow = htmlW / colNum,
      oh = htmlH / rowNum,
    } = item
    // { h: 3, i: '1', url: '', w: 3, x: 0, y: 0 },
    item.boxs.map((ite) => {
      lis.push(
        `<iframe  class="animated fadeIn" style="position: absolute; z-index: 10;left: ${
          ite.x * ow
        }px;top: ${ite.y * oh}px;"  width="${ite.w * ow}" height="${
          ite.h * oh
        }" src="${ite.url}" id=${item.path}${
          ite.id
        }  frameborder="0"  scrolling="no"></iframe>`
      )
    })
    $('#mainPage').append(lis)

    $('.nowTitle').html(commonObj.title)
  },
  beforeDelIframe: function () {
    //执行页面销毁回调事件
    if (window.emiter) {
      emiter.emit('beforeDestroedIframe')
      emiter.topics.clear()
    }
    var ifrs = $(`#mainPage > iframe`)
    for (let i = 0; i < ifrs.length; i++) {
      if (commonObj.dontDestroyFrameNames.indexOf(ifrs[i].name) == -1) {
        ifrs[i].name = 'delete'
        ifrs[i].src = 'about:blank'
      }
    }
    commonObj.dontDestroyFrameNames = []
    //中间页面
    $('#page_middle').empty()
    setTimeout(() => {
      var tbdy = $(`#mainPage > iframe[name='delete']`).prop('contentWindow')
      if (tbdy) {
        try {
          tbdy.document.write('')
          tbdy.document.clear()
          tbdy.close()
        } catch (error) {}
      }
      $(`#mainPage > iframe[name='delete']`).remove()
      commonObj.lastDestroyed = true
    }, 300)
  },

  // /清除draw资源图层
  clearLayerAndSource: function (clearEvent = false) {
    try {
      if (commonObj.__MapContentWindow == null) {
        return
      }
      // let map =top.document.getElementById("map").contentWindow.egs1.contentWindow.map;
      let map = commonObj.__MapContentWindow.map

      // const map = window.mainMap;
      map.getCanvas().style.cursor = ''
      if (map.getLayer('circle')) {
        map.removeLayer('circle')
        map.removeLayer('circle_line')
      }
      if (map.getSource('circle')) {
        map.removeSource('circle')
      }
      if (clearEvent) {
        // 取消事件
        map.off('mousemove', mouseMoveClick)
        map.off('click', clickEvent)
        map.off('dblclick', dbclickEvent)
        map.doubleClickZoom.enable()
        points = []
        starCoords = []
        isMousemove = false
        isFilst = true
        filstCoords = []
        moveCoords = []
        isDraws = true
        radius = 0
        jsonCircle = {
          type: 'FeatureCollection',
          features: [],
        }
        _pixelRadius = 0
      }
    } catch {}
  },
  //页面销毁
  destroyHtml: function (url) {
    commonObj.lastDestroyed = false
    $(`#mainPage > iframe`).removeClass('fadeIn').addClass('fadeOut')

    try {
      commonObj.__MapContentWindow.removeAllLayers()
      commonObj.__MapContentWindow.removeLayer('pop')
      commonObj.clearLayerAndSource(true)
    } catch (error) {}

    // var iframe = $(`#mainPage > iframe[name!='navFrame']`).prop('contentWindow')

    // $(`#mainPage > iframe[name!='navFrame']`).attr('src', 'about:blank')
    // try {
    //   iframe.document.write('')
    //   iframe.document.clear()
    // } catch (e) {}

    commonObj.beforeDelIframe()

    $('#navUl>li').unbind('click')
    window.rkmd && window.rkmd.$destroy()
    window.vm && window.vm.$destroy()
    return new Promise((resvole) => {
      resvole()
    })
  },
  //首次加载
  firstLoad: async function (mapUrl, url) {
    let menuArr = await commonObj.funSearchInfo(url)
    let fir = []
    menuArr.forEach((item, index) => {
      if (item.active == 1 || item.url == url) {
      } else {
        fir.push(
          `<li class="nav_li_lib" id="${'nav_li_' + item.url}">
            <div class="nav_li" data-name="${item.name}"  data-url="${
            item.url
          }">
              <tag class="${'tag_' + item.url}"></tag>
              <span>${item.name}</span>
            </div>
              <ul class="switch_menu menu_2">
              </ul>
              <ul class="switch_menu menu_3">
              </ul>
          </li>`
        )
      }
    })
    $('#navUl').html(fir.join(''))
    $('#navUl')
      .removeClass()
      .addClass('nav_ul nav_' + menuArr[0].url)
    commonObj.funClick()
    commonObj.loadHtml(mapUrl, url)
  },

  //显示一级菜单
  funClick: function () {
    $('.nav_li').click(function () {
      try {
        commonObj.__MapContentWindow.tool.changeBaseMap('black')
        commonObj.__MapContentWindow.tool.backHome()
      } catch (error) {}
      $('.menu_3').html('')
      let url = $(this).data('url')
      let els = $('.nav_li').children('tag')
      for (let i = 0; i < els.length; i++) {
        let clsName = els[i].className
        if (clsName.indexOf('active') != -1) {
          els[i].className = clsName.replace('_active', '')
        }
      }
      $('.nav_li').children('span').removeClass()
      $(this).children('tag')[0].className = 'tag_' + url + '_active'
      $(this).children('span')[0].className = 'active_text'
      commonObj.setMenu2(url)
    })
  },
  // 二级菜单
  async setMenu2(path) {
    let arr = []
    let fir = []
    arr = await commonObj.getMenuMap(path)

    if (arr.length == 1) {
      commonObj.loadHtml(path, arr[0].url)
      return
    }
    arr.forEach((item) => {
      fir.push(
        `<li class="menu_2_li" data-name="${item.name}"  id="${
          'nav_li_' + item.url
        }" onclick="commonObj.setMenu3('${item.url}','${path}','${
          item.name
        }')"   data-url="${item.url}"  data-parentUrl="${path}" >${
          item.name
        }</li>`
      )
    })
    $('.menu_2').html('')
    $('#nav_li_' + path)
      .children('.menu_2')
      .html(fir.join(''))
  },
  // 三级菜单
  async setMenu3(path, parentPath, name) {
    $('.menu_2_li').removeClass('active_text')
    $('#nav_li_' + path).addClass('active_text')
    let arr = await commonObj.getMenuMap(path)
    if (arr.length > 1) {
      let index = $('#nav_li_' + path).index()
      $('.menu_3').css('top', index * 76 + 'px')
      $('.menu_3').css('left', $('.menu_2_li').css('width'))
      let fir = []
      arr.forEach((item) => {
        fir.push(
          `<li data-name="${item.name}" onclick="commonObj.setMenu4('${path}','${item.url}')">${item.name}</li>`
        )
      })
      for (let i = 0; i < arr.length; i++) {
        let res = arr[i]
        commonObj.setMenuMap(res.url, res)
      }
      $('.menu_3').html('')
      $('#nav_li_' + parentPath)
        .children('.menu_3')
        .html(fir.join(''))
    } else if (arr.length == 1) {
      $('.menu_2').html('')
      $('.menu_3').html('')
      commonObj.loadHtml(path, arr[0].url)
    } else {
      $('.menu_2').html('')
      $('.menu_3').html('')
      commonObj.loadHtml(parentPath, path)
    }
  },
  async setMenu4(parentPath, path) {
    $('.menu_2').html('')
    $('.menu_3').html('')
    commonObj.loadHtml(parentPath, path)
  },
  async openMenuFun(url) {
    let argument = `?url=${url}&userId=${commonObj.userId}`
    let res = await axios({
      method: 'get',
      headers: {
        Authorization: commonObj.Authorization,
      },
      url: commonObj.pageConfig['getconcealmenu'] + argument,
    })
    let menuObj = res.data.data[0]

    commonObj.destroyHtml()

    commonObj.title = menuObj.name || ''

    if (menuObj.isMap == 1) {
      commonObj.hideMap()
    } else {
      commonObj.showMap()
    }
    if (menuObj.isStatus == 1) {
      arr = await commonObj.funLayoutSearchInfo(url)
      commonObj.readType2(arr)
    } else {
      commonObj.readType1(menuObj)
    }
  },
  //打开菜单
  async openMenu(url) {
    if (url == 'home') {
      try {
        commonObj.__MapContentWindow.tool.changeBaseMap('black')
      } catch (error) {}
      let els = $('.nav_li').children('tag')
      for (let i = 0; i < els.length; i++) {
        let clsName = els[i].className
        if (clsName.indexOf('active') != -1) {
          els[i].className = clsName.replace('_active', '')
        }
      }
      $('.nav_li').children('span').removeClass()
      // commonObj.loadHtml(url,url)
    }
    await commonObj.openMenuFun(url)

    return new Promise((resvole) => {
      resvole()
    })
  },
  funIframe: function () {
    window.addEventListener(
      'message',
      function (event) {
        let data = event.data
        let info
        //消息规范化兼容原来的字符串
        if ('string' != typeof data) return
        //判断传入参数格式
        if (data.indexOf('}') !== -1) {
          //修改接受message内容的格式
          info = JSON.parse(data)
        } else {
          info = data
        }
        switch (info.type) {
          //打开iframe
          case 'openIframe':
            console.log('进入openIframe')
            commonObj.funOpenIframe(info)
            break
          //关闭iframe
          case 'closeIframe':
            console.log('closeIframe')
            commonObj.funCloseIframe(info)
            break
          //获取  Authorization
          case 'getAuthorization':
            commonObj.getAuthorization()
            break
          case 'openWinHtml':
            commonObj.openWinHtml(3840, 2160, info.url)
            break
        }
      },
      false
    )
  },
  getAuthorization: function () {
    let token = sessionStorage.getItem('token').replaceAll('"', '')
    for (let i = 0; i < window.frames.length; i++) {
      window.frames[i].postMessage(
        JSON.stringify({
          Authorization: token,
          roleToken: commonObj.Authorization,
        }),
        '*'
      )
    }
  },
  /**
   * 打开新的iframe
   * @param {string} type openIframe
   * @param {String} name iframe name和id
   * @param {String} src iframe URL
   * @param {String} left iframe绝对定位left
   * @param {String} top iframe绝对定位top
   * @param {function} callback 回调函数
   * @param {Object} argument 请求参数对象
   */
  funOpenIframe: function (params) {
    let {
      type = 'openIframe',
      name = '',
      src = '',
      left = '2222',
      top = '700',
      width,
      height,
      zIndex = '20',
      argument = params.argument,
      callback = null,
    } = params
    var iframe = document.createElement('iframe')
    //如果存在相同name的iframe则不再重复创建
    if (window.frames[name]) {
      if (commonObj.lastDestroyed) {
        window.frames[name].postMessage(argument, '*')
        return
      }
    }
    //地址强转本地部署地址
    // src = src.replace('192.168.110.86:8093', '127.0.0.1:8601');
    iframe.src = src
    iframe.name = name
    iframe.frameBorder = '0'
    iframe.scrolling = 'no'

    iframe.style.position = 'absolute'
    iframe.style.left = left
    iframe.style.top = top
    iframe.style.width = width
    iframe.style.height = height
    iframe.style.zIndex = zIndex

    if (iframe.attachEvent) {
      iframe.attachEvent('onload', function () {
        alert('openIframe')
      })
    } else {
      iframe.onload = function () {
        window.frames[name].postMessage(argument, '*')
      }
    }
    $('#mainPage').append(iframe)
  },
  /**
   * 关闭iframe
   * @param {string} type closeIframe
   * @param {String} name 关闭iframe的name
   * @param {function} callback 回调函数
   */
  funCloseIframe: function (params) {
    let { type = 'closeIframe', name = '', callback = null } = params
    var ifrs = $(`#mainPage > iframe[name=${name}]`)
    for (let i = 0; i < ifrs.length; i++) {
      ifrs[i].src = 'about:blank'
    }
    setTimeout(() => {
      $(`#mainPage > iframe[name=${name}]`).remove()
    }, 300)
  },

  //处理窗口跳转的方法
  skipTo: function (name) {
    let target
    let isTargetUrl = false
    let height
    let width
    switch (name) {
      case '工业固废监管':
        target = 'http://localhost:10001/'
        isTargetUrl = false
        height = 1080
        width = 1920
        break
      case '高分应用':
        target = 'http://***********:8086/'
        break
      case '山洪预警与应急联动':
        target = 'http://web.dcyun.com:48433/#/router1/watershed'
        break
      case '医保支付服务':
        target = 'http://127.0.0.1:1048/home-pages/home'
        break
      case '生态环境监测':
        target = 'http://localhost:8086/'
        height = 2160
        widht = 7680
        isTargetUrl = false
        break
      case '裸露土地监测':
        // target = 'http://***********:6082/sty06/main.jssp'
        target = 'http://***********:6082/luotu'
        break
      default:
        break
    }
    //生态环境监测页面在新浏览器窗口打开
    if (isTargetUrl) {
      window.open(
        target,
        name,
        'directories=no, location=no, scrollbars=yes, resizable=yes, height=' +
          height +
          ', width = ' +
          width +
          ', top=0, left=0'
      )
    } else {
      // if (name === '裸露土地监测') {
      //   localStorage.setItem('page', 'hjbh')
      //   window.location.href = target
      // } else
      window.open(target)
    }
  },
  /**
   * @description 检测地图加载完成事件
   */
  detectMapInitQt() {
    return new Promise((resolve, reject) => {
      if (!top.mapUtil) {
        // 加载中
        let _interval = setInterval(() => {
          if (top.mapUtil) {
            window.__MapContentWindow = commonObj.__MapContentWindow =
              top.mapUtil
            clearInterval(_interval)
            resolve(true)
          }
        }, 50)
      } else {
        resolve(true)
      }
    })
  },
  //首页时间
  formatDate: function (day) {
    if (day < 10) {
      return '0' + day
    } else {
      return '' + day
    }
  },
  setDate: function () {
    $api('/cstz_tqjk').then((weaker) => {
      let date = new Date()
      let curhour = commonObj.formatDate(date.getHours())
      let curminute = commonObj.formatDate(date.getMinutes())
      let cursecond = commonObj.formatDate(date.getSeconds())
      for (var i = 0; i < weaker.length; i++) {
        if (date.getDate().toString().padStart(2, '0') === weaker[i].days) {
          let imgUrl = weaker[i].weather_path.slice(2)
          let wd =
            weaker[i].low.split(' ')[1] + '-' + weaker[i].high.split(' ')[1]
          var curday =
            date.getFullYear() +
            '.' +
            (date.getMonth() + 1) +
            '.' +
            weaker[i].days
          $('#headerTime').html(`
            <span class="week">
              <span style="width:100%;text-align: left;">${wd}</span>
              <img style="width:40px" src="${imgUrl}"/>
              <b class="data">${weaker[i].fx}&nbsp;${weaker[i].fl}</b>
            </span>
            <span class="userinfo">
              <span style="width:100%;text-align: left;">
                <h style="font-size:30px">${commonObj.userInfo.deptName}</h>
                <b>·</b>
                <h>${commonObj.userInfo.nickName}</h>
              </span>
              <b class="data" style="padding-top: 25px;margin-top: -20px;">${curday}&nbsp;${weaker[i].WEEK} <span id="nowTime">${curhour}:${curminute}:${cursecond}</span></b>
            </span>
            `)
        }
      }
    })
    // $api("http://10.45.14.162:8101/jhyjzh-server/screen_api/zhdd/home/<USER>").then(weaker => {
    //   let date = new Date();
    //   let curhour = commonObj.formatDate(date.getHours())
    //   let curminute = commonObj.formatDate(date.getMinutes())
    //   let cursecond = commonObj.formatDate(date.getSeconds())
    //   for (var i = 0; i < weaker.length; i++) {
    //     if (date.getDate().toString().padStart(2, '0') === weaker[i].day) {
    //       let imgUrl = weaker[0].img.slice(2)
    //       var curday = date.getFullYear() + '.' + (date.getMonth() + 1) + '.' + weaker[i].day;
    //       $('#headerTime').html(`<span><span id="nowTime">${curhour}:${curminute}:${cursecond}</span></b></b><b class="data">${curday}&nbsp;${weaker[i].mon}</b></span>
    //                     <span class="week"><img style="width:80px" src="${imgUrl}"/>${weaker[i].wd}<b class="data">${weaker[i].fx}&nbsp;${weaker[i].fd}</b></span>`)
    //     }
    //   }
    // })
  },
  getNewTime() {
    let date = new Date()
    let curhour = commonObj.formatDate(date.getHours())
    let curminute = commonObj.formatDate(date.getMinutes())
    let cursecond = commonObj.formatDate(date.getSeconds())
    $('#nowTime').html(`${curhour}:${curminute}:${cursecond}`)
  },
  getVideoList() {
    $api('/xxwh_bqcx').then((res) => {
      commonObj.videoList = res
    })
  },
  setUserInfo() {
    if (!sessionStorage.getItem('token')) {
      return
    }
    let token = sessionStorage.getItem('token').replaceAll('"', '')
    $.ajax({
      url: baseURL.admApi + '/getUserInfo?token=' + token,
      dataType: 'json',
      type: 'get',
      success: function (res) {
        if (res.code == 200) {
          commonObj.userInfo.deptName = res.data.deptName
          commonObj.userInfo.nickName = res.data.nickName
        }
      },
      error: function (e) {},
    })
  },
  initMain: function () {
    commonObj.userId = sessionStorage.getItem('role')
    let authorization = sessionStorage.getItem('Authorization')
    commonObj.Authorization =
      authorization && authorization.substring(1, authorization.length - 1)
    commonObj.setUserInfo()
    commonObj.menuTopics = new Map()
    commonObj.init()
    commonObj.funIframe()
    commonObj.setDate()
    setInterval(function () {
      commonObj.getNewTime()
    }, 1000)

    commonObj.firstLoad('pcmenu', 'pcmenu')
    commonObj.getVideoList()

    commonObj.detectMapInitQt().then((res) => {
      window.parent.postMessage(
        {
          type: 'mapInit',
        },
        '*'
      )
    })
  },

  /**
   * @description 退出
   */
  logOut: function () {
    let divs = `    
    <div id="exitSystem" class="exit-system-css">
       <div class="exit-top">
        <span>提醒</span>
        <div id="closeBtn" class="close-btn"></div>
       </div>
       <div class="exit-bottom">
        <div class="exit-system-text">您确定要退出金华城市大脑系统？</div>
        <div class="exit-btns">
           <div id="cancel">取消</div>
           <div id="define" class="exit-btn-active">确定</div>
        </div>
       </div>
    </div>
    `
    $('#mainPage').append(divs)
    commonObj.updateModalOverlayZindex(998)
    $('#define').click(function () {
      sessionStorage.clear()
      window.location.href = '/login3840'
    })
    $('#cancel').click(function () {
      $('#cancel').unbind()
      $('#define').unbind()
      $('#closeBtn').unbind()
      $('#exitSystem').remove()
      commonObj.updateModalOverlayZindex(0)
    })
    $('#closeBtn').click(function () {
      $('#cancel').unbind()
      $('#define').unbind()
      $('#closeBtn').unbind()
      $('#exitSystem').remove()
      commonObj.updateModalOverlayZindex(0)
    })
  },
}

;(function ($) {
  commonObj.initMain()
})(jQuery)
