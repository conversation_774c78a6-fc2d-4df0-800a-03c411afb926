<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>灾情信息汇集弹窗</title>
        <script src="/Vue/vue.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain/ggfw/css/common-dialog.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <!-- 轮播toolTip -->
    </head>

    <style>
        .table1 .th .th_td:nth-child(1) {
            flex: 0.1 !important;
        }
        .table1 .th .th_td:nth-child(2) {
            flex: 0.1;
        }
        .table1 .th .th_td:nth-child(3) {
            flex: 0.1;
        }
        .table1 .th .th_td:nth-child(4) {
            flex: 0.2;
        }
        .table1 .th .th_td:nth-child(5) {
            flex: 0.25;
        }
        .table1 .th .th_td:nth-child(6) {
            flex: 0.25;
        }
        #chart1,
        #chart2 {
            width: 100%;
            height: 300px;
        }

        .jbxx-con {
            width: 100%;
        }
        .jbxx-con li {
            width: 100%;
        }
    </style>

    <body>
        <div id="app" class="container" v-cloak>
            <div class="head">
                <span>灾情信息汇集</span>
                <div class="img" @click="closeDialog"></div>
            </div>
            <div class="content">
                <!-- 基本信息 -->
                <div class="jbxx">
                    <div class="s-c-blue2-gradient s-font-40">基础信息</div>
                    <div class="jbxx-con">
                        <li v-for="(item,index) in jbxxData" :key="index">
                            <img src="/static/citybrain/ggfw/img/list.png" alt="" style="width: 25px; height: 25px" />
                            <span class="s-c-yellow-gradient1 s-font-32 s-m-r-20">{{item.name}}</span
                            ><span class="s-c-grey-light s-font-32"
                                >{{item.info}}<span class="s-m-l-15">{{item.unit}}</span></span
                            >
                        </li>
                    </div>
                </div>
                <!-- 物联网监测 -->
                <div class="zylyfb">
                    <div class="s-c-blue2-gradient s-font-40">物联网监测</div>
                    <div id="chart1"></div>
                </div>
                <div class="zylyfb">
                    <div class="s-c-blue2-gradient s-font-40">社会舆论</div>
                    <div id="chart2"></div>
                </div>
            </div>
        </div>
    </body>
    <script type="module">
        new Vue({
            el: "#app",
            data: {
                jbxxData: [], //基本信息数据
                theadList: ["序号", "姓名", "性别", "所属领域", "所在单位", "联系电话"],
                tableList: [], //表格数据
            },
            methods: {
                closeDialog() {
                    top.commonObj.funCloseIframe({
                        name: "zqxx-dialog",
                    });
                },
                //数据初始化
                init() {
                    $api("shgl_kbxx_sgkb01").then((res) => {
                        this.jbxxData = res;
                    });
                    $api("shgl_kbxx_sgkb02").then((res) => {
                        this.getEcharts02("chart1", "水位", res);
                        this.getEcharts02("chart2", "讨论人数", res);
                    });
                },
                //绘制专业领域柱图
                getEcharts02(id, name, data) {
                    let myEc = echarts.init(document.getElementById(id));
                    let xData = [],
                        yData = [];

                    data.forEach((item) => {
                        xData.push(item.name);
                        yData.push(item.value);
                    });
                    var option = {
                        tooltip: {
                            trigger: "axis",
                            axisPointer: {
                                // 坐标轴指示器，坐标轴触发有效
                                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                            },
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "28",
                            },
                        },
                        legend: {
                            orient: "horizontal",
                            // itemWidth: 18,
                            // itemHeight: 18,
                            top: 0,
                            // right:0,
                            // icon: 'rect',
                            itemGap: 45,
                            textStyle: {
                                color: "#D6E7F9",
                                fontSize: 30,
                            },
                        },
                        grid: {
                            left: "2%",
                            right: "2%",
                            bottom: "10%",
                            top: "25%",
                            containLabel: true,
                        },
                        xAxis: [
                            {
                                type: "category",
                                data: xData,
                                offset: 20,
                                axisLine: {
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.3,
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLabel: {
                                    interval: 1,
                                    // rotate: -30,
                                    textStyle: {
                                        fontSize: 20,
                                        color: "white",
                                    },
                                },
                            },
                        ],
                        yAxis: [
                            {
                                name: "",
                                type: "value",
                                // max: 800,
                                min: 0,
                                nameTextStyle: {
                                    fontSize: 30,
                                    color: "#D6E7F9",
                                    padding: [0, 0, 20, 0],
                                },
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.1,
                                        width: 2,
                                    },
                                },
                                axisTick: {
                                    show: true,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.5,
                                        width: 2,
                                    },
                                },
                                axisLabel: {
                                    textStyle: {
                                        fontSize: 30,
                                        color: "#D6E7F9",
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: name,
                                type: "line",
                                yAxisIndex: 0,
                                smooth: true, //加这个
                                center: ["0%", "45%"],
                                radius: ["0%", "45%"],
                                data: yData,
                            },
                        ],
                    };
                    myEc.setOption(option);
                    tools.loopShowTooltip(myEc, option, { loopSeries: true });
                },
            },
            //项目生命周期
            mounted() {
                this.init();
            },
        });
    </script>
</html>
