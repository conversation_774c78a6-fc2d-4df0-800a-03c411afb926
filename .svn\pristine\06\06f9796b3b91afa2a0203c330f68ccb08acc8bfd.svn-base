<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <title>城市安全管控指标分析-中</title>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <script src="/Vue/vue.js"></script>
        <script src="/static/js/jslib/vue-count-to.min.js"></script>
        <script src="/echarts/echarts.min.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/datav.min.vue.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <style>
            * {
                margin: 0;
                padding: 0;
            }
            #app {
                /* width: 1700px;
        height: 1930px; */
                position: relative;
                top: 50px;
                left: 1070px;
            }
            .echars_box {
                position: absolute !important;
                top: 1330px !important;
                overflow: hidden;
                position: absolute;
                width: 1700px !important;
                height: 600px !important;
                top: 200px;
                background: url("/static/citybrain/csdn/img/video_share_bg.png") no-repeat;
                background-size: 100% 100%;
                background-color: #110e0ed9;
            }
            .header-title2[data-v-4d0d1712] {
                width: 100% !important;
            }
            .csaqgk_title {
                color: #fff;
                border-left: 20px solid rgb(9, 76, 219);
                padding-left: 20px;
            }
            .csaqgk_list {
                position: absolute;
                top: 20px;
            }
            .csaqgk_list p {
                color: #fff;
                font-size: 24px;
                width: 230px;
                display: flex;
                justify-content: space-between;
                padding: 5px 20px;
                border-radius: 20px;
                margin-bottom: 10px;
            }
            .csaqgk_list p:nth-child(1) {
                background: #f35a4c;
            }
            .csaqgk_list p:nth-child(2) {
                background: #ea9c47;
            }
            .csaqgk_list p:nth-child(3) {
                background: #3ecd7e;
            }
            .csaqgk_list p:nth-child(4) {
                background: #0496ff;
            }
            .csaqgk_list p:nth-child(5) {
                background: #9438fa;
            }
            .el-radio-group {
                position: absolute;
                top: 78px;
                left: 1417px;
                background-color: rgba(3, 25, 95, 0.808);
                padding-bottom: 10px;
            }
            .el-radio {
                display: block;
                display: flex;
                align-items: baseline;
                margin-top: 10px;
                color: #fff;
                padding: 10px 20px;
                border-top-left-radius: 25px;
                border-bottom-left-radius: 25px;
            }
            .el-radio:hover {
                background-image: linear-gradient(to right, rgba(20, 62, 199, 0.918), rgba(11, 86, 224, 0.562));
            }
            .el-radio__inner {
                width: 20px !important;
                height: 20px !important;
                border-radius: 0 !important;
            }
            .el-radio__label {
                font-size: 30px !important;
            }
            .radio_list {
                position: absolute;
                top: 30px;
                left: 1417px;
                width: 280px;
                font-size: 30px;
                padding: 5px 0;
                color: #fff;
                margin-bottom: 50px;
                background-color: rgb(6, 35, 88);
            }
            .radio_list span {
                cursor: pointer;
                text-align: center;
                width: 50%;
            }
            .active_span {
                background-color: #0496ff;
            }
        </style>
    </head>
    <body>
        <div id="app">
            <!-- 事件列表 -->
            <div class="csaqgk_list">
                <p v-for="item,i in list"><span>{{item.name}}</span> <span>{{item.num}}</span></p>
            </div>
            <!-- 勾选下拉 -->

            <template>
                <p class="s-flex s-row-around radio_list">
                    <span :class="{'active_span':click_index==0}" @click="click_index=0">当日</span>
                    <span :class="{'active_span':click_index==1}" @click="click_index=1">当月</span>
                </p>
                <el-radio-group v-model="radio" @change="Fun">
                    <el-radio :label="1">城市事件</el-radio>
                    <el-radio :label="2">社会治安事件</el-radio>
                    <el-radio :label="3" v-show="click_index==0" disabled>火灾</el-radio>
                    <el-radio :label="3" v-show="click_index==1">火灾</el-radio>
                    <el-radio :label="4" disabled>网络信息</el-radio>
                </el-radio-group>
            </template>
            <!--底部图标 -->
            <dv-border-box-8 class="echars_box">
                <nav class="s-m-t-20 s-m-b-10">
                    <p class="s-font-45 s-m-l-50 s-m-t-30 csaqgk_title">突发事件网络舆情分析</p>
                </nav>
                <div class="s-flex">
                    <div id="line_eh" style="width: 820px; height: 490px"></div>
                    <div id="bar_eh" style="width: 820px; height: 490px"></div>
                </div>
            </dv-border-box-8>
        </div>

        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/hjbh/js/date.js"></script>
        <script>
            window.addEventListener("message", async (e) => {
                if (e.data && e.data.type == "pointClick" && e.data.data.data) {
                    let data = JSON.parse(e.data.data.data);
                    if (data.csaqgk) {
                        csaqgk_vm.clearPop();
                        let _hotArr = csaqgk_vm.hotArr;
                        let hotMapData = [];
                        _hotArr.map((item) => {
                            let pointArr = [];
                            pointArr[0] = item.lng;
                            pointArr[1] = item.lat;
                            pointArr[2] = item.count;
                            pointArr[3] = item.geohash;
                            hotMapData.push(pointArr);
                        });
                        let _hotMapData = JSON.stringify(hotMapData).replace(/\"/g, "'");
                        let countStr = "";
                        for (let i = 0; i < data.obj.html.length; i++) {
                            countStr += `
                <div class="item" style="display: flex; font-size: 32px; color: #2299e2; line-height: 70px">
                  <span style="margin-left:30px;white-space: nowrap; ">${data.obj.html[i].key}  :</span>
                  <span style="color: #fff; margin-left:30px;">${data.obj.html[i].value}</span>
                </div>`;
                        }
                        let str =
                            data.csaqgk == "治安" || data.csaqgk == "火灾"
                                ? `<div onclick=" this.style.display = 'none'"
                style="
                  width: 800px;
                  position: absolute;

                  border-radius: 5px;
                  background-color: rgba(10, 31, 53, 0.8);
                  z-index: 999999;
                  -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                  box-shadow: inset 0 0 40px 0 #5ba3fa;
                  padding: 24px;
                " >
              <div class="container">${countStr}</div>
              </div>`
                                : data.csaqgk == "热力图"
                                ? `<div
                style="
                  width: 800px;
                  position: absolute;

                  border-radius: 5px;
                  background-color: rgba(10, 31, 53, 0.8);
                  z-index: 999999;
                  -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                  box-shadow: inset 0 0 40px 0 #5ba3fa;
                  padding: 24px;
                " >
              <div class="container">${countStr}</div>
              <div id="hot_btn"
               onclick="
                  top.document.getElementById('map').contentWindow.Work.change3D(7);
                  top.document.getElementById('map').contentWindow.Work.funChange(
                    JSON.stringify({
                      funcName: 'flyto',
                      flyData: {
                        center: [119.680359008789, 29.08903198242188],
                        zoom: 13.5,
                        pitch: 2,
                        bearing: 8,
                        essential: true,
                        duration: 4000,
                      },
                    })
                  );


                    const mapData = {
                      funcName: 'hotPowerMap',
                      hotPowerMapData: ${_hotMapData},
                      offset: 256,
                      heatMapId: 'rkztTimeHot',
                      threshold: 6000,
                      distance: 800,
                      alpha: 0.3,
                    };
                    top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(mapData));

               "
              style="cursor:pointer;width:50%;heigh:50px;line-height:50px;font-size:32px;color:#fff;background:#6fb0ee;text-align: center;">人群聚集热力分析</div>
              </div>`
                                : "";

                        let objData = {
                            funcName: "customPop",
                            coordinates:
                                data.csaqgk == "热力图"
                                    ? [119.66026448077417, 29.103060477365858]
                                    : [data.obj.pos[0], data.obj.pos[1]],
                            closeButton: true,
                            html: str,
                        };

                        top.document.getElementById("map").contentWindow.Work.funChange(JSON.stringify(objData));
                    }
                }
            });

            var csaqgk_vm = new Vue({
                el: "#app",
                data: {
                    list: [],
                    radio: "0",
                    click_index: 0,
                    //热力图
                    hotArr: [
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdbp",
                            count: 90,
                            lat: 29.0924835,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdbz",
                            count: 54,
                            lat: 29.0966034,
                        },
                        {
                            lng: 119.6569061,
                            geohash: "wtjjdcg",
                            count: 591,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6596527,
                            geohash: "wtjjdct",
                            count: 36,
                            lat: 29.1007233,
                        },
                        {
                            lng: 119.6582794,
                            geohash: "wtjjdcu",
                            count: 448,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6596527,
                            geohash: "wtjjdcv",
                            count: 251,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.661026,
                            geohash: "wtjjdcy",
                            count: 376,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdcz",
                            count: 430,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6569061,
                            geohash: "wtjjdf5",
                            count: 125,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.6569061,
                            geohash: "wtjjdf7",
                            count: 90,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6569061,
                            geohash: "wtjjdfe",
                            count: 394,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6569061,
                            geohash: "wtjjdfg",
                            count: 341,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6582794,
                            geohash: "wtjjdfh",
                            count: 36,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.6596527,
                            geohash: "wtjjdfj",
                            count: 18,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.6582794,
                            geohash: "wtjjdfk",
                            count: 125,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6596527,
                            geohash: "wtjjdfm",
                            count: 179,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdfp",
                            count: 824,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.661026,
                            geohash: "wtjjdfq",
                            count: 108,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdfr",
                            count: 1541,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6582794,
                            geohash: "wtjjdfs",
                            count: 179,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6596527,
                            geohash: "wtjjdft",
                            count: 90,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6582794,
                            geohash: "wtjjdfu",
                            count: 72,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6596527,
                            geohash: "wtjjdfv",
                            count: 125,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.661026,
                            geohash: "wtjjdfw",
                            count: 161,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdfx",
                            count: 699,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.661026,
                            geohash: "wtjjdfy",
                            count: 287,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdfz",
                            count: 699,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6569061,
                            geohash: "wtjjdg5",
                            count: 681,
                            lat: 29.108963,
                        },
                        {
                            lng: 119.6569061,
                            geohash: "wtjjdg7",
                            count: 54,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6555328,
                            geohash: "wtjjdgd",
                            count: 376,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.6569061,
                            geohash: "wtjjdge",
                            count: 161,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.6569061,
                            geohash: "wtjjdgg",
                            count: 18,
                            lat: 29.1130829,
                        },
                        {
                            lng: 119.6582794,
                            geohash: "wtjjdgh",
                            count: 287,
                            lat: 29.108963,
                        },
                        {
                            lng: 119.6582794,
                            geohash: "wtjjdgk",
                            count: 717,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6596527,
                            geohash: "wtjjdgm",
                            count: 341,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdgp",
                            count: 1075,
                            lat: 29.108963,
                        },
                        {
                            lng: 119.661026,
                            geohash: "wtjjdgq",
                            count: 1362,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdgr",
                            count: 1649,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6582794,
                            geohash: "wtjjdgs",
                            count: 376,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.6596527,
                            geohash: "wtjjdgt",
                            count: 125,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.6582794,
                            geohash: "wtjjdgu",
                            count: 143,
                            lat: 29.1130829,
                        },
                        {
                            lng: 119.6596527,
                            geohash: "wtjjdgv",
                            count: 90,
                            lat: 29.1130829,
                        },
                        {
                            lng: 119.661026,
                            geohash: "wtjjdgw",
                            count: 448,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdgx",
                            count: 161,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.661026,
                            geohash: "wtjjdgy",
                            count: 771,
                            lat: 29.1130829,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdgz",
                            count: 72,
                            lat: 29.1130829,
                        },
                        {
                            lng: 119.6541595,
                            geohash: "wtjjdu3",
                            count: 125,
                            lat: 29.1158295,
                        },
                        {
                            lng: 119.6555328,
                            geohash: "wtjjdu4",
                            count: 54,
                            lat: 29.1144562,
                        },
                        {
                            lng: 119.6569061,
                            geohash: "wtjjdu5",
                            count: 36,
                            lat: 29.1144562,
                        },
                        {
                            lng: 119.6555328,
                            geohash: "wtjjdu6",
                            count: 90,
                            lat: 29.1158295,
                        },
                        {
                            lng: 119.6569061,
                            geohash: "wtjjdu7",
                            count: 143,
                            lat: 29.1158295,
                        },
                        {
                            lng: 119.6582794,
                            geohash: "wtjjduh",
                            count: 90,
                            lat: 29.1144562,
                        },
                        {
                            lng: 119.6596527,
                            geohash: "wtjjduj",
                            count: 197,
                            lat: 29.1144562,
                        },
                        {
                            lng: 119.6582794,
                            geohash: "wtjjduk",
                            count: 430,
                            lat: 29.1158295,
                        },
                        {
                            lng: 119.6596527,
                            geohash: "wtjjdum",
                            count: 412,
                            lat: 29.1158295,
                        },
                        {
                            lng: 119.661026,
                            geohash: "wtjjdun",
                            count: 1255,
                            lat: 29.1144562,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdup",
                            count: 90,
                            lat: 29.1144562,
                        },
                        {
                            lng: 119.661026,
                            geohash: "wtjjduq",
                            count: 448,
                            lat: 29.1158295,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdur",
                            count: 179,
                            lat: 29.1158295,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdux",
                            count: 179,
                            lat: 29.1172028,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjduz",
                            count: 18,
                            lat: 29.118576,
                        },
                        {
                            lng: 119.661026,
                            geohash: "wtjjdvn",
                            count: 18,
                            lat: 29.1199493,
                        },
                        {
                            lng: 119.6623993,
                            geohash: "wtjjdvp",
                            count: 18,
                            lat: 29.1199493,
                        },
                        {
                            lng: 119.6637726,
                            geohash: "wtjje08",
                            count: 90,
                            lat: 29.0952301,
                        },
                        {
                            lng: 119.6678925,
                            geohash: "wtjje0g",
                            count: 2348,
                            lat: 29.0966034,
                        },
                        {
                            lng: 119.6637726,
                            geohash: "wtjje10",
                            count: 18,
                            lat: 29.0979767,
                        },
                        {
                            lng: 119.6651459,
                            geohash: "wtjje11",
                            count: 18,
                            lat: 29.0979767,
                        },
                        {
                            lng: 119.6665192,
                            geohash: "wtjje14",
                            count: 233,
                            lat: 29.0979767,
                        },
                        {
                            lng: 119.6665192,
                            geohash: "wtjje16",
                            count: 681,
                            lat: 29.09935,
                        },
                        {
                            lng: 119.6651459,
                            geohash: "wtjje19",
                            count: 233,
                            lat: 29.1007233,
                        },
                        {
                            lng: 119.6637726,
                            geohash: "wtjje1b",
                            count: 932,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6651459,
                            geohash: "wtjje1c",
                            count: 90,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6665192,
                            geohash: "wtjje1d",
                            count: 108,
                            lat: 29.1007233,
                        },
                        {
                            lng: 119.6665192,
                            geohash: "wtjje1f",
                            count: 18,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6733856,
                            geohash: "wtjje1x",
                            count: 394,
                            lat: 29.1007233,
                        },
                        {
                            lng: 119.6733856,
                            geohash: "wtjje1z",
                            count: 179,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6747589,
                            geohash: "wtjje3b",
                            count: 36,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6637726,
                            geohash: "wtjje40",
                            count: 251,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.6651459,
                            geohash: "wtjje43",
                            count: 18,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6637726,
                            geohash: "wtjje48",
                            count: 54,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6651459,
                            geohash: "wtjje49",
                            count: 18,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6637726,
                            geohash: "wtjje4b",
                            count: 18,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6665192,
                            geohash: "wtjje4d",
                            count: 18,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6665192,
                            geohash: "wtjje4f",
                            count: 125,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6678925,
                            geohash: "wtjje4g",
                            count: 54,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6720123,
                            geohash: "wtjje4n",
                            count: 125,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.6733856,
                            geohash: "wtjje4p",
                            count: 54,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.6720123,
                            geohash: "wtjje4q",
                            count: 520,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6733856,
                            geohash: "wtjje4r",
                            count: 72,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.670639,
                            geohash: "wtjje4t",
                            count: 54,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.670639,
                            geohash: "wtjje4v",
                            count: 143,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6720123,
                            geohash: "wtjje4w",
                            count: 161,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6733856,
                            geohash: "wtjje4x",
                            count: 90,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6637726,
                            geohash: "wtjje50",
                            count: 215,
                            lat: 29.108963,
                        },
                        {
                            lng: 119.6651459,
                            geohash: "wtjje51",
                            count: 18,
                            lat: 29.108963,
                        },
                        {
                            lng: 119.6637726,
                            geohash: "wtjje52",
                            count: 215,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6651459,
                            geohash: "wtjje53",
                            count: 161,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6665192,
                            geohash: "wtjje54",
                            count: 90,
                            lat: 29.108963,
                        },
                        {
                            lng: 119.6678925,
                            geohash: "wtjje55",
                            count: 36,
                            lat: 29.108963,
                        },
                        {
                            lng: 119.6665192,
                            geohash: "wtjje56",
                            count: 54,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6637726,
                            geohash: "wtjje58",
                            count: 466,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.6651459,
                            geohash: "wtjje59",
                            count: 699,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.6637726,
                            geohash: "wtjje5b",
                            count: 143,
                            lat: 29.1130829,
                        },
                        {
                            lng: 119.6651459,
                            geohash: "wtjje5c",
                            count: 341,
                            lat: 29.1130829,
                        },
                        {
                            lng: 119.6665192,
                            geohash: "wtjje5d",
                            count: 179,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.6678925,
                            geohash: "wtjje5e",
                            count: 36,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.6665192,
                            geohash: "wtjje5f",
                            count: 1004,
                            lat: 29.1130829,
                        },
                        {
                            lng: 119.6678925,
                            geohash: "wtjje5g",
                            count: 1595,
                            lat: 29.1130829,
                        },
                        {
                            lng: 119.6692657,
                            geohash: "wtjje5h",
                            count: 72,
                            lat: 29.108963,
                        },
                        {
                            lng: 119.670639,
                            geohash: "wtjje5j",
                            count: 108,
                            lat: 29.108963,
                        },
                        {
                            lng: 119.6692657,
                            geohash: "wtjje5k",
                            count: 394,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.670639,
                            geohash: "wtjje5m",
                            count: 305,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6720123,
                            geohash: "wtjje5q",
                            count: 179,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6733856,
                            geohash: "wtjje5r",
                            count: 179,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6692657,
                            geohash: "wtjje5s",
                            count: 161,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.670639,
                            geohash: "wtjje5t",
                            count: 72,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.670639,
                            geohash: "wtjje5v",
                            count: 215,
                            lat: 29.1130829,
                        },
                        {
                            lng: 119.6733856,
                            geohash: "wtjje5x",
                            count: 179,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.6747589,
                            geohash: "wtjje60",
                            count: 36,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.6747589,
                            geohash: "wtjje62",
                            count: 125,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6761322,
                            geohash: "wtjje63",
                            count: 18,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6775055,
                            geohash: "wtjje66",
                            count: 125,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6747589,
                            geohash: "wtjje68",
                            count: 251,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6761322,
                            geohash: "wtjje69",
                            count: 36,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6747589,
                            geohash: "wtjje6b",
                            count: 125,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6761322,
                            geohash: "wtjje6c",
                            count: 269,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6775055,
                            geohash: "wtjje6d",
                            count: 18,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6775055,
                            geohash: "wtjje6f",
                            count: 179,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6788788,
                            geohash: "wtjje6g",
                            count: 466,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6747589,
                            geohash: "wtjje70",
                            count: 18,
                            lat: 29.108963,
                        },
                        {
                            lng: 119.6747589,
                            geohash: "wtjje72",
                            count: 18,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6775055,
                            geohash: "wtjje74",
                            count: 484,
                            lat: 29.108963,
                        },
                        {
                            lng: 119.6775055,
                            geohash: "wtjje76",
                            count: 179,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6747589,
                            geohash: "wtjje78",
                            count: 72,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.6761322,
                            geohash: "wtjje79",
                            count: 358,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.6651459,
                            geohash: "wtjjeh1",
                            count: 54,
                            lat: 29.1144562,
                        },
                        {
                            lng: 119.6637726,
                            geohash: "wtjjeh2",
                            count: 54,
                            lat: 29.1158295,
                        },
                        {
                            lng: 119.6651459,
                            geohash: "wtjjeh3",
                            count: 18,
                            lat: 29.1158295,
                        },
                        {
                            lng: 119.6665192,
                            geohash: "wtjjeh4",
                            count: 143,
                            lat: 29.1144562,
                        },
                        {
                            lng: 119.6678925,
                            geohash: "wtjjeh5",
                            count: 197,
                            lat: 29.1144562,
                        },
                        {
                            lng: 119.6665192,
                            geohash: "wtjjeh6",
                            count: 36,
                            lat: 29.1158295,
                        },
                        {
                            lng: 119.6678925,
                            geohash: "wtjjeh7",
                            count: 215,
                            lat: 29.1158295,
                        },
                        {
                            lng: 119.6637726,
                            geohash: "wtjjehb",
                            count: 18,
                            lat: 29.118576,
                        },
                        {
                            lng: 119.6637726,
                            geohash: "wtjjej0",
                            count: 72,
                            lat: 29.1199493,
                        },
                        {
                            lng: 119.6651459,
                            geohash: "wtjjej1",
                            count: 90,
                            lat: 29.1199493,
                        },
                        {
                            lng: 119.6665192,
                            geohash: "wtjjej4",
                            count: 36,
                            lat: 29.1199493,
                        },
                        {
                            lng: 119.6472931,
                            geohash: "wtjjd9s",
                            count: 287,
                            lat: 29.1007233,
                        },
                        {
                            lng: 119.6486664,
                            geohash: "wtjjd9t",
                            count: 251,
                            lat: 29.1007233,
                        },
                        {
                            lng: 119.6472931,
                            geohash: "wtjjd9u",
                            count: 161,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6486664,
                            geohash: "wtjjd9v",
                            count: 574,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6500397,
                            geohash: "wtjjd9w",
                            count: 502,
                            lat: 29.1007233,
                        },
                        {
                            lng: 119.651413,
                            geohash: "wtjjd9x",
                            count: 430,
                            lat: 29.1007233,
                        },
                        {
                            lng: 119.6500397,
                            geohash: "wtjjd9y",
                            count: 358,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.651413,
                            geohash: "wtjjd9z",
                            count: 358,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6527863,
                            geohash: "wtjjdc8",
                            count: 72,
                            lat: 29.1007233,
                        },
                        {
                            lng: 119.6541595,
                            geohash: "wtjjdc9",
                            count: 18,
                            lat: 29.1007233,
                        },
                        {
                            lng: 119.6527863,
                            geohash: "wtjjdcb",
                            count: 842,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6541595,
                            geohash: "wtjjdcc",
                            count: 305,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6555328,
                            geohash: "wtjjdcd",
                            count: 36,
                            lat: 29.1007233,
                        },
                        {
                            lng: 119.6555328,
                            geohash: "wtjjdcf",
                            count: 699,
                            lat: 29.1020966,
                        },
                        {
                            lng: 119.6472931,
                            geohash: "wtjjddh",
                            count: 771,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.6486664,
                            geohash: "wtjjddj",
                            count: 1667,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.6472931,
                            geohash: "wtjjddk",
                            count: 108,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6486664,
                            geohash: "wtjjddm",
                            count: 1093,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6500397,
                            geohash: "wtjjddn",
                            count: 484,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.651413,
                            geohash: "wtjjddp",
                            count: 54,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.6500397,
                            geohash: "wtjjddq",
                            count: 448,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.651413,
                            geohash: "wtjjddr",
                            count: 215,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6472931,
                            geohash: "wtjjdds",
                            count: 878,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6486664,
                            geohash: "wtjjddt",
                            count: 1577,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6486664,
                            geohash: "wtjjddv",
                            count: 179,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6500397,
                            geohash: "wtjjddw",
                            count: 556,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.651413,
                            geohash: "wtjjddx",
                            count: 860,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6500397,
                            geohash: "wtjjddy",
                            count: 18,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6486664,
                            geohash: "wtjjdej",
                            count: 215,
                            lat: 29.108963,
                        },
                        {
                            lng: 119.6486664,
                            geohash: "wtjjdem",
                            count: 2814,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6500397,
                            geohash: "wtjjden",
                            count: 36,
                            lat: 29.108963,
                        },
                        {
                            lng: 119.651413,
                            geohash: "wtjjdep",
                            count: 108,
                            lat: 29.108963,
                        },
                        {
                            lng: 119.6500397,
                            geohash: "wtjjdeq",
                            count: 932,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.651413,
                            geohash: "wtjjder",
                            count: 627,
                            lat: 29.1103363,
                        },
                        {
                            lng: 119.6486664,
                            geohash: "wtjjdet",
                            count: 699,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.6486664,
                            geohash: "wtjjdev",
                            count: 233,
                            lat: 29.1130829,
                        },
                        {
                            lng: 119.6500397,
                            geohash: "wtjjdew",
                            count: 394,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.651413,
                            geohash: "wtjjdex",
                            count: 143,
                            lat: 29.1117096,
                        },
                        {
                            lng: 119.6500397,
                            geohash: "wtjjdey",
                            count: 538,
                            lat: 29.1130829,
                        },
                        {
                            lng: 119.651413,
                            geohash: "wtjjdez",
                            count: 72,
                            lat: 29.1130829,
                        },
                        {
                            lng: 119.6527863,
                            geohash: "wtjjdf0",
                            count: 72,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.6541595,
                            geohash: "wtjjdf1",
                            count: 18,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.6527863,
                            geohash: "wtjjdf2",
                            count: 448,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6541595,
                            geohash: "wtjjdf3",
                            count: 305,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6555328,
                            geohash: "wtjjdf4",
                            count: 645,
                            lat: 29.1034698,
                        },
                        {
                            lng: 119.6555328,
                            geohash: "wtjjdf6",
                            count: 878,
                            lat: 29.1048431,
                        },
                        {
                            lng: 119.6527863,
                            geohash: "wtjjdf8",
                            count: 950,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6541595,
                            geohash: "wtjjdf9",
                            count: 323,
                            lat: 29.1062164,
                        },
                        {
                            lng: 119.6527863,
                            geohash: "wtjjdfb",
                            count: 645,
                            lat: 29.1075897,
                        },
                        {
                            lng: 119.6541595,
                            geohash: "wtjjdfc",
                            count: 197,
                            lat: 29.1075897,
                        },
                    ],
                },
                mounted() {
                    this.initApi();
                    this.upText();
                },
                methods: {
                    initApi() {
                        $api("ldst_shgl_csaqgk", { type1: 7 }).then((res) => {
                            this.getBar("bar_eh", res);
                        });
                        $api("ldst_shgl_csaqgk", { type1: 8 }).then((res) => {
                            this.getLine("line_eh", res);
                        });
                        $api("ldst_shgl_csaqgk", { type1: 9 }).then((res) => {
                            this.list = res;
                        });
                    },
                    Fun(e) {
                        switch (e) {
                            case 1:
                                this.clearPoint("icon_sg3");
                                this.clearPoint("sjzx-消防救援平台");
                                // 调用热力图
                                $get("/textCity.json").then((res) => {
                                    let pointArr = [];
                                    res.map((ele) => {
                                        let str = {
                                            data: { csaqgk: "热力图", obj: ele },
                                            point: "119.66026448077417,29.103060477365858",
                                        };

                                        pointArr.push(str);
                                    });
                                    top.document.getElementById("map").contentWindow.Work.funChange(
                                        JSON.stringify({
                                            funcName: "pointLoad", //功能名称
                                            pointType: "aqsc-4", //点位类型图标
                                            pointId: "aqsc-4",
                                            pointData: pointArr.slice(1, 2),
                                            setClick: true,
                                            imageConfig: { iconSize: 0.5 },
                                        })
                                    );
                                });

                                break;
                            case 2:
                                this.clearHot();
                                this.goChange9();
                                this.clearPoint("sjzx-消防救援平台");
                                this.clearPoint("aqsc-4");
                                // 治安打点
                                $get("/textCity.json").then((res) => {
                                    let pointArr = [];
                                    res.map((ele) => {
                                        let str = {
                                            data: { csaqgk: "治安", obj: ele },
                                            point: ele.pos[0] + "," + ele.pos[1],
                                        };

                                        pointArr.push(str);
                                    });
                                    top.document.getElementById("map").contentWindow.Work.funChange(
                                        JSON.stringify({
                                            funcName: "pointLoad", //功能名称
                                            pointType: "icon_sg3", //点位类型图标
                                            pointId: "icon_sg3",
                                            pointData: pointArr,
                                            setClick: true,
                                            // imageConfig: { iconSize: 1 },
                                        })
                                    );
                                });
                                break;
                            case 3:
                                this.clearHot();
                                this.goChange9();
                                this.clearPoint("icon_sg3");
                                this.clearPoint("aqsc-4");
                                // 火灾打点
                                $get("/textCity.json").then((res) => {
                                    let pointArr = [];
                                    res.map((ele) => {
                                        let str = {
                                            data: { csaqgk: "火灾", obj: ele },
                                            point: ele.pos[0] + "," + ele.pos[1],
                                        };

                                        pointArr.push(str);
                                    });
                                    top.document.getElementById("map").contentWindow.Work.funChange(
                                        JSON.stringify({
                                            funcName: "pointLoad", //功能名称
                                            pointType: "sjzx-消防救援平台", //点位类型图标
                                            pointId: "sjzx-消防救援平台",
                                            pointData: [pointArr[pointArr.length - 1]],
                                            setClick: true,
                                            // imageConfig: { iconSize: 1 },
                                        })
                                    );
                                });
                                break;
                            case 4:
                                // 网格
                                break;
                            default:
                                console.log("没有选项");
                                break;
                        }
                    },
                    upText() {
                        top.document.getElementById("map").contentWindow.Work.change3D(9);
                        top.document.getElementById("map").contentWindow.Work.funChange(
                            JSON.stringify({
                                funcName: "flyto", //功能名称
                                flyData: {
                                    center: [119.98478050597587, 29.02213226366889],
                                    zoom: 9,
                                    pitch: 28,
                                    bearing: 0,
                                    duration: 4000, //飞行时间（建议加上）
                                },
                            })
                        );
                        $get("/textCity.json").then((res) => {
                            let textData = [];
                            top.document.getElementById("map").contentWindow.Work.funChange(
                                JSON.stringify({
                                    funcName: "3Dtext", //功能名称
                                    textData: res,
                                    textSize: 35,
                                })
                            );
                        });
                    },
                    // 清除地图撒点
                    clearPoint(id) {
                        window.document.getElementById("map").contentWindow.Work.funChange(
                            JSON.stringify({
                                funcName: "rmPoint",
                                pointId: id,
                            })
                        );
                    },
                    // 清楚热力图
                    clearHot() {
                        top.document.getElementById("map").contentWindow.Work.funChange(
                            JSON.stringify({
                                funcName: "rmhotPowerMap",
                            })
                        );
                    },
                    // 调整区划板块（9）
                    goChange9() {
                        top.document.getElementById("map").contentWindow.Work.change3D(9);
                        top.document.getElementById("map").contentWindow.Work.funChange(
                            JSON.stringify({
                                funcName: "flyto", //功能名称
                                flyData: {
                                    center: [119.98478050597587, 29.00013226366889],
                                    zoom: 9,
                                    pitch: 28,
                                    bearing: 0,
                                    duration: 2000, //飞行时间（建议加上）
                                },
                            })
                        );
                    },
                    // 清楚弹窗
                    clearPop() {
                        top.document.getElementById("map").contentWindow.Work.funChange(
                            JSON.stringify({
                                funcName: "rmPop",
                            })
                        );
                    },
                    // 回归原始位置
                    goToMap() {
                        top.document.getElementById("map").contentWindow.Work.change3D(7);
                        top.document.getElementById("map").contentWindow.Work.funChange(
                            JSON.stringify({
                                funcName: "flyto",
                                flyData: {
                                    center: [119.6300359008789, 29.09903198242188],
                                    zoom: 13.5,
                                    pitch: 2,
                                    bearing: 8,
                                    essential: true,
                                    duration: 4000,
                                },
                            })
                        );
                    },
                    getLine(id, echartsData) {
                        const myChartsRun = echarts.init(document.getElementById(id));
                        let length = [
                            "全部",
                            "数字报刊",
                            "网络媒体",
                            "政府机构",
                            "网络视频",
                            "咨询",
                            "论坛",
                            "博客",
                            "微博",
                            "微信",
                            "客户端",
                            "外媒",
                            "企业",
                        ];
                        let value = echartsData.map((item) => item.value);
                        let value1 = echartsData.map((item) => item.value1);
                        let value2 = echartsData.map((item) => item.value2);
                        let value3 = echartsData.map((item) => item.value3);
                        let value4 = echartsData.map((item) => item.value4);
                        let value5 = echartsData.map((item) => item.value5);
                        let value6 = echartsData.map((item) => item.value6);
                        let value7 = echartsData.map((item) => item.value7);
                        let value8 = echartsData.map((item) => item.value8);
                        let value9 = echartsData.map((item) => item.value9);
                        let value10 = echartsData.map((item) => item.value10);
                        let value11 = echartsData.map((item) => item.value11);
                        let value12 = echartsData.map((item) => item.value12);
                        let xdata = echartsData.map((v) => v.name);

                        let option = {
                            tooltip: {
                                trigger: "axis",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                textStyle: {
                                    color: "white",
                                    fontSize: "25",
                                },
                            },
                            legend: {
                                data: length,
                                top: "10",
                                left: 100,
                                textStyle: {
                                    fontSize: 22,
                                    color: "#fff",
                                },
                            },
                            grid: {
                                top: "20%",
                                left: "3%",
                                right: "4%",
                                bottom: "11%",
                                containLabel: true,
                            },
                            xAxis: [
                                {
                                    type: "category",
                                    data: xdata,
                                    axisLabel: {
                                        color: "#fff",
                                        fontSize: "22px",
                                    },
                                },
                            ],
                            yAxis: [
                                {
                                    type: "value",
                                    nameLocation: "middle",
                                    nameGap: 30,
                                    nameTextStyle: {
                                        fontWeight: "bold",
                                        fontSize: "20",
                                    },
                                    axisLabel: {
                                        formatter: "{value}",
                                        textStyle: {
                                            color: "#fff",
                                            fontSize: "22px",
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    name: "全部",
                                    type: "line",
                                    data: value,
                                    lineStyle: {
                                        normal: { width: 4 },
                                    },
                                },
                                {
                                    name: "数字报刊",
                                    type: "line",
                                    data: value1,
                                    lineStyle: {
                                        normal: { width: 4 },
                                    },
                                },
                                {
                                    name: "网络媒体",
                                    type: "line",
                                    data: value2,
                                    lineStyle: {
                                        normal: { width: 4 },
                                    },
                                },
                                {
                                    name: "政府机构",
                                    type: "line",
                                    data: value3,
                                    lineStyle: {
                                        normal: { width: 4 },
                                    },
                                },
                                {
                                    name: "网络视频",
                                    type: "line",
                                    data: value4,
                                    lineStyle: {
                                        normal: { width: 4 },
                                    },
                                },
                                {
                                    name: "咨询",
                                    type: "line",
                                    data: value5,
                                    lineStyle: {
                                        normal: { width: 4 },
                                    },
                                },
                                {
                                    name: "论坛",
                                    type: "line",
                                    data: value6,
                                    lineStyle: {
                                        normal: { width: 4 },
                                    },
                                },
                                {
                                    name: "博客",
                                    type: "line",
                                    data: value7,
                                    lineStyle: {
                                        normal: { width: 4 },
                                    },
                                },
                                {
                                    name: "微博",
                                    type: "line",
                                    data: value8,
                                    lineStyle: {
                                        normal: { width: 4 },
                                    },
                                },
                                {
                                    name: "微信",
                                    type: "line",
                                    data: value9,
                                    lineStyle: {
                                        normal: { width: 4 },
                                    },
                                },
                                {
                                    name: "客户端",
                                    type: "line",
                                    data: value10,
                                    lineStyle: {
                                        normal: { width: 4 },
                                    },
                                },
                                {
                                    name: "外媒",
                                    type: "line",
                                    data: value11,
                                    lineStyle: {
                                        normal: { width: 4 },
                                    },
                                },
                                {
                                    name: "企业",
                                    type: "line",
                                    data: value12,
                                    lineStyle: {
                                        normal: { width: 4 },
                                    },
                                },
                            ],
                        };

                        myChartsRun.setOption(option);
                    },
                    getBar(id, echartsData) {
                        const myChartsRun = echarts.init(document.getElementById(id));
                        let xdata = [],
                            ydata1 = [],
                            ydata2 = [];
                        echartsData.map((v) => {
                            xdata.push(v.name);
                            ydata1.push(v.up);
                            ydata2.push(v.low);
                        });
                        let option = {
                            color: ["#ff7736", "#77cdff"],

                            tooltip: {
                                trigger: "axis",
                                axisPointer: {
                                    // 坐标轴指示器，坐标轴触发有效
                                    type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                                },
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                textStyle: {
                                    color: "white",
                                    fontSize: "25",
                                },
                            },
                            legend: {
                                data: ["正面", "负面"],
                                top: "10",
                                left: 100,
                                textStyle: {
                                    fontSize: 22,
                                    color: "#fff",
                                },
                            },
                            grid: {
                                left: "3%",
                                right: "4%",
                                bottom: "11%",
                                containLabel: true,
                            },
                            calculable: true,
                            xAxis: [
                                {
                                    type: "category",
                                    data: xdata,
                                    axisLabel: {
                                        color: "#fff",
                                        fontSize: "22px",
                                    },
                                },
                            ],
                            yAxis: [
                                {
                                    type: "value",
                                    nameLocation: "middle",
                                    nameGap: 30,
                                    nameTextStyle: {
                                        fontWeight: "bold",
                                        fontSize: "20",
                                    },
                                    axisLabel: {
                                        formatter: "{value}",
                                        textStyle: {
                                            color: "#fff",
                                            fontSize: "22px",
                                        },
                                    },
                                },
                            ],
                            // dataZoom: [
                            //   {
                            //     textStyle: {
                            //       color: "#8392A5",
                            //     },
                            //     handleSize: "80%",
                            //     dataBackground: {
                            //       areaStyle: {
                            //         color: "#8392A5",
                            //       },
                            //       lineStyle: {
                            //         opacity: 0.8,
                            //         color: "#8392A5",
                            //       },
                            //     },
                            //     handleStyle: {
                            //       color: "#fff",
                            //       shadowBlur: 3,
                            //       shadowColor: "rgba(0, 0, 0, 0.6)",
                            //       shadowOffsetX: 2,
                            //       shadowOffsetY: 2,
                            //     },
                            //   },
                            //   {
                            //     type: "inside",
                            //   },
                            // ],
                            series: [
                                {
                                    name: "正面",
                                    type: "bar",
                                    data: ydata1,
                                    symbolSize: 300,
                                    markPoint: {
                                        data: [
                                            {
                                                type: "max",
                                                name: "最大值",
                                            },
                                            {
                                                type: "min",
                                                name: "最小值",
                                            },
                                        ],
                                    },
                                },
                                {
                                    name: "负面",
                                    type: "bar",
                                    data: ydata2,
                                    markPoint: {
                                        data: [
                                            {
                                                type: "max",
                                                name: "最大值",
                                            },
                                            {
                                                type: "min",
                                                name: "最小值",
                                            },
                                        ],
                                    },
                                },
                            ],
                        };
                        myChartsRun.setOption(option);
                    },
                },
            });
        </script>
    </body>
</html>
