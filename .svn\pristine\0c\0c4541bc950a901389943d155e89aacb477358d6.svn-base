<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Document</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <!-- <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" /> -->
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/jquery-3.4.1.min.js"></script>
    </head>
    <style>
        #jtys-right {
            width: 2045px;
            height: 1890px;
            background: url("/img/right-bg.png") no-repeat;
            background-size: 100% 100%;
            display: flex;
            justify-content: center;
        }

        .content {
            width: 1934px;
            height: 100%;
        }

        #barEcharts001 {
            background: url("../img/dwjcy-bottom.png") no-repeat;
            background-size: 100% 30%;
            background-position: 0 300px;
        }

        .jtys-right-bottom {
            display: flex;
        }

        .right-box {
            display: flex;
        }

        .box {
            flex: 1;
            background: url("../img/Base.png") no-repeat;
            background-size: 100% 30%;
            padding-top: 60px;
            text-align: center;
            background-position: 50%;
        }

        h1 {
            color: #afa086;
        }

        h2 {
            color: #fff;
        }
        .tabs {
            position: absolute;
            right: 50px;
            top: 10px;
            display: flex;
        }
        .tabs .tabItem {
            margin-left: 50px;
            font-size: 32px;
            color: #fff;
            text-align: center;
        }
        .tab-active {
            border-bottom: #fff solid 3px;
        }
    </style>

    <body>
        <div id="jtys-right">
            <div class="content">
                <div class="title">
                    <nav style="padding: 20px 45px">
                        <s-header-title title="租赁车辆情况" :data-time="time" htype="1"></s-header-title>
                    </nav>
                </div>
                <!-- 车辆情况柱状图 -->
                <div class="content_line">
                    <div id="barEcharts001" style="width: 100%; height: 400px"></div>
                </div>
                <!-- 运营情况 -->
                <div class="content_line" style="position: relative">
                    <nav style="margin: 50px 0 20px 0">
                        <s-header-title2 htype="1" title="运营里程数"></s-header-title2>
                    </nav>
                    <div class="tabs">
                        <div class="tabItem" :class="tabIndex===0? 'tab-active':''" @click="change(0)">年度</div>
                        <div class="tabItem" :class="tabIndex===1? 'tab-active':''" @click="change(1)">月度</div>
                    </div>
                    <div id="lineEcharts001" style="width: 100%; height: 400px"></div>
                </div>
                <div class="title">
                    <nav style="padding: 20px 45px">
                        <s-header-title title="停车场与充电站" :data-time="time" htype="1"></s-header-title>
                    </nav>
                </div>
                <div class="content_line">
                    <nav style="margin: 20px 0; width: 60%">
                        <s-header-title2 htype="1" title="公路划分情况"></s-header-title2>
                    </nav>
                    <div class="jtys-right-bottom">
                        <div id="lineEcharts002" style="width: 60%; height: 400px"></div>
                        <div class="right-box" style="width: 40%; height: 400px">
                            <div class="box" v-for="(item,i) in rbData" :key="i">
                                <h2>{{item.value}}</h2>
                                <h1>{{item.name}}</h1>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#jtys-right",
        data: {
            time: top.commonObj.nowDateTime,
            rbData: [],
            tabIndex: 0,
        },
        mounted() {
            this.initFun();
        },
        methods: {
            initFun() {
                $api("shgl_jtysright001").then((res) => {
                    this.getEcharts01(res);
                });

                $api("shgl_jtysright005").then((res) => {
                    this.getEcharts02(res);
                });

                $api("shgl_jtysright003").then((res) => {
                    this.getEcharts03(res);
                });

                $api("shgl_jtysright004").then((res) => {
                    this.rbData = res;
                });
            },
            change(index) {
                this.tabIndex = index;
                if (this.tabIndex == 0) {
                    $api("shgl_jtysright005").then((res) => {
                        this.getEcharts02(res);
                    });
                } else {
                    $api("shgl_jtysright002").then((res) => {
                        this.getEcharts02(res);
                    });
                }
            },
            getEcharts01(res) {
                let x = res.map((item, i) => {
                    return item.name;
                });
                let yData = res.map((item, i) => {
                    return item.value;
                });

                let echarts0 = echarts.init(document.getElementById("barEcharts001"));
                let option = {
                    // backgroundColor: "#0e202d",
                    tooltip: {
                        show: true,
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        formatter: "{b}: <br/> {c}万人",
                    },
                    grid: {
                        top: "7%",
                        left: "1%",
                        bottom: "2%",
                        right: "1%",
                        containLabel: true,
                    },
                    xAxis: {
                        type: "category",
                        data: x,
                        axisTick: {
                            alignWithLabel: true,
                        },
                        nameTextStyle: {
                            color: "#82b0ec",
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: "#82b0ec",
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                color: "#fff",
                                fontSize: 30,
                            },
                            margin: 30,
                        },
                    },
                    yAxis: {
                        show: false,
                        type: "value",
                        axisLabel: {
                            textStyle: {
                                color: "#fff",
                            },
                        },
                        splitLine: {
                            lineStyle: {
                                color: "#0c2c5a",
                            },
                        },
                        axisLine: {
                            show: false,
                        },
                    },
                    series: [
                        {
                            name: "",
                            type: "pictorialBar",
                            symbolSize: [70, 10],
                            symbolOffset: [0, -5], // 上部椭圆
                            symbolPosition: "end",
                            z: 12,
                            // "barWidth": "0",
                            label: {
                                normal: {
                                    show: true,
                                    position: "top",
                                    formatter: "{c}个",
                                    fontSize: 30,
                                    fontWeight: "bold",
                                    color: "#34DCFF",
                                },
                            },
                            color: "#2DB1EF",
                            data: yData,
                        },
                        {
                            name: "",
                            type: "pictorialBar",
                            symbolSize: [70, 10],
                            symbolOffset: [0, 7], // 下部椭圆
                            // "barWidth": "20",
                            z: 12,
                            color: "#2DB1EF",
                            data: yData,
                        },
                        {
                            name: "",
                            type: "pictorialBar",
                            symbolSize: function (d) {
                                return d > 0 ? [110, 20] : [0, 0];
                            },
                            symbolOffset: [-2, 18], // 下部内环
                            z: 10,
                            itemStyle: {
                                normal: {
                                    color: "transparent",
                                    borderColor: "#25759c",
                                    borderType: "solid",
                                    borderWidth: 4,
                                },
                            },
                            data: yData,
                        },
                        {
                            name: "",
                            type: "pictorialBar",
                            symbolSize: [150, 30],
                            symbolOffset: [-2, 25], // 下部外环
                            z: 10,
                            itemStyle: {
                                normal: {
                                    color: "transparent",
                                    borderColor: "#25759c",
                                    borderType: "solid",
                                    borderWidth: 4,
                                },
                            },
                            data: yData,
                        },
                        {
                            type: "bar",
                            //silent: true,
                            barWidth: "70",
                            barGap: "10%", // Make series be overlap
                            barCateGoryGap: "10%",
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 0.7, [
                                        {
                                            offset: 0,
                                            color: "#0B3147",
                                        },
                                        {
                                            offset: 1,
                                            color: "#38B2E6",
                                        },
                                    ]),
                                    opacity: 0.5,
                                },
                            },
                            data: yData,
                        },
                    ],
                };
                echarts0.setOption(option);
            },
            getEcharts02(res) {
                let myChart = echarts.init(document.getElementById("lineEcharts001"));
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                    },
                    legend: {
                        orient: "horizontal",
                        // icon: "circle",
                        itemGap: 45,
                        textStyle: {
                            color: "#D6E7F9",
                            fontSize: 30,
                        },
                    },
                    grid: {
                        left: "0%",
                        right: "3%",
                        top: "28%",
                        bottom: "1%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: "category",
                            data: res.map((item) => {
                                return item.name;
                            }),
                            axisLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)", // 颜色
                                    width: 1, // 粗细
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                // rotate: 30, //
                                textStyle: {
                                    color: "#D6E7F9",
                                    rotate: 30,
                                    fontSize: 30,
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: "运营里程数",
                            type: "value",
                            nameTextStyle: {
                                fontSize: 30,
                                color: "#D6E7F9",
                                padding: [0, 0, 0, 50],
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 30,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                        {
                            name: "增长率",
                            type: "value",
                            nameTextStyle: {
                                fontSize: 30,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                formatter: "{value}%",
                                textStyle: {
                                    fontSize: 30,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "共享自行车",
                            type: "bar",
                            barWidth: "12%",
                            yAxisIndex: 0,
                            itemStyle: {
                                color: "#00C0FF",
                            },
                            data: res.map((item) => {
                                return item.value0;
                            }),
                        },
                        {
                            name: "共享电动车",
                            type: "bar",
                            barWidth: "12%",
                            yAxisIndex: 0,
                            itemStyle: {
                                color: "#3FDAEE",
                            },
                            data: res.map((item) => {
                                return item.value1;
                            }),
                        },
                        {
                            name: "巡游出租车",
                            type: "bar",
                            barWidth: "12%",
                            yAxisIndex: 0,
                            itemStyle: {
                                color: "#A3CE5D",
                            },
                            data: res.map((item) => {
                                return item.value2;
                            }),
                        },
                        {
                            name: "共享自行车增长率",
                            type: "line",
                            smooth: true,
                            barWidth: "20%",
                            yAxisIndex: 1,
                            itemStyle: {
                                color: "#D55156",
                            },
                            data: res.map((item) => {
                                return item.value3;
                            }),
                        },
                        {
                            name: "共享电动车增长率",
                            type: "line",
                            smooth: true,
                            barWidth: "20%",
                            yAxisIndex: 1,
                            itemStyle: {
                                color: "#F19E3C",
                            },
                            data: res.map((item) => {
                                return item.value4;
                            }),
                        },
                        {
                            name: " 巡游出租车增长率",
                            type: "line",
                            smooth: true,
                            barWidth: "20%",
                            yAxisIndex: 1,
                            itemStyle: {
                                color: "#F1B86A",
                            },
                            data: res.map((item) => {
                                return item.value5;
                            }),
                        },
                    ],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },

            getEcharts03(res) {
                let echarts2 = echarts.init(document.getElementById("lineEcharts002"));
                var data = {
                    id: "multipleBarsLines",
                    legendBar: ["停车位", "充电桩"],
                    legendLine: ["停车位", "充电桩"],
                    xAxis: res.map((item) => {
                        return item.name;
                    }),
                    yAxis: [
                        res.map((item) => {
                            return item.value0;
                        }),
                        [10, 7, 8, 8, 7, 9, 8, 7, 3, 5, 10, 9],
                    ],
                    lines: [
                        [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
                        [12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1],
                    ],
                    barColor: ["#0aace4", "#21cad0"], //柱子颜色 必填参数
                    lineColor: ["red", "yellow"], // 折线颜色
                };
                var myData = (function test() {
                    let yAxis = data.yAxis || [];
                    let lines = data.lines || [];
                    let legendBar = data.legendBar || [];
                    let legendLine = data.legendLine || [];
                    var symbol = data.symbol || " ";
                    let seriesArr = [];
                    let legendArr = [];
                    yAxis &&
                        yAxis.forEach((item, index) => {
                            legendArr.push({
                                name: legendBar && legendBar.length > 0 && legendBar[index],
                            });
                            seriesArr.push({
                                name: legendBar && legendBar.length > 0 && legendBar[index],
                                type: "bar",
                                barGap: "0.5px",
                                data: item,
                                barWidth: data.barWidth || 12,
                                label: {
                                    normal: {
                                        show: true,
                                        formatter: "{c}" + symbol,
                                        position: "top",
                                        textStyle: {
                                            color: "#414957",
                                            fontStyle: "normal",
                                            fontFamily: "微软雅黑",
                                            textAlign: "left",
                                            fontSize: 11,
                                        },
                                    },
                                },
                                itemStyle: {
                                    //图形样式
                                    normal: {
                                        barBorderRadius: 4,
                                        color: data.barColor[index],
                                    },
                                },
                            });
                        });

                    lines &&
                        lines.forEach((item, index) => {
                            legendArr.push({
                                name: legendLine && legendLine.length > 0 && legendLine[index],
                            });
                            seriesArr.push({
                                name: legendLine && legendLine.length > 0 && legendLine[index],
                                type: "line",
                                smooth: true,
                                data: item,
                                itemStyle: {
                                    normal: {
                                        color: data.lineColor[index],
                                        lineStyle: {
                                            width: 3,
                                            type: "solid",
                                        },
                                    },
                                },
                                label: {
                                    normal: {
                                        show: false, //折线上方label控制显示隐藏
                                        position: "top",
                                    },
                                },
                                symbol: "none",
                                // symbolSize: 10,
                            });
                        });

                    return {
                        seriesArr,
                        legendArr,
                    };
                })();
                let option = {
                    title: {
                        show: true,
                        text: data.title,
                        subtext: data.subTitle,
                        link: "http://gallery.echartsjs.com/editor.html?c=xB1j9UgsXQ",
                    },
                    tooltip: {
                        trigger: "axis",
                        formatter: function (params) {
                            var time = "";
                            var str = "";
                            for (var i of params) {
                                time = i.name.replace(/\n/g, "") + "<br/>";
                                if (i.data == "null" || i.data == null) {
                                    str += i.seriesName + "：无数据" + "<br/>";
                                } else {
                                    str += i.seriesName + "：" + i.data + symbol + "%<br/>";
                                }
                            }
                            return time + str;
                        },
                        axisPointer: {
                            type: "none",
                        },
                    },
                    legend: {
                        right: data.legendRight || "30%",
                        top: 12,
                        itemGap: 50,
                        itemWidth: 20,
                        itemHeight: 20,
                        data: myData.legendArr,
                        textStyle: {
                            color: "#fff",
                            fontStyle: "normal",
                            fontFamily: "微软雅黑",
                            fontSize: 30,
                        },
                    },
                    grid: {
                        x: 30,
                        y: 80,
                        x2: 30,
                        y2: 60,
                    },
                    xAxis: {
                        type: "category",
                        data: data.xAxis,
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: "#ffffff14",
                            },
                        },
                        axisLabel: {
                            show: true,
                            interval: "0",
                            textStyle: {
                                lineHeight: 16,
                                padding: [2, 2, 0, 2],
                                height: 50,
                                fontSize: 12,
                            },
                            rich: {
                                Sunny: {
                                    height: 50,
                                    // width: 60,
                                    fontSize: 25,
                                    color: "#FFFFFF",
                                    padding: [0, 5, 0, 5],
                                    align: "center",
                                },
                            },
                            formatter: function (params, index) {
                                var newParamsName = "";
                                var splitNumber = 5;
                                var paramsNameNumber = params && params.length;
                                if (paramsNameNumber && paramsNameNumber <= 4) {
                                    splitNumber = 4;
                                } else if (paramsNameNumber >= 5 && paramsNameNumber <= 7) {
                                    splitNumber = 4;
                                } else if (paramsNameNumber >= 8 && paramsNameNumber <= 9) {
                                    splitNumber = 5;
                                } else if (paramsNameNumber >= 10 && paramsNameNumber <= 14) {
                                    splitNumber = 5;
                                } else {
                                    params = params && params.slice(0, 15);
                                }

                                var provideNumber = splitNumber; //一行显示几个字
                                var rowNumber = Math.ceil(paramsNameNumber / provideNumber) || 0;
                                if (paramsNameNumber > provideNumber) {
                                    for (var p = 0; p < rowNumber; p++) {
                                        var tempStr = "";
                                        var start = p * provideNumber;
                                        var end = start + provideNumber;
                                        if (p == rowNumber - 1) {
                                            tempStr = params.substring(start, paramsNameNumber);
                                        } else {
                                            tempStr = params.substring(start, end) + "\n";
                                        }
                                        newParamsName += tempStr;
                                    }
                                } else {
                                    newParamsName = params;
                                }
                                params = newParamsName;
                                return "{Sunny|" + params + "}";
                            },
                            color: "#687284",
                        },
                    },
                    yAxis: {
                        name: "单位：个",
                        nameTextStyle: {
                            color: "#ffffff",
                            align: "left",
                            fontSize: 25,
                        },
                        axisLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },
                        axisLabel: {
                            show: true,
                            fontSize: 25,
                            color: "#fff",
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: "#ffffff14",
                                type: "solid",
                            },
                            interval: 2,
                        },
                        splitNumber: 4,
                    },
                    series: myData.seriesArr,
                };
                echarts2.setOption(option);
            },

            getEcharts03(res) {
                let myChart = echarts.init(document.getElementById("lineEcharts002"));
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                    },
                    legend: {
                        orient: "horizontal",
                        // icon: "circle",
                        itemGap: 45,
                        textStyle: {
                            color: "#D6E7F9",
                            fontSize: 28,
                        },
                    },
                    grid: {
                        left: "0%",
                        right: "3%",
                        top: "28%",
                        bottom: "1%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: "category",
                            data: res.map((item) => {
                                return item.name;
                            }),
                            axisLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)", // 颜色
                                    width: 1, // 粗细
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                // rotate: 30, //
                                textStyle: {
                                    color: "#D6E7F9",
                                    rotate: 30,
                                    fontSize: 30,
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: "         ",
                            type: "value",
                            nameTextStyle: {
                                fontSize: 24,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 28,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                        {
                            name: "",
                            type: "value",
                            max: 10,
                            nameTextStyle: {
                                fontSize: 24,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                formatter: "{value}%",
                                textStyle: {
                                    fontSize: 28,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "停车位",
                            type: "bar",
                            barWidth: "20%",
                            yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#00C0FF",
                                        },
                                        {
                                            offset: 1,
                                            color: "rgba(0,192,255,0)",
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            data: res.map((item) => {
                                return item.value0;
                            }),
                        },
                        {
                            name: "充电桩",
                            type: "bar",
                            barWidth: "20%",
                            yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#3FDAEE",
                                        },
                                        {
                                            offset: 1,
                                            color: "rgba(0,192,255,0)",
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            data: res.map((item) => {
                                return item.value1;
                            }),
                        },
                        {
                            name: " 停车位",
                            type: "line",
                            smooth: true,
                            yAxisIndex: 1,
                            itemStyle: {
                                color: "#D55156",
                            },
                            data: res.map((item) => {
                                return item.value2;
                            }),
                        },
                        {
                            name: " 充电桩",
                            type: "line",
                            smooth: true,
                            yAxisIndex: 1,
                            itemStyle: {
                                color: "#F19E3C",
                            },
                            data: res.map((item) => {
                                return item.value3;
                            }),
                        },
                    ],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },
        },
    });
</script>
