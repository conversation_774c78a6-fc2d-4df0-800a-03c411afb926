    // let colors = ["#ffffff", "#f4e324"];
    let xsqOption = {
        // 提示框
        tooltip: {
            show: false,
            trigger: "axis",
            axisPointer: {
                //坐标轴指示器
                type: "line",
                lineStyle: {
                    type: "solid",
                },
            },
            borderWidth: 0,
            borderRadius: 5,
            padding: 15,
            backgroundColor: "#384c63",
            textStyle: {
                color: "#ffffff",
                fontSize: 40,
            },
            formatter: "{b}: {c}个",
        },

        //图表外边距
        grid: {
            left: 140,
            top: 20,
            right: 120,
            bottom: 20,
        },

        //横轴
        yAxis: [
            {
                type: "category",
                data:[],
                axisLine: {
                    //横轴线
                    show: false,
                    lineStyle: {
                        color: "rgba(119,179,241,0.5)",
                        width: 1,
                    },
                },
                axisLabel: {
                    //横轴标签
                    inside: false,
                    textStyle: {
                        color: "#d6e7f9",
                        fontSize: 32,
                    },
                    margin: 20,
                    // interval: 0,
                },
                axisTick: {
                    //刻度
                    show: false,
                },
            },
        ],
        //纵轴
        xAxis: [
            {
                type: "value",
                // name: "单位：个", //纵轴名称
                nameGap: 30,
                nameTextStyle: {
                    color: "#d6e7f9",
                    fontSize: 32,
                },
                position: "left",
                axisLine: {
                    //纵轴线
                    show: false,
                    lineStyle: {
                        color: "rgba(119,179,241,0.5)",
                        width: 1,
                    },
                },
                axisLabel: {
                    //纵轴线标签
                    show: false,
                    formatter: "{value}",
                    textStyle: {
                        color: "#d6e7f9",
                        fontSize: 32,
                    },
                    margin: 20,
                },
                splitLine: {
                    //分隔线
                    show: false,
                    lineStyle: {
                        color: "rgba(119,179,241,0.1)",
                        width: 1,
                    },
                },
            },
        ],
        //内容
        series: [
            {
                name: "各县(市、区)突发事件总数统计",
                type: "bar",
                barWidth: 20,
                yAxisIndex: 0,
                data: [],
                itemStyle: {
                    //柱状条样式
                    barBorderRadius: [0, 5, 5, 0],
                    color: "#387af0",
                },
                label: {
                    show: true,
                    position: "outside",
                    fontSize: 30,
                    color: "white",
                    formatter:"{c}件",
                    distance:20,
                },
                showBackground:true,
                backgroundStyle:{
                    color:"#103146",
                }
                // emphasis: {
                //     //悬浮选中效果
                //     itemStyle: {
                //         color: colors[1],
                //     },
                // },
            },
        ],
    };


