<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel='stylesheet' href='./css/index.css' type='text/css' />
    <link rel='stylesheet' href='./css/popup.css' type='text/css' />
    <link rel='stylesheet' href='./css/renkou.css' type='text/css' />
    <link rel='stylesheet' href='./css/economic.css' type='text/css' />
    <link rel='stylesheet' href='./css/mapbox-gl-draw.css' type='text/css' />
    <script src="./lib/videotest.js"></script>
    <script src="./lib/poupTemplate/publicPop.js"></script>
    <script src="./lib/poupTemplate/publicPop2.js"></script>
    <script src="./lib/point.js"></script>
    <script defer=defer src="./lib/egs(v1.0.0).min.js"></script>
    <script src="./lib/axios.js"></script>
    <script src="./lib/cdcity.js"></script>
    <script src="./lib/jinhua.js"></script>
    <script src="./lib/videoData.js"></script>
    <script src="./lib/test.js"></script>
    <script src="./js/config.js"></script>
    <script src="./lib/turf.js"></script>
    <script src="./js/bastMap.js"></script>
    <script src="./video/iview.js"></script>
	<script src="./video/DHWs_tc.js"></script>
	<script src="./lib/jhbuffer.js"></script>
    <script src="./js/animation.js"></script>
    <script src="./lib/earthquakes.js"></script>
    <script src="./lib/mapbox-gl-draw.js"></script>
    <title>egsDemo</title>
    <style>
        *{
            padding: 0;
            margin: 0;
        }
        html,body, .map, #egsmap{
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        #roam-map-box{
            position: absolute;
            top: 50%;
            right: 500px;
            width: 500px;
            height: 400px;
            z-index: 1000;
            background-color: #fff;
            border: 4px solid #ddd;
            border-radius: 4px;
            display: none;
        }
        #roamMap{
           width: 100%;
           height: 100%; 
        }
        ul{
            position: absolute;
            top: 20px;
            left: 20px;
            background-color: #ddd;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div class="map">
        <div id="egsmap"></div>
        <div id="roam-map-box">
            <div id="roamMap"></div> 
        </div>
        <!-- <ul>
            <li><button id="appear">出现</button></li>
            <li><button id="startRoam">开始播放</button></li>
            <li><button id="stopRoam">暂停播放</button></li>
            <li><button id="continueRoam">继续播放</button></li>
            <li><button id="resetRoam">重置漫游</button></li>
            <li><button id="escRoam">推出播放</button></li>
        </ul> -->
        
    </div>
    <div id="tabs" class="tabs"></div>
    <script src="./js/tab.js"></script>
    <script async>
        // console.log(pointData)
        window.onload = () => {
            const app = document.getElementById("egsmap");
            const { Map, eli } = egs;
            //egs.accessToken = 'pk.eyJ1IjoibWFvcmV5IiwiYSI6ImNqNWhrenIwcDFvbXUyd3I2bTJxYzZ4em8ifQ.KHZIehQuWW9AsMaGtATdwA';
            const map = new Map({
                el: app,
                init: {
                    scale: { maxWidth: 80, minWidth: 80, unit: "metric" },
                    style: mapConfig.style,
                    bearing: mapConfig.bearing,
                    pitch: mapConfig.pitch,
                    // bounds:[[114.35926671923897,22.577818133197027],[113.83498071428215,22.154139533657045]],
                    center: mapConfig.center,
                    zoom: mapConfig.zoom,
                    maxZoom: mapConfig.maxZoom,
                    minZoom: mapConfig.minZoom,
                },
                componentMounted() {
                    window.parent.postMessage({
                        type: 'mapInit',
                    }, "*");
                }
            });
            // setTimeout(() => {
                map.setMaxBounds([117.59137786326772,27.501760588580098,122.27155364451835,30.59990376398718])
                // map.setMaxBounds([118.89183295665077,27.501760588580098,121.94854388086122,30.59990376398718])
            // },5000)
            window.map = map;
            window.egs = egs;
            // console.log(1)
            // 增加画图功能
            let draw = new MapboxDraw({displayControlsDefault: false});
            map.addControl(draw);
            map.draw=draw
            const bastMap = new BastMap(egs,map);
            window.getJhData=(size,type)=>{bastMap.getJhData(size,type)}
            window.hh=bastMap;

            // 监听地图层级变化
            function mapZoom() {
                if (map.getZoom() > 16) {
                    // map.setLayoutProperty('ylroadname','visibility','visible')
                    // map.setLayoutProperty('ylpoi','visibility','visible')
                    map.setLayoutProperty('ylroadandpoi','visibility','visible')
                } else {
                    // map.setLayoutProperty('ylroadname','visibility','none')
                    // map.setLayoutProperty('ylpoi','visibility','none')
                    map.setLayoutProperty('ylroadandpoi','visibility','none')
                }
            }

            map.on('click',(e) => {
                window.mapClick=e
                console.log(e)
                
            })

            // new Tabs()
            window.Work = {
                getJhData: (num,data) => { bastMap.getJhData(num,data) },
                funChange :  (data) => {
                    const date = JSON.parse(data);
                   return bastMap.classiFication(date)
                },
                change3D: (num) => {
                    const obj = {
                        layerUrl: ''
                    }
                    map.off('zoomend',mapZoom)
                    bastMap.removeSection()
                    switch (num) {
                        case 3:
                        case 6:
                        case 1:
                            obj.layerUrl = mapConfig.bastLayers[0].url
                            obj.id = "mse7c7feb012"
                            break;
                        case 2:
                            obj.layerUrl = mapConfig.bastLayers[2].url
                            obj.id = "ms6a7db82518"
                        break;
                        case 7:
                            obj.layerUrl = mapConfig.bastLayers[1].url
                            obj.id = "msae0e0bf6b6"
                        break;
                        case 4: 
                        case 5: 
                        case 9:
                            map.on('zoomend',mapZoom)
                            obj.layerUrl = mapConfig.bastLayers[3].url
                            obj.id = "ggsmd1024"
                        break;
                    }
                    bastMap.toggleBastLayer(obj);
                    if (num == 9) {
                        this.Work.funChange(JSON.stringify({
                            'funcName': 'renderBankuai',
                            'renderBankuaiData': [
                                {
                                    name: '浦江县',
                                    color: [64,158,255,0.8]
                                },
                                {
                                    name: '兰溪市',
                                    color: [64,158,255,0.8]
                                },
                                {
                                    name: '婺城区',
                                    color: [64,158,255,0.8]
                                },
                                {
                                    name: '金义新区',
                                    color: [64,158,255,0.8]
                                },
                                {
                                    name: '义乌市',
                                    color: [64,158,255,0.8]
                                },
                                {
                                    name: '武义县',
                                    color: [64,158,255,0.8]
                                },
                                {
                                    name: '永康市',
                                    color: [64,158,255,0.8]
                                },
                                {
                                    name: '东阳市',
                                    color: [64,158,255,0.8]
                                },
                                {
                                    name: '磐安县',
                                    color: [64,158,255,0.8]
                                },
                            ]
                        }));
                        map.flyTo({center: [119.**************, 28.***************], zoom: 10,bearing:0,pitch:49});
                        map.on('click','SectionHighlight',(e) => {
                            let obj = this.map.queryRenderedFeatures(e.point, {
                                layers: ["SectionHighlight"]
                            })[0].properties
                            console.log({
                                type: 'bankuaiClick',
                                data: obj
                            })
                            window.parent.postMessage({
                                type: 'bankuaiClick',
                                data: obj
                            }, "*");
                        })
                    }
                }
            };
            map.on("load", ()=>{
                // 添加天空图层
                bastMap.addSkyLayer();
                // 添加图层
                bastMap.creatBastLayer(mapConfig.urlsIndex,mapConfig.bastLayers,mapConfig.FistLayerID);

                //  飞机飞行
                // document.getElementById("appear").addEventListener("click", () => {
                //     bastMap.UAVRoam(
                //         {
                //             "funcName":"UAVRoam", 
                //             "code":'1',
                //             line:[ [ 119.619898, 29.104004 ], [ 119.61951, 29.103705 ], [ 119.619102, 29.103416 ], [ 119.618723, 29.103153 ], [ 119.618144, 29.102798 ], [ 119.617332, 29.102236 ], [ 119.616567, 29.10165 ], [ 119.615969, 29.101187 ], [ 119.615436, 29.100657 ], [ 119.615028, 29.100177 ], [ 119.614617, 29.099615 ], [ 119.614138, 29.098628 ], [ 119.61383, 29.097864 ], [ 119.613593, 29.096994 ], [ 119.61336, 29.095934 ], [ 119.613262, 29.094843 ], [ 119.613473, 29.093212 ], [ 119.6141, 29.091712 ], [ 119.614913, 29.090172 ], [ 119.615835, 29.088519 ], [ 119.616349, 29.087618 ], [ 119.616697, 29.086979 ], [ 119.617053, 29.086343 ], [ 119.617339, 29.08585 ], [ 119.617778, 29.085058 ], [ 119.618088, 29.084574 ], [ 119.618398, 29.084031 ], [ 119.618748, 29.08343 ], [ 119.619025, 29.08289 ], [ 119.619315, 29.082358 ], [ 119.619613, 29.081866 ] ],
                //             time:29
                //         }
                //     );
                // })
                // document.getElementById("startRoam").addEventListener("click", () => {
                //     bastMap.startRoam();
                // })
                // document.getElementById("stopRoam").addEventListener("click", () => {
                //     bastMap.stopRoam()
                // })
                // document.getElementById("continueRoam").addEventListener("click", () => {
                //     bastMap.continueRoam()
                // })
                // document.getElementById("resetRoam").addEventListener("click", () => {
                //     bastMap.resetRoam()
                // })
                // document.getElementById("escRoam").addEventListener("click", () => {
                //     bastMap.escRoam()
                // })
                // Work.funChange(JSON.stringify(pointData)); // 加载点
                // // 删除点
                // setTimeout(() => {
                //                         Work.funChange(JSON.stringify({
                //         "funcName": "rmPoint",
                //         "pointId": "dzzhfxd",//传id清除单类，不传清除所有
                //     }));
                // },5000)

                // // 绘制圆
                // Work.funChange(JSON.stringify({
                //     "funcName":"startDrawPolygon",
                //     "circleData":[{// 可同时绘制多个
                //         coords:[119.64319610595703,29.08015251159668,84], //坐标
                //         r:1000, //半径
                //         color:[0,255,255,0.1], //颜色
                //         name:'名称1' // 名称
                //     }]
                // }));

                // setTimeout(() => {
                //     Work.funChange(JSON.stringify({
                //         "funcName":"clearCircle",
                //         "circleName":"名称1"// 删除对应圆形  不传则删除全部
                //     }));
                // },5000)
                
                // 3d文字
                // Work.funChange(JSON.stringify({
                //     "funcName":"3Dtext" , //3D文字功能
                //     id: '测试',
                //     "textData": [   // pos文字的位置  //text 展示的文字
                //         {pos: [119.94315399169922,29.5630503845215,11000],text:"测试文字（）",color:[255,255,255,1]},//浦江县
                //         {pos: [119.46214447021484,29.31345558166504,11000],text:"测试文字",color:[255,255,255,1]},//兰溪市
                //         {pos: [119.5569204711914, 29.00677101135254,11000],text:"测试文字",color:[255,255,255,1]},//婺城区
                //         {pos: [119.8483056640625, 29.18855995178222711000],text:"测试文字",color:[255,255,255,1]},//金东区
                //         {pos:[120.08206787109375,29.322123641967773,11000],text:"测试文字",color:[255,255,255,1]},//义乌市
                //         {pos: [119.7269204711914, 28.79677101135254,11000],text:"测试文字",color:[255,255,255,1]},//武义县
                //         {pos: [120.1469204711914, 28.97677101135254,11000],text:"测试文字",color:[255,255,255,1]},//永康市
                //         {pos: [120.4169204711914, 29.24677101135254,11000],text:"测试文字",color:[255,255,255,1]},//东阳市
                //         {pos: [120.6299204711914, 29.06677101135254,11000],text:"测试文字",color:[255,255,255,1]}//磐安县
                //     ],
                //     "textSize":100,
                //     color: [255,255,255,1]
                // }));
                // setTimeout(() => {
                //     Work.funChange(JSON.stringify({
                //         "funcName":"rm3DTextById" , //清除3D文字功能
                //         id: '测试',
                //     }));
                // },5000)

//                 Work.funChange(JSON.stringify({
//                         funcName: 'startDrawPolygon',
//                         color: [255, 0, 0, 0.5],//绘制区域颜色
//                         pointAndTextStatus: false, // 是否展示文字和点位
//                         getSectionDatas: true,//是否查询区域数据，默认false
//                     }))


// map.loadImage('/image/img.png', (error, image) => {
//             if (error) throw error;
//             // map.addImage('123', image);
//         })


// Work.change3D(9)
// Work.funChange(JSON.stringify({"funcName":"Histogram" , //功能名称
// "HistogramData":  [
// {name: '浦江县',num:50, unit: '个'}, 
// {name: '兰溪市',num:56, unit: '个'}, 
// {name: '婺城区',num:40, unit: '个'},
// {name: '金义新区',num:77, unit: '个'}, 
// {name: '义乌市',num:77, unit: '个'}, 
// {name: '武义县',num:65, unit: '个'}, 
// {name: '永康市',num:24, unit: '个'}, 
// {name: '东阳市',num:37, unit: '个'}, 
// {name: '磐安县',num:32, unit: '个'}] }));


                // 监听消息
                window.addEventListener('message', function (e) {
                    if (e.data) {
                        try {
                            let data = JSON.parse(e.data);
                            bastMap.classiFication(data) 
                        } catch (error) {

                        }
                    }
                });

            })

            window.wsss = DHWs.getInstance({
                        reConnectCount: 2,
                        connectionTimeout: 30 * 1000,
                        messageEvents: {
                          loginState() {
                            console.log('aaaa');
                          }
                        }
                      })
            function addVideo() {
                let DHWsInstance = window.wsss;

                // let ws = DHWsInstance;
                // this.ws = ws;
                // console.log(ws)
                DHWsInstance.detectConnectQt().then((res) => {
                    if (res) {
                        // 连接客户端成功
                        window.wsss.login({
                            loginIp: "*************",
                            // loginPort: "6443", //服务器
                            loginPort: "8001",// 本地
                            userName: "system",
                            userPwd: "JHcsdn@123456",
                            token: "",
                        });
                        window.wsss.on("loginState", (res) => {
                            if (res) {
                                console.log("登录成功");
                            } else {
                                console.log("登录失败");
                            }
                        });
                    } else {
                        // 连接客户端失败
                        console.log("连接客户端失败");
                    }
                });
            }
            addVideo()
        }
    
    </script>
</body>
</html>