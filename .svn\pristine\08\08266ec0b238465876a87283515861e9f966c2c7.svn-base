<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>安全隐患区域分布统计-左侧</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <link
      rel="stylesheet"
      href="/static/citybrain/shgl/css/aqyhqyfx-left.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
    <!-- 轮播toolTip -->
  </head>
  <style>
    .topTitle[data-v-399f3450] {
      width: 100% !important;
    }
  </style>

  <body>
    <div id="app" class="container" v-cloak>
      <!-- 安全隐患总体态势分布 -->
      <div class="first-con">
        <nav>
          <s-header-title
            htype="1"
            title="安全隐患总体态势分析"
            :data-time="nowTime"
          ></s-header-title>
        </nav>
        <div class="ndfb-con">
          <!-- 企业风险等级占比分析 -->
          <li>
            <s-header-title-2
              htype="1"
              title="企业风险等级占比分析"
            ></s-header-title-2>
            <div class="tabTitle">
              <div
                v-for="(item ,index) in zbfx"
                @click="changeEcharts('zbfx',index,item)"
                class="titleSG"
                :class="{active:isActive===index}"
              >
                {{item.name}}
              </div>
            </div>
            <div id="zbfx-chart"></div>
          </li>
          <!-- 企业风险分析趋势 -->
          <li>
            <s-header-title-2
              htype="1"
              title="企业风险分析趋势"
            ></s-header-title-2>
            <div class="tabTitle">
              <div
                v-for="(item ,index) in fxqs"
                @click="changeName(index)"
                class="titleSG"
                :class="{active:isActiveOne===index}"
              >
                {{item.name}}
              </div>
            </div>
            <div class="year-or-month">
              <div
                v-for="(item ,index) in fxqs1"
                @click="changeY(index)"
                class="li"
                :class="{active:isActiveTwo===index}"
              >
                {{item.name}}
              </div>
            </div>
            <div id="fxqs-chart"></div>
          </li>
          <!-- 企业安全生产走势 -->
          <li>
            <s-header-title-2
              htype="1"
              title="企业安全生产走势"
            ></s-header-title-2>
            <div class="tabTitle">
              <div
                v-for="(item ,index) in sczs"
                @click="changeT(index)"
                class="titleSG"
                :class="{active:isActiveThree===index}"
              >
                {{item.name}}
              </div>
            </div>
            <div class="year-or-month">
              <div
                v-for="(item ,index) in fxqs1"
                @click="changeF(index)"
                class="li"
                :class="{active:isActiveFour===index}"
              >
                {{item.name}}
              </div>
            </div>
            <div id="sczs-chart"></div>
          </li>
          <!-- 企业安全事故对比分析 -->
          <li>
            <s-header-title-2
              htype="1"
              title="企业安全事故对比分析"
            ></s-header-title-2>
            <div class="tabTitle">
              <div
                v-for="(item ,index) in aqsgdbfx"
                @click="changeEchart1('qyaq',index,item)"
                class="titleSG"
                :class="{active:aqsgdbfxIndex===index}"
              >
                {{item.name}}
              </div>
            </div>
            <div id="dbfx-chart"></div>
          </li>
          <!-- 行业企业安全事故对比分析 -->
          <li>
            <s-header-title-2
              htype="1"
              title="行业企业安全事故对比分析"
            ></s-header-title-2>
            <div class="tabTitle">
              <div
                v-for="(item ,index) in aqsgdbfx"
                @click="changeEchart1('hyaq',index,item)"
                class="titleSG"
                :class="{active:aqsgdbfxIndex1===index}"
              >
                {{item.name}}
              </div>
            </div>
            <div id="dbfx-chart1"></div>
          </li>
          <!-- 安全隐患、行政处罚、执法检查对比分析 -->
          <li>
            <s-header-title-2
              htype="1"
              title="安全隐患、行政处罚、执法检查对比分析"
            ></s-header-title-2>
            <div class="tabTitle">
              <div
                v-for="(item ,index) in dbfx"
                @click="changes(index)"
                class="titleSG"
                :class="{active:dbfxIndex===index}"
              >
                {{item.name}}
              </div>
            </div>
            <div id="dbfx-chart2"></div>
          </li>
        </div>
      </div>
      <!-- 矿山安全隐患综合统计与 危化品安全隐患综合统计-->
      <div class="second-con">
        <div class="ksaq">
          <nav>
            <s-header-title-2
              htype="1"
              title="矿山安全隐患综合统计"
            ></s-header-title-2>
          </nav>
          <div class="top-con">
            <div class="left">
              <div class="title">重大事故风险预警</div>
              <div class="table1">
                <div class="th">
                  <div
                    class="th_td"
                    style="flex: 0.36"
                    v-for="(item,index) in theadList"
                    :key="index"
                  >
                    {{item}}
                  </div>
                </div>
                <div class="tbody" id="box0">
                  <div class="tr" v-for="(item ,i) in tableList" :key="i">
                    <div class="tr_td" style="flex: 0.26">{{item.qy}}</div>
                    <div class="tr_td" style="flex: 0.56">{{item.name}}</div>
                    <div class="tr_td" style="flex: 0.16">{{item.czqk}}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="right">
              <div class="title">风险态势动态预警</div>
              <div class="dtyj">
                <li v-for="(item,index) in dtyjList" :key="index">
                  <span>{{item.name}}</span>
                  <span>{{item.value}}</span>
                </li>
              </div>
            </div>
          </div>
          <div class="bottom-con">
            <div class="left">
              <div class="title">综合分析</div>
              <div id="zhfx-chart"></div>
            </div>
            <div class="right">
              <div class="title">趋势分析</div>
              <div id="qsfx-chart"></div>
              <!-- <div style="font-size: 32px; color: #ffffff">建议增加培训</div> -->
            </div>
          </div>
        </div>
        <div class="whpaq">
          <nav>
            <s-header-title-2
              htype="1"
              title="危化品安全隐患综合统计"
            ></s-header-title-2>
          </nav>
          <div class="top-con">
            <li>
              <div class="title">企业概况</div>
              <div id="qygk-chart"></div>
              <div class="title">今日预警</div>
              <div class="tj-con">
                <div v-for="(item,index) in jryjList" class="li" :key="index">
                  <div class="l-tab">
                    <span
                      class="dian"
                      :style="item.lable1==='橙色预警'?`background-color: #ea9c47!important`:''"
                    ></span>
                    <span style="margin-right: 10px">{{item.lable1}}</span>
                    <span>{{item.value1}}</span>
                  </div>
                  <div class="r-tab">
                    <span style="margin-right: 10px">{{item.lable2}}</span>
                    <span>{{item.value2}}</span>
                  </div>
                </div>
              </div>
            </li>
            <li>
              <div class="title">危化品运输TOP5</div>
              <div id="whpys-chart"></div>
              <div class="title">今日报警</div>
              <div class="tj-con">
                <div v-for="(item,index) in jrbjList" class="li" :key="index">
                  <div class="l-tab">
                    <span
                      class="dian"
                      :style="item.lable1==='报警企业'?`background-color: #ea9c47!important`:''"
                    ></span>
                    <span style="margin-right: 10px">{{item.lable1}}</span>
                    <span>{{item.value1}}</span>
                  </div>
                  <div class="r-tab" @click="openWin">
                    <span style="margin-right: 10px">{{item.lable2}}</span>
                    <span>{{item.value2}}</span>
                  </div>
                </div>
              </div>
            </li>
            <li>
              <div class="title">危化品运单数量统计</div>
              <div id="whpsltj-chart"></div>
              <div class="title">当日承诺情况</div>
              <div class="tj-con">
                <div v-for="(item,index) in drcnqkList" class="li" :key="index">
                  <div class="l-tab">
                    <span
                      class="dian"
                      :style="item.lable1==='未承诺企业'?`background-color: #ea9c47!important`:''"
                    ></span>
                    <span style="margin-right: 10px">{{item.lable1}}</span>
                    <span>{{item.value1}}</span>
                  </div>
                  <div class="r-tab">
                    <span style="margin-right: 10px">{{item.lable2}}</span>
                    <span>{{item.value2}}</span>
                  </div>
                </div>
              </div>
            </li>
          </div>
          <div class="bottom-con">
            <li>
              <div class="title">危化品运输情况</div>
              <div id="whpysqk-chart"></div>
            </li>
            <li>
              <div class="table1" style="height: 100%">
                <div class="th">
                  <div
                    class="th_td"
                    style="flex: 0.26"
                    v-for="(item,index) in theadList1"
                    :key="index"
                  >
                    {{item}}
                  </div>
                </div>
                <div class="tbody" id="box0">
                  <div class="tr" v-for="(item ,i) in tableList1" :key="i">
                    <div class="tr_td" style="flex: 0.26">{{item.qy}}</div>
                    <div class="tr_td" style="flex: 0.56">{{item.name}}</div>
                    <div class="tr_td" style="flex: 0.26">{{item.bjsz}}</div>
                    <div class="tr_td" style="flex: 0.26">{{item.bjsc}}</div>
                  </div>
                </div>
              </div>
            </li>
            <li>
              <div id="dhyj-chart"></div>
            </li>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script type="module">
    new Vue({
      el: "#app",
      data: {
        nowTime: "", //当前时间
        zbfx: [
          {
            name: "企业数",
            type: "1",
          },
          {
            name: "规上企业",
            type: "2",
          },
        ],
        fxqs: [
          {
            name: "企业平均风险",
            type: "1",
          },
          {
            name: "行业平均风险",
            type: "2",
          },
        ],
        dbfx: [
          {
            name: "企业分析",
            type: "1",
          },
          {
            name: "行业分析",
            type: "2",
          },
        ],
        fxqs1: [
          {
            name: "月度",
            type: "1",
          },
          {
            name: "年度",
            type: "2",
          },
        ],
        sczs: [
          {
            name: "企业风险",
            type: "1",
          },
          {
            name: "行业风险分析",
            type: "2",
          },
        ],
        aqsgdbfx: [
          {
            name: "对比分析",
            type: "1",
          },
          {
            name: "历史走势",
            type: "2",
          },
        ],
        isActive: 0, //占比分析当前选中下标
        isActiveOne: 0, //风险趋势当前选中下标
        isActiveTwo: 0, //风险趋势年度选中
        isActiveThree: 0, //生产走势当前选中下标
        isActiveFour: 0, //生产走势年度选中
        dbfxIndex: 0, //安全隐患对比分析当前选中下标
        aqsgdbfxIndex: 0,
        aqsgdbfxIndex1: 0,
        dtyjList: [], //动态预警标签统计
        theadList: ["区域", "预警企业", "处置情况"],
        tableList: [],
        theadList1: ["区域", "预警企业", "报警数值", "报警时长"],
        tableList1: [],
        jryjList: [], //今日预警数据
        jrbjList: [], //今日报警
        drcnqkList: [], //当日承诺情况
        types: "企业平均风险",
        type1: "1",
        types2: "企业风险",
        type2: "1",
      },

      //项目生命周期
      mounted() {
        this.getTime();
        this.init();
        // this.initMap();
        this.openIframe();
        this.openIframe1();
        this.openIframe2();
      },

      methods: {
        openWin(){
          let Iframe = {
            type: "openIframe",
            name: "shgl-dialog",
            src:
              baseURL.url + "/static/citybrain/shgl/commont/aqyhqyfb-bjyy.html",
            left: "2040px",
            top: "1200px",
            width: "600px",
            height: "500px",
            zIndex: "10",
            argument: {
              status: "shgl-dialog",
            },
          };
          window.parent.postMessage(JSON.stringify(Iframe), "*");
        },
        changes(index) {
          this.dbfxIndex = index;
          if (this.dbfxIndex === 0) {
            // $api("shgl_csaq_aqyhdbfx", { type1: "企业分析" }).then((res) => {
            //   this.getEcharts05(
            //     "dbfx-chart2",
            //     res,
            //     "安全隐患",
            //     "行政处罚",
            //     "执法检查",
            //     40
            //   );
            // });
            $get("/shgl/csaq/aqyhdbfx", { type1: "企业分析" }).then((res) => {
              this.getEcharts05(
                "dbfx-chart2",
                res,
                "安全隐患",
                "行政处罚",
                "执法检查",
                40
              );
            });
          } else {
            // $api("shgl_csaq_aqyhdbfx", { type1: "行业分析" }).then((res) => {
            //   this.getEcharts05(
            //     "dbfx-chart2",
            //     res,
            //     "安全隐患",
            //     "行政处罚",
            //     "执法检查",
            //     40
            //   );
            // });
            $get("/shgl/csaq/aqyhdbfx01").then((res) => {
              this.getEcharts05(
                "dbfx-chart2",
                res,
                "安全隐患",
                "行政处罚",
                "执法检查",
                40
              );
            });
          }
        },
        changeName(index) {
          this.isActiveOne = index;
          if (this.isActiveOne === 0) {
            this.types = "企业平均风险";
            $api("shgl_csaq_qyfxqs", {
              type: this.type1,
              type1: this.types,
            }).then((res) => {
              this.getEcharts("fxqs-chart", res);
            });
          } else {
            this.types = "行业平均风险";

            $api("shgl_csaq_qyfxqs", {
              type: this.type1,
              type1: this.types,
            }).then((res) => {
              this.getEcharts("fxqs-chart", res);
            });
          }
        },
        changeY(index) {
          this.isActiveTwo = index;
          if (this.isActiveTwo === 0) {
            this.type1 = "1";
            $api("shgl_csaq_qyfxqs", {
              type: "1",
              type1: this.types,
            }).then((res) => {
              this.getEcharts("fxqs-chart", res);
            });
          } else {
            this.type1 = "2";
            $api("shgl_csaq_qyfxqs", {
              type: "2",
              type1: this.types,
            }).then((res) => {
              this.getEcharts("fxqs-chart", res);
            });
          }
        },
        changeT(index) {
          this.isActiveThree = index;

          if (this.isActiveThree === 0) {
            this.types2 = "企业风险";
            $api("shgl_csaq_sczs", {
              type: this.type2,
              type1: this.types2,
            }).then((res) => {
              this.getEcharts("sczs-chart", res);
            });
          } else {
            this.types2 = "行业风险";
            $api("shgl_csaq_sczs", {
              type: this.type2,
              type1: this.types2,
            }).then((res) => {
              this.getEcharts("sczs-chart", res);
            });
          }
        },
        changeF(index) {
          this.isActiveFour = index;
          console.log(index);
          if (this.isActiveFour === 0) {
            this.type2 = "1";
            $api("shgl_csaq_sczs", {
              type1: this.types2,
              type: this.type2,
            }).then((res) => {
              this.getEcharts("sczs-chart", res);
            });
          } else {
            this.type2 = "2";
            $api("shgl_csaq_sczs", {
              type1: this.types2,
              type: this.type2,
            }).then((res) => {
              this.getEcharts("sczs-chart", res);
            });
          }
        },
        //获取当前时间
        getTime() {
          var data = new Date();
          var yesterday = new Date(data.setDate(data.getDate() - 1));
          this.nowTime =
            yesterday.getFullYear() +
            "年" +
            (yesterday.getMonth() + 1) +
            "月" +
            yesterday.getDate() +
            "日";
        },
        //tab切换
        changeEcharts(cate, index, data) {
          if (cate === "zbfx") {
            this.isActive = index;
            if (this.isActive === 0) {
              //   $api("shgl_csaq_qyfxdj", { type: "企业数" }).then((res) => {
              //     this.getEcharts05(
              //       "zbfx-chart",
              //       res,
              //       "特大隐患",
              //       "重大隐患",
              //       "一般隐患",
              //       40
              //     );
              //   });
              $get("/shgl/csaq/qyfxdj", { type: "企业数" }).then((res) => {
                this.getEcharts05(
                  "zbfx-chart",
                  res,
                  "特大隐患",
                  "重大隐患",
                  "一般隐患",
                  40
                );
              });
            } else if (this.isActive === 1) {
              //   $api("shgl_csaq_qyfxdj", { type: "规上企业" }).then((res) => {
              //     this.getEcharts05(
              //       "zbfx-chart",
              //       res,
              //       "特大隐患",
              //       "重大隐患",
              //       "一般隐患",
              //       40
              //     );
              //   });
              $get("/shgl/csaq/qyfxdj01", { type: "规上企业" }).then((res) => {
                this.getEcharts05(
                  "zbfx-chart",
                  res,
                  "特大隐患",
                  "重大隐患",
                  "一般隐患",
                  40
                );
              });
            }
          } else if (cate === "qsfx") {
            this.isActiveOne = index;
            if (data.type === "1") {
              if (this.isActiveTwo === 0) {
                this.getQyfxqsData(data.type, "1");
              } else {
                this.getQyfxqsData(data.type, "2");
              }
            } else if (data.type === "2") {
              if (this.isActiveTwo === 0) {
                this.getQyfxqsData1(data.type, "1");
              } else {
                this.getQyfxqsData1(data.type, "2");
              }
            }
          } else if (cate === "sczs") {
            this.isActiveThree = index;
            if (data.type === "1") {
              if (this.isActiveFour === 0) {
                this.getSczsData(data.type, "1");
              } else {
                this.getSczsData(data.type, "2");
              }
            } else if (data.type === "2") {
              if (this.isActiveFour === 0) {
                this.getSczsData1(data.type, "1");
              } else {
                this.getSczsData1(data.type, "2");
              }
            }
          } else if (cate === "dbfx") {
            this.dbfxIndex = index;
          }
        },
        //年月切换

        changeEchart1(cate, index, data) {
          if (cate === "qyaq") {
            this.aqsgdbfxIndex = index;
            this.getQyaqData(data.type);
          } else {
            this.aqsgdbfxIndex1 = index;
            this.getHyaqData(data.type);
          }
        },
        //数据初始化
        init() {
          $api("shgl_csaq_sczs", { type1: "企业风险", type: "1" }).then(
            (res) => {
              this.getEcharts("sczs-chart", res);
            }
          );

          $api("shgl_csaq_qyfxqs", { type1: "企业平均风险", type: "1" }).then(
            (res) => {
              this.getEcharts("fxqs-chart", res);
            }
          );

          //   $api("shgl_csaq_qyfxdj", { type: "企业数" }).then((res) => {
          //     this.getEcharts05(
          //       "zbfx-chart",
          //       res,
          //       "特大隐患",
          //       "重大隐患",
          //       "一般隐患",
          //       40
          //     );
          //   });
          $get("/shgl/csaq/qyfxdj", { type: "企业数" }).then((res) => {
            this.getEcharts05(
              "zbfx-chart",
              res,
              "特大隐患",
              "重大隐患",
              "一般隐患",
              40
            );
          });
          //   $api("shgl_csaq_aqyhdbfx", { type: "企业分析" }).then((res) => {
          //     this.getEcharts05(
          //       "dbfx-chart2",
          //       res,
          //       "安全隐患",
          //       "行政处罚",
          //       "执法检查",
          //       40
          //     );
          //   });
          $get("/shgl/csaq/aqyhdbfx").then((res) => {
            this.getEcharts05(
              "dbfx-chart2",
              res,
              "安全隐患",
              "行政处罚",
              "执法检查",
              40
            );
          });
          //   $api("shgl_csaq_dtyj").then((res) => {
          //     this.dtyjList = res;
          //   });
          $get("/shgl/csaq/dtyj").then((res) => {
            this.dtyjList = res;
          });
          //   $api("shgl_csaq_fxyj").then((res) => {
          //     this.tableList = res;
          //   });
          $get("/shgl/csaq/fxyj").then((res) => {
            this.tableList = res;
          });
          //   $api("shgl_csaq_fxyj01").then((res) => {
          //     this.tableList1 = res;
          //   });
          $get("/shgl/csaq/fxyj01").then((res) => {
            this.tableList1 = res;
          });
          //   $api("shgl_csaq_zhfx").then((res) => {
          //     this.getEcharts05(
          //       "zhfx-chart",
          //       res,
          //       "监察行业",
          //       "重点企业",
          //       "重大风险",
          //       20
          //     );
          //   });
          $get("/shgl/csaq/zhfx").then((res) => {
            this.getEcharts05(
              "zhfx-chart",
              res,
              "监察行业",
              "重点企业",
              "重大风险",
              20
            );
          });
          //   $api("shgl_csaq_qsfx").then((res) => {
          //     console.log("趋势分析", res);
          //     this.getChart09("qsfx-chart", "2", "1", res.slice(-5), "15%");
          //   });
          $get("/shgl/csaq/qsfx").then((res) => {
            this.getChart09("qsfx-chart", "2", "1", res.slice(-5), "15%");
          });
          //   $api("shgl_csaq_qygk").then((res) => {
          //     this.getChart04("qygk-chart", res);
          //   });
          $get("/shgl/csaq/qygk").then((res) => {
            this.getChart04("qygk-chart", res);
          });
          //   $api("shgl_csaq_whpys").then((res) => {
          //     this.getChart03("whpys-chart", res);
          //   });
          $get("/shgl/csaq/whpys").then((res) => {
            this.getChart03("whpys-chart", res);
          });
          //   $api("shgl_csaq_whpsltj").then((res) => {
          //     this.getChart09("whpsltj-chart", "1", "1", res, "15%");
          //   });
          $get("/shgl/csaq/whpsltj").then((res) => {
            this.getChart09("whpsltj-chart", "1", "1", res, "15%");
          });
          //  $api("shgl_csaq_dhyj").then((res) => {
          //     this.getEcharts05(
          //       "dhyj-chart",
          //       res,
          //       "特级动火",
          //       "一级动火",
          //       "二级动火",
          //       20,
          //       "受限空间"
          //     );
          //   });
          $get("/shgl/csaq/dhyj").then((res) => {
            this.getEcharts05(
              "dhyj-chart",
              res,
              "特级动火",
              "一级动火",
              "二级动火",
              20,
              "受限空间"
            );
          });
          //   $api("shgl_csaq_whpysqk").then((res) => {
          //     this.getChart06("whpysqk-chart", res);
          //   });
          $get("/shgl/csaq/whpysqk").then((res) => {
            this.getChart06("whpysqk-chart", res);
          });
          //   $api("shgl_csaq_jryj").then((res) => {
          //     this.jryjList = res;
          //   });
          $get("/shgl/csaq/jryj").then((res) => {
            this.jryjList = res;
          });
          //   $api("shgl_csaq_jrbj").then((res) => {
          //     this.jrbjList = res;
          //   });
          $get("/shgl/csaq/jrbj").then((res) => {
            this.jrbjList = res;
          });
          //   $api("shgl_csaq_drcnqk").then((res) => {
          //     this.drcnqkList = res;
          //   });
          $get("/shgl/csaq/drcnqk").then((res) => {
            this.drcnqkList = res;
          });

          this.getQyaqData("1");
          this.getHyaqData("1");
        },
        //获取企业趋势-企业平均风险

        //获取企业趋势-行业平均风险
        // getQyfxqsData1(cate, type) {
        //     $api("shgl_csaq_qyfxqs01", { type1: "月", type: "1" }).then((res) => {
        //         let data = [];
        //         data = res.filter((item) => {
        //             return type === item.type;
        //         });
        //         this.getChart09("fxqs-chart", cate, data, "10%");
        //     });
        // },
        //获取企业安全生产走势-企业风险分析
        // getSczsData(cate, type) {
        //     $api("shgl_csaq_sczs").then((res) => {
        //         let data = [];
        //         data = res.filter((item) => {
        //             return type === item.type;
        //         });
        //         this.getChart09("sczs-chart", cate, type, data, "10%");
        //     });
        // },
        //获取企业安全生产走势-行业风险分析
        // getSczsData1(cate, type) {
        //     $api("shgl_csaq_sczs01").then((res) => {
        //         let data = [];
        //         data = res.filter((item) => {
        //             return type === item.type;
        //         });
        //         this.getChart09("sczs-chart", cate, type, data, "10%");
        //     });
        // },
        //获取企业安全事故-对比分析
        getQyaqData(type) {
          //   $api("shgl_csaq_qyaq").then((res) => {
          //     let data = [];
          //     data = res.filter((item) => {
          //       return type === item.type;
          //     });
          //     if (type === "1") {
          //       this.getEcharts01("dbfx-chart", data);
          //     } else {
          //       this.getEcharts02("dbfx-chart", data);
          //     }
          //   });
          $get("/shgl/csaq/qyaq").then((res) => {
            let data = [];
            data = res.filter((item) => {
              return type === item.type;
            });
            if (type === "1") {
              this.getEcharts01("dbfx-chart", data);
            } else {
              this.getEcharts02("dbfx-chart", data);
            }
          });
        },
        //获取行业企业安全事故对比
        getHyaqData(type) {
          //   $api("shgl_csaq_hyaq").then((res) => {
          //     let data = [];
          //     data = res.filter((item) => {
          //       return type === item.type;
          //     });
          //     if (type === "1") {
          //       this.getEcharts01("dbfx-chart1", data);
          //     } else {
          //       this.getEcharts02("dbfx-chart1", data);
          //     }
          //   });
          $get("/shgl/csaq/hyaq").then((res) => {
            let data = [];
            data = res.filter((item) => {
              return type === item.type;
            });
            if (type === "1") {
              this.getEcharts01("dbfx-chart1", data);
            } else {
              this.getEcharts02("dbfx-chart1", data);
            }
          });
        },
        //企业风险等级占比分析、执法检查对比分析
        getEcharts05(dom, echartData, name1, name2, name3, top, name4) {
          echarts.init(document.getElementById(dom)).dispose();
          let echarts1 = echarts.init(document.getElementById(dom));
          let xData = echartData.map((item) => {
            return item.time;
          });
          let yData = echartData.map((item) => {
            return item.value;
          });
          let yData1 = echartData.map((item) => {
            return item.value1;
          });
          let yData2 = echartData.map((item) => {
            return item.value2;
          });
          let yData3 = echartData.map((item) => {
            return item.value3;
          });
          let yData4 =
            name4 &&
            echartData.map((item) => {
              return item.value3;
            });

          let option = {
            tooltip: {
              backgroundColor: "#0c1a38",
              trigger: "axis",
              fontSize: 30,
              textStyle: {
                fontSize: 30,
                color: "#fff",
              },
              axisPointer: {
                type: "shadow",
                textStyle: {
                  color: "#fff",
                },
              },
              formatter: (params, ticket, callback) => {
                let sum = 0;
                params.map((item, index) => {
                  sum += Number(item.value);
                });
                const obj = params.map((item) => {
                  if (!item.value) {
                    item.value = 0;
                  }
                  const p = ((Number(item.value) / sum) * 100).toFixed(2);
                  const dot =
                    item.marker +
                    item.seriesName +
                    ": " +
                    item.value +
                    " " +
                    p +
                    "%" +
                    "</br>";
                  return dot;
                });
                return obj.join("");
              },
            },
            legend: {
              textStyle: {
                color: "#fff",
                fontSize: 28,
              },
              top: top,
              icon: "square",
              itemWidth: 20,
              itemHeight: 20,
            },
            grid: {
              left: "2%",
              right: "4%",
              bottom: "5%",
              top: "25%",
              containLabel: true,
            },
            calculable: true,
            xAxis: [
              {
                type: "category",
                offset: 10,
                axisLine: {
                  lineStyle: {
                    color: "#19416b",
                  },
                },
                splitLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                splitArea: {
                  show: false,
                },
                axisLabel: {
                  // interval: 0,
                  color: "rgba(255,255,255,0.7)",
                  fontSize: 30,
                },
                data: xData,
              },
            ],
            yAxis: [
              {
                type: "value",

                splitLine: {
                  show: true,
                  lineStyle: {
                    color: "#19416b",
                  },
                },
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  interval: 0,
                  color: "rgba(255,255,255,0.5)",
                  fontSize: 30,
                },
              },
            ],
            series: [
              {
                name: name1,
                type: "bar",
                stack: "总量",
                barMaxWidth: 20,
                barGap: "10%",
                itemStyle: {
                  normal: {
                    color: "#bd352c",
                  },
                },
                data: yData,
              },
              {
                name: name2,
                type: "bar",
                stack: "总量",
                itemStyle: {
                  normal: {
                    color: "#398912",
                    barBorderRadius: 0,
                  },
                },
                data: yData1,
              },
              {
                name: name3,
                type: "bar",
                stack: "总量",
                itemStyle: {
                  normal: {
                    color: "#213ba4",
                    barBorderRadius: 0,
                  },
                },
                data: yData2,
              },
              {
                name: name4,
                type: "bar",
                stack: "总量",
                itemStyle: {
                  normal: {
                    color: "#ecc935",
                    barBorderRadius: 0,
                  },
                },
                data: yData3,
              },
            ],
          };
          echarts1.setOption(option);
        },
        //绘制企业安全事故对比分析
        getEcharts01(id, data) {
          echarts.init(document.getElementById(id)).dispose();
          let myEc = echarts.init(document.getElementById(id));
          let xData = [],
            yData = [];

          data.forEach((item) => {
            xData.push(item.name);
            yData.push(item.value);
          });
          var option = {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
              },
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "28",
              },
            },
            // legend: {
            //     orient: 'horizontal',
            //     // itemWidth: 18,
            //     // itemHeight: 18,
            //     top: '8%',
            //     // icon: 'rect',
            //     itemGap: 25,
            //     textStyle: {
            //         color: '#D6E7F9',
            //         fontSize: 30,
            //     },
            // },
            grid: {
              left: "2%",
              right: "5%",
              bottom: "6%",
              top: "20%",
              containLabel: true,
            },
            xAxis: [
              {
                type: "category",
                data: xData,
                offset: 20,
                axisLine: {
                  lineStyle: {
                    color: "#77b3f1",
                    opacity: 0.3,
                  },
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  // interval: 0,
                  // rotate: -30,
                  textStyle: {
                    fontSize: 30,
                    color: "white",
                  },
                },
              },
            ],
            yAxis: [
              {
                name: "",
                type: "value",
                // max: 800,
                // min: 0,
                nameTextStyle: {
                  fontSize: 30,
                  color: "#D6E7F9",
                  padding: [0, 0, 20, 0],
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: "#77b3f1",
                    opacity: 0.1,
                    width: 2,
                  },
                },
                axisTick: {
                  show: true,
                  lineStyle: {
                    color: "#77b3f1",
                    opacity: 0.5,
                    width: 2,
                  },
                },
                axisLabel: {
                  textStyle: {
                    fontSize: 30,
                    color: "#D6E7F9",
                  },
                },
              },
            ],
            series: [
              {
                name: "数量",
                type: "bar",
                barWidth: 35,
                yAxisIndex: 0,
                smooth: true, //加这个
                center: ["0%", "45%"],
                radius: ["0%", "45%"],
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "#00C0FF",
                      },
                      {
                        offset: 0.2,
                        color: "#00C0FF",
                      },
                      {
                        offset: 1,
                        color: "#004F69",
                      },
                    ]),
                    barBorderRadius: 4,
                  },
                },
                data: yData,
              },
            ],
          };
          myEc.setOption(option);
          // tools.loopShowTooltip(myEc, option, { loopSeries: true });
        },
        //绘制企业安全事故历史走势
        getEcharts02(id, data) {
          echarts.init(document.getElementById(id)).dispose();
          let myEc = echarts.init(document.getElementById(id));
          let xData = [],
            yData = [];

          data.forEach((item) => {
            xData.push(item.name);
            yData.push(item.value);
          });
          var option = {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
              },
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "28",
              },
            },
            grid: {
              left: "3%",
              right: "4%",
              bottom: "5%",
              containLabel: true,
            },
            toolbox: {
              feature: {
                saveAsImage: {},
              },
            },
            xAxis: {
              type: "category",
              data: xData,
              offset: 20,
              axisLine: {
                lineStyle: {
                  color: "#77b3f1",
                  opacity: 0.3,
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                // rotate: -30,
                textStyle: {
                  fontSize: 30,
                  color: "white",
                },
              },
            },
            yAxis: {
              type: "value",
              min: 0,
              nameTextStyle: {
                fontSize: 30,
                color: "#D6E7F9",
                padding: [0, 0, 20, 0],
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: "#77b3f1",
                  opacity: 0.1,
                  width: 2,
                },
              },
              axisTick: {
                show: true,
                lineStyle: {
                  color: "#77b3f1",
                  opacity: 0.5,
                  width: 2,
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 30,
                  color: "#D6E7F9",
                },
              },
            },
            series: [
              {
                name: "搜索引擎",
                type: "line",
                stack: "总量",
                smooth: true, //加这个
                itemStyle: {
                  normal: {
                    color: "#968212",
                    barBorderRadius: 4,
                  },
                },
                data: yData,
              },
            ],
          };
          myEc.setOption(option);
          // tools.loopShowTooltip(myEc, option, { loopSeries: true });
        },
        //绘制企业风险等级趋势
        getChart09(id, cate, type, data, left) {
          echarts.init(document.getElementById(id)).dispose();
          let myChart = echarts.init(document.getElementById(id));
          //数据
          var XName = data.map((item) => {
            return item.time;
          });
          var data1_1 = data.map((item) => {
            return item.value;
          });
          var data2_1 =
            data.map((item) => {
              return item.value1;
            }) || [];
          var data3_1 =
            data.map((item) => {
              return item.value2;
            }) || [];
          var data4_1 =
            data.map((item) => {
              return item.value3;
            }) || [];
          // if(type==='1'){
          //     data1_1=data.map((item)=>{return item.value});
          // }else{

          // }
          var img = [
            "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABRCAYAAABFTSEIAAAACXBIWXMAAAsSAAALEgHS3X78AAAEp0lEQVR42u3cz4sjRRTA8W9Vd3Vn8mMmjj9WQWSRZQ+CsH+B7MnDIgiCd0E8CYJ/gOAIelo8ehUP/gF6WLw5/gMueFP2sIcF0dHd2Z1kknR11fOQZJJJMtlZd03H7HtQpNOTnpn+8Lrm1etmjIig8e/DKoECKqACKqCGAiqgAiqghgIqoAIqoIYCKqACKqCGAiqgAiqghgIqoAJudKTr+osZMNPvBUQBHwHsPF9fB9R0DeHMOQ6T6WOrhEzXBM4swDOL0M6CrArRVoq3t2dGUIb9fTvatg8ZZup1PDBgzPmy98mey6qfzjLz2WaWjEUZKEvGyi9nWyneMOvGIyFQo2Sbg4MUSChpU9IeTTUpJdsEajPZOJeJG5uBZj7rLLduWS5dGm6XNLEELOFUFj54ACJCaychkpDSASK3bwsXL0YgVpWJKwM0iy9Zy8HdGru7jvt3Pbu7w0wES7drTwAbjTHMGCsQcIAnYTC1/wRx0wEnl27JNgZI8HQ6Kc1mQq83RNzaMjPzXqDbjTQaJRFLxIyyMSxAXEkWrhrQzAAmo5HOjCQf7jflILxOkohL+aUPgV4vEGNJo+E5PAy02+UIMEwBxo0CPDP7Dg5SnEtpt1PA0e87XO25FOoh8IYIH2Y5b45RzGAQBiIltZoHxqMcjbksXAVgdc2EQMYzzzdotyeZWKuleULXJtwT4SODfC2QCWR+IF9KnjuX1Xbo99Op7LVE8iXlz0YBTk5SyLEEjo5OLuccEoFUvHfO+reuUPx4zftXAIcx1hdcF+/TvFab4A0Bs0VwqyhpVnkJT89/Q4DDQ0e77YCMwIUsFMeFZD856699URRvX4nxE4A/jbnxXp7v4Zw3ReGNSDHI8wFQjIafuoyn58L/fB6sth/Ybg9fez2TRC6QZcZYvgHsazF+MP7YCyLXcM7gvSXLDGBqYDg+NhwdmSpPoTrAkub0W+f4FSB1fDucIunMHSLpO8WAH0rSy8u+19MBCHB4OHzd2pI+CEUhpigEiN+l6WcdY252jLn5s7Wf472ImPcN8pUl/tEHoV4XWq1Ke4KrLmPsTA3oODpytFoOyJKSyzHyMSIxteWngMW5cSEdDJQUhTdZVgxOz3/+jFJm4+bA2e5JpNU6WZ4Fw99JwnWMKccwpeddP+B7GZTNUPKqybJy0O+Hs1YfMz9swwvpB8fbGDG0GuGkkK7V0hxSmZQpABI8l2z0v3sJf50qpAMJCd2qCulql3LD1lRGQjm7lEsDz0rkxTQOfiPPxUBcuJTbbhss/Y1eyi3NwsmKInmkZsKk5gtPUzNhvp11507CSy/X6XYStpvFudpZw1ZWIOF4Cq6SdtbKbioJyAhRTu3u9yMJXerN+ugvaQQsjcZ8Q3VnZwxlSDhe1lB9GjrSw5b+1avT8+Jw+979nNaOI6U3KpTrWAosxVQmygK4ld8X0ZtK/7eViExD7O1NQPb3T7fsl4/4sBpwYzPwjFbTo95Yl9l9Vd1YN1X/147HebSjary1AHyc5qc+XLQEQx9ve8Kg6xr6hKoCKqACKqCGAiqgAiqghgIqoAIqoIYCKqACKqCGAiqgAiqghgIq4JrHP8fEWV8FMTmOAAAAAElFTkSuQmCC",
            "image://data:image/png;base64,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",
            "image://data:image/png;base64,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",
            "image://data:image/png;base64,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",
          ];

          var data1_2 = [{ coords: [] }];
          var data2_2 = [{ coords: [] }];
          var data3_2 = [{ coords: [] }];
          var data4_2 = [{ coords: [] }];
          for (let i = 0; i < XName.length; i++) {
            data1_2[0].coords.push([XName[i], data1_1[i]]);
            data2_2[0].coords.push([XName[i], data2_1[i]]);
            data3_2[0].coords.push([XName[i], data3_1[i]]);
            data4_2[0].coords.push([XName[i], data4_1[i]]);
          }
          let series = [];
          if (cate === "1") {
            series = [
              {
                symbolSize: 150,
                symbol: img[2],
                smooth: true,
                name: "当前",
                type: "line",
                data: data1_1,
                itemStyle: {
                  normal: {
                    borderWidth: 5,
                    color: "#0696f9",
                  },
                },
              },
              {
                name: "",
                type: "lines",
                coordinateSystem: "cartesian2d",
                symbolSize: 30,
                polyline: true,
                effect: {
                  show: true,
                  trailLength: 0,
                  symbol: "arrow",
                  period: 10, //光点滑动速度
                  symbolSize: 150,
                  symbol: img[0],
                },
                lineStyle: {
                  normal: {
                    width: 1,
                    opacity: 0.6,
                    curveness: 0.2,
                  },
                },
                data: data1_2,
              },
            ];
          } else if ((cate = 2)) {
            series = [
              {
                symbolSize: 150,
                symbol: img[2],
                smooth: true,
                name: "综合基础信息",
                type: "line",
                data: data1_1,
                itemStyle: {
                  normal: {
                    borderWidth: 5,
                    color: "#0696f9",
                  },
                },
              },
              {
                symbolSize: 150,
                symbol: img[3],
                smooth: true,
                name: "执法信息",
                type: "line",
                data: data2_1,
                itemStyle: {
                  normal: {
                    borderWidth: 5,
                    color: "#d4dd48",
                  },
                },
              },
              {
                name: "",
                type: "lines",
                coordinateSystem: "cartesian2d",
                symbolSize: 30,
                polyline: true,
                effect: {
                  show: true,
                  trailLength: 0,
                  symbol: "arrow",
                  period: 10, //光点滑动速度
                  symbolSize: 150,
                  symbol: img[0],
                },
                lineStyle: {
                  normal: {
                    width: 1,
                    opacity: 0.6,
                    curveness: 0.2,
                  },
                },
                data: data1_2,
              },
              {
                name: "",
                type: "lines",
                coordinateSystem: "cartesian2d",
                symbolSize: 30,
                polyline: true,
                effect: {
                  show: true,
                  trailLength: 0,
                  symbol: "arrow",
                  period: 10, //光点滑动速度
                  symbolSize: 150,
                  symbol: img[1],
                },
                lineStyle: {
                  normal: {
                    width: 1,
                    opacity: 0.6,
                    curveness: 0.2,
                  },
                },
                data: data2_2,
              },
              {
                symbolSize: 150,
                symbol: img[2],
                smooth: true,
                name: "培训信息",
                type: "line",
                data: data3_1,
                itemStyle: {
                  normal: {
                    borderWidth: 5,
                    color: "#6e0b9b",
                  },
                },
              },
              {
                symbolSize: 150,
                symbol: img[3],
                smooth: true,
                name: "监测信息",
                type: "line",
                data: data4_1,
                itemStyle: {
                  normal: {
                    borderWidth: 5,
                    color: "red",
                  },
                },
              },
              {
                name: "",
                type: "lines",
                coordinateSystem: "cartesian2d",
                symbolSize: 30,
                polyline: true,
                effect: {
                  show: true,
                  trailLength: 0,
                  symbol: "arrow",
                  period: 10, //光点滑动速度
                  symbolSize: 150,
                  symbol: img[0],
                },
                lineStyle: {
                  normal: {
                    width: 1,
                    opacity: 0.6,
                    curveness: 0.2,
                  },
                },
                data: data3_2,
              },
              {
                name: "",
                type: "lines",
                coordinateSystem: "cartesian2d",
                symbolSize: 30,
                polyline: true,
                effect: {
                  show: true,
                  trailLength: 0,
                  symbol: "arrow",
                  period: 10, //光点滑动速度
                  symbolSize: 150,
                  symbol: img[1],
                },
                lineStyle: {
                  normal: {
                    width: 1,
                    opacity: 0.6,
                    curveness: 0.2,
                  },
                },
                data: data4_2,
              },
            ];
          } else {
            series = [
              {
                symbolSize: 150,
                symbol: img[2],
                smooth: true,
                name: "非煤矿山安全隐患",
                type: "line",
                data: data1_1,
                itemStyle: {
                  normal: {
                    borderWidth: 5,
                    color: "#0696f9",
                  },
                },
              },
              {
                symbolSize: 150,
                symbol: img[3],
                smooth: true,
                name: "重大危险源安全隐患",
                type: "line",
                data: data2_1,
                itemStyle: {
                  normal: {
                    borderWidth: 5,
                    color: "#d4dd48",
                  },
                },
              },
              {
                name: "",
                type: "lines",
                coordinateSystem: "cartesian2d",
                symbolSize: 30,
                polyline: true,
                effect: {
                  show: true,
                  trailLength: 0,
                  symbol: "arrow",
                  period: 10, //光点滑动速度
                  symbolSize: 150,
                  symbol: img[0],
                },
                lineStyle: {
                  normal: {
                    width: 1,
                    opacity: 0.6,
                    curveness: 0.2,
                  },
                },
                data: data1_2,
              },
              {
                name: "",
                type: "lines",
                coordinateSystem: "cartesian2d",
                symbolSize: 30,
                polyline: true,
                effect: {
                  show: true,
                  trailLength: 0,
                  symbol: "arrow",
                  period: 10, //光点滑动速度
                  symbolSize: 150,
                  symbol: img[1],
                },
                lineStyle: {
                  normal: {
                    width: 1,
                    opacity: 0.6,
                    curveness: 0.2,
                  },
                },
                data: data2_2,
              },
              {
                symbolSize: 150,
                symbol: img[2],
                smooth: true,
                name: "危化品安全隐患",
                type: "line",
                data: data3_1,
                itemStyle: {
                  normal: {
                    borderWidth: 5,
                    color: "#6e0b9b",
                  },
                },
              },
              {
                symbolSize: 150,
                symbol: img[3],
                smooth: true,
                name: "矿山安全隐患",
                type: "line",
                data: data4_1,
                itemStyle: {
                  normal: {
                    borderWidth: 5,
                    color: "red",
                  },
                },
              },
              {
                name: "",
                type: "lines",
                coordinateSystem: "cartesian2d",
                symbolSize: 30,
                polyline: true,
                effect: {
                  show: true,
                  trailLength: 0,
                  symbol: "arrow",
                  period: 10, //光点滑动速度
                  symbolSize: 150,
                  symbol: img[0],
                },
                lineStyle: {
                  normal: {
                    width: 1,
                    opacity: 0.6,
                    curveness: 0.2,
                  },
                },
                data: data3_2,
              },
              {
                name: "",
                type: "lines",
                coordinateSystem: "cartesian2d",
                symbolSize: 30,
                polyline: true,
                effect: {
                  show: true,
                  trailLength: 0,
                  symbol: "arrow",
                  period: 10, //光点滑动速度
                  symbolSize: 150,
                  symbol: img[1],
                },
                lineStyle: {
                  normal: {
                    width: 1,
                    opacity: 0.6,
                    curveness: 0.2,
                  },
                },
                data: data4_2,
              },
            ];
          }

          let option = {
            grid: {
              top: "20%",
              left: left,
              bottom: "20%",
              right: "5%",
            },
            tooltip: {
              trigger: "axis",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "28",
              },
            },
            legend: {
              textStyle: {
                color: "#ffff",
                // fontSize: 30,
              },
            },
            //   legend: {
            //     // orient: 'vertical',
            //     itemWidth: 18,
            //     itemHeight: 18,
            //     right: "10%",
            //     top: "5%",
            //     icon: "circle",
            //     itemGap: 45,
            //     textStyle: {
            //       color: "#D6E7F9",
            //       fontSize: 28,
            //       padding: [0, 0, 0, 20],
            //     },
            //     data: ["当年", "上年"],
            //   },
            yAxis: [
              {
                name: "",
                type: "value",
                position: "left",
                nameTextStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                  padding: [0, 0, 20, 0],
                },
                splitLine: {
                  lineStyle: {
                    type: "dashed",
                    color: "rgba(135,140,147,0.8)",
                  },
                },
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: true,
                  lineStyle: {
                    color: "#77b3f1",
                    opacity: 0.8,
                    width: 2,
                  },
                },
                axisLabel: {
                  textStyle: {
                    fontSize: 28,
                    color: "#D6E7F9",
                  },
                },
              },
            ],
            xAxis: [
              {
                type: "category",
                axisTick: {
                  show: false,
                },
                offset: 20,
                axisLine: {
                  show: false,
                  lineStyle: {
                    color: "#0696f9",
                  },
                },
                axisLabel: {
                  inside: false,
                  textStyle: {
                    fontSize: 28,
                    color: "#D6E7F9",
                    lineHeight: 22,
                  },
                },
                data: XName,
              },
            ],
            series: series,
          };
          myChart.setOption(option);
        },
        //绘制企业概况饼图
        getChart04(id, chartData) {
          let myCharts = echarts.init(document.getElementById(id));
          let colorList = ["#0090ff", "#06d3c4", "#ecc935", "#7fff00"];
          let option = {
            tooltip: {
              trigger: "item",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "24",
              },
              formatter: function (param) {
                return param.data.name + ":" + param.value + "个";
              },
            },
            legend: {
              x: "60%",
              y: "center",
              orient: "vertical",
              itemWidth: 20,
              itemHeight: 20,
              textStyle: {
                color: "#fff",
                fontSize: 24,
              },
            },
            series: [
              {
                name: "整体分类",
                type: "pie",
                radius: [0, "80%"],
                center: ["30%", "50%"],
                label: {
                  position: "inner",
                  textStyle: {
                    color: "#fff",
                    fontSize: 24,
                  },
                  formatter: function (param) {
                    return param.value + "%";
                  },
                },
                itemStyle: {
                  normal: {
                    borderColor: "#fff",
                    borderWidth: 0,
                    color: function (params) {
                      return colorList[params.dataIndex];
                    },
                  },
                },
                selectedMode: "single",
                data: chartData,
              },
            ],
          };
          myCharts.setOption(option);
          myCharts.getZr().on("mousemove", (param) => {
            myCharts.getZr().setCursorStyle("default");
          });
        },
        //绘制危化品运输top5
        getChart03(id, res) {
          const myCharts = echarts.init(document.getElementById(id));
          var data = [];
          var titlename = [];
          var valdata = [];
          res.forEach((item, index) => {
            data.push(item.value);
            titlename.push(index + 1 + "." + item.name);
            valdata.push(item.value);
          });
          var myColor = ["#1089E7", "#F57474", "#56D0E3", "#F8B448", "#8B78F6"];
          let option = {
            grid: {
              left: "-10%",
              right: "0%",
              top: "10%",
              bottom: "0%",
              containLabel: true,
            },
            xAxis: {
              show: false,
            },
            yAxis: [
              {
                show: false,
                data: titlename,
                inverse: true,
                axisLine: {
                  show: false,
                },

                splitLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  textStyle: {
                    color: function (value, index) {
                      var num = myColor.length;
                      return myColor[index % num];
                    },
                  },
                  formatter: function (value, index) {
                    return ["{title|" + value + "} "].join("\n");
                  },
                  rich: {},
                },
              },
              {
                show: true,
                inverse: true,
                data: valdata,
                axisLabel: {
                  textStyle: {
                    fontSize: 30,
                    color: function (value, index) {
                      var num = myColor.length;
                      return myColor[index % num];
                    },
                  },
                },
                axisLine: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
              },
            ],
            series: [
              {
                name: "条",
                type: "bar",
                yAxisIndex: 0,
                data: data,
                barWidth: 30,
                itemStyle: {
                  normal: {
                    barBorderRadius: 30,
                    color: function (params) {
                      var num = myColor.length;
                      return myColor[params.dataIndex % num];
                    },
                  },
                },
                label: {
                  normal: {
                    show: true,
                    position: "insideLeft",
                    padding: [0, 10],
                    formatter: "{b}",
                    fontSize: 24,
                    color: "#fff",
                  },
                },
              },
            ],
          };

          myCharts.setOption(option);
        },
        //绘制危化品运输情况
        getChart06(id, chartData) {
          const myCharts = echarts.init(document.getElementById(id));
          var legend = ["偏离路线", "事故求救", "执行运输任务", "驾驶疲劳"];
          var colorList = ["#5087EC", "#68BBC4", "#58A55C"];
          var data = [];
          let x = chartData.map((item) => {
            return item.name;
          });
          let y1 = chartData.map((item) => {
            return item.value1;
          });
          let y2 = chartData.map((item) => {
            return item.value2;
          });
          let y3 = chartData.map((item) => {
            return item.value3;
          });
          let y4 = chartData.map((item) => {
            return item.value4;
          });
          data.push(y1, y2, y3, y4);
          let option = {
            tooltip: {
              trigger: "item",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "24",
              },
            },
            // color: colors,
            legend: {
              x: "30%",
              y: "15",
              itemWidth: 20,
              itemHeight: 20,
              textStyle: {
                color: "#fff",
                fontSize: 20,
              },
              data: legend,
            },
            grid: {
              left: "3%",
              right: "4%",
              bottom: "0%",
              top: "20%",
              containLabel: true,
            },
            xAxis: {
              type: "category",
              axisLine: {
                lineStyle: {
                  color: "#77b3f1",
                  opacity: 0.3,
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                // interval: 0,
                // rotate: -30,
                textStyle: {
                  fontSize: 24,
                  color: "white",
                },
              },
              data: x,
            },
            yAxis: [
              {
                name: "",
                type: "value",
                nameTextStyle: {
                  fontSize: 24,
                  color: "#D6E7F9",
                  // padding:[-15,0]
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: "#77b3f1",
                    opacity: 0.1,
                    width: 2,
                  },
                },
                axisTick: {
                  show: true,
                  lineStyle: {
                    color: "#77b3f1",
                    opacity: 0.5,
                    width: 2,
                  },
                },
                axisLabel: {
                  textStyle: {
                    fontSize: 24,
                    color: "#D6E7F9",
                  },
                },
              },
            ],
            series: [],
          };
          for (var i = 0; i < legend.length; i++) {
            option.series.push({
              name: legend[i],
              type: "bar",
              stack: "总量",
              barWidth: 20,
              itemStyle: {
                normal: {
                  color: colorList[i],
                },
              },
              label: {
                show: false,
                position: "inside",
                textStyle: {
                  color: "#fff",
                  fontSize: 24,
                },
              },
              data: data[i],
            });
          }
          myCharts.setOption(option);
          myCharts.getZr().on("mousemove", (param) => {
            myCharts.getZr().setCursorStyle("default");
          });
        },
        openIframe() {
          let Iframe = {
            type: "openIframe",
            name: "shgl-map",
            src:
              baseURL.url + "/static/citybrain/shgl/commont/aqyhqyfb-map.html",
            left: "2680px",
            top: "230px",
            width: "350px",
            height: "470px",
            zIndex: "10",
            argument: {
              status: "shgl-map",
            },
          };
          window.parent.postMessage(JSON.stringify(Iframe), "*");
        },
        openIframe1() {
          let Iframe = {
            type: "openIframe",
            name: "shgl-bottom",
            src:
              baseURL.url + "/static/citybrain/shgl/pages/aqyhqyfx-bottom.html",
            left: "calc(50% - 1089px)",
            top: "1484px",
            width: "2178px",
            height: "600px",
            zIndex: "10",
            argument: {
              status: "shgl-bottom",
            },
          };
          window.parent.postMessage(JSON.stringify(Iframe), "*");
        },
        openIframe2() {
          let Iframe = {
            type: "openIframe",
            name: "scroll-alert",
            src:
              baseURL.url + "/static/citybrain/shgl/commont/scroll-alert.html",
            left: "calc(50% - 570px)",
            top: "200px",
            width: "1200px",
            height: "140px",
            zIndex: "10",
            argument: {
              status: "scroll-alert",
            },
          };
          window.parent.postMessage(JSON.stringify(Iframe), "*");
        },

        initMap() {
          top.document.getElementById("map").contentWindow.Work.change3D(7);
          this.flyTo();
          this.addPoint();
        },
        //飞入
        flyTo() {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "flyto", //功能名称
              flyData: {
                center: [119.95478050597587, 29.01613226366889],
                zoom: 10.5,
                pitch: 40,
                bearing: 0,
              },
            })
          );
        },
        addPoint() {
          let res = [
            {
              title: "浦江县",
              pos: [119.94315399169922, 29.5630503845215],
              text: "地下矿山2093",
            },
            {
              title: "兰溪市",
              pos: [119.46214447021484, 29.31345558166504],
              text: "1096",
            },
            {
              title: "婺城区",
              pos: [119.5569204711914, 29.00677101135254],
              text: "2288",
            },
            {
              title: "婺城区",
              pos: [119.64896993689013, 29.090742350540673],
              text: "3057",
            },
            {
              title: "婺城区",
              pos: [119.75305962805735, 29.17810148693506],
              text: "2123",
            },
          ];
          let arr = res.map((item) => {
            return {
              data: {},
              point: item.pos[0] + "," + item.pos[1],
            };
          });
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "pointLoad",
              pointType: "rckz-住宅区", // 点位类型（图标名称）
              pointId: "point1", // 点位唯一id
              setClick: true,
              pointData: arr,
              imageConfig: { iconSize: 2 },
              popup: {
                offset: [50, -100],
              },
            })
          );
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "3Dtext", //3D文字功能
              textData: res,
              textSize: 40,
              id: "text1",
              // zoomShow: true,
              color: [255, 255, 255, 1],
            })
          );
        },
        //清除点位
        rmPoint() {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmPoint",
              pointId: "",
            })
          );
        },
        getEcharts(id, data) {
          //数据
          let myCharts = echarts.init(document.getElementById(id));

          let name = data.map((item) => item.time);
          var XName = name;
          let y = data.map((item) => item.value);
          let y1 = data.map((item) => item.value1);
          let y2 = data.map((item) => item.value2);
          let y3 = data.map((item) => item.value3);
          var data1 = [y, y1, y2, y3];
          var Line = [
            "非煤矿山安全隐患",
            "重大危险源安全隐患",
            "危化品安全隐患",
            "矿山安全隐患",
          ];
          var img = [
            "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAABRCAYAAABFTSEIAAAACXBIWXMAAAsSAAALEgHS3X78AAAEp0lEQVR42u3cz4sjRRTA8W9Vd3Vn8mMmjj9WQWSRZQ+CsH+B7MnDIgiCd0E8CYJ/gOAIelo8ehUP/gF6WLw5/gMueFP2sIcF0dHd2Z1kknR11fOQZJJJMtlZd03H7HtQpNOTnpn+8Lrm1etmjIig8e/DKoECKqACKqCGAiqgAiqghgIqoAIqoIYCKqACKqCGAiqgAiqghgIqoAJudKTr+osZMNPvBUQBHwHsPF9fB9R0DeHMOQ6T6WOrhEzXBM4swDOL0M6CrArRVoq3t2dGUIb9fTvatg8ZZup1PDBgzPmy98mey6qfzjLz2WaWjEUZKEvGyi9nWyneMOvGIyFQo2Sbg4MUSChpU9IeTTUpJdsEajPZOJeJG5uBZj7rLLduWS5dGm6XNLEELOFUFj54ACJCaychkpDSASK3bwsXL0YgVpWJKwM0iy9Zy8HdGru7jvt3Pbu7w0wES7drTwAbjTHMGCsQcIAnYTC1/wRx0wEnl27JNgZI8HQ6Kc1mQq83RNzaMjPzXqDbjTQaJRFLxIyyMSxAXEkWrhrQzAAmo5HOjCQf7jflILxOkohL+aUPgV4vEGNJo+E5PAy02+UIMEwBxo0CPDP7Dg5SnEtpt1PA0e87XO25FOoh8IYIH2Y5b45RzGAQBiIltZoHxqMcjbksXAVgdc2EQMYzzzdotyeZWKuleULXJtwT4SODfC2QCWR+IF9KnjuX1Xbo99Op7LVE8iXlz0YBTk5SyLEEjo5OLuccEoFUvHfO+reuUPx4zftXAIcx1hdcF+/TvFab4A0Bs0VwqyhpVnkJT89/Q4DDQ0e77YCMwIUsFMeFZD856699URRvX4nxE4A/jbnxXp7v4Zw3ReGNSDHI8wFQjIafuoyn58L/fB6sth/Ybg9fez2TRC6QZcZYvgHsazF+MP7YCyLXcM7gvSXLDGBqYDg+NhwdmSpPoTrAkub0W+f4FSB1fDucIunMHSLpO8WAH0rSy8u+19MBCHB4OHzd2pI+CEUhpigEiN+l6WcdY252jLn5s7Wf472ImPcN8pUl/tEHoV4XWq1Ke4KrLmPsTA3oODpytFoOyJKSyzHyMSIxteWngMW5cSEdDJQUhTdZVgxOz3/+jFJm4+bA2e5JpNU6WZ4Fw99JwnWMKccwpeddP+B7GZTNUPKqybJy0O+Hs1YfMz9swwvpB8fbGDG0GuGkkK7V0hxSmZQpABI8l2z0v3sJf50qpAMJCd2qCulql3LD1lRGQjm7lEsDz0rkxTQOfiPPxUBcuJTbbhss/Y1eyi3NwsmKInmkZsKk5gtPUzNhvp11507CSy/X6XYStpvFudpZw1ZWIOF4Cq6SdtbKbioJyAhRTu3u9yMJXerN+ugvaQQsjcZ8Q3VnZwxlSDhe1lB9GjrSw5b+1avT8+Jw+979nNaOI6U3KpTrWAosxVQmygK4ld8X0ZtK/7eViExD7O1NQPb3T7fsl4/4sBpwYzPwjFbTo95Yl9l9Vd1YN1X/147HebSjary1AHyc5qc+XLQEQx9ve8Kg6xr6hKoCKqACKqCGAiqgAiqghgIqoAIqoIYCKqACKqCGAiqgAiqghgIq4JrHP8fEWV8FMTmOAAAAAElFTkSuQmCC",
            "image://data:image/png;base64,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",
            "image://data:image/png;base64,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",
            "image://data:image/png;base64,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",
          ];
          var color = ["#00f8ff", "#00f15a", "#0696f9", "#dcf776"];

          //数据处理
          var datas = [];
          Line.map((item, index) => {
            datas.push({
              symbolSize: 150,
              symbol: img[index],
              name: item,
              type: "line",
              yAxisIndex: 1,
              data: data1[index],
              itemStyle: {
                normal: {
                  borderWidth: 5,
                  color: color[index],
                },
              },
            });
          });

          let option = {
            tooltip: {
              trigger: "axis",
              textStyle: {
                fontSize: 30,
              },
            },
            grid: {
              left: "10%",
              top: "25%",
              bottom: "10%",
              right: "5%",
            },

            yAxis: [
              {
                type: "value",
                position: "right",
                splitLine: {
                  show: false,
                },
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  color: "#fff",
                  fontSize: 30,
                },
              },
              {
                type: "value",
                position: "left",
                nameTextStyle: {
                  color: "#00FFFF",
                },
                splitLine: {
                  lineStyle: {
                    type: "dashed",
                    color: "rgba(135,140,147,0.8)",
                  },
                },
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  formatter: "{value}",
                  color: "#fff",
                  fontSize: 30,
                },
              },
            ],
            xAxis: [
              {
                type: "category",
                axisTick: {
                  show: false,
                },
                axisLine: {
                  show: false,
                  lineStyle: {
                    color: "#6A989E",
                  },
                },
                axisLabel: {
                  inside: false,
                  textStyle: {
                    color: "#fff", // x轴颜色
                    fontWeight: "normal",
                    fontSize: 30,
                    lineHeight: 22,
                  },
                },
                data: XName,
              },
            ],
            series: datas,
          };
          myCharts.setOption(option);
        },
      },
      destroyed() {
        this.rmPoint();
      },
    });
  </script>
</html>
