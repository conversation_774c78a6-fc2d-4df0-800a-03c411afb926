<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>食品药品监管指标分析</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
      }
      #app {
        width: 3840px;
        height: 1930px;
        background: url("/img/left-bg.png") no-repeat;
        background-size: 100% 100%;
        overflow: hidden;
      }
      .row {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
      }
      .tab1 {
        width: 80%;
        margin-left: 10%;
        margin-top: 20px;
        height: 50px;
        border: 1px solid #00c0ff;
        border-right: 0px solid #00c0ff;
        display: flex;
        align-items: center;
      }

      .tab1 > div {
        flex: 1;
        font-size: 28px;
        height: 50px;
        line-height: 50px;
        color: #00c0ff;
        text-align: center;
        border-right: 1px solid #00c0ff;
        cursor: pointer;
      }

      .tab1-active {
        background-color: #00c0ff;
        color: #fff !important;
      }
      .echart {
        width: 100%;
        height: 500px;
        /* border: 2px #fff solid; */
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="row">
        <div class="row_box" style="flex: 1">
          <nav style="width: 100%">
            <s-header-title-2 title="总体情况展示" htype="2"></s-header-title-2>
          </nav>
          <div class="tab1">
            <div
              v-for="(item,i) in data0"
              :key="i"
              :class="{'tab1-active':tabActive==i+1}"
              @click="clickTab(i)"
            >
              {{item}}
            </div>
          </div>
          <div style="width: 100%; height: 650px" id="echarts001"></div>
        </div>
        <div class="row_box" style="flex: 2">
          <nav style="width: 100%">
            <s-header-title-2
              title="食品药品监管统计分析"
              htype="1"
            ></s-header-title-2>
          </nav>
          <div class="row">
            <div style="width: 130%; height: 370px" id="echarts002"></div>
            <div style="width: 70%; height: 370px" id="echarts003"></div>
          </div>
          <div style="width: 100%; height: 370px" id="echarts004"></div>
        </div>
        <div class="row_box" style="flex: 1">
          <nav style="width: 100%">
            <s-header-title-2
              title="食品药品抽检统计分析"
              htype="2"
            ></s-header-title-2>
          </nav>
          <div style="width: 100%; height: 730px" id="echarts005"></div>
        </div>
      </div>
      <div class="row">
        <nav style="width: 50%">
          <s-header-title-2 title="抽检查询" htype="1"></s-header-title-2>
        </nav>
        <nav style="width: 50%">
          <s-header-title-2
            title="稽查执法情况统计分析"
            htype="1"
          ></s-header-title-2>
        </nav>
      </div>
      <div class="row">
        <div class="echart" id="echarts006"></div>
        <div class="echart" id="echarts007"></div>
        <div class="echart" id="echarts008"></div>
        <div class="echart" id="echarts009"></div>
      </div>
      <div class="row">
        <div class="echart" id="echarts010"></div>
        <div class="echart" id="echarts011"></div>
        <div class="echart" id="echarts012"></div>
        <div class="echart" id="echarts013"></div>
      </div>
    </div>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
      let vm = new Vue({
        el: "#app",
        data() {
          return {
            data0: ["年", "季度", "月"],
            tabActive: 1,
          };
        },
        mounted() {
          this.initEc();
        },
        methods: {
          initEc() {
            $api("/ldst_scjg_spypjgzbfx", { type: "1_1" }).then((res) => {
              let arr = res[0].describe.split(",");
              let lengd = arr.filter((val, index, arr) => {
                return index !== 0;
              });

              let ydata = [];
              let xdata = res.map((e) => e.name);
              ydata[0] = res.map((e) => e.value1);
              ydata[1] = res.map((e) => e.value2);
              this.setBar("echarts001", lengd, xdata, ydata, 20, {
                left: "次",
                right: "%",
              });
            });
            $api("/ldst_scjg_spypjgzbfx", { type: "2" }).then((res) => {
              let arr = res[0].describe.split(",");
              let lengd = arr.filter((val, index, arr) => {
                return index !== 0;
              });

              let ydata = [];
              let xdata = res.map((e) => e.name);
              ydata[0] = res.map((e) => e.value1);
              this.setBar("echarts002", lengd, xdata, ydata, 0, {
                left: "个",
              });
            });
            $api("/ldst_scjg_spypjgzbfx", { type: "3" }).then((res) => {
              let data = JSON.parse(
                JSON.stringify(res).replace(/value1/g, "value")
              );
              this.setPie("echarts003", data);
            });
            $api("/ldst_scjg_spypjgzbfx", { type: "4" }).then((res) => {
              let arr = res[0].describe.split(",");
              let lengd = arr.filter((val, index, arr) => {
                return index !== 0;
              });

              let ydata = [];
              let xdata = res.map((e) => e.name);
              ydata[0] = res.map((e) => e.value1);
              this.setBar("echarts004", lengd, xdata, ydata, 0, {
                left: "个",
              });
            });
            $api("/ldst_scjg_spypjgzbfx", { type: "5" }).then((res) => {
              let arr = res[0].describe.split(",");
              let lengd = arr.filter((val, index, arr) => {
                return index !== 0;
              });

              let ydata = [];
              let xdata = res.map((e) => e.name);
              ydata[0] = res.map((e) => e.value1);
              ydata[1] = res.map((e) => e.value2);
              ydata[2] = res.map((e) => e.value3);
              this.setBar("echarts005", lengd, xdata, ydata, 0, {
                left: "次 ",
                right: "次",
              });
            });
            $api("/ldst_scjg_spypjgzbfx", { type: "6" }).then((res) => {
              let arr = res[0].describe.split(",");
              let lengd = arr.filter((val, index, arr) => {
                return index !== 0;
              });

              let ydata = [];
              let xdata = res.map((e) => e.name);
              ydata[0] = res.map((e) => e.value1);
              this.setBar("echarts006", lengd, xdata, ydata, 0, {
                left: "次",
              });
            });
            $api("/ldst_scjg_spypjgzbfx", { type: "7" }).then((res) => {
              let arr = res[0].describe.split(",");
              let lengd = arr.filter((val, index, arr) => {
                return index !== 0;
              });

              let ydata = [];
              let xdata = res.map((e) => e.name);
              ydata[0] = res.map((e) => e.value1);
              this.setBar("echarts007", lengd, xdata, ydata, 20, {
                left: "次",
              });
            });
            $api("/ldst_scjg_spypjgzbfx", { type: "10" }).then((res) => {
              let arr = res[0].describe.split(",");
              let lengd = arr.filter((val, index, arr) => {
                return index !== 0;
              });

              let ydata = [];
              let xdata = res.map((e) => e.name);
              ydata[0] = res.map((e) => e.value1);
              this.setBar("echarts008", lengd, xdata, ydata, 0, {
                left: "次",
              });
            });
            $api("/ldst_scjg_spypjgzbfx", { type: "11" }).then((res) => {
              let arr = res[0].describe.split(",");
              let lengd = arr.filter((val, index, arr) => {
                return index !== 0;
              });

              let ydata = [];
              let xdata = res.map((e) => e.name);
              ydata[0] = res.map((e) => e.value1);
              this.setBar("echarts009", lengd, xdata, ydata, 20, {
                left: "次",
              });
            });
            $api("/ldst_scjg_spypjgzbfx", { type: "8" }).then((res) => {
              let arr = res[0].describe.split(",");
              let lengd = arr.filter((val, index, arr) => {
                return index !== 0;
              });

              let ydata = [];
              let xdata = res.map((e) => e.name);
              ydata[0] = res.map((e) => e.value1);
              ydata[1] = res.map((e) => e.value2);
              this.setBar("echarts010", lengd, xdata, ydata, 0, {
                left: "次",
                right: "次",
              });
            });
            $api("/ldst_scjg_spypjgzbfx", { type: "9" }).then((res) => {
              let arr = res[0].describe.split(",");
              let lengd = arr.filter((val, index, arr) => {
                return index !== 0;
              });

              let ydata = [];
              let xdata = res.map((e) => e.name);
              ydata[0] = res.map((e) => e.value1);
              ydata[1] = res.map((e) => e.value2);
              this.setBar("echarts011", lengd, xdata, ydata, 20, {
                left: "次",
                right: "次",
              });
            });
            $api("/ldst_scjg_spypjgzbfx", { type: "12" }).then((res) => {
              let arr = res[0].describe.split(",");
              let lengd = arr.filter((val, index, arr) => {
                return index !== 0;
              });

              let ydata = [];
              let xdata = res.map((e) => e.name);
              ydata[0] = res.map((e) => e.value1);
              ydata[1] = res.map((e) => e.value2);
              this.setBar("echarts012", lengd, xdata, ydata, 0, {
                left: "次",
                right: "次",
              });
            });
            $api("/ldst_scjg_spypjgzbfx", { type: "13" }).then((res) => {
              let arr = res[0].describe.split(",");
              let lengd = arr.filter((val, index, arr) => {
                return index !== 0;
              });

              let ydata = [];
              let xdata = res.map((e) => e.name);
              ydata[0] = res.map((e) => e.value1);
              ydata[1] = res.map((e) => e.value2);
              this.setBar("echarts013", lengd, xdata, ydata, 20, {
                left: "次",
                right: "次",
              });
            });
          },
          clickTab(index) {
            this.tabActive = index + 1;
            let typeCode = `1_${index + 1}`;
            $api("/ldst_scjg_spypjgzbfx", { type: typeCode }).then((res) => {
              let arr = res[0].describe.split(",");
              let lengd = arr.filter((val, index, arr) => {
                return index !== 0;
              });

              let ydata = [];
              let xdata = res.map((e) => e.name);
              ydata[0] = res.map((e) => e.value1);
              ydata[1] = res.map((e) => e.value2);
              this.setBar("echarts001", lengd, xdata, ydata, 10, {
                left: "次",
                right: "%",
              });
            });
          },
          setBar(id, lend, xdata, vdata, xtext, ytext) {
            const myChartsDivine = echarts.init(document.getElementById(id));
            let option = {
              color: ["#5087ec", "#51dd90", "#68bbc4"],
              grid: {
                left: "10%",
                top: "18%",
                right: "10%",
                bottom: "12%",
              },
              tooltip: {
                borderWidth: 0,
                trigger: "axis",
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "30",
                },
                axisPointer: {
                  type: "shadow",
                },
              },
              legend: {
                y: 20,
                textStyle: {
                  color: "#fff",
                  fontSize: 28,
                },
                data: lend,
              },
              xAxis: [
                {
                  type: "category",
                  data: xdata,
                  axisPointer: {
                    type: "shadow",
                  },
                  axisLine: {
                    show: true, //隐藏X轴轴线
                    lineStyle: {
                      color: "#aaa",
                      width: 1,
                    },
                  },
                  axisTick: {
                    show: true, //隐藏X轴刻度
                    //   alignWithLabel: true,
                  },
                  splitLine: {
                    show: false,
                  },
                  axisLabel: {
                    show: true,
                    margin: 20,
                    textStyle: {
                      color: "#fff", //X轴文字颜色
                      fontSize: 28,
                    },
                    interval: 0,
                    rotate: xtext,
                  },
                },
              ],
              yAxis: [
                {
                  type: "value",
                  axisLine: {
                    show: false, //隐藏X轴轴线
                    lineStyle: {
                      color: "#aaa",
                      width: 1,
                    },
                  },
                  axisTick: {
                    show: false, //隐藏X轴刻度
                    alignWithLabel: false,
                  },
                  splitLine: {
                    show: false,
                  },
                  axisLabel: {
                    show: true,
                    formatter: "{value}" + ytext.left,
                    textStyle: {
                      color: "#fff", //X轴文字颜色
                      fontSize: 26,
                    },
                    interval: 0,
                    // rotate: 30,
                  },
                },
                {
                  type: "value",
                  axisLine: {
                    show: false, //隐藏X轴轴线
                    lineStyle: {
                      color: "#aaa",
                      width: 1,
                    },
                  },
                  axisTick: {
                    show: false, //隐藏X轴刻度
                    alignWithLabel: false,
                  },
                  splitLine: {
                    show: false,
                  },
                  axisLabel: {
                    show: true,
                    formatter: "{value}" + ytext.right,
                    textStyle: {
                      color: "#fff", //X轴文字颜色
                      fontSize: 26,
                    },
                    interval: 0,
                    // rotate: 30,
                  },
                },
              ],
              series: [
                {
                  name: lend[0],
                  type: "bar",
                  data: vdata[0],
                },
                {
                  name: lend[1],
                  type: "line",
                  data: vdata[1],
                  yAxisIndex: 1,
                },
                {
                  name: lend[2],
                  type: "bar",
                  data: vdata[2],
                },
              ],
            };
            myChartsDivine.setOption(option);
          },
          setPie(id, pieData) {
            const myEc = echarts.init(document.getElementById(id));
            var fontColor = "#30eee9";
            let option = {
              grid: {
                top: "10%",
                bottom: "0%",
                containLabel: true,
              },
              tooltip: {
                trigger: "item",
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "24",
                },
              },
              color: ["#EE752F", "#5087EC", "#68BBC4", "#58A55C", "#F2BD42"],
              legend: {
                show: false,
              },
              series: [
                {
                  name: "",
                  type: "pie",
                  radius: ["0%", "70%"],
                  center: ["50%", "50%"],
                  data: pieData,
                  itemStyle: {
                    color: "#fff",
                    emphasis: {
                      shadowBlur: 10,
                      shadowOffsetX: 0,
                      shadowColor: "rgba(0, 0, 0, 0.5)",
                    },
                  },
                  itemStyle: {
                    normal: {
                      label: {
                        show: true,
                        color: "#fff",
                        fontSize: "24",
                        formatter: "{b}:\n{c}个",
                      },
                    },
                    labelLine: { show: true },
                  },
                },
              ],
            };
            myEc.setOption(option);
          },
        },
      });
    </script>
  </body>
</html>
