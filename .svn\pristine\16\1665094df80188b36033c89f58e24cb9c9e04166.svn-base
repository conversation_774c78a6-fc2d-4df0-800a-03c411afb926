<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>卫生服务右侧弹窗</title>
        <script src="/Vue/vue.js"></script>
        <script src="/static/js/jslib/datav.min.vue.js"></script>
        <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain/ggfw/css/jyfw-diaolog.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <link rel="stylesheet" href="../css/wsfw-right-dialog.css" />
        <link rel="stylesheet" href="../css/wsfw-left-dialog.css" />

        <!-- 轮播toolTip -->
    </head>
    <style>
        .text[data-v-4d0d1712] {
            font-size: 32px !important;
        }
    </style>

    <body>
        <div id="app" class="jyfw-right-main">
            <div class="trzb" style="display: flex;">
                <!-- 防控投入 -->
                <div>
                    <nav>
                        <s-header-title style="width: 967px" title="防控投入"> </s-header-title>
                    </nav>
                    <div style="width: 967px; height: 310px" id="fktr-chart"></div>
                </div>
                <div>
                    <nav>
                        <s-header-title style="width: 967px" title="全市卫生体制改革"> </s-header-title>
                    </nav>
                    <div style="width: 967px; height: 310px" id="qsws-chart"></div>
                </div>
            </div>
            <div class="ndbh wsBox">
                <div>
                    <nav>
                        <s-header-title title="卫生资源" style="width: 684px"> </s-header-title>
                    </nav>
                    <div class="ajgf">
                        <dv-border-box-8 class="wszy">
                            <div class="select">
                                <el-select v-model="value" placeholder="请选择">
                                    <el-option
                                        v-for="item in option"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </div>
                            <div v-for="(item,index) in wszyList" class="wszyBox">
                                <div>
                                    <div class="name">{{item.name}}</div>
                                    <div class="value valueOne" style="text-align: left">
                                        {{item.value}}<span>{{item.dw}}</span>
                                    </div>
                                </div>
                            </div>
                        </dv-border-box-8>
                    </div>
                </div>
                <div>
                    <nav>
                        <s-header-title title="卫生服务" style="width: 1300px" :click-flag="true" @click="clickToMap(0)"> </s-header-title>
                    </nav>
                    <div style="width: 1300px; height: 310px" id="wsfw-echart"></div>
                </div>
            </div>
            <!-- 卫生工作 -->
            <div style="height: 18%" class="zbdc-sf">
                <nav>
                    <s-header-title title="卫生工作" :data-time="nowTime" :click-flag="true" @click="clickToMap(2)"> </s-header-title>
                </nav>
                <div class="fzgkCon">
                    <li style="margin-top: 15px" v-for="(item,index) in wsgzList" :index="index">
                        <img src="/static/citybrain/ggfw/img/list.png" alt="" style="width: 25px; height: 25px" />
                        <div class="img-right">
                            <div class="name">{{item.name}}</div>
                            <div class="value" style="text-align: left">{{item.value}}<span>{{item.dw}}</span></div>
                        </div>
                    </li>
                </div>
            </div>
            <!-- 教育国际化发展 -->
            <div style="height: 33%" class="gjhfz">
                <nav>
                    <s-header-title title="居民健康" :data-time="nowTime"> </s-header-title>
                </nav>
                <div style="display: flex">
                    <div>
                        <div class="thead">
                            <div class="thTitle" v-for="item in thName">{{item}}</div>
                        </div>
                        <div class="tbody" v-for="item in tableList">
                            <div>{{item.name}}</div>
                            <div>{{item.value}}</div>
                            <div>{{item.value1}}</div>
                            <div>{{item.value2}}</div>
                            <div>{{item.value3}}</div>
                            <div>{{item.value4}}</div>
                        </div>
                    </div>
                    <div>
                        <div style="width: 800px; height: 400px" id="qsjm-echart"></div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    <script type="module">
        new Vue({
            el: "#app",
            data: {
                nowTime: "", //当前时间
                wszyList: [],
                wsgzList: [],
                thName: ["区县", "居民健康总体系数", "基层诊疗人次", "孕妇死亡率", "婴儿死亡率", "平均期望寿命"],
                tableList: [],
                value: "1",
                option: [
                    {
                        value: "1",
                        label: "金东区",
                    },
                    {
                        value: "2",
                        label: "兰溪市",
                    },
                    {
                        value: "3",
                        label: "婺城区",
                    },
                    {
                        value: "4",
                        label: "武义县",
                    },
                    {
                        value: "5",
                        label: "浦江县",
                    },
                    {
                        value: "6",
                        label: "磐安县",
                    },
                    {
                        value: "7",
                        label: "义乌市",
                    },
                    {
                        value: "8",
                        label: "东阳市",
                    },
                    {
                        value: "9",
                        label: "永康市",
                    },
                ],
            },
            methods: {
                //获取当前时间
                getTime() {
                    var data = new Date();
                    var yesterday = new Date(data.setDate(data.getDate() - 1));
                    this.nowTime =
                        yesterday.getFullYear() + "年" + (yesterday.getMonth() + 1) + "月" + yesterday.getDate() + "日";
                },
                //获取数据
                initData() {
                    $api("ggfw_wsfw_wsfw_right001").then((res) => {
                        this.getEcharts01(res);
                    });
                    $api("ggfw_wsfw_wsfw_right002").then((res) => {
                        this.getEcharts02(res);
                    });
                    $api("ggfw_wsfw_wsfw_right003").then((res) => {
                        this.wszyList = res;
                    });
                    $api("ggfw_wsfw_wsfw_right004").then((res) => {
                        this.getEcharts03("wsfw-echart", res);
                    });
                    $api("ggfw_wsfw_wsfw_right005").then((res) => {
                        this.wsgzList = res;
                    });
                    $api("ggfw_wsfw_wsfw_right006").then((res) => {
                        this.tableList = res;
                    });
                    $api("ggfw_wsfw_wsfw_right007").then((res) => {
                        this.getEcharts04("qsjm-echart", res);
                    });
                },


                clickToMap(index){
                    top.emiter.emit('wsfw',index)
                    this.closeDialog()
                },

                closeDialog() {
                    top.commonObj.funCloseIframe({
                        name: "wsfw-diaog1",
                    });
                },


                // 防控投入柱状图
                getEcharts01(Echartsdata) {
                    let myChart = echarts.init(document.getElementById("fktr-chart"));
                    var legend = ["投入资金", "投入人数"];
                    var colorList = ["#66b1ff", "#968212", "#58A55C"];
                    var data = [];
                    let x = Echartsdata.map((item) => {
                        return item.name;
                    });

                    let y2 = Echartsdata.map((item) => {
                        return item.alreadyValue;
                    });
                    let y3 = Echartsdata.map((item) => {
                        return item.stayValue;
                    });
                    data.push(y3, y2);
                    let option = {
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        legend: {
                            x: "center",
                            y: "15",
                            itemWidth: 20,
                            itemHeight: 20,
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                            data: legend,
                        },
                        grid: {
                            left: "3%",
                            right: "4%",
                            bottom: "0",
                            containLabel: true,
                        },
                        xAxis: {
                            type: "category",
                            axisLabel: {
                                color: "#fff",
                                fontSize: 28,
                                // rotate: 45,
                            },
                            axisLine: {
                                show: true,
                                lineStyle: {
                                    color: "#bbb",
                                },
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: "#195384",
                                },
                            },
                            data: x,
                        },
                        yAxis: [
                            {
                                type: "value",
                                name: "单位：万元",
                                min: 0,
                                max: 1600,
                                interval: 400,
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                axisLabel: {
                                    formatter: "{value}",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: "#fff",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#11366e",
                                    },
                                },
                            },
                            {
                                type: "value",
                                name: "单位：人",
                                min: 0,
                                max: 1600,
                                interval: 400,
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.1,
                                        width: 2,
                                    },
                                },
                                axisTick: {
                                    show: true,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.5,
                                        width: 2,
                                    },
                                },
                                axisLabel: {
                                    textStyle: {
                                        fontSize: 30,
                                        color: "#D6E7F9",
                                    },
                                },
                            },
                        ],
                        series: [],
                    };
                    for (var i = 0; i < legend.length; i++) {
                        option.series.push({
                            name: legend[i],
                            type: "bar",
                            stack: "总量",
                            barWidth: 35,
                            itemStyle: {
                                normal: {
                                    // color: colorList[i]
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 0.9, [
                                        {
                                            offset: 0,
                                            color: colorList[i],
                                        },
                                        {
                                            offset: 0.5,
                                            color: colorList[i],
                                        },
                                        {
                                            offset: 1,
                                            color: "#031827",
                                        },
                                    ]),
                                },
                            },
                            label: {
                                show: false,
                                position: "insideRight",
                            },
                            data: data[i],
                        });
                    }
                    myChart.setOption(option);
                    tools.loopShowTooltip(myChart, option, { loopSeries: true });
                },
                // 全市卫生体制改革
                getEcharts02(data) {
                    let myEc = echarts.init(document.getElementById("qsws-chart"));
                    let xData = [],
                        yData = [],
                        y1Data = [];

                    data.forEach((item) => {
                        xData.push(item.name);
                        yData.push(item.value);
                        y1Data.push(item.value1);
                    });
                    var option = {
                        tooltip: {
                            trigger: "axis",
                            axisPointer: {
                                // 坐标轴指示器，坐标轴触发有效
                                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                            },
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "28",
                            },
                        },
                        legend: {
                            orient: "horizontal",
                            top: "2%",
                            width: "50%",
                            textStyle: {
                                color: "#D6E7F9",
                                fontSize: 30,
                            },
                        },
                        grid: {
                            left: "2%",
                            right: "5%",
                            bottom: "8%",
                            top: "25%",
                            containLabel: true,
                        },
                        xAxis: [
                            {
                                type: "category",
                                data: xData,
                                offset: 20,
                                axisLine: {
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.3,
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLabel: {
                                    interval: 0,
                                    // rotate: -30,
                                    textStyle: {
                                        fontSize: 30,
                                        color: "white",
                                    },
                                },
                            },
                        ],
                        yAxis: [
                            {
                                name: "单位：万元",
                                type: "value",
                                max: 1600,
                                min: 0,
                                interval: 400,
                                nameTextStyle: {
                                    fontSize: 30,
                                    color: "#D6E7F9",
                                    padding: [0, 0, 20, 0],
                                },
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.1,
                                        width: 2,
                                    },
                                },
                                axisTick: {
                                    show: true,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.5,
                                        width: 2,
                                    },
                                },
                                axisLabel: {
                                    textStyle: {
                                        fontSize: 30,
                                        color: "#D6E7F9",
                                    },
                                },
                            },
                            {
                                name: "单位：人",
                                type: "value",
                                max: 1.6,
                                interval: 0.4,
                                min: 0,
                                nameTextStyle: {
                                    fontSize: 30,
                                    color: "#D6E7F9",
                                    padding: [0, 0, 20, 0],
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.1,
                                        width: 2,
                                    },
                                },
                                axisTick: {
                                    show: true,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.5,
                                        width: 2,
                                    },
                                },
                                axisLabel: {
                                    textStyle: {
                                        fontSize: 30,
                                        color: "#D6E7F9",
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "卫生体制改革项目数",
                                type: "bar",
                                barWidth: 35,
                                yAxisIndex: 0,
                                smooth: true, //加这个
                                center: ["0%", "45%"],
                                radius: ["0%", "45%"],
                                itemStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                            {
                                                offset: 0,
                                                color: "#00C0FF",
                                            },
                                            {
                                                offset: 0.2,
                                                color: "#00C0FF",
                                            },
                                            {
                                                offset: 1,
                                                color: "#004F69",
                                            },
                                        ]),
                                        barBorderRadius: 4,
                                    },
                                },
                                data: yData,
                            },
                            {
                                name: "卫生体制改革项创新指数",
                                type: "line",
                                smooth: true, //加这个
                                yAxisIndex: 1,
                                itemStyle: {
                                    normal: {
                                        color: "#968212",
                                        barBorderRadius: 4,
                                    },
                                },
                                data: y1Data,
                            },
                        ],
                    };
                    myEc.setOption(option);
                    tools.loopShowTooltip(myEc, option, { loopSeries: true });
                },
                //
                getEcharts03(id, data) {
                    let myEc = echarts.init(document.getElementById(id));
                    let xData = [],
                        yData = [],
                        y1Data = [],
                        y2Data = [],
                        y3Data = [],
                        y4Data = [],
                        y5Data = [];

                    data.forEach((item) => {
                        xData.push(item.name);
                        yData.push(item.value);
                        y1Data.push(item.value1);
                        y2Data.push(item.value2);
                        y3Data.push(item.value3);
                        y4Data.push(item.value4);
                        y5Data.push(item.value5);
                    });
                    let option = {
                        tooltip: {
                            trigger: "axis",
                            axisPointer: {
                                // 坐标轴指示器，坐标轴触发有效
                                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                            },
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "28",
                            },
                        },
                        // color: ["#FF0000", "#00BFFF", "#FF00FF", "#1ce322", "#000000"],
                        legend: {
                            data: ["卫生服务便利度", "诊疗量", "住院量", "网上挂号量", "网上挂号率", "实时结算率"],
                            //    orient: 'horizontal',
                            // itemWidth: 18,
                            // itemHeight: 18,
                            top: "0",
                            // icon: 'rect',
                            itemGap: 5,
                            textStyle: {
                                color: "#D6E7F9",
                                fontSize: 22,
                            },
                        },
                        grid: {
                            left: 0,
                            right: "4%",
                            bottom: "20px",
                            containLabel: true,
                        },
                        // toolbox: {
                        //     feature: {
                        //         saveAsImage: {},
                        //     },
                        // },
                        xAxis: {
                            type: "category",
                            data: xData,
                            offset: 20,
                            axisLine: {
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.3,
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                // rotate: -30,
                                textStyle: {
                                    fontSize: 30,
                                    color: "white",
                                },
                            },
                        },
                        yAxis: [
                            {
                                name: "",
                                type: "value",
                                // max: 800,
                                min: 0,
                                nameTextStyle: {
                                    fontSize: 30,
                                    color: "#D6E7F9",
                                    padding: [0, 0, 20, 0],
                                },
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.1,
                                        width: 2,
                                    },
                                },
                                axisTick: {
                                    show: true,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.5,
                                        width: 2,
                                    },
                                },
                                axisLabel: {
                                    textStyle: {
                                        fontSize: 30,
                                        color: "#D6E7F9",
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "卫生服务便利度",
                                type: "line",
                                smooth: true, //加这个
                                lineStyle: {
                                    normal: {
                                        width: 2,
                                    },
                                },
                                data: yData,
                            },
                            {
                                name: "诊疗量",
                                type: "line",
                                smooth: true, //加这个
                                lineStyle: {
                                    normal: {
                                        width: 2,
                                    },
                                },
                                data: y1Data,
                            },
                            {
                                name: "住院量",
                                type: "line",
                                smooth: true, //加这个
                                lineStyle: {
                                    normal: {
                                        width: 2,
                                    },
                                },
                                data: y2Data,
                            },
                            {
                                name: "网上挂号量",
                                type: "line",
                                smooth: true, //加这个
                                lineStyle: {
                                    normal: {
                                        width: 2,
                                    },
                                },
                                data: y3Data,
                            },
                            {
                                name: "网上挂号率",
                                type: "line",
                                smooth: true, //加这个
                                lineStyle: {
                                    normal: {
                                        width: 2,
                                    },
                                },
                                data: y4Data,
                            },
                            {
                                name: "实时结算率",
                                type: "line",
                                smooth: true, //加这个
                                lineStyle: {
                                    normal: {
                                        width: 2,
                                    },
                                },
                                data: y5Data,
                            },
                        ],
                    };

                    myEc.setOption(option);
                },
                getEcharts04(id, data) {
                    let myEc = echarts.init(document.getElementById(id));
                    let xData = [],
                        yData = [],
                        y1Data = [],
                        y2Data = [];

                    data.forEach((item) => {
                        xData.push(item.name);
                        yData.push(item.value);
                        y1Data.push(item.value1);
                        y2Data.push(item.value2);
                    });
                    let option = {
                        tooltip: {
                            trigger: "axis",
                            axisPointer: {
                                // 坐标轴指示器，坐标轴触发有效
                                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                            },
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "28",
                            },
                        },
                        // color: ["#FF0000", "#00BFFF", "#FF00FF", "#1ce322", "#000000"],
                        legend: {
                            data: ["全市居民健康总体系数", "全省居民健康总体系数", "全国居民健康总体系数"],
                            //    orient: 'horizontal',
                            // itemWidth: 18,
                            // itemHeight: 18,
                            top: "0",
                            // icon: 'rect',
                            itemGap: 5,
                            textStyle: {
                                color: "#D6E7F9",
                                fontSize: 22,
                            },
                        },
                        grid: {
                            left: 0,
                            right: "4%",
                            bottom: "20px",
                            containLabel: true,
                        },
                        // toolbox: {
                        //     feature: {
                        //         saveAsImage: {},
                        //     },
                        // },
                        xAxis: {
                            type: "category",
                            data: xData,
                            offset: 20,
                            axisLine: {
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.3,
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                // rotate: -30,
                                textStyle: {
                                    fontSize: 30,
                                    color: "white",
                                },
                            },
                        },
                        yAxis: [
                            {
                                name: "",
                                type: "value",
                                max: 2.0,
                                min: 0,
                                interval: 0.4,
                                nameTextStyle: {
                                    fontSize: 30,
                                    color: "#D6E7F9",
                                    padding: [0, 0, 20, 0],
                                },
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.1,
                                        width: 2,
                                    },
                                },
                                axisTick: {
                                    show: true,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.5,
                                        width: 2,
                                    },
                                },
                                axisLabel: {
                                    textStyle: {
                                        fontSize: 30,
                                        color: "#D6E7F9",
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "全市居民健康总体系数",
                                type: "line",
                                smooth: true, //加这个
                                lineStyle: {
                                    normal: {
                                        width: 2,
                                    },
                                },
                                data: yData,
                            },
                            {
                                name: "全省居民健康总体系数",
                                type: "line",
                                smooth: true, //加这个
                                lineStyle: {
                                    normal: {
                                        width: 2,
                                    },
                                },
                                data: y1Data,
                            },
                            {
                                name: "全国居民健康总体系数",
                                type: "line",
                                smooth: true, //加这个
                                lineStyle: {
                                    normal: {
                                        width: 2,
                                    },
                                },
                                data: y2Data,
                            },
                        ],
                    };

                    myEc.setOption(option);
                },
            },
            //项目生命周期
            mounted() {
                this.getTime();
                this.initData();
            },
        });
    </script>
</html>
