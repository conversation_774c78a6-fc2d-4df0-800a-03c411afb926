[v-cloak] {
  display: none;
}

* {
  margin: 0;
  padding: 0;
}
#jtys-middle{
  position: relative;
  left: 2094px;
  top: 64px;
}
.jtys-container{
  width: 1000px;
  margin: 0 auto;
  height: 900px;
  position: absolute;
  left: 1280px;
  top: 400px;
  background-color: rgb(3 24 39 / 80%);
  box-shadow: -3px 2px 35px 0px #000000;
  border: 1px solid #359cf8;
  /* border-style: solid;
  border-width: 2px;
  border-image-source: linear-gradient(-32deg, #359cf8 0%, #afdcfb 100%);
  border-image-slice: 1; */
  /* border-radius: 60px; */
}
.jtys-container .head{
  width: 100%;
  height: 100px;
  line-height: 100px;
  background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%), linear-gradient(#ffffff, #ffffff);
  background-blend-mode: normal, normal;
  padding: 10px 50px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  justify-content: space-between;
  /* border-top-left-radius: 60px;
  border-top-right-radius: 60px; */
}
.head span{
  font-size: 48px !important;
  font-weight: 500;
  color: #fff;
  font-weight: bold;
}
.head .img{
      display: inline-block;
      margin: 20px;
      float: right;
      width: 34px;
      height: 34px;
      background-image: url(/static/citybrain/csdn/img/cstz2-middle/close-hover.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
}
.map-dialog{
  padding: 40px;
  box-sizing: border-box;
}
.map-dialog li{
  width: 100%;
  height: 80px;
  /* list-style: none; */
  line-height: 80px;
  font-size: 32px;
  color: #fff;

}
.name{
  background: linear-gradient(to bottom, #ffebce, #ffffff, #ffc559, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
}

.value{
  background: linear-gradient(to bottom, #cfceff, #ffffff, #037aa3, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
}
.bottom-btn{
  width: 550px;
  display: flex;
  position: absolute;
  /* bottom: -182px; */
  bottom: 45px;
  left:25.2%;
  z-index: 99;
}
.bottom-btn-item{
  width: 250px;
  height: 61px;
  background: url(/static/citybrain/djtl/img/qsskdw/sprhpt-2.png) no-repeat;
  background-size: 100% 100%;
  font-size: 36px;
  color: #FFFFFF;
  line-height: 52px;
  text-align: center;
  cursor: pointer;
  margin-right: 40px;
}
.active{
  background: url(/static/citybrain/djtl/img/qsskdw/sprhpt-3.png) no-repeat;
  background-size: 100% 100%;
}
