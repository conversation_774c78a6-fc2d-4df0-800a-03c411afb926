    // let colors = ['#00c0fe', '#fec460'];


    let sjfbOption = {
        //提示框
        tooltip: {
            trigger: "axis",
            axisPointer: {
                //坐标轴指示器
                type: "line",
                lineStyle: {
                    type: "solid",
                },
            },
            borderWidth: 0,
            backgroundColor: "#384c63",
            textStyle: {
                color: "#ffffff",
                fontSize: 40,
            },
            formatter: function (params) {
                let iconHtml1 = `<span style="display:inline-block;margin-right:5px;border-radius:20px;
                width:20px;height:20px;background-color:#fec460"></span>`;
                let iconHtml2 = `<span style="display:inline-block;margin-right:5px;border-radius:20px;
                width:20px;height:20px;background-color:#00c0fe"></span>`;
                return `${params[0].name}<br>
                ${iconHtml1} ${params[0].seriesName}：${params[0].value}万户<br>
                ${iconHtml2} ${params[1].seriesName}：${params[1].value}%`;
            },
        },

        //图表外边距
        grid: {
            top: 100,
            bottom: 50,
        },
        //图例
        legend: {
            // orient:'vertical',       //图例方向
            // x:'center',       //图例位置，也可以使用left等
            y: 20,
            // align:'left',      //icon位置
            data: [
                {
                    icon: "roundRect",
                    name: "户数",
                },
                {
                    name: "同比增幅",
                },
            ],
            textStyle: {
                color: "#d6e7f9",
                fontSize: 32,
            },
            itemGap: 50, //图例项的距离
            itemWidth: 40, //icon宽高
            itemheight: 40,
        },
        //横轴
        xAxis: [
            {
                type: "category",
                data: [],
                axisLine: {
                    //横轴线
                    show: true,
                    lineStyle: {
                        color: "rgba(119,179,241,0.5)",
                        width: 1,
                    },
                },
                axisLabel: {
                    //横轴标签
                    inside: false,
                    textStyle: {
                        color: "#d6e7f9",
                        fontSize: 32,
                    },
                    margin: 20,
                    interval: 0,
                },
            },
        ],
        //纵轴
        yAxis: [
            {
                type: "value",
                name: "户数: 万户", //纵轴名称
                nameGap: 50,
                nameTextStyle: {
                    color: "#accbee",
                    fontSize: 32,
                },
                min: 0,
                max: 250,
                position: "left",
                axisLine: {
                    //纵轴线
                    show: true,
                    lineStyle: {
                        color: "#f8ca50",
                        width: 1,
                    },
                },
                axisLabel: {
                    //纵轴线标签
                    formatter: "{value}",
                    textStyle: {
                        color: "#d6e7f9",
                        fontSize: 32,
                    },
                    margin: 20,
                },
                splitLine: {
                    //分隔线
                    show: true,
                    lineStyle: {
                        color: "rgba(119,179,241,0.1)",
                        width: 1,
                    },
                },
            },
            {
                type: "value",
                name: "同比增幅：%",
                nameGap: 50,
                nameTextStyle: {
                    color: "#accbee",
                    fontSize: 32,
                },
                position: "right",
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: "#00c0fe",
                        width: 1,
                    },
                },
                axisLabel: {
                    formatter: "{value}",
                    textStyle: {
                        color: "#d6e7f9",
                        fontSize: "32",
                    },
                    margin: 20,
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: "rgba(119,179,241,0.1)",
                        width: "1",
                    },
                },
            },
        ],
        //数据
        series: [
            {
                name: "户数",
                type: "bar",
                barWidth: 54,
                yAxisIndex: 0,
                data: [],
                itemStyle: {
                    barBorderRadius: [5, 5, 0, 0],
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: "#f8ca50",
                        }, //柱图渐变色
                        {
                            offset: 1,
                            color: "#e9a53a",
                        }, //柱图渐变色
                    ]),
                },
                emphasis: {
                    //悬浮选中效果
                    itemStyle: {
                        color: "#e9a53a",
                    },
                },
            },
            {
                name: "同比增幅",
                type: "line",
                // symbol: "circle",
                // symbolSize: "15",
                // color: "#00c0fe",
                // lineStyle: {
                //     width: "2",
                // },
                // yAxisIndex: 1,
                // connectNulls: true,
                data: [],
                smooth: true,
                symbol: "circle",
                symbolSize: 5,
                color: "#00c0fe",
                showSymbol: false,
                lineStyle: {
                    normal: {
                        width: 2,
                    },
                },
                yAxisIndex: 1,
            },
        ],
    };
