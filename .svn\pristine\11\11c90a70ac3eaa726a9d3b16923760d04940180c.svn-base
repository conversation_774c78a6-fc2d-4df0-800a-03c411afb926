<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />

    <script src="./jquery/jquery-3.4.1.min.js"></script>
    <script src="./Vue/vue.js"></script>
    <link rel="stylesheet" href="./css/sigma.css" />
    <link rel="stylesheet" href="./elementui/css/elementui.css" />
    <script src="./elementui/js/elementui.js"></script>
  </head>

  <body>
    <div id="cssjApp" class="cssj_app_box">
      <div style="height: 912px; width: 100%">
        <div class="cssj_header clearfix">
          <h2 class="title">
            <i class="title_icon_one"></i>
            特种设备
          </h2>
          <div class="title_hr" style="width: 1050px; left: 0px"></div>
          <h3 class="title s-m-r-80 s-font-32">数据至:2022年3月4日</h3>
        </div>

        <div class="s-flex s-row-center">
          <div class="title_box" style="width: 766px">
            <div class="title_box_txt s-m-r-30">正常状态</div>
            <div class="title_box_num s-m-l-10" v-for="item in num1">
              <div class="txt_color4">{{item}}</div>
            </div>
            <div class="txt_color4 s-m-l-30" style="font-size: 50px">99.3%</div>
          </div>
          <div class="title_box" style="width: 766px; margin-left: 220px">
            <div class="title_box_txt s-m-r-30">异常状态</div>
            <div class="title_box_num s-m-l-10" v-for="item in num2">
              <div class="txt_color2">{{item}}</div>
            </div>
            <div class="txt_color2 s-m-l-30" style="font-size: 50px">0.7%</div>
          </div>
        </div>

        <div
          class="s-m-t-40 s-flex s-c-white s-row-right"
          style="height: 50px; width: 100%"
        >
          <div class="s-font-30 s-m-r-15">区域</div>
          <el-dropdown>
            <el-button
              :round="true"
              style="width: 216px; height: 61px; font-size: 30px"
            >
              婺城区
              <i class="el-icon-caret-bottom el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>婺城区</el-dropdown-item>
              <el-dropdown-item>开发区</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <div class="s-font-30 s-m-r-15 s-m-l-40">设备种类</div>
          <el-dropdown>
            <el-button
              :round="true"
              style="width: 216px; height: 61px; font-size: 30px"
            >
              电梯
              <i class="el-icon-caret-bottom el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>电梯</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <el-button
            class="s-m-30 s-font-30"
            style="width: 138px; height: 61px; font-size: 28px"
          >
            查看
          </el-button>
        </div>

        <div class="s-m-t-50" style="width: 1935px; height: 450px">
          <el-table
            :row-style="rowStyle"
            :header-cell-style="headCellStyle"
            :cell-style="cellStyle"
            class="s-m-t-20"
            :data="yjDatas"
            style="width: 100%"
            height="100%"
          >
            <el-table-column
              prop="yjsy"
              label="预警事由"
              width="387"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="czry"
              label="处置人员"
              width="387"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="czyj"
              label="处置意见"
              width="387"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="fssj"
              label="发生时间"
              width="387"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="yjssgx"
              label="预警所属管辖"
              width="387"
              align="center"
            ></el-table-column>
          </el-table>
        </div>
      </div>

      <div>
        <div class="cssj_header clearfix">
          <h2 class="title">
            <i class="title_icon_one"></i>
            交通事故
          </h2>
          <div class="title_hr" style="width: 1050px; left: 0px"></div>
          <h3 class="title s-m-r-80 s-font-32">数据至:2022年3月4日</h3>
        </div>
        <!-- jtsglnum: [3, 6, 3], //交通事故量
        yclnum: [3, 2, 6], //已处理
        wclnum: [0, 2, 6],//未处理
      
       <div class="title_box_txt s-m-r-30">异常状态</div>
            <div class="title_box_num s-m-l-10" v-for="item in num2">
              <div class="txt_color2">{{item}}</div>
            </div>
      
      -->

        <div class="s-flex s-row-center">
          <div class="title_box" style="width: 614px">
            <div class="title_box_txt s-m-r-30">交通事故量</div>

            <div class="title_box_num s-m-l-10" v-for="item in jtsglnum">
              <div class="txt_color2">{{item}}</div>
            </div>
            <div class="title_box_txt s-m-l-10">起</div>
          </div>
          <div class="title_box s-m-l-50" style="width: 614px">
            <div class="title_box_txt s-m-r-30">已处理</div>
            <div class="title_box_num s-m-l-10" v-for="item in yclnum">
              <div class="txt_color4">{{item}}</div>
            </div>
            <div class="title_box_txt s-m-l-10">起</div>
            <div class="txt_color4 s-m-l-30" style="font-size: 50px">96%</div>
          </div>
          <div class="title_box s-m-l-50" style="width: 614px">
            <div class="title_box_txt s-m-r-30">未处理</div>
            <div class="title_box_num s-m-l-10" v-for="item in wclnum">
              <div class="txt_color3">{{item}}</div>
            </div>
            <div class="title_box_txt s-m-l-10">起</div>
            <div class="txt_color3 s-m-l-30" style="font-size: 50px">4%</div>
          </div>
        </div>

        <div
          class="s-m-t-50 s-flex s-c-white s-row-right"
          style="height: 50px; width: 100%"
        >
          <div class="s-font-30 s-m-r-15">区域</div>
          <el-dropdown>
            <el-button
              :round="true"
              style="width: 216px; height: 61px; font-size: 30px"
            >
              婺城区
              <i class="el-icon-caret-bottom el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>婺城区</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <el-button
            class="s-m-30 s-font-30"
            style="width: 138px; height: 61px; font-size: 28px"
          >
            查看
          </el-button>
        </div>

        <div class="s-m-t-50" style="width: 1935px; height: 450px">
          <el-table
            :row-style="rowStyle"
            :header-cell-style="headCellStyle"
            :cell-style="cellStyle"
            class="s-m-t-20"
            :data="zfDatas"
            style="width: 100%"
            height="100%"
          >
            <el-table-column
              prop="name"
              label="事故编号"
              width="324"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="addr"
              label="事故地点"
              width="324"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="type"
              label="事故类型"
              width="324"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="state"
              label="事故形态"
              width="324"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="handle"
              label="处警经过"
              width="324"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="istimeout"
              label="事故处理状态"
              width="315"
              align="center"
            ></el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <script>
      new Vue({
        el: '#cssjApp',
        data: {
          num1: [1, 2, 6, 3, 8], //正常状态
          num2: [1, 2, 6], //异常状态
          jtsglnum: [3, 6, 3], //交通事故量
          yclnum: [3, 2, 6], //已处理
          wclnum: [0, 2, 6], //未处理
          yjDatas: [
            {
              yjsy: '****',
              czry: '****',
              czyj: '****',
              fssj: '****',
              yjssgx: '****',
            },
            {
              yjsy: '****',
              czry: '****',
              czyj: '****',
              fssj: '****',
              yjssgx: '****',
            },
            {
              yjsy: '****',
              czry: '****',
              czyj: '****',
              fssj: '****',
              yjssgx: '****',
            },
            {
              yjsy: '****',
              czry: '****',
              czyj: '****',
              fssj: '****',
              yjssgx: '****',
            },
            {
              yjsy: '****',
              czry: '****',
              czyj: '****',
              fssj: '****',
              yjssgx: '****',
            },
          ],

          zfDatas: [
            {
              name: '****',
              addr: '****',
              type: '****',
              state: '****',
              handle: '****',
              istimeout: '未处理',
            },
            {
              name: '****',
              addr: '****',
              type: '****',
              state: '****',
              handle: '****',
              istimeout: '已处理',
            },
            {
              name: '****',
              addr: '****',
              type: '****',
              state: '****',
              handle: '****',
              istimeout: '未处理',
            },
            {
              name: '****',
              addr: '****',
              type: '****',
              state: '****',
              handle: '****',
              istimeout: '未处理',
            },
            {
              name: '****',
              addr: '****',
              type: '****',
              state: '****',
              handle: '****',
              istimeout: '未处理',
            },
          ],
        },
        methods: {
          cellStyle({ row, column, rowIndex, columnIndex }) {
            let cellstyl =
              'border-top:6px solid #081b32;border-bottom:6px solid #081b32;'

            if (row.istimeout == '未处理' && columnIndex == 5) {
              cellstyl +=
                ';background: linear-gradient(to bottom,#ffb1b1,#f4f1ff, #ff4949,#ffffff) ;-webkit-background-clip: text;-webkit-text-fill-color: transparent;'
            } else if (row.istimeout == '已处理' && columnIndex == 5) {
              cellstyl +=
                ';background: linear-gradient(to bottom,#faffc4,#f4f1ff, #cad55d,#ffffff);-webkit-background-clip: text;-webkit-text-fill-color: transparent;'
            }
            return cellstyl
          },
          headCellStyle() {
            return 'padding:10px;background-color: #0e3a65;color:#fff;font-size:30px;height:49px;color: #77b3f1;border-bottom:0px'
          },
          rowStyle({ row, rowIndex }) {
            let rowStyl = {
              'background-color': '#0d223d',
              color: '#fff',
              'font-size': '30px',
              height: '66px',
            }

            // if (this.name === row.name) {
            //   return {
            //     '	background-image':
            //       'linear-gradient(0deg, #2790ff 0%, #2790ff 100%)',
            //   }
            // }

            return rowStyl
          },
        },
      })
    </script>

    <style scoped>
      .el-table {
        background-color: transparent;
      }
      .el-dropdown-menu {
        background-color: #0f3c67;
        width: 200px;
      }
      .el-dropdown-menu__item {
        color: white;
        font-size: 20px;
      }
      .el-button {
        background: linear-gradient(
          to bottom,
          rgba(0, 89, 147, 0.9),
          rgba(0, 32, 52, 0.9)
        );
        color: #ffffff;
        width: 216px;
        height: 47px;
        border: #359cf8 1xp solid;
      }
      .el-table .cell {
        line-height: 30px;
      }
      .el-table:before {
        height: 0px;
      }

      .el-table tbody tr:hover > td {
        background-color: transparent !important;
        background: linear-gradient(
          to bottom,
          rgba(0, 89, 147, 0.9),
          rgba(0, 32, 52, 0.9)
        ) !important;

        height: 70px;
      }

      .cssj_app_box {
        position: relative;
        box-sizing: border-box;
        width: 2045px;
        height: 1849px;
        background-image: url('./img/bg.png');
        background-size: 100% 100%;
        padding: 10px 55px 30px;
      }
      .cssj_header {
        width: 1935px;
        display: flex;
        height: 130px;
        align-items: center;
        justify-content: space-between;
        background: url('./img/一级标题3.png') no-repeat;
        background-position: 0 55px;
      }
      .cssj_header .title {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        font-size: 40px;
        background: linear-gradient(to bottom, #ffffff, #4797ff);
        -webkit-background-clip: text;
        color: transparent;
      }

      .cssj_header .title .title_icon_one {
        display: inline-block;
        margin-right: 10px;
        width: 78px;
        height: 75px;
        vertical-align: bottom;
        background-image: url('./img/一级标题1.png');
        background-size: 100% 100%;
      }
      .cssj_header .title_hr {
        position: relative;
        /**left: 30px;**/
        width: 50%;
        height: 21px;
        background-image: url('./img/一级标题2.png');
        background-size: 100% 100%;
      }
      .title_box {
        width: 674px;
        height: 100px;
        background-image: url('./img/cssj_title.png');
        background-size: 674px 44px;
        background-repeat: no-repeat;
        background-position: bottom;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .title_box .title_box_txt {
        font-size: 34px;
        color: #ffffff;
      }
      .title_box .title_box_num {
        width: 41px;
        height: 58px;
        background: url('./img/cssj_icon1.png');
        background-size: 41px 58px;
        background-repeat: no-repeat;
        background-position: bottom;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .txt_color1 {
        background: linear-gradient(to bottom, #f0b55f, #ffffff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 60px;
      }
      .txt_color2 {
        background: linear-gradient(
          to bottom,
          #ffb1b1,
          #f4f1ff,
          #ff4949,
          #ffffff
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 60px;
      }
      .txt_color3 {
        background: linear-gradient(
          to bottom,
          #ffe9af,
          #f4f1ff,
          #ffd461,
          #ffffff
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 60px;
      }
      .txt_color4 {
        background: linear-gradient(
          to bottom,
          #faffc4,
          #f4f1ff,
          #cad55d,
          #ffffff
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 60px;
      }
    </style>
  </body>
</html>
