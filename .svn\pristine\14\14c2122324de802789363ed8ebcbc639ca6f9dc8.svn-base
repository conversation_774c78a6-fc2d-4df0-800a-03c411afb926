/*
 * @Author: CK
 * @email: <EMAIL>
 * @Date: 2022-08-01 10:21:35
 * @LastEditTime: 2022-08-01 11:47:29
 * @FilePath: \2DAnd3D\js\work\createHeatMap.js
 * @Description: 创建热力图
 */

// import heatMap from '../../data/earthquakes.js';
import { heatMapVariable } from '../globalVariable/mapFor2D.js';
import removeHeatMap from './removeHeatMap.js';

function createHeatMap (data) {
    removeHeatMap(); // 清除热力图
    const geoJson = {
        "type": "FeatureCollection",
        "crs": { "type": "name", "properties": { "name": "urn:ogc:def:crs:OGC:1.3:CRS84" } },
        "features": []
    }
    // 计算当前数组最大值
    const maxNum = data.heatMapData.reduce((item,items) => {
        return item.count > items.count ? item.count : items.count
    })
    // 组装热力图数据
    const length = data.heatMapData.length;
    for (let i = 0; i < length; i++) {
        const weight = Number((data.heatMapData[i].count/maxNum).toFixed(4)) * 1000;
        geoJson.features.push(turf.point( data.heatMapData[i].pos,{ "id": i, "mag": weight}));
    }
    // 添加热力图
    heatMapVariable.heatMap = egis.createHeatmp(
        'heatMap',
        // heatMap,
        geoJson,
        {
            // type: 'light',
            opacity: 0.8,
            color:[[0,0,255],[52,0,248],[38,254,45],[231,255,39],[213,109,33],[248,5,9]]
        }
    )
}

export default createHeatMap
