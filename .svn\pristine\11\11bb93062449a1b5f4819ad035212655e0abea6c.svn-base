<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <title>城市安全-企业安全生产情况分析-左</title>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/elementui/css/elementui.css" />
        <script src="/Vue/vue.js"></script>
        <script src="/elementui/js/elementui.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/citybrain/hjbh/js/echarts.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/js/jslib/Emiter.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/vue-count-to.min.js"></script>
        <style>
          [v-cloak] {
            display: none;
          }
          html,body,ul,p{
              padding:0;
              margin:0;
              list-style: none;
          }
          .container{
              width:250px;
              height:200px;
              background-color: #0a2443;
              padding: 20px;
              box-sizing: border-box;
              border: 1px solid #ccc; 
          }
          .el-tree {
            background: none;
            color: #fff;
            font-size: 30px;
            }

            .el-tree-node__label {
            font-size: 30px;
            }

            .el-tree-node__content {
            height: 50px;
            line-height: 50px;
            }

            .node-lable {
            line-height: 3.125rem;
            font-size: 30px;
            font-family: PangMenZhengDao;
            font-weight: bold;
            color: #c0d6ed;
            line-height: 58px;
            }

            .node-img {
            position: relative;
            left: 20px;
            top: 10px;
            width: 40px;
            }

            .el-tree-node__expand-icon {
            display: none;
            }

            .el-tree-node.is-current > .el-tree-node__content,
            .el-tree-node__content:hover {
            background: linear-gradient(
                94deg,
                rgba(3, 97, 156, 0) 0%,
                #03619c 100%
            ) !important;
            border-radius: 0px 30px 30px 0px;
            }

            .el-tree-node__content {
            height: 50px;
            }

            .el-checkbox__inner {
            width: 20px;
            height: 20px;
            }

            .el-checkbox__inner::after {
            border: 3px #fff solid;
            width: 7px;
            height: 14px;
            left: 3px;
            top: -2px;
            border-left: 0;
            border-top: 0;
            }

            .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
            height: 4px;
            top: 8px;
            }
        </style>
    </head>
    <body>
        <div id="app" v-cloak class="container">
            <div class="tree">
                <el-tree
                  ref="treeForm"
                  node-key="id"
                  :data="treeData"
                  :props="defaultProps"
                  :check-on-click-node="true"
                  show-checkbox
                  @check-change="checkChange"
                  :default-checked-keys="[1]"
                >
                </el-tree>
              </div>
        </div>

        <script>
            var vm = new Vue({
                el: "#app",
                data: {
                    treeData: [
                        {
                            label: "空间分布",
                            id: 1,
                        },
                        {
                            label: "分类",
                            id: 2,
                        },
                        {
                            label: "特性",
                            id: 3,
                        }
                    ],
                    defaultProps: {
                        children: "children",
                        label: "label",
                    },
                },
                mounted() {
                    this.addPoint("空间分布")
                },
                methods: {
                    checkChange(item, flag) {
                        console.log(item);
                        if (flag) {
                            this.addPoint(item.label)
                        } else {
                            this.rmPoint(item.label)
                        }
                    },
                    addPoint(name){
                        console.log(name);
                        let res = [
                            {
                                title: '空间分布',
                                gps_x: "119.94315399169922",
                                gps_y: "29.5630503845215",
                                text:'浦江县'
                            },
                            {
                                title: '空间分布',
                                gps_x: "119.46214447021484",
                                gps_y: "29.31345558166504",
                                text:'兰溪市'
                            },
                            {
                                title: '空间分布',
                                gps_x: "119.5569204711914",
                                gps_y: "29.00677101135254",
                                text:'婺城区'
                            },
                            {
                                title: '空间分布',
                                gps_x: "119.8483056640625",
                                gps_y: "29.188559951782227",
                                text:'金义新区'
                            },
                            {
                                title: '空间分布',
                                gps_x: "120.08206787109375",
                                gps_y: "29.322123641967773",
                                text:'义乌市'
                            },
                            {
                                title: '分类',
                                gps_x: "119.7269204711914",
                                gps_y: "28.79677101135254",
                                text:'武义县'
                            },
                            {
                                title: '分类',
                                gps_x: "120.1469204711914",
                                gps_y: "28.97677101135254",
                                text:'永康市'
                            },
                            {
                                title: '特性',
                                gps_x: "120.4169204711914",
                                gps_y: "29.24677101135254",
                                text:'东阳市'
                            },
                            {
                                title: '特性',
                                gps_x: "120.6299204711914",
                                gps_y: "29.06677101135254",
                                text:'磐安县'
                            }
                        ];
                        let array = res.filter((item)=>{
                            return item.title==name
                        })
                        let arr = array.map((item) => {
                            return {
                                data: {
                                    title:item.text + '区域详细数据项',
                                    key:['当前区域隐患总数','一般隐患','重大隐患','同比增长率','同比增长率'],
                                    value:['6776起','999起','77起','****%','-1.4%'],
                                },
                                point: item.gps_x + "," + item.gps_y,
                                
                            };
                        });
                        console.log(arr);
                        top.document.getElementById("map").contentWindow.Work.funChange(
                            JSON.stringify({
                                funcName: "pointLoad",
                                pointType: "digital-yellow", // 点位类型（图标名称）
                                pointId: name, // 点位唯一id
                                setClick: true,
                                pointData: arr,
                                imageConfig: { iconSize: 1 },
                                popup:{
                                    offset:[50,-100]
                                }
                            })
                        );
                    },
                    //清除点位
                    rmPoint(name){
                        top.document.getElementById("map").contentWindow.Work.funChange(
                            JSON.stringify({
                                funcName:"rmPoint" ,
                                pointId: name,
                            })
                        )
                    },
         
                },
            });
        </script>
    </body>
</html>
