<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>事故快报-中间</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <link rel="stylesheet" href="/static/citybrain/shgl/css/shfx-middle.css" />
    <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
</head>
<style>
    .node-img {
        left: 4px !important;
    }

    .tree {
        height: 600px;
    }

    #shfx-middle {
        position: absolute;
        left: 0px;
        top: 0px;
    }

    .tree {
        width: 390px;
    }
</style>

<body>
    <div id="shfx-middle" v-cloak>
        <div class="tree">
            <el-tree :data="treeData" show-checkbox node-key="id" ref="tree" highlight-current
                @check-change="checkChange" class="auth-tree" :render-after-expand="false"
                icon-class="el-icon-caret-left" default-expand-all>
                <!-- :default-checked-keys="[0]" -->
                <div style="display: flex; align-items: center" slot-scope="{ node, data }">
                    <div class="node-lable">
                        <img v-if="!data.children" class="node-img"
                            :src="`/static/citybrain/shgl/img/aqsc/aqsc-${data.id}.png`" alt="" />
                        {{ node.label }}
                        <span v-if="data.children">({{data.children.length}})</span>
                    </div>
                </div>
            </el-tree>
        </div>
    </div>
</body>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>

<script>
    var vm = new Vue({
        el: "#shfx-middle",
        data() {
            return {
                treeData: [],
            };
        },
        mounted() {
            $api("shgl_kbxx_sgkbTree").then((res) => {
                this.treeData = res;
            });
            top.emiter &&
                top.emiter.on("aqsckb_tree", (res) => {
                    if (res) {
                        this.$refs.tree.setCheckedKeys([1, 2, 3, 4, 5, 6, 7, 8, 9, 10]);
                    } else {
                        this.$refs.tree.setCheckedKeys([]);
                    }
                });
            top.mapUtil.tool.changeBaseMap('black')
        },
        methods: {
            checkChange(item, flag) {
                console.log(item, 11111);
                if (flag) {
                    this.getPoint(item);
                } else {
                    this.rmPoint(item);
                }
            },
            getPoint(item) {
                let that=this
                $api("shgl_kbxx_sgkbPoint").then((res) => {
                    let result = [];
                    let pointData = [];
                    result = res.filter((el) => {
                        return el.name == item.label;
                    });
                    let key = [];
                    let value = [];
                    let icon = "";
                    result.forEach((obj, index) => {
                        icon = obj.icon;
                        if (item.label === "重点危险源") {
                            key = null;
                            value = null;
                        } else {
                            key = ["名称", "地点"];
                            value = [obj.mc, obj.dd];
                        }
                        let str = {
                            data: {
                                title: obj.name + "详情",
                                key: key,
                                value: value,
                            },
                            point: obj.lnglat,
                            lng: obj.lnglat.split(',')[0],//经度
                            lat: obj.lnglat.split(',')[1],//纬度
                        };
                        pointData.push(str);
                    });
                    top.mapUtil.loadPointLayer({
                        data: pointData,
                        layerid:  "sgkb-" + item.id, //图层id
                        iconcfg: { image: `/static/spritesImage/${icon}.png`, iconSize: 1}, //图标
                        onclick: function(e){
                            if(e.data.title=== "重点危险源详情"){that.openIframe()}
                        },
                        popcfg: {
                            offset: [50, 30],
                            show: false,
                        },
                    })
                });
            },
            rmPoint(item) {
                top.mapUtil.removeLayer("sgkb-" + item.id)
            },
            rmAllPoint() {
                top.mapUtil.removeAllLayers([""])
            },
            openIframe() {
                top.commonObj.funOpenIframe({
                    width: "1120px",
                    height: "660px",
                    zIndex: "999",
                    src: "/static/citybrain/shgl/commont/aqyhfbtj-point-dialog.html",
                    left: "3275px",
                    top: "827px",
                    name: "aqyhfbtj-point-dialog",
                });
            },
        },
        destroyed() {
            this.rmAllPoint();
        },
    });
</script>

</html>