<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <link
      rel="stylesheet"
      href="/static/citybrain/shgl/css/jtys-left-copy.css"
    />
  </head>
  <style>
    #szcg-left {
      width: 2045px;
      height: 1890px;
      background: url("/img/left-bg.png") no-repeat;
      background-size: 100% 100%;
      display: flex;
      justify-content: center;
    }

    .content {
      width: 1934px;
      height: 100%;
    }

    .line1 {
      width: 1934px;
      height: 750px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
    }

    .line1-box {
      width: 967px;
      height: 100%;
      position: relative;
    }

    .line2 {
      width: 1934px;
      height: 780px;
    }

    .line2-1 {
      width: 100%;
      height: 200px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }

    .line2-2 {
      width: 100%;
      height: 560px;
      margin-top: 20px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }

    .line2-2-box {
      width: 967px;
      height: 560px;
    }

    .indexs {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      width: 900px;
      text-align: center;
      margin-left: 550px;
    }

    .indexsItem {
      width: 300px;
      height: 144px;
      background: url("../img/Base5.png") no-repeat;
      background-size: 300px 144px;
    }

    .item-name {
      color: #fff;
      font-size: 30px;
      margin-top: -15px;
    }

    .item-value {
      font-size: 60px;
      background: linear-gradient(
        to bottom,
        #ffeccb,
        #ffffff,
        #ffc460,
        #ffe2b0,
        #ffffff
      );
      -webkit-background-clip: text;
      color: transparent;
      font-weight: 600;
      margin-top: 5px;
    }

    .unit {
      font-size: 20px;
    }

    .table {
      width: 100%;
      height: 500px;
    }

    .table-title {
      width: 100%;
      height: 100px;
      background-color: #00396f;
      color: #77b3f1;
      font-size: 35px;
      font-weight: 400;
      font-family: Source Han Sans CN;
      display: flex;
      align-items: center;
      text-align: center;
    }

    .table-content {
      width: 100%;
      height: 400px;
      padding-bottom: 10px;
      box-sizing: border-box;
      overflow-y: auto;
    }

    .table-content::-webkit-scrollbar {
      width: 0 !important;
    }

    .table-item {
      margin-top: 10px;
      width: 100%;
      height: 90px;
      font-size: 30px;
      text-align: center;
      color: #fff;
      background: rgb(13, 34, 61);
      /* opacity: 0.16; */
      display: flex;
      align-items: center;
    }

    .el-select .el-input .el-select__caret {
      left: 140px;
    }
    .returnbutton {
      position: absolute;
      top: 3%;
      right: 10%;
      width: 100px;
      height: 50px;
      background-color: #21407b;
      border: #fff solid 1px;
      font-size: 25px;
      color: #fff;
      text-align: center;
      padding-top: 7px;
      box-sizing: border-box;
    }
  </style>
  <body>
    <div id="szcg-left">
      <div class="content">
        <div class="title">
          <nav style="padding: 20px 45px">
            <s-header-title
              style="width: 100%"
              title="设施数量"
              data-time="2022年7月22日"
              htype="1"
            ></s-header-title>
          </nav>
        </div>
        <div class="line1">
          <div class="line1-box">
            <nav>
              <s-header-title-2 title="类别分布"></s-header-title-2>
            </nav>
            <div
              id="charts1"
              style="width: 100%; height: 100%; margin-top: 70px"
            ></div>
          </div>
          <div class="line1-box">
            <nav>
              <s-header-title-2 title="部门分布"></s-header-title-2>
            </nav>
            <div class="returnbutton" v-show="bumen" @click="returnChart2">
              返回
            </div>
            <div
              id="charts2"
              style="width: 100%; height: 70%; margin-top: 70px"
            ></div>
          </div>
        </div>
        <div class="title">
          <nav style="padding: 20px 45px">
            <s-header-title
              style="width: 100%"
              title="城管监督员情况"
              data-time="2022年7月22日"
              htype="1"
            ></s-header-title>
          </nav>
        </div>
        <div class="line2">
          <div class="line2-1">
            <div class="indexs">
              <div class="indexsItem" v-for="(item,i) in indexs" :key="i">
                <div class="item-name">{{item.name}}</div>
                <div class="item-value">
                  {{item.value}} <span class="unit">人</span>
                </div>
              </div>
            </div>
            <div class="select">
              <el-select v-model="selectValue" placeholder="请选择">
                <el-option
                  v-for="item in selectList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="line2-2">
            <div class="line2-2-box">
              <nav>
                <s-header-title-2 title="各区域在线监督员"></s-header-title-2>
              </nav>
              <div id="charts3" style="width: 967px; height: 540px"></div>
            </div>
            <div class="line2-2-box">
              <nav>
                <s-header-title-2 title="在线监督员"></s-header-title-2>
              </nav>
              <div class="table">
                <div class="table-title">
                  <div style="flex: 1">姓名</div>
                  <div style="flex: 1">所属区域</div>
                </div>
                <div class="table-content">
                  <div class="table-item" v-for="(item,i) in table" :key="i">
                    <div style="flex: 1">{{item.name}}</div>
                    <div style="flex: 1">{{item.value}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  var vm = new Vue({
    el: "#szcg-left",
    data: {
      indexs: "",
      table: "",
      selectValue: 0,
      selectList: [{ name: "处置人员窗口", value: 0 }],
      charts1Data: "",
      charts2Data: "",
      charts3Data: "",
      bumen: "",
      bumen1: [],
      bumen2: [],
      bumen3: [],
      bumen4: [],
      bumen5: [],
    },
    mounted() {
      this.init();
    },
    methods: {
      init() {
        // $api("shgl_szcg_leibie").then((res) => {
        //   this.charts1Data = res;
        //   this.initCharts1();
        // });
        $get("/shgl/szcg/leibie").then((res) => {
          this.charts1Data = res;
          this.initCharts1();
        });
        // $api("shgl_szcg_bumen").then((res) => {
        //   this.charts2Data = res;
        //   this.initCharts2();
        // });
         $get("/shgl/szcg/bumen").then((res) => {
          this.charts2Data = res;
          this.initCharts2();
        });
        // $api("shgl_szcg_bmfby1", { type: 1 }).then((res) => {
        //   this.bumen1 = res;
        // });
        // $api("shgl_szcg_bmfby1", { type: 2 }).then((res) => {
        //   this.bumen2 = res;
        // });
        // $api("shgl_szcg_bmfby1", { type: 3 }).then((res) => {
        //   this.bumen3 = res;
        // });
        // $api("shgl_szcg_bmfby1", { type: 4 }).then((res) => {
        //   this.bumen4 = res;
        // });
        // $api("shgl_szcg_bmfby1", { type: 5 }).then((res) => {
        //   this.bumen5 = res;
        // });
        // $api("shgl_szcg_jiandu").then((res) => {
        //   this.charts3Data = res;
        //   this.initCharts3();
        // });
        $get("shgl/szcg/jiandu").then((res) => {
          this.charts3Data = res;
          this.initCharts3();
        });
        // $api("shgl_szcg_indexs").then((res) => {
        //   this.indexs = res;
        // });
        $get("shgl/szcg/indexs").then((res) => {
          this.indexs = res;
        });
        // $api("shgl_szcg_table").then((res) => {
        //   this.table = res;
        // });
        $get("shgl/szcg/table").then((res) => {
          this.table = res;
        });
      },
      returnChart2() {
        this.initCharts2();
        this.bumen = "";
      },
      initCharts1() {
        let that = this;
        let myChart = echarts.init(document.getElementById("charts1"));
        // let myChart = echarts.init(document.getElementById("myEcharts3"));
        let imgUrl = "/static/citybrain/djtl/img/djtl-left/echarts-bg.png";
        let data = this.showData;
        let option = {
          color: [
            "#00C0FF",
            "#22E8E8",
            "#FFD461",
            "#A9DB52",
            "#B76FD8",
            "#FD852E",
            "#FF4949",
            "#0594C3",
            "#009D9D",
            "#A47905",
          ],

          tooltip: {
            trigger: "item",
            formatter:
              "大类名称：{b}<br/>数量占比：{d}%<br/>包含小类数量：209类<br/>包含设施数量：{c}个",
            borderWidth: 0,
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "25",
            },
            extraCssText: "border:#fff 1px solid",
          },
          legend: {
            orient: "vertical",
            left: "48%",
            top: "18%",
            bottom: "0%",
            icon: "circle",
            itemGap: 30,
            textStyle: {
              rich: {
                name: {
                  fontSize: 25,
                  color: "#ffffff",
                  padding: [0, 20, 0, 15],
                },
                value: {
                  fontSize: 25,
                  color: "#2CC6F9",
                  // padding: [10, 0, 0, 15]
                },
              },
            },
            formatter: function (name) {
              var data = option.series[0].data; //获取series中的data
              //   console.log(option);
              var total = 0;
              var tarValue;
              for (var i = 0, l = data.length; i < l; i++) {
                total += data[i].value;
                if (data[i].name == name) {
                  tarValue = data[i].value;
                }
              }
              that.serverNum = total;
              var p = ((tarValue / total) * 100).toFixed(2);
              return (
                "{name|" + name + "}{value|" + tarValue + "个  " + p + "%}"
              );
            },
          },
          graphic: [
            {
              type: "image",
              id: "logo",
              left: "10.4%",
              top: "16.4%",
              z: -10,
              bounding: "raw",
              rotation: 0, //旋转
              origin: [50, 50], //中心点
              scale: [0.8, 0.8], //缩放
              style: {
                image: imgUrl,
                opacity: 1,
              },
            },
          ],
          series: [
            {
              name: "",
              type: "pie",
              radius: ["30%", "40%"],
              center: ["25%", "35%"],
              roseType: "",
              itemStyle: {
                borderRadius: 5,
              },
              label: {
                show: false,
              },
              data: this.charts1Data,
            },
          ],
        };

        // echarts监听legend事件
        myChart.on("legendselectchanged", function (e) {
          top.vm.rmAllPoint();
          if (e.name == "园林绿化设施") {
            top.vm.getPoint({ label: "古树名木" });
            top.vm.getPoint({ label: "行道树" });
          } else if (e.name == "交通设施") {
            top.vm.getPoint({ label: "交通设施1" });
            top.vm.getPoint({ label: "交通设施2" });
          } else if (e.name == "市容环境设施") {
            top.vm.getPoint({ label: "市容环境设施1" });
            top.vm.getPoint({ label: "市容环境设施2" });
          } else if (e.name == "公用设备") {
            top.vm.getPoint({ label: "公共设施1" });
            top.vm.getPoint({ label: "公共设施2" });
          } else if (e.name == "其他部件") {
            top.vm.getPoint({ label: "其他部件1" });
            top.vm.getPoint({ label: "其他部件2" });
          }
          $api("/cstz_rlt_qx").then((res) => {
            console.log(res);
            let hotMapData = [];
            res[0].heatmap.map((item) => {
              let pointArr = [];
              pointArr[0] = item.lng;
              pointArr[1] = item.lat;
              pointArr[2] = item.count;
              pointArr[3] = item.geohash;
              hotMapData.push(pointArr);
            });

            const mapData = {
              funcName: "hotPowerMap",
              hotPowerMapData: hotMapData,
              offset: 256,
              heatMapId: "rkztTimeHot",
              threshold: 6000,
              distance: 800,
              alpha: 0.3,
            };
            top.document
              .getElementById("map")
              .contentWindow.Work.funChange(JSON.stringify(mapData));
          });
        });
        myChart.setOption(option);
        myChart.getZr().on("mousemove", (param) => {
          myChart.getZr().setCursorStyle("default");
        });
      },
      initCharts2() {
        let myChart = echarts.init(document.getElementById("charts2"));
        let option = {
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "28",
            },
          },
          legend: {
            selectedMode: false,
            data: ["人"],
            right: "4%",
            textStyle: {
              fontSize: 16,
              color: "#fff",
            },
          },
          grid: {
            top: "0%",
            left: "5%",
            right: "25%",
            bottom: "0",
            containLabel: true,
          },
          xAxis: {
            type: "value",
            show: false,
          },
          yAxis: {
            name: "",
            type: "category",
            triggerEvent: false,
            inverse: true,
            axisLine: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: "#fff",
                fontSize: 28,
              },
            },
            axisTick: {
              show: false,
              length: 10,
            },
            data: this.charts2Data.map((item) => item.name),
          },
          series: [
            {
              // cursor:"auto",
              type: "bar",
              name: "",
              showBackground: true,
              itemStyle: {
                barBorderRadius: [0, 10, 10, 0],
                color: function (params) {
                  var colors = [
                    "#4587E7",
                    "#35AB33",
                    "#F5AD1D",
                    "#ff7f50",
                    "#da70d6",
                    "#32cd32",
                    "#6495ed",
                  ];
                  // 通过返回值的下标一一对应将颜色赋给柱子上，return出去什么颜色就是什么颜色，这里可以写判断
                  if (params.dataIndex == 0) {
                    return new echarts.graphic.LinearGradient(
                      1,
                      0,
                      0,
                      0,
                      [
                        {
                          offset: 0,
                          color: "#FF9434", //指0%处的颜色
                        },
                        {
                          offset: 1,
                          color: "#F90808", //指100%处的颜色
                        },
                      ],
                      false
                    );
                  } else if (params.dataIndex == 1) {
                    return new echarts.graphic.LinearGradient(
                      1,
                      0,
                      0,
                      0,
                      [
                        {
                          offset: 0,
                          color: "#FFF220", //指0%处的颜色
                        },
                        {
                          offset: 1,
                          color: "#F98508", //指100%处的颜色
                        },
                      ],
                      false
                    );
                  } else if (params.dataIndex == 2) {
                    return new echarts.graphic.LinearGradient(
                      1,
                      0,
                      0,
                      0,
                      [
                        {
                          offset: 0,
                          color: "#2DF09F", //指0%处的颜色
                        },
                        {
                          offset: 1,
                          color: "#0EB1E5", //指100%处的颜色
                        },
                      ],
                      false
                    );
                  } else {
                    return new echarts.graphic.LinearGradient(
                      1,
                      0,
                      0,
                      0,
                      [
                        {
                          offset: 0,
                          color: "#2BDAFF", //指0%处的颜色
                        },
                        {
                          offset: 1,
                          color: "#078FF7", //指100%处的颜色
                        },
                      ],
                      false
                    );
                  }
                  // return colors[params.dataIndex];
                },
              },
              label: {
                show: true,
                position: [540, -2],
                color: "#fff",
                formatter: function (params) {
                  return params.value + " 个";
                },
                fontSize: 28,
              },
              barWidth: 20,
              color: "#539FF7",
              data: this.charts2Data.map((item) => item.value),
            },
          ],
        };

        myChart.getZr().on("click", (params) => {
          console.log(params);
          let pointInPixel = [params.offsetX, params.offsetY];
          if (myChart.containPixel("grid", pointInPixel)) {
            //点击第几个柱子
            let pointInGrid = myChart.convertFromPixel(
              { seriesIndex: 0 },
              pointInPixel
            );
            // console.log(pointInGrid)
            // 也可以通过params.offsetY 来判断鼠标点击的位置是否是图表展示区里面的位置
            // 也可以通过name[xIndex] != undefined，name是x轴的坐标名称来判断是否还是点击的图表里面的内容
            // x轴数据的索引
            let xIndex = pointInGrid[0];
            // y轴数据的索引
            let yIndex = pointInGrid[1];
            this.bumen = yIndex + 1;
            //模拟数据
            if (yIndex === 0) {
              option.yAxis.data = this.bumen1.map((item) => item.name);
              option.series[0].data = this.bumen1.map((item) => item.value);
              myChart.setOption(option);
            } else if (yIndex === 1) {
              option.yAxis.data = this.bumen2.map((item) => item.name);
              option.series[0].data = this.bumen2.map((item) => item.value);
              myChart.setOption(option);
            } else if (yIndex === 2) {
              option.yAxis.data = this.bumen3.map((item) => item.name);
              option.series[0].data = this.bumen3.map((item) => item.value);
              myChart.setOption(option);
            } else if (yIndex === 3) {
              option.yAxis.data = this.bumen4.map((item) => item.name);
              option.series[0].data = this.bumen4.map((item) => item.value);
              myChart.setOption(option);
            } else if (yIndex === 4) {
              option.yAxis.data = this.bumen5.map((item) => item.name);
              option.series[0].data = this.bumen5.map((item) => item.value);
              myChart.setOption(option);
            }
          }
        });

        myChart.setOption(option);
        myChart.getZr().on("mousemove", (param) => {
          myChart.getZr().setCursorStyle("default");
        });
      },
      initCharts3() {
        let myChart = echarts.init(document.getElementById("charts3"));
        let option = {
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "28",
            },
          },
          legend: {
            orient: "horizontal",
            // icon: "circle",
            itemGap: 45,
            textStyle: {
              color: "#D6E7F9",
              fontSize: 28,
            },
          },
          grid: {
            left: "8%",
            right: "6%",
            top: "18%",
            bottom: "12%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: this.charts3Data.map((item) => item.name),
              axisLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)", // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: "单位：人",
              type: "value",
              nameTextStyle: {
                fontSize: 24,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: "",
              type: "value",
              max: 100,
              nameTextStyle: {
                fontSize: 24,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: "{value}%",
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              type: "bar",
              barWidth: "20%",
              yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#00C0FF",
                    },
                    {
                      offset: 1,
                      color: "rgba(0,192,255,0)",
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              data: this.charts3Data.map((item) => item.value),
            },
          ],
        };
        myChart.setOption(option);
        myChart.getZr().on("mousemove", (param) => {
          myChart.getZr().setCursorStyle("default");
        });
      },
    },
  });
</script>
