<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>交通管理-弹框1</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
  </head>
  <style>
    [v-cloak] {
      display: none;
    }
    html,body,ul,p{
      padding:0;
      margin:0;
      list-style: none;
    }
    .container{
      width:3340px;
      height:700px;
      box-sizing: border-box;
      padding:20px;
      position: relative;
    }

    .top{
      width:100%;
      height:50px;
      text-align: center;
      font-size:40px;
      color:#fff;
      position: relative;
    }
    .boxList{
      width:100%;
      height:650px;
      display: flex;
      justify-content: space-evenly;
    }
    .box{
      width:600px;
      height:100%;
      position: relative;
    }
    .video_box{
      width:600px;
      height:500px;
      margin-top:30px;
    }
    .video_name{
      width:100%;
      text-align: center;
      color: #fff;
      font-size: 40px;
      position: absolute;
      bottom:70px;
    }
    .video_box{
      position: absolute;
      top:1360px;
      left:4230px;
    }


    /* 下拉 */
    .select {
      display: inline-block;
      width: 280px;
      height: 40px;   
      position: absolute;  
      right: 0px; 
    }

    .flow-icon {
      width: 25px;
      position: absolute;
      top: -8px;
      right: 10px;
    }
    .flow-icon1 {
      margin-top: 6px;
      transform: rotateX(180deg);
    }

    .ul > div {
      width: 100%;
      height: 40px;
      line-height: 40px;
    }

    .select ul {
      width: 90%;
      height: 240px;
      text-align: center;
      font-size: 24px;
      color: #fefefe;
      overflow-y: auto;
      display: none;
      list-style: none;
      margin: 0 15px;
      padding: 0;
      position: absolute;
    }

    .select > span {
      display: block;
      font-size: 26px;
      color: #fff;
      position: absolute;
      top: -40px;
      left: 65px;
    }

    .ul {
      width: 100%;
      height: 40px;
      text-align: center;
      font-size: 26px;
      color: #fefefe;
      background-color: #132c4e;
      border: 1px solid #359cf8;
      border-radius: 40px;
      margin-top: 25px;
    }

    .select ul > li {
      width: 100%;
      height: 40px;
      line-height: 40px;
      background-color: #132c4ec2;
      box-sizing: border-box;
    }

    .select ul > li:hover {
      background-color: #359cf8;
    }

    .ul-active {
      display: block !important;
    }

    .ul-active > li:last-of-type {
      border-radius: 0 0 20px 20px;
    }

    .select ul::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
      /* scrollbar-arrow-color: red; */
    }

    .select ul::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 10px;
    }
    
  </style>

  <body>
    <div id="app" class="container" v-cloak>
      <div class="top">
        <span>实时视频</span>
        <div class="select" @click="showSelct=showSelct">
          <div class="flow-icon" :class="showSelct?'flow-icon1':''">
            <img
              src="/static/citybrain/hjbh/img/rkzt/up.png"
              alt=""
              width="25"
            />
          </div>
          <div class="ul" style="margin-top: 0">
            <div style="cursor: not-allowed">{{startName}}</div>
            <ul :class="[showSelct?'ul-active':'']">
              <li
                style="cursor: pointer"
                v-for="(item,index) in flowList"
                @click="change(item)"
              >
                {{item.name}}
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="boxList">
        <div class="box" v-for="(item,index) in showVideoList" :key="index">
          <div class="video_box" :id="item.dom"></div>
          <div class="video_name">{{item.title}}</div>
        </div>
      </div>
    </div>
  </body>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>

  <script>
    const DHWsInstance = DHWs.getInstance({
      reConnectCount: 2,
      connectionTimeout: 30 * 1000,
      messageEvents: {
        loginState() {
          console.log('aaaa')
        },
      },
    })
    var vm = new Vue({
      el: "#app",
      data() {
        return {
          ws:DHWsInstance,
          isLogin:false,
          videoList:[],
          showVideoList:[],
          startName:'暂无',
          showSelct:false,
          flowList:[
            {
              name:'金华站'
            },{
              name:'金华南站'
            }
          ],
          flowList1:[
            {
              name:'金华市汽车西站'
            }
          ],
          flowList2:[
            {
              name:'金华市汽车西站'
            }
          ],
          flowList3:[
            {
              name:'五百滩游船码头'
            }
          ]
        }
      },
      mounted() {
        this.login()
        // this.init()
        let that = this
        // window.addEventListener('message', function (e) {
        //   let info = e.data
        //   console.log(info);
        //   this.ws.destroyCtrl(['ctrl0']);
        //   this.ws.destroyCtrl(['ctrl1']);
        //   this.ws.destroyCtrl(['ctrl2']);
        //   this.ws.destroyCtrl(['ctrl3']);
        //   this.ws.destroyCtrl(['ctrl4']);
        //   if(info==0){
        //     that.flowList = that.flowList0
        //   }else if(info==1){
        //     that.flowList = that.flowList1
        //   }else if(info==2){
        //     that.flowList = that.flowList2
        //   }else if(info==3){
        //     that.flowList = that.flowList3
        //   }
        //   that.startName = that.flowList[0].name
        //   that.getVideoList()
        // })
        top.emiter && top.emiter.on('info111',(res)=>{
            console.log(res)
            that.getVideoList(res)
        })
      },
      methods: {
        init(){
          $get('/shgl/doing/jtgl1_1').then(res=>{
            this.flowList=res
          })
        },
        change(item){
          this.startName = item.name
        },
        login(){
          // 调用登录接口
          this.ws.detectConnectQt().then((res) => {
            if (res) {
              // 连接客户端成功
              this.ws.login({
                loginIp: "*************",
                loginPort: "8001",
                userName: "yjgl",
                userPwd: "yjgl1234",
                token: "",
                https: 0,
              })
              // this.$Message.info('登录中...')
              console.log('登录中...')
              this.ws.on('loginState', (res) => {
                this.isLogin = res
                if (res) {
                  console.log('登录成功')
                  this.getVideoList(0);
                } else {
                  console.log('登录失败')
                }
              })
            } else {
              // 连接客户端失败
              this.$Message.info('请重新安装客户端')
            }
          })
        },
        getVideoList(i) {

          // $api('/csdn/cstz/cstzRight009', { code: 1 }).then((res) => {
            let res = [
              {
                channelId: "33070265001311084456",
                title: "婺城高铁火车站站前广场3"
              },
              {
                channelId: "33070265001320082412",
                title: "婺城高铁火车站站前广场9"
              },
              {
                channelId: "33070352001321080065",
                title: "金东金华南站一站台中间朝检票口"
              },
              {
                channelId: "33070352001320080218",
                title: "金东金华南站候车室2"
              },
              {
                channelId: "33070352001320080223",
                title: "金东金华南站候车室3"
              },
              {
                channelId: "33071007041321088093",
                title: "岭下朱高速金华方向"
              },
              {
                channelId: "33071007041321087792",
                title: "岭下朱高速武义方向路口"
              },
              {
                channelId: "33070255001321082162",
                title: "迎宾大道高速出口"
              },
              {
                channelId: "33070255001321082395",
                title: "婺城高速公路出口"
              },
              {
                channelId: "33079957001320080194",
                title: "江南罗埠金华西高速路口"
              },
              {
                channelId: "33070265001320082864",
                title: "婺城汽车西站候车厅内1"
              },
              {
                channelId: "33070299001321043504",
                title: "迪耳路汽车西站球机北"
              },
              {
                channelId: "33079952001320081917",
                title: "江南三江汽车南站西北侧1"
              },
              {
                channelId: "33079952001320081918",
                title: "江南三江汽车南站西北侧2"
              },
              {
                channelId: "33070265001320082865",
                title: "婺城汽车西站候车厅内2"
              },
              {
                channelId: "33070251001320080238",
                title: "婺城婺州公园游船码头1"
              },
              {
                channelId: "33070251001320080239",
                title: "婺城婺州公园游船码头2"
              },
              {
                channelId: "33070254001320080319",
                title: "婺城燕尾洲公园北面游船码头路口"
              },
              {
                channelId: "33078154001321086297",
                title: "兰溪水上水门码头围墙"
              },
              {
                channelId: "33078159621320083030",
                title: "兰溪女埠邵家码头1(镇)"
              },
            ]
            this.showVideoList=[]
            this.videoList = res.slice(i*5,(i+1)*5).map((ele,index)=>{
              let str={
                channelId: ele.channelId,
                title: ele.title,
                dom: "dom"+index,
                num:index
              }
              return str
              // this.showVideoList.push(str)
            })
            this.showVideoList = this.videoList
            this.create();
          // })
        },
        create() { // 调用创建控件接口
          var this_ = this
          var paramList = [];
          console.log(this_.showVideoList);
          for (let index = 0; index < this_.showVideoList.length; index++) {
                let item = this_.showVideoList[index]
                console.log(item);
                paramList[index] = {
                    ctrlType: 'playerWin',
                    ctrlCode: 'ctrl' + item.num,
                    ctrlProperty: {
                        displayMode: 1,
                        splitNum: 1,
                        channelList: [
                            {
                                channelId: item.channelId,
                            },
                        ],
                    },
                    visible: true,
                    domId: item.dom
                }
            }
          console.log("paramList===>",paramList);

          setTimeout(function () {
            this_.ws.createCtrl(paramList).then(res => {
              console.log(res);
            }).catch(e => {
              console.log(e);
            });
            this_.ws.on('createCtrlResult', (res) => {
              console.warn(res);
            });
          }, 2000)

        },
      },
    });
  </script>
</html>
