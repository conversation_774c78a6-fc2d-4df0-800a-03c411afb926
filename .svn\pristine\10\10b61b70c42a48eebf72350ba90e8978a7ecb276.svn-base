<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>一体化智能平台-中间</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
</head>
<style>
    [v-cloak] {
        display: none;
    }

    html,
    body,
    ul,
    p {
        padding: 0;
        margin: 0;
        list-style: none;
    }

    .container {
        width: 1700px;
        height: 1930px;
        background-color: #0a2443;
        padding: 30px;
        box-sizing: border-box;
    }

    .header-title2[data-v-4d0d1712] {
        width: 100% !important;
    }

    .zjqkzs {
        width: 100%;
        height: 750px;
        display: flex;
    }

    .zjqkzs-left {
        width: 50%;
        height: 750px;
        display: flex;
        justify-content: space-evenly;
        flex-wrap: wrap;
    }

    .zjqkzs-right {
        width: 50%;
        height: 750px;
    }

    .sjzyzs {
        width: 100%;
        height: 960px;
    }

    .sjzyzs-top {
        display: flex;
        justify-content: space-evenly;
        margin: 20px 0;
    }

    .sjzyzs-bottom-left,
    .sjzyzs-bottom-right {
        width: 50%;
        height: 800px;
    }

    .item {
        width: 40%;
        height: 130px;
        background-color: #0087ec;
        padding: 10px 0;
        box-sizing: border-box;
    }

    .item1 {
        width: 20%;
    }

    .item>p {
        text-align: center;
        font-size: 30px;
        color: white;
        margin: 10px 0;
    }

    .el-date-editor .el-range-input,
    .el-date-editor .el-range-separator {
        font-size: 20px;
    }

    .el-select .el-input .el-input__inner {
        font-size: 24px;
    }

    .el-select-dropdown__item {
        font-size: 24px;
    }
</style>

<body>
    <div id="app" class="container" v-cloak>
        <nav>
            <s-header-title-2 title="组件情况展示" htype="1"></s-header-title-2>
        </nav>
        <div class="zjqkzs">
            <div class="zjqkzs-left">
                <div class="item" v-for="(item,index) in itemList" :key="index">
                    <p>{{item.value + item.unit}}</p>
                    <p>{{item.name}}</p>
                </div>
            </div>
            <div class="zjqkzs-right">
                <nav>
                    <s-header-title-2 title="高频调用组件排名" htype="2"></s-header-title-2>
                </nav>
                <div id="chart01" style="width: 100%; height: 700px"></div>
            </div>
        </div>
        <nav>
            <s-header-title-2 title="数据资源展示" htype="1"></s-header-title-2>
        </nav>
        <div class="sjzyzs">
            <div class="sjzyzs-top">
                <div class="item item1" v-for="(item,index) in item1List" :key="index">
                    <p>{{item.value + item.unit}}</p>
                    <p>{{item.name}}</p>
                </div>
            </div>
            <div class="sjzyzs-bottom" style="display: flex; padding: 20px; box-sizing: border-box">
                <div class="sjzyzs-bottom-left">
                    <nav>
                        <s-header-title-2 title="数据共享申请总数变化分析" htype="2"></s-header-title-2>
                    </nav>
                    <div style="display: flex; font-size: 30px; color: #fff">
                        <el-select v-model="value3" style="width: 150px" @change="change">
                            <el-option v-for="(item,index) in options" :key="index" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                        <div>至</div>
                        <el-select v-model="value2" style="width: 150px" @change="change1">
                            <el-option v-for="(item,index) in options1" :key="index" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                    </div>

                    <div id="chart02" style="width: 100%; height: 600px; margin-top: 100px"></div>
                </div>
                <div class="sjzyzs-bottom-right">
                    <nav>
                        <s-header-title-2 title="数据有效利用率分析" htype="2"></s-header-title-2>
                    </nav>
                    <div style="display: flex; font-size: 30px; color: #fff">
                        <el-select v-model="value3" style="width: 150px" @change="change11">
                            <el-option v-for="(item,index) in options" :key="index" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                        <div>至</div>
                        <el-select v-model="value2" style="width: 150px" @change="change111">
                            <el-option v-for="(item,index) in options1" :key="index" :label="item.label"
                                :value="item.value"></el-option>
                        </el-select>
                    </div>
                    <div id="chart03" style="width: 100%; height: 600px; margin-top: 100px"></div>
                </div>
            </div>
        </div>
    </div>
</body>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>

<script type="module">
    new Vue({
        el: "#app",
        data: {
            item1List: [],
            itemList: [],
            options: [
                {
                    value: "1月",
                    label: "1月",
                },
                {
                    value: "2月",
                    label: "2月",
                },
                {
                    value: "3月",
                    label: "3月",
                },
                {
                    value: "4月",
                    label: "4月",
                },
                // {
                //     value: "5月",
                //     label: "5月",
                // },
                // {
                //     value: "6月",
                //     label: "6月",
                // },
                // {
                //     value: "7月",
                //     label: "7月",
                // },
                // {
                //     value: "8月",
                //     label: "8月",
                // },
                // {
                //     value: "9月",
                //     label: "9月",
                // },
                // {
                //     value: "10月",
                //     label: "10月",
                // },
                // {
                //     value: "11月",
                //     label: "11月",
                // },
                // {
                //     value: "12月",
                //     label: "12月",
                // },
            ],
            options1: [
                {
                    value: "1月",
                    label: "1月",
                },
                {
                    value: "2月",
                    label: "2月",
                },
                {
                    value: "3月",
                    label: "3月",
                },
                {
                    value: "4月",
                    label: "4月",
                },
                // {
                //     value: "5月",
                //     label: "5月",
                // },
                // {
                //     value: "6月",
                //     label: "6月",
                // },
                // {
                //     value: "7月",
                //     label: "7月",
                // },
                // {
                //     value: "8月",
                //     label: "8月",
                // },
                // {
                //     value: "9月",
                //     label: "9月",
                // },
                // {
                //     value: "10月",
                //     label: "10月",
                // },
                // {
                //     value: "11月",
                //     label: "11月",
                // },
                // {
                //     value: "12月",
                //     label: "12月",
                // },
            ],
            value3: "1月",
            value2: "4月",
            value1: null,
            pickerOptions: {
                shortcuts: [
                    {
                        text: "最近一周",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: "最近一个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                    {
                        text: "最近三个月",
                        onClick(picker) {
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
                            picker.$emit("pick", [start, end]);
                        },
                    },
                ],
            },
            mins: "1",
            maxs: "5",
            minss: "1",
            maxss: "5",
        },
        //项目生命周期
        mounted() {
            this.init();
        },

        methods: {
            change(item) {
                this.mins = item;
                $api("ldst_shgl_ythznpt13", { min: item, max: this.maxs }).then((res) => {
                    this.lineEchart("chart02", res, "次", "申请次数");
                });
            },
            change1(item) {
                this.maxs = item;
                $api("ldst_shgl_ythznpt13", { min: this.mins, max: item }).then((res) => {
                    this.lineEchart("chart02", res, "次", "申请次数");
                });
            },
            change11(item) {
                this.minss = item;
                $api("ldst_shgl_ythznpt14", { min: item, max: this.maxss }).then((res) => {
                    this.lineEchart01("chart03", res, "次", "有效使用数据", "已申请数据");
                });
            },
            change111(item) {
                this.maxss = item;
                $api("ldst_shgl_ythznpt14", { min: this.minss, max: item }).then((res) => {
                    this.lineEchart01("chart03", res, "次", "有效使用数据", "已申请数据");
                });
            },
            init() {
                $api("ldst_shgl_ythznpt", { type: 10 }).then((res) => {
                    this.itemList = res;
                });
                $api("ldst_shgl_ythznpt", { type: 11 }).then((res) => {
                    this.barEchart02("chart01", res, "20%");
                });
                $api("ldst_shgl_ythznpt", { type: 12 }).then((res) => {
                    this.item1List = res;
                });
                $api("ldst_shgl_ythznpt13", { min: 1, max: 4 }).then((res) => {
                    this.lineEchart("chart02", res, "次", "申请次数");
                });
                $api("ldst_shgl_ythznpt14", { min: 1, max: 4 }).then((res) => {
                    this.lineEchart01("chart03", res, "次", "有效使用数据", "已申请数据");
                });
            },

            barEchart02(id, barData, left) {
                const myEc = echarts.init(document.getElementById(id));
                barData.sort(function (a, b) {
                    return a.value - b.value;
                });
                let yData = barData.map((item) => {
                    return item.name;
                });
                let value = barData.map((item) => {
                    return item.value;
                });
                let option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 24,
                        },
                    },
                    grid: {
                        top: "10%",
                        bottom: "10%",
                        left: left,
                    },
                    xAxis: {
                        type: "value",

                        splitLine: {
                            show: false,
                        },
                        axisLabel: {
                            fontSize: 24,
                            color: "#ffff",
                        },
                    },
                    yAxis: {
                        type: "category",
                        data: yData,
                        axisLabel: {
                            fontSize: 24,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            name: "数据",
                            type: "bar",
                            data: value,
                            label: {
                                normal: {
                                    show: true,
                                    position: "right",
                                    formatter: "{c}次",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 24,
                                    },
                                },
                            },
                        },
                    ],
                };
                myEc.setOption(option);
            },

            lineEchart(id, lineData, unit, name) {
                const myEc = echarts.init(document.getElementById(id));
                let datax = [],
                    datay = [];
                lineData.map((ele) => {
                    datax.push(ele.name);
                    datay.push(ele.value);
                });
                let option = {
                    legend: {
                        show: true,
                        x: "center",
                        y: "10",
                        itemWidth: 40,
                        itemHeight: 20,
                        textStyle: {
                            color: "#fff",
                            fontSize: "28",
                        },
                    },
                    grid: {
                        left: "3%",
                        right: "4%",
                        bottom: "8%",
                        containLabel: true,
                    },
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            color: "rgba(212, 232, 254, 1)",
                            fontSize: 28,
                        },
                    },
                    xAxis: [
                        {
                            type: "category",
                            offset: 20,
                            axisLine: {
                                //坐标轴轴线相关设置。数学上的x轴
                                show: true,
                                lineStyle: {
                                    color: "rgba(108, 166, 219, 0.3)",
                                },
                            },
                            axisLabel: {
                                //坐标轴刻度标签的相关设置
                                // rotate: -30,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: "#192a44",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            data: datax,
                        },
                    ],
                    yAxis: [
                        {
                            name: "单位:" + unit,
                            min: (value) => {
                                return parseInt(value.min - 1);
                            },
                            nameTextStyle: {
                                fontSize: 24,
                                color: "#D6E7F9",
                                padding: [0, 20, 10, 0],
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                formatter: "{value}" + unit,
                                textStyle: {
                                    fontSize: 28,
                                    color: "#D6E7F9",
                                },
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: "#233653",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: name,
                            type: "line",
                            symbolSize: 10,
                            itemStyle: {
                                normal: {
                                    // color: "#3A84FF",
                                    lineStyle: {
                                        // color: "#1b759c",
                                        width: 2,
                                    },
                                },
                            },
                            data: datay,
                        },
                    ],
                };
                myEc.setOption(option);
            },
            lineEchart01(id, lineData, unit, name, name1) {
                const myEc = echarts.init(document.getElementById(id));
                let datax = [],
                    datay = [],
                    datay1 = [];
                lineData.map((ele) => {
                    datax.push(ele.name);
                    datay.push(ele.value);
                    datay1.push(ele.unit);
                });
                let option = {
                    legend: {
                        show: true,
                        x: "center",
                        y: "10",
                        itemWidth: 40,
                        itemHeight: 20,
                        textStyle: {
                            color: "#fff",
                            fontSize: "28",
                        },
                    },
                    grid: {
                        left: "3%",
                        right: "4%",
                        bottom: "8%",
                        containLabel: true,
                    },
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            color: "rgba(212, 232, 254, 1)",
                            fontSize: 28,
                        },
                    },
                    xAxis: [
                        {
                            type: "category",
                            offset: 20,
                            axisLine: {
                                //坐标轴轴线相关设置。数学上的x轴
                                show: true,
                                lineStyle: {
                                    color: "rgba(108, 166, 219, 0.3)",
                                },
                            },
                            axisLabel: {
                                //坐标轴刻度标签的相关设置
                                // rotate: -30,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: "#192a44",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            data: datax,
                        },
                    ],
                    yAxis: [
                        {
                            name: "单位:" + unit,
                            min: (value) => {
                                return parseInt(value.min - 1);
                            },
                            nameTextStyle: {
                                fontSize: 24,
                                color: "#D6E7F9",
                                padding: [0, 20, 10, 0],
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                formatter: "{value}" + unit,
                                textStyle: {
                                    fontSize: 28,
                                    color: "#D6E7F9",
                                },
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: "#233653",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: name,
                            type: "line",
                            symbolSize: 10,
                            itemStyle: {
                                normal: {
                                    // color: "#3A84FF",
                                    lineStyle: {
                                        // color: "#1b759c",
                                        width: 2,
                                    },
                                },
                            },
                            data: datay,
                        },
                        {
                            name: name1,
                            type: "line",
                            symbolSize: 10,
                            data: datay1,
                        },
                    ],
                };
                myEc.setOption(option);
            },
        },
    });
</script>

</html>