let pieChartOption4 = {
    tooltip: {
        //提示框
        show: false,
        trigger: "item",
        borderWidth: 0,
        borderRadius: 5,
        padding: 20,
        backgroundColor: "#384c63",
        textStyle: {
            color: "#d0e1f5",
            fontSize: 30,
        },
        formatter: function (params) {
            // let iconHtml = `<span style="display:inline-block;margin-right:5px;border-radius:20px;
            // width:20px;height:20px;"></span>`
            return `${params.marker} ${params.name}：
                <br> <span style="margin-left:30px">${params.value}户 ${params.percent}%</span>`;
        },
    },

    legend: {
        //图例
        show: false,
        orient: "vertical", //垂直排列
        icon: "circle",
        right: 40,
        top: 60,
        align: "left", //icon的位置
        itemWidth: 16,
        itemHeight: 16, //icon的宽高
        itemGap: 50, //图例每项之间的间隔
        textStyle: {
            fontSize: 30,
            color: "#d6e7f9",
        },
        formatter: function (name) {
            let total = 0;
            let tarValue;
            for (let i = 0; i < data.length; i++) {
                total += data[i].value;
                if (data[i].name == name) {
                    tarValue = data[i].value;
                }
            }
            let percent = ((tarValue / total) * 100).toFixed(2);
            return ` ${name} :  ${tarValue}户  ${percent}%`;
        },
    },

    color: ["#32d7d9", "#b076cd"], //颜色
    series: [
        {
            type: "pie",
            radius: ["40%", "60%"],
            center: ["50%", "50%"], //调整图位置
            avoidLabelOverlap: false,
            minAngle: 15,
            startAngle: 60,
            roseType: "radius",
            label: {
                show: true,
                position: "outside",
                formatter: `{a|{b}}\n{b|{c}家}\n{c|{d}%}`,
                rich: {
                    a: {
                        fontSize: 30,
                        color: "#d6e7f9",
                    },
                    b: {
                        fontSize: 30,
                        color: "#d6e7f9",
                    },
                    c: {
                        fontSize: 30,
                        color: "#ffff",
                    },
                },
            },
            labelLine: {
                show: true,
                length: 5,
                length2: 5,
            },
            data: [],
        },
    ],
};
