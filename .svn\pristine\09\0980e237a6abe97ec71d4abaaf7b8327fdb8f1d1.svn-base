<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>城市安全管控指标分析-左</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
      }
      #app {
        position: relative;
        width: 1050px;
        height: 1930px;
        background: url('/img/left-bg.png') no-repeat;
        background-size: 100% 100%;
        padding: 30px;
        box-sizing: border-box;
        overflow: hidden;
      }
      .header-title2[data-v-4d0d1712] {
        width: 100% !important;
      }
      .main_list {
        width: 420px !important;
        height: 130px !important;
        margin: 10px 30px;
        background: linear-gradient(
          to bottom,
          rgba(31, 105, 218, 0.2),
          rgba(31, 105, 218, 0.5),
          rgba(31, 105, 218, 0.9)
        );
        text-align: center;
        padding-top: 40px;
      }
      /* 下拉框 */
      .select {
        position: absolute;
        left: 775px;
        top: 577px;
      }
      .el-select {
        width: 150px;
      }
      .el-input__inner {
        height: 50px !important;
        width: 200px !important;
        background-color: #00487f;
        color: #fff;
        font-size: 28px;
      }
      .el-select-dropdown {
        border: 1px solid #2578a6;
        background-color: #032f46d3;
      }
      .el-select-dropdown__item.hover,
      .el-select-dropdown__item:hover {
        background-color: #00487f;
      }
      .el-select-dropdown__item {
        color: #fff;
        background-color: #00487f;
        font-size: 28px;
        height: 50px;
        line-height: 50px;
      }
      .el-select-dropdown__list {
        background-color: #00487f;
      }
      .el-select .el-input .el-select__caret {
        position: relative;
        left: 40px;
        font-size: 28px;
        color: #fff;
      }
      .el-select .el-input__inner {
        /* border-radius: 30px !important; */
      }
      .el-scrollbar {
        width: 200px;
      }
      .el-input.is-disabled .el-input__inner {
        background-color: #2d4a67;
      }
    </style>
  </head>
  <body>
    <div id="app" style="position: relative">
      <nav class="s-m-t-20 s-m-b-10">
        <s-header-title title="社会治安事件分析" htype="2"></s-header-title>
      </nav>
      <i
        class="el-icon-refresh"
        @click="showChart=!showChart"
        style="
          position: absolute;
          right: 30px;
          top: 125px;
          font-size: 40px;
          color: #fff;
        "
      ></i>
      <div class="s-flex s-flex-wrap s-row-between" v-show="!showChart">
        <dv-border-box-8
          class="main_list"
          v-for="(item,index) in list"
          :key="index"
        >
          <p class="s-font-30 s-c-white">{{item.name}}</p>
          <p class="s-font-30 s-c-white">
            <count-to
              :start-val="0"
              :end-val="Number(item.value)"
              :duration="3000"
              class="s-c-yellow-gradient s-font-40"
            ></count-to>
            {{item.unit}}
          </p>
        </dv-border-box-8>
      </div>
      <div class="s-flex s-flex-wrap s-row-between" v-show="showChart">
        <div id="chart001" style="width: 990px; height: 380px"></div>
      </div>

      <nav class="s-m-t-20 s-m-b-10">
        <s-header-title title="区域分类统计分析" htype="2"></s-header-title>
      </nav>
      <div class="select">
        <el-select v-model="value" @change="selectMothFun" placeholder="月份">
          <el-option
            v-for="item,index in options"
            :key="index"
            :label="item"
            :value="item"
          >
          </el-option>
        </el-select>
      </div>
      <div id="line_eh" style="width: 100%; height: 600px"></div>
      <nav class="s-m-t-20 s-m-b-10">
        <s-header-title
          title="社会突发事件分类统计分析"
          htype="2"
        ></s-header-title>
      </nav>
      <div id="pie_eh" style="width: 100%; height: 480px"></div>
    </div>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
      let vm = new Vue({
        el: '#app',
        data: {
          list: [],
          value: '一月',
          options: [
            '一月',
            '二月',
            '三月',
            '四月',
            // '五月',
            // '六月',
            // '七月',
            // '八月',
            // '九月',
            // '十月',
            // '十一月',
          ],
          showChart: false,
        },
        mounted() {
          this.initApi()
        },
        methods: {
          initApi() {
            $api('ldst_shgl_csaqgk', { type1: 1 }).then((res) => {
              this.list = res.slice(0, 4)
              this.getLine001('chart001', this.list)
            })
            $api('ldst_shgl_csaqgk_month', { type1: 2, month: '一月' }).then(
              (res) => {
                this.getLine('line_eh', res)
              }
            )
            $api('ldst_shgl_csaqgk', { type1: 3 }).then((res) => {
              this.getPie('pie_eh', res)
            })
          },
          selectMothFun(e) {
            $api('ldst_shgl_csaqgk_month', { type1: 2, month: e }).then(
              (res) => {
                this.getLine('line_eh', res)
              }
            )
          },

          getLine(id, echartsData) {
            const myChartsRun = echarts.init(document.getElementById(id))
            let xdata = [],
              ydata1 = [],
              ydata2 = []
            echartsData.map((ele) => {
              xdata.push(ele.name)
              ydata1.push(ele.xs)
              ydata2.push(ele.za)
            })

            let option = {
              tooltip: {
                trigger: 'axis',
                borderWidth: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                textStyle: {
                  color: 'white',
                  fontSize: '24',
                },
              },
              legend: {
                show: true,
                icon: 'circle',
                top: 20,
                textStyle: {
                  fontSize: 24,
                  color: '#c8c8c8',
                },
              },
              grid: {
                left: '5%',
                right: '5%',
                top: '15%',
                bottom: '6%',
                containLabel: true,
              },
              xAxis: {
                type: 'category',
                boundaryGap: true, //坐标轴两边留白
                axisLabel: {
                  color: '#fff',
                  fontSize: '26px',
                },
                axisLine: {
                  show: true,
                  lineStyle: {
                    color: '#bbb',
                  },
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    color: '#195384',
                  },
                },
                data: xdata,
              },
              yAxis: {
                type: 'value',
                name: '单位：起',
                splitNumber: 5,
                nameTextStyle: {
                  color: '#fff',
                  fontSize: 24,
                  padding: [10, 0, 20, 0],
                },
                axisLabel: {
                  formatter: '{value}',
                  textStyle: {
                    color: '#fff',
                    fontSize: '24px',
                  },
                },
                axisLine: {
                  lineStyle: {
                    color: '#fff',
                  },
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    color: '#5087EC',
                  },
                },
              },
              series: [
                {
                  name: '刑事案件发生量',
                  type: 'line',
                  smooth: true,
                  symbol: 'circle',
                  symbolSize: 13,
                  lineStyle: {
                    normal: {
                      width: 3,
                    },
                  },
                  itemStyle: {
                    color: '#027ad7',
                    borderColor: '#fff',
                    borderWidth: 2,
                  },
                  areaStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 0,
                            color: 'rgba(2, 134, 236, 0.5)',
                          },
                          {
                            offset: 0.8,
                            color: 'rgba(2, 134, 236, 0.1)',
                          },
                        ],
                        false
                      ),
                      shadowColor: 'rgba(2, 134, 236, 0.5)',
                      shadowBlur: 10,
                    },
                  },
                  data: ydata1,
                },
                {
                  name: '治安事件发生量',
                  type: 'line',
                  smooth: true,
                  symbol: 'circle',
                  symbolSize: 13,
                  lineStyle: {
                    normal: {
                      width: 3,
                    },
                  },
                  itemStyle: {
                    color: '#8452e7',
                    borderColor: '#fff',
                    borderWidth: 2,
                  },
                  areaStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 0,
                            color: 'rgba(145, 90, 254, 0.5)',
                          },
                          {
                            offset: 0.8,
                            color: 'rgba(145, 90, 254, 0.1)',
                          },
                        ],
                        false
                      ),
                      shadowColor: 'rgba(145, 90, 254, 0.5)',
                      shadowBlur: 10,
                    },
                  },
                  data: ydata2,
                },
              ],
            }

            myChartsRun.setOption(option)
          },
          getPie(id, echartsData) {
            const myChartsRun = echarts.init(document.getElementById(id))

            let imgUrl = '/static/citybrain/djtl/img/djtl-left/echarts-bg.png'
            const option = {
              tooltip: {
                trigger: 'item',
                formatter: '{b}: <br/>{d}%',
                borderWidth: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                textStyle: {
                  color: 'white',
                  fontSize: '30',
                },
              },
              legend: {
                orient: 'vertical',
                itemWidth: 18,
                itemHeight: 18,
                left: '60%',
                top: '25%',
                icon: 'circle',
                itemGap: 50,
                textStyle: {
                  color: '#D6E7F9',
                  fontSize: 30,
                  padding: [0, 0, 0, 20],
                },
                formatter: function (name) {
                  var data = option.series[0].data //获取series中的data
                  var total = 0
                  var tarValue
                  for (var i = 0, l = data.length; i < l; i++) {
                    total += data[i].value
                    if (data[i].name == name) {
                      tarValue = data[i].value
                    }
                  }
                  var p = (tarValue / total) * 100
                  return name + '  ' + tarValue + '件'
                },
              },
              graphic: [
                {
                  z: 4,
                  type: 'image',
                  id: 'logo',
                  left: '10%',
                  top: '12.5%',
                  z: -10,
                  bounding: 'raw',
                  rotation: 0, //旋转
                  origin: [-30, 30], //中心点
                  scale: [1.2, 1.2], //缩放
                  style: {
                    image: imgUrl,
                    opacity: 1,
                  },
                },
              ],
              series: [
                {
                  name: '',
                  type: 'pie',
                  radius: ['50%', '80%'],
                  center: ['30%', '50%'],
                  itemStyle: {
                    normal: {
                      borderColor: '#0A1934',
                      // borderWidth: 10
                    },
                  },
                  label: {
                    show: false,
                  },
                  data: echartsData,
                },
              ],
            }

            myChartsRun.setOption(option)
          },
          getLine001(id, echartsData) {
            const myChartsRun = echarts.init(document.getElementById(id))
            let xdata = [],
              ydata = []
            echartsData.map((ele) => {
              xdata.push(ele.name)
              ydata.push(ele.value)
            })

            let option = {
              tooltip: {
                trigger: 'axis',
                borderWidth: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                textStyle: {
                  color: 'white',
                  fontSize: '24',
                },
              },
              legend: {
                show: true,
                icon: 'circle',
                top: 20,
                textStyle: {
                  fontSize: 24,
                  color: '#c8c8c8',
                },
              },
              grid: {
                left: '5%',
                right: '5%',
                top: '15%',
                bottom: '6%',
                containLabel: true,
              },
              xAxis: {
                type: 'category',
                boundaryGap: true, //坐标轴两边留白
                axisLabel: {
                  color: '#fff',
                  fontSize: '26px',
                },
                axisLine: {
                  show: true,
                  lineStyle: {
                    color: '#bbb',
                  },
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    color: '#195384',
                  },
                },
                data: xdata,
              },
              yAxis: {
                type: 'value',
                // name: "单位：起",
                splitNumber: 5,
                nameTextStyle: {
                  color: '#fff',
                  fontSize: 24,
                  padding: [10, 0, 20, 0],
                },
                axisLabel: {
                  formatter: '{value}',
                  textStyle: {
                    color: '#fff',
                    fontSize: '24px',
                  },
                },
                axisLine: {
                  lineStyle: {
                    color: '#fff',
                  },
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    color: '#5087EC',
                  },
                },
              },
              series: [
                {
                  name: '',
                  type: 'line',
                  smooth: true,
                  symbol: 'circle',
                  symbolSize: 13,
                  lineStyle: {
                    normal: {
                      width: 3,
                    },
                  },
                  itemStyle: {
                    color: '#027ad7',
                    borderColor: '#fff',
                    borderWidth: 2,
                  },
                  areaStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 0,
                            color: 'rgba(2, 134, 236, 0.5)',
                          },
                          {
                            offset: 0.8,
                            color: 'rgba(2, 134, 236, 0.1)',
                          },
                        ],
                        false
                      ),
                      shadowColor: 'rgba(2, 134, 236, 0.5)',
                      shadowBlur: 10,
                    },
                  },
                  data: ydata,
                },
              ],
            }

            myChartsRun.setOption(option)
          },
        },
      })
    </script>
  </body>
</html>
