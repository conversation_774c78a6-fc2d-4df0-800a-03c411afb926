<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>职称人员分布弹窗</title>
    <script src="/Vue/vue.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css">
    <link rel="stylesheet" href="/static/citybrain/ggfw/css/common-dialog.css">
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script> <!-- 轮播toolTip -->
</head>

<style>
    .table1 .th .th_td:nth-child(1) {
        flex: 0.1 !important;
    }

    .table1 .th .th_td:nth-child(2) {
        flex: 0.1;
    }

    .table1 .th .th_td:nth-child(3) {
        flex: 0.1;
    }

    .table1 .th .th_td:nth-child(4) {
        flex: 0.2;
    }

    .table1 .th .th_td:nth-child(5) {
        flex: 0.25;
    }

    .table1 .th .th_td:nth-child(6) {
        flex: 0.25;
    }

    #zyly-chart {
        width: 100%;
        height: 300px;
    }

    .ryly-con {
        width: 100%;
        height: 300px;
        display: flex;
    }

    .ryly,
    .xbfx {
        width: 50%;
        height: 100%;
    }

    #ryly-chart,
    #xbfx-chart {
        width: 100%;
        height: 85%;
    }
</style>

<body>
    <div id="app" class="container" style="min-height: 700px;" v-cloak>
        <div class="head">
            <span>{{title}}</span>
            <div class="img" @click="closeDialog" style="cursor: pointer;"></div>
        </div>
        <div class="content">
            <div class="spajqd">
                <div class="table1">
                    <div class="th">
                        <div class="th_td" style="flex: 0.3 1 0%!important;" v-for="(item,index) in theadList" :key="index">
                            {{item}}
                        </div>
                    </div>
                    <div class="tbody" id="box0" @mouseover="mouseenterEvent()" @mouseleave="mouseleaveEvent()">
                        <div class="tr" v-for="(item ,i) in tableList" :key="i">
                            <div class="tr_td" style="flex: 0.3 1 0%;">{{i+1}}</div>
                            <div class="tr_td" style="flex: 0.3 1 0%;">{{item.name}}</div>
                            <div class="tr_td" style="flex: 0.3 1 0%;">{{item.num}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
<script type="module">

    new Vue({
        el: '#app',
        data: {
            title: "高级职称",
            jbxxData: [],//基本信息数据
            theadList: ['序号', '专业名', '人数',],
            tableList: [],//表格数据

        },
        methods: {
            mouseenterEvent() {
                clearInterval(this.time)
            },
            mouseleaveEvent() {
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },
            autoScroll() {
                this.dom = document.getElementById('box0')
                // this.scpDom = document.getElementsByClassName('text')
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },
            closeDialog() {
                top.commonObj.funCloseIframe({
                    name: 'rcjs-people-dialog'
                })
            },
            //数据初始化
            init() {

            },
        },
        //项目生命周期
        mounted() {
            let that = this
            window.addEventListener('message', function (event) {
                that.title = event.data.status
                switch (event.data.status) {
                    case "高级职称":
                        that.tableList = [
                            {
                                name: "工程类",
                                num: "21",
                            },
                            {
                                name: "农业类",
                                num: "12",
                            },
                            {
                                name: "教学类",
                                num: "32",
                            },
                            {
                                name: "飞机技术",
                                num: "12",
                            },
                            {
                                name: "经济类",
                                num: "32",
                            },
                            {
                                name: "企业法律顾问",
                                num: "34",
                            },
                            {
                                name: "会计类",
                                num: "22",
                            },
                            {
                                name: "统计类",
                                num: "34",
                            },
                            {
                                name: "翻译类",
                                num: "44",
                            },
                            {
                                name: "律师类",
                                num: "12",
                            },
                            {
                                name: "公证类",
                                num: "43",
                            },
                            {
                                name: "广播电视类",
                                num: "12",
                            },
                        ]
                        break;
                    case "中级职称":
                        that.tableList = [
                            {
                                name: "工程类",
                                num: "53",
                            },
                            {
                                name: "农业类",
                                num: "54",
                            },
                            {
                                name: "教学类",
                                num: "56",
                            },
                            {
                                name: "飞机技术",
                                num: "67",
                            },
                            {
                                name: "经济类",
                                num: "21",
                            },
                            {
                                name: "企业法律顾问",
                                num: "45",
                            },
                            {
                                name: "会计类",
                                num: "43",
                            },
                            {
                                name: "统计类",
                                num: "43",
                            },
                            {
                                name: "翻译类",
                                num: "65",
                            },
                            {
                                name: "律师类",
                                num: "67",
                            },
                            {
                                name: "公证类",
                                num: "34",
                            },
                            {
                                name: "广播电视类",
                                num: "43",
                            },
                        ]
                        break;
                    case "初级职称":
                        that.tableList = [
                            {
                                name: "工程类",
                                num: "211",
                            },
                            {
                                name: "农业类",
                                num: "122",
                            },
                            {
                                name: "教学类",
                                num: "312",
                            },
                            {
                                name: "飞机技术",
                                num: "312",
                            },
                            {
                                name: "经济类",
                                num: "342",
                            },
                            {
                                name: "企业法律顾问",
                                num: "734",
                            },
                            {
                                name: "会计类",
                                num: "228",
                            },
                            {
                                name: "统计类",
                                num: "374",
                            },
                            {
                                name: "翻译类",
                                num: "414",
                            },
                            {
                                name: "律师类",
                                num: "132",
                            },
                            {
                                name: "公证类",
                                num: "453",
                            },
                            {
                                name: "广播电视类",
                                num: "112",
                            },
                        ]
                        break;

                    default:
                        break;
                }
            })
            this.init();
            this.autoScroll();
        }


    })


</script>

</html>