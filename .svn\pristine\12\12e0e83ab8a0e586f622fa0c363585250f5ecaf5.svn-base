<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>隐患数据按监管行业统计</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <link rel="stylesheet" href="../css/shgl-csaq-yhsjajghytj-left.css" />
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <style>
            .titleOne {
                position: relative;
            }
            .select {
                position: absolute;
                z-index: 99;
                right: 0;
            }
            .el-input__inner {
                font-size: 30px;
                height: 50px;
                background-color: #113854;
                width: 160px;
                color: white;
            }
            .el-select-dropdown__item.hover,
            .el-select-dropdown__item:hover {
                background-color: #113854;
            }
            .el-select-dropdown {
                font-size: 30px;
                background-color: #113854;
            }
            .el-select-dropdown__item {
                font-size: 30px;
                color: white;
            }
            .tbody {
                height: 320px;
                overflow: auto;
            }
        </style>
    </head>

    <body>
        <div id="shgl-csaq-yhsjajghytj-left">
            <div class="content">
                <div class="title">
                    <nav style="padding: 20px 45px 0 45px">
                        <s-header-title style="width: 100%" title="安全生产隐患分类" htype="1"></s-header-title>
                    </nav>
                </div>
                <div class="titleBox">
                    <div class="title titleOne">
                        <nav style="padding: 20px 0 20px 0">
                            <s-header-title2
                                style="width: 100%"
                                title="安全生产隐患分类统计"
                                htype="2"
                            ></s-header-title2>
                        </nav>
                        <el-select class="select" v-model="value" placeholder="请选择" @change="changeYYYY">
                            <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                        <div id="pieEcharts001" style="height: 390px"></div>
                    </div>
                    <div class="title">
                        <nav style="padding: 20px 0 20px 0">
                            <s-header-title2
                                style="width: 100%"
                                title="安全生产隐患排查治理分析"
                                htype="2"
                            ></s-header-title2>
                        </nav>
                        <div id="lineEcharts001" style="height: 390px"></div>
                    </div>
                </div>
                <div class="title" style="position: relative">
                    <nav style="padding: 0px 45px 0 45px">
                        <s-header-title style="width: 100%" title="安全生产隐患处置统计" htype="1"></s-header-title>
                    </nav>
                    <div class="right-filterbar">
                        <div class="tabChange">
                            <div
                                v-for="(item,index) in tabName"
                                @click="changeY(index)"
                                :class="{activeOne:isActiveOne===index}"
                            >
                                {{item}}
                            </div>
                        </div>
                        <div class="time-range-area">
                            <div class="month-range-picker" v-if="isActiveOne === 0">
                                <el-select v-model="monthData" placeholder="选择月" @change="changeMMM">
                                    <el-option
                                        v-for="item in options1"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                                <el-select v-model="monthData1" placeholder="选择月" @change="changeMM">
                                    <el-option
                                        v-for="item in options1"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </div>
                            <div class="year-range-picker" v-if="isActiveOne === 1">
                                <el-select v-model="minsss" placeholder="" @change="change11">
                                    <el-option
                                        v-for="item in options2"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                                <el-select v-model="maxsss" placeholder="" @change="change1111">
                                    <el-option
                                        v-for="item in options2"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class="search-btn" @click="clickBtn">搜索</div>
                    </div>
                </div>
                <div class="titleBox">
                    <div class="title">
                        <nav style="padding: 20px 0 20px 0">
                            <s-header-title2 style="width: 100%" title="隐患处理率分析" htype="2"></s-header-title2>
                        </nav>
                        <div class="cityName">
                            <div
                                v-for="(item,index) in cityName"
                                @click="changeA(index,item)"
                                :class="{active:isActive===index}"
                            >
                                {{item}}
                            </div>
                        </div>
                        <div id="lineEcharts002" style="height: 390px"></div>
                    </div>
                    <div class="title">
                        <div class="tabs-wrap">
                            <div
                                class="tab-item"
                                :class="czTabActive === 0 ? 'tab-item-active' : ''"
                                @click="togglrCzTab(0)"
                            >
                                隐患处置率排名
                            </div>
                            <div
                                class="tab-item"
                                :class="czTabActive === 1 ? 'tab-item-active' : ''"
                                @click="togglrCzTab(1)"
                            >
                                平均处置时长核算
                            </div>
                        </div>
                        <div v-if="czTabActive === 0" id="barEcharts002" style="height: 450px"></div>
                        <div v-if="czTabActive === 1" id="barEcharts003" style="height: 450px"></div>
                    </div>
                </div>
                <div class="titleBoxOne">
                    <div class="title">
                        <nav style="padding: 20px 0 20px 0">
                            <s-header-title2 style="width: 100%" title="行业处置率排名" htype="2"></s-header-title2>
                        </nav>
                        <div class="thNameOne">
                            <div v-for="item in thName">{{item}}</div>
                        </div>
                        <div class="tbody">
                            <div class="czBox" v-for="item in czlList">
                                <div class="sort">{{item.sort}}</div>
                                <div class="nameTitle">{{item.name}}</div>
                                <div class="lastTitle">{{item.value}}{{item.dw}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="title">
                        <nav style="padding: 20px 0 20px 0">
                            <s-header-title2 style="width: 100%" title="行业处置时长排名" htype="2"></s-header-title2>
                        </nav>
                        <div class="thNameOne">
                            <div v-for="item in thNameOne">{{item}}</div>
                        </div>
                        <div class="tbody">
                            <div class="czBox" v-for="item in czscList">
                                <div class="sort">{{item.sort}}</div>
                                <div class="nameTitle">{{item.name}}</div>
                                <div class="lastTitle">{{item.value}}{{item.dw}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#shgl-csaq-yhsjajghytj-left",
        data: {
            cityName: ["金东区", "婺城区", "武义县", "义乌市"],
            isActive: 0,
            isActiveOne: 0,
            czTabActive: 0,
            tabName: ["月度", "年度"],
            thName: ["排名", "行业名称", "处置率"],
            thNameOne: ["排名", "行业名称", "平均处置时长"],
            monthData: "1",
            monthData1: "10",
            options1: [
                {
                    value: "1",
                    label: "2022-01",
                },
                {
                    value: "2",
                    label: "2022-02",
                },
                {
                    value: "3",
                    label: "2022-03",
                },
                {
                    value: "4",
                    label: "2022-04",
                },
                {
                    value: "5",
                    label: "2022-05",
                },
                {
                    value: "6",
                    label: "2022-06",
                },
                {
                    value: "7",
                    label: "2022-07",
                },
                {
                    value: "8",
                    label: "2022-08",
                },
                {
                    value: "9",
                    label: "2022-09",
                },
                {
                    value: "10",
                    label: "2022-10",
                },
            ],
            minsss: "2014",
            maxsss: "2022",
            options2: [
                {
                    value: "2014",
                    label: "2014",
                },
                {
                    value: "2015",
                    label: "2015",
                },
                {
                    value: "2016",
                    label: "2016",
                },
                {
                    value: "2017",
                    label: "2017",
                },
                {
                    value: "2018",
                    label: "2018",
                },
                {
                    value: "2019",
                    label: "2019",
                },
                {
                    value: "2020",
                    label: "2020",
                },
                {
                    value: "2021",
                    label: "2021",
                },
                {
                    value: "2022",
                    label: "2022",
                },
            ],
            options: [
                {
                    value: "10",
                    label: "2022-10",
                },
                {
                    value: "9",
                    label: "2022-09",
                },
                {
                    value: "8",
                    label: "2022-08",
                },
                {
                    value: "7",
                    label: "2022-07",
                },
                {
                    value: "6",
                    label: "2022-06",
                },
                {
                    value: "5",
                    label: "2022-05",
                },
                {
                    value: "4",
                    label: "2022-04",
                },
                {
                    value: "3",
                    label: "2022-03",
                },
                {
                    value: "2",
                    label: "2022-02",
                },
                {
                    value: "1",
                    label: "2022-01",
                },
            ],

            value: "10",
            czlList: [],
            czscList: [],
            area: "金东区",
            time: "月度",
            monthRangeData: "",
            yearStart: "2014",
            yearEnd: "",
            value111111: "",
            type11111: "隐患处置",
            maxss: "10",
            minss: "1",
        },
        mounted() {
            this.yearEnd = new Date().getFullYear().toString();
            this.monthRangeData = this.timeDefault;
            this.initFun();
            this.initMap();
            this.openIframe();
            this.openIframe1();
        },
        computed: {
            // 默认时间
            timeDefault() {
                let date = new Date();
                // 通过时间戳计算
                let defalutStartTime = date.getTime() - 7 * 24 * 3600 * 1000; // 转化为时间戳
                let defalutEndTime = date.getTime();
                let startDateNs = new Date(defalutStartTime);
                let endDateNs = new Date(defalutEndTime);
                // 月，日 不够10补0
                defalutStartTime =
                    startDateNs.getFullYear() +
                    "-" +
                    1 +
                    "-" +
                    (startDateNs.getDate() >= 10 ? startDateNs.getDate() : "0" + startDateNs.getDate());
                defalutEndTime =
                    endDateNs.getFullYear() +
                    "-" +
                    (endDateNs.getMonth() + 1 >= 10 ? endDateNs.getMonth() + 1 : "0" + (endDateNs.getMonth() + 1)) +
                    "-" +
                    (endDateNs.getDate() >= 10 ? endDateNs.getDate() : "0" + endDateNs.getDate());
                return [defalutStartTime, defalutEndTime];
            },
        },
        methods: {
            changeYYYY(item) {
                $api("shgl_yhsjajghytj_yhsjajghytj001", { type: item }).then((res) => {
                    this.getEcharts01("pieEcharts001", res);
                });
            },
            clickBtn() {
                this.flag = true;
                if (this.isActiveOne === 0) {
                    this.changeMMM(this.minss);
                    this.changeMM(this.maxss);
                } else {
                    this.change11(this.minsss);
                    this.change1111(this.maxsss);
                }
            },
            change11(item) {
                this.minsss = item;
                if (this.flag === true) {
                    $api("shgl_yhsjajghytj_yhsjajghytj0019", {
                        type: "年",
                        type1: this.type11111,
                        min: this.minsss,
                        max: this.maxsss,
                    }).then((res) => {
                        this.getEcharts04("barEcharts002", res);
                    });
                }
                this.flag = false;
            },
            change1111(item) {
                this.maxsss = item;
                if (this.flag === true) {
                    $api("shgl_yhsjajghytj_yhsjajghytj0019", {
                        type: "年",
                        type1: this.type11111,
                        min: this.minsss,
                        max: this.maxsss,
                    }).then((res) => {
                        this.getEcharts04("barEcharts002", res);
                    });
                }
                this.flag = false;
            },
            changeMM(item) {
                this.maxss = item;
                if (this.flag === true) {
                    $api("shgl_yhsjajghytj_yhsjajghytj0019", {
                        type: "月",
                        type1: this.type11111,
                        min: this.minss,
                        max: this.maxss,
                    }).then((res) => {
                        this.getEcharts04("barEcharts002", res);
                    });
                }
                this.flag = false;
            },
            changeMMM(item) {
                this.minss = item;
                if (this.flag === true) {
                    $api("shgl_yhsjajghytj_yhsjajghytj0019", {
                        type: "月",
                        type1: this.type11111,
                        min: this.minss,
                        max: this.maxss,
                    }).then((res) => {
                        this.getEcharts04("barEcharts002", res);
                    });
                }
                this.flag = false;
            },

            togglrCzTab(i) {
                this.czTabActive = i;
                if (this.czTabActive === 0) {
                    this.type11111 = "隐患处置";
                    $api("shgl_yhsjajghytj_yhsjajghytj0019", {
                        type: "月",
                        type1: "隐患处置",
                        min: "1",
                        max: "10",
                    }).then((res) => {
                        this.getEcharts04("barEcharts002", res);
                    });
                } else {
                    this.type11111 = "平均处置";

                    $api("shgl_yhsjajghytj_yhsjajghytj0019", {
                        type: "月",
                        type1: "平均处置",
                        min: "1",
                        max: "10",
                    }).then((res) => {
                        this.getEcharts05("barEcharts003", res);
                    });
                }
            },
            changeA(index, item) {
                this.isActive = index;
                this.area = item;
                $api("shgl_yhsjajghytj_yhsjajghytj003", {
                    type: item,
                }).then((res) => {
                    this.getEcharts03("lineEcharts002", res);
                });
            },
            changeY(index) {
                this.isActiveOne = index;
            },
            initFun() {
                $api("shgl_yhsjajghytj_yhsjajghytj001", { type: "10" }).then((res) => {
                    this.getEcharts01("pieEcharts001", res);
                });

                // $api("shgl_yhsjajghytj_yhsjajghytj002").then((res) => {
                //  this.getEcharts02("lineEcharts001", res);
                // });
                $get("shgl/yhsjajghytj/yhsjajghytj002").then((res) => {
                    this.getEcharts02("lineEcharts001", res);
                });
                $api("shgl_yhsjajghytj_yhsjajghytj003", {
                    type: "金东区",
                }).then((res) => {
                    this.getEcharts03("lineEcharts002", res);
                });
                $api("shgl_yhsjajghytj_yhsjajghytj0019", {
                    type: "月",
                    type1: "隐患处置",
                    min: "1",
                    max: "10",
                }).then((res) => {
                    this.getEcharts04("barEcharts002", res);
                });
                // $get("shgl/yhsjajghytj/yhsjajghytj004").then((res) => {
                //     this.getEcharts04("barEcharts002", res);
                // });
                // $api("shgl_yhsjajghytj_yhsjajghytj006").then((res) => {
                //  this.czlList = res;
                // });
                $get("shgl/yhsjajghytj/yhsjajghytj006").then((res) => {
                    this.czlList = res;
                });
                //$api("shgl_yhsjajghytj_yhsjajghytj007").then((res) => {
                //  this.czscList = res;
                //});
                $get("shgl/yhsjajghytj/yhsjajghytj007").then((res) => {
                    this.czscList = res;
                });
            },
            getEcharts01(id, echartsData) {
                const myChartsRun = echarts.init(document.getElementById(id));

                let option = {
                    tooltip: {
                        trigger: "item",
                        backgroundColor: "rgba(50,50,50,0.7)",
                        formatter: "{b} : {c} ({d}%)",
                        borderColor: "rgba(50,50,50,0.7)",
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    legend: {
                        orient: "vertical",
                        // right: "right",
                        // left: "left",
                        bottom: "50px",
                        right: "90px",
                        icon: "square",
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    series: [
                        {
                            type: "pie",
                            radius: "80%",
                            label: {
                                show: false,
                            },
                            data: echartsData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: "rgba(0, 0, 0, 0.5)",
                                },
                            },
                        },
                    ],
                };
                myChartsRun.setOption(option);
                tools.loopShowTooltip(myChartsRun, option, {
                    loopSeries: true,
                }); //轮播
            },
            getEcharts02(id, echartsData) {
                const myChartsRun = echarts.init(document.getElementById(id));
                let xData = echartsData.map((item) => {
                    return item.name;
                });
                let yData = echartsData.map((item) => {
                    return item.value;
                });
                let yData1 = echartsData.map((item) => {
                    return item.value1;
                });
                let yData2 = echartsData.map((item) => {
                    return item.value2;
                });
                let option = {
                    tooltip: {
                        backgroundColor: "rgba(50,50,50,0.7)",
                        borderColor: "rgba(50,50,50,0.7)",
                        trigger: "axis",
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    legend: {
                        data: ["已整改", "已销案", "未整改"],
                        icon: "circle",
                        right: 10,
                        itemWidht: 25,
                        itemGap: 30,
                        itemHeight: 25,
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    grid: {
                        left: "3%",
                        right: "0%",
                        bottom: "3%",
                        containLabel: true,
                    },

                    xAxis: {
                        type: "category",
                        // boundaryGap: false,
                        data: xData,
                        axisLabel: {
                            textStyle: {
                                fontSize: 30,
                                color: "#fff",
                            },
                        },
                    },
                    yAxis: {
                        name: "单位: 起",
                        nameTextStyle: {
                            color: "#fff",
                            fontSize: 30,
                            align: "right",
                        },
                        type: "value",
                        min: 0,
                        max: 5000,
                        interval: 1000,
                        axisLabel: {
                            textStyle: {
                                fontSize: 30,
                                color: "#fff",
                            },
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: "#ccc",
                            },
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: "#213c56 ",
                            },
                        },
                    },
                    series: [
                        {
                            smooth: true,
                            name: "已整改",
                            type: "line",
                            color: "#2391ff",
                            data: yData,
                            symbolSize: 15,
                        },
                        {
                            smooth: true,
                            name: "已销案",
                            type: "line",
                            color: "#5ad8a6",
                            symbolSize: 15,
                            data: yData1,
                        },
                        {
                            smooth: true,
                            name: "未整改",
                            type: "line",
                            color: "#ffc328",
                            symbolSize: 15,
                            data: yData2,
                        },
                    ],
                };
                myChartsRun.setOption(option);
                tools.loopShowTooltip(myChartsRun, option, {
                    loopSeries: true,
                }); //轮播
            },
            getEcharts03(id, echartsData) {
                const myChartsRun = echarts.init(document.getElementById(id));
                let xData = echartsData.map((item) => {
                    return item.name;
                });
                let yData = echartsData.map((item) => {
                    return item.value;
                });
                let yData1 = echartsData.map((item) => {
                    return item.value1;
                });
                let yData2 = echartsData.map((item) => {
                    return item.value2;
                });
                let option = {
                    tooltip: {
                        backgroundColor: "rgba(50,50,50,0.7)",
                        borderColor: "rgba(50,50,50,0.7)",
                        trigger: "axis",
                        icon: "none",
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    // legend: {
                    //     data: ["已整改", "已销案", "未整改"],
                    //     icon: "circle",
                    //     right: 10,
                    //     itemWidht: 25,
                    //     itemGap: 30,
                    //     itemHeight: 25,
                    //     textStyle: {
                    //         fontSize: 30,
                    //         color: "#fff",
                    //     },
                    // },
                    grid: {
                        left: "3%",
                        right: "0%",
                        bottom: "3%",
                        containLabel: true,
                    },
                    xAxis: {
                        type: "category",
                        // boundaryGap: false,
                        data: xData,
                        axisLabel: {
                            textStyle: {
                                fontSize: 30,
                                color: "#fff",
                            },
                        },
                    },
                    yAxis: {
                        type: "value",

                        axisLabel: {
                            textStyle: {
                                fontSize: 30,
                                color: "#fff",
                            },
                        },
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: "#ccc",
                            },
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: "#213c56 ",
                            },
                        },
                    },
                    series: [
                        {
                            name: this.area,

                            smooth: true,
                            type: "line",
                            color: "#105cac",
                            data: yData,
                            symbolSize: 15,
                        },
                    ],
                };
                myChartsRun.setOption(option);
                tools.loopShowTooltip(myChartsRun, option, {
                    loopSeries: true,
                }); //轮播
            },
            getEcharts04(id, echartsData) {
                const myChartsRun = echarts.init(document.getElementById(id));
                let xData = echartsData.map((item) => {
                    return item.name;
                });
                let yData = echartsData.map((item) => {
                    return item.value;
                });
                let yData1 = echartsData.map((item) => {
                    return item.value1;
                });

                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "#000000",
                        textStyle: {
                            color: "white",
                            fontSize: "27",
                        },
                        axisPointer: {
                            type: "shadow",
                        },
                    },
                    xAxis: {
                        type: "category",
                        data: xData,
                        axisLabel: {
                            textStyle: {
                                color: "#FFFFFF",
                                fontSize: 30,
                            },
                        },
                    },
                    yAxis: [
                        {
                            type: "value",
                            position: "left",
                            nameTextStyle: {
                                fontSize: 24,
                            },

                            axisLabel: {
                                textStyle: {
                                    fontSize: 30,
                                    color: "#fff",
                                },
                            },
                        },
                        {
                            name: "单位：%",
                            type: "value",
                            position: "right",

                            nameTextStyle: {
                                fontSize: 24,
                                color: "#fff",
                            },

                            axisLabel: {
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 30,
                                },
                            },
                        },
                    ],
                    legend: {
                        itemGap: 32,
                        textStyle: {
                            fontSize: 27,
                            color: "white",
                        },
                    },

                    series: [
                        {
                            type: "bar",
                            name: "排名",
                            color: "deepskyblue",
                            data: yData,
                            yAxisIndex: 0,
                        },
                        {
                            type: "line",
                            name: "处置率",
                            symbol: "circle",
                            smooth: true,
                            symbolSize: 12,
                            data: yData1,
                            lineStyle: {
                                width: 3,
                            },
                            yAxisIndex: 1,
                        },
                    ],
                };

                myChartsRun.setOption(option);
                tools.loopShowTooltip(myChartsRun, option, {
                    loopSeries: true,
                }); //轮播
            },
            getEcharts05(id, echartsData) {
                const myChartsRun = echarts.init(document.getElementById(id));
                let xData = echartsData.map((item) => {
                    return item.name;
                });
                let yData = echartsData.map((item) => {
                    return item.value;
                });
                let yData1 = echartsData.map((item) => {
                    return item.value1;
                });
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "#000000",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            color: "white",
                            fontSize: "27",
                        },
                    },
                    xAxis: {
                        type: "category",
                        data: xData,

                        axisLabel: {
                            textStyle: {
                                color: "#fff",
                                fontSize: 30,
                            },
                        },
                    },
                    yAxis: [
                        {
                            type: "value",
                            position: "left",
                            nameTextStyle: {
                                fontSize: 24,
                            },

                            axisLabel: {
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 30,
                                },
                            },
                        },
                        {
                            name: "单位：h",
                            type: "value",
                            position: "right",
                            nameTextStyle: {
                                fontSize: 30,
                                color: "white",
                            },

                            axisLabel: {
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 30,
                                },
                            },
                        },
                    ],
                    legend: {
                        itemGap: 32,
                        textStyle: {
                            fontSize: 27,
                            color: "white",
                        },
                    },

                    series: [
                        {
                            type: "bar",
                            name: "排名",
                            color: "deepskyblue",
                            data: yData,
                            yAxisIndex: 0,
                        },
                        {
                            type: "line",
                            name: "平均处置时长",
                            symbol: "circle",
                            symbolSize: 12,
                            data: yData1,
                            yAxisIndex: 1,
                        },
                    ],
                };

                myChartsRun.setOption(option);
                tools.loopShowTooltip(myChartsRun, option, {
                    loopSeries: true,
                }); //轮播
            },

            initMap() {
                top.document.getElementById("map").contentWindow.Work.change3D(7);
                this.flyTo();
                // this.addPoint();
                this.add3DText();
            },
            //飞入
            flyTo() {
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "flyto", //功能名称
                        flyData: {
                            center: [119.95478050597587, 29.01613226366889],
                            zoom: 10.5,
                            pitch: 40,
                            bearing: 0,
                        },
                    })
                );
            },

            addPoint() {
                let res = [
                    {
                        title: "浦江县",
                        gps_x: "119.94315399169922",
                        gps_y: "29.5630503845215",
                    },
                    {
                        title: "兰溪市",
                        gps_x: "119.46214447021484",
                        gps_y: "29.31345558166504",
                    },
                    {
                        title: "婺城区",
                        gps_x: "119.5569204711914",
                        gps_y: "29.00677101135254",
                    },
                    {
                        title: "金义新区",
                        gps_x: "119.8483056640625",
                        gps_y: "29.188559951782227",
                    },
                    {
                        title: "义乌市",
                        gps_x: "120.08206787109375",
                        gps_y: "29.322123641967773",
                    },
                    {
                        title: "武义县",
                        gps_x: "119.7269204711914",
                        gps_y: "28.79677101135254",
                    },
                    {
                        title: "永康市",
                        gps_x: "120.1469204711914",
                        gps_y: "28.97677101135254",
                    },
                    {
                        title: "东阳市",
                        gps_x: "120.4169204711914",
                        gps_y: "29.24677101135254",
                    },
                    {
                        title: "磐安县",
                        gps_x: "120.6299204711914",
                        gps_y: "29.06677101135254",
                    },
                ];
                let arr = res.map((item) => {
                    return {
                        data: {
                            title: item.title + "区域详细数据项",
                            key: ["当前区域隐患总量", "一般隐患", "重大隐患", "同比增长率", "同比增长率"],
                            value: ["6776起", "999起", "77起", "****%↑", "-1.4%↓"],
                        },
                        point: item.gps_x + "," + item.gps_y,
                    };
                });
                console.log(arr);
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "pointLoad",
                        pointType: "digital-yellow", // 点位类型（图标名称）
                        pointId: "point1", // 点位唯一id
                        setClick: true,
                        pointData: arr,
                        imageConfig: { iconSize: 1 },
                        popup: {
                            offset: [50, -100],
                        },
                    })
                );
            },

            //清除点位
            rmPoint() {
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "rmPoint",
                        pointId: "",
                    })
                );
            },

            // 加载3D文字方法
            add3DText() {
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "3Dtext", //3D文字功能
                        textData: [
                            // pos文字的位置  //text 展示的文字
                            {
                                pos: [119.94315399169922, 29.5630503845215, 11000],
                                text: "浦江县",
                            },
                            {
                                pos: [119.46214447021484, 29.31345558166504, 11000],
                                text: "兰溪市",
                            },
                            {
                                pos: [119.5569204711914, 29.00677101135254, 11000],
                                text: "婺城区",
                            },
                            {
                                pos: [119.8483056640625, 29.188559951782227, 11000],
                                text: "金义新区",
                            },
                            {
                                pos: [120.08206787109375, 29.322123641967773, 11000],
                                text: "义乌市",
                            },
                            {
                                pos: [119.7269204711914, 28.79677101135254, 11000],
                                text: "武义县",
                            },
                            {
                                pos: [120.1469204711914, 28.97677101135254, 11000],
                                text: "永康市",
                            },
                            {
                                pos: [120.4169204711914, 29.24677101135254, 11000],
                                text: "东阳市",
                            },
                            {
                                pos: [120.6299204711914, 29.06677101135254, 11000],
                                text: "磐安县",
                            },
                        ],
                        textSize: 40,
                        id: "text1",
                        // zoomShow: true,
                        color: [255, 255, 255, 1],
                    })
                );
            },

            //清除3D文字方法
            rm3DText() {
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "rm3Dtext", //清除柱状体
                    })
                );
            },

            openIframe() {
                let Iframe = {
                    type: "openIframe",
                    name: "IframeName",
                    src: baseURL.url + "/static/citybrain/shgl/commont/shgl-yhsj-search.html",
                    left: "4730px",
                    top: "230px",
                    width: "800px",
                    height: "200px",
                    zIndex: "10",
                    argument: {
                        status: "openIframe",
                    },
                };
                window.parent.postMessage(JSON.stringify(Iframe), "*");
            },
            openIframe1() {
                let Iframe = {
                    type: "openIframe",
                    name: "IframeName1",
                    src: baseURL.url + "/static/citybrain/shgl/commont/shgl-yhsj-select.html",
                    left: "2160px",
                    top: "230px",
                    width: "250px",
                    height: "200px",
                    zIndex: "10",
                    argument: {
                        status: "openIframe1",
                    },
                };
                window.parent.postMessage(JSON.stringify(Iframe), "*");
            },
        },
        destroyed() {
            this.rmPoint();
            this.rm3DText();
        },
    });
</script>
