<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>表格的营商环境</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/Vue/vue.js"></script>

    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <style>
      /* 教育服务弹窗 */
      .container {
        width: 2510px;
        height: 1800px;
        background-color: #031827;
        box-shadow: -3px 2px 35px 0px #000000;
        border: 1px solid #359cf8;
        border-radius: 60px;
      }

      .container .head {
        width: 100%;
        height: 100px;
        line-height: 100px;
        background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%),
          linear-gradient(#ffffff, #ffffff);
        background-blend-mode: normal, normal;
        padding: 10px 50px;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        justify-content: space-between;
        border-top-left-radius: 60px;
        border-top-right-radius: 60px;
      }

      .head span {
        font-size: 48px !important;
        font-weight: 500;
        color: #fff;
        font-weight: bold;
      }

      .head .img {
        display: inline-block;
        margin: 20px;
        float: right;
        width: 34px;
        height: 34px;
        background-image: url(/static/citybrain/csdn/img/cstz2-middle/close-hover.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }

      .content {
        width: 100%;
        height: calc(100% - 100px);
        padding: 0 20px 0 20px;
        box-sizing: border-box;
      }
      .row {
        position: relative;
        display: flex;
        width: 100%;
        height: 340px;
      }
      .table001 {
        width: 50%;
      }
      #chart1,
      #chart2 {
        width: 50%;
        height: 100%;
      }
      /* 表格样式 */
      .el-table {
        width: 95% !important;
        margin: 0 auto;
        color: #d2c3c3;
      }
      .el-table td,
      .el-table th.is-leaf {
        background-color: rgba(3, 23, 56, 0.9);
        font-size: 27px;
      }
      .el-table td,
      .el-table th {
        text-align: center !important;
      }

      .el-table--enable-row-hover .el-table__body tr:hover > td {
        background-color: #031738 !important;
      }
      .el-table__body-wrapper {
        max-height: 238px;
        overflow-y: auto;
      }

      .el-table__body-wrapper::-webkit-scrollbar {
        width: 4px;
        /*滚动条整体样式*/
        height: 4px;
        background-color: #000000;
        /*高宽分别对应横竖滚动条的尺寸*/
      }

      .el-table__body-wrapper::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: #20aeff;
        height: 8px;
      }

      /* 切换 */
      .tabs {
        width: 14%;
    height: 50px;
    display: flex;
    justify-content: space-evenly;
    /* border-bottom: 1px #917777 solid; */
    cursor: pointer;
    position: absolute;
    top: -66px;
      }
      .tab_item {
        line-height: 50px;
        text-align: center;
        font-size: 30px;
        color: #fff;
      }
      .tab_active {
        color: rgb(37, 97, 165);
        border-bottom: 2px rgb(37, 97, 165) solid;
      }
    </style>
  </head>
  <body>
    <div id="app" class="container">
      <div class="head">
        <span>营商环境</span>
        <div class="img" @click="closeDialog('table_yshj_diaog')"></div>
      </div>
      <div class="content">
        <nav class="s-m-b-5">
          <s-header-title-2 htype="1" title="全省数据对比"></s-header-title-2>
        </nav>
        <div class="row">
          <div class="table001">
            <el-table
              :data="table1"
              style="width: 100%; border: 3px solid #04457e"
              :default-sort="{prop: 'date', order: 'descending'}"
            >
              <el-table-column prop="name" label="区域"> </el-table-column>
              <el-table-column prop="hs" label="市场主体户数" sortable>
              </el-table-column>
              <el-table-column prop="zczb" sortable label="市场主体注册资本">
              </el-table-column>
            </el-table>
          </div>
          <div id="chart1"></div>
        </div>
        <nav class="s-m-b-5">
          <s-header-title-2 htype="1" title="全市数据对比"></s-header-title-2>
        </nav>
        <div class="row">
          <div class="table001">
            <el-table
              :data="table2"
              style="width: 100%; border: 3px solid #04457e"
              :default-sort="{prop: 'date', order: 'descending'}"
            >
              <el-table-column prop="name" label="区域"> </el-table-column>
              <el-table-column prop="hs" label="市场主体户数" sortable>
              </el-table-column>
              <el-table-column prop="zczb" sortable label="市场主体注册资本">
              </el-table-column>
            </el-table>
          </div>
          <div id="chart2"></div>
        </div>
        <nav class="s-m-b-5">
          <s-header-title-2 htype="1" title="内资来源追踪" :click-flag="true" @click="openDiaog"></s-header-title-2>
        </nav>
        <div class="row">
          <div class="table001">
            <div class="tabs">
              <div
                class="tab_item"
                v-for="(item,index) in tabList"
                @click="changeTab('nzly',index,item)"
                :class="tabIndex==index?'tab_active':''"
              >
                {{item.name}}
              </div>
            </div>
            <el-table
              :data="table3"
              style="width: 100%; border: 3px solid #04457e"
              :default-sort="{prop: 'date', order: 'descending'}"
            >
              <el-table-column label="序号" type="index" width="100px">
              </el-table-column>
              <el-table-column prop="value" label="投资金额数量" sortable>
              </el-table-column>
              <el-table-column prop="name" label="资金来源省份">
              </el-table-column>
            </el-table>
          </div>
          <div class="table001">
            <el-table
              :data="table4"
              style="width: 100%; border: 3px solid #04457e"
              :default-sort="{prop: 'date', order: 'descending'}"
            >
              <el-table-column label="序号" type="index" width="100px">
              </el-table-column>
              <el-table-column prop="value" label="投资金额数量" sortable>
              </el-table-column>
              <el-table-column prop="name" label="外资来源国家">
              </el-table-column>
            </el-table>
          </div>
        </div>
        <nav class="s-m-b-5">
          <s-header-title-2 htype="1" title="投资者数量分布"></s-header-title-2>
        </nav>
        <div class="row">
          <div class="table001" style="width: 100%">
            <div class="tabs">
              <div
                class="tab_item"
                v-for="(item,index) in tabList1"
                @click="changeTab('tzzsl',index,item)"
                :class="tabIndex1==index?'tab_active':''"
              >
                {{item.name}}
              </div>
            </div>
            <el-table
              :data="table5"
              style="width: 100%; border: 3px solid #04457e"
              :default-sort="{prop: 'date', order: 'descending'}"
            >
              <el-table-column
                label="序号"
                type="index"
                width="100px"
              ></el-table-column>
              <el-table-column prop="name" label="地区" >
              </el-table-column>
              <el-table-column prop="value" label="投资者数量" sortable>
              </el-table-column>
              <el-table-column prop="tb" label="同比" sortable></el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
      new Vue({
        el: "#app",
        data: {
          table1: [],
          table2: [],
          table3: [],
          table4: [],
          table5: [],
          tabIndex: 0,
          tabIndex1: 0,
          tabList: [
            {
              name:"省外投资",
              url:"/shgl/dialog_table_yshj05",
        
            },
            {
              name:"省内投资",
              url:"/shgl/dialog_table_yshj04",
            }
          ],
          tabList1: [
            {
              name:"省外投资",
              url:"/shgl/dialog_table_yshj08",
        
            },
            {
              name:"省内投资",
              url:"/shgl/dialog_table_yshj07",
            }
          ],
        },
        mounted() {
          this.initApi();
        },
        methods: {
          changeTab(type,index,item) {
            if(type==='nzly'){
              this.tabIndex = index;
              this.getData(item.url)
            }else if(type==='tzzsl'){
              this.tabIndex1 = index;
              this.getData1(item.url)
            }
           
          },
          openDiaog(){
            let diaog = {
                    type: 'openIframe',
                    name: "yshj_dialog",
                    src: baseURL.url + '/static/citybrain/scjg/commont/scjgyzt/yshj_dialog.html',
                    left:"34%" ,
                    top: "20%",
                    width: "2210px",
                    height:"1480px",
                    zIndex:"999",
                    argument: {
                        status:""
                    }
                }
                window.parent.postMessage(JSON.stringify(diaog), '*')
            },

          getData(url){
            console.log(url)
            $get(url).then((res) => {
              this.table3 = res;
            });
          },
          getData1(url){
            $get(url).then((res) => {
              this.table5 = res;
            });
          },
          initApi() {
            $get("/shgl/dialog_table_yshj").then((res) => {
              this.table1 = res;
            });
            $get("/shgl/dialog_table_yshj01").then((res) => {
              this.getBar("chart1", res);
            });
            $get("/shgl/dialog_table_yshj02").then((res) => {
              this.table2 = res;
            });
            $get("/shgl/dialog_table_yshj03").then((res) => {
              this.getBar("chart2", res);
            });
            
            
            $get("/shgl/dialog_table_yshj06").then((res) => {
              this.table4 = res;
            });
            this.getData("/shgl/dialog_table_yshj05")
            this.getData1("/shgl/dialog_table_yshj08")
          },
          closeDialog() {
            top.commonObj.funCloseIframe({
              name: 'table_yshj_dialog',
            });
          },
          getBar(id, echartsData) {
            const myCharts = echarts.init(document.getElementById(id));
            let xdata = echartsData.map((v) => v.name);
            let y1 = echartsData.map((v) => v.value);
            let y2 = echartsData.map((v) => v.value1);
            let titleArray = ["户数", "注册资本"];

            let option = {
              grid: {
                top: "20%",
                right: "5%",
                left: "8%",
                bottom: "0%",
                containLabel: true,
              },
              tooltip: {
                show: true,
                trigger: "axis",
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "28",
                },
              },
              legend: {
                show: true,
                x: "center",
                y: "0",
                itemWidth: 25,
                itemHeight: 25,
                itemGap: 40,
                textStyle: {
                  color: "#fff",
                  fontSize: 26,
                },
                top: "5%",
                left: "center",
                data: titleArray,
              },
              xAxis: [
                {
                  type: "category",
                  axisLabel: {
                    color: "#fff",
                    fontSize: 28,
                    // rotate: 45,
                  },
                  axisLine: {
                    show: true,
                    lineStyle: {
                      color: "#bbb",
                    },
                  },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      color: "#195384",
                    },
                  },
                },
              ],
              yAxis: [
                {
                  type: "value",
                  axisLabel: {
                    formatter: "{value}",
                    textStyle: {
                      color: "#fff",
                      fontSize: 28,
                    },
                  },
                  axisLine: {
                    lineStyle: {
                      color: "#fff",
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  splitLine: {
                    show: true,
                    lineStyle: {
                      color: "#11366e",
                    },
                  },
                },
              ],
              series: [
                {
                  name: titleArray[0],
                  type: "bar",
                  data: y1,
                  barWidth: 35,
                  itemStyle: {
                    normal: {
                      color: "#5894ff",
                    },
                  },
                },
                {
                  name: titleArray[1],
                  type: "bar",
                  data: y2,
                  barWidth: 35,
                  itemStyle: {
                    normal: {
                      color: "#85d0dc",
                    },
                  },
                },
              ],
            };

            myCharts.setOption(option);
            myCharts.getZr().on("mousemove", (param) => {
              myCharts.getZr().setCursorStyle("default");
            });
          },
        },
      });
    </script>
  </body>
</html>
