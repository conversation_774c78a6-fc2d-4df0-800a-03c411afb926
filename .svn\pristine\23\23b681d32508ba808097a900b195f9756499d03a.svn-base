[v-cloak] {
    display: none;
  }
  html,body,ul,p{
    padding:0;
    margin:0;
    list-style: none;
  }
  .container{
    width:1050px;
    height:1930px;
    background: url("/img/right-bg.png") no-repeat;
    background-size: 100% 100%;
    border-radius: 10px;
    padding: 30px 40px;
    box-sizing: border-box;
  }
  .title{
      font-size: 28px;
      margin: 10px;
      color: #fff;
  }
  .tj-con{
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
  .tj-con li{
    width: 30%;
    height: 90px;
    line-height: 45px;
    background-color: rgba(95, 168, 224, 1);
    color: rgba(255, 255, 255, 1);
    font-size: 28px;
    text-align: center;
    list-style: none;
    /* margin-left: 20px; */
  }
  .top-con{
      width: 100%;
      display: flex;
      justify-content: space-between;
  }
  .btn{
    width: 290px;
    height: 50px;
    line-height: 50px;
    /* line-height: 20px; */
    border-radius: 2px 2px 2px 2px;
    background-color: rgba(86, 119, 252, 1);
    color: #fff;
    font-size: 28px;
    text-align: center;
    cursor: pointer;
  }
#jtjt-chart{
    width: 100%;
    height: 520px;
   
}
#glys-chart{
    width: 100%;
    height: 520px;
}






   /* 表格 */
.table {
    width: 100%;
    height: 400px;
    padding: 10px;
    box-sizing: border-box;
    overflow-y: auto;
  }
  
  .table .th {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-style: italic;
    font-weight: 700;
    font-size:28px;
    line-height: 60px;
    background: #00396f;
    color: #FFFFFF;
  }
  
  .table .th_td {
    letter-spacing: 0px;
    text-align: center;
    flex: 0.25;
  }
  
  .table .tbody {
    width: 100%;
    height: calc(100% - 80px);
    overflow: hidden;
  }
  
  .table .tbody:hover {
    overflow-y: auto;
  }
  
  .table .tbody::-webkit-scrollbar {
    width: 4px;
    /*滚动条整体样式*/
    height: 4px;
    /*高宽分别对应横竖滚动条的尺寸*/
  }
  
  .table .tbody::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #20aeff;
    height: 8px;
  }
  .table .tr:nth-child(2n) {
    background: rgb(0 57 111 / 30%);
  }

  .table .tr:nth-child(2n+1) {
    /* background: #035b86; */
  }

  .table .tr {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;
    line-height: 60px;
    font-size: 28px;
    color: #FFFFFF;
    cursor: pointer;
  }
  
  /* .table .tr:nth-child(2n) {
    background: #00396f;
  }
  
  .table .tr:nth-child(2n+1) {
    background: #035b86;
  }
   */
  .table .tr:hover {
    background-color: #6990b6;
  }
  
  .table .tr_td {
    letter-spacing: 0px;
    text-align: center;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
 