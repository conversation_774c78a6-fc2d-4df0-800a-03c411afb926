<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>城乡就业</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css">
    <link rel="stylesheet" href="/static/citybrain/ggfw/css/cxjy.css">
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script> <!-- 轮播toolTip -->
</head>
<style>
    * {
        padding: 0;
        margin: 0;
    }

    .ztqs {
        position: relative;
    }

    .el-input__inner {
        background-color: #041230;
        border: 1px solid #686f7d;
        color: #fff;
        font-size: 24px;
        border-radius: 10%;
        height: 50px;
    }

    .el-range-editor.is-active,
    .el-range-editor.is-active:hover,
    .el-select .el-input__inner:focus,
    .el-select .el-input.is-focus .el-input__inner {
        border-color: #fff;
    }

    .el-select-dropdown__list {
        background-color: #032f46d3;
        color: #fff;
        /* height: 200px; */
        /* width: 300px; */
    }

    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
        background: none;
    }

    .el-select-dropdown__item {
        color: #fff;
        line-height: 60px;
        font-size: 24px;
        height: 60px;
    }

    .el-select-dropdown {
        /* border: 1px solid #2578a6; */
        background-color: #032f46d3;
    }

    .el-select {
        width: 160px;
        position: absolute;
        top: 98px;
        right: 1524px;
        z-index: 1000;
    }

    .el-input__suffix {
        right: 18px;
        font-size: 30px !important;
    }

    .el-picker-panel__body-wrapper {
        font-size: 30px;
    }

    .el-input__icon {
        line-height: 65px !important;
        /* margin-right: 20px; */
    }

    .dimensionSelected {
        position: absolute;
        width: 160px;
        right: 410px;
        top: 20px;
    }

    .ageSelected {
        position: absolute;
        width: 160px;
        right: 138px;
        top: 20px;
    }
</style>

<body>
    <div id="app" class="container" v-cloak>
        <!-- 城乡就业顶部 -->
        <div class="cxjy-top">
            <div class="cxjy_item" v-for="(item,index) in cxjyList" :key="index">
                <p class="s-c-yellow-gradient s-font-50 s-w7">{{item.value}}</p>
                <p class="s-c-yellow-gradient s-font-30">{{item.unit}}</p>
                <p class="s-c-white s-font-30">{{item.name}}</p>
            </div>
        </div>
        <!-- 补贴发放情况 -->
        <div class="btff">
            <nav>
                <s-header-title htype="1" title="补贴发放情况" data-time=""></s-header-title>
            </nav>
            <div class="top-con">
                <li v-for="(item,index) in btffData" :key="index">
                    <span class="title">{{item.name||'-'}}</span>
                    <div class="num-bg"><span class="num">{{item.value||'-'}}</span><span
                            class="unit">{{item.unit}}</span>
                    </div>
                </li>
            </div>
            <div class="bottom-con">
                <!-- 登记失业率统计 -->
                <div class="left-part">
                    <nav>
                        <s-header-title-2 title="登记失业率统计"></s-header-title-2>
                    </nav>
                    <div class="djsy-tab">
                        <li class="djsy-item" v-for="(item,index) in djsyTabList" :key="index"
                            :class="currentDjsyIndex===index?'active':''" @click="changeTab('djsy',index,item)">
                            {{item.name}}
                        </li>
                    </div>
                    <div id="djsy-chart"></div>
                </div>
                <!-- 就业资金支出 -->
                <div class="right-part">
                    <nav>
                        <s-header-title-2 title="就业资金支出"></s-header-title-2>
                    </nav>
                    <div class="jyzjzc-tab" style="width: 40%;position: absolute;right: 0px;">
                        <li class="djsy-item" v-for="(item,index) in jyzjTabList" :key="index"
                            :class="currentJyzjIndex===index?'active':''" @click="changeTab('jyzj',index,item)">
                            {{item.name}}
                        </li>
                    </div>
                    <div id="jyzjzc-chart"></div>
                </div>
            </div>
        </div>
        <!-- 就业困难人员分布 -->
        <div class="jykn">
            <nav>
                <s-header-title htype="1" title="就业困难人员分布" data-time=""></s-header-title>
            </nav>
            <div class="jykn-con">
                <div id="jykn-chart1"></div>
                <div id="jykn-chart2"></div>
                <div id="jykn-chart3"></div>
            </div>
        </div>
        <!-- 城乡就业总体趋势 -->
        <div class="ztqs">
            <nav>
                <s-header-title htype="1" title="城乡就业总体趋势" data-time=""></s-header-title>
            </nav>
            <div class="jykn-con">
                <el-select v-model="value5" @change="changeSelectYears">
                    <el-option v-for="item in options5" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="dimensionValue" class="dimensionSelected" @change="changeDimensionValue">
                    <el-option v-for="item in dimensionOptions" :key="item.value" :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="ageSelectedValue" class="ageSelected" @change="changeAgeSelectedValue">
                    <el-option v-for="item in ageSelectedOptions" :key="item.value" :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
                <div id="cxjy-chart1"></div>
                <div id="cxjy-chart2"></div>
            </div>
        </div>
    </div>
</body>
<script type="module">

    new Vue({
        el: '#app',
        data: {
            nowTime: '',//当前时间
            cxjyList: [],//城乡就业顶部数据统计
            btffData: [],//补贴发放顶部统计数据
            djsyTabList: [],//登记失业统计tab数据
            currentDjsyIndex: 0,//登记失业统计当前下标
            jyzjTabList: [],//就业资金支出tab数据
            currentJyzjIndex: 0,//就业资金支出当前下标
            value5: "2023",
            options5: [
                {
                    value: "2023",
                    label: "2023",
                },
            ],
            dimensionValue: "月度",
            dimensionOptions: [
                {
                    value: "年度",
                    label: "年度",
                },
                {
                    value: "季度",
                    label: "季度",
                },
                {
                    value: "月度",
                    label: "月度",
                },
            ],
            ageSelectedValue: "20-50",
            ageSelectedOptions: [
                {
                    value: "20-50",
                    label: "20-50",
                },
                {
                    value: "20-30",
                    label: "20-30",
                },
                {
                    value: "30-40",
                    label: "30-40",
                },
                {
                    value: "40-50",
                    label: "40-50",
                },
            ],
        },
        methods: {
            //获取当前时间
            getTime() {
                var data = new Date();
                var yesterday = new Date(data.setDate(data.getDate() - 1));
                this.nowTime =
                    yesterday.getFullYear() +
                    "年" +
                    (yesterday.getMonth() + 1) +
                    "月" +
                    yesterday.getDate() +
                    "日";
            },
            //数据初始化
            init() {
                $api("/ggfw_rsfw_rsfw02").then(res => {
                    this.cxjyList = res;
                })
                $api("/ggfw_rsfw_btff01").then((res) => {
                    this.btffData = res;
                });
                $api("/ggfw_rsfw_btff02").then((res) => {
                    this.djsyTabList = res;
                });
                $api("/ggfw_rsfw_btff03").then((res) => {
                    this.jyzjTabList = res;
                });
                $api("/ggfw_rsfw_jykn01").then((res) => {
                    this.getChart01('jykn-chart1', res);
                });
                $api("/ggfw_rsfw_jykn02").then((res) => {
                    this.getChart01('jykn-chart2', res);
                });
                $api("/ggfw_rsfw_jykn03").then((res) => {
                    this.getJyBarCharts('jykn-chart3', res);
                });
                $get("/ggfw/rsfw/cxjy01").then((res) => {
                    this.getChart01('cxjy-chart1', res);
                });
                $get("/ggfw/rsfw/cxjy02").then((res) => {
                    this.getJyBarCharts('cxjy-chart2', res);
                });

                this.getFyjkEchartsData("1", "1");
                this.getJyzjEchartsData("1", "1");
            },
            //切换tab
            changeTab(type, index, data) {
                if (type === 'djsy') {
                    this.currentDjsyIndex = index;
                    this.getFyjkEchartsData(data.value, index + 1);
                } else {
                    this.currentJyzjIndex = index;
                    this.getJyzjEchartsData(data.value, index + 1);
                }
            },
            // 切换选择年份
            changeSelectYears(item) {
            },
            // 维度选择器
            changeDimensionValue(item) {
                $api("/ggfw_rsfw_cxjy02", { type: item }).then((res) => {
                    this.getJyBarCharts('cxjy-chart2', res);
                });
            },
            // 年龄选择
            changeAgeSelectedValue(item) {
                if (this.dimensionValue === "年度") {
                    this.getJyBarCharts('cxjy-chart2', [
                        {
                            name: "2022",
                            value: "4342"
                        },
                        {
                            name: "2021",
                            value: "5512"
                        },
                        {
                            name: "2020",
                            value: "5431"
                        },
                        {
                            name: "2019",
                            value: "5021"
                        },
                    ]);

                } else if ((this.dimensionValue === "季度")) {
                    this.getJyBarCharts('cxjy-chart2', [
                        {
                            name: "一季度",
                            value: "1342"
                        },
                        {
                            name: "二季度",
                            value: "784"
                        },
                        {
                            name: "三季度",
                            value: "1205"
                        },
                        {
                            name: "四季度",
                            value: "1371"
                        },
                    ]);
                } else if ((this.dimensionValue === "月度")) {
                    $api("/ggfw_rsfw_cxjy02", { type: "月度" }).then((res) => {
                        this.getJyBarCharts('cxjy-chart2', res);
                    });
                }
            },
            // 获取登记失业率echarts
            getFyjkEchartsData(type, index) {
                $api("/ggfw_rsfw_djsyl", { type: index }).then((res) => {
                    let data = [];
                    data = res.filter((item) => {
                        return type === item.type;
                    })
                    let unit = "单位：%";
                    switch (index) {
                        case 1:
                            unit = "单位：%";
                            break;
                        case 2:
                            unit = "单位：人";
                            break;
                        case 3:
                            unit = "单位：万元";
                            break;

                        default:
                            break;
                    }
                    this.getDjsychartsShow(data, unit)
                });
            },
            // 获取就业资金echarts
            getJyzjEchartsData(type, index) {
                $api("/ggfw_rsfw_jyzjzc", { type: index }).then((res) => {
                    let data = [];
                    data = res.filter((item) => {
                        return type === item.type;
                    })
                    this.getJyBarCharts('jyzjzc-chart', data);
                });
            },
            //绘制登记失业率折线图
            getDjsychartsShow(data, unit) {
                echarts.init(document.getElementById('djsy-chart')).dispose();
                const myChartsRun = echarts.init(document.getElementById("djsy-chart"))
                var fontColor = "#30eee9";
                let x = data.map((item) => {
                    return item.name;
                })
                let y = data.map((item) => {
                    return item.value;
                })
                let option = {
                    // backgroundColor: "#11183c",
                    title: {
                        text: "",
                        x: "center",
                        top: "0",
                        textStyle: { color: "#fff", fontSize: "32" },
                    },
                    grid: {
                        left: "5%",
                        right: "5%",
                        top: "15%",
                        bottom: "5%",
                        containLabel: true,
                    },
                    tooltip: {
                        trigger: "item",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "30",
                        },
                    },
                    legend: {
                        show: true,
                        x: "center",
                        y: "45",
                        itemWidth: 20,
                        itemHeight: 20,
                        textStyle: {
                            color: "#fff",
                            fontSize: "28px",
                        },
                    },
                    xAxis: [
                        {
                            type: "category",
                            boundaryGap: false,
                            axisLabel: {
                                color: '#fff',
                                // rotate: 45,
                                fontSize: "28px",
                            },
                            axisLine: {
                                show: true,
                                lineStyle: {
                                    color: "#bbb",
                                },
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: "#195384",
                                },
                            },
                            data: x,
                        },
                    ],
                    yAxis: [
                        {
                            type: "value",
                            name: unit,
                            // min: 0,
                            // max: 1000,
                            nameTextStyle: {
                                color: "#fff",
                                fontSize: 23,
                            },
                            axisLabel: {
                                formatter: "{value}",
                                textStyle: {
                                    color: "#fff",
                                    fontSize: "28px",
                                },
                            },
                            axisLine: {
                                lineStyle: {
                                    color: "#fff",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.5,
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "",
                            type: "line",
                            stack: "总量",
                            smooth: true, //加这个
                            // symbol: "circle",
                            symbolSize: 10,
                            itemStyle: {
                                normal: {
                                    color: "#0092f6",
                                    lineStyle: {
                                        color: "#5087EC",
                                        width: 4,
                                    },
                                },
                            },
                            data: y,
                        },
                    ],
                };
                myChartsRun.setOption(option);
            },
            //绘制就业资金、就业困难分布柱图
            getJyBarCharts(id, data) {
                echarts.init(document.getElementById(id)).dispose();
                var series = [];
                if (id == "cxjy-chart2") {
                    switch (this.ageSelectedValue) {
                        case "20-30":

                            data.forEach(element => {
                                element.value = Number(element.value);
                                element.value *= 0.5462;
                                element.value = element.value.toFixed(0)
                            });

                            break;
                        case "30-40":
                            data.forEach(element => {
                                element.value = Number(element.value);
                                element.value *= 0.3034;
                                element.value = element.value.toFixed(0)
                            });
                            break;
                        case "40-50":
                            data.forEach(element => {
                                element.value = Number(element.value);
                                element.value *= 0.1504;
                                element.value = element.value.toFixed(0)
                            });
                            break;
                        default:
                            break;
                    }
                }
                let unit = "单位：人";
                if (id == "jyzjzc-chart") {
                    unit = "单位：万元"
                    series = [
                        {
                            name: "",
                            type: 'bar',
                            barWidth: 35,
                            yAxisIndex: 0,
                            smooth: true, //加这个
                            center: ['0%', '45%'],
                            radius: ['0%', '45%'],
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: '#00C0FF',
                                        },
                                        {
                                            offset: 0.2,
                                            color: '#00C0FF',
                                        },
                                        {
                                            offset: 1,
                                            color: '#004F69',
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            data: data.map((item) => { return item.value }),
                        },
                        {
                            name: '',
                            type: 'line',
                            smooth: true,
                            data: data.map((item) => { return item.value }),
                        }
                    ]
                } else {
                    series = [
                        {
                            name: "",
                            type: 'bar',
                            barWidth: 35,
                            yAxisIndex: 0,
                            smooth: true, //加这个
                            center: ['0%', '45%'],
                            radius: ['0%', '45%'],
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: '#00C0FF',
                                        },
                                        {
                                            offset: 0.2,
                                            color: '#00C0FF',
                                        },
                                        {
                                            offset: 1,
                                            color: '#004F69',
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            data: data.map((item) => { return item.value }),
                        }
                    ]
                }

                let myEc = echarts.init(document.getElementById(id));
                var option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
                        },
                        borderWidth: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.6)',
                        textStyle: {
                            color: 'white',
                            fontSize: '28',
                        },
                    },
                    legend: {
                        orient: 'horizontal',
                        // itemWidth: 18,
                        // itemHeight: 18,
                        top: '0',
                        // icon: 'rect',
                        itemGap: 25,
                        textStyle: {
                            color: '#D6E7F9',
                            fontSize: 30,
                        },
                    },
                    grid: {
                        left: '2%',
                        right: '5%',
                        bottom: '10%',
                        top: '10%',
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: data.map((item) => { return item.name }),
                            offset: 20,
                            axisLine: {
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.3,
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                // rotate: -30,
                                textStyle: {
                                    fontSize: 30,
                                    color: 'white',
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: unit,
                            type: 'value',
                            // max: 800,
                            // min: 0,
                            nameTextStyle: {
                                fontSize: 23,
                                color: '#D6E7F9',
                                padding: [10, 0, -12, 0],
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 30,
                                    color: '#D6E7F9',
                                },
                            },
                        },
                    ],
                    series: series
                }
                myEc.setOption(option)
                tools.loopShowTooltip(myEc, option, { loopSeries: true });
            },
            //绘制就业困难人员环图
            getChart01(id, data) {
                echarts.init(document.getElementById(id)).dispose();
                let myEc = echarts.init(document.getElementById(id));
                let imgUrl = '/static/citybrain/djtl/img/djtl-left/echarts-bg.png';
                let unit = "人";
                
                const option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{b}: <br/>{d}%',
                        borderWidth: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.6)',
                        textStyle: {
                            color: 'white',
                            fontSize: '30',
                        },
                    },
                    legend: {
                        orient: 'vertical',
                        itemWidth: 18,
                        itemHeight: 18,
                        left: '55%',
                        top: 'center',
                        // icon: 'circle',
                        itemGap: 30,
                        textStyle: {
                            color: '#D6E7F9',
                            fontSize: 28,
                            padding: [0, 0, 0, 10]
                        },
                        formatter: function (name) {
                            var data = option.series[0].data; //获取series中的data
                            var total = 0;
                            var tarValue
                            for (var i = 0, l = data.length; i < l; i++) {
                                total += data[i].value
                                if (data[i].name == name) {
                                    tarValue = data[i].value
                                }
                            }
                            var p = (tarValue / total) * 100;
                            return name + '  ' + tarValue + unit;
                        },
                    },
                    graphic: [
                        {
                            z: 4,
                            type: "image",
                            id: "logo",
                            left: "29.3%",
                            top: "30.5%",
                            z: -10,
                            bounding: "raw",
                            rotation: 0, //旋转
                            origin: [-140, -50], //中心点
                            scale: [0.5, 0.5], //缩放
                            style: {
                                image: imgUrl,
                                opacity: 1,
                            },
                        },
                    ],
                    series: [
                        {
                            name: "",
                            type: 'pie',
                            radius: ['40%', '55%'],
                            center: ['31.5%', '51.3%'],
                            itemStyle: {
                                normal: {
                                    borderColor: "#0A1934",
                                    // borderWidth: 10
                                },
                            },
                            label: {
                                show: false,
                            },
                            data: data,
                        },
                    ],
                }
                myEc.setOption(option)
                tools.loopShowTooltip(myEc, option, { loopSeries: true });
            },
        },
        //项目生命周期
        mounted() {
            this.getTime();
            this.init();
        }


    })


</script>

</html>