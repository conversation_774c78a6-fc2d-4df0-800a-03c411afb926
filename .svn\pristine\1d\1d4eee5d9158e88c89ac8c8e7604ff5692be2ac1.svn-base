<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>社会管理指标分析</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <style>
      #shglzbfx-right {
        width: 1050px;
        height: 1930px;
        background: url("/img/right-bg.png") no-repeat;
        background-size: 100% 100%;
      }
      .title {
        position: relative;
      }
      .select {
        position: absolute;
        z-index: 2;
        right: 20px;
      }
      .lb2 .el-input__inner {
        width: 180px;
      }
      .el-input__inner {
        font-size: 30px;
        width: 130px;
        height: 50px;
        line-height: 50px;
        color: #fff;
        background-color: #011040b3;
      }
      .el-select-dropdown__item.hover,
      .el-select-dropdown__item:hover {
        background-color: #011040b3;
      }
      .el-input__icon {
        line-height: 48px;
      }
      .el-select-dropdown {
        background-color: #011040b3;
      }
      .el-select-dropdown__item {
        font-size: 30px;
        color: #fff;
      }
      .el-select .el-input .el-select__caret {
        font-size: 30px;
      }
    </style>
  </head>

  <body>
    <div id="shglzbfx-right">
      <div class="content">
        <div class="title">
          <nav style="padding: 20px 45px">
            <s-header-title
              style="width: 100%"
              title="社会管理案件综合展示"
              htype="2"
            ></s-header-title>
          </nav>
        </div>
        <div class="title">
          <nav style="padding: 0px 0">
            <s-header-title2
              style="width: 100%"
              title="案件趋势"
              htype="2"
            ></s-header-title2>
          </nav>
          <div class="select">
            <el-select v-model="value" placeholder="请选择" @change="change1">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-select v-model="value1" placeholder="请选择" @change="change2">
              <el-option
                v-for="item in options1"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-select v-model="value2" placeholder="请选择" @change="change3">
              <el-option
                v-for="item in options2"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div id="lineEcharts01" style="height: 500px"></div>
        <div class="title">
          <nav style="padding: 0px 0">
            <s-header-title2
              style="width: 100%"
              title="案件区域分析"
              htype="2"
            ></s-header-title2>
          </nav>
          <div class="select">
            <!-- <el-select v-model="value3" placeholder="请选择" @change="change4">
                            <el-option
                                v-for="item in options3"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select> -->
            <el-select
              v-model="value4"
              placeholder="请选择"
              @change="change5"
              class="lb2"
            >
              <el-option
                v-for="item in options4"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div id="barEcharts01" style="height: 500px"></div>
        <div class="title">
          <nav style="padding: 0px 0">
            <s-header-title2
              style="width: 100%"
              title="案件类型分析"
              htype="2"
            ></s-header-title2>
          </nav>
          <div class="select">
            <el-select v-model="value5" placeholder="请选择" @change="change6">
              <el-option
                v-for="item in options5"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-select v-model="value6" placeholder="请选择" @change="change7">
              <el-option
                v-for="item in options6"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div id="pieEcharts01" style="height: 500px"></div>
      </div>
    </div>
  </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  var vm = new Vue({
    el: "#shglzbfx-right",
    data: {
      wgDataMap: [
        {
          name: "金华市",
          url: "",
          idName: "qkwg_jhs",
          lat: 29.14978834164311,
          lng: 119.9387785285524,
        },
        {
          name: "婺城区",
          url: "/gismap/egis-map-engine/data/services/wcqqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
          idName: "qkwg_wc",
          lat: 28.93380203290269,
          lng: 119.49635566172856,
        },
        {
          name: "金义新区",
          url: "/gismap/egis-map-engine/data/services/jdqqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
          idName: "qkwg_jy",
          lat: 29.16049804730686,
          lng: 119.79435122905141,
        },
        {
          name: "东阳市",
          url: "/gismap/egis-map-engine/data/services/dysqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
          idName: "qkwg_dy",
          lat: 29.248251186036732,
          lng: 120.5201441138766,
        },
        {
          name: "义乌市",
          url: "/gismap/egis-map-engine/data/services/ywsqkwlsjfu/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
          idName: "qkwg_yw",
          lat: 29.094683075383088,
          lng: 120.01739085612695,
        },
        {
          name: "永康市",
          url: "/gismap/egis-map-engine/data/services/yksqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
          idName: "qkwg_yk",
          lat: 28.956095962721335,
          lng: 120.03588432177509,
        },
        {
          name: "兰溪市",
          url: "/gismap/egis-map-engine/data/services/lxsqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
          idName: "qkwg_lx",
          lat: 29.279330478334103,
          lng: 119.43054068980388,
        },
        {
          name: "浦江县",
          url: "/gismap/egis-map-engine/data/services/pjxqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
          idName: "qkwg_pj",
          lat: 29.58151310562738,
          lng: 119.8954545240473,
        },
        {
          name: "武义县",
          url: "/gismap/egis-map-engine/data/services/wyxqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
          idName: "qkwg_wy",
          lat: 28.740013504460414,
          lng: 119.73219264445578,
        },
        {
          name: "磐安县",
          url: "/gismap/egis-map-engine/data/services/paxqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
          idName: "qkwg_pa",
          lat: 29.046470243324755,
          lng: 120.62932165476633,
        },
        {
          name: "开发区",
          url: "/gismap/egis-map-engine/data/services/jhkfqqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
          idName: "qkwg_kf",
          lat: 29.04554809805949,
          lng: 119.35362523492347,
        },
        {
          name: "金东区",
          url: "/gismap/egis-map-engine/data/services/jhkfqqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
          idName: "qkwg_kf",
          lat: 29.04554809805949,
          lng: 119.35362523492347,
        },
      ],
      wgDataUrlEnum: {
        婺城区:
          "/gismap/egis-map-engine/data/services/wcqqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
        金义新区:
          "/gismap/egis-map-engine/data/services/jdqqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
        东阳市:
          "/gismap/egis-map-engine/data/services/dysqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
        义乌市:
          "/gismap/egis-map-engine/data/services/ywsqkwlsjfu/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
        永康市:
          "/gismap/egis-map-engine/data/services/yksqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
        兰溪市:
          "/gismap/egis-map-engine/data/services/lxsqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
        浦江县:
          "/gismap/egis-map-engine/data/services/pjxqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
        武义县:
          "/gismap/egis-map-engine/data/services/wyxqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
        磐安县:
          "/gismap/egis-map-engine/data/services/paxqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
        开发区:
          "/gismap/egis-map-engine/data/services/jhkfqqkwlsjfw/wfs200/2.0.0/GetFeature?token=3adffb2d586245e182c24828c8b63989&count=999999999",
      },

      options: [
        {
          value: "2022",
          label: "2023",
        },
        {
          value: "2023",
          label: "2024",
        },
        {
          value: "2024",
          label: "2025",
        },
      ],
      value: "2024",
      time: "2022",
      options1: [
        {
          value: "婺城区",
          label: "婺城区",
        },
        {
          value: "金义新区",
          label: "金义新区",
        },
        {
          value: "东阳市",
          label: "东阳市",
        },
        {
          value: "义乌市",
          label: "义乌市",
        },
        {
          value: "永康市",
          label: "永康市",
        },
        {
          value: "兰溪市",
          label: "兰溪市",
        },
        {
          value: "浦江县",
          label: "浦江县",
        },
        {
          value: "武义县",
          label: "武义县",
        },
        {
          value: "磐安县",
          label: "磐安县",
        },
      ],
      value1: "婺城区",
      area1: "婺城区",
      options2: [
        {
          value: "轻微事故",
          label: "轻微事故",
        },
        {
          value: "一般事故",
          label: "一般事故",
        },
        {
          value: "重大事故",
          label: "重大事故",
        },
      ],
      value2: "轻微事故",
      lb: "轻微事故",
      options3: [
        {
          value: "时间",
          label: "时间",
        },
      ],
      value3: "时间",
      options4: [
        {
          value: "轻微事故",
          label: "轻微事故",
        },
        {
          value: "一般事故",
          label: "一般事故",
        },
        {
          value: "重大事故",
          label: "重大事故",
        },
      ],
      value4: "轻微事故",
      options5:  [
        {
          value: "2022",
          label: "2023",
        },
        {
          value: "2023",
          label: "2024",
        },
        {
          value: "2024",
          label: "2025",
        },
      ],
      value5: '2024',
      item: {
        codeid: "szwh-lyfw",
        label: "文化馆",
        name: "案件",
        num: "22",
        orderid: 10,
        parentid: "szwh",
      },
      options6: [
      {
          value: "婺城区",
          label: "婺城区",
        },
        {
          value: "金义新区",
          label: "金义新区",
        },
        {
          value: "东阳市",
          label: "东阳市",
        },
        {
          value: "义乌市",
          label: "义乌市",
        },
        {
          value: "永康市",
          label: "永康市",
        },
        {
          value: "兰溪市",
          label: "兰溪市",
        },
        {
          value: "浦江县",
          label: "浦江县",
        },
        {
          value: "武义县",
          label: "武义县",
        },
        {
          value: "磐安县",
          label: "磐安县",
        },
      ],
      value6: "婺城区",
      year: 2023,
      area: "婺城区",
    },
    mounted() {
      this.initFun();
    },
    methods: {
      change1(item) {
        switch (item) {
          case "2023":
            this.item.label = "景区";
            break;
          case "2022":
            this.item.label = "文化馆";
            break;

          default:
            break;
        }
        // this.hotMap()
        this.getPoint(this.item);
        this.hotMap();
        this.time = item;
        $api("ldst_shgl_shglzbfx", {
          type: 4,
          area: this.area1,
          lb: this.lb,
          year: item,
        }).then((res) => {
          this.getEcharts01(res);
        });
      },
      change2(item) {
        switch (item) {
          case "金义新区":
            this.item.label = "旅行社";
            break;
          case "婺城区":
            this.item.label = "文化馆";
            break;
          case "武义县":
            this.item.label = "星级酒店";
            break;
          case "东阳市":
            this.item.label = "博物馆";
            break;
          case "磐安县":
            this.item.label = "图书馆";
            break;
          case "义乌市":
            this.item.label = "文化馆";
            break;
          case "浦江县":
            this.item.label = "旅行社";
            break;
          case "永康市":
            this.item.label = "景区";
            break;
          default:
            break;
        }
        this.hotMap();
        this.getPoint(this.item);
        // this.hotMap()

        this.area1 = item;
        $api("ldst_shgl_shglzbfx", {
          type: 4,
          area: item,
          lb: this.lb,
          year: this.time,
        }).then((res) => {
          this.getEcharts01(res);
        });
      },
      change3(item) {
        switch (item) {
          case "一般事故":
            this.item.label = "厕所";
            break;
          case "轻微事故":
            this.item.label = "图书馆";
            break;
          case "重大事故":
            this.item.label = "星级酒店";
            break;
          default:
            break;
        }
        this.hotMap();
        this.getPoint(this.item);

        this.lb = item;
        $api("ldst_shgl_shglzbfx", {
          type: 4,
          area: this.area1,
          lb: item,
          year: this.time,
        }).then((res) => {
          this.getEcharts01(res);
        });
      },
      // change4(item) {
      //     $api("ldst_shgl_shglzbfx", { type: 5, lb: "轻微事故" }).then((res) => {
      //         this.getEcharts02(res);
      //     });
      // },
      change5(item) {
        switch (item) {
          case "一般事故":
            this.item.label = "景区";
            break;
          case "轻微事故":
            this.item.label = "旅行社";
            break;
          case "重大事故":
            this.item.label = "博物馆";
            break;
          default:
            break;
        }
        this.hotMap();
        this.getPoint(this.item);

        this.value4 = item;
        $api("ldst_shgl_shglzbfx", { type: 5, lb: item }).then((res) => {
          this.getEcharts02(res);
        });
      },
      change6(item) {
        switch (item) {
          case "2023":
            this.item.label = "景区";
            break;
          case "2022":
            this.item.label = "文化馆";
            break;

          default:
            break;
        }
        this.hotMap();
        this.getPoint(this.item);
        // this.hotMap()

        this.year = item;
        $api("ldst_shgl_shglzbfx", {
          type: 6,
          year: item,
          area: this.area,
        }).then((res) => {
          this.getEcharts03(res);
        });
      },
      change7(item) {
        // debugger
        this.shapeFun(item);
        this.flytoFun(item);
        switch (item) {
          case "金义新区":
            this.item.label = "旅行社";
            break;
          case "婺城区":
            this.item.label = "文化馆";
            break;
          case "武义县":
            this.item.label = "星级酒店";
            break;
          case "东阳市":
            this.item.label = "博物馆";
            break;
          case "磐安县":
            this.item.label = "图书馆";
            break;
          case "义乌市":
            this.item.label = "文化馆";
            break;
          case "浦江县":
            this.item.label = "旅行社";
            break;
          case "永康市":
            this.item.label = "景区";
            break;
          default:
            break;
        }
        this.hotMap();
        this.getPoint(this.item);
        this.area = item;
        $api("ldst_shgl_shglzbfx", {
          type: 6,
          year: this.year,
          area: item,
        }).then((res) => {
          this.getEcharts03(res);
        });
      },
      initFun() {
        $api("ldst_shgl_shglzbfx", {
          type: 4,
          area: "金义新区",
          lb: "轻微事故",
          year: '2024',
        }).then((res) => {
          this.getEcharts01(res);
        });
        $api("ldst_shgl_shglzbfx", { type: 5, lb: "轻微事故" }).then((res) => {
          this.getEcharts02(res);
        });
        $api("ldst_shgl_shglzbfx", {
          type: 6,
          year: '2024',
          area: "金义新区",
        }).then((res) => {
          this.getEcharts03(res);
        });
      },
      hotMap() {
        $api("/zt_bsczt_qyrl").then((res) => {
          let hotMapData = [];
          res.map((item, index) => {
            // 画热力图的数据
            let pointArr = [];
            pointArr[0] = Number(item.jd);
            pointArr[1] = Number(item.wd);
            pointArr[2] = item.num;
            pointArr[3] = index + 1;
            hotMapData.push(pointArr);
          });
          const mapData = {
            funcName: "hotPowerMap",
            hotPowerMapData: hotMapData,
            offset: 256,
            heatMapId: "bscztHot",
            threshold: 6000,
            distance: 800,
            alpha: 0.3,
          };
          window.parent.document
            .getElementById("map")
            .contentWindow.Work.funChange(JSON.stringify(mapData));
        });
      },
      //清除热力图
      rmHot() {
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "rmhotPowerMap", //热力图
            heatMapId: "Hot",
          })
        );
      },
      // 获取经纬度
      getPoint(item) {
        let that = this;
        $api("yxzl_szwh_center011", { code: item.label }).then((res) => {
          let pointData = [];
          let icon = "zhdd_map_whg";
          res.forEach((obj, index) => {
            if (
              obj.lng.split(",")[0].indexOf("无") < 0 ||
              obj.lng.split(",")[1].indexOf("无") < 0 ||
              obj.lng.split(",")[0] == 0 ||
              obj.lng.split(",")[1] == 0
            ) {
              let str = {
                data: {
                  pointId: "szwh",
                  obj,
                  key: ["时间", "地址"],
                  value: ["2022-8-23", "阳光路134号"],
                  title: item.name,
                },
                point: obj.lng,
              };
              pointData.push(str);
            }
          });
          //   debugger
          that.pointTextMapFun(icon, pointData, item.codeid);
        });
      },
      // 添加点位方法
      pointTextMapFun(icon, pointData, pointId) {
        this.hotMap();
        this.rmPoint();
        console.log(pointData);
        console.log("icon", icon);
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "pointLoad", //功能名称
            pointType: icon, //点位类型图标
            pointId: "0" + pointId,
            setClick: true,
            pointData: pointData,
            imageConfig: { iconSize: 0.6 },
            size: [0.01, 0.01, 0.01, 0.01],
            popup: {
              offset: [50, -100],
            },
          })
        );
      },
      rmPoint() {
        // this.rmHot()
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "rmPoint",
            pointId: "", //传id清除单类，不传清除所有
          })
        );
      },
      shapeFun(name) {
        let that = this;
        var nameId = this.wgDataMap.filter((ele) => ele.name == name)[0].idName;
        // debugger
        axios.get(that.wgDataUrlEnum[name]).then((res) => {
          console.log(res);
          let textDataArr = [];
          res.data.features.map((v) => {
            let num = Math.floor(Math.random() * 251) + 10;
            let obj = {
              pos: [v.properties.center_x, v.properties.center_y, 11000],
              text: num,
              color: [255, 255, 255, 1],
            };
            textDataArr.push(obj);
          });

          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "3Dtext", //加载多边形功能
              textData: textDataArr,
              textSize: 36,
            })
          );

          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "shape", //加载多边形功能
              geojson: res.data,
              colorOutline: [255, 50, 40, 0.9], //多边形边缘颜色 RGBA格式,
              color: [193, 210, 240, 0.2], //多边形填充颜色 RGBA格式
              name: 0 + nameId, //多边形名称 可用于删除
              feedback: true,
            })
          );
        });
      },
      flytoFun(name) {
        var oneObj = this.wgDataMap.filter((ele) => ele.name == name)[0];
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "flyto",
            flyData: {
              center: [oneObj.lng, oneObj.lat],
              zoom: 13, //大
              essential: true,
              duration: 5000,
            },
          })
        );
      },
      getEcharts01(res) {
        let myCharts = echarts.init(document.getElementById("lineEcharts01"));
        res = res.map(item => {
          return {
            ...item,
            name: item.name.replace('2024','2025').replace('2023','2024').replace('2022','2023')
          }
        })
        if (res[0].name.includes('2025')) {
          res = res.slice(0,4)
        }
        let yData = res.map((item) => {
          return item.name;
        });
        let value = res.map((item) => {
          return item.value;
        });
        let option = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
            textStyle: {
              fontSize: 30,
            },
          },
          legend: {
            itemWidth: 40,
            itemHeight: 30,

            textStyle: {
              fontSize: 30,
              color: "#ffff",
            },
          },
          grid: {
            bottom: "10%",
          },
          yAxis: {
            type: "value",
            min: 0,
            splitLine: {
              show: false,
            },
            axisLabel: {
              fontSize: 30,
              color: "#ffff",
            },
          },
          xAxis: {
            type: "category",
            data: yData,

            axisLabel: {
              fontSize: 30,
              color: "#ffff",
            },
          },
          series: [
            {
              name: "事件总量",
              type: "line",
              smooth: true, //平滑曲线显示
              showAllSymbol: true, //显示所有图形。
              symbol: "circle", //标记的图形为实心圆
              symbolSize: 10, //标记的大小
              itemStyle: {
                //折线拐点标志的样式
                color: "#058cff",
              },
              lineStyle: {
                color: "#058cff",
              },
              areaStyle: {
                color: "rgba(5,140,255, 0.2)",
              },
              data: value,
            },
          ],
        };
        myCharts.setOption(option);
      },
      getEcharts02(res) {
        let myCharts = echarts.init(document.getElementById("barEcharts01"));
        let yData = res.map((item) => {
          return item.name;
        });
        let value = res.map((item) => {
          return item.value;
        });
        let value1 = res.map((item) => {
          return item.value;
        });
        let option = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
            textStyle: {
              fontSize: 30,
            },
          },
          legend: {
            itemWidth: 40,
            itemHeight: 30,

            textStyle: {
              fontSize: 30,
              color: "#ffff",
            },
          },
          grid: {
            bottom: "15%",
          },
          yAxis: {
            type: "value",

            splitLine: {
              show: false,
            },
            axisLabel: {
              fontSize: 30,
              color: "#ffff",
            },
          },
          xAxis: {
            type: "category",
            data: yData,
            offset: 15,
            axisLabel: {
              rotate: 25,
              fontSize: 30,
              color: "#ffff",
            },
          },
          series: [
            {
              name: "2025",
              type: "bar",
              data: value,
            },
            {
              name: "2024",
              type: "bar",
              data: value1,
            },
          ],
        };
        myCharts.setOption(option);
      },
      getEcharts03(res) {
        let myCharts = echarts.init(document.getElementById("pieEcharts01"));
        let option = {
          tooltip: {
            trigger: "item",
            textStyle: {
              fontSize: 30,
            },
          },

          series: [
            {
              type: "pie",
              radius: ["50%", "80%"],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: "#2b516f",
                borderWidth: 2,
              },
              label: {
                formatter: "{b}\n{c}",
                textStyle: {
                  fontSize: 30,
                  color: "#fff",
                },
              },
              labelLine: {
                show: true,
                length: 25,
                length2: 30,
              },
              data: res,
            },
          ],
        };
        myCharts.setOption(option);
      },
    },
  });
</script>
