<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>市场监管</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <style>
            * {
                padding: 0;
                margin: 0;
            }
            #scjg-right {
                width: 1050px;
                height: 1930px;
                background: url("/img/right-bg.png") no-repeat;
                background-size: 100% 100%;
            }
            .value {
                color: #0087ec;
            }

            .sxyt {
                font-size: 30px;
                color: #fff;
                display: flex;
                justify-content: space-around;
                text-align: center;
            }
            .sxytBox {
                background: url("../img/itembg.png") no-repeat;
                background-size: 100% 100%;
                padding: 15px 10px;
            }

            .title {
                position: relative;
            }
            .detailBtn {
                position: absolute;
                right: 50px;
                top: 10px;
                font-size: 30px;
            }
        </style>
    </head>

    <body>
        <div id="scjg-right">
            <div class="content">
                <div class="title">
                    <nav style="padding: 20px 45px">
                        <s-header-title style="width: 100%" title="重点企业监管" htype="2"></s-header-title>
                    </nav>
                    <el-button type="primary" class="detailBtn" @click="openIframe01">详情</el-button>
                </div>
                <div id="pieEcharts01" style="height: 300px; width: 100%"></div>
                <div class="title">
                    <nav style="padding: 20px 45px">
                        <s-header-title style="width: 100%" title="营商环境" htype="2"></s-header-title>
                    </nav>
                </div>
                <div class="title">
                    <nav style="padding: 0">
                        <s-header-title2 style="width: 100%" title="市场主体分析" htype="2"></s-header-title2>
                    </nav>
                    <el-button type="primary" class="detailBtn" @click="openIframe02">详情</el-button>
                </div>
                <div id="barEcharts01" style="height: 300px; width: 100%"></div>
                <div class="title">
                    <nav style="padding: 0">
                        <s-header-title2 style="width: 100%" title="投资者数量分布" htype="2"></s-header-title2>
                    </nav>
                </div>

                <div id="pieEcharts02" style="height: 300px; width: 100%"></div>
                <div class="title">
                    <nav style="padding: 20px 45px">
                        <s-header-title style="width: 100%" title="三小一摊" htype="2"></s-header-title>
                    </nav>
                    <el-button type="primary" class="detailBtn" @click="openIframe03">详情</el-button>
                </div>
                <div class="sxyt">
                    <div class="sxytBox" v-for="item in sxytList">
                        <div>{{item.name}}</div>
                        <div class="value">{{item.value}}{{item.unit}}</div>
                    </div>
                </div>

                <div id="barEcharts03" style="height: 300px"></div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#scjg-right",
        data: {
            sxytList: [],
        },
        mounted() {
            this.initFun();
        },
        methods: {
            initFun() {
                $api("ldst_scjg_scjg", { type: 11 }).then((res) => {
                    this.getEcharts01(res);
                });
                $api("ldst_scjg_scjg", { type: 12 }).then((res) => {
                    this.getEcharts02(res);
                });
                $api("ldst_scjg_scjg", { type: 13 }).then((res) => {
                    this.getEcharts03(res);
                });
                $api("ldst_scjg_scjg", { type: 14 }).then((res) => {
                    this.sxytList = res;
                });
                $api("ldst_scjg_scjg", { type: 15 }).then((res) => {
                    this.getEcharts04(res);
                });
            },
            openIframe01() {
                let Iframe1 = {
                    type: "openIframe",
                    name: "scjg-right-dialog01",
                    src: baseURL.url + "/static/citybrain3840/scjg/pages/scjg-right-dialog01.html",
                    left: "1170px",
                    top: "230px",
                    width: "1500px",
                    height: "650px",
                    zIndex: "200",
                };
                window.parent.postMessage(JSON.stringify(Iframe1), "*");
            },
            openIframe02() {
                let Iframe2 = {
                    type: "openIframe",
                    name: "scjg-right-dialog02",
                    src: baseURL.url + "/static/citybrain3840/scjg/pages/scjg-right-dialog02.html",
                    left: "1170px",
                    top: "230px",
                    width: "1500px",
                    height: "650px",
                    zIndex: "200",
                };
                window.parent.postMessage(JSON.stringify(Iframe2), "*");
            },
            openIframe03() {
                let Iframe3 = {
                    type: "openIframe",
                    name: "scjg-right-dialog03",
                    src: baseURL.url + "/static/citybrain3840/scjg/pages/scjg-right-dialog03.html",
                    left: "1170px",
                    top: "230px",
                    width: "1500px",
                    height: "1250px",
                    zIndex: "200",
                };
                window.parent.postMessage(JSON.stringify(Iframe3), "*");
            },

            getEcharts01(res) {
                let myCharts = echarts.init(document.getElementById("pieEcharts01"));
                let option = {
                    tooltip: {
                        trigger: "item",
                        formatter: "{b}\n{c}户",
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    series: [
                        {
                            type: "pie",
                            radius: ["50%", "80%"],
                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderRadius: 10,
                                borderColor: "#2b516f",
                                borderWidth: 2,
                            },
                            label: {
                                formatter: "{b}\n{c}户",
                                textStyle: {
                                    fontSize: 30,
                                    color: "#fff",
                                },
                            },
                            labelLine: {
                                show: true,
                                length: 15,
                                length2: 15,
                            },
                            data: res,
                        },
                    ],
                };
                myCharts.setOption(option);
            },

            getEcharts02(res) {
                let myCharts = echarts.init(document.getElementById("barEcharts01"));
                let yData = res.map((item) => {
                    return item.name;
                });
                let value = res.map((item) => {
                    return item.value;
                });
                let value1 = res.map((item) => {
                    return item.value1;
                });
                let option = {
                    legend: {
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    grid: {
                        bottom: "20%",
                        left: "12%",
                        right: "12%",
                    },
                    yAxis: [
                        {
                            type: "value",
                            position: "left",
                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                        {
                            type: "value",
                            position: "right",
                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                    ],

                    xAxis: {
                        type: "category",
                        data: yData,
                        offset: 15,
                        axisLabel: {
                            fontSize: 30,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            name: "诉求量",
                            type: "bar",
                            data: value,
                        },
                        {
                            yAxisIndex: 1,
                            name: "占比",
                            type: "line",
                            data: value1,
                        },
                    ],
                };
                myCharts.setOption(option);
            },
            getEcharts03(res) {
                let myCharts = echarts.init(document.getElementById("pieEcharts02"));
                let option = {
                    tooltip: {
                        trigger: "item",
                        formatter: "{b}\n{c}%",
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    series: [
                        {
                            type: "pie",

                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderRadius: 10,
                                borderColor: "#2b516f",
                                borderWidth: 2,
                            },
                            label: {
                                formatter: "{b}\n{c}%",
                                textStyle: {
                                    fontSize: 30,
                                    color: "#fff",
                                },
                            },
                            labelLine: {
                                show: true,
                                length: 15,
                                length2: 15,
                            },
                            data: res,
                        },
                    ],
                };
                myCharts.setOption(option);
            },
            getEcharts04(res) {
                let myCharts = echarts.init(document.getElementById("barEcharts03"));
                let yData = res.map((item) => {
                    return item.name;
                });
                let value = res.map((item) => {
                    return item.value;
                });
                let value1 = res.map((item) => {
                    return item.value1;
                });
                let option = {
                    legend: {
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    grid: {
                        bottom: "20%",
                        left: "12%",
                        right: "12%",
                    },
                    yAxis: [
                        {
                            type: "value",
                            position: "left",
                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                        {
                            type: "value",
                            position: "right",
                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                    ],

                    xAxis: {
                        type: "category",
                        data: yData,
                        offset: 15,
                        axisLabel: {
                            fontSize: 30,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            name: "企业数",
                            type: "bar",
                            data: value,
                        },
                        {
                            yAxisIndex: 1,
                            name: "同比",
                            type: "line",
                            data: value1,
                        },
                    ],
                };
                myCharts.setOption(option);
            },
        },
    });
</script>
