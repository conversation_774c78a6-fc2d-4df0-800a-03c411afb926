<!--
 * @Author: hdw <EMAIL>
 * @Date: 2022-06-28 14:15:09
 * @LastEditors: hdw <EMAIL>
 * @LastEditTime: 2022-07-28 16:43:45
 * @FilePath: \EGS(v1.0.0)\index1.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        *{
            padding: 0;
            margin: 0;
        }
        html,body,iframe{
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>











    <!-- http://3888z2k945.wicp.vip:6074/EGS/index.html -->
    <iframe src="./index.html" id="mapIframe"></iframe>
    <script>

    // window.onload = () => {
    //     const mapIframe = top.document.getElementById("mapIframe").contentWindow;
    //     setTimeout(() => {
    //         const hotPowerMapData = 
    //             {
    //                 "funcName":"getDistance",//功能名称
    //                 "hotPowerMapData":[
    //                     [119.648559,29.118399],
    //                     [119.62282,29.07687],
    //                     [119.62282,29.07687],
    //                     [119.594765,29.083525],
    //                     [119.577458,29.099688],
    //                     [119.577458,29.099688],
    //                     [119.663934,29.123565],
    //                     [119.577074,29.099152],
    //                     [119.577074,29.099152],
    //                     [119.638581,29.073789],
    //                     [119.663118,29.074813]
    //                 ], //热力图点位数据
    //             }
    //         mapIframe.postMessage(JSON.stringify(hotPowerMapData), "*");
    //     }, 2000);
    //     window.addEventListener('message', function (e) {
    //         console.log(e)
    //                 // if (e.data) {
    //                 //     try {
    //                 //         let data = JSON.parse(e.data);
    //                 //         bastMap.classiFication(data) 
    //                 //     } catch (error) {

    //                 //     }
    //                 // }
    //     });
        
    // }

    </script>
</body>
</html>