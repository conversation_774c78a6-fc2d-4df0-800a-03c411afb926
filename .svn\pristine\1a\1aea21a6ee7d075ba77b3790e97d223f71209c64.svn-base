let reqAbort={};
onmessage = event => {
    
    let type=event.data.type;
    if(type==="createBitMap"){
        createBitMap(event.data)
    }else if(type==="httpRequest"){
        httpRequest(event.data.data)
    }else if(type==="httpRequestCancel"){
        //httpRequest(event.data.data)
        
        let url=event.data.data.url;
        if(reqAbort[url]){
            //console.log("cancel:"+event.data.data.url);
            reqAbort[url].abort();
            delete reqAbort[url];
        }
    }
    else if(type==="mapTileBitMap"){
        let d=event.data.data;
        var blob = new Blob([d.buf], { type: "image/png" });

        createImageBitmap(blob, {
            imageOrientation: 'none',
            premultiplyAlpha: 'none',
        }).then(function (bitmap) {
            postMessage({
                type:"mapTileBitMapRet",
                data:bitmap,
                url:d.url,
                x:d.x,
                y:d.y,
                level:d.level,
                source:d.source || "",
                className:d.className
            });
        }).catch(e=>{
            console.error(e);
        });
    }

};

// 2.接收到一条无法被序列化的消息时，会触发这个事件。
onmessageerror = event => {
    console.log("接收到一条无法被序列化的消息");
};

function createBitMap(d) {
    let buffers=d.data;
    let prims=[];
    var baseSize=512;
    for(let i=0;i<buffers.length;i++){

        if(buffers[i] instanceof Array){
            var imgArray=buffers[i];
            var w=imgArray[2];
            var h=imgArray[3];
            var step=1.0;
            var max=w>h ? w :h;
            
            step=max>baseSize ? baseSize/max :step; 
            
            let p=new Promise((resolve, reject)=>{
                var blob = new Blob([imgArray[1]], { type: "image/jpeg" });
                createImageBitmap(blob, {
                    imageOrientation: 'none',
                    premultiplyAlpha: 'none',
                    //resizeWidth:w*step,
                    //resizeHeight:h*step
                }).then(function (bitmap) {
                    resolve([bitmap,w,h]);
                }).catch(e=>{
                    console.error(e);
                });
            }) 
            prims.push(p);
        }

    }
    Promise.all(prims).then((bitmaps)=>{
        for(let i=0;i<bitmaps.length;i++){
            buffers[i]=[
                buffers[i][0],
                bitmaps[i][0],
                bitmaps[i][1],
                bitmaps[i][2]
            ]
        }
        postMessage({
            type:"vsmBitMap",
            data:buffers,
            dataSource:d.dataSource,
            saveData:d.saveData
        });
    })
}

function httpRequest(params) {
    let controller = new AbortController();
    let signal = controller.signal;
    reqAbort[params.url]=controller;
    fetch(params.url, { signal}).then(function (response) {
        if (response.ok) {
            return response.arrayBuffer();
        } else {
            throw new Error('Network response was not ok.');
        }
    }).then(function (ab) {
        //cur.finish(ab);
        delete reqAbort[params.url]
        postMessage({
            type:"httpRequestRet",
            data:ab,
            status:true,
            params:params
        });
    }).catch(function (error) {
        //cur.cancel(error);
        //console.log(error)
        delete reqAbort[params.url];
        postMessage({
            type:"httpRequestRet",
            data:error,
            status:false,
            params:params
        });
        
    });
}