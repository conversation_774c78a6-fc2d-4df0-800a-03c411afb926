<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <title>数字政府指标分析-中间的上面</title>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <script src="/Vue/vue.js"></script>
        <script src="/static/js/jslib/vue-count-to.min.js"></script>
        <script src="/echarts/echarts.min.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <style scoped>
            * {
                margin: 0;
                padding: 0;
            }

            #app {
                width: 930px;
                height: 170px;
            }

            .topDiv {
                width: 930px;
                height: 170px;
                background: url("/img/left-bg.png") no-repeat;
                background-size: 100% 100%;
            }

            .topContainer {
                display: flex;
                justify-content: center;
            }

            .topContainer .item {
                width: 310px;
                height: 170px;
                text-align: center;
                background: url("../../../shgl/img/itembg.png") no-repeat;
                background-size: 100% 100%;
            }

            .topContainer .item .name {
                color: #fff;
                font-size: 30px;
                margin-top: 30px;
            }

            .topContainer .item .value {
                background: linear-gradient(to bottom, #ffebce, #ffffff, #ffc559, #ffffff);
                -webkit-background-clip: text;
                color: transparent;
                font-size: 50px;
                margin-top: 5px;
            }

            .topContainer .item .value span {
                font-size: 30px;
            }
        </style>
    </head>

    <body>
        <div id="app">
            <div class="topDiv">
                <div class="topContainer">
                    <div class="item">
                        <div class="name">矛盾纠纷事件总数</div>
                        <div class="value">{{topData1}} <span> 件</span></div>
                    </div>
                    <div class="item">
                        <div class="name">调处化解事件数量</div>
                        <div class="value">{{topData2}} <span> 件</span></div>
                    </div>
                    <div class="item">
                        <div class="name">调处化解率</div>
                        <div class="value">{{topData3}} <span> %</span></div>
                    </div>
                </div>
            </div>
        </div>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script>
            let vm = new Vue({
                el: "#app",
                data: {
                    topData1: "",
                    topData2: "",
                    topData3: "",
                },
                mounted() {
                    $api("ldst_szzfzbfx_middle", { type: 1 }).then((res) => {
                        let x = res[0].value.toString().replace(/(\d)(?=(?:\d{3})+$)/g, "$1,");
                        this.topData1 = x;
                        this.topData2 = res[1].value;
                        this.topData3 = res[2].value;
                    });
                },
                methods: {},
            });
        </script>
    </body>
</html>
