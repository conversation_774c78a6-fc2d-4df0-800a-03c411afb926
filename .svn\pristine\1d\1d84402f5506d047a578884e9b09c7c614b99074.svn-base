/* 教育服务弹窗 */
.container{
    width: 4300px;
    margin: 0 auto;
    height: 1850px;
    background-color: #031827;
    box-shadow: -3px 2px 35px 0px #000000;
    border: 1px solid #359cf8;
    /* border-style: solid;
    border-width: 2px;
    border-image-source: linear-gradient(-32deg, #359cf8 0%, #afdcfb 100%);
    border-image-slice: 1; */
    border-radius: 60px;
}
p{
    margin:0;
    padding:0;
}
.container .head{
    width: 100%;
    height: 100px;
    line-height: 100px;
    background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%), linear-gradient(#ffffff, #ffffff);
    background-blend-mode: normal, normal;
    padding: 10px 50px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 60px;
    border-top-right-radius: 60px;
}
.head span{
    font-size: 48px !important;
    font-weight: 500;
    color: #fff;
    font-weight: bold;
}
.head .img{
        display: inline-block;
        margin: 20px;
        float: right;
        width: 34px;
        height: 34px;
        background-image: url(/static/citybrain/csdn/img/cstz2-middle/close-hover.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
}
.content{
    width: 100%;
    height: calc(100% - 100px);
    display: flex;
    justify-content: space-between;
}

/* 教育服务弹窗左侧 */
.jyfw-left-main{
  width: 100%;
  height: 1690px;
  padding-left: 40px;
  box-sizing: border-box;
}


/* 教育机构分布 */

.jyfw-left-main .jyjg-fb{
    width: 100%;
    height: 27%;
    
}
.jyjg-fb .top-con{
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: space-between;
  padding:10px 60px;
  box-sizing:border-box ;
}
.top-con li{
    list-style: none;
    display: flex;
}
.top-con span{
    font-size: 30px;
    color: #fff;
}
.num-bg{
    width: 179px;
    height: 57px;
    background-image: url(/static/citybrain/ggfw/img/title-bg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin-left: 20px;
    text-align: center;
    margin-top: -4px;
}
.num-bg .num{
    font-size: 40px;
    background: linear-gradient(to top, #83b8ff, #74b4f4, #f0ffff);
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 800;
}
.num-bg .unit{
    font-size: 20px;
    background: linear-gradient(to top, #83b8ff, #74b4f4, #f0ffff);
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 500;
    margin-left: 10px;
}
.bottom-echarts{
    width: 100%;
    height: calc(100% - 180px);
    display: flex;
    justify-content: space-around;
}
.bottom-echarts .echarts{
   width: 30%;
   height: 100%;
}


/* 全市教育发展概况 */
.jyfw-left-main .fzgk{
    width: 100%;
    height: 23%;
}
.fzgk .fzgk-con{
  width: 100%;
  height: 70%;
  display: flex;
  flex-wrap: wrap;
  padding-left: 65px;
  box-sizing: border-box;
}
.fzgk-con li{
    list-style: none;
    width: 23%;
    height: 60px;
    margin-right: 25px;
    display: flex;
}
.fzgk-con .img-right{
  margin-left: 20px;
}
.img-right .name{
    font-size: 28px;
    /* background: linear-gradient(to top, #83b8ff, #74b4f4, #f0ffff);
    -webkit-background-clip: text; */
    color: #889aa0;
    font-weight:bold;
}
.img-right .value{
    font-size: 28px;
    background: linear-gradient(to top, #83b8ff, #74b4f4, #f0ffff);
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 800;
    text-align: center;
    position: relative;
    /* width:130px; */
}
.img-right .value img{
    position: absolute;
    right:-15px;
    top:22px;
    width:20px;
    height:40px;
}
.img-right .value span{
    font-size: 24px;
    font-weight: 500;
    color: #74b4f4;
    margin-left: 10px;
}
.img-right .value p{
    font-size: 24px;
    margin-left: 10px;
}


/* 教育机构办学申报条件 */

.jyfw-left-main .sbtj{
    width: 100%;
    height: 14%;
}
.sbtj-con{
    width: 100%;
    height: 40%;
    display: flex;
    justify-content: space-around;
    margin-top: 15px;
    padding-left: 40px;
    box-sizing: border-box;
}
.sbtj-con .li{
    list-style: none;
    width: 23%;
    height:95% ;
    /* background-image: url(/static/citybrain/ggfw/img/title-bg2.png);
    background-repeat: no-repeat;
    background-size: 100% 100%; */
    padding:8px 20px;
    box-sizing: border-box;
    text-align: center;
}
/* 办学政策 */

.jyfw-left-main .bxzc{
    width: 100%;
    height: 35%;
}
.bxzc .bxzc-con{
    width: 100%;
    height: 80%;
    display: flex;
    justify-content: space-between;
    padding-left: 40px;
    box-sizing: border-box;
    position: relative;
}
.bxzc .left-con{
    width: 321px;
    height: 100%;
    text-align: center;
}
.left-con .img-right{
    padding-top: 3px;
    box-sizing: border-box;
}
.left-con li{
    width: 321px;
    height: 127px;
    background-image: url(/static/citybrain/ggfw/img/bxzc-bg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin-top: 30px;
    list-style: none;
}
.name-bg{
    text-align: center;
    width: 100%;
    margin: 7px auto;
    background-image: url('/static/images/components/first-title.png');
    background-size: 100% 100%;
    height: 60px;
    line-height: 60px;

}
.bxzc .right-echarts{
    width: calc(100% - 321px);
    height: 100%;
}
.bxzc .select{
   position: absolute;
   right: 60px;
   top: 0px;
   z-index: 999;
}



/* 教育服务弹窗右侧 */
.jyfw-right-main{
    width: 100%;
    height: 1690px;
}
.jyfw-right-main .trzb{
   width: 100%;
   height: 25%;
   /* display: flex; */
}
.trzb #trzb-chart{
  width: 100%;
  height: 70%;
}
.jyfw-right-main .ndbh{
    width: 100%;
    height: 25%;
    
 }
 .ndbh-con,.zbdc-con{
     width: 100%;
     padding: 0 60px;
     box-sizing: border-box;
     display: flex;
     justify-content: space-between;
     height: 70%;
 }
 .ndbh-left{
   width: 240px;
   height:100% ;
 }
 .ndbh-left li{
    width: 100%;
    height: 127px;
    background-image: url(/static/citybrain/ggfw/img/bxzc-bg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin-top: 20px;
    list-style: none;
    padding-top: 1px;
    box-sizing: border-box;
    
 }
 .jyfw-right-main .zbdc-sf{
    width: 100%;
    height: 25%;
  
 }
 .jyfw-right-main .gjhfz{
    width: 100%;
    height: 25%;
 }
.ndbh-midle,.zbdc-midle{
    width: 40%;
    height: 100%;
    padding: 10px 40px;
    box-sizing: border-box;
}
.zbdc-midle li{
    width: 100%;
    height: 70px;
    display: flex;
    line-height: 70px;
    justify-content: space-between;
    padding: 0 30px;
    box-sizing: border-box;
}
.gjhfz-text{
    font-size: 32px;
    font-weight: 700;
    background: linear-gradient(to bottom, #f0ffff, #74b4f4, #83b8ff);
    -webkit-background-clip: text;
    color: transparent;
    text-align: center;
}
.ndbh-right,.zbdc-right{
    width: 50%;
    height: 100%;
}

.gjhfz-con{
    width: 100%;
    height: 70%;
    display: flex;
}
.gjhfz-con .gjhfz-left{
    width: 100%;
    height: 100%;
}
.gjhfz-con .gjhfz-right{
    width: 60%;
    height: 100%;
   
}
/* 下拉框 */
.el-select {
    width: 150px;
    margin-right: 20px;
  }
  .el-input__inner {
    height: 50px !important;
    width: 200px !important;
    background-color: #00487f;
    color: #fff;
    font-size: 28px;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #00487f;
  }
  .el-select-dropdown__item {
    color: #fff;
    background-color: #00487f;
    font-size: 28px;
  }
  .el-select-dropdown__list {
    background-color: #00487f;
  }
  .el-select .el-input .el-select__caret {
    position: relative;
    left: 40px;
    font-size: 28px;
    color: #fff;
  }
  .box2-content {
    display: unset;
  }
  .el-select .el-input__inner{
    border-radius: 30px !important;
  }
  .el-scrollbar{
    width: 198px;
  }
  .el-tag{
    font-size: 25px;
  }

