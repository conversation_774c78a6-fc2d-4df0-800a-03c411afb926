<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>系统及数据接入</title>
    <!-- <script src="/static/citybrain/csdn/static/citybrain/jhpro/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/static/citybrain/jhpro/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/static/citybrain/jhpro/echarts/echarts.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/static/citybrain/jhpro/elementui/css/elementui.css">
    <link rel="stylesheet" href="/static/citybrain/csdn/static/citybrain/hjbh-right/css/common.css">
    <script src="/static/citybrain/csdn/static/citybrain/jhpro/elementui/js/elementui.js"></script> -->
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css">
    <link rel="stylesheet" href="/static/citybrain/csdn/css/common.css">
    <link rel="stylesheet" href="/static/citybrain/csdn/css/ztyx-middle.css">
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
</head>
<style>

</style>

<body>
    <div id="app" v-cloak>
        <div id="mainbox">
            <div class="ztyx_app_box">
                <div class="ztyx_header clearfix">
                    <h2 class="title"> <i class="title_icon_one"></i>系统及数据接入</h2>
                    <div class="title_hr" style="width: 1750px;left: 0px"></div>
                    <h3 class="title" style="font-weight: normal;font-size: 36px; margin-right: 70px">数据至:2022年3月4日
                    </h3>
                </div>
                <div class="ztyx_container">
                    <div class="containerLeft">
                        <div class="containerLeftT">
                            <div class="ztyx_header_center_text">
                                <div class="ztyx_header_title different_font">
                                    <div class="left_cell"><img src="/static/citybrain/csdn/img/right.png" alt=""></div>
                                    <div class="titletext"
                                        style="font-size: 40px;text-align: center;margin-top: -10px;">业务系统</div>
                                    <div class="right_cell"><img src="/static/citybrain/csdn/img/left.png" alt=""></div>
                                </div>
                            </div>
                            <div class="leftTitle">
                                <div class="leftTitlelist">
                                    <div class="leftTitlelistImg">
                                        <img src="/static/citybrain/csdn/img/middle/部门系统综述.png" alt="">
                                    </div>
                                    <div class="leftTitlelistTex">
                                        <p>部门系统总数</p>
                                        <h5>24<span>个</span></h5>
                                    </div>
                                </div>
                                <div class="leftTitlelist">
                                    <div class="leftTitlelistImg">
                                        <img src="/static/citybrain/csdn/img/middle/县区市系统综述.png" alt="">
                                    </div>
                                    <div class="leftTitlelistTex">
                                        <p>县（市、区）系统总数</p>
                                        <h5>9<span>个</span></h5>
                                    </div>
                                </div>
                            </div>
                            <ul class="leftList">
                                <li class="leftListItem">
                                    <div class="leftListItemTitle">1</div>
                                    <div class="leftListItemCont">
                                        <div class="leftListItemContL">
                                            市财政局
                                        </div>
                                        <div class="leftListItemContR">
                                            <p class="contRnum">接入系统数量</p>
                                            <h6>4<span>个</span></h6>
                                        </div>
                                    </div>
                                </li>
                                <li class="leftListItem">
                                    <div class="leftListItemTitle">2</div>
                                    <div class="leftListItemCont">
                                        <div class="leftListItemContL">
                                            市生态环境局
                                        </div>
                                        <div class="leftListItemContR">
                                            <p class="contRnum">接入系统数量</p>
                                            <h6>2<span>个</span></h6>
                                        </div>
                                    </div>
                                </li>
                                <li class="leftListItem">
                                    <div class="leftListItemTitle">3</div>
                                    <div class="leftListItemCont">
                                        <div class="leftListItemContL">
                                            市发改委
                                        </div>
                                        <div class="leftListItemContR">
                                            <p class="contRnum">接入系统数量</p>
                                            <h6>2<span>个</span></h6>
                                        </div>
                                    </div>
                                </li>
                                <li class="leftListItem">
                                    <div class="leftListItemTitle1">4</div>
                                    <div class="leftListItemCont">
                                        <div class="leftListItemContL">
                                            市市场监督管理局
                                        </div>
                                        <div class="leftListItemContR">
                                            <p class="contRnum">接入系统数量</p>
                                            <h6>2<span>个</span></h6>
                                        </div>
                                    </div>
                                </li>
                                <li class="leftListItem">
                                    <div class="leftListItemTitle1">5</div>
                                    <div class="leftListItemCont">
                                        <div class="leftListItemContL">
                                            市行政执法局
                                        </div>
                                        <div class="leftListItemContR">
                                            <p class="contRnum">接入系统数量</p>
                                            <h6>2<span>个</span></h6>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="containerLeftB">
                            <div class="ztyx_header_center_text">
                                <div class="ztyx_header_title different_font">
                                    <div class="left_cell"><img src="/static/citybrain/csdn/img/right.png" alt=""></div>
                                    <div class="titletext"
                                        style="font-size: 40px;text-align: center;margin-top: -10px;">数据接入</div>
                                    <div class="right_cell"><img src="/static/citybrain/csdn/img/left.png" alt=""></div>
                                </div>
                            </div>
                            <div class="leftBTitle">
                                <div class="leftBTitleList">
                                    <div class="leftBTitleListImg">
                                        <img src="/static/citybrain/csdn/img/middle/归集数据量.png" alt="">
                                    </div>
                                    <div class="leftBTitleListTex">
                                        <p>归集数据量</p>
                                        <h5>46.3<span>亿条</span></h5>
                                    </div>
                                </div>
                                <div class="leftBTitleList">
                                    <div class="leftBTitleListImg">
                                        <img src="/static/citybrain/csdn/img/middle/开放数据量.png" alt="">
                                    </div>
                                    <div class="leftBTitleListTex">
                                        <p>开放数据量</p>
                                        <h5>2.3<span>亿条</span></h5>
                                    </div>
                                </div>
                                <div class="leftBTitleList">
                                    <div class="leftBTitleListImg">
                                        <img src="/static/citybrain/csdn/img/middle/接入指标项.png" alt="">
                                    </div>
                                    <div class="leftBTitleListTex">
                                        <p>接入指标项</p>
                                        <h5>415<span>项</span></h5>
                                    </div>
                                </div>
                                <div class="leftBTitleList" style="width: 30%;">
                                    <div class="leftBTitleListImg">
                                        <img src="/static/citybrain/csdn/img/middle/城市大脑.png" alt="">
                                    </div>
                                    <div class="leftBTitleListTex">
                                        <p>城市大脑指标模型</p>
                                        <h5>48<span>个</span></h5>
                                    </div>
                                </div>
                            </div>
                            <div class="leftBList">
                                <!-- <div class="leftBListL">
                                    <div class="leftBListTitle">部门数据指标提供数量TOP5</div>
                                </div> -->
                                <div class="bottom" style="width: 670px;">
                                    <div class="title1">
                                        <div class="icon"></div>
                                        <span>部门数据指标提供数量TOP5</span>
                                    </div>
                                    <div class="right_2">
                                        <div class="right_2_0" v-for="(item,i) in data1" :key="i">
                                            <div class="right_2_0_0">
                                                <div class="right_2_0_0_0">{{item.top}}<div>{{item.a}}</div>
                                                    
                                                </div>
                                                <div class="right_2_0_0_1">提供指标数量:{{item.c}}</div>
                                                <!-- <div class="right_2_0_0_1" style="margin-left: 20px;">{{(item.c/13000*100).toFixed(2)+'%'}}
                                                </div> -->
                                            </div>
                                            <div class="right_2_0_1">
                                                <div :style="{backgroundImage: item.d,width:item.c/120*943+'px'}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="leftBListR">
                                    <div class="bottom" style="width: 670px;">
                                        <div class="title1">
    
                                            <div class="icon"></div>
                                            <span>模型调用次数TOP5</span>
                                        </div>
                                        <div class="right_2">
                                            <div class="right_2_0" v-for="(item,i) in data2" :key="i">
                                                <div class="right_2_0_0">
                                                    <div class="right_2_0_0_0">{{item.top}} <div>{{item.a}}</div>
                                                        
                                                    </div>
                                                    <div class="right_2_0_0_1">访问量：{{item.c}}</div>
                                                    <!-- <div class="right_2_0_0_1" style="margin-left: 20px;">{{(item.c/13000*100).toFixed(2)+'%'}}
                                                    </div> -->
                                                </div>
                                                <div class="right_2_0_1">
                                                    <div :style="{backgroundImage: item.d,width:item.c/2000*943+'px'}">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="containerRight">
                        <div class="containerRightT">
                            <div class="ztyx_header_center_text">
                                <div class="ztyx_header_title different_font">
                                    <div class="left_cell"><img src="/static/citybrain/csdn/img/right.png" alt=""></div>
                                    <div class="titletext"
                                        style="font-size: 40px;text-align: center;margin-top: -10px;">应用场景</div>
                                    <div class="right_cell"><img src="/static/citybrain/csdn/img/left.png" alt=""></div>
                                </div>
                            </div>
                            <div class="rightTTitle">
                                <div class="rightTTitleList" style="padding-bottom: 37px;">
                                    <div class="rightTTitleListImg">
                                        <img src="/static/citybrain/csdn/img/middle/接入指标项.png" alt="">
                                    </div>
                                    <div class="rightTTitleListTex">
                                        <p>数字化改革应用总数</p>
                                        <h5>35<span>个</span></h5>
                                    </div>
                                </div>
                                <div class="rightTTitleList" style="padding-bottom: 37px;">
                                    <div class="rightTTitleListImg">
                                        <img src="/static/citybrain/csdn/img/middle/接入指标项.png" alt="">
                                    </div>
                                    <div class="rightTTitleListTex">
                                        <p>数字化改革应用接入数</p>
                                        <h5>2<span>个</span></h5>
                                    </div>
                                </div>
                                <div class="rightTTitleList">
                                    <div class="rightTTitleListImg">
                                        <img src="/static/citybrain/csdn/img/middle/接入指标项.png" alt="">
                                    </div>
                                    <div class="rightTTitleListTex">
                                        <p>当前多跨场景数</p>
                                        <h5>47<span>个</span></h5>
                                    </div>
                                </div>
                                <div class="rightTTitleList">
                                    <div class="rightTTitleListImg">
                                        <img src="/static/citybrain/csdn/img/middle/接入指标项.png" alt="">
                                    </div>
                                    <div class="rightTTitleListTex">
                                        <p>市场累计覆盖部门</p>
                                        <h5>55<span>个</span></h5>
                                    </div>
                                </div>
                            </div>
                            <div class="rightTList">
                                <div class="rightTListItem">
                                    <div class="rightTListItemL">
                                        <p>本市级</p>
                                        <h5>14<span>个</span></h5>
                                    </div>
                                    <div class="rightTListItemR">
                                        <div class="leftBtn"></div>
                                        <div class="rightTListItemRList">
                                            <img src="/static/citybrain/csdn/img/组 2764.png" alt="">
                                        </div>
                                        <div class="rightTListItemRList">
                                            <img src="/static/citybrain/csdn/img/组 2765.png" alt="">
                                        </div>
                                        <div class="rightTListItemRList">
                                            <img src="/static/citybrain/csdn/img/组 2766.png" alt="">
                                        </div>
                                        <div class="rightBtn"></div>
                                    </div>
                                </div>
                                <div class="rightTListItem">
                                    <div class="rightTListItemL">
                                        <p>县（市、区）</p>
                                        <h5>33<span>个</span></h5>
                                    </div>
                                    <div class="rightTListItemR">
                                        <div class="leftBtn"></div>
                                        <div class="rightTListItemRList">
                                            <img src="/static/citybrain/csdn/img/组 2767.png" alt="">
                                        </div>
                                        <div class="rightTListItemRList">
                                            <img src="/static/citybrain/csdn/img/组 2768.png" alt="">
                                        </div>
                                        <div class="rightTListItemRList">
                                            <img src="/static/citybrain/csdn/img/组 2769.png" alt="">
                                        </div>
                                        <div class="rightBtn"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="containerRightB">
                            <div class="ztyx_header_center_text">
                                <div class="ztyx_header_title different_font">
                                    <div class="left_cell"><img src="/static/citybrain/csdn/img/right.png" alt=""></div>
                                    <div class="titletext"
                                        style="font-size: 40px;text-align: center;margin-top: -10px;">系统接口</div>
                                    <div class="right_cell"><img src="/static/citybrain/csdn/img/left.png" alt=""></div>
                                </div>
                            </div>
                            <div class="rightBTitle">
                                <div class="rightBTitleList">
                                    <div class="rightBTitleImg">
                                        <img src="/static/citybrain/csdn/img/middle/接口总数.png" alt="">
                                    </div>
                                    <div class="rightBTitleTex">
                                        <p>接口总数</p>
                                        <h5>820<span>个</span></h5>
                                    </div>
                                </div>
                                <div class="rightBTitleList">
                                    <div class="rightBTitleImg">
                                        <img src="/static/citybrain/csdn/img/middle/接口调用书.png" alt="">
                                    </div>
                                    <div class="rightBTitleTex">
                                        <p>接口调用次数</p>
                                        <h5>52326<span>次</span></h5>
                                    </div>
                                </div>
                            </div>
                            <div class="rightBContent">
                                <div class="rightBContentL">
                                    <div class="echartsImg">
                                        <div id="resident" ref="resident" style="width:300px;height: 300px;"></div>
                                    </div>
                                    <div class="rightBContentLTitle">
                                        <div class="rightBContentLTitleList">
                                            <span class="rightBContentLTitleListline"></span>
                                            <h5>故障接口数<span>811个</span></h5>
                                        </div>
                                        <div class="rightBContentLTitleList">
                                            <span class="rightBContentLTitleListline1"></span>
                                            <h5>正常接口数<span>817个</span></h5>
                                        </div>
                                    </div>
                                </div>
                                <div class="rightBContentR">
                                    <div class="rbListHea">
                                        <div>部门名称</div>
                                        <div>接口数</div>
                                        <div>故障数</div>
                                    </div>
                                    <div class="rbListBod">
                                        <div class="rbListBodList">
                                            <div>市公安局</div>
                                            <div>2个</div>
                                            <div class="rbListBodListNum">13次</div>
                                        </div>
                                        <div class="rbListBodList">
                                            <div>市文明办</div>
                                            <div>1个</div>
                                            <div class="rbListBodListNum">14次</div>
                                        </div>
                                        <div class="rbListBodList">
                                            <div>市生态林业中心</div>
                                            <div>3个</div>
                                            <div class="rbListBodListNum">15次</div>
                                        </div>
                                        <div class="rbListBodList">
                                            <div>市市场监督局</div>
                                            <div>2个</div>
                                            <div class="rbListBodListNum">16次</div>
                                        </div>
                                        <div class="rbListBodList">
                                            <div>市农业农村局</div>
                                            <div>2个</div>
                                            <div class="rbListBodListNum">12次</div>
                                        </div>
                                        <div class="rbListBodList">
                                            <div>市农业农村局</div>
                                            <div>2个</div>
                                            <div class="rbListBodListNum">13次</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    </div>

    <script>
        var vm = new Vue({
            el: '#app',
            data: {
                populationArr: [
                    {
                    title: '系统接口',
                    color: 'rgb(231,96,137)',
                    id: 'flow',
                    data: [{
                        value: 811,
                        name: '故障接口数'
                    }, {
                        value: 817,
                        name: '正常接口数'
                    }]
                    }
                ],
                data1: [{
                        top:'TOP1',
                        a: "市统计局",
                       
                        c: "62",
                        d: "linear-gradient(360deg, #df3c30, #ff9b78)"
                    },
                    {
                        top:'TOP2',
                        a: "市发改委",
                       
                        c: "58",
                        d: "linear-gradient(360deg, #df8f30, #faff78)"
                    },
                    {
                        top:'TOP3',
                        a: "市市场监督管理局",
                       
                        c: "50",
                        d: "linear-gradient(360deg, #304ddf, #7882ff)"
                    },
                    {
                        top:'TOP4',
                        a: "市生态环境局",
                       
                        c: "35",
                        d: "linear-gradient(360deg, #30a9df, #78d5ff)"
                    },
                    {
                        top:'TOP5',
                        a: "市交通局",
                       
                        c: "24",
                        d: "linear-gradient(360deg, #30a9df, #78d5ff)"
                    },],
                data2: [{
                        top:'TOP1',
                        a: "城市人口流动分析",
                       
                        c: "1242",
                        d: "linear-gradient(360deg, #df3c30, #ff9b78)"
                    },
                    {
                        top:'TOP2',
                        a: "车辆识别",
                       
                        c: "1138",
                        d: "linear-gradient(360deg, #df8f30, #faff78)"
                    },
                    {
                        top:'TOP3',
                        a: "影像增强",
                       
                        c: "1087",
                        d: "linear-gradient(360deg, #304ddf, #7882ff)"
                    },
                    {
                        top:'TOP4',
                        a: "空气质量预测",
                       
                        c: "983",
                        d: "linear-gradient(360deg, #30a9df, #78d5ff)"
                    },
                    {
                        top:'TOP5',
                        a: "植被指数计算",
                       
                        c: "963",
                        d: "linear-gradient(360deg, #30a9df, #78d5ff)"
                    },
                ]
            },
            mounted() {
                // document.getElementById("map").style.display = "none";
                this.showPopulationCharts()
            },
            methods: {
                /*
                * 加载人口部分图表
                */
                showPopulationCharts() {
                    this.$nextTick(()=> {
                    let myChart,
                    item = {},
                    option = {}

                    for (let i = 0; i < this.populationArr.length; i++) {

                    item = this.populationArr[i]

                    myChart = echarts.init(this.$refs.resident)

                    let option = {
                        tooltip: {
                        trigger: 'item',
                        formatter: '{b}: {c} ({d}%)',
                        borderWidth: 0,
                        // position: function (point, params, dom, rect, size) {
                        //   return [point[0], '20%'];
                        // },
                        position: 'top',
                        backgroundColor: '#000000',
                        textStyle: {
                            color: 'white',
                            fontSize: '27',
                        }
                        },
                        series: [{
                        name: '系统接口',
                        type: 'pie',
                        radius: ['70%', '80%'],
                        avoidLabelOverlap: false,
                        label: {
                            show: false,
                            position: 'center'
                        },
                        labelLine: {
                            show: false
                        },
                        data: item.data,
                        itemStyle: {
                            normal: {
                            color: function (params) {
                                //自定义颜色
                                var colorList = [
                                    '#ffd461', '#0882d0'
                                ];
                                return colorList[params.dataIndex]
                            }
                            }
                        }
                        }]
                    }

                    myChart.setOption(option)
                    
                    }
                    })
                },

            },
            destroyed() {
                // document.getElementById("map").style.display = "block";
            }

        })
    </script>
</body>

</html>