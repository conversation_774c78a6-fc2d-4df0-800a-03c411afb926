<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>重大危险源分类统计</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <script src="/static/citybrain/csdn/js/drawCircleMap.js"></script>
    <script src="/static/js/jslib/turf.js"></script>
    <style>
      .content {
        display: flex;
        font-size: 30px;
        color: #fff;
        text-align: center;
        align-items: center;
        width: 5500px;
      }
      .box {
        width: 380px;
        height: 100px;
        line-height: 100px;
        border: 1px solid #fff;
        background-color: #051431;
        margin-right: 20px;
      }
      .point {
        cursor: pointer;
        border: 1px solid rgba(32, 77, 201, 0.945);
      }
      .not_allowed {
        cursor: not-allowed;
      }
    </style>
  </head>

  <body>
    <div id="shgl-csaq-zdwxyfltj-bottom">
      <div class="content">
        <div
          class="box"
          v-for="(item,index) in thName"
          :class="click_index==item.name?'point':''"
          @click="openDialod(item.name)"
        >
          {{item.name}}
        </div>
      </div>
    </div>
  </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  var vm = new Vue({
    el: "#shgl-csaq-zdwxyfltj-bottom",
    data: {
      click_index: "",
      thName: [
        {
          name: "风险隐患",
          disabled: true,
        },
        {
          name: "分布展示",
          disabled: true,
        },
        {
          name: "距离聚合",
          disabled: true,
        },
        {
          name: "区划聚合",
          disabled: true,
        },
        {
          name: "风险评估",
          disabled: true,
        },
        {
          name: "专题图",
          disabled: true,
        },
        {
          name: "区划图",
          disabled: true,
        },
        {
          name: "专题制图",
          disabled: true,
        },
        {
          name: "重大危险源事故泄露上图",
          disabled: true,
        },
        {
          name: "气体泄漏模型",
          disabled: true,
        },
        {
          name: "液体泄露模型",
          disabled: true,
        },
        {
          name: "储罐泄漏模型",
          disabled: true,
        },
        {
          name: "管道泄漏模型",
          disabled: true,
        },
        {
          name: "重大危险源事故扩散模型上图",
          disabled: true,
        },
        {
          name: "拉格朗日烟团模型",
          disabled: true,
        },
      ],
    },
    mounted() {
      this.initFun();
    },
    methods: {
      initFun() {},
      openDialod(index) {
        this.click_index = index;
        // 初始化
        top.frames["shgl-csaq-zdwxyfltj-select"].vm.rmAll();
        top.frames["shgl-csaq-zdwxyfltj-select"].vm.rmDraw();

        switch (index) {
          case "风险隐患":
            top.document.getElementById("map").contentWindow.Work.change3D(7);
            top.frames["shgl-csaq-zdwxyfltj-select"].vm.addPoint();
            break;
          case "分布展示":
            top.document.getElementById("map").contentWindow.Work.change3D(7);
            top.frames["shgl-csaq-zdwxyfltj-select"].vm.draw();
            top.frames["shgl-csaq-zdwxyfltj-select"].vm.drawLine();
            top.frames["shgl-csaq-zdwxyfltj-select"].vm.addPoint();
            top.commonObj.funCloseIframe({
              name: "zdwxy_dialog_hot",
            });
            window.parent.postMessage(
              JSON.stringify({
                type: "openIframe",
                name: "zdwxy_dialog_point",
                src:
                  baseURL.url +
                  "/static/citybrain/shgl/commont/zdwxy_dialog_point.html",
                left: "2210px",
                top: "664px",
                width: "310px",
                height: "335px",
                zIndex: "10",
              }),
              "*"
            );
            break;
          case "距离聚合":
            top.document.getElementById("map").contentWindow.Work.change3D(9);
            top.frames["shgl-csaq-zdwxyfltj-select"].vm.add3DText2();
            break;
          case "区划聚合":
            top.document.getElementById("map").contentWindow.Work.change3D(9);
            top.frames["shgl-csaq-zdwxyfltj-select"].vm.add3DText1();
            break;
          case "风险评估":
            top.document.getElementById("map").contentWindow.Work.change3D(7);
            this.addPoint();
            break;
          case "专题图":
            top.document.getElementById("map").contentWindow.Work.change3D(7);
            this.addPoint();
            this.openIframe5("zdwxy-dialog5");
            break;
          case "区划图":
            top.document.getElementById("map").contentWindow.Work.change3D(9);
            this.colorBankuai();
            break;
          case "专题制图":
            top.document.getElementById("map").contentWindow.Work.change3D(9);
            this.closeIframe("zdwxy-dialog5");
            top.frames["zdwxy-dialog3"].vm.showItem = true;
            break;
          case "重大危险源事故泄露上图":
            this.openIframe("zdwxy-dialog2");
            break;
          case "气体泄漏模型":
            this.openIframe("zdwxy-dialog2");
            break;
          case "液体泄露模型":
            this.openIframe("zdwxy-dialog2");
            break;
          case "储罐泄漏模型":
            this.openIframe("zdwxy-dialog2");
            break;
          case "管道泄漏模型":
            this.openIframe("zdwxy-dialog2");
            break;
          case "重大危险源事故扩散模型上图":
            this.openIframe("zdwxy-dialog1");
            break;
          case "拉格朗日烟团模型":
            this.openIframe("zdwxy-dialog1");
            break;
          default:
            top.frames["shgl-csaq-zdwxyfltj-select"].vm.rmAll();
            top.frames["shgl-csaq-zdwxyfltj-select"].vm.closeIframe();
            top.frames["shgl-csaq-zdwxyfltj-select"].vm.close(
              "zdwxy_dialog_point"
            );
            break;
        }
      },
      openIframe(name) {
        let Iframe = {
          type: "openIframe",
          name: name,
          src: baseURL.url + "/static/citybrain/shgl/commont/" + name + ".html",
          left: "2200px",
          top: "850px",
          width: "1030px",
          height: "1034px",
          zIndex: "10",
          argument: {
            status: "zdwxy-dialog",
          },
        };
        window.parent.postMessage(JSON.stringify(Iframe), "*");
      },

      openIframe5(name) {
        let Iframe = {
          type: "openIframe",
          name: name,
          src: baseURL.url + "/static/citybrain/shgl/commont/" + name + ".html",
          left: "6500px",
          top: "220px",
          width: "1015px",
          height: "600px",
          zIndex: "10",
          argument: {
            status: "zdwxy-dialog",
          },
        };
        window.parent.postMessage(JSON.stringify(Iframe), "*");
      },
      closeIframe(name) {
        top.commonObj.funCloseIframe({
          name: name,
        });
      },
      addPoint() {
        let res = [
          {
            title: "浦江县",
            gps_x: "119.**************",
            gps_y: "29.*************",
          },
          {
            title: "兰溪市",
            gps_x: "119.**************",
            gps_y: "29.**************",
          },
          {
            title: "婺城区",
            gps_x: "119.5569204711914",
            gps_y: "29.00677101135254",
          },
          {
            title: "金义新区",
            gps_x: "119.8483056640625",
            gps_y: "29.188559951782227",
          },
          {
            title: "义乌市",
            gps_x: "120.08206787109375",
            gps_y: "29.322123641967773",
          },
          {
            title: "武义县",
            gps_x: "119.7269204711914",
            gps_y: "28.79677101135254",
          },
          {
            title: "永康市",
            gps_x: "120.1469204711914",
            gps_y: "28.97677101135254",
          },
          {
            title: "东阳市",
            gps_x: "120.4169204711914",
            gps_y: "29.24677101135254",
          },
          {
            title: "磐安县",
            gps_x: "120.6299204711914",
            gps_y: "29.06677101135254",
          },
          {
            title: "婺城区",
            gps_x: "119.64896993689013",
            gps_y: "29.090742350540673",
          },
        ];
        let arr = res.map((item) => {
          return {
            data: {
              title: "风险评估报告",
              key: [
                "风险隐患基本信息",
                "风险隐患名称",
                "所属企业",
                "企业地址",
                "风险等级",
                "风险描述",
                "风险承受能力与控制能力分析",
                "风险可能性分析",
                "风险后果分析",
                "风险等级确定",
              ],
              value: [
                " ",
                "化学品泄漏",
                "金华市化工有限公司",
                "金东区阳光路109号",
                "一级",
                "遇明火引发爆炸",
                "承受能力较弱，控制能力一般",
                "可能影响",
                "造成2000人伤亡，12栋楼房坍塌",
                "一级",
              ],
            },
            point: item.gps_x + "," + item.gps_y,
          };
        });
        console.log(arr);
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "pointLoad",
            pointType: "digital-purple", // 点位类型（图标名称）
            pointId: "point", // 点位唯一id
            setClick: true,
            pointData: arr,
            imageConfig: { iconSize: 1 },
            popup: {
              offset: [50, -100],
            },
          })
        );
      },
      colorBankuai() {
        top.document
          .getElementById("map")
          .contentWindow.egs1.contentWindow.map.setPaintProperty(
            "SectionHighlight",
            "fill-extrusion-color",
            [
              "match",
              ["get", "FNAME"],
              "磐安县",
              "#1c6fbd",
              "#00b848",
              ["get", "FNAME"],
              "婺城区",
              "#d05500",
              "#00b848",
            ]
          );
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "3Dtext", //3D文字功能
            textData: [
              // pos文字的位置  //text 展示的文字
              {
                pos: [119.**************, 29.*************, 11000],
                text: "浦江县  \n  (41.5%)",
              },
              {
                pos: [119.**************, 29.**************, 11000],
                text: "兰溪市  \n  (43.0%)",
              },
              {
                pos: [119.5569204711914, 29.00677101135254, 11000],
                text: "婺城区  \n  (28.4%)",
              },
              {
                pos: [119.8483056640625, 29.188559951782227, 11000],
                text: "金义新区  \n  (28.1%)",
              },
              {
                pos: [120.08206787109375, 29.322123641967773, 11000],
                text: "义乌市  \n  (44.8%)",
              },
              {
                pos: [119.7269204711914, 28.79677101135254, 11000],
                text: "武义县  \n  (39.6%)",
              },
              {
                pos: [120.1469204711914, 28.97677101135254, 11000],
                text: "永康市  \n  (38.5%)",
              },
              {
                pos: [120.4169204711914, 29.24677101135254, 11000],
                text: "东阳市  \n  (44.6%)",
              },
              {
                pos: [120.6299204711914, 29.06677101135254, 11000],
                text: "磐安县  \n  (36.6%)",
              },
            ],
            textSize: 40,
            id: "text1",
            // zoomShow: true,
            color: [255, 255, 255, 1],
          })
        );
      },
    },
  });
</script>
