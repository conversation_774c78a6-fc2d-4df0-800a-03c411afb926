<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>水利设施指标分析</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <style>
      #slsszbfx-left {
        width: 1050px;
        height: 1930px;
        background: url("/img/left-bg.png") no-repeat;
        background-size: 100% 100%;
      }
      .fxjcfxBox {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
      }
      .fxjcfx {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30px;
        color: #fff;
        width: 50%;
      }
      .fxjcfx img {
        width: 70px;
        height: 70px;
      }
      .value {
        font-weight: 700;
        color: #0087ec;
      }
      .tabName {
        display: flex;
        font-size: 30px;
        color: #fff;
        justify-content: space-around;
        margin-top: 15px;
      }
      .tabName div {
        cursor: pointer;
      }
      .active {
        color: #0087ec;
      }
      .table {
        font-size: 30px;
        width: 1000px;
        margin: 15px auto 0;
        color: #fff;
        border-collapse: collapse;
        text-align: center;
        border: 1px solid #0087ec;
      }
      .tableBox {
        height: 417px !important;
      }
      .psgl {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30px;
        color: #fff;
        width: 33%;
      }
      .tds {
        display: inline-block;
        width: 100px;
        height: 100%;
        line-height: 80px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    </style>
  </head>

  <body>
    <div id="slsszbfx-left">
      <div class="content">
        <div class="title">
          <nav style="padding: 20px 45px">
            <s-header-title
              style="width: 100%"
              title="防汛监测分析"
              htype="2"
            ></s-header-title>
          </nav>
        </div>
        <div class="fxjcfxBox">
          <div class="fxjcfx" v-for="(item ,index) in fxjcfxList">
            <img :src="`/img/img${index+1}.png`" />
            <div>
              {{item.name}}&emsp;<span class="value">{{item.value}}</span>
            </div>
          </div>
        </div>
        <div class="tabName">
          <div
            v-for="(item, index) in tab"
            :class="{active:isActive===index}"
            @click="change(index)"
          >
            {{item}}
          </div>
        </div>

        <table border class="table tableBox" style="font-size: 22px">
          <thead>
            <tr>
              <th v-for="item in thName">{{item}}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in tableList">
              <td class="tds" :title="item.name">{{item.name}}</td>
              <td>{{item.city}}</td>
              <td>{{item.three}}</td>
              <td>{{item.four}}</td>
              <td>{{item.five}}</td>
              <td>{{item.six}}</td>
              <td>{{item.seven}}</td>
              <td>{{item.eight}}</td>
              <td v-show="isActive==0">{{item.nine}}</td>
              <td v-show="isActive==1">{{item.sshl}}</td>
              <td v-show="isActive==1">{{item.sw}}</td>
              <td v-show="isActive==2">{{item.nine}}</td>
              <td v-show="isActive==2">{{item.cjsj}}</td>
            </tr>
          </tbody>
        </table>
        <div class="title">
          <nav style="padding: 20px 45px">
            <s-header-title
              style="width: 100%"
              title="排水管理"
              htype="2"
            ></s-header-title>
          </nav>
        </div>
        <div class="fxjcfxBox">
          <div class="psgl" v-for="(item ,index) in psglList">
            <div>
              {{item.name}}&emsp;<span class="value">{{item.value}}</span>
            </div>
          </div>
        </div>
        <table border class="table">
          <thead>
            <tr>
              <th v-for="item in thNameOne">{{item}}</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in tableListOne">
              <td>{{item.name}}</td>
              <td>{{item.city}}</td>
              <td>{{item.content}}</td>
              <td>{{item.status}}</td>
              <td>{{item.time}}</td>
            </tr>
          </tbody>
        </table>
        <div class="title">
          <nav style="padding: 20px 45px 0">
            <s-header-title
              style="width: 100%"
              title="易涝点分析"
              htype="2"
            ></s-header-title>
          </nav>
        </div>
        <div style="display: flex">
          <div>
            <div class="title">
              <nav style="padding: 0">
                <s-header-title2
                  style="width: 100%"
                  title="内涝事件区域分布"
                  htype="2"
                ></s-header-title2>
              </nav>
            </div>
            <div id="pieEcharts01" style="height: 450px; width: 525px"></div>
          </div>
          <div>
            <div class="title">
              <nav style="padding: 0">
                <s-header-title2
                  style="width: 100%"
                  title="积水问题事件区域分布"
                  htype="2"
                ></s-header-title2>
              </nav>
            </div>
            <div id="pieEcharts02" style="height: 450px; width: 525px"></div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  var vm = new Vue({
    el: "#slsszbfx-left",
    data: {
      fxjcfxList: [],
      isActive: 0,
      tab: ["雨情信息", "河道水情", "水库水情"],
      thName: [
        "站名",
        "行政区",
        "当日累计雨量",
        "昨日累计雨量",
        "实时雨量",
        "1小时雨量",
        "所属河流",
        "属性",
        "采集时间",
      ],
      thNameOne: ["事件名称", "行政区", "事件内容", "状态", "发生时间"],
      tableList: [],
      psglList: [],
      tableListOne: [],
    },
    mounted() {
      this.initFun();
    },
    methods: {
      initFun() {
        $api("ldst_shgl_slsszbfx", { type: 1 }).then((res) => {
          this.fxjcfxList = res;
        });
        $api("ldst_shgl_slsszbfx", { type: 2 }).then((res) => {
          this.tableList = res.map(item=>{
            return {
              ...item,
              nine: item.nine.replace("05-", "04-").replace("06-", "04-")
            }
          })
        });
        $api("ldst_shgl_slsszbfx", { type: 3 }).then((res) => {
          this.psglList = res;
        });
        $api("ldst_shgl_slsszbfx", { type: 4 }).then((res) => {
          this.tableListOne = res.map(item=>{
            return {
              ...item,
              time: item.time.replace('10-','04-')
            }
          })
        });
        $api("ldst_shgl_slsszbfx", { type: 5 }).then((res) => {
          this.getEcharts01(res);
        });
        $api("ldst_shgl_slsszbfx", { type: 6 }).then((res) => {
          this.getEcharts02(res);
        });
      },
      change(index) {
        this.isActive = index;
        if (this.isActive == 0) {
          this.thName = [
            "站名",
            "行政区",
            "当日累计雨量",
            "昨日累计雨量",
            "实时雨量",
            "1小时雨量",
            "所属河流",
            "属性",
            "采集",
          ];
          $api("ldst_shgl_slsszbfx", { type: 2 }).then((res) => {
            this.tableList = res;
          });
        } else if (this.isActive == 1) {
          this.thName = [
            "站名",
            "行政区",
            "流量",
            "超警戒水位",
            "超保证水位",
            "水位涨幅",
            "属性",
            "采集时间",
            "所属河流",
            "水位",
          ];
          $api("ldst_shgl_slsszbfx", { type: "2-1" }).then((res) => {
            this.tableList = res;
          });
        } else if (this.isActive == 2) {
          this.thName = [
            "站名",
            "行政区",
            "水位",
            "泄洪流量",
            "超汛限水位",
            "超正常蓄水位",
            "水位涨幅",
            "所属河流",
            "属性",
            "采集时间",
          ];
          $api("ldst_shgl_slsszbfx", { type: "2-2" }).then((res) => {
            this.tableList = res;
          });
        }
      },
      getEcharts01(res) {
        let myCharts = echarts.init(document.getElementById("pieEcharts01"));
        let value = res.map((item, index) => {
          return item.value;
        });
        let option = {
          tooltip: {
            trigger: "item",
            textStyle: {
              fontSize: 30,
            },
          },

          series: [
            {
              type: "pie",
              radius: ["50%", "70%"],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: "#2b516f",
                borderWidth: 2,
              },
              label: {
                formatter: "{b}\n{c}",
                textStyle: {
                  fontSize: 30,
                  color: "#fff",
                },
              },
              labelLine: {
                show: true,
                length: 5,
                length2: 5,
              },
              data: res,
            },
          ],
        };
        myCharts.setOption(option);
      },
      getEcharts02(res) {
        let myCharts = echarts.init(document.getElementById("pieEcharts02"));

        let value = res.map((item) => {
          return item.value;
        });
        let max = res.map((item) => item.max);
        let name = res.map((item) => item.name);
        console.log(max);
        let option = {
          normal: {
            top: 200,
            left: 300,
            width: 500,
            height: 400,
            zIndex: 6,
            backgroundColor: "",
          },
          color: ["rgba(245, 166, 35, 1)", "rgba(19, 173, 255, 1)"],

          tooltip: {
            show: true,
            trigger: "item",
            textStyle: {
              fontSize: 30,
            },
          },
          radar: {
            axisName: {
              fontSize: 30,
              color: "#fff",
            },
            center: ["50%", "50%"],
            radius: "70%",
            startAngle: 90,
            splitNumber: 4,
            shape: "circle",
            splitArea: {
              areaStyle: {
                color: ["transparent"],
              },
            },
            axisLabel: {
              show: false,
              fontSize: 30,
              color: "#fff",
              fontStyle: "normal",
              fontWeight: "normal",
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "white", //
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "white", //
              },
            },
            indicator: [
              {
                name: name[0],
                max: max[0],
              },
              {
                name: name[1],
                max: max[1],
              },
              {
                name: name[2],
                max: max[2],
              },
              {
                name: name[3],
                max: max[3],
              },
              {
                name: name[4],
                max: max[4],
              },
              {
                name: name[5],
                max: max[5],
              },

              {
                name: name[6],
                max: max[6],
              },

              {
                name: name[7],
                max: max[7],
              },
            ],
          },
          series: [
            {
              name: "积水问题数量",
              type: "radar",
              symbol: "circle",
              symbolSize: 10,

              itemStyle: {
                color: "#5087ec",
                borderColor: "#5087ec",
                borderWidth: 10,
              },
              lineStyle: {
                normal: {
                  type: "solid",
                  color: "#5087ec",
                  width: 5,
                },
              },
              label: {
                show: true,
                fontSize: 30,
                color: "#5087ec",
              },
              data: [value],
            },
          ],
        };

        myCharts.setOption(option);
      },
    },
  });
</script>
