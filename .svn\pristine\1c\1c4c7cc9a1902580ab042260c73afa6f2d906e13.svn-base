<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数字法治指标分析</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <style>
      * {
        padding: 0;
        margin: 0;
      }
      #szfzzbfx-left {
        width: 1050px;
        height: 1930px;
        background: url("/img/left-bg.png") no-repeat;
        background-size: 100% 100%;
      }
      .value {
        color: #0087ec;
        font-size: 40px;
      }

      .sjcm {
        display: flex;
        font-size: 30px;
        color: #fff;
        justify-content: center;
        align-items: center;
        margin-top: 30px;
        width: 450px;
      }
      .image {
        width: 150px;
        height: 150px;
      }
      .xzcf {
        font-size: 30px;
        margin-top: 80px;
        color: #fff;
        text-align: center;
      }
      .el-input__inner {
        width: 150px;
        font-size: 30px;
        background-color: #2e405a;
        color: #fff;
      }
      .el-scrollbar__wrap {
        background-color: #2e405a;
      }
      .el-select-dropdown__item.hover,
      .el-select-dropdown__item:hover {
        background-color: #2e405a;
      }
      .select {
        float: right;
        right: 50px;
        z-index: 9;
      }
      .el-select-dropdown__item {
        font-size: 30px;
        color: #fff;
      }
    </style>
  </head>

  <body>
    <div id="szfzzbfx-left">
      <div class="content">
        <div class="title">
          <nav style="padding: 20px 45px">
            <s-header-title-2
              style="width: 100%"
              title="政法一体化办案情况"
              htype="2"
            ></s-header-title-2>
          </nav>
        </div>

        <div style="display: flex">
          <div style="width: 100%">
            <div v-for="item in sjcm">
              <div class="sjcm">
                <img class="image" src="../img/img01.png" />
                <div>
                  <div>{{item.name}}</div>
                  <div class="value">
                    {{item.value}}<span>{{item.unit}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="pieEcharts01" style="height: 320px; width: 100%"></div>
        </div>
        <div class="title">
          <nav style="padding: 20px 45px">
            <s-header-title-2
              style="width: 100%"
              title="综合行政执法情况"
              htype="2"
            ></s-header-title-2>
          </nav>
        </div>

        <div style="display: flex">
          <div
            id="pieEcharts02"
            style="height: 320px; width: 100%; flex: 1"
          ></div>
          <div style="flex: 1">
            <div class="xzcf" v-for="item in xzcf">
              <div class="value">{{item.value}}</div>
              <div>{{item.name}}</div>
            </div>
          </div>
        </div>
        <el-select
          class="select"
          v-model="value"
          placeholder="请选择"
          @change="change"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <div id="barEcharts02" style="height: 470px"></div>
        <div id="barEcharts03" style="height: 470px"></div>
      </div>
    </div>
  </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  var vm = new Vue({
    el: "#szfzzbfx-left",
    data: {
      sjcm: [],
      xzcf: [],
      options: [
        {
          value: "7月",
          label: "7月",
        },
        {
          value: "8月",
          label: "8月",
        },
        {
          value: "9月",
          label: "9月",
        },
        {
          value: "10月",
          label: "10月",
        },
      ],
      value: "7月",
    },
    mounted() {
      this.initFun();
      top.document.getElementById("map").contentWindow.Work.funChange(
        JSON.stringify({
          funcName: "rmPoint", //功能名称
        })
      );
      this.initIframe();
      this.initMap();
    },
    methods: {
      initIframe() {
        let iframe4 = {
          type: "openIframe",
          name: "list",
          src: "/static/citybrain3840/szhgg/commont/list.html",
          width: "460px",
          height: "750px",
          left: "1090px",
          top: "230px",
          zIndex: "10",
        };
        window.parent.postMessage(JSON.stringify(iframe4), "*");
      },
      initMap() {
        top.document.getElementById("map").contentWindow.Work.change3D(9);
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "flyto", //功能名称
            flyData: {
              center: [119.98478050597587, 29.18613226366889],
              zoom: 9,
              pitch: 28,
              bearing: 0,
              duration: 4000, //飞行时间（建议加上）
            },
          })
        );
        $get("/textCity.json").then((res) => {
          let textData = [];
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "3Dtext", //功能名称
              textData: res,
              textSize: 35,
            })
          );
        });
      },
      initFun() {
        $get("/3840/szfzzbfx/szfzzbfx01").then((res) => {
          this.sjcm = res;
        });
        $get("/3840/szfzzbfx/szfzzbfx02").then((res) => {
          this.getEcharts01(res);
        });
        $get("/3840/szfzzbfx/szfzzbfx03").then((res) => {
          this.getEcharts02(res);
        });
        $get("/3840/szfzzbfx/szfzzbfx04").then((res) => {
          this.xzcf = res;
        });
        $get("/3840/szfzzbfx/szfzzbfx05-1").then((res) => {
          this.getEcharts03(res);
        });
        $get("/3840/szfzzbfx/szfzzbfx05-2").then((res) => {
          this.getEcharts04(res);
        });

        // $api("ldst_szhgg_szfzzbfx", { type: 1 }).then((res) => {
        //     this.sjcm = res;
        // });
        // $api("ldst_szhgg_szfzzbfx", { type: 2 }).then((res) => {
        //     this.getEcharts01(res);
        // });
        // $api("ldst_szhgg_szfzzbfx", { type: 3 }).then((res) => {
        //     this.getEcharts02(res);
        // });
        // $api("ldst_szhgg_szfzzbfx", { type: 4 }).then((res) => {
        //     this.xzcf = res;
        // });
        // $api("ldst_szhgg_szfzzbfx", { type: 5 }).then((res) => {
        //     this.getEcharts03(res);
        // });
        // $api("ldst_szhgg_szfzzbfx", { type: 6 }).then((res) => {
        //     this.getEcharts04(res);
        // });
      },
      change(item) {
        if (item === "7月") {
          //   $api("ldst_szhgg_szfzzbfx", { type: 5 }).then((res) => {
          //     this.getEcharts03(res);
          //   });
          $get("/3840/szfzzbfx/szfzzbfx05").then((res) => {
            this.getEcharts03(res);
          });
        } else if (item === "8月") {
          //   $api("ldst_szhgg_szfzzbfx", { type: "5-1" }).then((res) => {
          //     this.getEcharts03(res);
          //   });
          $get("/3840/szfzzbfx/szfzzbfx05-1").then((res) => {
            this.getEcharts03(res);
          });
        } else if (item === "9月") {
          //   $api("ldst_szhgg_szfzzbfx", { type: "5-2" }).then((res) => {
          //     this.getEcharts03(res);
          //   });
          $get("/3840/szfzzbfx/szfzzbfx05-2").then((res) => {
            this.getEcharts03(res);
          });
        } else if (item === "10月") {
          //   $api("ldst_szhgg_szfzzbfx", { type: "5-3" }).then((res) => {
          //     this.getEcharts03(res);
          //   });
          $get("/3840/szfzzbfx/szfzzbfx05-3").then((res) => {
            this.getEcharts03(res);
          });
        }
      },

      getEcharts01(res) {
        let myCharts = echarts.init(document.getElementById("pieEcharts01"));
        let option = {
          tooltip: {
            trigger: "item",
            formatter: "{b}\n{c}%",
            textStyle: {
              fontSize: 30,
            },
          },
          legend: {
            orient: "vertical",
            right: "right",
            top: 70,
            textStyle: {
              fontSize: 30,
              color: "#fff",
            },
          },
          series: [
            {
              type: "pie",
              center: ["30%", "50%"],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: "#2b516f",
                borderWidth: 2,
              },
              label: {
                show: false,
              },

              data: res,
            },
          ],
        };
        myCharts.setOption(option);
        tools.loopShowTooltip(myCharts, option, { loopSeries: true });
      },
      getEcharts02(res) {
        let myCharts = echarts.init(document.getElementById("pieEcharts02"));
        let option = {
          tooltip: {
            trigger: "item",
            formatter: "{b}\n{c}%",
            textStyle: {
              fontSize: 30,
            },
          },

          series: [
            {
              type: "pie",
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: "#2b516f",
                borderWidth: 2,
              },
              label: {
                textStyle: {
                  fontSize: 30,
                  color: "#fff",
                },
              },
              labelLine: {
                show: true,
                length: 10,
                length2: 10,
              },

              data: res,
            },
          ],
        };
        myCharts.setOption(option);
        tools.loopShowTooltip(myCharts, option, { loopSeries: true });
      },
      getEcharts03(res) {
        let myCharts = echarts.init(document.getElementById("barEcharts02"));
        let yData = res.map((item) => {
          return item.name;
        });
        let value = res.map((item) => {
          return item.value;
        });

        let option = {
          legend: {
            textStyle: {
              fontSize: 30,
              color: "#fff",
            },
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
            textStyle: {
              fontSize: 30,
            },
          },
          grid: {
            bottom: "20%",
            left: "12%",
            right: "12%",
          },
          yAxis: [
            {
              type: "value",

              splitLine: {
                show: false,
              },
              axisLabel: {
                fontSize: 30,
                color: "#ffff",
              },
            },
          ],

          xAxis: {
            type: "category",
            data: yData,
            offset: 15,
            axisLabel: {
              fontSize: 30,
              color: "#ffff",
            },
          },
          series: [
            {
              name: "处理事项数",
              type: "bar",
              data: value,
            },
          ],
        };
        myCharts.setOption(option);
        tools.loopShowTooltip(myCharts, option, { loopSeries: true });
      },
      getEcharts04(res) {
        let myCharts = echarts.init(document.getElementById("barEcharts03"));
        let yData = res.map((item) => {
          return item.name;
        });
        let value = res.map((item) => {
          return item.value;
        });

        let option = {
          legend: {
            textStyle: {
              fontSize: 30,
              color: "#fff",
            },
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
            textStyle: {
              fontSize: 30,
            },
          },
          grid: {
            bottom: "20%",
            left: "12%",
            right: "12%",
          },
          yAxis: [
            {
              type: "value",

              splitLine: {
                show: false,
              },
              axisLabel: {
                formatter: "{value}%",
                fontSize: 30,
                color: "#ffff",
              },
            },
          ],

          xAxis: {
            type: "category",
            data: yData,
            offset: 15,
            axisLabel: {
              fontSize: 30,
              color: "#ffff",
            },
          },
          series: [
            {
              name: "应用率",
              type: "bar",
              data: value,
            },
          ],
        };
        myCharts.setOption(option);
        tools.loopShowTooltip(myCharts, option, { loopSeries: true });
      },
    },
  });
</script>
