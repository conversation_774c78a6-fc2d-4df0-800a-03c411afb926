<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>领域4-中间地图</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
  </head>
  <style>
    #map-middle {
      position: relative;
      left: 0px;
      top: 0px;
    }
    .btn-con {
      width: 880px;
      height: 70px;
      display: flex;
      justify-content: space-between;
    }
    .btn-con li {
      width: 200px;
      height: 70px;
      line-height: 70px;
      border: 1px solid cornflowerblue;
      list-style: none;
      color: cornflowerblue;
      font-size: 28px;
      text-align: center;
      border-radius: 12px;
      cursor: pointer;
    }
    .active {
      background-color: cornflowerblue !important;
      color: #fff !important;
    }
  </style>

  <body>
    <div id="map-middle" v-cloak>
      <div class="btn-con">
        <li
          v-for="(item,index) in topConData"
          :class="currentIndex===index?'active':''"
          @click="clcikTab(index)"
          :key="index"
        >
          {{item.name}}
        </li>
      </div>
    </div>
  </body>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>

  <script>
    var vm = new Vue({
      el: "#map-middle",
      data() {
        return {
          currentIndex: null,
          topConData: [
            {
              name: "社会安全态势",
              value: 1,
            },
            {
              name: "安全生产态势",
              value: 2,
            },
            {
              name: "自然灾害态势",
              value: 3,
            },
            {
              name: "公共卫生态势",
              value: 4,
            },
          ],
        };
      },
      mounted() {},
      methods: {
        initMap() {
          top.document.getElementById("map").contentWindow.Work.change3D(9);
        },
        clcikTab(index) {
          this.currentIndex = index;

          if (index === 0) {
            this.hotMap();
          } else {
            this.loadHot();
          }
        },
        hotMap() {
          $api("/zt_bsczt_qyrl").then((res) => {
            let hotMapData = [];
            res.map((item, index) => {
              // 画热力图的数据
              let pointArr = [];
              pointArr[0] = Number(item.jd);
              pointArr[1] = Number(item.wd);
              pointArr[2] = item.num;
              pointArr[3] = index + 1;
              hotMapData.push(pointArr);
            });
            const mapData = {
              funcName: "hotPowerMap",
              hotPowerMapData: hotMapData,
              offset: 256,
              heatMapId: "bscztHot",
              threshold: 6000,
              distance: 800,
              alpha: 0.3,
            };
            window.parent.document
              .getElementById("map")
              .contentWindow.Work.funChange(JSON.stringify(mapData));
          });
        },
        loadHot() {
          $api("/cstz_rlt_qx").then((res) => {
            console.log(res);
            let hotMapData = [];

            res.map((ele) => {
              ele.heatmap.map((item) => {
                let pointArr = [];
                pointArr[0] = item.lng;
                pointArr[1] = item.lat;
                pointArr[2] = item.count;
                pointArr[3] = item.geohash;
                hotMapData.push(pointArr);
              });
            });

            const mapData = {
              funcName: "hotPowerMap",
              hotPowerMapData: hotMapData,
              offset: 256,
              heatMapId: "rkztTimeHot",
              threshold: 6000,
              distance: 800,
              alpha: 0.3,
            };
            window.parent.document
              .getElementById("map")
              .contentWindow.Work.funChange(JSON.stringify(mapData));
          });
        },
      },
    });
  </script>
</html>
