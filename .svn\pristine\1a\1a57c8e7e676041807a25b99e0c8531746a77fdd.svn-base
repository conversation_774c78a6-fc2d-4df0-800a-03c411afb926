<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>人口动态指标分析-左</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css">
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
      }
      #app {
        width: 1050px;
        height: 1930px;
        background: url("/img/left-bg.png") no-repeat;
        background-size: 100% 100%;
        padding:30px;
        box-sizing: border-box;
        overflow: hidden;
      }
      .ssrkfx {
        width:100%;
        height:500px;
        margin: 5px 0;
        display: flex;
        flex-wrap: wrap;
      }
      .ssrqhx {
        width:100%;
        height:615px;
        margin: 5px 0;
        display: flex;
        flex-wrap: wrap;
      }
      .ssrqhx>div{
        width:50%;
        height:50%;
      }
      .ssrqhx-title{
        width: 100%;
        font-size: 28px;
        color: #fff;
      }
      .col_blue{
        color: rgb(21 138 211);
      }
      .ldrktj{
        width:100%;
        height:500px;
        margin: 5px 0;
        display: flex;
        flex-wrap: wrap;
      }
      .ldrktj-title{
        width:100%;
        display: flex;
        justify-content: space-around;
        font-size: 24px;
        color: #fff;
      }
      .s-font-28{
        font-size: 28px;
      }
      .button{
        width: 200px;
        height: 40px;
        font-size: 28px;
      }
      .button_active{
        background-color: #108aec;
        color:#fff;
        border: none;
      }

    </style>
  </head>
  <body>
    <div id="app">
      <nav>
        <s-header-title-2 title="实时人口分析" htype="1"></s-header-title>
      </nav>
      <div class="ssrkfx">
        <div id="chart01" style="width:100%;height:150px;"></div>
        <div id="chart02" style="width:50%;height:300px;"></div>
        <div id="chart03" style="width:50%;height:300px;"></div>
      </div>
      <nav>
        <s-header-title-2 title="实时人群画像" htype="1"></s-header-title>
      </nav>
      <div class="ssrqhx">
        <div>
          <div class="ssrqhx-title"><i class="el-icon-caret-right"></i>性别分布</div>
          <div id="chart04" style="width:100%;height:270px;"></div>
        </div>
        <div>
          <div class="ssrqhx-title"><i class="el-icon-caret-right"></i>年龄分布</div>
          <div id="chart05" style="width:100%;height:270px;"></div>
        </div>
        <div>
          <div class="ssrqhx-title"><i class="el-icon-caret-right"></i>消费水平</div>
          <div id="chart06" style="width:100%;height:270px;"></div>
        </div>
        <div> 
          <div class="ssrqhx-title" @click="showChart07=!showChart07" style="cursor: pointer;">
            <i class="el-icon-caret-right"></i>
            <span :class="showChart07?'col_blue':''">兴趣爱好</span> / <span :class="showChart07?'':'col_blue'">学历</span>
          </div>
          <div id="chart07" style="width:495px;height:270px;" v-show="showChart07"></div>
          <div id="chart08" style="width:495px;height:270px;" v-show="!showChart07"></div>
        </div>
      </div>
      <nav>
        <s-header-title-2 title="流动人口统计" htype="1"></s-header-title>
      </nav>
      <div class="ldrktj">
        <div class="ldrktj-title">
          <div v-for="(item,index) in titleList">
            <span>{{item.name}}</span>
            <span class="s-font-28">{{item.value + "万人"}}</span>
          </div>
        </div>
        <div style="width:50%;height:450px">
          <div style="display: flex;justify-content: space-evenly;">
            <button class="button" :class="showChart09?'button_active':''" @click="showChart09=true">省内流入</button>
            <button class="button" :class="showChart09?'':'button_active'" @click="showChart09=false">省外流入</button>
          </div>
          <div id="chart09" style="width:495px;height:400px;" v-show="showChart09"></div>
          <div id="chart10" style="width:495px;height:400px;" v-show="!showChart09"></div>
        </div>
        <div style="width:50%;height:450px">
          <div id="chart11" style="width:100%;height:100%;"></div>
        </div>
      </div>
    </div>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
      let vm = new Vue({
        el: "#app",
        data: {
          showChart07:true,
          showChart09:true,
          titleList:[],
        },
        mounted() {
          this.init()
          this.openIframe()
        },
        methods: {
          init(){
            $api('ldst_shgl_rkdt',{type:1}).then(res => {
              this.getChart01('chart01',res)
            })
            $api('ldst_shgl_rkdt',{type:2}).then(res => {
              this.pieEchart("chart02",res)
            })
            $api('ldst_shgl_rkdt',{type:3}).then(res => {
              this.pieEchart("chart03",res)
            })
            $api('ldst_shgl_rkdt',{type:4}).then(res => {
              this.pieEchart("chart04",res)
            })
            $api('ldst_shgl_rkdt',{type:5}).then(res => {
              this.barEchart("chart05",res)
            })
            $api('ldst_shgl_rkdt',{type:6}).then(res => {
              this.barEchart("chart06",res)
            })
            $api('ldst_shgl_rkdt',{type:7}).then(res => {
              this.pieEchart("chart07",res)
            })
            $api('ldst_shgl_rkdt',{type:8}).then(res => {
              this.pieEchart("chart08",res)
            })
            $api('ldst_shgl_rkdt',{type:9}).then(res => {
              this.titleList = res
            })
            $api('ldst_shgl_rkdt',{type:10}).then(res => {
              this.pieEchart("chart09",res)
            })
            $api('ldst_shgl_rkdt',{type:11}).then(res => {
              this.pieEchart("chart10",res)
            })
            $api('ldst_shgl_rkdt',{type:12}).then(res => {
              this.lineEchart("chart11",res)
            })
          },

          getChart01(id, chartData){
            const myEc = echarts.init(document.getElementById(id))
            var total = 40000; // 数据总数
            var datas = [];
            chartData.forEach((item) => {
              datas.push(item.value);
            });
            option = {
              xAxis: {
                max: total,
                splitLine: {
                  show: false,
                },
                axisLine: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
              },
              grid: {
                left: "20%",
                top: "5%", // 设置条形图的边距
                right: "15%",
                bottom: "5%",
              },
              yAxis: [
                {
                  type: "category",
                  inverse: false,
                  data: chartData,
                  axisLine: {
                    show: false,
                  },
                  axisTick: {
                    show: false,
                  },
                  axisLabel: {
                    show: false,
                  },
                },
              ],
              series: [
                {
                  // 内
                  type: "bar",
                  barWidth: 18,
                  legendHoverLink: false,
                  silent: true,
                  itemStyle: {
                    normal: {
                      color: function (params) {
                        var color = {
                            type: "linear",
                            x: 0,
                            y: 0,
                            x2: 1,
                            y2: 0,
                            colorStops: [
                              {
                                offset: 0,
                                color: "#1588D1", // 0% 处的颜色
                              },
                              {
                                offset: 1,
                                color: "#0F4071", // 100% 处的颜色
                              },
                            ],
                          };
                        return color;
                      },
                    },
                  },
                  label: {
                    normal: {
                      show: true,
                      position: "left",
                      formatter: "{b}",
                      textStyle: {
                        color: "#fff",
                        fontSize: 24,
                      },
                    },
                  },
                  data: chartData,
                  z: 1,
                  animationEasing: "elasticOut",
                },
                {
                  // 分隔
                  type: "pictorialBar",
                  itemStyle: {
                    normal: {
                      color: "#061348",
                    },
                  },
                  symbolRepeat: "fixed",
                  symbolMargin: 6,
                  symbol: "rect",
                  symbolClip: true,
                  symbolSize: [1, 21],
                  symbolPosition: "start",
                  symbolOffset: [1, -1],
                  symbolBoundingData: this.total,
                  data: chartData,
                  z: 2,
                  animationEasing: "elasticOut",
                },
                {
                  // 外边框
                  type: "pictorialBar",
                  symbol: "rect",
                  symbolBoundingData: total,
                  itemStyle: {
                    normal: {
                      color: "none",
                    },
                  },
                  label: {
                    normal: {
                      formatter: (params) => {
                        return "{a|" + params.data + "万人}";
                      },
                      rich: {
                        a: {
                          color: "#ffffff",
                          fontSize: 24,
                        },
                      },
                      position: "right",
                      distance: 10, // 向右偏移位置
                      show: true,
                      textStyle: {
                        color: "#fff",
                        fontSize: 24,
                      },
                    },
                  },
                  data: datas,
                  z: 0,
                  animationEasing: "elasticOut",
                },
                {
                  name: "外框",
                  type: "bar",
                  barGap: "-120%", // 设置外框粗细
                  data: [
                    total,
                    total,
                  ],
                  barWidth: 25,
                  itemStyle: {
                    normal: {
                      color: "transparent", // 填充色
                      barBorderColor: "#1C4B8E", // 边框色
                      barBorderWidth: 1, // 边框宽度
                      // barBorderRadius: 0, //圆角半径
                      label: {
                        // 标签显示位置
                        show: false,
                        position: "top", // insideTop 或者横向的 insideLeft
                      },
                    },
                  },
                  z: 0,
                },
              ],
            };
            myEc.setOption(option);
          },

          pieEchart(id, pieData){
            const myEc = echarts.init(document.getElementById(id))
            var fontColor = "#30eee9";
            let option = {
              grid: {
                  top: "10%",
                  bottom: "0%",
                  containLabel: true,
              },
              tooltip: {
                trigger: "item",
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                  textStyle: {
                      color: "white",
                      fontSize: "24",
                  },
              },
              color: ["#EE752F","#5087EC", "#68BBC4", "#58A55C", "#F2BD42" ],
              legend: {
                show:false,
              },
              series: [
                {
                  name: "交通事故成因",
                  type: "pie",
                  radius: ["45%", "70%"],
                  center: ["50%", "50%"],
                  data: pieData,
                  itemStyle: {
                      color: '#fff',
                      emphasis: {
                          shadowBlur: 10,
                          shadowOffsetX: 0,
                          shadowColor: "rgba(0, 0, 0, 0.5)",
                      },
                  },
                  itemStyle: {
                      normal: {
                          label: {
                              show: true,
                              color: '#fff',
                              fontSize:"24",
                              formatter: "{b}:\n{d}%",
                          },
                      },
                      labelLine: { show: true },
                  },
                },
              ],
            };
            myEc.setOption(option);
          },

          barEchart(id, barData){
            const myEc = echarts.init(document.getElementById(id))
            let yData = barData.map((item) => {
                return item.name;
            });
            let value = barData.map((item) => {
                return item.value;
            });
            let option = {
                // tooltip: {
                //     trigger: "axis",
                //     axisPointer: {
                //         type: "shadow",
                //     },
                //     textStyle: {
                //         fontSize: 24,
                //     },
                // },
                grid: {
                    top: "5%",
                    bottom: "15%",
                    left: "30%",
                },
                xAxis: {
                    type: "value",

                    splitLine: {
                        show: false,
                    },
                    axisLabel: {
                        fontSize: 24,
                        color: "#ffff",
                    },
                },
                yAxis: {
                    type: "category",
                    data: yData,
                    axisLabel: {
                        fontSize: 24,
                        color: "#ffff",
                    },
                },
                series: [
                    {
                        name: "高频事件",
                        type: "bar",
                        data: value,
                        label: {
                          normal: {
                            show: true,
                            position: "inside",
                            formatter: "{c}%",
                            textStyle: {
                              color:"#fff",
                              fontSize: 20,
                            },
                          },
                        },
                    },
                ],
            };
            myEc.setOption(option);
          },

          lineEchart(id, lineData){
            const myEc = echarts.init(document.getElementById(id))
            let datax = [],datay1 = [],datay2 = [],datay3 = [];
            lineData.map((ele) => {
              datax.push(ele.name);
              datay1.push(ele.lr);
              datay2.push(ele.lc);
              datay3.push(ele.jld);
            });
            let option = {
              title: {
                show: false,
                text: "用电量",
              },
              tooltip: {
                trigger: "axis",
              },
              legend: {
                show: true,
                x: "center",
                y: "10",
                itemWidth: 40,
                itemHeight: 20,
                textStyle: {
                    color: "#fff",
                    fontSize:"28",
                },
              },
              grid: {
                left: "3%",
                right: "4%",
                bottom: "8%",
                containLabel: true,
              },
              tooltip: {
                trigger: "axis",
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                axisPointer: {
                  lineStyle: {
                    color: "rgba(11, 208, 241, 1)",
                    type: "slider",
                  },
                },
                textStyle: {
                  color: "rgba(212, 232, 254, 1)",
                  fontSize: 28,
                },
              },
              xAxis: [
                {
                  type: "category",
                  offset: 20,
                  axisLine: {
                    //坐标轴轴线相关设置。数学上的x轴
                    show: true,
                    lineStyle: {
                      color: "rgba(108, 166, 219, 0.3)",
                    },
                  },
                  axisLabel: {
                    //坐标轴刻度标签的相关设置
                    // rotate: -30,
                    textStyle: {
                      color: "#fff",
                      fontSize: 24,
                    },
                  },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      color: "#192a44",
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  data: datax,
                },
              ],
              yAxis: [
                {
                  name: "",
                  min: (value) => {
                    return parseInt(value.min - 1);
                  },
                  nameTextStyle: {
                    fontSize: 24,
                    color: "#D6E7F9",
                    padding: [0, 20, 10, 0],
                  },
                  splitLine: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.1,
                      width: 2,
                    },
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.5,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 24,
                      color: "#D6E7F9",
                    },
                  },
                  axisLine: {
                    show: false,
                    lineStyle: {
                      color: "#233653",
                    },
                  },
                },
              ],
              series: [
                {
                  name: "流入",
                  type: "line",
                  symbolSize:10,
                  itemStyle: {
                    normal: {
                      // color: "#3A84FF",
                      lineStyle: {
                        // color: "#1b759c",
                        width: 4,
                      },
                    },
                  },
                  data: datay1,
                },
                {
                  name: "流出",
                  type: "line",
                  symbolSize:10,
                  itemStyle: {
                    normal: {
                      // color: "#3A84FF",
                      lineStyle: {
                        // color: "#1b759c",
                        width: 4,
                      },
                    },
                  },
                  data: datay2,
                },
                {
                  name: "净流入",
                  type: "line",
                  symbolSize:10,
                  itemStyle: {
                    normal: {
                      // color: "#3A84FF",
                      lineStyle: {
                        // color: "#1b759c",
                        width: 4,
                      },
                    },
                  },
                  data: datay3,
                }
              ],
            };
            myEc.setOption(option);
          },

          openIframe() {
            let Iframe = {
                type: "openIframe",
                name: 'rkdt-select',
                src: baseURL.url + "/static/citybrain3840/shgl/pages/rkdtzbfx/rkdtzbfx-select.html",
                left: "1070px",
                top: "230px",
                width: "240px",
                height: "270px",
                zIndex: "10",
                argument: {
                    status: "rkdt-select",
                },
            };
            window.parent.postMessage(JSON.stringify(Iframe), "*");
            let Iframe1 = {
              type: "openIframe",
              name: 'rkdtzbfx_kx',
              src: baseURL.url + "/static/citybrain3840/shgl/pages/rkdtzbfx/rkdtzbfx_kx.html",
              left: "1340px",
              top: "230px",
              width: "300px",
              height: "100px",
              zIndex: "10",
              argument: {
                  status: "rkdtzbfx_kx",
              },
            };
            window.parent.postMessage(JSON.stringify(Iframe1), "*");
          },

          closeIframe() {
              top.commonObj.funCloseIframe({
                  name: "rkdt-select",
              });
          },
        },
      });
    </script>
  </body>
</html>
