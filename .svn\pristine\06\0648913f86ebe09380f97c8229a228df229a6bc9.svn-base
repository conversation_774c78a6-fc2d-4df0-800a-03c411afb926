let ccjgColors = ["#62ccf9", "#2266b3"];

let ccjgOption = {
    // 提示框
    tooltip: {
        trigger: "axis",
        axisPointer: {
            //坐标轴指示器
            type: "line",
            lineStyle: {
                type: "solid",
            },
        },
        borderWidth: 0,
        borderRadius: 5,
        padding: 15,
        backgroundColor: "#384c63",
        textStyle: {
            color: "#d0e1f5",
            fontSize: 40,
        },
        formatter: "{b}: {c}个",
    },

    //图表外边距
    grid: {
        left: 150,
        top: 80,
        right: 80,
        bottom: 50,
    },

    //横轴
    xAxis: [
        {
            type: "category",
            data: [],
            axisLine: {
                //横轴线
                show: true,
                lineStyle: {
                    color: "rgba(119,179,241,0.5)",
                    width: 1,
                },
            },
            axisLabel: {
                //横轴标签
                inside: false,
                textStyle: {
                    color: "#d6e7f9",
                    fontSize: 32,
                },
                margin: 20,
                interval: 0,
            },
            axisTick: {
                //刻度
                show: false,
            },
        },
    ],
    //纵轴
    yAxis: [
        {
            type: "value",
            name: "单位：个", //纵轴名称
            nameGap: 30,
            nameTextStyle: {
                color: "#d6e7f9",
                fontSize: 32,
            },

            position: "left",
            axisLine: {
                //纵轴线
                show: true,
                lineStyle: {
                    color: "rgba(119,179,241,0.5)",
                    width: 1,
                },
            },
            axisLabel: {
                //纵轴线标签
                formatter: "{value}",
                textStyle: {
                    color: "#d6e7f9",
                    fontSize: 32,
                },
                margin: 20,
            },
            splitLine: {
                //分隔线
                show: true,
                lineStyle: {
                    color: "rgba(119,179,241,0.1)",
                    width: 1,
                },
            },
        },
    ],
    //内容
    series: [
        {
            name: "特种设备总数",
            type: "bar",
            barWidth: 54,
            yAxisIndex: 0,
            data: [],
            itemStyle: {
                //柱状条样式
                barBorderRadius: [5, 5, 0, 0],
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                        offset: 0,
                        color: ccjgColors[0],
                    },
                    {
                        offset: 1,
                        color: ccjgColors[1],
                    },
                ]),
            },
            emphasis: {
                //悬浮选中效果
                itemStyle: {
                    color: ccjgColors[1],
                },
            },
        },
    ],
};
