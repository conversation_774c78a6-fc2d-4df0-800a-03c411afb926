<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>金东区办学申报情况</title>
        <script src="/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain/ggfw/css/ggfw-bxzc-dialog.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <!-- 轮播toolTip -->
        <style>
            ul li {
                list-style: none;
                font-size: 40px;
                color: #fff;
                line-height: 60px;
            }
            .wsgz {
                display: flex;
                justify-content: space-around;
                font-size: 40px;
                color: #cc915e;
            }

            .bxzc {
                margin-left: 45px;
            }
        </style>
    </head>

    <body>
        <div id="app" class="container">
            <div class="head">
                <span>{{titleName}}办学申报情况</span>
                <div class="img" @click="closeDialog"></div>
            </div>
            <div class="content">
                <div style="width: 100%; padding: 30px">
                    <ul>
                        <li v-for="item in bxzcList">
                            <img src="/static/citybrain/ggfw/img/list.png" />
                            {{item.name}} : &ensp;<span class="valueName">{{item.value}}</span>
                        </li>
                    </ul>
                    <div class="valueName bxzc">办学条件发展指数近五年</div>
                    <div id="lineEcharts002" style="width: 100%; height: 300px"></div>
                </div>
            </div>
        </div>
    </body>
    <script type="module">
        new Vue({
            el: "#app",
            data: {
                bxzcList: [],
                titleName:'',
            },
            //项目生命周期
            mounted() {
                var that = this
                window.addEventListener("message", function (e) {
                    let info = e.data;
                    let title = info.title
                    if(info.status == 'mapDiaog'){
                        that.titleName = title
                        that.init(title);
                    }
                });
            },
            methods: {
                closeDialog() {
                    let data = JSON.stringify({
                        type: "closeIframe",
                        name: "mapDiaog",
                    });
                    top.window.parent.postMessage(data, "*");
                },
                init(title) {
                    $get("/ggfw/index/jyfw-dialog01").then((res) => {
                        this.bxzcList = res.filter((item)=>{ return item.typeName == title });
                    });
                    $get("/ggfw/index/jyfw-dialog02").then((res) => {
                        let result = res.filter((item)=>{ return item.typeName == title });
                        this.getEcharts02("lineEcharts002", result);
                    });
                },
                getEcharts02(dom, echartData) {
                    let echarts1 = echarts.init(document.getElementById(dom));

                    const xAxisData = echartData.map((item) => {
                        return item.time;
                    });
                    const yData = echartData.map((item) => {
                        return item.value;
                    });

                    let option = {
                        textStyle: {
                            fontFamily: "Din-Light",
                        },

                        tooltip: {
                            backgroundColor: "rgba(50,50,50,0.7)",
                            borderColor: "rgba(50,50,50,0.7)",
                            trigger: "axis",
                            formatter: "{b} : {c}",
                            axisPointer: {
                                type: "none",
                            },
                            textStyle: {
                                color: "#ffff",
                                lineHeight: 28,
                                fontSize: 30,
                            },
                            confine: true,
                            padding: 12,
                        },

                        xAxis: {
                            type: "category",
                            boundaryGap: true,
                            offset: 5,
                            data: xAxisData,
                            axisLabel: {
                                interval: 0,
                                align: "left",
                                color: "#fff",
                                fontSize: 30,
                            },
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                        },
                        yAxis: {
                            type: "value",
                            splitNumber: 5,
                            axisLabel: {
                                color: "#fff",
                                fontSize: 30,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "#2d4e6c",
                                },
                            },
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                        },
                        series: [
                            {
                                data: yData,
                                type: "line",
                                smooth: true,
                                smoothMonotone: "x",
                                cursor: "pointer",
                                showSymbol: false,
                                lineStyle: {
                                    shadowColor: "rgba(18,61,172,0.5)",
                                    color: "#0398d1",
                                    shadowBlur: 10,
                                },
                            },
                        ],
                    };
                    echarts1.setOption(option);
                    tools.loopShowTooltip(echarts1, option, { loopSeries: true });
                },
            },
        });
    </script>
</html>
