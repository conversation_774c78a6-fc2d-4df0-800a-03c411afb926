[v-cloak] {
    display: none;
  }
  
  * {
    margin: 0;
    padding: 0;
  }
  #shfx-middle{
    position: relative;
    left: 2160px;
    top: 230px;
  }
  .tree{
    position: relative;
    width:385px;
    height: 300px;
    padding:30px;
    box-sizing: border-box;
    background: linear-gradient(179deg, #0e1a40 0%, #064069 100%);
  }
  .auth-tree{
    background: none;
  }
  .node-lable{
    line-height: 3.125rem;
    font-size: 30px;
    font-family: PangMenZhengDao;
    font-weight: bold;
    color: #00ddd5;
    line-height: 58px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
    background-color: #00ddd5;
    border-color: #00d4cf;
  }
  .node-img{
    position: relative;
    left: 20px;
    top: 10px;
    width: 40px;
  }
  
  .el-tree-node__expand-icon{
    display: none;
  }
  .el-tree-node.is-current > .el-tree-node__content, .el-tree-node__content:hover {
    background: linear-gradient(94deg,rgba(3, 97, 156, 0) 0%, #03619c 100%) !important;
    border-radius: 0px 30px 30px 0px;
  }
  .el-tree-node__content{
    height:50px;
  }
  .el-checkbox__inner{
    width:20px;
    height:20px;
  }
  .el-checkbox__inner::after{
    border:3px #fff solid;
    width:7px;
    height:14px;
    left:3px;
    top:-2px;
    border-left: 0;
    border-top: 0
  }
  .el-checkbox__input.is-indeterminate .el-checkbox__inner::before{
    height: 4px;
    top: 8px;
  }

  .el-tree-node.is-expanded>.el-tree-node__children{
    position: absolute;
    left: 337px;
    top: -20px;
    background: linear-gradient(179deg, #0e1a40 0%, #064069 100%);
    width: 300px;
    height: 250px;
    padding: 20px;
    border-left: 1px solid #fff;
  }
  