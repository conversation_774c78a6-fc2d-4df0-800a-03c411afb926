<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>交通运输-弹框3</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
  </head>
  <style>
    [v-cloak] {
      display: none;
    }

    html,
    body,
    ul,
    p {
      padding: 0;
      margin: 0;
      list-style: none;
    }

    .container {
      width: 640px;
      height: 80px;
      box-sizing: border-box;
      /* padding: 20px; */
      background: url("/img/left-bg.png") no-repeat;
      background-size: 100% 100%;
      display: flex;
    }

    .select {
    }

    .el-input__inner {
      font-size: 28px;
      width: 180px;
      height: 80px;
      color: #fff;
      background-color: #011040b3;
    }

    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-color: #011040b3;
    }

    .el-input__icon {
      line-height: 65px;
    }

    .el-select-dropdown {
      background-color: #011040b3;
    }

    .el-select-dropdown__item {
      font-size: 30px;
      color: #fff;
    }

    .el-select .el-input .el-select__caret {
      font-size: 30px;
    }

    .inputbox {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 460px;
      border: #fff solid 1px;
    }

    .input {
      width: 200px;
      height: 80px;
      font-size: 28px;
      border: none;
      background-color: transparent;
      color: #fff;
      padding: 0;
    }
    .searchbutton {
      width: 190px;
      height: 60px;
      background-color: #24c6e4;
      border-radius: 10px;
      padding-top: 10px;
      box-sizing: border-box;
      font-size: 30px;
      text-align: center;
    }
  </style>

  <body>
    <div id="app" class="container" v-cloak>
      <div class="select">
        <el-select v-model="value" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div class="inputbox">
        <img src="../img/jtys-search.png" width="50px" />
        <input
          class="input"
          type="text"
          placeholder="请选择"
          v-model="value2"
        />
        <div class="searchbutton" @click="serchMap">搜索</div>
      </div>
    </div>
  </body>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>

  <script>
    var vm = new Vue({
      el: "#app",
      data() {
        return {
          value: 1,
          value2: "",
          options: [
            { value: 1, label: "线路" },
            { value: 2, label: "公交车" },
          ],
          currentType: "4",
          historyPathList: [
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.676474584733, 29.0867177339493],
            [119.66228576613, 29.0751559630451],
            [119.653665401965, 29.0651249113103],
            [119.639227190661, 29.0470822310761],
            [119.638172485028, 29.0465303666767],
            [119.638172485028, 29.0465303666767],
            [119.638172485028, 29.0465303666767],
            [119.638172485028, 29.0465303666767],
            [119.638172485028, 29.0465303666767],
            [119.638172485028, 29.0465303666767],
            [119.638172485028, 29.0465303666767],
            [119.638172485028, 29.0465303666767],
            [119.638172485028, 29.0465303666767],
            [119.638172485028, 29.0465303666767],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
            [119.639871336806, 29.0468799953076],
          ],
        };
      },
      mounted() {},
      methods: {
        serchMap() {
          console.log(this.value);
          if (this.value === 1) {
            this.drawLine();
          } else {
            this.getStation();
          }
        },
        //根据点击type查询点位
        getStation() {
          this.rmPoint();
          $api("shgl_doing_jtysdw").then((res) => {
            console.log(res);
            let data;
            data = res.filter((item) => {
              return item.type === this.currentType;
            });
            console.log(data);
            let pointData = [];
            data.forEach((item) => {
              let str = {
                data: {
                  pointId: "gjc",
                  obj: item,
                },
                point: item.lnglat,
              };
              pointData.push(str);
            });
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "pointLoad", //功能名称
                pointType: "point-公交站", //点位类型图标
                pointId: "00000" + this.currentType,
                setClick: true,
                pointData: pointData,
                imageConfig: { iconSize: 1 },
                size: [0.01, 0.01, 0.01, 0.01],
                popup: {
                  offset: [50, -100],
                },
              })
            );
          });
        },
        drawLine() {
          const imgIcon = `${baseURL.url}/static/citybrain/tckz/img/tckz_gj/公交车.png`;
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "createTrajectory",
              data: {
                id: "TDT_TITLE_ID",
                type: "dynamic",
                icon: imgIcon,
                coordinates: this.historyPathList,
                iconStyle: {
                  "icon-size": 0.7,
                  "icon-rotate": 360,
                },
                style: {
                  "line-width": 10,
                },
                isGlowLine: false,
                isBezierCurve: false,
                color: "#e86056 ",
                loop: false,
                steps: 100,
              },
            })
          );
        },
        rmPoint() {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmPoint",
              pointId: "", //传id清除单类，不传清除所有
            })
          );
          if (
            top.document.getElementById("map").contentWindow.egs1.contentWindow
              .map.TDT_TITLE_ID
          ) {
            top.document
              .getElementById("map")
              .contentWindow.egs1.contentWindow.map.TDT_TITLE_ID.remove();
          }
        },
      },
    });
  </script>
</html>
