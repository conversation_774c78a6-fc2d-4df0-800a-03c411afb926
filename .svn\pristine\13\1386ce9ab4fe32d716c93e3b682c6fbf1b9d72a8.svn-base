<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title></title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/citybrain/scjg/css/hjbh-right/common.css" />
  <script src="/Vue/vue.js"></script>
  <script src="/static/citybrain/hjbh/js/echarts.js"></script>
  <script src="/static/js/jslib/Emiter.js"></script>
  <script src="/static/js/jslib/vue-count-to.min.js"></script>
  <link rel="stylesheet" href="/elementui/css/elementui.css" />
  <script src="/elementui/js/elementui.js"></script>

  <style>
    * {
      margin: 0;
      padding: 0;
    }

    .sjzx_middle_left {
      /* position: absolute;
        top: 0;
        left: 48px; */
      width: 440px;
      min-height: 120px;
      /* background: linear-gradient(179deg, #0e1a40 0%, #064069 100%); */
      /* box-shadow: 0px 7px 50px 0px rgba(35, 154, 228, 0.56); */
      /* opacity: 0.85; */
      border-radius: 10px;
    }

    .sjzx_middle_title {
      font-size: 36px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #d6e7f9;
      background: linear-gradient(180deg,
          #aed6ff 0%,
          #74b8ff 47.4853515625%,
          #9ccfff 50%,
          #ddeeff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
    }

    .sjzx_middle_title p {
      margin-top: 10px;
      height: 82px;
      line-height: 83px;
      white-space: nowrap;
    }

    .sjzx_middle_title p:before {
      content: "";
      height: 1px;
      top: -3%;
      position: relative;
      width: 8%;
      height: 1px;
      border-bottom: 3px solid #74b8ff;
      display: inline-block;
      margin-right: 5px;
      margin-bottom: 12px;
    }

    .sjzx_middle_title p:after {
      content: "";
      top: -3%;
      position: relative;
      width: 8%;
      height: 1px;
      border-bottom: 3px solid #74b8ff;
      display: inline-block;
      margin-left: 5px;
      margin-bottom: 12px;
    }

    .sjzx_middle_title .before {
      display: inline-block;
      width: 10px;
      height: 10px;
      background-color: #74b8ff;
      /* transform: rotateZ(90deg); */
      border-radius: 5px;
      margin-bottom: 10px;
    }

    .sjzx_middle_title .after {
      /* display: inline-block; */
      display: inline-block;
      width: 10px;
      height: 10px;
      background-color: #74b8ff;
      /* transform: rotateZ(90deg); */
      border-radius: 5px;
      margin-bottom: 10px;
    }

    .sjzx_middle_title p .tab {
      cursor: pointer;
    }

    .el-checkbox__input {
      float: right;
      margin-right: 30px;
    }

    .el-tree-node__label {
      font-size: 30px;
      font-family: PangMenZhengDao;
      font-weight: bold;

      color: #c0d6ed;
      line-height: 58px;
    }

    .el-tree-node__content {
      height: 50px !important;
      margin-bottom: 10px;
      padding: 0 !important;
    }

    .is-focusable {
      background-color: unset;
    }

    /* .el-tree-node__expand-icon.expanded {
        display: block;
      } */
    .el-tree-node__content>.el-tree-node__expand-icon {
      /* display: none; */
    }

    /* .el-tree--highlight-current
        .el-tree-node.is-current
        > .el-tree-node__content {
        background-color: unset;
      }
      .el-tree-node__content:hover,
      .el-tree > .el-tree-node.is-current {
        background: linear-gradient(
          94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%
        ) !important;
        border-radius: 0px 30px 30px 0px;
      }

      .el-tree-node.is-focusable.is-checked {
        background: linear-gradient(
          94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%
        ) !important;
        border-radius: 0px 30px 30px 0px;
      } */

    .el-tree-node.is-current>.el-tree-node__content,
    .el-tree-node__content:hover {
      background-color: transparent;
      /* background: linear-gradient(
          94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%
        ) !important;
        border-radius: 0px 30px 30px 0px; */
    }

    .el-checkbox {
      /* position: absolute;
        right: 0; */
      display: block;
      border-radius: 15px;
      margin-bottom: 20px;
      margin-right: 0;
    }

    .el-checkbox-group .el-checkbox:hover {
      background: linear-gradient(94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%) !important;
      border-radius: 0px 30px 30px 0px;
    }

    .el-checkbox-group .is-checked {
      background: linear-gradient(94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%) !important;
      border-radius: 0px 30px 30px 0px;
    }

    .el-checkbox__label {
      font-size: 30px;
      font-family: PangMenZhengDao;
      font-weight: bold;
      /* font-style: italic; */
      color: #c0d6ed;
      line-height: 58px;
    }

    .el-checkbox__inner {
      width: 33px;
      height: 33px;
      margin-top: 15px;
      background-color: #344d67;
    }

    .auth-tree .el-checkbox__inner {
      width: 33px;
      height: 33px;
      margin-top: 21px;
      /* background-color: #344d67; */
      border-radius: 10px;
    }

    .sjzx_middle_left_container {
      /* padding: 30px 20px 0 40px; */
    }

    .checkbox-box-img {
      width: 30px;
      height: 42px;
      position: relative;
      top: 10px;
    }

    /* .checkbox-box >span{
        position: relative;
        top:-10px;
      } */
    /* .el-checkbox.is-checked {
        background: linear-gradient(
          94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%
        );
        border-radius: 0px 30px 30px 0px;
      } */
    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #252316;
      border-color: #ffc561;
    }

    .el-checkbox__inner::after {
      width: 7px;
      height: 18px;
      left: 37%;
      color: #ffc561 !important;
      /* top: 3px; */
      top: 10%;
    }

    .sjzx_middle_right {
      position: absolute;
      left: 2800px;
      top: 0;
      width: 620px;
      height: 1350px;
      background: linear-gradient(179deg, #0e1a40 0%, #064069 100%);
      box-shadow: 0px 7px 50px 0px rgba(35, 154, 228, 0.56);
    }

    .sjzx_middle_right_container {
      margin-top: 10px;
      margin-left: 90px;
      border-left: 8px solid #00ffff;
      /* height: 1200px; */
      /* overflow-y: auto; */
    }

    .sjzx_middle_right_content {
      height: 1000px;
      overflow-y: scroll;
    }

    .sjzx_middle_right_content::-webkit-scrollbar {
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
      /* scrollbar-arrow-color: red; */
    }

    .sjzx_middle_right_content::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }

    .btn_right {
      padding: 0 50px;
      min-width: 250px;
      width: auto;
      height: 53px;
      background-image: url("/static/citybrain/djtl/img/sjzx-middle/btn.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      font-size: 24px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #ffffff;
      line-height: 25px;
      text-shadow: 0px 2px 5px #000000;
      background-color: transparent;
      border: unset;
      margin-left: 40px;
      margin-bottom: 20px;
    }

    .yjyp-item p {
      font-size: 30px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
      margin-left: 50px;
      line-height: 40px;
    }

    .yjyp-item {
      margin-bottom: 70px;
    }

    .red {
      background: linear-gradient(180deg,
          #ffffff 0%,
          #ffcdcd 50.244140625%,
          #ff4949 53.0029296875%,
          #ffcdcd 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .yellow {
      background: linear-gradient(180deg,
          #ffffff 0%,
          #ffeccb 50.244140625%,
          #ffc460 53.0029296875%,
          #ffeccb 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .blue {
      color: #22e8e8 !important;
    }

    .item-s {
      margin-bottom: 20px;
    }

    .item-time {
      position: relative;
    }

    .item-time::before {
      position: absolute;
      left: -65px;
      content: "";
      display: inline-block;
      width: 51px;
      height: 25px;
      background-image: url("/static/citybrain/djtl/img/sjzx-middle/circle.png");
      background-size: 100% 100%;
    }

    .center_bottom {
      position: absolute;
      top: 1365px;
      left: 45px;

      display: flex;
      justify-content: center;
    }

    .shijian {
      width: 549px;
      height: 263px;
      background: url("/static/citybrain/csdn/img/ywt/dwjc-right-bc.png") no-repeat;
      background-size: 100% 100%;
      margin: 20px auto;
    }

    .shijian #eventMain {
      display: inline-block;
      width: 93.5%;
      margin: 25px;
    }

    .shijian .contain {
      overflow-y: auto;
      height: 239px;
      overflow-x: hidden;
      width: 525px;
    }

    .el-tree {
      background-color: unset;
    }

    .sjzx_middle_left_container {
      /* height: 555px; */
      /* overflow-y: scroll; */
    }

    .shijian .contain::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    .shijian .contain::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }

    .sjzx_middle_left_container::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    .sjzx_middle_left_container::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }

    .auth-tree>.el-tree-node>.el-tree-node__content {
      display: none;
    }

    .auth-tree>.el-tree-node>.el-tree-node__content .el-checkbox {
      display: none;
    }

    .el-icon-caret-left:before {
      font-size: 30px;
    }

    .el-tree-node__expand-icon {
      position: absolute;
      right: 0;
    }

    .el-tree-node__label {
      padding-left: 15px;
    }

    /* .el-tree-node__expand-icon.expanded {
        -webkit-transform: rotate(2700deg);
        transform: rotate(270deg);
      } */
    .el-tree-node__expand-icon.expanded {
      -webkit-transform: rotate(-90deg);
      transform: rotate(-90deg);
      display: none;
    }

    .el-checkbox__input.is-checked+.el-checkbox__label {
      color: #c0d6ed;
    }

    .el-tree-node__content>label.el-checkbox {
      position: absolute;
      right: 0;
    }

    .el-tree-node:focus>.el-tree-node__content {
      background-color: transparent;
    }
  </style>
</head>

<body>
  <div id="sjzx-middle">
    <div class="sjzx_middle_left">
      <div class="sjzx_middle_title"></div>
      <div class="sjzx_middle_left_container">
        <el-tree :data="treeData" show-checkbox node-key="codeid" ref="treeForm" highlight-current :props="defaultProps"
          :default-checked-keys="checkedKeys" @check-change="checkChange" @check="treeCheck" class="auth-tree"
          :render-after-expand="false" icon-class="el-icon-caret-left" default-expand-all>
          <div style="display: flex; align-items: center" slot-scope="{ node, data }">
            <img v-if="data.parentid!='szwh'" style="width: 30px; margin-right: 15px"
              :src="`/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/szwh-${data.label}.png`" alt="" />

            <div v-if="data.parentid!='szwh'" style="
                  line-height: 3.125rem;
                  font-size: 30px;
                  font-family: PangMenZhengDao;
                  font-weight: bold;
                  color: #c0d6ed;
                  line-height: 58px;
                ">
              {{ data.name }}
              <span>({{ data.num }})</span>
            </div>
          </div>
        </el-tree>
      </div>
    </div>
  </div>
</body>

</html>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  var vm = new Vue({
    el: "#sjzx-middle",
    data: {
      treeData: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 分页参数
      checkedKeys: ["szwh-whfw-tsg", "szwh-whfw-whg"],
      tab_id: 0,
      tab_index: null,
      tabAllCount: 10,
      //分页结束
      clickid: "",
      cenLeft: [],
      showright: false,
      sjzxValueList: [],
      sjzxList: [],
      tabShow: true,
      dataMain: "",
      dataList: [
        {
          handleDescribe: "婺城区新狮子街道-新增裸露土地",
          handleTime: "",
          handleNode: "",
        },
        {
          handleDescribe: "卫星遥感识别出婺城区多湖街道新增裸露土地",
          handleTime: "2022-07-07 18:00:00",
          handleNode: "预警研判",
        },
        {
          handleDescribe: "产生黄色预警",
          handleTime: "2022-07-07 18:00:07",
          handleNode: "",
        },
      ],
      dataArr: [],
      jrts_res: null,
      jrtsData: [],

    },
    mounted() {
      window.addEventListener("message", async (e) => {
        // console.log(e)
        // debugger
        if (!e.data.data.data) return;

        const item = JSON.parse(e.data.data.data);

        if (item.pointId !== "szwh") return;
        let coor = item.obj.lng.split(",");
        console.log(item);
        const res = await $api("yxzl_szwh_center012", {
          id: item.obj.id,
          code: item.obj.ly,
        });

        let arr = Object.keys(res[0]).map((item) => {
          return {
            name: item,
            value: res[0][item],
          };
        });

        // console.log(res)
        let countStr = "";
        for (let index = 0; index < arr.length; index++) {
          if (arr[index].name.indexOf("负责人") > -1) continue;
          else if (arr[index].name.indexOf("法人") > -1) continue;
          else if (arr[index].name.indexOf("代表人") > -1) continue;
          countStr += `<div
          class="item"
          style="display: flex; font-size: 32px; color: #2299e2; line-height: 70px"
        >
          <span style="margin-left:30px;white-space: nowrap; ">${arr[index].name}  :</span>
          <span style="color: #fff; margin-left:30px;

          "
            >${arr[index].value}</span
          >
        </div>`;
        }
        let str = `
        <div
      onclick=" this.style.display = 'none'"
      style="
        width: 800px;
        position: absolute;

        border-radius: 5px;
        background-color: rgba(10, 31, 53, 0.8);
        z-index: 999999;
        -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
        box-shadow: inset 0 0 40px 0 #5ba3fa;
        padding: 24px;
      "
    >
      <div class="container">${countStr}</div>
    </div>
        `;

        let objData = {
          funcName: "customPop",
          coordinates: coor,

          // coordinates: ['119.607129', '29.068155'],
          closeButton: true,
          html: str,
        };

        top.document
          .getElementById("map")
          .contentWindow.Work.funChange(JSON.stringify(objData));
      });

    },

    created() {
      this.getTreeData();
    },
    methods: {
      initFun() {
        let that = this;
      },
      checkChange(item, flag) {
        console.log(item);
        if (flag) {
          this.getPoint(item);
        } else {
          this.rmpop();
          this.rmPoint(item.codeid);
        }
      },
      treeCheck(node, list) {
        if (list.checkedKeys.length == 3) {
          //单选实现
          this.$refs.treeForm.setCheckedKeys([node.codeid]);
        }
      },
      // 获取经纬度
      getPoint(item) {
        let that = this;
        $api("yxzl_szwh_center011", { code: item.label }).then((res) => {
          let pointData = [];
          let icon = res[0] ? "szwh-" + res[0].ly : "";
          res.forEach((obj, index) => {
            if (
              obj.lng.split(",")[0].indexOf("无") < 0 ||
              obj.lng.split(",")[1].indexOf("无") < 0 ||
              obj.lng.split(",")[0] == 0 ||
              obj.lng.split(",")[1] == 0
            ) {
              let str = {
                data: {
                  pointId: "szwh",
                  obj,
                },
                point: obj.lng,
              };
              pointData.push(str);
            }
          });
          that.pointTextMapFun(icon, pointData, item.codeid);
        });
      },
      // 添加点位方法
      pointTextMapFun(icon, pointData, pointId) {
        console.log(pointData);
        console.log("icon", icon);
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "pointLoad", //功能名称
            pointType: icon, //点位类型图标
            pointId: "0" + pointId,
            setClick: true,
            pointData: pointData,
            imageConfig: { iconSize: 0.6 },
            size: [0.01, 0.01, 0.01, 0.01],
            popup: {
              offset: [50, -100],
            },
          })
        );
      },
      //清除弹窗
      rmpop() {
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "rmPop",
          })
        );
      },
      rmPoint(id) {
        top.document.getElementById("map").contentWindow.Work.funChange(
          JSON.stringify({
            funcName: "rmPoint",
            pointId: "0" + id, //传id清除单类，不传清除所有
          })
        );
      },
      tranListToTreeData(list, rootValue) {
        var arr = [];
        list.forEach((item) => {
          if (item.parentid === rootValue) {
            // 找到之后 就要去找 item 下面有没有子节点
            const children = this.tranListToTreeData(list, item.codeid);
            if (children.length) {
              // 如果children的长度大于0 说明找到了子节点
              item.children = children;
            }
            arr.push(item); // 将内容加入到数组中
          }
        });
        return arr;
      },
      getTreeData() {
        let that = this
        let arr = [
          { codeid: "szwh-lyfw", label: "旅游服务", parentid: "szwh", orderid: 10, name: "未来工厂", num: "22" },
          { codeid: "szwh-lyfw-jd", label: "星级酒店", parentid: "szwh-lyfw", orderid: 12, name: "数字化建设社区", num: "22" },
          { codeid: "szwh-lyfw-ms", label: "等级民宿", parentid: "szwh-lyfw", orderid: 13, name: "运营标准体系社区", num: "35" }
        ]
        this.treeData = this.tranListToTreeData(arr, "szwh");
        // $api("yxzl_szwh_center033").then((res) => {
        //   this.treeData = this.tranListToTreeData(res, "szwh");
        //   console.log("treeData",res);
        // });
      },
    },
  });
</script>