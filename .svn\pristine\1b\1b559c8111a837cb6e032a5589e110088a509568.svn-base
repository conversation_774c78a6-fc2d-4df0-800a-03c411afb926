<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>人事服务弹窗</title>
    <script src="/Vue/vue.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css">
    <link rel="stylesheet" href="/static/citybrain/ggfw/css/common-dialog.css">
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script> <!-- 轮播toolTip -->
</head>

<style>
    .table1 .th .th_td:nth-child(1) {
        flex: 0.05;
    }

    .table1 .th .th_td:nth-child(2) {
        flex: 0.1;
    }

    .table1 .th .th_td:nth-child(3) {
        flex: 0.14;
    }

    .table1 .th .th_td:nth-child(4) {
        flex: 0.11;
    }

    .table1 .th .th_td:nth-child(5) {
        flex: 0.16;
    }

    .table1 .th .th_td:nth-child(6) {
        flex: 0.16;
    }

    .table1 .th .th_td:nth-child(7) {
        flex: 0.16;
    }

    .table1 .th .th_td:nth-child(8) {
        flex: 0.16;
    }

    .tr_td {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>

<body>
    <div id="app" class="container" v-cloak style="width:1540px">
        <div class="head">
            <span>人事档案代管机构</span>
            <div class="img" @click="closeDialog"></div>
        </div>
        <div class="content">
            <!--代管机构 -->
            <div class="dgjg">
                <div class="s-c-blue2-gradient s-font-40">代管机构</div>
                <div class="table1" style="height: 700px;">
                    <div class="th">
                        <div class="th_td" v-for="(item,index) in theadList" :key="index">
                            {{item}}
                        </div>
                    </div>
                    <div class="tbody" id="box0" @mouseover="mouseenterEvent()" @mouseleave="mouseleaveEvent()">
                        <div class="tr" v-for="(item ,i) in tableList" :key="i">
                            <div class="tr_td" style="flex: 0.05">{{i+1}}</div>
                            <div class="tr_td" style="flex: 0.1">{{item.name}}</div>
                            <div class="tr_td" style="flex: 0.14">{{item.dz}}</div>
                            <div class="tr_td" style="flex: 0.11">{{item.dafwrc}}</div>
                            <div class="tr_td" style="flex: 0.16">{{item.dagll}}</div>
                            <div class="tr_td" style="flex: 0.16">{{item.dazrl}}</div>
                            <div class="tr_td" style="flex: 0.16">{{item.dazcs}}</div>
                            <div class="tr_td" style="flex: 0.16">{{item.dafb}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
<script type="module">

    new Vue({
        el: '#app',
        data: {
            theadList: ['序号', '名称', '档案服务人次', '档案管理', '人事档案管理层', '人事档案转入量', '人事档案转出数', '各类档案分布'],
            tableList: [],//表格数据

        },
        methods: {
            closeDialog() {
                top.commonObj.funCloseIframe({
                    name: 'rsfw-dialog'
                })
            },
            mouseenterEvent() {
                clearInterval(this.time)
            },
            mouseleaveEvent() {
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },
            autoScroll() {
                this.dom = document.getElementById('box0')
                // this.scpDom = document.getElementsByClassName('text')
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },
            //数据初始化
            init() {
                $api("/ggfw_rsfw_dgjg").then((res) => {
                    this.tableList = res;
                });

            },
        },
        //项目生命周期
        mounted() {
            this.init();
            this.autoScroll();
        }


    })


</script>

</html>