<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>质量技术监管</title>
    <link rel="stylesheet" href="./css/hjbh-right/common.css" />
    <link rel="stylesheet" href="./css/zljsjg-left.css" />
  </head>

  <body>
    <main id="lljs">
      <div class="top" style="margin-top: 20px">
        <div class="topLeft">
          <nav>
            <s-header-title
              htype="2"
              title="特种设备总数"
              data-time="2021年10月"
            ></s-header-title>
          </nav>
          <div class="topLeftContent" id="histogram1"></div>
        </div>
        <div class="topRight">
          <nav>
            <s-header-title
              htype="2"
              title="行政审批总数"
              data-time="2021年10月"
            ></s-header-title>
          </nav>
          <div class="topRightContent" id="pieChart"></div>
        </div>
      </div>
      <div class="center">
        <nav>
          <s-header-title
            title="特种设备占比"
            data-time="2021年10月"
          ></s-header-title>
        </nav>
        <!-- <div class="centerContent" id="histogram2"></div> -->
        <div
          style="
            display: flex;
            justify-content: space-between;
            padding: 0 50px;
            box-sizing: border-box;
          "
        >
          <div class="centerContent" id="histogram2_1"></div>
          <div class="centerContent" id="histogram2_2"></div>
        </div>
      </div>
      <div class="bottom">
        <nav>
          <s-header-title
            title="3C认证企业情况"
            data-time="2021年10月"
            :click-flag="true"
            @click="open3C"
          ></s-header-title>
        </nav>
        <div class="bottomContent" id="histogram3"></div>
      </div>
    </main>
    <script src="./js/lib/vue.js"></script>
    <script src="./js/lib/echarts.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="./js/lib/echarts-auto-tooltip.js"></script>
    <!-- 轮播toolTip -->
    <script src="/static/citybrain/csdn/hcharts/highcharts.js"></script>
    <script src="/static/citybrain/csdn/hcharts/highcharts-3d.js"></script>
    <script src="./js/zljsjg-left/histogram1.js"></script>
    <script src="./js/zljsjg-left/histogram2.js"></script>
    <script src="./js/zljsjg-left/histogram3.js"></script>
    <script src="./js/zljsjg-left/pieChart.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script>
      // window.onload = function () {
      //     console.log('zljsjg')
      //     //切换行政区划地图场景
      //     top.document.getElementById("map").contentWindow.Work.change3D(9);
      // }

      new Vue({
        el: "#lljs",
        data: {
          show_3c: false,
        },
        methods: {
          initFun() {
            let that = this;
            $api("zljsjg-left_zljsjg_tzsbzs").then((res) => {
              that.showChart1(res);
            });
            $api("zljsjg-left_zljsjg_xzspzs").then((res) => {
              let data = [];
              res.forEach((o) => {
                data.push([o.name, Number(o.value)]);
              });
              that.showChart2(data);
            });
            $api("zljsjg-left_zljsjg_3crzqk").then((res) => {
              that.showChart4(res);
            });

            $api("zljsjg-left_zljsjg_tezbzb1").then((res) => {
              that.showChart21("histogram2_1", res);
            });
            $api("zljsjg-left_zljsjg_tezbzb2").then((res) => {
              that.showChart21("histogram2_2", res);
            });
          },
          showHightCharts(id, data) {
            var pieChartOption5 = Highcharts.chart(id, {
              chart: {
                type: "pie",
                options3d: {
                  enabled: true,
                  alpha: 45,
                },
                backgroundColor: "none",
              },
              title: {
                text: "",
              },
              plotOptions: {
                pie: {
                  innerSize: 100,
                  depth: 65,
                  format: "<b>{point.name}</b>: {point.percentage:.1f} %",
                },
              },
              // colors: ["#fdd67f", "#0d9bc9"],
              credits: {
                enabled: false, // false禁用版权信息
              },
              series: [
                {
                  name: "主体类型",
                  dataLabels: {
                    distance: "10%", //连线长度
                    style: {
                      color: "#fff",
                      fontSize: "24px",
                      fontWeight: "blod",
                      fontFamily: "Courier new",
                    },
                  },
                  data: data,
                },
              ],
            });
          },
          showChart1(data) {
            // 特种设备总数
            let sbzs_xdata = []; //横轴data
            let sbzs_ydata = []; //纵轴data
            // for (let i = 0; i < data.length; i++) {
            //     for (let key in data[i]) {
            //         sbzs_xdata.push(key)
            //         sbzs_ydata.push(data[i][key])
            //     }
            // }
            data.forEach((element) => {
              sbzs_xdata.push(element.name);
              sbzs_ydata.push(element.value);
            });

            let sbzsMyChart = echarts.init(
              document.getElementById("histogram1")
            );
            sbzsOption.xAxis[0].data = sbzs_xdata;
            sbzsOption.series[0].data = sbzs_ydata;
            sbzsMyChart.setOption(sbzsOption);
            tools.loopShowTooltip(sbzsMyChart, sbzsOption, {
              loopSeries: true,
            }); //轮播
          },
          showChart2(data) {
            this.showHightCharts("pieChart", data);
            // 行政审批总数
            // let spzsMyChart = echarts.init(document.getElementById("pieChart"));
            // pieChartOption.series[0].data = data;
            // spzsMyChart.setOption(pieChartOption);
            // tools.loopShowTooltip(spzsMyChart, pieChartOption, {
            //   loopSeries: true,
            // }); //轮播
          },
          showChart3(data) {
            // 行政执法情况
            let xzzf_xdata = []; //横轴data
            let xzzf_ydata = []; //纵轴data
            for (let i = 0; i < data.length; i++) {
              for (let key in data[i]) {
                xzzf_xdata.push(key);
                xzzf_ydata.push(data[i][key]);
              }
            }

            let xzzfMyChart = echarts.init(
              document.getElementById("histogram2")
            );
            xzzfOption.xAxis[0].data = xzzf_xdata;
            xzzfOption.series[0].data = xzzf_ydata;
            xzzfMyChart.setOption(xzzfOption);
            tools.loopShowTooltip(xzzfMyChart, xzzfOption, {
              loopSeries: true,
            }); //轮播
          },
          showChart21(id, data) {
            let dom = echarts.init(document.getElementById(id));
            let option = {
              color: [
                "#6BA364",
                "#7CB9C3",
                "#5D86E5",
                "#C95948",
                "#DF7C41",
                "#EAC05B",
                "#6BA364",
                "#5D86E5",
                "#E7BCF3",
                "#FB7293",
                "#9FE6B8",
                "#32C5E9",
              ],
              tooltip: {
                trigger: "item",
                formatter: "{a} <br/>{b} : {c} ({d}%)",
                textStyle: {
                  color: "#ffffff",
                  fontSize: 40,
                },
                backgroundColor: "#384c63",
              },
              toolbox: {
                show: true,
              },
              legend: {
                show: false,
                type: "scroll",
                orient: "vertical",
                left: "10%",
                align: "left",
                top: "middle",
                textStyle: {
                  color: "#8C8C8C",
                },
                height: 150,
              },
              label: {
                fontSize: 30,
                borderWidth: 0,
              },
              labelLine: {
                lineStyle: {
                  color: "#fff",
                },
              },
              series: [
                {
                  name: "业务警种",
                  type: "pie",
                  radius: [0, 190],
                  label: {
                    fontSize: 32,
                    color: "#fff",
                  },
                  data: data,
                },
              ],
            };

            dom.setOption(option);
            dom.getZr().on("mousemove", (param) => {
              dom.getZr().setCursorStyle("default");
            });
          },

          showChart4(data) {
            // 3C认证企业情况
            let qyqk_xdata = []; //横轴data
            let qyqk_ydata = []; //纵轴data
            // for (let i = 0; i < data.length; i++) {
            //     for (let key in data[i]) {
            //         qyqk_xdata.push(key)
            //         qyqk_ydata.push(data[i][key])
            //     }
            // }
            data.forEach((element) => {
              qyqk_xdata.push(element.name);
              qyqk_ydata.push(element.value);
            });
            let qyqkMyChart = echarts.init(
              document.getElementById("histogram3")
            );
            qyqkOption.xAxis[0].data = qyqk_xdata;
            qyqkOption.series[0].data = qyqk_ydata;
            qyqkMyChart.setOption(qyqkOption);
            tools.loopShowTooltip(qyqkMyChart, qyqkOption, {
              loopSeries: true,
            }); //轮播
          },

          initMap() {
            top.document.getElementById("map").contentWindow.Work.change3D(9);
            this.flyTo();
            this.add3DText();
            this.add3DText1();
            this.addPoint();
          },

          // 加载3D文字方法
          add3DText() {
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "3Dtext", //3D文字功能
                textData: [
                  // pos文字的位置  //text 展示的文字
                  {
                    pos: [119.94315399169922, 29.5330503845215, 11000],
                    text: "浦江县",
                  },
                  {
                    pos: [119.46214447021484, 29.28345558166504, 11000],
                    text: "兰溪市",
                  },
                  {
                    pos: [119.5569204711914, 28.97677101135254, 11000],
                    text: "婺城区",
                  },
                  {
                    pos: [119.8483056640625, 29.158559951782227, 11000],
                    text: "金义新区",
                  },
                  {
                    pos: [120.08206787109375, 29.292123641967773, 11000],
                    text: "义乌市",
                  },
                  {
                    pos: [119.7269204711914, 28.76677101135254, 11000],
                    text: "武义县",
                  },
                  {
                    pos: [120.1469204711914, 28.94677101135254, 11000],
                    text: "永康市",
                  },
                  {
                    pos: [120.4169204711914, 29.21677101135254, 11000],
                    text: "东阳市",
                  },
                  {
                    pos: [120.6299204711914, 29.03677101135254, 11000],
                    text: "磐安县",
                  },
                ],
                textSize: 40,
                id: "text1",
                // zoomShow: true,
                color: [255, 255, 255, 0.8],
              })
            );
          },
          add3DText1() {
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "3Dtext", //3D文字功能
                textData: [
                  // pos文字的位置  //text 展示的文字
                  {
                    pos: [119.94315399169922, 29.7030503845215, 11000],
                    name: "浦江县",
                    text: "客运索道",
                  },
                  {
                    pos: [119.46214447021484, 29.45345558166504, 11000],
                    name: "兰溪市",
                    text: "起重机械",
                  },
                  {
                    pos: [119.5569204711914, 29.14677101135254, 11000],
                    name: "婺城区",
                    text: "起重机械",
                  },
                  {
                    pos: [119.8483056640625, 29.328559951782227, 11000],
                    name: "金义新区",
                    text: "客运索道",
                  },
                  {
                    pos: [120.08206787109375, 29.462123641967773, 11000],
                    name: "义乌市",
                    text: "电梯",
                  },
                  {
                    pos: [119.7269204711914, 28.93677101135254, 11000],
                    name: "武义县",
                    text: "电梯",
                  },
                  {
                    pos: [120.1469204711914, 29.11677101135254, 11000],
                    name: "永康市",
                    text: "起重机械",
                  },
                  {
                    pos: [120.4169204711914, 29.38677101135254, 11000],
                    name: "东阳市",
                    text: "起重机械",
                  },
                  {
                    pos: [120.6299204711914, 29.20677101135254, 11000],
                    name: "磐安县",
                    text: "电梯",
                  },
                ],
                textSize: 40,
                id: "text2",
                color: [251, 255, 0, 1],
              })
            );
          },
          add3DText2() {
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "3Dtext", //3D文字功能
                textData: [
                  // pos文字的位置  //text 展示的文字
                  {
                    pos: [119.94315399169922, 29.7030503845215, 11000],
                    name: "浦江县",
                    text: "120",
                  },
                  {
                    pos: [119.46214447021484, 29.45345558166504, 11000],
                    name: "兰溪市",
                    text: "200",
                  },
                  {
                    pos: [119.5569204711914, 29.14677101135254, 11000],
                    name: "婺城区",
                    text: "300",
                  },
                  {
                    pos: [119.8483056640625, 29.328559951782227, 11000],
                    name: "金义新区",
                    text: "400",
                  },
                  {
                    pos: [120.08206787109375, 29.462123641967773, 11000],
                    name: "义乌市",
                    text: "500",
                  },
                  {
                    pos: [119.7269204711914, 28.93677101135254, 11000],
                    name: "武义县",
                    text: "300",
                  },
                  {
                    pos: [120.1469204711914, 29.11677101135254, 11000],
                    name: "永康市",
                    text: "200",
                  },
                  {
                    pos: [120.4169204711914, 29.38677101135254, 11000],
                    name: "东阳市",
                    text: "200",
                  },
                  {
                    pos: [120.6299204711914, 29.20677101135254, 11000],
                    name: "磐安县",
                    text: "150",
                  },
                ],
                textSize: 40,
                id: "text3",
                color: [251, 255, 0, 1],
              })
            );
          },

          //清除3D文字方法
          rm3DText() {
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "rm3Dtext", //清除柱状体
              })
            );
          },

          addPoint() {
            let res = [
              {
                title: "浦江县",
                gps_x: "119.94315399169922",
                gps_y: "29.5630503845215",
                text: "客运索道",
              },
              {
                title: "兰溪市",
                gps_x: "119.46214447021484",
                gps_y: "29.31345558166504",
                text: "起重机械",
              },
              {
                title: "婺城区",
                gps_x: "119.5569204711914",
                gps_y: "29.00677101135254",
                text: "起重机械",
              },
              {
                title: "金义新区",
                gps_x: "119.8483056640625",
                gps_y: "29.188559951782227",
                text: "客运索道",
              },
              {
                title: "义乌市",
                gps_x: "120.08206787109375",
                gps_y: "29.322123641967773",
                text: "电梯",
              },
              {
                title: "武义县",
                gps_x: "119.7269204711914",
                gps_y: "28.79677101135254",
                text: "电梯",
              },
              {
                title: "永康市",
                gps_x: "120.1469204711914",
                gps_y: "28.97677101135254",
                text: "起重机械",
              },
              {
                title: "东阳市",
                gps_x: "120.4169204711914",
                gps_y: "29.24677101135254",
                text: "起重机械",
              },
              {
                title: "磐安县",
                gps_x: "120.6299204711914",
                gps_y: "29.06677101135254",
                text: "电梯",
              },
            ];
            let arr = res.map((item) => {
              return {
                data: {},
                point: item.gps_x + "," + item.gps_y,
                item: item.text,
              };
            });
            console.log(arr);
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "pointLoad",
                pointType: "water", // 点位类型（图标名称）
                pointId: "point1", // 点位唯一id
                setClick: true,
                pointData: arr,
                imageConfig: { iconSize: 1 },
                popup: {
                  offset: [50, -100],
                },
              })
            );
          },
          addPoint1() {
            let res = [
              {
                title: "浦江县",
                gps_x: "119.94315399169922",
                gps_y: "29.5630503845215",
                text: "客运索道",
              },
              {
                title: "兰溪市",
                gps_x: "119.46214447021484",
                gps_y: "29.31345558166504",
                text: "起重机械",
              },
              {
                title: "婺城区",
                gps_x: "119.5569204711914",
                gps_y: "29.00677101135254",
                text: "起重机械",
              },
              {
                title: "金义新区",
                gps_x: "119.8483056640625",
                gps_y: "29.188559951782227",
                text: "客运索道",
              },
              {
                title: "义乌市",
                gps_x: "120.08206787109375",
                gps_y: "29.322123641967773",
                text: "电梯",
              },
              {
                title: "武义县",
                gps_x: "119.7269204711914",
                gps_y: "28.79677101135254",
                text: "电梯",
              },
              {
                title: "永康市",
                gps_x: "120.1469204711914",
                gps_y: "28.97677101135254",
                text: "起重机械",
              },
              {
                title: "东阳市",
                gps_x: "120.4169204711914",
                gps_y: "29.24677101135254",
                text: "起重机械",
              },
              {
                title: "磐安县",
                gps_x: "120.6299204711914",
                gps_y: "29.06677101135254",
                text: "电梯",
              },
            ];
            let arr = res.map((item) => {
              return {
                data: {},
                point: item.gps_x + "," + item.gps_y,
                // item:item.text
              };
            });
            console.log(arr);
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "pointLoad",
                pointType: "address_default", // 点位类型（图标名称）
                pointId: "point2", // 点位唯一id
                setClick: true,
                pointData: arr,
                imageConfig: { iconSize: 1 },
                popup: {
                  offset: [50, -100],
                },
              })
            );
          },

          //清除点位
          rmPoint() {
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "rmPoint",
                pointId: "",
              })
            );
          },
          //飞入
          flyTo() {
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "flyto", //功能名称
                flyData: {
                  center: [119.95478050597587, 29.01613226366889],
                  zoom: 10.5,
                  pitch: 40,
                  bearing: 0,
                },
              })
            );
          },

          open3C() {
            this.show_3c = !this.show_3c;
            this.rm3DText();
            this.rmPoint();
            if (this.show_3c) {
              this.openIframe();
              this.add3DText2();
              this.addPoint1();
            } else {
              this.closeIframe();
              this.rm3DText();
              this.rmPoint();
              this.rmHot();
            }
          },
          rmHot() {
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "rmhotPowerMap", //热力图
                heatMapId: "Hot",
              })
            );
            this.initMap();
          },
          openIframe() {
            top.window.parent.postMessage(
              JSON.stringify({
                type: "openIframe",
                name: "zljsjg-3c",
                src:
                  baseURL.url +
                  "/static/citybrain/scjg/commont/zljsjg-3c-dialog.html",
                left: "2120px",
                top: "230px",
                width: "440px",
                height: "800px",
                zIndex: "9",
                argument: {
                  status: "zljsjg-3c",
                },
              }),
              "*"
            );
          },
          openIframe1(item) {
            top.window.parent.postMessage(
              JSON.stringify({
                type: "openIframe",
                name: "zljsjg",
                src:
                  baseURL.url +
                  "/static/citybrain/scjg/commont/zljsjg-dialog.html",
                left: "3400px",
                top: "630px",
                width: "915px",
                height: "1015px",
                zIndex: "999",
                argument: {
                  status: "zljsjg",
                  item: item,
                },
              }),
              "*"
            );
          },
          openIframe2() {
            top.window.parent.postMessage(
              JSON.stringify({
                type: "openIframe",
                name: "zljsjg-middle",
                src:
                  baseURL.url +
                  "/static/citybrain/scjg/commont/zljsjg-middle.html",
                left: "2900px",
                top: "230px",
                width: "2010px",
                height: "270px",
                zIndex: "999",
                argument: {
                  status: "zljsjg-middle",
                },
              }),
              "*"
            );
          },
          closeIframe() {
            top.commonObj.funCloseIframe({
              name: "zljsjg-3c",
            });
          },
        },
        mounted() {
          this.initFun();
          this.initMap();
          // this.openIframe();

          this.openIframe2();
          let that = this;
          window.addEventListener("message", function (e) {
            let info = e.data;
            console.log(info);
            if (info.type === "pointClick") {
              that.openIframe1(info.item);
            }
          });
        },
        destroyed() {
          this.rm3DText();
          this.rmPoint();
        },
      });
    </script>
  </body>
</html>
