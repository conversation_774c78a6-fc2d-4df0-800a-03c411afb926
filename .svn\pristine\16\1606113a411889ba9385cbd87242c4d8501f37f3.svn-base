<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>人口动态指标分析-底部</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/elementui.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/elementui/js/elementui.js"></script>
</head>
<style>
    [v-cloak] {
        display: none;
    }
    html,body,ul,p{
        padding:0;
        margin:0;
        list-style: none;
    }
    .container{
        width:240px;
        height:270px;
        background-color: #0a2443;
        padding: 10px 10px;
        box-sizing: border-box;
        border: 1px solid #ccc; 
    }
    .el-checkbox {
        display: block;
        border-radius: 15px;
        margin-bottom: 2px;
        margin-right: 0;
    }

    .el-checkbox-group .el-checkbox:hover {
        background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #139e9e 100%) !important;
        border-radius: 0px 30px 30px 0px;
    }

    .el-checkbox__label {
        font-size: 32px;
        font-family: PangMenZhengDao;
        font-weight: bold;
        color: #c0d6ed;
        line-height: 58px;
    }

    .el-checkbox__inner {
        width: 33px;
        height: 33px;
        margin-top: 15px;
        background-color: #344d67;
    }
    .el-checkbox__input {
        float: left;
        margin-right: 1px;
    }

    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        background-color: #252316;
        border-color: #00ffff;
    }

    .el-checkbox__inner::after {
        width: 7px;
        height: 18px;
        left: 10px;
        color: #00ffff !important;
    }

    .el-checkbox__inner:hover {
        border-color: #00ffff;
    }

    .el-checkbox__inner::after {
        border-color: #61fffd;
    }
    
</style>

<body>
    <div id="app" class="container" v-cloak>
        <el-checkbox-group v-model="selectArr" @change="changeSelect">
            <el-checkbox v-for="(item,index) in optionArr" :label="item.name" :key="index"
                :checked="item.flag" class="checkbox-box"></el-checkbox>
        </el-checkbox-group>
    </div>
</body>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>

<script type="module">

    new Vue({
        el: '#app',
        data: {
            selectArr: [],
            optionArr: [
                { name: "区域图", flag: false },
                { name: "热力图", flag: false },
                { name: "流入飞线图", flag: false },
                { name: "流出飞线图", flag: false },
            ],
        },
        //项目生命周期
        mounted() {  
          let that=this 
          window.addEventListener('message', function (e) {
            if (e.data && e.data.type == 'bankuaiClick') {
              let center=e.data.data.center.replace("[","").replace("]","").split(",")
              that.flytoAdd(center)
              that.loadHot()
            }
          })         
        },

        methods: {
            changeSelect(item) {
                let data = ""
                if (item.length > 1) {
                    this.selectArr.splice(0, 1)
                }
                data = this.selectArr[0]
                switch (data) {
                    case "区域图":
                        this.clearHotMapAll();
                        this.rm3DText();
                        top.document.getElementById("map").contentWindow.Work.change3D(9) 
                        this.add3DText1()
                        this.add3DText2()
                        this.closeIframe()
                        break;
                    case "热力图":
                        this.loadHot()
                        break;
                    case "流入飞线图":
                        this.openIframe(1)
                        break;
                    case "流出飞线图":
                        this.openIframe(2)
                        break;
                    default:
                        top.document.getElementById("map").contentWindow.Work.change3D(7)
                        this.clearHotMapAll();
                        this.rm3DText()
                        this.closeIframe()
                        break; 
                }

            },
            // 加载热力图
            
              loadHot() {
                $api("/cstz_rlt_qx").then((res) => {
                  console.log(res);
                  let hotMapData = [];
                
                  res.map((ele) => {
                    ele.heatmap.map((item) => {
                      let pointArr = [];
                      pointArr[0] = item.lng;
                      pointArr[1] = item.lat;
                      pointArr[2] = item.count;
                      pointArr[3] = item.geohash;
                      hotMapData.push(pointArr);
                    });
                  });
                
                  const mapData = {
                    funcName: "hotPowerMap",
                    hotPowerMapData: hotMapData,
                    offset: 256,
                    heatMapId: "rkztTimeHot",
                    threshold: 6000,
                    distance: 800,
                    alpha: 0.3,
                  };
                  window.parent.document
                    .getElementById("map")
                    .contentWindow.Work.funChange(JSON.stringify(mapData));
                });
              },

            // 加载3D文字方法
            add3DText1(){
                top.document.getElementById('map').contentWindow.Work.funChange(
                    JSON.stringify({
                    funcName:"3Dtext" , //3D文字功能
                    textData: [   // pos文字的位置  //text 展示的文字
                        {pos: [119.94315399169922,29.5630503845215,11000],text:"浦江县"},
                        {pos: [119.46214447021484,29.31345558166504,11000],text:"兰溪市"},
                        {pos: [119.5569204711914, 29.00677101135254,11000],text:"婺城区"},
                        {pos: [119.8483056640625, 29.188559951782227,11000],text:"金义新区"},
                        {pos: [120.08206787109375,29.322123641967773,11000],text:"义乌市"},
                        {pos: [119.7269204711914, 28.79677101135254,11000],text:"武义县"},
                        {pos: [120.1469204711914, 28.97677101135254,11000],text:"永康市"},
                        {pos: [120.4169204711914, 29.24677101135254,11000],text:"东阳市"},
                        {pos: [120.6299204711914, 29.06677101135254,11000],text:"磐安县"}
                    ],
                    textSize:40,
                    id: 'text1',
                    color: [255,255,255,1],
                    })
                )
            },
            // 加载3D文字方法
            add3DText2(){
                top.document.getElementById('map').contentWindow.Work.funChange(
                    JSON.stringify({
                    funcName:"3Dtext" , //3D文字功能
                    textData: [   // pos文字的位置  //text 展示的文字
                        {pos: [119.94315399169922,29.6230503845215,11000],text:"65"},
                        {pos: [119.46214447021484,29.3734555816650,11000],text:"87"},
                        {pos: [119.5569204711914, 29.0667710113525,11000],text:"120"},
                        {pos: [119.8483056640625, 29.2485599517822,11000],text:"109"},
                        {pos: [120.08206787109375,29.3821236419673,11000],text:"65"},
                        {pos: [119.7269204711914, 28.8567710113525,11000],text:"87"},
                        {pos: [120.1469204711914, 29.0367710113525,11000],text:"65"},
                        {pos: [120.4169204711914, 29.3067710113525,11000],text:"65"},
                        {pos: [120.6299204711914, 29.1267710113525,11000],text:"65"}
                    ],
                    textSize:40,
                    id: 'text2',
                    color: [255,255,255,1],
                    })
                )
            },
            //清除3D文字方法
            rm3DText(){
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName:"rm3Dtext" , //清除柱状体
                    })
                )
            },
          // 飞行
          flytoAdd(obj) {
            top.document.getElementById('map').contentWindow.Work.funChange(
              JSON.stringify({
                funcName: 'flyto',
                flyData: {
                  center: obj,
                  zoom: 13, //大
                  pitch: 2, //倾斜角
                },
              })
            )
          },
            //加载热力图
            hotPowerMap(){
                let timeStr = "2022-09-18 00:00:00";
                $api("/cstz_sjz_rlt_new", { date: timeStr }).then((res) => {
                    let hotMapData = [];
                    let heatArr = [];
                    let len = res[0].heatmap.length;
                    let sumLen = 20000 - len;
                    if (len >= 20000) {
                        heatArr = res[0].heatmap.slice(0, 20000);
                    } else {
                        heatArr = res[0].heatmap;
                        for (let j = 0; j < sumLen; j++) {
                        let a = {
                            count: 0,
                            geohash: 0,
                            lat: 0,
                            lng: 0,
                        };
                        heatArr.push(a);
                        }
                    }
                    heatArr.map((item) => {
                        // 画热力图的数据
                        let pointArr = [];
                        pointArr[0] = item.lng;
                        pointArr[1] = item.lat;
                        pointArr[2] = item.count;
                        pointArr[3] = item.geohash;
                        hotMapData.push(pointArr);
                    });
                    const mapData = {
                        funcName: "hotPowerMap",
                        hotPowerMapData: hotMapData,
                        offset: 256,
                        heatMapId: "Hot",
                        threshold: 6000,
                        distance: 800,
                        alpha: 0.3,
                    };
                    window.parent.document.getElementById("map").contentWindow.Work.funChange(JSON.stringify(mapData));
                });
            },
         
            //清除热力图
            rmHot(){
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                    funcName:"rmhotPowerMap", //热力图
                    heatMapId:'Hot'
                    })
                )
            },
              /*
               * 清除热力图
               */
              clearHotMapAll() {
                try {
                  top.document.getElementById('map').contentWindow.Work.funChange(
                    JSON.stringify({
                      funcName: 'rmhotPowerMap',
                    })
                  )
                } catch (error) {}
              },

            openIframe(code) {
                let Iframe = {
                    type: "openIframe",
                    name: 'rkdt-fly',
                    src: baseURL.url + "/static/citybrain3840/shgl/pages/rkdtzbfx/rkdtzbfx-fly.html",
                    left: "1060px",
                    top: "480px",
                    width: "1700px",
                    height: "1000px",
                    zIndex: "10",
                    argument: {
                        status: "rkdt-fly",
                        code:code
                    },
                };
                window.parent.postMessage(JSON.stringify(Iframe), "*");
            },

            closeIframe() {
                top.commonObj.funCloseIframe({
                    name: "rkdt-fly",
                });
            },
        },
        

    })


</script>

</html>