<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数字城管-中间</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <link rel="stylesheet" href="/static/citybrain/shgl/css/szss-middle.css" />
    <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
  </head>
  <style>
    .node-img {
      left: 4px !important;
    }
    .tree {
      height: 460px;
    }
    .el-tree-node.is-expanded > .el-tree-node__children {
      height: 410px;
    }

    .middletop {
      position: absolute;
      top: -50px;
      left: 900px;
      width: 1508px;
      height: 180px;
      background-color: #00000080;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
    }
    .left,
    .right {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      width: 700px;
      font-size: 60px;
      color: #fff;
      font-weight: bold;
    }
    .selectBox {
      width: 390px;
      overflow: hidden;
      display: flex;
      margin: 10px 0;
    }
    .el-input__inner {
      color: #fff;
      width: 95%;
      font-size: 24px !important;
      height: 55px;
      background-color: #0b2e53e8;
    }
    .el-button {
      padding: 3px 11px !important;
      font-size: 23px !important;
      width: 20% !important;
      height: 55px !important;
    }
  </style>

  <body>
    <div id="shfx-middle" v-cloak>
      <div class="selectBox">
        <el-input
          v-model="selseValue"
          placeholder="请输入在线监督员查询..."
        ></el-input>
        <el-button type="primary" @click="selectPer()">搜索</el-button>
      </div>
      <div class="tree">
        <el-tree
          :data="treeData"
          show-checkbox
          node-key="id"
          ref="tree"
          highlight-current
          @check-change="checkChange"
          class="auth-tree"
          :render-after-expand="false"
          icon-class="el-icon-caret-left"
        >
          <div
            style="display: flex; align-items: center"
            slot-scope="{ node, data }"
          >
            <div class="node-lable">
              <img
                v-if="data.children"
                class="node-img"
                :src="`/static/citybrain/shgl/img/shfx/szcg-${data.id}.png`"
                alt=""
              />
              {{ node.label }}
              <span v-if="data.children">({{data.children.length}})</span>
            </div>
          </div>
        </el-tree>
      </div>
      <div class="middletop">
        <div class="left">
          <div style="cursor: pointer" @click="showHotOne()">
            <img src="/img/b mjr/bmjr_green_normal.png" alt="" />
          </div>
          <div>设施总数</div>
          <div style="color: aqua">{{sszs}}</div>
        </div>
        <div class="right">
          <div style="cursor: pointer" @click="showHotOne()">
            <img src="/img/bmjr/bmjr_red_error.png" alt="" />
          </div>
          <div>当前处置数</div>
          <div style="color: firebrick">{{czs}}</div>
        </div>
      </div>
    </div>
  </body>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>
    var vm = new Vue({
      el: "#shfx-middle",
      data() {
        return {
          selseValue: "",
          sszs: 319365,
          czs: 321,
          treeData: [
            {
              id: 0,
              label: "园林绿化设施",
              children: [
                {
                  id: 01,
                  label: "古树名木",
                },
                {
                  id: 02,
                  label: "行道树",
                },
              ],
            },
            {
              id: 1,
              label: "公共设施",
              children: [
                {
                  id: 11,
                  label: "公共设施1",
                },
                {
                  id: 12,
                  label: "公共设施2",
                },
              ],
            },
            {
              id: 2,
              label: "其他部件",
              children: [
                {
                  id: 21,
                  label: "其他部件1",
                },
                {
                  id: 22,
                  label: "其他部件2",
                },
              ],
            },
            {
              id: 3,
              label: "市容环境设施",
              children: [
                {
                  id: 31,
                  label: "市容环境设施1",
                },
                {
                  id: 32,
                  label: "市容环境设施2",
                },
              ],
            },
            {
              id: 4,
              label: "交通设施",
              children: [
                {
                  id: 41,
                  label: "交通设施1",
                },
                {
                  id: 42,
                  label: "交通设施2",
                },
              ],
            },
            {
              id: 5,
              label: "监督员信息",
              children: [
                {
                  id: 51,
                  label: "张三",
                },
                {
                  id: 52,
                  label: "王五",
                },
                {
                  id: 53,
                  label: "张安",
                },
                {
                  id: 54,
                  label: "王毅",
                },
                {
                  id: 55,
                  label: "王兵",
                },
                {
                  id: 56,
                  label: "杨思",
                },
              ],
            },
            {
              id: 6,
              label: "井盖",
              children: [
                {
                  id: 61,
                  label: "井盖1",
                },
                {
                  id: 62,
                  label: "井盖2",
                },
              ],
            },
            {
              id: 7,
              label: "井盖案件",
              children: [
                {
                  id: 71,
                  label: "井盖案件1",
                },
                {
                  id: 72,
                  label: "井盖案件2",
                },
              ],
            },
          ],
        };
      },
      mounted() {
        // top.document.getElementById("map").contentWindow.Work.change3D(7);
        window.addEventListener("message", function (e) {
          if (e.data && e.data.type == "pointClick" && e.data.data) {
            let data = JSON.parse(e.data.data.data);
            if (data.porson_select) {
              window.parent.postMessage(
                JSON.stringify({
                  type: "openIframe",
                  name: "szcg_jdy_main",
                  src:
                    baseURL.url +
                    "/static/citybrain/shgl/commont/szcg/szcg_jdy_main.html",
                  left: "2110px",
                  top: "815px",
                  width: "600px",
                  height: "700px",
                  zIndex: "10",
                  argument: data,
                }),
                "*"
              );
            }
            let objData = {};
            if (data.其他部件) {
              objData = {
                funcName: "customPop",
                coordinates: data.lnglat.split(","),
                closeButton: true,
                html: ` <div
                  style="
                    width: max-content;
                    position: absolute;
                    border-radius: 5px;
                    background-color: rgba(10, 31, 53, 0.8);
                    z-index: 999999;
                    -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                    box-shadow: inset 0 0 40px 0 #5ba3fa;
                    padding: 24px;
                  "
                >
                  <div
                    onclick=" this.parentNode.style.display = 'none'"
                    style="
                      position: absolute;
                      right: 30px;
                      top: 10px;
                      font-size: 50px;
                      color: #fff;
                      cursor: pointer;
                    "
                  >
                    x
                  </div>
                  <div class="container">
                    <div style="margin: 10px; display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">部件ID ：</span>
                      <span>${obj.bjid}</span>
                    </div>
                    <div style="margin: 10px; display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">部件名称 ：</span>
                      <span>${obj.bjmc}</span>
                    </div>
                    <div style="margin: 10px; display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">部件所属部门 ：</span>
                      <span>${obj.ssbm}</span>
                    </div>
                    <div style="margin: 10px; display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">部件办理部门 ：</span>
                      <span>${obj.blbm}</span>
                    </div>
                    <div style="margin: 10px; display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">所在县区 ：</span>
                      <span>${obj.szqx}</span>
                    </div>
                    <div style="margin: 10px; display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">所在街道 ：</span>
                      <span>${obj.szjd}</span>
                    </div>
                    <div style="margin: 10px; display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">单元网格 ：</span>
                      <span>${obj.dywg}</span>
                    </div>
                    <div style="margin: 10px; display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">大类 ：</span>
                      <span>${obj.dl}</span>
                    </div>
                    <div style="margin: 10px; display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">小类 ：</span>
                      <span>${obj.xl}</span>
                    </div>
                    <img
                      src="/static/citybrain3840/shgl/img/shgl-video2.jpg"
                      width="600px"
                      alt=""
                    />
                  </div>
                </div>`,
              };
            } else if (data.案件详情) {
              objData = {
                funcName: "customPop",
                coordinates: data.lnglat.split(","),
                closeButton: true,
                html: `<div
                  style="
                    width: max-content;
                    position: absolute;
                    border-radius: 5px;
                    background-color: rgba(10, 31, 53, 0.8);
                    z-index: 999999;
                    -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                    box-shadow: inset 0 0 40px 0 #5ba3fa;
                    padding: 24px;
                  "
                >
                  <div
                    onclick=" this.parentNode.style.display = 'none'"
                    style="
                      position: absolute;
                      right: 30px;
                      top: 10px;
                      font-size: 50px;
                      color: #fff;
                      cursor: pointer;
                    "
                  >
                    x
                  </div>
                  <div class="container">
                    <div style="display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">案件ID ：</span>
                      <span>${obj.id}</span>
                    </div>
                    <div style="display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">案件名称 ：</span>
                      <span>${obj.a}</span>
                    </div>
                    <div style="display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">案件内容 ：</span>
                      <span>${obj.wrond}</span>
                    </div>
                    <div style="display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">案件位置 ：</span>
                      <span>${obj.address}</span>
                    </div>
                    <div style="display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">案件所属部门 ：</span>
                      <span>${obj.e}</span>
                    </div>
                    <div style="display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">案件当前办理部门 ：</span>
                      <span>${obj.c}</span>
                    </div>
                    <div style="display: flex; font-size: 40px; color: #fff">
                      <span style="text-align: left">案件状态 ：</span>
                      <span>${obj.state}</span>
                    </div>
                    <div
                      style="display: flex; font-size: 50px; color: #fff; margin-top: 20px"
                    >
                      案件处置流程
                    </div>
                    <div>
                      <div
                        style="
                          display: flex;
                          font-size: 40px;
                          color: #fff;
                          padding-top: 20px;
                          box-sizing: border-box;
                        "
                      >
                        <div
                          style="
                            width: 180px;
                            height: 60px;
                            background-color: #31a7ff;
                            border-radius: 10px;
                            text-align: center;
                            padding-top: 2px;
                            box-sizing: border-box;
                          "
                        >
                          上级
                        </div>
                        <div style="margin: 0 40px">→</div>
                        <div
                          style="
                            width: 180px;
                            height: 60px;
                            background-color: #31a7ff;
                            border-radius: 10px;
                            text-align: center;
                            padding-top: 2px;
                            box-sizing: border-box;
                          "
                        >
                          受理
                        </div>
                        <div style="margin: 0 40px">→</div>
                        <div
                          style="
                            width: 180px;
                            height: 60px;
                            background-color: #31a7ff;
                            border-radius: 10px;
                            text-align: center;
                            padding-top: 2px;
                            box-sizing: border-box;
                          "
                        >
                          立案
                        </div>
                      </div>
                      <div style="font-size: 40px; color: #fff; margin: 5px 0 0 670px">
                        ↓
                      </div>
                      <div
                        style="
                          display: flex;
                          font-size: 40px;
                          color: #fff;
                          padding-top: 5px;
                          box-sizing: border-box;
                          margin-bottom: 20px;
                        "
                      >
                        <div
                          style="
                            width: 180px;
                            height: 60px;
                            background-color: #31a7ff;
                            border-radius: 10px;
                            text-align: center;
                            padding-top: 2px;
                            box-sizing: border-box;
                          "
                        >
                          派发
                        </div>
                        <div style="margin: 0 40px">→</div>
                        <div
                          style="
                            width: 180px;
                            height: 60px;
                            background-color: #31a7ff;
                            border-radius: 10px;
                            text-align: center;
                            padding-top: 2px;
                            box-sizing: border-box;
                          "
                        >
                          办理
                        </div>
                        <div style="margin: 0 40px">→</div>
                        <div
                          style="
                            width: 180px;
                            height: 60px;
                            background-color: #31a7ff;
                            border-radius: 10px;
                            text-align: center;
                            padding-top: 2px;
                            box-sizing: border-box;
                          "
                        >
                          结案
                        </div>
                      </div>
                    </div>
                  </div>
                </div>`,
              };
            }
            top.document
              .getElementById("map")
              .contentWindow.Work.funChange(JSON.stringify(objData));
          }
        });
      },
      methods: {
        // 点击搜索
        selectPer() {
          if (this.selseValue == "在线监督员") {
            $get("/shgl/szcg/szcg-point").then((res) => {
              let result = res.filter((el) => {
                return el.main == "监督员信息";
              });

              let pointData = result.map((obj) => {
                return {
                  data: { porson_select: obj },
                  point: obj.lnglat,
                };
              });

              top.document.getElementById("map").contentWindow.Work.funChange(
                JSON.stringify({
                  funcName: "pointLoad", //功能名称
                  pointType: "zhdd_map_gwxxtyxmjycs", //点位类型图标
                  pointId: "porson_select",
                  setClick: true,
                  pointData: pointData,
                  imageConfig: { iconSize: 0.6 },
                  size: [0.01, 0.01, 0.01, 0.01],
                  popup: {
                    offset: [50, 30],
                  },
                })
              );
            });
          }
        },

        //莫一时间（热力图）
        async showHotOne() {
          $api("/sysjk001").then((res) => {
            let pointArr = res.map((v) => {
              return {
                data: { 案件详情: v },
                point: v.lng,
              };
            });

            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "pointLoad", //功能名称
                pointType: "aqsc_whqy_cc", //点位类型图标
                pointId: "hotPoint",
                setClick: false,
                pointData: pointArr,
                imageConfig: { iconSize: 0.8 },
                size: [0.01, 0.01, 0.01, 0.01],
                popup: {
                  offset: [50, 30],
                },
              })
            );
          });
          let res = await $api("/rkztRight005");
          $api("/cstz_sjz_rlt_new", { date: res[res.length - 1].data0 }).then(
            (res) => {
              let hotMapData = [];
              let heatArr = res[0].heatmap.slice(0, 10000);

              heatArr.map((item) => {
                // 画热力图的数据
                let pointArr = [];
                pointArr[0] = item.lng;
                pointArr[1] = item.lat;
                pointArr[2] = item.count;
                pointArr[3] = item.geohash;
                hotMapData.push(pointArr);
              });
              const mapData = {
                funcName: "hotPowerMap",
                hotPowerMapData: hotMapData,
                offset: 256,
                heatMapId: "rkztTimeHot",
                threshold: 6000,
                distance: 800,
                alpha: 0.3,
              };
              window.parent.document
                .getElementById("map")
                .contentWindow.Work.funChange(JSON.stringify(mapData));
            }
          );
        },

        checkChange(item, flag) {
          // this.rmAllPoint("hotPoint");
          // this.rmAllPoint("porson_select");
          // this.rmHotFun();
          if (flag) {
            this.getPoint(item);
          } else {
            this.rmPoint(item);
          }
        },
        getPoint(item) {
          $get("/shgl/szcg/szcg-point").then((res) => {
            let result = [];
            let pointData = [];
            result = res.filter((el) => {
              return el.name == item.label;
            });

            let icon = "";
            result.forEach((obj, index) => {
              icon = obj.icon;

              let datas = {};
              datas = {
                title: obj.name + "详情",
                key: ["设施编号", "设施名称", "设施地点"],
                value: [obj.ssbh, obj.ssmc, obj.ssdd],
              };
              if (obj.type === "3") {
                datas = {
                  其他部件: obj,
                };
              } else if (obj.type === "6") {
                datas = {
                  porson_select: obj,
                };
              } else if (obj.type === "8") {
                datas = {
                  title: obj.name + "详情",
                  key: [
                    "案件id",
                    "案件名称",
                    "案件内容",
                    "案件位置",
                    "案件所属部门",
                    "案件当前办理部门",
                    "所在区县",
                    "所属网格",
                  ],
                  value: [
                    obj.ajid,
                    obj.ajmc,
                    obj.ajnr,
                    obj.ajwz,
                    obj.ssbm,
                    obj.blbm,
                    obj.szqx,
                    obj.sswg,
                  ],
                };
              }
              let str = {
                data: datas,
                point: obj.lnglat,
              };
              pointData.push(str);
            });
            console.log(pointData);
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "pointLoad", //功能名称
                pointType: icon, //点位类型图标
                pointId: "szcg-" + item.id,
                setClick: true,
                pointData: pointData,
                imageConfig: { iconSize: 0.9 },
                size: [0.01, 0.01, 0.01, 0.01],
                popup: {
                  offset: [50, 30],
                },
              })
            );
          });
        },
        rmPoint(item) {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmPoint",
              pointId: "szcg-" + item.id, //传id清除单类，不传清除所有
            })
          );
        },
        rmAllPoint(id) {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmPoint",
              pointId: id, //传id清除单类，不传清除所有
            })
          );
        },
        // 清除柱状图
        rmHistogramFun() {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmHistogram",
            })
          );
        },
        // 清除柱状图
        rmHotFun() {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmhotPowerMap",
            })
          );
        },
      },
      destroyed() {
        this.rmAllPoint();
      },
    });
  </script>
</html>
