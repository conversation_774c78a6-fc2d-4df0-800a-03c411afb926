<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>质量技术监管中间面板</title>
        <script src="/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain3840/scjg/css/zljs-midle.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <!-- 轮播toolTip -->
    </head>
    <style>
        .header-title2[data-v-4d0d1712] {
            width: 100% !important;
        }
    </style>

    <body>
        <div id="app" class="zljs-container" v-cloak>
            <nav>
                <s-header-title-2 htype="2" title="质监双随机一公开信息统计展示"></s-header-title-2>
            </nav>
            <div id="zjs-chart1"></div>
            <div id="zjs-chart2"></div>
            <nav>
                <s-header-title-2 htype="2" title="质监行政处罚案件统计展示"></s-header-title-2>
            </nav>
            <div id="zjxz-chart1"></div>
            <div id="zjxz-chart2"></div>
        </div>
    </body>
    <script type="module">
        new Vue({
            el: "#app",
            data: {
                tjData: [],
            },
            methods: {
                init() {
                    $api("ldst_scjg_zljs", { type: 3 }).then((res) => {
                        this.LinechartsShow("zjs-chart1", "双随机抽查季度总数", "同比增幅", res);
                    });
                    $api("ldst_scjg_zljs", { type: 4 }).then((res) => {
                        this.LinechartsShow("zjs-chart2", "双随机抽查季度总数", "同比增幅", res);
                    });
                    $api("ldst_scjg_zljs", { type: 5 }).then((res) => {
                        this.LinechartsShow("zjxz-chart1", "行政处罚安全数", "同比增幅", res);
                    });
                    $api("ldst_scjg_zljs", { type: 6 }).then((res) => {
                        this.LinechartsShow("zjxz-chart2", "行政处罚安全数", "同比增幅", res);
                    });
                },
                //绘制柱图
                LinechartsShow(id, name1, name2, data) {
                    const myChartsDivine = echarts.init(document.getElementById(id));
                    let x = data.map((item) => {
                        return item.name;
                    });
                    let y = data.map((item) => {
                        return item.value;
                    });
                    let y1 = data.map((item) => {
                        return item.value1;
                    });
                    let option = {
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },

                        grid: {
                            left: "8%",
                            top: "18%",
                            right: "8%",
                            bottom: "10%",
                        },
                        legend: {
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                        },
                        xAxis: {
                            data: x,
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: "#aaa",
                                    width: 1,
                                },
                            },
                            axisTick: {
                                show: true, //隐藏X轴刻度
                                alignWithLabel: true,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff", //X轴文字颜色
                                    fontSize: 28,
                                },
                                interval: 0,
                                // rotate: 30,
                            },
                        },
                        yAxis: [
                            {
                                type: "value",
                                name: "",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        width: 1,
                                        color: "#3d5269",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                            {
                                type: "value",
                                name: "",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                position: "right",
                                splitLine: {
                                    show: false,
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#fff",
                                        width: 2,
                                    },
                                },
                                axisLabel: {
                                    show: true,
                                    formatter: "{value} ", //右侧Y轴文字显示
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: name1,
                                type: "bar",
                                barWidth: 70,
                                color: "#5087EC",
                                itemStyle: {
                                    normal: {
                                        color: "#5087EC",
                                    },
                                },
                                data: y,
                            },

                            {
                                name: name2,
                                type: "line",
                                yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                                showAllSymbol: true, //显示所有图形。
                                // symbol: "circle", //标记的图形为实心圆
                                symbolSize: 10, //标记的大小
                                itemStyle: {
                                    normal: {
                                        color: "#26D9FF",
                                        lineStyle: {
                                            color: "#26D9FF",
                                            width: 4,
                                        },
                                    },
                                },
                                data: y1,
                            },
                        ],
                    };

                    myChartsDivine.setOption(option);
                    tools.loopShowTooltip(myChartsDivine, option, {
                        loopSeries: true,
                    }); //轮播
                },
            },
            //项目生命周期
            mounted() {
                this.init();
            },
        });
    </script>
</html>
