<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>重大危险源分类统计</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <link rel="stylesheet" href="../css/shgl-csaq-zdwxyfltj-left.css" />
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <style>
            .tables {
                height: 450px;
                overflow: auto;
            }
            .tables::-webkit-scrollbar {
                width: 0 !important;
            }
        </style>
    </head>

    <body>
        <div id="shgl-csaq-zdwxyfltj-left">
            <div class="content">
                <div class="title">
                    <nav style="padding: 20px 45px 0 45px">
                        <s-header-title
                            style="width: 100%"
                            title="重大危险源风险态势评估展示"
                            htype="1"
                        ></s-header-title>
                    </nav>
                </div>
                <div class="titleBox">
                    <div id="barEcharts001" style="height: 450px; width: 950px"></div>
                    <div>
                        <div class="zdwxy">
                            <el-select class="wxlx" v-model="value" placeholder="风险类型" @change="change1">
                                <el-option
                                    v-for="item in options"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                            <el-select class="city" v-model="value1" placeholder="请选择" @change="change">
                                <el-option
                                    v-for="item in options1"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                            <el-select class="wxlx" v-model="value2" placeholder="请选择" @change="changeType">
                                <el-option
                                    v-for="item in options2"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </div>
                        <div class="tables">
                            <table class="table">
                                <thead class="thead">
                                    <tr>
                                        <th v-for="item in thName">{{item}}</th>
                                    </tr>
                                </thead>
                                <tbody class="tbody">
                                    <tr v-for="item in tableList">
                                        <td>{{item.name}}</td>
                                        <td>{{item.value}}</td>
                                        <td>{{item.type}}</td>
                                        <td>{{item.ssgs}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="title" style="position: relative">
                    <nav style="padding: 20px 45px 0 45px">
                        <s-header-title style="width: 100%" title="重大危险源风险统计" htype="1"></s-header-title>
                    </nav>
                </div>
                <div class="titleBox">
                    <div class="title" style="flex: 1">
                        <nav style="padding: 20px 0 20px 0">
                            <s-header-title2 title="风险源区域分布" htype="2"></s-header-title2>
                        </nav>
                        <div id="barEcharts002" style="height: 400px"></div>
                    </div>
                    <div class="title" style="position: relative">
                        <nav style="padding: 20px 0 20px 0">
                            <s-header-title2 title="风险源等级分布" htype="2"></s-header-title2>
                        </nav>
                        <!-- <el-button class="btnContent btn" type="primary" @click="exportChart" style="z-index:10">导出</el-button> -->
                        <div id="pieEcharts001" style="height: 400px"></div>
                    </div>
                </div>
                <div class="titleBox">
                    <div class="title" style="flex: 1">
                        <nav style="padding: 20px 0 20px 0">
                            <s-header-title2 title="风险源风险大类分布" htype="2"></s-header-title2>
                        </nav>
                        <div id="pieEcharts002" style="height: 400px"></div>
                    </div>
                    <div class="title">
                        <nav style="padding: 20px 0 20px 0">
                            <s-header-title2 title="产品导出" htype="2"></s-header-title2>
                        </nav>
                        <div>
                            <div class="cpdc" v-for="item in cpdcList">
                                <div class="box-cpdc">
                                    <div class="box-content" @click="previewPdf(item.url)">{{item.name}}</div>
                                    <el-button class="btnContent" type="primary" @click="exportPdf(item)"
                                        >导出
                                        <!-- <a
                      style="text-decoration: none; color: #fff"
                      :href="item.url"
                      :download="item.name"
                      >导出</a
                    > -->
                                    </el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script src="/static/js/jslib/jquery-3.4.1.min.js"></script>
<script>
    var vm = new Vue({
        el: "#shgl-csaq-zdwxyfltj-left",
        data: {
            thName: ["风险名称", "行政区别", "风险等级", "所属公司"],
            options: [
                {
                    value: "化学品类",
                    label: "化学品类",
                },
                {
                    value: "辐射类",
                    label: "辐射类",
                },
                {
                    value: "生物类",
                    label: "生物类",
                },
                {
                    value: "特种设备类",
                    label: "特种设备类",
                },
                {
                    value: "电气类",
                    label: "电气类",
                },
                {
                    value: "土木工程类",
                    label: "土木工程类",
                },
                {
                    value: "交通运输类",
                    label: "交通运输类",
                },
            ],
            value: "自然风险",
            options1: [
                {
                    value: "金华市",
                    label: "金华市",
                },
                {
                    value: "金义新区",
                    label: "金义新区",
                },
                {
                    value: "兰溪市",
                    label: "兰溪市",
                },
                {
                    value: "婺城区",
                    label: "婺城区",
                },
            ],
            value1: "金华市",
            options2: [
                {
                    value: "一级",
                    label: "一级",
                },
                {
                    value: "二级",
                    label: "二级",
                },
                {
                    value: "三级",
                    label: "三级",
                },
                {
                    value: "四级",
                    label: "四级",
                },
            ],
            value2: "一级",
            tableList: [],
            value3: "",
            cpdcList: [
                {
                    name: "风险评估报告",
                    url: "http://dsj-csddzspt.oss-cn-jinhua-jhzwy-d01-a.inner.jhszwy.net/csdn/file/%E9%A3%8E%E9%99%A9%E8%AF%84%E4%BC%B0%E6%8A%A5%E5%91%8A/%E9%A3%8E%E9%99%A9%E8%AF%84%E4%BC%B0%E6%8A%A5%E5%91%8A.pdf?Expires=1667776697&OSSAccessKeyId=vzxpFmp10GY2FgDM&Signature=AL5Q8ijrx12ofYAOmOlSLX4wgwU%3D",
                },
                {
                    name: "行业风险评估报告",
                    url: "http://dsj-csddzspt.oss-cn-jinhua-jhzwy-d01-a.inner.jhszwy.net/csdn/file/%E8%A1%8C%E4%B8%9A%E9%A3%8E%E9%99%A9%E8%AF%84%E4%BC%B0%E6%8A%A5%E5%91%8A/%E8%A1%8C%E4%B8%9A%E9%A3%8E%E9%99%A9%E8%AF%84%E4%BC%B0%E6%8A%A5%E5%91%8A.pdf?Expires=1667776886&OSSAccessKeyId=vzxpFmp10GY2FgDM&Signature=v25tV9SbVAct3hoyB%2B9Si7dGx2I%3D",
                },
                {
                    name: "区域风险评估报告",
                    url: "http://dsj-csddzspt.oss-cn-jinhua-jhzwy-d01-a.inner.jhszwy.net/csdn/file/%E5%8C%BA%E5%9F%9F%E9%A3%8E%E9%99%A9%E8%AF%84%E4%BC%B0%E6%8A%A5%E5%91%8A/%E5%8C%BA%E5%9F%9F%E9%A3%8E%E9%99%A9%E8%AF%84%E4%BC%B0%E6%8A%A5%E5%91%8A.pdf?Expires=1667776836&OSSAccessKeyId=vzxpFmp10GY2FgDM&Signature=bvT0rPGaF0ajHGuAn%2F5LeZi2aZw%3D",
                },
            ],
        },
        mounted() {
            this.open();
            this.initFun();
        },
        methods: {
            //产品导出
            //预览
            previewPdf(url) {
                window.open(url, "_blank");
            },
            //导出
            exportPdf(item) {
                $.ajax({
                    url: baseURL.admApi + "/system/file/download",
                    // url: "/adm-api/system/file/download",
                    type: "get",
                    data: {
                        fileName: item.name + ".PDF",
                    },
                    headers: {
                        Authorization: sessionStorage.getItem("Authorization"),
                    },
                    dataType: "json",
                    success: function (res) {
                        const blob = new Blob([res]);
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement("a");
                        a.setAttribute("download", item.name + ".pdf");
                        a.setAttribute("href", url);
                        a.click();
                        document.body.removeChild(a);
                    },
                    error: function (res) {
                        const blob = new Blob([res]);
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement("a");
                        a.setAttribute("download", item.name + ".PDF");
                        a.setAttribute("href", url);
                        a.click();
                        document.body.removeChild(a);
                    },
                });
            },
            changeType(item) {
                $api("shgl_zdwxyfltj_zdwxyfltj002", {
                    value: this.value1,
                    type: item,
                    name: this.value,
                }).then((res) => {
                    this.tableList = res;
                });
            },
            change1(item) {
                $api("shgl_zdwxyfltj_zdwxyfltj002", {
                    value: this.value1,
                    type: this.value2,
                    name: item,
                }).then((res) => {
                    this.tableList = res;
                });
            },
            change(item) {
                $api("shgl_zdwxyfltj_zdwxyfltj002", {
                    value: item,
                    type: this.value2,
                    name: this.value,
                }).then((res) => {
                    this.tableList = res;
                });
            },
            initFun() {
                $api("shgl_zdwxyfltj_zdwxyfltj001").then((res) => {
                    this.getEcharts01("barEcharts001", res);
                });

                $api("shgl_zdwxyfltj_zdwxyfltj002", {
                    value: this.value1,
                    type: this.value2,
                }).then((res) => {
                    this.tableList = res;
                });
                $api("shgl_zdwxyfltj_zdwxyfltj003").then((res) => {
                    this.getEcharts02("barEcharts002", res);
                });

                $api("shgl_zdwxyfltj_zdwxyfltj004").then((res) => {
                    this.getEcharts03("pieEcharts001", res);
                });

                $api("shgl_zdwxyfltj_zdwxyfltj005").then((res) => {
                    this.getEcharts04("pieEcharts002", res);
                });
            },
            open() {
                let iframe1 = {
                    type: "openIframe",
                    name: "shgl-csaq-zdwxyfltj-bottom",
                    src: baseURL.url + "/static/citybrain/shgl/pages/shgl-csaq-zdwxyfltj-bottom.html",
                    width: "5500px",
                    height: "125px",
                    left: "2200px",
                    top: "1950px",
                    zIndex: "555",
                };
                let iframe2 = {
                    type: "openIframe",
                    name: "shgl-csaq-zdwxyfltj-select",
                    src: baseURL.url + "/static/citybrain/shgl/pages/shgl-csaq-zdwxyfltj-select.html",
                    width: "460px",
                    height: "400px",
                    left: "2210px",
                    top: "230px",
                    zIndex: "100",
                };
                window.parent.postMessage(JSON.stringify(iframe1), "*");
                window.parent.postMessage(JSON.stringify(iframe2), "*");
            },
            getEcharts01(id, echartsData) {
                const myChartsRun = echarts.init(document.getElementById(id));
                let option = {
                    tooltip: {
                        trigger: "item",
                        backgroundColor: "rgba(50,50,50,0.7)",
                        formatter: "{b} </br>{a} {c}",
                        borderColor: "rgba(50,50,50,0.7)",
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    xAxis: {
                        type: "category",
                        data: echartsData.map((item) => item.name),
                        offset: 10,
                        axisLabel: {
                            rotate: 30,
                            textStyle: {
                                fontSize: 30,
                                color: "#fff",
                            },
                        },
                    },
                    yAxis: {
                        type: "value",

                        splitLine: {
                            show: false,
                        },
                        axisLabel: {
                            textStyle: {
                                fontSize: 30,
                                color: "#fff",
                            },
                        },
                    },
                    series: [
                        {
                            name: "园林绿化设施数量",
                            data: echartsData.map((item) => item.value),
                            type: "bar",
                            label: {
                                show: true,
                                textStyle: {
                                    color: "#5087ec",
                                    fontSize: 30,
                                },
                                position: "outside",
                            },
                        },
                    ],
                };
                myChartsRun.setOption(option);
                tools.loopShowTooltip(myChartsRun, option, {
                    loopSeries: true,
                }); //轮播
            },
            getEcharts02(id, echartsData) {
                const myChartsRun = echarts.init(document.getElementById(id));
                let option = {
                    tooltip: {
                        trigger: "item",
                        backgroundColor: "rgba(50,50,50,0.7)",
                        formatter: "{b} </br>{a} {c}",
                        borderColor: "rgba(50,50,50,0.7)",
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    xAxis: {
                        type: "category",
                        data: echartsData.map((item) => item.name),
                        offset: 10,
                        axisLabel: {
                            rotate: 30,
                            textStyle: {
                                fontSize: 30,
                                color: "#fff",
                            },
                        },
                    },
                    yAxis: {
                        type: "value",

                        splitLine: {
                            show: false,
                        },
                        axisLabel: {
                            textStyle: {
                                fontSize: 30,
                                color: "#fff",
                            },
                        },
                    },
                    series: [
                        {
                            name: "园林绿化设施数量",
                            data: echartsData.map((item) => item.value),
                            type: "bar",
                            label: {
                                show: true,
                                textStyle: {
                                    color: "#5087ec",
                                    fontSize: 30,
                                },
                                position: "outside",
                            },
                        },
                    ],
                };
                myChartsRun.setOption(option);
                tools.loopShowTooltip(myChartsRun, option, {
                    loopSeries: true,
                }); //轮播
            },
            getEcharts03(id, echartsData) {
                const myChartsRun = echarts.init(document.getElementById(id));
                let option = {
                    toolbox: {
                        top: 0,
                        right: 80,
                        itemSize: 30,
                        feature: {
                            saveAsImage: {
                                title: "导出",
                                backgroundColor: "#000",
                            },
                        },
                        iconStyle: {
                            borderColor: "#fff",
                            borderWidth: 3,
                        },
                        emphasis: {
                            iconStyle: {
                                textFill: "#fff",
                                textPadding: [10, 0, 0, 0],
                            },
                        },
                    },
                    tooltip: {
                        trigger: "item",
                        backgroundColor: "rgba(50,50,50,0.7)",
                        borderColor: "rgba(50,50,50,0.7)",
                        formatter: "{b} : {c}",
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    series: [
                        {
                            type: "pie",
                            radius: "80%",
                            data: echartsData,
                            label: {
                                fontSize: 30,
                                color: "#fff",
                                formatter: "{b} \n {c}",
                            },
                            labelLine: {
                                length: 20,
                                length2: 20,
                            },

                            emphasis: {
                                itemStyle: {
                                    fontSize: 30,
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: "rgba(0, 0, 0, 0.5)",
                                },
                            },
                        },
                    ],
                };
                myChartsRun.setOption(option);
                tools.loopShowTooltip(myChartsRun, option, {
                    loopSeries: true,
                }); //轮播
            },
            getEcharts04(id, echartsData) {
                const myChartsRun = echarts.init(document.getElementById(id));
                let option = {
                    tooltip: {
                        trigger: "item",
                        backgroundColor: "rgba(50,50,50,0.7)",
                        borderColor: "rgba(50,50,50,0.7)",
                        formatter: "{b} : {c}",
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    series: [
                        {
                            type: "pie",
                            radius: "80%",
                            data: echartsData,
                            label: {
                                fontSize: 30,
                                color: "#fff",
                                formatter: "{b} \n {c}",
                            },
                            labelLine: {
                                length: 20,
                                length2: 20,
                            },

                            emphasis: {
                                itemStyle: {
                                    fontSize: 30,
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: "rgba(0, 0, 0, 0.5)",
                                },
                            },
                        },
                    ],
                };
                myChartsRun.setOption(option);
                tools.loopShowTooltip(myChartsRun, option, {
                    loopSeries: true,
                }); //轮播
            },
        },
    });
</script>
