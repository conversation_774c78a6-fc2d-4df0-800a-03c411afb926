﻿/*
 * @Description: file content
 * @Author: xufeng
 * @Date: 2022-04-26 10:40:01
 * @LastEditTime: 2022-08-07 13:58:06
 * @FilePath: \jinhua\lib\EGS(v1.0.0)\js\bastMap.js
 */
class BastMap {
    // 弹窗
    constructor(egs, map) {
        this.egs = egs;      // 地图类
        this.map = map;      // 地图对象
        this.popups = null;  // 弹窗
        this.addSouceLayers = {}; // 所有数据图层集合
        this.pointLayers = {};  // 带弹窗的点位集合
        this.heatMap = null; // 热力图层
        this.columnarThermodynamicDiagram = null; // 柱状热力图
        this.videoPointAggregationMap = null; // 聚合图
        this.circleLayerId = []; // 圆形layerid
        this.polygonLayerId = [];

        // 线缓冲数据查询对象
        this.lineStringBuffObj = {};
        // 3D文字对象
        this.text3DObj = {};
        this.popArr = [];
        this.text3DShow = [] // 带隐藏的3D文字数组
        this.mapIdArray = [];

        this.tipsPopup = null;

        this.text3DShowfun();
    }

    text3DShowfun() {
        this.map.on('zoomend', () => {
            const length = this.text3DShow.length
            for (let i = 0; i < length; i++) {
                if (map.getZoom() >= 16) {
                    map.setLayoutProperty(this.text3DShow[i], 'visibility', 'visible')
                } else {
                    map.setLayoutProperty(this.text3DShow[i], 'visibility', 'none')
                }
            }
        })
    }

    /**
     * @description: 初始化天空图层
     */
    addSkyLayer() {
        this.map.addLayer({
            id: "sky",
            type: "sky",
            paint: {
                "sky-type": "atmosphere",
                "sky-atmosphere-sun": [0.0, 0.0],
                "sky-atmosphere-sun-intensity": 15,
            },
        });
    }
    /**
     * @description: 基础图层切换，显示于影藏
     * @param {*} data:配置中的bastLayers:[], showID:初始地图显示的ID:string,
     */
    creatBastLayer(urlsIndex, bastLayers, showID) {
        let showUrls;
        for (let j = 0; j < bastLayers.length; j++) {
            if (bastLayers[j].id == showID) {
                showUrls = bastLayers[j];
                break
            }
        }
        for (let i = 0; i < urlsIndex.length; i++) {
            let layerConfig = {
                id: urlsIndex[i].id,
                type: 'raster',
                source: {
                    type: 'raster',
                    tiles: [urlsIndex[i].url],  // 4326
                    tileSize: 256,
                },
            }
            let isShow = false;
            for (let k = 0; k < showUrls.urls.length; k++) {
                if (urlsIndex[i].id == showUrls.urls[k]) {
                    isShow = true;
                    break;
                }
            }
            if (isShow) {
                layerConfig.layout = {
                    'visibility': 'visible'
                }
            } else {
                layerConfig.layout = {
                    'visibility': 'none'
                }
            }
            if (i == 1) {
                // 添加遮罩
                let worldwide = [[[-180, -90], [180, -90], [180, 90], [-180, 90], [-180, -90]]];
                var boundGeo = turf.polygon(worldwide);
                let arr = []
                arr.push(jinhua.features[0].geometry.coordinates[0][0])
                var boundJHGeo = turf.polygon(arr);
                var clipped = turf.difference(boundGeo, boundJHGeo);
                this.map.addLayer({
                    id: 'maine',
                    type: 'fill',
                    source: {
                        type: 'geojson',
                        data: clipped,
                    },
                    layout: {},
                    paint: {
                        'fill-color': '#08294A',
                        'fill-opacity': 1,
                    },
                })
            }
            this.map.addLayer(layerConfig);
        }



        // this.map.addLayer({
        //     id: 'maine',
        //     type: 'fill',
        //     source: {
        //       type: 'geojson',
        //       data: jinhua,
        //     },
        //     layout: {},
        //     paint: {
        //       'fill-color': '#fff',
        //       'fill-opacity': 1,
        //     },
        // })    
        // map.addLayer(
        //     {
        //         id: 'ArcGIS_WMTS_Map',
        //         type:'raster',
        //         source: {
        //             type: 'raster',
        //             tiles: ["http://************:8081/tileMap/services/MapServer/jh_test/tile/otherF/{z}/{y}/{x}"],  // 3857
        //             tileSize: 256,
        //             //zoomOffset: -1,
        //             scheme: "xyz"
        //         },
        //     },
        // );
    }
    /**
     * @description: 加载图层
     * @param {*} data,{funcName:string, layerUrl:string}
     */
    addMap(data) {
        let _this = this;
        if (data.data) {
            let layerConfig = {
                id: data.data.id || _this.creatUuid(8, 16),
                type: 'raster',
                source: {
                    id: data.id || _this.creatUuid(8, 16),
                    type: 'raster',
                    tiles: (data.data.url instanceof Array) ? data.data.url : [data.data.url], // 4326
                    tileSize: 256,
                },
            }
            this.map.addLayer(layerConfig);
            this.mapIdArray.push(layerConfig.id)
        }
    }
    /**
    * @description:删除图层
    * @param {*} data,{id:string}
    */
    removeMap(id) {
        if (this.map.getLayer(id)) {
            this.map.removeLayer(id)
            this.map.removeSource(id)
            this.mapIdArray = this.mapIdArray.filter(item => item != id)
        }
    }
    /**
     * @description: 图层切换
     * @param {*} data,{funcName:string, layerUrl:string}
     */
    toggleBastLayer(obj) {
        let showId = obj.id;
        for (let m = 0; m < mapConfig.urlsIndex.length; m++) {
            this.map.setLayoutProperty(mapConfig.urlsIndex[m].id, 'visibility', "none");
        }
        for (let i = 0; i < mapConfig.bastLayers.length; i++) {
            if (showId == mapConfig.bastLayers[i].id) {
                // 显示下面的图层
                for (let j = 0; j < mapConfig.bastLayers[i].urls.length; j++) {
                    this.map.setLayoutProperty(mapConfig.bastLayers[i].urls[j], 'visibility', "visible");
                }
            }
        }
    }
    /**
     * @description: 创建UUid
     * @param {Number} len 位数
     * @param {Number} radix 进制
     * @return {*} uuid
     */
    creatUuid(len, radix) {
        var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
        var uuid = [], i;
        radix = radix || chars.length;

        if (len) {
            // Compact form
            for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix];
        } else {
            // rfc4122, version 4 form
            var r;

            // rfc4122 requires these characters
            uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
            uuid[14] = '4';

            // Fill in random data.  At i==19 set the high bits of clock sequence as
            // per rfc4122, sec. 4.1.5
            for (i = 0; i < 36; i++) {
                if (!uuid[i]) {
                    r = 0 | Math.random() * 16;
                    uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
                }
            }
        }

        return uuid.join('');
    }

    /************************************************ 线缓冲查询 ********************************************************/

    getRoadVideo(distance = '50', lngLats = '119.64600294828413,29.10509534934764;119.6462121605873,29.10573747284333') {
        let _this = this;
        var formData = new FormData();
        formData.append('distance', distance)
        formData.append('lngLats', lngLats)

        //return http.post(`http://*************:8180/data_use/road/videos`,formData);
        // 显示图片视屏点位置
        // 添加点位置
        //const searchData = {"data":[{"channel_name":"GZ550011aGF1婺城浙师大东门_DH201912HS雪亮X","score":0.0,"gid":"e020352d4c9b4f3fb7226ce63c35b882","channel_code":"33070255001321084314","geom":"POINT (119.646872 29.138178)"},{"channel_name":"GZ550025aGM1浙师大东门_DH201912YD雪亮X","score":0.0,"gid":"4860e558b86244d4af870567916334c9","channel_code":"33070255001321084758","geom":"POINT (119.647074 29.138123)"},{"channel_name":"GJ583267aQM1迎宾大道高速出口球机_DH201912DX雪亮G","score":0.0,"gid":"4e3f766eb6fd4b92883beb72c1ab9dd8","channel_code":"33070299001321043766","geom":"POINT (119.647273 29.139431)"}],"total":3,"pages":1,"pageNum":1,"pageSize":500}
        //axios({
        //method: "post",
        //url: "http://*************:8180/data_use/road/videos",
        //data: formData,
        //}).then(searchData => {
        //searchData.json()
        //}).then(res => {
        //_this.secrchPoint(res.data)
        //}).catch(res =>{})    
        fetch('http://*************:8180/data_use/road/videos', {
            method: 'POST',
            body: formData,
            mode: 'cors',
            headers: new Headers({
                // 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryrGKCBY7qhFd3TrwA',
                'Access-Token': window.token
            })
        }).then(res => res.json()).then(res => {
            _this.secrchPoint(res)
        }).catch(res => console.log(res))
    }
    secrchPoint(searchData) {
        let _this = this;
        if (searchData.data && searchData.data.length) {
            // 循环生成点位
            let geojsonData = { "type": "FeatureCollection", "features": [] }
            for (let i = 0; i < searchData.data.length; i++) {
                const element = searchData.data[i];
                let lonLat = element.geom.slice(element.geom.indexOf("(") + 1, element.geom.indexOf(")"))
                let lonLatArr = lonLat.split(" ");
                let feauther = turf.point(lonLatArr, element);
                geojsonData.features.push(feauther);
            }
            // 判断图标是否加载
            const imgUrl = "./image/spritesImage/videoCon.png";
            // if(!this.map.getImage("searchImg")){
            this.map.loadImage(imgUrl, (error, image) => {
                if (error) throw error;
                this.map.addImage("searchImg", image);
                // 数据处理
                addSearchLayer(geojsonData)

            })
            // }
            function addSearchLayer(geojsonData) {
                // 创建UUid
                let uuid = _this.creatUuid(8, 16);
                let searcgLayer = {
                    "id": uuid,
                    "type": "symbol",
                    "source": {
                        'type': 'geojson',
                        'data': geojsonData,
                    },
                    "layout": {
                        'icon-image': 'searchImg', // reference the image
                        'icon-size': 0.5,
                        "icon-offset": [0, 0],
                        "icon-allow-overlap": true,
                        //"text-allow-overlap": true
                    },
                }
                _this.lineStringBuffObj[uuid] = searcgLayer;
                _this.map.addLayer(searcgLayer);
                _this.map.on("click", uuid, (e) => {
                    searchLayerClick(e);
                })
                _this.map.on('mouseenter', uuid, () => {
                    _this.layerMouseenter();
                });
                _this.map.on('mouseleave', uuid, () => {
                    _this.layerMouseleave()
                });
                // 定位位置
                let jzData = turf.bbox(geojsonData);
                _this.map.fitBounds(jzData, { padding: 1000 });
            }
            function searchLayerClick(e) {
                // searchData
                // 
                if (e.features[0].properties && e.features[0].properties.channel_code) {
                    window.wsss.openVideo([e.features[0].properties.channel_code])
                }
            }
        } else if (searchData.length > 0) {
            // 循环生成点位
            let geojsonData = { "type": "FeatureCollection", "features": [] }
            for (let i = 0; i < searchData.length; i++) {
                const element = searchData[i];
                let lonLat = [Number(element.x), Number(element.y)]
                let feauther = turf.point(lonLat, element);
                console.log(feauther)
                geojsonData.features.push(feauther);
            }
            // 判断图标是否加载
            const imgUrl = "./image/spritesImage/videoCon.png";
            // if(!this.map.getImage("searchImg")){
            this.map.loadImage(imgUrl, (error, image) => {
                if (error) throw error;
                this.map.addImage("searchImg", image);
                // 数据处理
                addSearchLayer(geojsonData)

            })
            // }
            function addSearchLayer(geojsonData) {
                // 创建UUid
                let uuid = _this.creatUuid(8, 16);
                let searcgLayer = {
                    "id": uuid,
                    "type": "symbol",
                    "source": {
                        'type': 'geojson',
                        'data': geojsonData,
                    },
                    "layout": {
                        'icon-image': 'searchImg', // reference the image
                        'icon-size': 0.5,
                        "icon-offset": [0, 0],
                        "icon-allow-overlap": true,
                        //"text-allow-overlap": true
                    },
                }
                _this.lineStringBuffObj[uuid] = searcgLayer;
                _this.map.addLayer(searcgLayer);
                _this.map.on("click", uuid, (e) => {
                    searchLayerClick(e);
                })
                _this.map.on('mouseenter', uuid, () => {
                    _this.layerMouseenter();
                });
                _this.map.on('mouseleave', uuid, () => {
                    _this.layerMouseleave()
                });
                // 定位位置
                let jzData = turf.bbox(geojsonData);
                _this.map.fitBounds(jzData, { padding: 1000 });
            }
            function searchLayerClick(e) {
                console.log(e, 123)
                // if(e.features[0].properties && e.features[0].properties.channel_code){
                //     console.log(e,123)
                //     window.wsss.openVideo([e.features[0].properties.channel_code])
                // }
            }
        }
    }
    /**
     * @description: 线缓冲删除
     * @return {*}
     */
    removeRoadVideo() {
        // 弹窗移除

        // 图层删除
        for (const key in this.lineStringBuffObj) {
            if (this.map.getLayer(key)) {
                // 事件删除
                this.map.off('click', key);
                this.map.off('mouseenter', key);
                this.map.off('mouseleave', key)
                // 图层删除
                this.map.removeLayer(key)
                this.map.removeSource(key)
                delete this.lineStringBuffObj[key];
            }
        }
    }
    /**
     * @description: 添加3D文字
     * @return {*}
     */
    text3D(data) {
        const textData = data.textData
        const color = data.color ? `rgba(${data.color[0]},${data.color[1]},${data.color[2]},${data.color[3]})` : "rgb(250,250,6)"
        if (textData && textData.length) {
            let geojsonData = { "type": "FeatureCollection", "features": [] }
            for (let i = 0; i < textData.length; i++) {
                const element = textData[i];
                let feauther = turf.point([element.pos[0], element.pos[1]], { text: element.text, type: element.type })
                geojsonData.features.push(feauther);
            }
            let uuid = this.creatUuid(8, 16);
            let symbolLayer = {
                "id": data.id || uuid,
                "type": "symbol",
                "source": {
                    'type': 'geojson',
                    'data': geojsonData
                },
                "layout": {
                    "text-field": "{text}",
                    "text-font": [
                        // "songti",
                        'Microsoft YaHei Regular'
                        //  "Open Sans Bold,Arial Unicode MS Bold",
                    ],
                    "text-size": data.textSize || 40,
                    "text-offset": [0, 0],
                    "text-anchor": "top",
                    "text-allow-overlap": true,

                },
                "paint": {
                    "text-color": color,
                    // "text-halo-color": "#fff", 
                    // "text-halo-width": 1,
                    // "text-opacity": 1 
                },
            }
            if (data.id) {
                this.text3DObj[data.id] = symbolLayer;
            } else {
                this.text3DObj[uuid] = symbolLayer;
            }
            if (data.zoomShow) {
                this.text3DShow.push(data.id || uuid)
                if (map.getZoom() >= 16) {
                    symbolLayer.layout.visibility = 'visible';
                } else {
                    symbolLayer.layout.visibility = 'none';
                }
            }
            this.map.addLayer(symbolLayer);
        }
    }
    // 根据id加载文字
    text3DById(data) {
        let textData = data.textData;
        if (textData && textData.length) {
            let geojsonData = { "type": "FeatureCollection", "features": [] }
            for (let i = 0; i < textData.length; i++) {
                const element = textData[i];
                let feauther = turf.point([element.pos[0], element.pos[1]], { text: element.text, type: element.type })
                geojsonData.features.push(feauther);
            }
            let uuid = this.creatUuid(8, 16);
            let symbolLayer = {
                "id": data.id || uuid,
                "type": "symbol",
                "source": {
                    'type': 'geojson',
                    'data': geojsonData
                },
                "layout": {
                    "text-field": "{text}",
                    "text-font": [
                        "Open Sans Semibold,Arial Unicode MS Bold",
                        //  "Open Sans Bold,Arial Unicode MS Bold",
                    ],
                    "text-size": (data.option && data.option.fontSize) ? data.option.fontSize : 40,
                    "text-offset": (data.option && data.option.offset) ? (data.option && data.option.offset) : [0, 0],
                    "text-anchor": "top",
                    "text-allow-overlap": true,

                },
                "paint": {
                    "text-color": (data.option && data.option.color) ? (data.option && data.option.color) : "rgb(250,250,6)",
                    "text-halo-color": "#fff",
                    "text-halo-width": 1,
                    "text-opacity": 1
                },
            }
            this.text3DObj[data.id || uuid] = symbolLayer;
            this.map.addLayer(symbolLayer);
        }


    }
    /**
     * @description: 清除3D文字
     * @return {*}
     */
    rm3DText() {
        console.log(this.text3DObj)
        if (this.text3DObj) {
            for (const key in this.text3DObj) {
                if (this.map.getLayer(key)) {
                    this.map.removeLayer(key)
                    this.map.removeSource(key)
                    delete this.pointLayers[key];
                }
            }
            this.text3DShow = [];
        }
    }
    /**
    * @description: 根据文字清除id
    * @return {*}
    */
    rm3DTextById(id) {
        if (this.text3DObj) {
            if (this.map.getLayer(id)) {
                this.map.removeLayer(id)
                this.map.removeSource(id)
                delete this.pointLayers[id];
                this.text3DShow.remove(id)
            }
        }
        // if(this.text3DObj&&id){
        //     if(this.map.getLayer(id)){
        //         this.map.removeLayer(id)
        //         this.map.removeSource(id)
        //         delete this.pointLayers[id];
        //     }  
        // }else{
        //     if(this.map.getLayer(key)){
        //         this.map.removeLayer(key)
        //         this.map.removeSource(key)
        //         delete this.pointLayers[key];
        //     }  
        // }


    }


    addTipsPoint(data) {
        const imgSize = data.imgSize ? data.imgSize : 1;
        const imgOffset = data.imgOffset ? data.imgOffset : [0, 0];
        const imgAllowOverlap = data.imgAllowOverlap ? data.imgAllowOverlap : true;
        const textColor = data.textColor ? data.textColor : 'black';
        const backgroundColor = data.backgroundColor ? data.backgroundColor : 'rgba(255,0,0,1)';
        const tipsOffset = data.tipsOffset ? data.tipsOffset : [0, 0]

        // 判断是否有图片
        if (!this.map.hasImage((data.id))) {
            this.map.loadImage(data.imgUrl, (error, image) => {
                if (error) throw error;
                this.map.addImage(data.id, image);
            })
        }

        const geoJson = { "type": "FeatureCollection", "features": [] };
        const length = data.pointDatas.length;
        for (let i = 0; i < length; i++) {
            const feature = turf.point(data.pointDatas[i].pos, { text: data.pointDatas[i].itemDatas.text, textColor, backgroundColor, tipsOffset, pos: data.pointDatas[i].pos });
            geoJson.features.push(feature);
        }

        // 添加点位图层
        const layer = {
            "id": data.id,
            "type": "symbol",
            "source": {
                'type': 'geojson',
                'data': geoJson,
            },
            "layout": {
                'icon-image': data.id, // reference the image
                'icon-size': imgSize,
                "icon-offset": imgOffset,
                "icon-allow-overlap": imgAllowOverlap,
            }
        }
        this.map.addLayer(layer);
        this.map.on("click", data.id, (e) => {
            const obj = this.map.queryRenderedFeatures(e.point, {
                layers: [data.id]
            })[0].properties;
            let pos = obj.pos.substr(1); //删除第一个字符
            pos = pos.substr(0, pos.length - 1); //删除第一个字符
            pos = pos.split(',')
            const centent = `<p style="color: ${obj.textColor}">${obj.text}</p>`;
            console.log(obj)
            if (this.tipsPopup) {
                this.tipsPopup.setLngLat([Number(pos[0]), Number(pos[1])]);
                this.tipsPopup.setContent(centent)
            } else {
                this.tipsPopup = new this.egs.eli.Popup(
                    {
                        content: centent,
                        center: [Number(pos[0]), Number(pos[1])],
                        // offset: obj.tipsOffset,
                        // closeButton: true
                    },
                    this.map
                )
            }
            // // 背景颜色
            document.getElementsByClassName('egs-popup-tip').length > 0 && (document.getElementsByClassName('egs-popup-tip')[0].style.borderTopColor = obj.backgroundColor);
            document.getElementsByClassName('egs-popup-content').length > 0 && (document.getElementsByClassName('egs-popup-content')[0].style.backgroundColor = obj.backgroundColor);
            // 按钮点击事件
            document.getElementsByClassName('egs-popup-close-button').length > 0 && (document.getElementsByClassName('egs-popup-close-button')[0].onclick = () => {
                this.tipsPopup = null;
            });
        })
        this.map.on('mouseenter', data.id, () => {
            this.layerMouseenter();
        });
        this.map.on('mouseleave', data.id, () => {
            this.layerMouseleave()
        });
    }

    /**
     * @description: 点位图层添加
     * @param {*} data: {funcName:string,pointType:点位类型图标 string,pointId:string,height:高度：number, pointData}
     */
    addPointLayer(data) {
        // 图片添加 
        let _this = this;
        let imgUrl
        if (data.pointType == '出租GPS') {
            imgUrl = "./image/spritesImage/taixGPS.png"
        } else {
            imgUrl = "./image/spritesImage/" + data.pointType + '.png';
        }
        this.map.loadImage(imgUrl, (error, image) => {
            if (error) throw error;
            let uuid = this.creatUuid(8, 16);
            data.pointId = data.pointId ? data.pointId.toString() : uuid;
            if (!this.map.hasImage((data.pointId))) { this.map.addImage(data.pointId, image); }
            // 数据处理
            _this.addSource.call(_this, data)
            // _this.map.on("click", uuid, (e)=>{
            //     searchLayerClick(e);
            // }) 
            // function searchLayerClick(e){
            //     console.log(e,123)
            //     if(e.features[0]&&e.features[0].properties&&e.features[0].properties.code){
            //         if(e.features[0].properties && e.features[0].properties.code){
            //             console.log(e,123)
            //             window.wsss.openVideo([e.features[0].properties.code])
            //         }
            //     }
            // }
        })
    }
    /**
     * @description: 添加数据
     */
    addSource(data) {
        let geojsonData = { "type": "FeatureCollection", "features": [] }
        let pointData = data.pointData;
        let pointId = data.pointId;
        for (let i = 0; i < pointData.length; i++) {
            const code = pointData[i].code;
            const pointArr = pointData[i].point.split(",");

            let feature = turf.point(pointArr, pointData[i] ? pointData[i] : {});
            geojsonData.features.push(feature);
        }
        // 添加点位图层
        if (!data.imageConfig) {
            data.imageConfig = {}
        }
        let layer = {
            "id": pointId,
            "type": "symbol",
            "source": {
                'type': 'geojson',
                'data': geojsonData,
                //"cluster": true,
                //"clusterRadius": 35 // 聚合半径
            },
            //"filter": ["==", "$type", "Point"],
            //"filter": ["in", "id", ""],
            "layout": {
                'icon-image': data.pointId, // reference the image
                'icon-size': data.imageConfig.iconSize ? data.imageConfig.iconSize : 1,
                "icon-offset": data.imageConfig.iconOffset ? data.imageConfig.iconOffset : [0, -30],
                "icon-allow-overlap": data.imageConfig.iconAllowOverlap ? data.imageConfig.iconAllowOverlap : true,
                //"text-allow-overlap": true
            },
        }
        this.map.addLayer(layer);
        // this.map.off('click', pointId, this.destinationPoint)
        this.map.on("click", pointId, (e) => {
            if (data.pointType === "camera-load1") {
                if (e.features[0] && e.features[0].properties && e.features[0].properties.code) {
                    window.wsss.openVideo([e.features[0].properties.code])
                }
            } else if (data.setClick) {
                window.parent.postMessage({
                    type: 'pointClick',
                    data: e.features[0].properties
                }, "*");

            }
            this.layerClick(e, data);

        })
        this.map.on('mouseenter', pointId, () => {
            this.layerMouseenter();
        });
        this.map.on('mouseleave', pointId, () => {
            this.layerMouseleave()
        });
        this.pointLayers[pointId] = layer;
        // this.map.parent.postMessage(geojsonData, "*");
        const bbox = this.egs.turf.bbox({
            "type": "geojson",
            data: geojsonData,
        });
        this.map.fitBounds(bbox, {
            padding: { top: 40, bottom: 40, left: 200, right: 200 },
        });
    }
    // 自定义HTMLtankuang
    customPoup(data) {
        // this.removePopup()
        this.popArr.push(new egs.eli.Popup(
            {
                // content: `<div style="background:red;width:240px;" >${data.coordinates}</div><button id="${data.id}"></button>`,
                content: data.html,
                closeButton: (data.popup && data.popup.closeButton) ? data.popup.closeButton : true,
                isTooltip: false,
                offset: (data.popup && data.popup.offset) ? data.popup.offset : [0, 20],
                center: data.coordinates,
            },
            this.map
        ))
        // document.getElementsByClassName('egs-popup')[0].style.maxWidth=null;
        const dom = document.getElementsByClassName('egs-popup-tip')
        document.getElementsByClassName('egs-popup-tip')[0].style.display = 'none';
        document.getElementsByClassName('egs-popup-content').length > 0 && (document.getElementsByClassName('egs-popup-content')[0].style.backgroundColor = 'rgba(0,0,0,0)');
        document.getElementsByClassName('egs-popup-close-button')[0].style.display = 'none';

    }
    /**
     * @description: 图层点击
     */
    layerClick(e, data) {
        // 弹窗效果
        let coordinates = e.features[0].geometry.coordinates; // 当前点位数据
        let featureData = null;
        if (["skyj"].includes(data.pointType)) {
            featureData = e.features[0].properties
        } else {
            featureData = JSON.parse(e.features[0].properties.msg || e.features[0].properties.data);
        }
        // featureData = {"key":["名称","地址","电话"],"value":["王二","金华","1388888888"]}

        // let keyArr = featureData.key;
        // let valueArr = featureData.value;

        this.removePopup()
        // if(data.pointType==="air-point-1" || data.pointType==="water-section-1"){
        //     let obj=JSON.parse(e.features[0].properties.msg||e.features[0].properties.data)
        //     let content={
        //         "data":obj,
        //         "point":'',
        //         "eventType": data.pointType,
        //         "layerName": data.pointType,
        //         "popType": ""
        //     }
        //     window.top.postMessage(JSON.stringify(content), "*");
        // } else {
        // this.createPopup(data,featureData,coordinates)
        // }


        // 判断是否让对方来写弹窗
        if (["skyj-red", "skyj", 'air-point-1', 'water-source-1', 'water-section-1', 'digital-orange', 'szwh'].includes(data.pointType)) {
            window.top.postMessage(JSON.stringify({
                type: 'pointClick',
                data: featureData
            }), "*")
        } else {
            this.createPopup(data, featureData, coordinates)
        }
    }
    /**
     * @description: 创建弹窗
     */
    createPopup(data, featureData, coordinates) {

        let contentObject = {};
        this.removePopup()
        if (!data.popup) {
            data.popup = {};
        }
        switch (data.pointType) {
            case 'point-视频点位':
                contentObject = videoPointpopVondeFn(JSON.parse(JSON.stringify(videoPointpopVonde)), featureData);
                break;
            default:
                contentObject = publicPopVNodeFn(JSON.parse(JSON.stringify(publicPopVNode)), featureData);
                break;
        }
        this.popups = new egs.eli.Popup(
            {
                content: contentObject,
                // isTooltip: data.popup.isTooltip ? data.popup.isTooltip: true,
                closeButton: data.popup.closeButton ? data.popup.closeButton : true,
                height: data.popup.height ? data.popup.height : 0,
                width: data.popup.width ? data.popup.width : 0,
                offset: data.popup.offset ? data.popup.offset : [50, -500],
                center: coordinates,
            },
            this.map
        )
        document.getElementsByClassName('egs-popup-content')[0].style.backgroundColor = 'rgba(0,0,0,0)';
        document.getElementsByClassName('egs-popup-tip')[0].style.display = 'none';
        document.getElementsByClassName('egs-popup-close-button')[0].style.display = 'none';
        document.getElementById('title') && (document.getElementById('title').onclick = () => { this.removePopup() });
        document.getElementById('close') && (document.getElementById('close').onclick = () => { this.removePopup() });
        document.getElementById('video') && (document.getElementById('video').onclick = () => { window.wsss.openVideo([featureData.code]) });

    }
    /**
  * @description: 创建弹窗
  */
    createPopup2(data, featureData, coordinates) {
        let popNode = null
        if (data.type == "rkpc") {
            popNode = renkouPopVNode
            data.popup = {}
            data.popup.offset = [0, 300]
        } else if (data.type == "jjtj") {
            popNode = economicPopVNode
        } else {
            popNode = publicPopVNode2
        }
        const contentObject = publicPopVNodeFn2(JSON.parse(JSON.stringify(popNode)), featureData)
        if (!data.popup) {
            data.popup = {};
        }
        let poups = new egs.eli.Popup(
            {
                content: contentObject,
                // isTooltip: data.popup.isTooltip ? data.popup.isTooltip: true,
                closeButton: data.popup.closeButton ? data.popup.closeButton : true,
                height: data.popup.height ? data.popup.height : 0,
                width: data.popup.width ? data.popup.width : 0,
                offset: data.popup.offset ? data.popup.offset : [0, 0],
                center: coordinates,
            },
            this.map
        )
        this.popArr.push(poups)
        document.getElementsByClassName('egs-popup-content')[0].style.backgroundColor = 'rgba(0,0,0,0)';
        document.getElementsByClassName('egs-popup-tip')[0].style.display = 'none';
        document.getElementsByClassName('egs-popup-close-button')[0].style.display = 'none';
        document.getElementById('title').onclick = () => { poups.remove() };

    }
    /**
     * @description: 移除弹窗
     */
    removePopup() {
        if (this.popups) {
            this.popups.remove();
        }
        if (this.popArr.length) {
            this.popArr.forEach(item => {
                item.remove()
            })
            this.popArr = []
        }
    }
    /**
     * @description: 点位图层删除
     */
    removePointLayer(data) {
        // 弹窗删除
        if (this.popups) {
            this.popups.remove();
        }
        // 图层删除
        if (data.pointId) {
            if (this.map.getLayer(data.pointId)) {
                // 事件删除
                this.map.off('click', data.pointId);
                this.map.off('mouseenter', data.pointId);
                this.map.off('mouseleave', data.pointId)
                // 图层删除
                this.map.removeLayer(data.pointId)
                this.map.removeSource(data.pointId)
                delete this.pointLayers[data.pointId];
            }
        } else {
            // 全删除
            for (const key in this.pointLayers) {
                if (this.map.getLayer(key)) {
                    // 事件删除
                    this.map.off('click', key);
                    this.map.off('mouseenter', key);
                    this.map.off('mouseleave', key)
                    // 图层删除
                    this.map.removeLayer(key)
                    this.map.removeSource(key)
                    delete this.pointLayers[key];
                }
            }
        }
    }
    /**
     * @description: 添加文字标注
     * @param {*}
     * @return {*}
     */
    /**
     * flyToPoint@description: 图层移入
     */
    layerMouseenter() {
        this.map.getCanvas().style.cursor = 'pointer';
    }
    /**
     * @description: 图层移出
     */
    layerMouseleave() {
        this.map.getCanvas().style.cursor = '';
    }
    // 消息安全性检测
    checkMessage() {
        return true
    }
    /**
     * @description: 通过点定位
     * @param {*}
     * @return {*}
     */

    flyToPoint(data) {
        this.map.flyTo({
            center: [data.flyData.postion.x, data.flyData.postion.y],
            zoom: data.flyData.zoom ? data.flyData.zoom : 16
        });
    }

    /**
     * @description: 加载热力图
     * @param {*}
     * @return {*}
     */
    getHeatMap(pointArray, threshold, test = true) {
        this.removeSection()
        this.removeHeatMap();
        if (test) {
            const data = {
                "type": "FeatureCollection",
                "crs": { "type": "name", "properties": { "name": "urn:ogc:def:crs:OGC:1.3:CRS84" } },
                "features": []
            }

            // 获取最大值
            const array = [];
            const length = pointArray.length;
            for (let i = 0; i < length; i++) {
                if (pointArray[i].length == 4) {
                    array.push(pointArray[i][2])
                }
            }
            const maxNum = array.reduce((item, items) => {
                return item > items ? item : items
            })


            for (let i = 0; i < length; i++) {
                const weight = Number((pointArray[i][2] / maxNum).toFixed(4)) * 1000;
                data.features.push(turf.point(pointArray[i], { "id": i, "mag": weight }));
            }
		
            this.heatMap = this.map.createHeatmp(
                'heatMapDemo',
                data,
                {
                    // type: 'light',
                    opacity: 0.8,
                    // color:[[0,0,255],[52,0,248],[38,254,45],[231,255,39],[213,109,33],[248,5,9]]
                    color: [[62, 202, 253], [0, 0, 255], [44, 255, 101], [101, 255, 37], [249, 217, 34], [255, 0, 0]]
                }
            )
            // let flyPoint=pointArray[0]
        } else {
            this.heatMap = this.map.createHeatmp(
                'heatMapDemo',
                heatmap,
                {
                    // type: 'light',
                    opacity: 0.8,
                    color: [[0, 0, 255], [52, 0, 248], [38, 254, 45], [231, 255, 39], [213, 109, 33], [248, 5, 9]]
                }
            )
        }

    }
    /**
     * @description: 删除热力图
     * @param {*}
     * @return {*}
     */
    removeHeatMap() {
        if (this.heatMap) {
            this.heatMap.remove();
            this.heatMap = null;
        }
    }

    /**
     * @description: 加载柱状图
     * @param {*}
     * @return {*}
     */
    getHistogram(array) {
        this.removeHistogram();
        const regionalLocation = [
            {
                name: '浦江县',
                latLon: [119.94315399169922, 29.5630503845215]
            },
            {
                name: '兰溪市',
                latLon: [119.46214447021484, 29.28517738342285]
            },
            {
                name: '婺城区',
                latLon: [119.5569204711914, 29.00677101135254]
            },
            {
                name: '金义新区',
                latLon: [119.8483056640625, 29.188559951782227]
            },
            {
                name: '义乌市',
                latLon: [120.08206787109375, 29.322123641967773]
            },
            {
                name: '武义县',
                latLon: [119.7269204711914, 28.79677101135254]
            },
            {
                name: '永康市',
                latLon: [120.1469204711914, 28.97677101135254]
            },
            {
                name: '东阳市',
                latLon: [120.4169204711914, 29.24677101135254]
            },
            {
                name: '磐安县',
                latLon: [120.6299204711914, 29.06677101135254]
            },
        ];
        const sum = array.reduce((prev, cur) => prev + cur.num, 0);
        const features = [];
        const heightArray = [];
        const length = array.length;
        const textArrat = [];
        for (let i = 0; i < length; i++) {
            const latLon = regionalLocation.filter(item => item.name == array[i].name)[0].latLon;
            features.push(turf.circle(latLon, 2, { steps: 4, units: 'kilometers', properties: { id: i + 1 } }));
            heightArray.push(Math.ceil((array[i].num * 70000) / sum))
            textArrat.push({
                text: `${array[i].name}
                ${array[i].num}${array[i].unit}`,
                pos: latLon
            })
        }
        this.text3D(
            { textData: textArrat }
        );

        // 2022-8-1 9:30      张四虎组提出不需要柱状体飞行动画，经余宗庆组同意注释此方法
        // this.map.flyTo({center: [119.95919003962149, 29.263317761569176], zoom: 10,bearing:0,pitch:49});


        this.map.addLayer({
            id: 'histogramLayer',
            source: {
                type: 'geojson',
                data: {
                    "type": "FeatureCollection",
                    "features": features
                }
            },
            type: "fill-extrusion",
            'paint': {
                'fill-extrusion-color': '#409EFF',
                'fill-extrusion-height': [
                    'at',
                    ["-", ["get", "id"], 1],
                    ["literal", heightArray]
                ],
                'fill-extrusion-base': 1,
                'fill-extrusion-opacity': 0.8
            }
        });
    }
    /**
     * @description: 移除柱状图
     * @param {*}
     * @return {*}
     */
    removeHistogram() {
        if (this.map.getLayer('histogramLayer')) {
            this.map.removeLayer('histogramLayer');
            this.map.removeSource('histogramLayer');
        }
    }
    /**
     * @description: 加载版块高亮
     * @param {*}
     * @return {*}
     */
    getSectionHighlight(regionalSectionItem) {
        const regionalSectionFeatureCollection = JSON.parse(JSON.stringify(regionalSection));
        const regionalSectionFeatureLineCollection = JSON.parse(JSON.stringify(regionalSectionLine));
        regionalSectionFeatureCollection.features = regionalSectionFeatureCollection.features.filter(item => regionalSectionItem.some(items => item.properties.NAME == items.name));
        regionalSectionFeatureLineCollection.features = regionalSectionFeatureLineCollection.features.filter(item => regionalSectionItem.some(items => item.properties.FNAME == items.name));
        const fillExtrusionColor = ["match", ["get", "NAME"]];
        const fillExtrusionLineColor = ["match", ["get", "NAME"]];
        const length = regionalSectionItem.length
        for (let i = 0; i < length; i++) {
            const color = `rgba(${regionalSectionItem[i].color[0]},${regionalSectionItem[i].color[1]},${regionalSectionItem[i].color[2]},${regionalSectionItem[i].color[3]})`;
            fillExtrusionColor.push(regionalSectionItem[i].name)
            fillExtrusionLineColor.push(regionalSectionItem[i].name)
            fillExtrusionColor.push(color)
            fillExtrusionLineColor.push(color)
        }
        fillExtrusionColor.push('rgba(64,158,255,0.8)')
        fillExtrusionLineColor.push('#fff')

        if (this.map.getLayer('SectionHighlight')) {
            this.map.flyTo({ center: [120.00395942381772, 29.13218770513378], zoom: 10, bearing: 0, pitch: 0 });
            this.map.setPaintProperty('SectionHighlight', 'fill-extrusion-color', fillExtrusionColor);
            this.map.setPaintProperty('SectionHighlightLine', 'fill-extrusion-color', fillExtrusionLineColor);
        } else {
            console.log(regionalSectionFeatureLineCollection)
            regionalSectionLine
            this.map.addLayer({
                id: 'SectionHighlight',
                source: {
                    type: 'geojson',
                    data: regionalSectionFeatureCollection
                },
                type: "fill-extrusion",
                'paint': {
                    'fill-extrusion-color': fillExtrusionColor,
                    'fill-extrusion-height': 1000,
                    'fill-extrusion-base': 1,
                    'fill-extrusion-opacity': 0.4
                }
            });
            this.map.addLayer({
                id: 'SectionHighlightLine',
                source: {
                    type: 'geojson',
                    data: regionalSectionFeatureLineCollection
                },
                type: "fill-extrusion",
                'paint': {
                    'fill-extrusion-color': '#fff',
                    'fill-extrusion-height': 1010,
                    'fill-extrusion-base': 1000,
                    'fill-extrusion-opacity': 1
                }
            });
        }
    }
    /**
   * @description: 移除版块高亮
   * @param {*}
   * @return {*}
   */
    removeSectionHighlight(call = () => { }) {
        if (this.map.getLayer('SectionHighlight')) {
            this.map.setPaintProperty('SectionHighlight', 'fill-extrusion-color', 'rgba(64,158,255,0.8)');
            this.map.setPaintProperty('SectionHighlightLine', 'fill-extrusion-color', 'rgba(64,158,255,0.8)');
            call();
        }
    }

    /**
     * @description: 柱状热力图
     * @param {*}
     * @return {*}
     */
    getColumnarThermodynamicDiagram(array) {
        const data = {
            "type": "FeatureCollection",
            "crs": { "type": "name", "properties": { "name": "urn:ogc:def:crs:OGC:1.3:CRS84" } },
            "features": []
        }
        const length = array.length;
        for (let i = 0; i < length; i++) {
            data.features.push(turf.point([array[i].lng, array[i].lat], { "id": i, "mag": 1 }));
        }
        this.columnarThermodynamicDiagram = this.map.createHeatmp(
            'heatMapDemo',
            data,
            {
                type: "columnar",
                cellSizePixels: 300,
                elevationScale: 100,
            }
        )
    }
    /**
     * @description: 移除柱状热力图
     * @param {*}
     * @return {*}
     */
    removeColumnarThermodynamicDiagram() {
        if (this.columnarThermodynamicDiagram) {
            this.columnarThermodynamicDiagram.remove();
            this.columnarThermodynamicDiagram = null;
        }
    }
    /**
* @description: 移除区划板块
* @param {*}
* @return {*}
*/
    removeSection() {
        this.removeSectionHighlight(() => {
            this.map.removeLayer('SectionHighlight');
            this.map.removeSource('SectionHighlight');
            this.map.removeLayer('SectionHighlightLine');
            this.map.removeSource('SectionHighlightLine');
        });
    }
    /**
     * @description: 移除所有
     * @param {*}
     * @return {*}
     */
    removeAll() {
        this.layerMouseleave();
        this.removeHeatMap();
        this.removeHistogram();
        this.removePopup()
        //this.removeSectionHighlight();
        // this.removeSectionHighlight(() => {
        //     this.map.removeLayer('SectionHighlight');
        //     this.map.removeSource('SectionHighlight');
        //     this.map.removeLayer('SectionHighlightLine');
        //     this.map.removeSource('SectionHighlightLine');
        // });
        this.removeColumnarThermodynamicDiagram();
        this.removeRoadVideo()
        this.removeLineSegments();
        this.removeMeasuringSurface();
        this.rm3DText();
        const circleLength = this.circleLayerId.length;
        for (let i = 0; i < circleLength; i++) {
            this.removeCircle(this.circleLayerId[i]);
        }
        const polygonLength = this.polygonLayerId.length;
        for (let i = 0; i < polygonLength; i++) {
            this.removeCircle(this.polygonLayerId[i]);
        }
        // if (this.map.getLayer('SectionHighlight')) {
        //     this.map.removeLayer('SectionHighlight')
        // }
        this.removePointLayer({})

        // 删除所有wms服务
        const mapIdLength = this.mapIdArray.length;
        for (let i = 0; i < mapIdLength; i++) {
            if (this.map.getLayer(this.mapIdArray[i])) {
                this.map.removeLayer(this.mapIdArray[i]);
                this.map.removeSource(this.mapIdArray[i]);
            }
        }
        this.mapIdArray = [];

        this.map.flyTo({ center: [119.64748791232473, 29.078955841081186], zoom: 13, bearing: 0, pitch: 0 });

    }
    /**
     * @description: 视频缓冲点位
     * @param {*}
     * @return {*}
     */
    getJhData(size, type) {
        // return console.log('走到了')
        let num = 20000; //聚合视野高
        let stateType = type ? type : '无'
        let _this = this
        fetch(`http://*************:9000/jhVideo/videoPoi/getGrid?classNum=${num}&type=${stateType}`, {
            method: 'GET',
            mode: 'cors',
            headers: new Headers({
                // 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryrGKCBY7qhFd3TrwA',
                'Access-Token': window.token
            })
        }).then(resp => resp.json()).then(res => {
            if (res) {
                _this.videoPointAggregation(res)
            }
        }).catch(res => console.log(res))
    }
    /**
     * @description: 视频点位聚合
     * @param {*}
     * @return {*}
     */
    videoPointAggregation(videoPointArray) {
        console.log(videoPointArray)
        let _this = this
        const gojesion = {
            'type': 'FeatureCollection',
            'crs': {
                'type': 'name',
                'properties': {
                    'name': 'urn:ogc:def:crs:OGC:1.3:CRS84'
                }
            },
            'features': []
        }
        const length = videoPointArray.length;
        for (let i = 0; i < length; i++) {
            let arr = videoPointArray[i].point.split(',')
            gojesion.features.push(turf.point([Number(arr[0]), Number(arr[1])], {
                "coor": videoPointArray[i].id,
                "mag": 1,
                "name": videoPointArray[i].points,
                // "first": videoPointArray[i].first,
                // "second": videoPointArray[i].second,
                // "third": videoPointArray[i].third
            }))
        }

        this.map.addSource("earthquakes", {
            type: "geojson",
            data: gojesion,
            cluster: true,
            clusterMaxZoom: 16, //最大缩放到群集点
            clusterRadius: 400 // 每一组点的半径（=50）
        });

        // 外围有数字的圆圈，加晕染
        this.map.addLayer({
            id: "clusters",
            type: "circle",
            source: "earthquakes",
            filter: ["has", "point_count"],
            paint: {
                //*蓝色，当点数小于100时为20px圆
                //*点计数在100到750之间时为黄色，21px圆
                //*点计数大于或等于750时为22像素的粉红色圆圈
                "circle-color": [
                    "step",
                    ["get", "point_count"],
                    "rgba(81, 187, 214, 0.8)",
                    100,
                    "rgba(241, 240, 117, 0.8)",
                    750,
                    "rgba(242, 140, 177, 0.8)"
                ],
                "circle-radius": [
                    "step",
                    ["get", "point_count"],
                    20, //蓝色，当点数小于100时为20px圆
                    100, //对应上面circle-color 数字，意思为100以内
                    21, //点计数在100到750之间时为黄色，21px圆
                    750, //对应上面circle-color 数字，意思为750以内
                    22 //点计数大于或等于750时为22像素的粉红色圆圈
                ],
                // 这个是外边框的颜色 circle-stroke-color这个对应了上面circle-color
                "circle-stroke-color": [
                    "step",
                    ["get", "point_count"],
                    "rgba(81, 187, 214, 0.2)",
                    100,
                    "rgba(241, 240, 117, 0.2)",
                    750,
                    "rgba(242, 140, 177, 0.2)"
                ],
                // 这个是外边框晕染的范围
                "circle-stroke-width": [
                    "step",
                    ["get", "point_count"],
                    5, //蓝色晕染长度，当点数小于100时为5px晕染
                    100, //对应上面circle-color 数字，意思为100以内
                    6, //点计数在100到750之间时为黄色，6px晕染
                    750, //对应上面circle-color 数字，意思为750以内
                    7 //点计数大于或等于750时为7px像素的粉红色晕染
                ]
            }
        });
        //聚合图圆圈中的数字
        this.map.addLayer({
            id: "cluster-count",
            type: "symbol",
            source: "earthquakes",
            filter: ["has", "point_count"],
            layout: {
                "text-field": "{point_count_abbreviated}",
                "text-font": ["Open Sans Bold,Arial Unicode MS Bold"],
                "text-size": 12
            },
            // 添加这个就可以改变圆圈内字样式，这里我改变了他的颜色
            paint: {
                "text-color": "#fff",
                "text-opacity": 1
            }
        });

        // 聚合图中没有数字的显示小圆点
        this.map.addLayer({
            id: "unclustered-point",
            type: "circle",
            source: "earthquakes",
            filter: ["!has", "point_count"],
            paint: {
                "circle-color": "#11b4da",
                "circle-radius": 10,
                "circle-stroke-width": 1,
                "circle-stroke-color": "#fff"
            }
        });
        this.map.addLayer({
            id: "cluster-count2",
            type: "symbol",
            source: "earthquakes",
            filter: ["!has", "point_count"],
            layout: {
                "text-field": "{name}",
                "text-font": ["Open Sans Bold,Arial Unicode MS Bold"],
                "text-size": 12
            },
            // 添加这个就可以改变圆圈内字样式，这里我改变了他的颜色
            paint: {
                "text-color": "#fff",
                "text-opacity": 1
            }
        });

        // 单击时检查群集
        this.map.on("click", "unclustered-point", (e) => {
            let obj = this.map.queryRenderedFeatures(e.point, {
                layers: ["unclustered-point"]
            })[0].properties
            if (obj && obj.coor) {
                console.log(obj, typeof (obj.coor))
                fetch(`https://http.allmai.net:7010/jhVideo/videoPoi/getPoints?id=${obj.coor}`, {
                    method: 'GET',
                    mode: 'cors',
                    headers: new Headers({
                        // 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryrGKCBY7qhFd3TrwA',
                        'Access-Token': window.token
                    })
                }).then(resp => resp.json()).then(res => { _this.secrchPoint(res) }).catch(res => console.log(res))
                // let form = new FormData();
                // form.append('distance',800)
                // form.append('lngLats',coor)
                // form.append('lngLats','119.69963059497778,29.22793384174889;119.69963059497778,29.22793384174889')
                // form.append('lngLats','119.64600294828413,29.10509534934764;119.64600294828413,29.10509534934764')
                // fetch('http://*************:8180/data_use/road/videos', {
                //     method: 'POST',
                //     body: form,
                //     mode: 'cors',
                //     headers: new Headers({
                //         // 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryrGKCBY7qhFd3TrwA',
                //         'Access-Token': window.token,
                //         // 'Content-Type': 'application/x-www-form-urlencoded',
                //     })
                // }).then(res => 
                //     res.json()
                // ).then(res => {
                //     _this.secrchPoint(res)
                // }).catch(res => console.log(res))
            }
        });
    }
    /**

     * @description: 绘制方法内置点位飞行

     * @param {*}

     * @return {*}

     */

    drawFlyFn(coor, zoom) {

        this.map.flyTo({

            center: coor,

            zoom: zoom,

            pitch: 0,

            bearing: 0

        });

    }
    /**
     * @description: 绘制线段
     * @param {*}
     * @return {*}
     */
    drawLineSegments(lineObj) {
        if (this.map.getLayer('drawLineSegments')) {
            this.map.removeLayer('drawLineSegments')
            this.map.removeSource('drawLineSegments')
        }
        const lineArray = [];
        const length = lineObj.coords.length / 3;
        const color = lineObj.color ? `rgba(${lineObj.color[0]},${lineObj.color[1]},${lineObj.color[2]},${lineObj.color[3]})` : '#fff'
        const width = lineObj.width ? lineObj.width : 2
        for (let i = 1; i <= length; i++) {
            const index = i * 3;
            lineArray.push([lineObj.coords[index - 3], lineObj.coords[index - 2]])
        }
        const geojson = turf.lineString(lineArray)
        this.map.addLayer({
            "id": "drawLineSegments",
            "type": "line",
            source: {
                "type": "geojson",
                data: geojson,
            },
            "layout": {
                "line-join": "round",
                "line-cap": "round"
            },
            "paint": {
                "line-color": color,
                "line-width": width
            },
            "metaData": {
                zIndex: 999
            }
        });
        let flyPoint = geojson.geometry.coordinates[0]
        if (!this.heatMap) {
            this.drawFlyFn(flyPoint, 16)
        }

        //const bbox = this.egs.turf.bbox({
        //  "type": "geojson",
        //data: geojson,
        //});
        //this.map.fitBounds(bbox, {
        //padding: { top: 40, bottom: 40, left: 20, right: 20 },
        //});
    }
    /**
     * @description: 清除线段
     * @param {*}
     * @return {*}
     */
    removeLineSegments() {
        if (this.map.getLayer('drawLineSegments')) {
            this.map.removeLayer('drawLineSegments')
            this.map.removeSource('drawLineSegments')
        }
        if (this.map.getLayer('rangingLineLayer')) {
            this.map.removeLayer('rangingLineLayer');
            this.map.removeSource('rangingLineSource');
            this.map.removeLayer('rangingPointLayer');
            this.map.removeSource('rangingPointSource');
            this.map.removeImage('linePointIcon')
        }
    }
    /**
     * @description: 绘制圆
     * @param {*}
     * @return {*}
     */
    drawCircle(circleObj) {
        const length = circleObj.length;
        for (let i = 0; i < length; i++) {
            const circleArray = [circleObj[i].coords[0], circleObj[i].coords[1]];
            const geojson = turf.circle(circleArray, circleObj[i].r / 1000, { steps: 100, units: 'kilometers' })
            const color = `rgba(${circleObj[i].color[0]},${circleObj[i].color[1]},${circleObj[i].color[2]},${circleObj[i].color[3]})`
            if (i === 0) {
                let flyCenter = geojson.geometry.coordinates[0][0]

                this.drawFlyFn(flyCenter, 14)
            }
            this.map.addLayer({
                "id": circleObj[i].name,
                "type": "fill",
                source: {
                    "type": "geojson",
                    data: geojson,
                },
                'paint': {
                    'fill-color': color,
                }
            })
            this.circleLayerId.push(circleObj[i].name);
        }

        // const bbox = this.egs.turf.bbox({
        //     "type": "geojson",
        //     data: geojson,
        // });
        // this.map.fitBounds(bbox, {
        //   padding: { top: 40, bottom: 40, left: 20, right: 20 },
        // });
    }
    /**
     * @description: 清除圆
     * @param {*}
     * @return {*}
     */
    removeCircle(circleName) {
        if (this.map.getLayer(circleName)) {
            this.map.removeLayer(circleName);
            this.map.removerSource(circleName)
            this.circleLayerId = this.circleLayerId.filter(item => item != circleName);
        }
    }
    /**
     * @description: 绘制多边形
     * @param {*}
     * @return {*}
     */
    drawPolygon(polygonObj) {
        const length = polygonObj.coords.length / 2;
        const PolygonArray = [];
        const color = `rgba(${polygonObj.color[0]},${polygonObj.color[1]},${polygonObj.color[2]},${polygonObj.color[3]})`
        for (let i = 1; i <= length; i++) {
            const index = i * 2
            PolygonArray.push([polygonObj.coords[index - 2], polygonObj.coords[index - 1]])
        }
        const geojson = turf.polygon([PolygonArray]);
        this.map.addLayer({
            "id": polygonObj.name,
            "type": "fill",
            source: {
                "type": "geojson",
                "data": geojson,
            },
            'paint': {
                'fill-color': color,
            }
        })
        this.polygonLayerId.push(polygonObj.name)
        const bbox = this.egs.turf.bbox({
            "type": "geojson",
            data: geojson,
        });
        this.map.fitBounds(bbox, {
            padding: { top: 40, bottom: 40, left: 20, right: 20 },
        });
    }
    /**
     * @description: 清除多边形
     * @param {*}
     * @return {*}
     */
    clearPolygon(polygonName) {
        if (this.map.getLayer(polygonName)) {
            this.map.removeLayer(polygonName);
            this.map.removerSource(polygonName)
            this.polygonLayerId = this.polygonLayerId.filter(item => item != polygonName);
        }
    }
    /**
     * @description: 两点测距
     * @param {*}
     * @return {*}
     */
    ranging() {
        let fristClick = true; // 第一次点击
        const clickFeatures = [];
        const linePostion = [];
        // 判断是否已经加入图标
        if (!this.map.hasImage('linePointIcon')) {
            this.map.loadImage('./image/linePointIcon.png', (error, image) => {
                if (error) { throw error; }
                this.map.addImage('linePointIcon', image);
            })
        }
        this.map.addSource('rangingPointSource', {
            'type': 'geojson',
            'data': {
                'type': 'FeatureCollection',
                'features': []
            }
        })
        this.map.addSource('rangingLineSource', {
            'type': 'geojson',
            'data': {
                'type': 'FeatureCollection',
                'features': {}
            }
        })
        this.map.addLayer({
            'id': 'rangingLineLayer',
            'type': 'line',
            'source': 'rangingLineSource',
            "layout": {
                "line-join": "round",
                "line-cap": "round"
            },
            "paint": {
                "line-color": "#fff",
                "line-width": 5
            }
        })
        this.map.addLayer({
            'id': 'rangingPointLayer',
            'type': 'symbol',
            'source': 'rangingPointSource',
            'layout': {
                'icon-image': 'linePointIcon', // reference the image
                'icon-size': .5,
                'icon-offset': [0, -90]
            }
        })
        const mapClick = (e) => {
            clickFeatures.push(turf.point([e.lngLat.lng, e.lngLat.lat]))
            this.map.getSource('rangingPointSource').setData({
                "type": "FeatureCollection",
                "features": clickFeatures
            });
            if (fristClick) {
                fristClick = false;
                linePostion.push([e.lngLat.lng, e.lngLat.lat])
            } else {
                this.map.off('click', mapClick);
                this.map.off('mousemove', mapMousemove);
                window.parent.postMessage({
                    "type": "line",
                    "linenData": linePostion,
                    "getDistance": turf.distance(linePostion[0], linePostion[1], { units: 'kilometers' }) * 1000,
		     	"center": turf.center(turf.points(linePostion))

                }, '*');
            }
        }
        const mapMousemove = (e) => {
            if (!fristClick) {
                if (linePostion.length > 1) {
                    linePostion.pop();
                }
                linePostion.push([e.lngLat.lng, e.lngLat.lat]);
                const lineFeatures = turf.lineString(linePostion);
                this.map.getSource('rangingLineSource').setData(lineFeatures);
            }
        }
        this.map.on('click', mapClick);
        this.map.on('mousemove', mapMousemove);
    }
    /**
     * @description: 测面
     * @param {*}
     * @return {*}
     */
    measuringSurface(data) {
        const clickFeatures = [];
        const noodlesPostion = [[]]
        const color = data.color ? `rgba(${data.color[0]},${data.color[1]},${data.color[2]},${data.color[3]})` : '#088';
        // 判断是否已经加入图标
        if (!this.map.hasImage('noodlesPointIcon')) {
            this.map.loadImage('./image/linePointIcon.png', (error, image) => {
                if (error) { throw error; }
                this.map.addImage('noodlesPointIcon', image);
            })
        }
        // 点source
        this.map.addSource('measuringSurfacePointSource', {
            'type': 'geojson',
            'data': {
                'type': 'FeatureCollection',
                'features': []
            }
        })
        // 面source
        this.map.addSource('measuringSurfaceSource', {
            'type': 'geojson',
            'data': {
                'type': 'FeatureCollection',
                'features': {}
            }
        })
        // 线source
        this.map.addSource('measuringSurfaceLineSource', {
            'type': 'geojson',
            'data': {
                'type': 'FeatureCollection',
                'features': {}
            }
        })
        // 面layer
        this.map.addLayer({
            'id': 'measuringSurfaceLayer',
            'type': 'fill',
            'source': 'measuringSurfaceSource',
            'paint': {
                'fill-color': color,
                'fill-opacity': 0.8
            }
        })
        // 点layer
        this.map.addLayer({
            'id': 'measuringSurfacePointLayer',
            'type': 'symbol',
            'source': 'measuringSurfacePointSource',
            'layout': {
                'icon-image': 'noodlesPointIcon', // reference the image
                'icon-size': .5,
                'icon-offset': [0, -90]
            }
        })
        // 线layer
        this.map.addLayer({
            'id': 'measuringSurfaceLineLayer',
            'type': 'line',
            'source': 'measuringSurfaceLineSource',
            "layout": {
                "line-join": "round",
                "line-cap": "round"
            },
            "paint": {
                "line-color": color,
                "line-width": 5
            }
        })
        const mapClick = (e) => {
            clickFeatures.push(turf.point([e.lngLat.lng, e.lngLat.lat]))
            this.map.getSource('measuringSurfacePointSource').setData({
                "type": "FeatureCollection",
                "features": clickFeatures
            });
            noodlesPostion[0].push([e.lngLat.lng, e.lngLat.lat]);
        }
        const mapMousemove = (e) => {
            const clickFeaturesLength = clickFeatures.length;
            switch (clickFeaturesLength) {
                case 0:
                    break;
                case 1:
                    const lineFeatures = turf.lineString([noodlesPostion[0][0], [e.lngLat.lng, e.lngLat.lat]])
                    this.map.getSource('measuringSurfaceLineSource').setData(lineFeatures);
                    break;
                default:
                    if (this.map.getLayer('measuringSurfaceLineLayer')) {
                        this.map.removeLayer('measuringSurfaceLineLayer')
                        this.map.removeSource('measuringSurfaceLineSource')
                    }
                    noodlesPostion[0].push([e.lngLat.lng, e.lngLat.lat]);
                    noodlesPostion[0].push(noodlesPostion[0][0]);
                    const noodlesFeatures = turf.polygon(noodlesPostion);
                    this.map.getSource('measuringSurfaceSource').setData(noodlesFeatures);
                    noodlesPostion[0].pop();
                    noodlesPostion[0].pop();
                    break;
            }
        }
        const mapContextmenu = (e) => {
            if (noodlesPostion[0].length > 2) {
                noodlesPostion[0].push(noodlesPostion[0][0]);
                const noodlesFeatures = turf.polygon(noodlesPostion);
                this.map.getSource('measuringSurfaceSource').setData(noodlesFeatures);
                this.map.off('click', mapClick);
                this.map.off('mousemove', mapMousemove);
                this.map.off('ontextmenu', mapContextmenu);
                const polygonFuteace = turf.polygon(noodlesPostion)
                if (data.getSectionDatas) {
                    this.geojson = { "type": "FeatureCollection", "features": [{ "type": "Feature", "properties": { "NAME": "自定义" }, "geometry": { "type": "Polygon", "coordinates": polygonFuteace.geometry } }] }

                    this.getDatas({ geojson: this.geojson })
                } else {
                    window.parent.postMessage({
                        "type": "Polygon",
                        "linenData": noodlesPostion,
                        "getDistance": turf.area(polygonFuteace),
                        "center": turf.center(polygonFuteace)

                    }, '*');
                }
            }
        }
        this.map.on('click', mapClick);
        this.map.on('mousemove', mapMousemove);
        this.map.on('contextmenu', mapContextmenu);
    }

    /**
     * @description: 重置多边形
     * @param {*}
     * @return {*}
     */
    removeMeasuringSurface() {
        if (this.map.getLayer('measuringSurfaceLayer')) {
            this.map.removeLayer('measuringSurfaceLayer');
            this.map.removeSource('measuringSurfaceSource');
            this.map.removeLayer('measuringSurfacePointLayer');
            this.map.removeSource('measuringSurfacePointSource');
            this.map.removeImage('noodlesPointIcon')
        }
    }
    /**
     * @description: 飞线
     * @param {*}
     * @return {*}
     */
    flyingLine(data) {
        // 创建贝塞尔曲线
        const bezierArray = [];
        const length = data.flyData.length
        for (let i = 0; i < length; i++) {
            console.log()
            const sPoint = data.flyData[i].sPoint.split(",");
            const ePoint = data.flyData[i].ePoint.split(",");
            const line = turf.lineString([sPoint, ePoint])
            bezierArray.push(turf.bezierSpline(line));
        }
        // 创建分割线
        const arcs = []
        const step = data.bezierRate

        const split = (lines) => {
            for (let line of lines.features) {
                const arc = []
                const bezier = turf.bezierSpline(line)
                const len = turf.length(bezier)

                for (let i = 0; i < len; i += len / step) {
                    const p = turf.along(bezier, i)
                    arc.push(p.geometry.coordinates)
                }

                arcs.push(arc)
            }
        }
        split({
            'type': 'FeatureCollection',
            'features': bezierArray
        });

        this.map.addSource('segment', {
            type: 'geojson',
            lineMetrics: true,
            data: null
        })

        this.map.addLayer({
            id: 'segment',
            source: 'segment',
            type: 'line',
            paint: {
                'line-color': 'red',
                'line-width': 2,
                'line-gradient': [
                    'interpolate',
                    ['linear'],
                    ['line-progress'],
                    0,
                    'rgba(255, 255, 255, 0.1)',
                    0.8,
                    'rgba(255, 0, 0, 0.6)',
                    1,
                    'red'
                ]
            }
        })

        const source = map.getSource('segment')

        const segments = {
            type: 'FeatureCollection',
            features: []
        }

        let counter = 0
        const animate = () => {
            if (counter === step) {
                counter = 0
                segments.features.forEach(f => f.geometry.coordinates.length = 0)
            }

            arcs.forEach((arc, i) => {
                if (!segments.features[i]) {
                    segments.features[i] = {
                        type: 'Feature',
                        geometry: {
                            type: 'LineString',
                            coordinates: []
                        }
                    }
                }

                segments.features[i].geometry.coordinates.push(arc[counter])
            })

            source.setData(segments)
            requestAnimationFrame(animate)
            counter++
        }
        animate()
    }
    /************************************************* 无人机漫游 *******************************************/
    /**
     * @description: 无人机飞行
     * @return {*}
     */
    UAVRoam(data) {
        document.getElementById("roam-map-box").style.display = 'block';
        if (!this.roamMap) {
            const roam = document.getElementById("roamMap");
            const { Map, eli } = egs;
            this.roamMap = new Map({
                el: roam,
                init: {
                    scale: { maxWidth: 80, minWidth: 80, unit: "metric" },
                    style: mapConfig.style,
                    bearing: mapConfig.bearing,
                    pitch: mapConfig.pitch,
                    // bounds:[[114.35926671923897,22.577818133197027],[113.83498071428215,22.154139533657045]],
                    center: mapConfig.center,
                    zoom: mapConfig.zoom,
                }
            });


            // 创建点位置图层
        }
        // 添加图片
        if (!this.roamMap.hasImage('airplane')) {
            this.roamMap.loadImage('./image/airplane.png', (error, image) => {
                if (error) { throw error; }
                this.roamMap.addImage('airplane', image);
                // 出创建图层
                // 添加服务
                let layerConfig = {
                    id: mapConfig.bastLayers[0].id,
                    type: 'raster',
                    source: {
                        type: 'raster',
                        tiles: [mapConfig.bastLayers[0].url],  // 4326
                        tileSize: 256,
                        //zoomOffset: -1,
                        //scheme: "xyz"
                    },
                    layout: {
                        'visibility': 'visible'
                    }
                }
                this.roamMap.addLayer(layerConfig);
                this.creatRoamMapLayer(data);
            })

        } else {
            this.creatRoamMapLayer(data);
        }


        //window.cancelAnimationFrame(this.track.timer)
        //const pathDistance = turf.length(data.line);
        // 创建小地图
        // 创建道路线图标


    }
    creatRoamMapLayer(data) {
        let geojsonData = { "type": "FeatureCollection", "features": [] }
        let feature = turf.lineString(data.line);
        geojsonData.features.push(feature);

        if (!this.animation) {
            this.animation = new AnimationRoute(this.roamMap, geojsonData)
        } else {
            this.animation.init();
        }
    }
    /**
     * @description: 开始漫游
     * @return {*}
     */
    startRoam() {
        this.animation.continueRoam();
    }
    /**
     * @description: 暂停漫游
     * @return {*}
     */
    stopRoam() {
        this.animation.stopRoam();
    }
    /**
     * @description: 继续漫游
     * @return {*}
     */
    continueRoam() {
        this.animation.continueRoam();
    }
    /**
     * @description: 重置漫游
     * @return {*}
     */
    resetRoam() {
        this.animation.resetRoam();
    }
    /**
     * @description: 退出漫游
     * @return {*}
     */
    escRoam() {
        this.animation.destory();
        document.getElementById("roam-map-box").style.display = 'none';
    }
    // 测量面
    measureAnalysisPolygon(data) {
        const clickFeatures = [];
        const noodlesPostion = [[]]
        const _this = this;
        // 判断是否已经加入图标
        if (!this.map.hasImage('noodlesPointIcon')) {
            this.map.loadImage('./image/linePointIcon.png', (error, image) => {
                if (error) { throw error; }
                this.map.addImage('noodlesPointIcon', image);
            })
        }
        // 点sourceWW
        this.map.addSource('measuringSurfacePointSource', {
            'type': 'geojson',
            'data': {
                'type': 'FeatureCollection',
                'features': []
            }
        })
        // 面source
        this.map.addSource('measuringSurfaceSource', {
            'type': 'geojson',
            'data': {
                'type': 'FeatureCollection',
                'features': {}
            }
        })
        // 线source
        this.map.addSource('measuringSurfaceLineSource', {
            'type': 'geojson',
            'data': {
                'type': 'FeatureCollection',
                'features': {}
            }
        })
        // 面layer
        this.map.addLayer({
            'id': 'measuringSurfaceLayer',
            'type': 'fill',
            'source': 'measuringSurfaceSource',
            'paint': {
                'fill-color': '#088',
                'fill-opacity': 0.8
            }
        })
        // 点layer
        this.map.addLayer({
            'id': 'measuringSurfacePointLayer',
            'type': 'symbol',
            'source': 'measuringSurfacePointSource',
            'layout': {
                'icon-image': 'noodlesPointIcon', // reference the image
                'icon-size': .5,
                'icon-offset': [0, -90]
            }
        })
        // 线layer
        this.map.addLayer({
            'id': 'measuringSurfaceLineLayer',
            'type': 'line',
            'source': 'measuringSurfaceLineSource',
            "layout": {
                "line-join": "round",
                "line-cap": "round"
            },
            "paint": {
                "line-color": "#088",
                "line-width": 5
            }
        })
        const mapClick = (e) => {
            clickFeatures.push(turf.point([e.lngLat.lng, e.lngLat.lat]))
            // this.map.getSource('measuringSurfacePointSource').setData({
            //     "type": "FeatureCollection",
            //     "features": clickFeatures
            // });
            noodlesPostion[0].push([e.lngLat.lng, e.lngLat.lat]);
        }
        const mapMousemove = (e) => {
            const clickFeaturesLength = clickFeatures.length;
            switch (clickFeaturesLength) {
                case 0:
                    break;
                case 1:
                    const lineFeatures = turf.lineString([noodlesPostion[0][0], [e.lngLat.lng, e.lngLat.lat]])
                    this.map.getSource('measuringSurfaceLineSource').setData(lineFeatures);
                    break;
                default:
                    if (this.map.getLayer('measuringSurfaceLineLayer')) {
                        this.map.removeLayer('measuringSurfaceLineLayer')
                        this.map.removeSource('measuringSurfaceLineSource')
                    }
                    noodlesPostion[0].push([e.lngLat.lng, e.lngLat.lat]);
                    noodlesPostion[0].push(noodlesPostion[0][0]);
                    const noodlesFeatures = turf.polygon(noodlesPostion);
                    this.map.getSource('measuringSurfaceSource').setData(noodlesFeatures);
                    noodlesPostion[0].pop();
                    noodlesPostion[0].pop();
                    break;
            }
        }
        const mapContextmenu = (e) => {
            if (noodlesPostion[0].length > 2) {
                noodlesPostion[0].push(noodlesPostion[0][0]);
                const noodlesFeatures = turf.polygon(noodlesPostion);
                this.map.getSource('measuringSurfaceSource').setData(noodlesFeatures);
                this.map.off('click', mapClick);
                this.map.off('mousemove', mapMousemove);
                this.map.off('ontextmenu', mapContextmenu);
                const polygonFuteace = turf.polygon(noodlesPostion);
                // let polygonAeare=turf.area(polygonFuteace);
                _this.geojson = { "type": "FeatureCollection", "features": [{ "type": "Feature", "properties": { "NAME": "自定义" }, "geometry": { "type": "Polygon", "coordinates": polygonFuteace.geometry } }] }

                _this.getDatas({ geojson: _this.geojson })
                // window.parent.postMessage({
                //     "type": "Polygon",
                //     "linenData": noodlesPostion,
                //     "getDistance": turf.area(polygonFuteace)
                // },'*');
            }
        }
        this.map.on('click', mapClick);
        this.map.on('mousemove', mapMousemove);
        this.map.on('contextmenu', mapContextmenu);

    }
    // 查询多边形区域数据
    getDatas(pagram) {
        var formData = new FormData();
        formData.append('geojson', JSON.stringify(pagram.geojson) || '');
        formData.append('groupColumns', pagram.type || 'esType1');
        formData.append('keyword', pagram.keyword || '');
        // this.geojson=JSON.stringify(pagram.geojson);//保存画面面数据
        formData.append('bufferDistance', pagram.bufferDistance || 0);
        fetch('http://*************:10126/spacesearch/esData/listGroup', {
            method: 'POST',
            body: formData,
            mode: 'cors',
            headers: new Headers({
                // 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryrGKCBY7qhFd3TrwA',
                // 'Access-Token': window.token
            }
            )
        }).then(res => res.json()).catch(res => console.log(res)).then(res => {
            console.log('结果', res)
            window.parent.postMessage({
                type: 'getDatas',
                data: res.data
            }, "*");
        })
    }
    // 获取兴趣点类型
    getDataByType(pagram = { type: '其他兴趣点', keyword: '', pageIndex: 1, pageSize: 20, bufferDistance: 0 }) {
        var formData = new FormData();
        formData.append('groupColumns', 'esType1');
        formData.append('esType1', pagram.type);
        formData.append('pageIndex', pagram.pageIndex || 1);
        formData.append('pageSize', pagram.pageSize || 20);
        formData.append('keyword', pagram.keyword || '');
        let geojson = this.geojson;

        formData.append('geojson', geojson ? JSON.stringify(geojson) : '');
        formData.append('bufferDistance', pagram.bufferDistance || 0);
        fetch('http://*************:10126/spacesearch/esData/geojson/list', {
            method: 'POST',
            body: formData,
            mode: 'cors',
            headers: new Headers({
                // 'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryrGKCBY7qhFd3TrwA',
                // 'Access-Token': window.token
            })
        }).then(res => res.json()).catch(res => console.log(res)).then(res => {
            window.parent.postMessage({
                type: 'getDataByType',
                data: res.data
            }, "*");
        })

    }
    /**
    * @description: postMessage 轨迹描绘
    * @param {*} data {funcName: '消息类型' ...}
    */

    createTrajectory(data) {
        this.map[data.id] = this.map.createTrajectory(data)
        // setTimeout(() => {
        //     map.moveLayer(`${data.id}_route`, `${data.id}_point`);
        // }, 1000)
    }
    /**
   * @description: postMessage 返回点击事件的event并且执行回调函数
   * @param {*} data {funcName: '消息类型' ...}
   */
    startReturnClickEvent() {
        this.map.on("click", (event) => {
            // data.callback(event)
            window.parent.postMessage({
                type: 'returnPoint',
                data: event.lngLat
            }, "*");
        })
    }
    /**
    * @description: postMessage 返回点击事件的event并且执行回调函数
    * @param {*} data {funcName: '消息类型' ...}
    */
    stopReturnClickEvent() {
        this.map.off("click", (event) => {
            // data.callback(event)
            window.parent.postMessage({
                type: 'returnPoint',
                data: event.lngLat
            }, "*");
        })
    }
    /**
     * @description: postMessage 消息分类
     * @param {*} data {funcName: '消息类型' ...}
     */
    classiFication(data) {
        console.log(data.funcName)
        // postMessage 消息安全性检测
        // this.checkMessage()
        switch (data.funcName) {
            // 图层切换
            case "layer":
                this.toggleBastLayer(data);
                break;
            // 3D文字添加    
            case "3Dtext":
                this.text3D(data);
                break;
            case "text3DById":
                this.text3DById(data)
                break;
            // 根据文字删除id
            case "rm3DtextById":
                this.rm3DTextById(data.id)
                break;
            // 3D文字删除
            case "rm3Dtext":
                console.log(data)
                this.rm3DText();
                break;
            // 线缓冲查询
            case "RoadVideo":
                this.getRoadVideo(data.distance, data.lngLats);
                break;
            case "rmVideo":
                this.removeRoadVideo();
                break;
            // 点位添加
            case "pointLoad":
                this.addPointLayer(data);
                break;
            // 弹窗移除
            case "rmPop":
                this.removePopup(data);
                break;
            // 删除点位
            case "rmPoint":
                this.removePointLayer(data);
                break;
            // 飞行定位
            case "flyto":
                //this.flyToPoint(data); 
                this.map.flyTo(data.flyData)
                break;
            // 添加热力图
            case "hotPowerMap":
                this.getHeatMap(data.hotPowerMapData, data.threshold)
                break;
            // 添加热力图
            case "hotPowerMap1":
                this.getHeatMap(data.hotPowerMapData, 0, false)
                break;
            // 删除热力图
            case "rmhotPowerMap":
                this.removeHeatMap()
                break;
            // 加载柱状图
            case "Histogram":
                console.log(123)
                this.getHistogram(data.HistogramData)
                break;
            // 移除柱状图
            case "rmHistogram":
                this.removeHistogram()
                break;
            // 加载版块高亮
            case "renderBankuai":
                this.getSectionHighlight(data.renderBankuaiData)
                break;
            // 移除版块高亮
            case "rmRedbankuai":
                this.removeSectionHighlight()
                break;
            // 柱状热力图
            case "HistogramMap":
                this.getColumnarThermodynamicDiagram(data.HistogramMapName)
                break;
            // 移除柱状热力图
            case "rmHistogramMap":
                this.removeColumnarThermodynamicDiagram()
                break;
            // 绘制线段
            case "createLine":
                this.drawLineSegments(data)
                break;
            // 绘制圆
            case "drawCircle":
                this.drawCircle(data.circleData)
                break;
            // 清除圆
            case "clearCircle":
                this.removeCircle(data.circleName)
                break;
            // 绘制多边形
            case "shape":
                this.drawPolygon(data)
                break;
            // 清除多边形
            case "rmShape":
                this.clearPolygon(data.shapeName)
                break;
            // 两点测距
            case "getDistance":
                this.ranging()
                break;
            // 测面
            case "startDrawPolygon":
                this.measuringSurface(data)
                break;
            // 重置多边形
            case "rmDrawPolygon":
                this.removeMeasuringSurface()
                break;
            // 飞线
            case "newFlyLine":
                this.flyingLine(data)
                break;
            case "rmAll":
                this.removeAll()
                break;
            case "textData":
                //this.flyToPoint(data);
                break;
            case 'activeLine':
                this.drawLineSegments(data)
                break;
            case "rmLine":
                this.removeLineSegments();
                break;
            //聚合点位添加
            case 'loadJuhePoint':
                this.addPointLayer(data);
                break;
            //聚合点位删除
            case 'rmJuhePoint':
                this.removePointLayer(data)
                break;
            // 无人机漫游
            case 'UAVRoam':
                this.UAVRoam(data);
                break;
            // 开始漫游
            case 'startRoam':
                this.startRoam();
                break;
            // 暂停漫游
            case 'stopRoam':
                this.stopRoam();
                break;
            // 继续漫游
            case 'continueRoam':
                this.continueRoam();
                break;
            // 重置漫游
            case 'resetRoam':
                this.resetRoam();
                break;
            // 退出漫游
            case 'escRoam':
                this.escRoam();
                break;
            //批量创建弹窗
            case 'popup':
                let datas = []
                if (data.type == 'jjtj') {
                    // 如果弹窗是经济调节，重新组装数据
                    data.textData.forEach(item => {
                        let list = item.text.split('\n')
                        let list2 = []
                        list.forEach(element => {
                            let num = element.split(':')
                            if (num[1].includes(',')) {
                                let arr = num[1].split(',')
                                let num2 = []
                                num2.push(num[0], arr[0], arr[1])
                                num = num2
                            }
                            list2.push(num)
                        })
                        let obj = {
                            key: [],
                            value: [],
                            name: item.name,
                            pos: item.pos
                        }
                        list2.forEach(ele => {
                            obj.key.push(ele[0])
                            let str = ''
                            if (ele[2]) {
                                str = ele[1] + ele[2]
                            } else {
                                str = ele[1]
                            }
                            obj.value.push(str)
                        })
                        datas.push(obj)
                    })
                } else {
                    datas = data.textData
                }
                datas.forEach(item => {
                    item.type = data.type
                    let featureData = {
                        key: item.key,
                        title: item.name,
                        value: item.value
                    }
                    let coordinates = [Number(item.pos[0]), Number(item.pos[1])]
                    this.createPopup2(item, featureData, coordinates)
                })
                this.drawFlyFn([120.12052969238281, 29.154541015625], 10)
                break;
            // 切换场景
            case 'tiltPhotography':
                window.Work.change3D(Number(data.tiltPhotographySwitch))
                break;
            // 空间范围分析
            case "measureAnalysisPolygon":
                this.measureAnalysisPolygon(data)
                break;
            case "getDataByType":
                this.getDataByType(data)
                break;
            // 添加点击事件
            case 'click':
                // this.mapClick()
                break;
            // 自定义弹框
            case 'customPop':
                this.customPoup(data);
            case 'addMap':
                this.addMap(data)
                break;
            case "rmAddMap":
                this.removeMap(data.id)
                break;
            case 'setZoom':
                switch (data.inOrOut) {
                    case 'in':
                        // 放大地图
                        this.map.zoomIn();
                        break;
                    case 'out':
                        // 缩小地图
                        this.map.zoomOut();
                        break;
                }
                break;
            case 'change2DOr3D':
                if (data.is2D) {
                    this.map.setView('2D')
                } else {
                    this.map.setView('3D')
                }
                break;
            case 'backToTheCenterPoint':
                this.map.flyTo({
                    center: [119.64748254784801, 29.078969905698866],
                    zoom: 14,
                    bearing: -0,
                    pitch: 0
                })
                break;
            case 'addTipsPoint':
                this.addTipsPoint(data.data)
                break;
            case 'createTrajectory':
                return this.createTrajectory(data.data)
                break;
            case 'stopReturnClickEvent':
                this.stopReturnClickEvent()
                break;
            case 'startReturnClickEvent':
                this.startReturnClickEvent()
                break;
            default:
                break;
        }
    }
}