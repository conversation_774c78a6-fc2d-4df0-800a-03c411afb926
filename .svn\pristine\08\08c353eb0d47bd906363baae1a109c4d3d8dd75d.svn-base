<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>高发案件-左</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/elementui/js/index.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/js/jslib/Emiter.js"></script>

    <!-- <script src="/static/js/jslib/vue-count-to.min.js"></script> -->

    <!-- <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script> -->
    <!-- <link rel="stylesheet" href="/static/css/animate_dn.css" /> -->
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>

    <link rel="stylesheet" href="/static/citybrain/shgl/css/gfsj-left.css" />
    <style>
      .act {
        color: #5087ec;
      }

      .nameValue {
        font-size: 30px;
        color: #fff;
        display: flex;
        justify-content: space-around;
        position: absolute;
        width: 100%;
        cursor: pointer;
        z-index: 9;
      }
    </style>
    <!-- <script src="/static/citybrain/hjbh/js/date.js"></script> -->
  </head>

  <body>
    <div id="gfsj-left" v-cloak>
      <div class="gfsj-left_box">
        <!-- 党建统领 -->
        <div class="box box1">
          <div class="box-title">
            <s-header-title
              title="高发案件区域分布"
              htype="1"
              data-time="2022年9月20日"
            ></s-header-title>
          </div>
          <div class="box-con box1-content">
            <div class="box1-content-item">
              <div class="gfsj001" style="width: 400px; height: 350px">
                <div class="gfsjValue s-c-yellow-gradient">
                  {{gfsj.value}}
                  <span class="unit">{{gfsj.unit}}</span>
                </div>
                <div style="margin-top: 70px; font-size: 34px">
                  {{gfsj.name}}
                </div>
              </div>
            </div>
            <div class="box1-content-item">
              <div id="echarts01" style="width: 1500px; height: 450px"></div>
            </div>
          </div>
        </div>
        <!--数字政府  -->
        <div class="box box2">
          <div class="box-title">
            <s-header-title
              title="高发案件关联区域分布"
              htype="2"
            ></s-header-title>
            <div class="box-title">
              <s-header-title
                title="高发案件关联类别分布"
                htype="2"
              ></s-header-title>
            </div>
          </div>
          <div class="box-con box2-content" style="position: relative">
            <div
              class="box2-content-item"
              id="echarts02"
              style="flex: 1; height: 450px"
            ></div>
            <div class="box2-content-item">
              <div
                class="box2-content-item"
                id="echarts03"
                style="width: 900px; height: 430px"
              ></div>
            </div>
          </div>
        </div>
        <!-- 数字经济 -->
        <div class="box box3">
          <div class="box-title">
            <s-header-title
              title="高发案件同比分析"
              htype="1"
              data-time="2022年9月20日"
            ></s-header-title>
          </div>
          <div class="nameValue">
            <div
              v-for="(item ,index) in name"
              :class="{act:isAct===index}"
              @click="changeY(index)"
            >
              {{item}}
            </div>
          </div>
          <div class="box-con box3-content">
            <div id="echarts04" style="width: 100%; height: 450px"></div>
          </div>
        </div>
      </div>
    </div>

    <script>
      var vm = new Vue({
        el: "#gfsj-left",
        data: {
          gfsj: {},
          isAct: 0,
          name: ["去年", "上月"],
          pointData: [
            {
              pos: [119.8314905582194, 29.623060984755377, 15000],
              name: "浦江县",
              key: ["待处理事件", "在线人员"],
              value: ["33件", "33人"],
            },
            {
              pos: [119.70440936200465, 29.10010792774123, 15000],
              name: "金义新区",
              key: ["待处理事件", "在线人员"],
              value: ["33件", "33人"],
            },
            {
              pos: [119.32419295719222, 29.219102728348943, 15000],
              name: "兰溪市",
              key: ["待处理事件", "在线人员"],
              value: ["33件", "33人"],
            },
            {
              pos: [119.44160138147026, 28.908609191923716, 15000],
              name: "婺城区",
              key: ["待处理事件", "在线人员"],
              value: ["33件", "33人"],
            },
            {
              pos: [120.02438915928201, 29.18180844822686, 15000],
              name: "义乌市",
              key: ["待处理事件", "在线人员"],
              value: ["33件", "33人"],
            },
            {
              pos: [119.67487615578625, 28.707011171587162, 15000],
              name: "武义县",
              key: ["待处理事件", "在线人员"],
              value: ["33件", "33人"],
            },
            {
              pos: [120.10886352478184, 28.887053762616034, 15000],
              name: "永康市",
              key: ["待处理事件", "在线人员"],
              value: ["33件", "33人"],
            },
            {
              pos: [120.39254935807531, 29.215565856200392, 15000],
              name: "东阳市",
              key: ["待处理事件", "在线人员"],
              value: ["33件", "33人"],
            },
            {
              pos: [120.53638710113705, 28.986431602110855, 15000],
              name: "磐安县",
              key: ["待处理事件", "在线人员"],
              value: ["33件", "33人"],
            },
          ],
        },
        computed: {},
        mounted() {
          this.initApi();
          // this.getEcharts02("echarts3");
          this.initMap();
          this.openCustomPop();
          let that = this;
          window.addEventListener("message", function (e) {
            // if (!e.data.data.data) return;
            // const item = JSON.parse(e.data.data.data);
            if (e.data.type == "bankuaiClick") {
              // that.rmPop();
              that.openIframe2();
            }
          });
        },
        methods: {
          rmPop() {
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "rmPop",
              })
            );
          },
          changeY(index) {
            this.isAct = index;
            if (this.isAct == 0) {
              $get("/shgl/gfsj004", { type: "去年" }).then((res) => {
                this.getEcharts04("echarts04", res);
              });
              // $api("shgl_gfsj004", { type: "去年" }).then((res) => {
              //   this.getEcharts04("echarts04", res);
              // });
            } else {
              $get("/shgl/gfsj0010", { type: "上月" }).then((res) => {
                this.getEcharts04("echarts04", res);
              });
              // $api("shgl_gfsj004", { type: "上月" }).then((res) => {
              //   this.getEcharts04("echarts04", res);
              // });
            }
          },
          initApi() {
            $get("/shgl/gfsj000").then((res) => {
              this.gfsj = res;
            });
            // $api("shgl_gfsj000").then((res) => {
            //   this.gfsj = res[0];
            // });
            $get("/shgl/gfsj001").then((res) => {
              this.getEcharts01("echarts01", res);
            });
            // $api("shgl_gfsj001").then((res) => {
            //   this.getEcharts01("echarts01", res);
            // });
            $get("/shgl/gfsj004", { type: "去年" }).then((res) => {
              this.getEcharts04("echarts04", res);
            });
            // $api("shgl_gfsj004", { type: "去年" }).then((res) => {
            //   this.getEcharts04("echarts04", res);
            // });
            $get("/shgl/gfsj002").then((res) => {
              this.getEcharts02("echarts02", res);
            });
            // $api("shgl_gfsj002").then((res) => {
            //   this.getEcharts02("echarts02", res);
            // });
            $get("/shgl/gfsj003").then((res) => {
              this.getEcharts03("echarts03", res);
            });
            // $api("shgl_gfsj003").then((res) => {
            //   this.getEcharts03("echarts03", res);
            // });
          },
          getEcharts01(dom, data) {
            let myEc = echarts.init(document.getElementById(dom));
            let xData = [],
              yData = [],
              y1Data = [],
              y2Data = [];

            data.forEach((item) => {
              xData.push(item.name);
              yData.push(item.las);
              y1Data.push(item.jas);
              y2Data.push(item.asjal);
            });
            var option = {
              tooltip: {
                trigger: "axis",
                axisPointer: {
                  // 坐标轴指示器，坐标轴触发有效
                  type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                },
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "28",
                },
              },
              legend: {
                orient: "horizontal",
                itemWidth: 18,
                itemHeight: 18,
                top: "8%",
                icon: "rect",
                itemGap: 45,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 30,
                },
              },
              grid: {
                left: "2%",
                right: "5%",
                bottom: "20%",
                top: "20%",
                containLabel: true,
              },
              xAxis: [
                {
                  type: "category",
                  data: xData,
                  offset: 20,
                  axisLine: {
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.3,
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  axisLabel: {
                    interval: 0,
                    // rotate: -30,
                    textStyle: {
                      fontSize: 30,
                      color: "white",
                    },
                  },
                },
              ],
              yAxis: [
                {
                  name: "单位:件",
                  type: "value",
                  // max: 800,
                  min: 0,
                  nameTextStyle: {
                    fontSize: 30,
                    color: "#D6E7F9",
                    padding: [0, 0, 20, 0],
                  },
                  splitLine: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.1,
                      width: 2,
                    },
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.5,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 30,
                      color: "#D6E7F9",
                    },
                  },
                },
                {
                  name: "单位：%",
                  type: "value",
                  //   max: 30,
                  //   min: 0,
                  nameTextStyle: {
                    fontSize: 30,
                    color: "#D6E7F9",
                    padding: [0, 0, 20, 0],
                  },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.1,
                      width: 2,
                    },
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.5,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 30,
                      color: "#D6E7F9",
                    },
                  },
                },
              ],
              series: [
                {
                  name: "立案数",
                  type: "bar",
                  barWidth: "40",
                  yAxisIndex: 0,
                  smooth: false, //加这个
                  center: ["0%", "45%"],
                  radius: ["0%", "45%"],
                  itemStyle: {
                    normal: {
                      color: "#5087ec",
                      barBorderRadius: 4,
                    },
                  },
                  data: yData,
                },
                {
                  name: "结案数",
                  type: "bar",
                  barWidth: "40",
                  yAxisIndex: 0,
                  smooth: true, //加这个
                  center: ["0%", "45%"],
                  radius: ["0%", "45%"],
                  itemStyle: {
                    normal: {
                      color: "#68bbc4",
                      barBorderRadius: 4,
                    },
                  },
                  data: y1Data,
                },
                {
                  name: "按时结案率",
                  type: "line",
                  barWidth: "15%",
                  smooth: false, //加这个
                  yAxisIndex: 1,
                  itemStyle: {
                    normal: {
                      color: "#16d46b",
                      barBorderRadius: 8,
                    },
                  },
                  data: y2Data,
                },
              ],
            };
            myEc.setOption(option);
            myEc.getZr().on("mousemove", (param) => {
              myEc.getZr().setCursorStyle("default");
            });
          },
          getEcharts04(dom, data) {
            let myEc = echarts.init(document.getElementById(dom));
            let xData = [],
              yData = [],
              y1Data = [],
              y2Data = [];

            data.forEach((item) => {
              xData.push(item.name);
              yData.push(item.las);

              y2Data.push(item.tbzzl);
            });
            var option = {
              tooltip: {
                trigger: "axis",
                axisPointer: {
                  // 坐标轴指示器，坐标轴触发有效
                  type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                },
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "28",
                },
              },
              legend: {
                orient: "horizontal",
                itemWidth: 18,
                itemHeight: 18,
                top: "8%",
                icon: "rect",
                itemGap: 45,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 30,
                },
              },
              grid: {
                left: "2%",
                right: "5%",
                bottom: "10%",
                top: "20%",
                containLabel: true,
              },
              xAxis: [
                {
                  type: "category",
                  data: xData,
                  offset: 20,
                  axisLine: {
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.3,
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  axisLabel: {
                    interval: 0,
                    // rotate: -30,
                    textStyle: {
                      fontSize: 30,
                      color: "white",
                    },
                  },
                },
              ],
              yAxis: [
                {
                  name: "单位:件",
                  type: "value",
                  // max: 800,
                  min: 0,
                  nameTextStyle: {
                    fontSize: 30,
                    color: "#D6E7F9",
                    padding: [0, 0, 20, 0],
                  },
                  splitLine: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.1,
                      width: 2,
                    },
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.5,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 30,
                      color: "#D6E7F9",
                    },
                  },
                },
                {
                  name: "单位：%",
                  type: "value",
                  //   max: 30,
                  //   min: 0,
                  nameTextStyle: {
                    fontSize: 30,
                    color: "#D6E7F9",
                    padding: [0, 0, 20, 0],
                  },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.1,
                      width: 2,
                    },
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.5,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 30,
                      color: "#D6E7F9",
                    },
                  },
                },
              ],
              series: [
                {
                  name: "立案数",
                  type: "bar",
                  barWidth: "80",
                  yAxisIndex: 0,
                  smooth: false, //加这个
                  center: ["0%", "45%"],
                  radius: ["0%", "45%"],
                  itemStyle: {
                    normal: {
                      color: "#5087ec",
                      barBorderRadius: 4,
                    },
                  },
                  data: yData,
                },

                {
                  name: "同比增长率",
                  type: "line",
                  barWidth: "15%",
                  smooth: false, //加这个
                  yAxisIndex: 1,
                  itemStyle: {
                    normal: {
                      color: "#16d46b",
                      barBorderRadius: 8,
                    },
                  },
                  data: y2Data,
                },
              ],
            };
            myEc.setOption(option);
            myEc.getZr().on("mousemove", (param) => {
              myEc.getZr().setCursorStyle("default");
            });
          },
          getEcharts02(dom, data) {
            let myEc = echarts.init(document.getElementById(dom));
            let xData = [],
              yData = [],
              y1Data = [],
              y2Data = [],
              y3Data = [];

            data.forEach((item) => {
              xData.push(item.name);
              yData.push(item.value);
              y1Data.push(item.value1);
              y2Data.push(item.value2);
              y3Data.push(item.value3);
            });
            var option = {
              tooltip: {
                trigger: "axis",
                axisPointer: {
                  // 坐标轴指示器，坐标轴触发有效
                  type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                },
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "28",
                },
              },
              legend: {
                orient: "horizontal",
                itemWidth: 18,
                itemHeight: 18,

                icon: "rect",
                itemGap: 45,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 30,
                },
              },
              grid: {
                left: "0%",
                right: "2%",
                top: "20%",
                containLabel: true,
              },
              xAxis: [
                {
                  type: "category",
                  data: xData,
                  offset: 20,
                  axisLine: {
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.3,
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  axisLabel: {
                    interval: 0,
                    // rotate: -30,
                    textStyle: {
                      fontSize: 30,
                      color: "white",
                    },
                  },
                },
              ],
              yAxis: [
                {
                  name: "单位:件",
                  type: "value",
                  // max: 800,
                  min: 0,
                  nameTextStyle: {
                    fontSize: 30,
                    color: "#D6E7F9",
                    padding: [0, 0, 20, 0],
                  },
                  splitLine: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.1,
                      width: 2,
                    },
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.5,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 30,
                      color: "#D6E7F9",
                    },
                  },
                },
                {
                  name: "单位：%",
                  type: "value",
                  //   max: 30,
                  //   min: 0,
                  nameTextStyle: {
                    fontSize: 30,
                    color: "#D6E7F9",
                    padding: [0, 0, 20, 0],
                  },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.1,
                      width: 2,
                    },
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.5,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 30,
                      color: "#D6E7F9",
                    },
                  },
                },
              ],
              series: [
                {
                  name: "区域关联数",
                  type: "bar",

                  yAxisIndex: 0,

                  data: yData,
                },
                {
                  name: "人员关联数",
                  type: "bar",

                  yAxisIndex: 0,

                  data: y1Data,
                },
                {
                  name: "号码关联数",
                  type: "bar",

                  yAxisIndex: 0,

                  data: y2Data,
                },
                {
                  name: "关联率",
                  type: "line",

                  smooth: false, //加这个
                  yAxisIndex: 1,

                  data: y3Data,
                },
              ],
            };
            myEc.setOption(option);
            tools.loopShowTooltip(myEc, option, {
              loopSeries: true,
            }); //轮播
          },

          getEcharts03(dom, dataNew) {
            let myEc = echarts.init(document.getElementById(dom));
            let imgUrl = "../img/echarts-bg.png";
            const option = {
              tooltip: {
                trigger: "item",
                formatter: "{b}: <br/> {d}%",
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "30",
                },
              },
              legend: {
                orient: "vertical",
                left: "58%",
                bottom: "20%",
                icon: "circle",
                itemGap: 62,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 26,
                  lineHeight: 30,
                },
                formatter: function (name) {
                  var data = option.series[0].data; //获取series中的data
                  var total = 0;
                  var tarValue;
                  for (var i = 0, l = data.length; i < l; i++) {
                    total += data[i].value;
                    if (data[i].name == name) {
                      tarValue = data[i].value;
                    }
                  }
                  var p = (tarValue / total) * 100;
                  //   return name + ': ' + tarValue + '景'
                  return name + "\n" + p.toFixed(1) + "%";
                },
              },
              graphic: [
                {
                  z: 4,
                  type: "image",
                  id: "logo",
                  left: "16%",
                  top: "36%",
                  z: -10,
                  bounding: "raw",
                  rotation: 0, //旋转
                  origin: [50, 50], //中心点
                  scale: [0.3, 0.3], //缩放
                  style: {
                    image: imgUrl,
                    opacity: 1,
                  },
                },
              ],
              series: [
                {
                  name: dom === "charts_lxfl" ? "类型分类" : "事项分类",
                  type: "pie",
                  radius: ["20%", "100%"],
                  center: ["25%", "55%"],
                  roseType: "area",
                  itemStyle: {
                    borderRadius: 5,
                  },
                  label: {
                    show: false,
                  },
                  data: dataNew,
                },
              ],
            };
            myEc.setOption(option);
          },
          initMap() {
            top.document.getElementById("map").contentWindow.Work.change3D(9);
            this.flyTo();
            this.add3DText();
          },

          //飞入
          flyTo() {
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "flyto", //功能名称
                flyData: {
                  center: [119.95478050597587, 29.01613226366889],
                  zoom: 10.5,
                  pitch: 40,
                  bearing: 0,
                },
              })
            );
          },
          // 加载3D文字方法
          add3DText() {
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "3Dtext", //3D文字功能
                textData: [
                  // pos文字的位置  //text 展示的文字
                  {
                    pos: [119.94315399169922, 29.5630503845215, 11000],
                    text: "浦江县",
                  },
                  {
                    pos: [119.46214447021484, 29.31345558166504, 11000],
                    text: "兰溪市",
                  },
                  {
                    pos: [119.5569204711914, 29.00677101135254, 11000],
                    text: "婺城区",
                  },
                  {
                    pos: [119.8483056640625, 29.188559951782227, 11000],
                    text: "金义新区",
                  },
                  {
                    pos: [120.08206787109375, 29.322123641967773, 11000],
                    text: "义乌市",
                  },
                  {
                    pos: [119.7269204711914, 28.79677101135254, 11000],
                    text: "武义县",
                  },
                  {
                    pos: [120.1469204711914, 28.97677101135254, 11000],
                    text: "永康市",
                  },
                  {
                    pos: [120.4169204711914, 29.24677101135254, 11000],
                    text: "东阳市",
                  },
                  {
                    pos: [120.6299204711914, 29.06677101135254, 11000],
                    text: "磐安县",
                  },
                ],
                textSize: 40,
                id: "text1",
                // zoomShow: true,
                color: [255, 255, 255, 1],
              })
            );
          },

          openCustomPop() {
            this.pointData.forEach((item) => {
              this.customPop(item);
            });
          },
          //添加自定义弹框
          customPop(item) {
            console.log("111", item.key);
            let p = [];

            item.key.forEach((el, index) => {
              p.push(
                `<p>
                        <span > ${el}:</span>
                        <span classs="s-c-yellow-gradient"> ${item.value[index]}</span>
                    </p>`
              );
            });
            console.log(p);
            let objData = {
              funcName: "customPop",
              coordinates: item.pos,
              closeButton: true,
              html: `<div class="contain" id="customPop"
                      style="
                        position: absolute;
                        height: 160px;
                        width: max-content;
                        display:inline-block;
                        background-color: rgba(0, 0, 0, 0.8);
                        border: 2px solid #00aae2;
                        box-sizing: border-box;
                        border-style: solid;
                        border-width: 4px;
                        border-image-source: linear-gradient(0deg, #32abe4 0%, #0b5aa4 100%);
                        border-image-slice: 1;">
                        <div
                            class="content"
                            style="font-size: 28px;color: #fff;padding: 20px;line-height: 55px;">
                            ${p.join("")}
                        </div>
                    </div>`,
            };
            top.document
              .getElementById("map")
              .contentWindow.Work.funChange(JSON.stringify(objData));
          },
          openIframe2() {
            let left = {
              type: "openIframe",
              name: "jtgl-doing3",
              src: baseURL.url + "/static/citybrain/shgl/commont/gfajyzt.html",
              left: "calc(50% - 1690px)",
              top: "1683px",
              width: "3380px",
              height: "400px",
              zIndex: "10",
              argument: {
                status: "",
              },
            };
            window.parent.postMessage(JSON.stringify(left), "*");
          },
        },

        destroyed() {
          top.document
            .getElementById("map")
            .contentWindow.egs1.contentWindow.map.TDT_TITLE_ID.remove();
        },
      });
    </script>
  </body>
</html>
