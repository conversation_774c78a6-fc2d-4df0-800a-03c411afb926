<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>领域6中间面板</title>
        <script src="/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <!-- 轮播toolTip -->
    </head>
    <style>
        .header-title2[data-v-4d0d1712] {
            width: 100% !important;
        }

        [v-cloak] {
            display: none;
        }

        html,
        body,
        ul,
        p {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .ly6-container {
            width: 1700px;
            height: 500px;
            background-color: #0a2443;
            padding: 30px 40px;
            box-sizing: border-box;
        }

        .content {
            width: 100%;
            height: 80%;
            display: flex;
        }

        #line-chart {
            width: 60%;
            height: 100%;
        }

        #bar-chart {
            width: 40%;
            height: 100%;
        }
        .btn1 {
            width: 100px;
            height: 45px;
            background-color: #5eb0f5;
            color: #fff;
            font-size: 28px;
            text-align: center;
            line-height: 45px;
            border-radius: 10px;
            position: absolute;
            right: 40px;
            cursor: pointer;
        }
    </style>

    <body>
        <div id="app" class="ly6-container" v-cloak>
            <div class="btn1" @click="clickBtn">详情</div>
            <nav>
                <s-header-title-2 htype="1" title="城市管理汇聚展示"></s-header-title-2>
            </nav>
            <div class="content">
                <div id="line-chart"></div>
                <div id="bar-chart"></div>
            </div>
        </div>
    </body>
    <script type="module">
        new Vue({
            el: "#app",
            data: {},
            methods: {
                clickBtn() {
                    top.commonObj.funOpenIframe({
                        name: "ly6-midle-detail-dialog",
                        src: "static/citybrain3840/shgl/pages/ly6-midle-detail-dialog.html",
                        width: "1700px",
                        height: "600px",
                        left: "1070px",
                        top: "1300px",
                    });
                },
                init() {
                    $api("ldst_shgl_ly6", { type1: 9 }).then((res) => {
                        this.LinechartsShow("line-chart", res);
                    });
                    $api("ldst_shgl_ly6", { type1: 10 }).then((res) => {
                        this.PiechartsShow(res);
                    });
                },
                //绘制折线图
                LinechartsShow(id, data) {
                    const myChartsRun = echarts.init(document.getElementById(id));
                    var fontColor = "#30eee9";
                    let option = {
                        grid: {
                            left: "2%",
                            right: "5%",
                            top: "15%",
                            bottom: "0%",
                            containLabel: true,
                        },
                        title: {
                            text: "基层治理",
                            x: "left",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        legend: {
                            show: true,
                            x: "center",
                            y: "5",
                            itemWidth: 20,
                            itemHeight: 20,
                            textStyle: {
                                color: "#fff",
                                fontSize: "28px",
                            },
                            // data: [legend],
                        },
                        xAxis: [
                            {
                                type: "category",
                                boundaryGap: false,
                                axisLabel: {
                                    color: "#fff",
                                    rotate: 45,
                                    fontSize: "28px",
                                },
                                axisLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#bbb",
                                    },
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#195384",
                                    },
                                },
                                // data: data.map((item) => {
                                //     return item.name;
                                // }),
                                data:['2023/12','2024/01','2024/02','2024/03','2024/04','2024/05','2024/06','2024/07','2024/08','2024/09','2024/10','2024/11','2024/12','2025/01','2025/02','2025/03','2025/04']
                            },
                        ],
                        yAxis: [
                            {
                                type: "value",
                                name: "",
                                min: 0,
                                // max: 1000,
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                axisLabel: {
                                    formatter: "{value}",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: "28px",
                                    },
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: "#fff",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#5087EC",
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "新增",
                                type: "line",
                                stack: "总量",
                                // symbol: "circle",
                                symbolSize: 10,
                                itemStyle: {
                                    normal: {
                                        color: "#0092f6",
                                        lineStyle: {
                                            color: "#5087EC",
                                            width: 4,
                                        },
                                    },
                                },
                                areaStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "rgba(71,121,213,1)",
                                                },
                                                {
                                                    offset: 1,
                                                    color: "rgba(71,121,213,0)",
                                                },
                                            ],
                                            false
                                        ),
                                    },
                                },
                                data: data.map((item) => {
                                    return item.value;
                                }),
                            },
                            {
                                name: "办结",
                                type: "line",
                                stack: "总量",
                                // symbol: "circle",
                                symbolSize: 10,
                                itemStyle: {
                                    normal: {
                                        color: "rgba(105, 187, 196,1)",
                                        lineStyle: {
                                            color: "rgba(105, 187, 196,1)",
                                            width: 2,
                                        },
                                    },
                                },
                                areaStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "rgba(105, 187, 196,1)",
                                                },
                                                {
                                                    offset: 1,
                                                    color: "rgba(105 ,187, 196,0)",
                                                },
                                            ],
                                            false
                                        ),
                                    },
                                },
                                data: data.map((item) => {
                                    return item.value1;
                                }),
                            },
                        ],
                    };
                    myChartsRun.setOption(option);
                    tools.loopShowTooltip(myChartsRun, option, {
                        loopSeries: true,
                    }); //轮播
                },
                //绘制饼图
                PiechartsShow(data) {
                    const myChartsPerson = echarts.init(document.getElementById("bar-chart"));
                    var fontColor = "#30eee9";
                    let option = {
                        grid: {
                            // left: "15%",
                            right: "2%",
                            // top: "30%",
                            // bottom: "15%",
                            containLabel: true,
                        },
                        title: {
                            text: "矛盾纠纷",
                            x: "left",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        color: ["#68BBC4", "#5087EC", "#58A55C", "#F2BD42", "#EE752F"],
                        series: [
                            {
                                name: "矛盾纠纷",
                                type: "pie",
                                type: "pie",
                                radius: ["45%", "60%"],
                                center: ["50%", "50%"],
                                data: data,
                                itemStyle: {
                                    color: "#fff",
                                    emphasis: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: "rgba(0, 0, 0, 0.5)",
                                    },
                                },
                                itemStyle: {
                                    normal: {
                                        label: {
                                            show: true,
                                            color: "#fff",
                                            fontSize: 28,
                                            //	                            position:'inside',
                                            formatter: "{b}:\n{d}%",
                                        },
                                    },
                                    labelLine: { show: true },
                                },
                            },
                        ],
                    };

                    myChartsPerson.setOption(option);
                    tools.loopShowTooltip(myChartsPerson, option, {
                        loopSeries: true,
                    }); //轮播
                },
            },
            //项目生命周期
            mounted() {
                this.init();
            },
        });
    </script>
</html>
