<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>公共服务-首页-左侧</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <link
      rel="stylesheet"
      href="/static/citybrain/ggfw/css/ggfw_index_left.css"
    />
  </head>
  <style></style>

  <body>
    <div id="app" class="container" v-cloak>
      <nav>
        <s-header-title
          htype="1"
          title="政务服务"
          data-time=""
        ></s-header-title>
      </nav>
      <div class="zwfw">
        <div class="zwfw_item" v-for="(item,index) in zwfwList" :key="index" @click="zwfwClick(index)"
              :class="zwfwIndex==index?'zwfw_active':''">
          <p class="s-c-yellow-gradient s-font-50 s-w7">{{item.value}}</p>
          <p class="s-c-yellow-gradient s-font-30">{{item.unit}}</p>
          <p class="s-c-white s-font-30">{{item.name}}</p>
        </div>
      </div>
      <nav>
        <s-header-title
          htype="1"
          title="住建服务"
          data-time=""
          :click-flag="true"
          @click="openDiaog('zjfw-dialog')"
        ></s-header-title>
      </nav>
      <div class="zjfw">
        <div class="zjfw-top">
          <div
            class="zjfw-top-item"
            v-for="(item,index) in zjfwTopList"
            :key="index"
          >
            <span class="s-font-30 s-c-white">{{item.name}}</span>
            <div
              class="number s-c-yellow-gradient"
              v-for="(item, i) in item.value"
              :key="i"
            >
              <span class="numbg">
                <count-to
                  :start-val="0"
                  :end-val="Number(item)"
                  :duration="3000"
                  class="s-c-yellow-gradient"
                >
                </count-to>
              </span>
            </div>
            <span class="s-font-30 s-c-white">{{item.unit}}</span>
          </div>
        </div>
        <div class="zjfw-con s-flex">
          <div class="zjfw-con-left">
            <nav>
              <s-header-title-2 title="房产交易因素分析"></s-header-title-2>
            </nav>
            <div>
              <div class="zjfw-con-left-top">
                <div
                  class="zjfw-con-top-item"
                  v-for="(item,index) in fcjyList"
                  :key="index"
                >
                  <p>{{item.name}}</p>
                  <div>
                    <span class="s-c-blue-gradient s-font-40 s-w7"
                      >{{item.value}}</span
                    >
                    <span class="s-c-blue-gradient s-font-30"
                      >{{item.unit}}</span
                    >
                  </div>
                </div>
              </div>
              <div id="chart01" style="width: 100%; height: 360px"></div>
            </div>
          </div>
          <div class="zjfw-con-right">
            <nav>
              <s-header-title-2 title="房产交易分析" :click-flag="true" @click="fcjyClick"></s-header-title-2>
            </nav>
            <div class="select-property">
              <el-select v-model="cityVal" placeholder="区域">
                  <el-option v-for="item in city" :key="item.label" :label="item.label" :value="item.label">
                  </el-option>
              </el-select>
            </div>
            <div id="chart02" style="width: 100%; height: 440px"></div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="jyfw">
          <nav>
            <s-header-title
              htype="2"
              title="教育服务"
              data-time=""
              :click-flag="true"
              @click="openDiaog('jyfw-dialog')"
            ></s-header-title>
          </nav>
          <div class="jyfw-con">
            <div class="select">
              <el-select v-model="value" placeholder="区域">
                  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
              </el-select>
            </div>
            <div class="jyfw-con-top">
              <div
                class="jyfw-con-top-item"
                v-for="(item,index) in jyfwList"
                :key="index"
              >
                <p class="s-c-white s-font-30">{{item.name}}</p>
                <p>
                  <span class="s-c-blue-gradient s-font-40 s-sty"
                    >{{item.value}}</span
                  >
                  <span class="s-c-white s-font-30">{{item.unit}}</span>
                </p>
              </div>
            </div>
            <div id="chart03" style="width: 100%; height: 390px"></div>
          </div>
        </div>
        <div class="rsfw">
          <nav>
            <s-header-title
              htype="2"
              title="人社服务"
              data-time=""
              :click-flag="true"
              @click="openDiaog('rsfw-dialog')"
            ></s-header-title>
          </nav>
          <div class="rsfw-con">
            <div
              class="rsfw-con-item" :class="index==1?'pointer':''"
              v-for="(item,index) in rsfwList"
              :key="index" @click="rsfwClick(index)"
            >
              <div class="s-c-white s-font-35">{{item.name}}</div>
              <div class="rsfw-con-item-block">
                <span class="s-c-blue-gradient s-font-40">{{item.value}}</span>
                <span class="s-c-white s-font-30">{{item.unit}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>

  <script>
    var vm = new Vue({
      el: "#app",
      data() {
        return {
          zwfwIndex:null,
          zwfwList: [],
          zjfwTopList: [],
          fcjyList: [],
          bxzcData: [],
          bxzcData1: [],
          jyfwList: [],
          lineData: [],
          rsfwIndex:null,
          rsfwList: [],

          value:'1',
          cityVal:"全市",
          options: [
            {
                value: '1',
                label: '高等学校',
            },
            {
                value: '2',
                label: '普通中等专业学校',
            },
            {
                value: '3',
                label: '普通中学',
            },
            {
                value: '4',
                label: '职业高中',
            },
            {
                value: '5',
                label: '普通小学',
            },
            {
                value: '6',
                label: '特殊教育学校',
            },
            {
                value: '7',
                label: '幼儿园',
            },
            {
                value: '8',
                label: '技工学校',
            }
          ],
          bkName:null,
          dialogIndex:null,
          bankuaiClick:false,
          city:[
            {
                value: '1',
                label: '全市',
            },
            {
                value: '2',
                label: '婺城区',
            },
            {
                value: '3',
                label: '金义新区',
            },
            {
                value: '4',
                label: '兰溪市',
            },
            {
                value: '5',
                label: '东阳市',
            },
            {
                value: '6',
                label: '义乌市',
            },
            {
                value: '7',
                label: '永康市',
            },
            {
                value: '8',
                label: '浦江县',
            },
            {
                value: '9',
                label: '武义县',
            },
            {
                value: '10',
                label: '磐安县',
            },

          ],
          titleValue:'',
          districtProperty:[1.2,1.2,1.2,1.2,1.2,1.2,1.3,1.4,1.5,1.6,1.7,1.8],
          pointData:[
            {
              title: '浦江县',
              gps_x: "119.94315399169922",
              gps_y: "29.5630503845215",
            },
            {
              title: '兰溪市',
              gps_x: "119.46214447021484",
              gps_y: "29.31345558166504",
            },
            {
              title: '婺城区',
              gps_x: "119.5569204711914",
              gps_y: "29.00677101135254",
            },
            {
              title: '金义新区',
              gps_x: "119.8483056640625",
              gps_y: "29.188559951782227",
            },
            {
              title: '义乌市',
              gps_x: "120.08206787109375",
              gps_y: "29.322123641967773",
            },
            {
              title: '武义县',
              gps_x: "119.*************",
              gps_y: "28.79677101135254",
            },
            {
              title: '永康市',
              gps_x: "120.1469204711914",
              gps_y: "28.97677101135254",
            },
            {
              title: '东阳市',
              gps_x: "120.4169204711914",
              gps_y: "29.24677101135254",
            },
            {
              title: '磐安县',
              gps_x: "120.6299204711914",
              gps_y: "29.06677101135254",
            },
          ],
        };
      },
      watch: {
        cityVal: {
          handler(newName, oldName) {
            $get("/ggfw/index/left05").then((res) => {
            this.bxzcData1 = res;
            this.getChart02("chart02");
          });
          },
          immediate: true
        }
      },
      mounted() {
        this.init();
        this.initMap();
        var that = this
        window.addEventListener("message", function (e) {
          let info = e.data;
          if(info.type == "pointClick" && that.dialogIndex!=null){
            let title = info.data.title
            that.openMapDialog(that.dialogIndex,title)
          }else if(info.type == "bankuaiClick" && that.bkName != info.data.NAME && that.bankuaiClick){
            that.rmPop()  //清除
            let coor = JSON.parse(info.data.center)
            let name = info.data.NAME
            that.bkName = info.data.NAME
            that.openCustomPop(coor,name)
          }
        });

        top.emiter && top.emiter.on('jyfw',(res)=>{
          that.rmMap()
          that.dialogIndex = null
          that.titleValue = '办学申报情况'
          if(res==0){
            that.addPoint2()
            that.add3DText()
            that.bankuaiClick = false
            that.dialogIndex = res
          }else if(res==1){
            that.addPoint2()
            that.add3DText()
            that.bankuaiClick = false
            that.dialogIndex = res
          }else if(res==2){
            that.Histogram()
            that.bankuaiClick = false
          }
        })
        top.emiter && top.emiter.on('rsfw',(res)=>{
          that.rmMap()
          that.dialogIndex = null
          this.titleValue = '劳动关系'
          if(res==0){
            that.addPoint2()
            that.add3DText()
            that.dialogIndex = res
            that.bankuaiClick = false
          }
        })
        top.emiter && top.emiter.on('wsfw',(res)=>{
          that.rmMap()
          that.dialogIndex = null
          if(res==0){
            that.addPoint2()
            that.add3DText()
            that.bankuaiClick = false
            that.dialogIndex = res
            this.titleValue = '卫生服务'
          }else if(res==2){
            that.addPoint2()
            that.add3DText()
            that.bankuaiClick = false
            that.dialogIndex = res
          }else if(res==3){
            that.Histogram()
            that.hotPowerMap()
            that.bankuaiClick = false
          }
        })
        top.emiter && top.emiter.on('wlfw',(res)=>{ 
          that.rmMap()
          that.dialogIndex = null
          if(res==0){
            that.addPoint2()
            that.hotPowerMap()
            that.add3DText()
            that.bankuaiClick = false
            that.dialogIndex = res
            this.titleValue = '场馆'
          }else if(res==1){
            that.addPoint2()
            that.add3DText()
            that.bankuaiClick = false
            that.dialogIndex = 0
            that.openIframe2()
            that.closeIframe()
            this.titleValue = '游客画像'
          }
        })
        top.emiter && top.emiter.on('zjfw',(res)=>{
          that.rmMap()
          that.dialogIndex = null
          that.zwfwIndex = 5
          if(res==1){
            that.addPoint2();
            that.Histogram1()
            that.bankuaiClick = true
          }else if(res==2){
            $get('/ggfw/index/qsfwggxq').then((res)=>{
              that.addPoint(res)
            })
            that.add3DText()
            that.bankuaiClick = false
          }else if(res==3){
            $get('/ggfw/index/zcwjxq').then((res)=>{
              that.addPoint(res)
            })
            that.add3DText()
            that.bankuaiClick = false
          }else if(res==4){
            that.dialogIndex = res
            that.addPoint2()
            that.add3DText()
            that.bankuaiClick = false
          }
        })
      },
      methods: {
        init() {
          $get("/ggfw/index/left01").then((res) => {
            this.zwfwList = res;
          });
          $get("/ggfw/index/left02").then((res) => {
            this.zjfwTopList = res;
          });
          $get("/ggfw/index/left03").then((res) => {
            this.fcjyList = res;
          });
          $get("/ggfw/index/left04").then((res) => {
            this.bxzcData = res;
            this.getChart01("chart01");
          });
          
          $get("/ggfw/index/left06").then((res) => {
            this.jyfwList = res;
          });
          $get("/ggfw/index/left07").then((res) => {
            this.lineData = res;
            this.getChart03("chart03");
          });
          $get("/ggfw/index/left08").then((res) => {
            this.rsfwList = res;
          });
        },

        zwfwClick(index){
          this.zwfwIndex = index
          this.bankuaiClick = true
          this.closeIframe2()
          this.closeMapDialog()
          this.rmMap()
          if(index==0){
            this.closeIframe()
            this.Histogram()
          }else if(index==1){
            this.closeIframe()
            this.Histogram()
          }else if(index==2){
            this.openIframe('区县网上可办率排名')
            this.Histogram()
          }else if(index==3){
            this.openIframe('区县掌上可办率排名')
            this.Histogram()
          }else if(index==4){
            this.openIframe('区县跑零次可办率排名')
            this.Histogram()
          }
        },
        rsfwClick(index){
          this.closeIframe2()
          this.closeMapDialog()
          this.rsfwIndex = index
          this.bankuaiClick = false
          this.dialogIndex = null
          this.rmMap()
          if(index==1){
            $get('/ggfw/index/rsfwxq').then((res)=>{
              this.addPoint(res)
            })
            this.add3DText()
            this.openIframe('区县人社服务排名')
          }
        },
        fcjyClick(){
          this.closeIframe()
          this.closeIframe2()
          this.bankuaiClick = false
          this.rmMap()
          this.Histogram()
        },
        initMap(){
          top.document.getElementById("map").contentWindow.Work.change3D(9)
          this.flyTo()
          this.add3DText()
        },
        rmMap(){
          this.rmHistogram()
          this.rmPoint()
          this.rmHot()
          this.rmPop()
          this.rm3DText()
          this.closeMapDialog()
        },

        openCustomPop(coor,name){
          console.log(this.zwfwIndex);
          $get('/ggfw/index/tjqhdtxq').then((res)=>{
            let result = []
            result = res.filter((item)=>{
              return this.zwfwIndex == item.type && name == item.name
            })
            this.customPop(coor,name,result)
          })
          
        },
        //添加自定义弹框
        customPop(coor,name,result){
          let objData = {
            funcName: 'customPop',
            coordinates: coor,
            // coordinates: [119.*************, 28.79677101135254],
            closeButton: true,
            html: `<div onclick="this.style.display = 'none'"
                      class="contain" id="customPop"
                      style="
                        position: absolute;
                        height: 200px;
                        width: max-content;
                        display:inline-block;
                        background-color: rgba(0, 0, 0, 0.8);
                        border: 2px solid #00aae2;
                        box-sizing: border-box;
                        border-style: solid;
                        border-width: 4px;
                        border-image-source: linear-gradient(0deg, #32abe4 0%, #0b5aa4 100%);
                        border-image-slice: 1;">
                      <div
                        class="title"
                        style="
                          background: linear-gradient(360deg, #096c8d, #073446);
                          width: 100%;
                          height: 60px;
                          font-size: 32px;
                          color: #fff;
                          padding:0 30px;
                          box-sizing: border-box;
                          line-height: 60px;">
                        ${name}
                      </div>
                      <div
                        class="content"
                        style="font-size: 28px;color: #fff;padding: 20px;line-height: 55px;">
                        <p> 
                          ${result[0].lable1 +":"+ result[0].value1 + result[0].unit}
                        </p>
                        <p> 
                          ${result[0].lable2 ? result[0].lable2 +":"+ result[0].value2 + result[0].unit : '' }
                        </p>
                      </div>
                    </div>`,
          }
          top.document.getElementById('map').contentWindow.Work.funChange(JSON.stringify(objData))
        },

        //清除弹框
        rmPop(){
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName:"rmPop", 
            })
          )
        },

        //区划地图柱状图
        Histogram(){
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              "funcName":"Histogram" , //功能名称
              "HistogramData":  [   //name 对应的区划，num柱状图高度 ，unit 展示单位
                  {name: '浦江县',num:50, unit: '个'}, 
                  {name: '兰溪市',num:56, unit: '个'}, 
                  {name: '婺城区',num:40, unit: '个'},
                  {name: '金义新区',num:77, unit: '个'}, 
                  {name: '义乌市',num:77, unit: '个'}, 
                  {name: '武义县',num:65, unit: '个'}, 
                  {name: '永康市',num:24, unit: '个'}, 
                  {name: '东阳市',num:37, unit: '个'}, 
                  {name: '磐安县',num:32, unit: '个'}
              ] 
            })
          );
        },
        Histogram1(){
          console.log(5555);
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              "funcName":"Histogram" , //功能名称
              "HistogramData":  [   //name 对应的区划，num柱状图高度 ，unit 展示单位
                {name: '浦江县', num: 90, unit: '亿元'},
                {name: '兰溪市', num: 86, unit: '亿元'},
                {name: '婺城区', num: 83, unit: '亿元'},
                {name: '金义新区', num: 150, unit: '亿元'},
                {name: '义乌市', num: 152, unit: '亿元'},
                {name: '武义县', num: 79, unit: '亿元'},
                {name: '永康市', num: 81, unit: '亿元'},
                {name: '东阳市', num: 44, unit: '亿元'},
                {name: '磐安县', num: 67, unit: '亿元'}],
              color:[1,"rgba(0, 255, 127,1)",2,"rgba(0, 255, 127,1)",3,"rgba(0, 255, 127,1)",4,"rgba(255, 0, 0,1)",5,"rgba(255, 0, 0,1)",6,"rgba(0, 255, 127,1)",7,"rgba(0, 255, 127,1)",8,"rgba(255, 255, 0,1)",9,"rgba(0, 255, 127,1)","rgba(0, 255, 127,1)"]
            })
          );
        },
        addPoint5(){
                let res = [
                    {
                    title: '兰溪市',
                    gps_x: "119.61393786758725",
                    gps_y: "29.337548730602492",
                    },
                    {
                    title: '浦江县',
                    gps_x: "119.82309325155927",
                    gps_y: "29.57229712251442",
                    },
                    {
                    title: '婺城区',
                    gps_x: "119.44905760065507",
                    gps_y: "29.02695479427512",
                    },
                    {
                    title: '磐安县',
                    gps_x: "120.50584713234258",
                    gps_y: "29.030693518821003",
                    },
                    {
                    title: '武义县',
                    gps_x: "119.62350263983319",
                    gps_y: "28.743033979151782",
                    },
                    
                    {
                    title: '永康市',
                    gps_x: "120.05292223767299",
                    gps_y: "28.94776816680602",
                    },
                        ];
                let res1 =[
                {
                    title: '东阳市',
                    gps_x: "120.2699131291854",
                    gps_y: "29.23985374898153",
                    },
                ]
                let res2 =[
                  {
                    title: '金义新区',
                    gps_x: "119.75733591327185",
                    gps_y: "29.162967800404942",
                    },
                    {
                    title: '义乌市',
                    gps_x: "119.9986694041882",
                    gps_y: "29.2330852742355",
                  },
                ]
                let arr = res.map((item) => {
                    return {
                    data: {},
                    title:item.title,
                    point: item.gps_x + "," + item.gps_y,
                    };
                });
                let arr1 = res1.map((item) => {
                    return {
                    data: {},
                    title:item.title,
                    point: item.gps_x + "," + item.gps_y,
                    };
                });
                let arr2 = res2.map((item) => {
                    return {
                    data: {},
                    title:item.title,
                    point: item.gps_x + "," + item.gps_y,
                    };
                });
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                    funcName: "pointLoad",
                    pointType: "digital-green", // 点位类型（图标名称）
                    pointId: "digital-green", // 点位唯一id
                    setClick: true,
                    pointData: arr,
                    imageConfig: { iconSize: 0.6 },
                    popup:{
                        offset:[50,-100]
                    }
                    })
                );
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                    funcName: "pointLoad",
                    pointType: "digital-yellow", // 点位类型（图标名称）
                    pointId: "digital-yellow", // 点位唯一id
                    setClick: true,
                    pointData: arr1,
                    imageConfig: { iconSize: 0.6 },
                    popup:{
                        offset:[50,-100]
                    }
                    })
                );
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                    funcName: "pointLoad",
                    pointType: "aqsc-1", // 点位类型（图标名称）
                    pointId: "aqsc-1", // 点位唯一id
                    setClick: true,
                    pointData: arr2,
                    imageConfig: { iconSize: 0.6 },
                    popup:{
                        offset:[50,-100]
                    }
                    })
                );
          
        },
        

        //清除柱状体
        rmHistogram(){
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              "funcName":"rmHistogram" , //清除柱状体
            })
          )
        },

        //飞入
        flyTo(){
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "flyto", //功能名称
              flyData: {
                center: [119.95478050597587, 29.01613226366889],
                zoom: 10.5,
                pitch: 40,
                bearing: 0,
              },
            })
          )
        },

        //添加点位
        addPoint(res){
          // let res = [
          //   {
          //     title: '浦江县',
          //     gps_x: "119.94315399169922",
          //     gps_y: "29.5630503845215",
          //   },
          //   {
          //     title: '兰溪市',
          //     gps_x: "119.46214447021484",
          //     gps_y: "29.31345558166504",
          //   },
          //   {
          //     title: '婺城区',
          //     gps_x: "119.5569204711914",
          //     gps_y: "29.00677101135254",
          //   },
          //   {
          //     title: '金义新区',
          //     gps_x: "119.8483056640625",
          //     gps_y: "29.188559951782227",
          //   },
          //   {
          //     title: '义乌市',
          //     gps_x: "120.08206787109375",
          //     gps_y: "29.322123641967773",
          //   },
          //   {
          //     title: '武义县',
          //     gps_x: "119.*************",
          //     gps_y: "28.79677101135254",
          //   },
          //   {
          //     title: '永康市',
          //     gps_x: "120.1469204711914",
          //     gps_y: "28.97677101135254",
          //   },
          //   {
          //     title: '东阳市',
          //     gps_x: "120.4169204711914",
          //     gps_y: "29.24677101135254",
          //   },
          //   {
          //     title: '磐安县',
          //     gps_x: "120.6299204711914",
          //     gps_y: "29.06677101135254",
          //   }
          // ];
          let arr = res.map((item) => {
            return {
              data: {
                title: item.title,
                key: item.detail.map((el)=>{
                  return el.name
                }),
                value: item.detail.map((el)=>{
                  return el.value
                }),
              },
              point: item.gps_x + "," + item.gps_y,
            };
          });
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "pointLoad",
              pointType: "橙色预警事件", // 点位类型（图标名称）
              pointId: "point1", // 点位唯一id
              setClick: true,
              pointData: arr,
              imageConfig: { iconSize: 0.6 },
              popup:{
                offset:[50,-100]
              }
            })
          );
        },
        addPoint2(){
          let res = [
            {
              title: '浦江县',
              gps_x: "119.94315399169922",
              gps_y: "29.5630503845215",
            },
            {
              title: '兰溪市',
              gps_x: "119.46214447021484",
              gps_y: "29.31345558166504",
            },
            {
              title: '婺城区',
              gps_x: "119.5569204711914",
              gps_y: "29.00677101135254",
            },
            {
              title: '金义新区',
              gps_x: "119.8483056640625",
              gps_y: "29.188559951782227",
            },
            {
              title: '义乌市',
              gps_x: "120.08206787109375",
              gps_y: "29.322123641967773",
            },
            {
              title: '武义县',
              gps_x: "119.*************",
              gps_y: "28.79677101135254",
            },
            {
              title: '永康市',
              gps_x: "120.1469204711914",
              gps_y: "28.97677101135254",
            },
            {
              title: '东阳市',
              gps_x: "120.4169204711914",
              gps_y: "29.24677101135254",
            },
            {
              title: '磐安县',
              gps_x: "120.6299204711914",
              gps_y: "29.06677101135254",
            }
          ];
          let arr = res.map((item) => {
            return {
              data: {},
              title:item.title,
              point: item.gps_x + "," + item.gps_y,
            };
          });
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "pointLoad",
              pointType: "橙色预警事件", // 点位类型（图标名称）
              pointId: "point1", // 点位唯一id
              setClick: true,
              pointData: arr,
              imageConfig: { iconSize: 0.6 },
              popup:{
                offset:[50,-100]
              }
            })
          );
        },

        //清除点位
        rmPoint(){
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName:"rmPoint" ,
              pointId: "",
            })
          )
        },

        // 加载3D文字方法
        add3DText(){
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName:"3Dtext" , //3D文字功能
              textData: [   // pos文字的位置  //text 展示的文字
                  {pos: [119.94315399169922,29.5630503845215,11000],text:"浦江县"},
                  {pos: [119.46214447021484,29.31345558166504,11000],text:"兰溪市"},
                  {pos: [119.5569204711914, 29.00677101135254,11000],text:"婺城区"},
                  {pos: [119.8483056640625, 29.188559951782227,11000],text:"金义新区"},
                  {pos: [120.08206787109375,29.322123641967773,11000],text:"义乌市"},
                  {pos: [119.*************, 28.79677101135254,11000],text:"武义县"},
                  {pos: [120.1469204711914, 28.97677101135254,11000],text:"永康市"},
                  {pos: [120.4169204711914, 29.24677101135254,11000],text:"东阳市"},
                  {pos: [120.6299204711914, 29.06677101135254,11000],text:"磐安县"}
              ],
              textSize:40,
              id: 'text1',
              // zoomShow: true,
              color: [255,255,255,1],
            })
          )
        },

        //清除3D文字方法
        rm3DText(){
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName:"rm3Dtext" , //清除柱状体
            })
          )
        },

        //加载热力图
        hotPowerMap(){
          let timeStr = "2022-09-18 00:00:00";
            $api("/cstz_sjz_rlt_new", { date: timeStr }).then((res) => {
              let hotMapData = [];
              let heatArr = [];
              let len = res[0].heatmap.length;
              let sumLen = 20000 - len;
              if (len >= 20000) {
                heatArr = res[0].heatmap.slice(0, 20000);
              } else {
                heatArr = res[0].heatmap;
                for (let j = 0; j < sumLen; j++) {
                  let a = {
                    count: 0,
                    geohash: 0,
                    lat: 0,
                    lng: 0,
                  };
                  heatArr.push(a);
                }
              }
              heatArr.map((item) => {
                // 画热力图的数据
                let pointArr = [];
                pointArr[0] = item.lng;
                pointArr[1] = item.lat;
                pointArr[2] = item.count;
                pointArr[3] = item.geohash;
                hotMapData.push(pointArr);
              });
              const mapData = {
                funcName: "hotPowerMap",
                hotPowerMapData: hotMapData,
                offset: 256,
                heatMapId: "Hot",
                threshold: 6000,
                distance: 800,
                alpha: 0.3,
              };
              window.parent.document.getElementById("map").contentWindow.Work.funChange(JSON.stringify(mapData));
            });
        },
         
        //清除热力图
        rmHot(){
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName:"rmhotPowerMap", //热力图
              heatMapId:'Hot'
            })
          )
          this.initMap()
        },

        openIframe(titleName) {
          let Iframe = {
            type: "openIframe",
            name: 'IframeName',
            src: baseURL.url + "/static/citybrain/ggfw/commont/rsfwpm.html",
            left: "2200px",
            top: "225px",
            width: "600px",
            height: "800px",
            zIndex: "10",
            argument: {
              status: "openIframe",
              title:titleName
            },
          };
          window.parent.postMessage(JSON.stringify(Iframe), "*");
        },

        closeIframe() {
          top.commonObj.funCloseIframe({
            name: "IframeName",
          });
        },


        //文旅服务左上角弹框
        openIframe2() {
          let Iframe = {
            type: "openIframe",
            name: 'IframeName2',
            src: baseURL.url + "/static/citybrain/ggfw/commont/ggfw-index-wlfw-dialog.html",
            left: "2200px",
            top: "225px",
            width: "1000px",
            height: "600px",
            zIndex: "10",
            argument: {
              status: "openIframe2",
            },
          };
          window.parent.postMessage(JSON.stringify(Iframe), "*");
        },

        closeIframe2() {
          top.commonObj.funCloseIframe({
            name: "IframeName2",
          });
        },

        

        openDiaog(openName) {
          this.closeIframe()
          this.closeIframe2()
          this.closeMapDialog()
          let diaog = {
            type: "openIframe",
            name: openName,
            src:
              baseURL.url +
              "/static/citybrain/ggfw/pages/" +
              openName +
              ".html",
            left: "calc(50% - 2157px)",
            top: "225px",
            width: "4315px",
            height: "1865px",
            zIndex: "10",
            argument: {
              status: "",
            },
          };
          window.parent.postMessage(JSON.stringify(diaog), "*");
        },
        
        openMapDialog(index,title){
          let mapDiaogurl = null
          let width= null
          let height= null
          // let item = []
          if(index==0){
            //公共
            width='800px'
            height='850px'
            mapDiaogurl= 'ggfw-index-jyfw-gljyjg-dialog'
 
          }else if(index==1){
            //教育服务-办学政策
            width='800px'
            height='700px'
            mapDiaogurl= 'ggfw-index-jyfw-bxzc-dialog'
          }else if(index==2){
            //卫生服务-卫生工作
            width='800px'
            height='500px'
            mapDiaogurl= 'ggfw-index-wsfw-dialog'
          }else if(index==4){
            //住建服务-咨询互动
            mapDiaogurl= 'ggfw-index-zjfw-dialog'
            width='1500px'
            height='500px'
          }
          let mapDiaog = {
            type: "openIframe",
            name: 'mapDiaog',
            src: baseURL.url + "/static/citybrain/ggfw/commont/" + mapDiaogurl + ".html",
            left: "3430px",
            top: "570px",
            width: width,
            height: height,
            zIndex: "10",            
            argument: {
              status: "mapDiaog",
              title:title,
              titleValue:this.titleValue,
              // item:item,
            },
          };
          window.parent.postMessage(JSON.stringify(mapDiaog), "*");
        },

        closeMapDialog() {
          top.commonObj.funCloseIframe({
            name: "mapDiaog",
          });
        },
        
        getChart01(id) {
          let myChart = echarts.init(document.getElementById(id));
          var legend = ["商品房住宅成交面积", "商品房住宅供应面积"];
          var colorList = ["#66b1ff", "#968212", "#58A55C"];
          var data = [];
          let x = this.bxzcData.map((item) => {
            return item.name;
          });
          let y1 = this.bxzcData.map((item) => {
            return item.value1;
          });
          let y2 = this.bxzcData.map((item) => {
            return item.value2;
          });
          data.push(y1, y2);
          let option = {
            tooltip: {
              trigger: "item",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "30",
              },
            },
            // color: colors,
            legend: {
              x: "center",
              y: "15",
              itemWidth: 20,
              itemHeight: 20,
              textStyle: {
                color: "#fff",
                fontSize: 28,
              },
              data: legend,
            },
            grid: {
              left: "3%",
              right: "4%",
              bottom: "0",
              containLabel: true,
            },
            xAxis: {
              type: "category",
              axisLabel: {
                color: "#fff",
                fontSize: 28,
                // rotate: 45,
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#bbb",
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  color: "#195384",
                },
              },
              data: x,
            },
            yAxis: {
              type: "value",
              name: "面积:㎡",
              nameTextStyle: {
                color: "#fff",
                fontSize: 28,
              },
              axisLabel: {
                formatter: "{value}",
                textStyle: {
                  color: "#fff",
                  fontSize: 28,
                },
              },
              axisLine: {
                lineStyle: {
                  color: "#fff",
                },
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: "#11366e",
                },
              },
            },
            series: [],
          };
          for (var i = 0; i < legend.length; i++) {
            option.series.push({
              name: legend[i],
              type: "bar",
              stack: "总量",
              barWidth: 35,
              itemStyle: {
                normal: {
                  // color: colorList[i]
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 0.9, [
                    {
                      offset: 0,
                      color: colorList[i],
                    },
                    {
                      offset: 0.5,
                      color: colorList[i],
                    },
                    {
                      offset: 1,
                      color: "#031827",
                    },
                  ]),
                },
              },
              label: {
                show: false,
                position: "insideRight",
              },
              data: data[i],
            });
          }
          myChart.setOption(option);
        },
        
        getChart02(id) {
          let myChart = echarts.init(document.getElementById(id));
          let legend=[];
           legend = [
            "新建商品房交易套数",
            "二手房交易套数",
            "全市房产交易均衡系数",
          ];
          let list=[];
          if(this.cityVal!=="全市"){
            var title=this.cityVal+"房产交易均衡系数";
            legend.push(title);
            list=this.districtProperty;
          }else{
            list=[];
          }
          console.log(legend);
          
          var colorList = ["#66b1ff", "#58A55C", "#968212"];
          var data = [];
          let x = this.bxzcData1.map((item) => {
            return item.name;
          });
          let y1 = this.bxzcData1.map((item) => {
            return item.value1;
          });
          let y2 = this.bxzcData1.map((item) => {
            return item.value2;
          });
          let y3 = this.bxzcData1.map((item) => {
            return item.value3;
          });
          data.push(y1, y2, y3);
          let option = {
            tooltip: {
              trigger: "item",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "30",
              },
            },
            // color: colors,
            legend: {
              x: "15%",
              y: "10",
              itemWidth: 20,
              itemHeight: 20,
              textStyle: {
                color: "#fff",
                fontSize: 28,
              },
              data: legend,
            },
            grid: {
              left: "3%",
              right: "4%",
              bottom: "0",
              top: "20%",
              containLabel: true,
            },
            xAxis: {
              type: "category",
              axisLabel: {
                color: "#fff",
                fontSize: 28,
                // rotate: 45,
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#bbb",
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  color: "#195384",
                },
              },
              data: x,
            },
            yAxis: [
              {
                type: "value",
                name: "面积:㎡",
                nameTextStyle: {
                  color: "#fff",
                  fontSize: 28,
                },
                axisLabel: {
                  formatter: "{value}",
                  textStyle: {
                    color: "#fff",
                    fontSize: 28,
                  },
                },
                axisLine: {
                  lineStyle: {
                    color: "#fff",
                  },
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: "#11366e",
                  },
                },
              },
              {
                name: "系数",
                type: "value",
                nameTextStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                  padding: [0, 0, 20, 0],
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: "#77b3f1",
                    opacity: 0.1,
                    width: 2,
                  },
                },
                axisTick: {
                  show: true,
                  lineStyle: {
                    color: "#77b3f1",
                    opacity: 0.5,
                    width: 2,
                  },
                },
                axisLabel: {
                  show: true,
                  formatter: "{value}",
                  textStyle: {
                    color: "#fff",
                    fontSize: 28,
                  },
                },
              },
            ],
            series: [
              {
                name: legend[0],
                type: "bar",
                yAxisIndex: 0,
                stack: "总量",
                barWidth: 35,
                itemStyle: {
                  normal: {
                    // color: colorList[i]
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 0.9, [
                      {
                        offset: 0,
                        color: colorList[0],
                      },
                      {
                        offset: 0.5,
                        color: colorList[0],
                      },
                      {
                        offset: 1,
                        color: "#031827",
                      },
                    ]),
                  },
                },
                label: {
                  show: false,
                  position: "insideRight",
                },
                data: data[0],
              },
              {
                name: legend[1],
                type: "bar",
                yAxisIndex: 0,
                stack: "总量",
                barWidth: 35,
                itemStyle: {
                  normal: {
                    // color: colorList[i]
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 0.9, [
                      {
                        offset: 0,
                        color: colorList[1],
                      },
                      {
                        offset: 0.5,
                        color: colorList[1],
                      },
                      {
                        offset: 1,
                        color: "#031827",
                      },
                    ]),
                  },
                },
                label: {
                  show: false,
                  position: "insideRight",
                },
                data: data[1],
              },
              {
                name: legend[2],
                type: "line", // 直线ss
                yAxisIndex: 1,
                smooth: false,
                symbolSize: 10,
                itemStyle: {
                  normal: {
                    color: "#ffc460",
                  },
                },
                data: data[2],
              },
              {
                name: legend[3],
                type: "line", // 直线ss
                yAxisIndex: 1,
                smooth: false,
                symbolSize: 10,
                itemStyle: {
                  normal: {
                    color: "#ffc460",
                  },
                },
                data: list,
              },
            ],
          };

          myChart.setOption(option);
        },
        
        getChart03(id) {
          let myChart = echarts.init(document.getElementById(id));
          let datax = [],
            datay = [];
          this.lineData.map((ele) => {
            datax.push(ele.name);
            datay.push(ele.value);
          });
          let option = {
            tooltip: {
              trigger: "axis",
            },
            legend: {
              show: false,
            },
            grid: {
              left: "3%",
              right: "4%",
              bottom: "8%",
              containLabel: true,
            },
            tooltip: {
              trigger: "axis",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              axisPointer: {
                lineStyle: {
                  color: "rgba(11, 208, 241, 1)",
                  type: "slider",
                },
              },
              textStyle: {
                color: "rgba(212, 232, 254, 1)",
                fontSize: 28,
              },
            },
            xAxis: [
              {
                type: "category",
                offset: 20,
                axisLine: {
                  //坐标轴轴线相关设置。数学上的x轴
                  show: true,
                  lineStyle: {
                    color: "rgba(108, 166, 219, 0.3)",
                  },
                },
                axisLabel: {
                  //坐标轴刻度标签的相关设置
                  rotate: -30,
                  textStyle: {
                    color: "#fff",
                    fontSize: 24,
                  },
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    color: "#192a44",
                  },
                },
                axisTick: {
                  show: false,
                },
                data: datax,
              },
            ],
            yAxis: [
              {
                name: "",
                nameTextStyle: {
                  fontSize: 24,
                  color: "#D6E7F9",
                  padding: [0, 20, 10, 0],
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: "#77b3f1",
                    opacity: 0.1,
                    width: 2,
                  },
                },
                axisTick: {
                  show: true,
                  lineStyle: {
                    color: "#77b3f1",
                    opacity: 0.5,
                    width: 2,
                  },
                },
                axisLabel: {
                  textStyle: {
                    fontSize: 24,
                    color: "#D6E7F9",
                  },
                },
                axisLine: {
                  show: false,
                  lineStyle: {
                    color: "#233653",
                  },
                },
              },
            ],
            series: [
              {
                name: "数量",
                type: "line",
                itemStyle: {
                  normal: {
                    color: "#3A84FF",
                    lineStyle: {
                      color: "#1b759c",
                      width: 4,
                    },
                    areaStyle: {
                      color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                        {
                          offset: 0,
                          color: "rgba(2, 92, 131,0.9)",
                        },
                        {
                          offset: 1,
                          color: "rgba(2, 92, 131,0.2)",
                        },
                      ]),
                    },
                  },
                },
                data: datay,
              },
            ],
          };
          myChart.setOption(option);
        },
      
      },
    });
  </script>
</html>
<style>
  .select-property{
    margin-left: 554px;
    width: 200px;
    height: 50px;
  }
</style>