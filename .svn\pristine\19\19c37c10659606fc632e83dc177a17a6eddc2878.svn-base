<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>一网通4-左</title>
    <script src="./Vue/vue.js"></script>
    <script src="./jquery/jquery-3.4.1.min.js"></script>
    <script src="./echarts/echarts.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <link rel="stylesheet" href="./elementui/css/elementui.css" />
    <script src="./elementui/js/elementui.js"></script>
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="/static/citybrain/csdn/css/ywt4-left.css" />
    <link rel="stylesheet" href="/static/css/xiaoguo.css" />
    <script src="/static/citybrain/hjbh/js/date.js"></script>
  </head>
  <body>
    <div id="ywt4-left" v-cloak>
      <div class="ywt4-left_box">
        <!-- 党建统领 -->
        <div class="box box1">
          <div class="box-title">
            <s-header-title
              title="党建统领"
              htype="1"
              :data-time="nowTime"
              :click-flag="true"
              onclick="top.commonObj.openMenuFun('dztl-sy')"
            ></s-header-title>
          </div>
          <div class="box-con box1-content">
            <div
              class="box1-con-item"
              v-for="(item,index) in djtlData"
              :key="index"
            >
              <s-img-text-box
                img-width="136px"
                img-height="120px"
                :name="item.name"
                :value="item.value"
                :unit="item.unit"
                :src="`/static/citybrain/csdn/img/ywt4-left/djtl-${index}.png`"
              >
              </s-img-text-box>
              <div
                v-if="index==1 || index==2||index==3"
                :id="`echarts${index}`"
                class="djtl-echarts"
                style="width: 290px; height: 25px"
              ></div>
            </div>
          </div>
        </div>
        <!--数字政府  -->
        <div class="box box2">
          <div class="box-title">
            <s-header-title
              title="数字政府"
              htype="1"
              :data-time="nowTime"
              :click-flag="true"
              onclick="top.commonObj.openMenuFun('szzf-sy')"
            ></s-header-title>
          </div>
          <div class="box-con box2-content">
            <div class="box2-left">
              <div
                class="box2-left-item"
                v-for="(item,index) in szzfLeftData"
                :key="index"
              >
                <img
                  :src="`/static/citybrain/csdn/img/ywt4-left/szzf-left-${index}.png`"
                  alt=""
                />
                <div class="b2-l-i-t">
                  <p>
                    <span>{{item.value}}</span>
                    <span>{{item.unit}}</span>
                  </p>
                  <p>{{item.name}}</p>
                </div>
              </div>
            </div>
            <div class="box2-right">
              <div class="box2-title-top">
                <div class="box2-t">
                  <p class="yel-color">19.35万家</p>
                  <p>服务企业</p>
                </div>
                <div class="box2-t">
                  <p class="yel-color">182.65万人</p>
                  <p>服务个人</p>
                </div>
              </div>
              <ul class="box2-r-item">
                <li v-for="(item,index) in szzfRightData" :key="index">
                  <img class="breath-light"
                    :src="`/static/citybrain/csdn/img/ywt4-left/szzf-right-${index}.png`"
                    alt=""
                  />
                  <p>{{item.name}}</p>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 数字经济 -->
        <div class="box box3">
          <div class="box-title">
            <s-header-title
              title="数字经济"
              htype="1"
              :data-time="nowTime"
              :click-flag="true"
              onclick="top.commonObj.openMenuFun('szjj-sy')"
            ></s-header-title>
          </div>
          <div class="box-con box3-content">
            <div class="box3-con-left">
              <p>{{szjjTitle1}}</p>
              <dv-border-box-8 style="width:566px;height:130px;">
                <div class="box3-left-item" style="margin-bottom: 50px">
                  <div class="line-css">
                    <img
                      src="/static/citybrain/csdn/img/ywt4-left/szjj-left-img1.png"
                      alt=""
                    />
                    <div>
                      <p>{{szjjLeftData[0].ymbq}}</p>
                      <p class="yel-color">
                        {{szjjLeftData[0].value}}{{szjjLeftData[0].unit}}
                      </p>
                    </div>
                  </div>
                  <div class="line-css">
                    <div>
                      <p>{{szjjLeftData[1].ymbq}}</p>
                      <p class="green-color">
                        {{szjjLeftData[1].value}}{{szjjLeftData[1].unit}}
                        <span></span>
                      </p>
                    </div>
                  </div>
                  <div>
                    <div>
                      <p>{{szjjLeftData[2].ymbq}}</p>
                      <p class="yel-color">{{szjjLeftData[2].value}}</p>
                    </div>
                  </div>
                </div>
              </dv-border-box-8>
              <p>{{szjjTitle2}}</p>
              <dv-border-box-8 style="width:566px;height:130px;">
                <div class="box3-left-item">
                  <div class="line-css">
                    <img
                      src="/static/citybrain/csdn/img/ywt4-left/szjj-left-img2.png"
                      alt=""
                    />
                    <div>
                      <p>{{szjjLeftData[3].ymbq}}</p>
                      <p class="yel-color">
                        {{szjjLeftData[3].value}}{{szjjLeftData[3].unit}}
                      </p>
                    </div>
                  </div>
                  <div class="line-css">
                    <div>
                      <p>{{szjjLeftData[4].ymbq}}</p>
                      <p class="green-color">
                        {{szjjLeftData[4].value}}{{szjjLeftData[4].unit}}<span
                        ></span>
                      </p>
                    </div>
                  </div>
                  <div>
                    <div>
                      <p>{{szjjLeftData[5].ymbq}}</p>
                      <p class="yel-color">{{szjjLeftData[5].value}}</p>
                    </div>
                  </div>
                </div>
              </dv-border-box-8>
              
            </div>
            <div class="box3-con-right">
              <div class="box3-con-top">
                <div
                  :class="[szjjTabNum==index?'tab-acitve':'','b3-tab'] "
                  v-for="(item,index) in sjzjTabData"
                  @click="szjjRightTab(item,index)"
                >
                  {{item.name}}
                </div>
              </div>
              <div class="box3-con-bottom" style="position: relative">
                <div v-if="szjjTabNum==3" class="top-title">
                  <span>{{szjjRigthData4Top.title}}</span>
                  <span v-for="(item,index) in szjjRigthData4Top.detail" v-if="index==0" style="margin:0 40px;">{{item.value}}{{item.unit}}</span>
                  <span v-for="(item,index) in szjjRigthData4Top.detail" v-if="index==1" >{{item.label}}{{item.value}}{{item.unit}}</span>
                </div>

                <img
                  v-if="listsLen>showListNum"
                  src="/static/citybrain/csdn/img/ywt/swiper-left.png"
                  alt=""
                  width="82"
                  height="113"
                  class="btn-left mouse-pointer"
                  @click="to_prev"
                />
                <el-carousel
                  ref="part1_bottom"
                  arrow="never"
                  :autoplay="false"
                  indicator-position="outside"
                  style="height: 400px; overflow: hidden"
                >
                  <el-carousel-item
                    v-for="(item,i) in list"
                    :key="i"
                    style="
                      height: 400px;
                      display: flex;
                      justify-content: space-between;
                    "
                  >
                  <!-- 调用数据 -->                  
                  <div
                  v-if="szjjTabNum!=3 "
                  :class="[szjjTabNum==1?'box3-bottom-item-2':'','box3-bottom-item']"
                  v-for="(item,index) in list[i]"
                  :key="index"
                >
                  <p class="b3-b-title" >{{item.title}}</p>
                  <div class="b3-b-con">                    
                      <div v-for="ele in item.detail" style="padding: 0;margin: 0;">
                        <p
                        v-if="ele.showtype==2&&ele.label.indexOf('产能')>-1"
                        class="green-color">
                      {{ele.label}} {{ele.value}}{{ele.unit}}
                        <span class="szjj-arrow"></span>
                      </p>
                      <p v-if="ele.showtype==5" class="red-color">
                        {{ele.label}} {{ele.value}}{{ele.unit}}
                        <span class="szjj-arrow"></span>
                      </p>
                      <p v-if="ele.showtype==1">{{ele.label}}</p>
                      <p v-if="ele.showtype==1">
                        <s-num
                          :value="ele.value"
                          color="lg-yellow"
                          :unit="ele.unit"
                          unit-color="lg-yellow"
                        />
                      </p>
                      <p v-if="ele.showtype==2&&ele.label.indexOf('产能')==-1" class="green-color">
                        {{ele.label}}{{ele.value}}{{ele.unit}}
                        <span class="szjj-arrow"></span>
                      </p>
                      <p class="pm-css" v-if="ele.showtype==3">
                        <span>{{ele.label}}</span>
                        <span>{{ele.value}}</span>
                        <span>{{ele.unit}}</span>
                      </p>
                      </div>
                  </div>
                </div>
                    <!-- 数字化改造成效良好 -->
                    <div
                      v-if="szjjTabNum==3"
                      style="height: 290px;width: 431px;"
                      class="box3-bottom-item"
                      v-for="(item,index) in list[i]"
                      :key="index"
                    >
                    <p class="b3-b-title" v-if="item.title">{{item.title}}</p>
                    <div class="b3-b-con" style="height: 100%;">                    
                        <div v-for="ele in item.detail" style="padding: 0;margin: 0;">
                          <p
                          v-if="ele.showtype==2&&ele.label.indexOf('产能')>-1"
                          class="green-color">
                        {{ele.label}} {{ele.value}}{{ele.unit}}
                          <span class="szjj-arrow"></span>
                        </p>
                        <p v-if="ele.showtype==5" class="red-color">
                          {{ele.label}} {{ele.value}}{{ele.unit}}
                          <span class="szjj-arrow"></span>
                        </p>
                        <p v-if="ele.showtype==1">
                          <s-num
                            :value="ele.value"
                            color="lg-yellow"
                            :unit="ele.unit"
                            unit-color="lg-yellow"
                          />
                        </p>
                        <p v-if="ele.showtype==2&&ele.label.indexOf('产能')==-1" class="green-color">
                          {{ele.label}}{{ele.value}}{{ele.unit}}
                          <span class="szjj-arrow"></span>
                        </p>
                        <p class="pm-css" v-if="ele.showtype==3">
                          <span>{{ele.label}}</span>
                          <span>{{ele.value}}</span>
                          <span>{{ele.unit}}</span>
                        </p>
                        </div>
                    </div>
                    </div>
                  </el-carousel-item>
                </el-carousel>
                <img
                  v-if="listsLen>showListNum"
                  src="/static/citybrain/csdn/img/ywt/swiper-right.png"
                  alt=""
                  width="82"
                  height="113"
                  class="btn-right mouse-pointer"
                  @click="to_next"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
      var vm = new Vue({
        el: '#ywt4-left',
        data: {
          nowTime: '',
          szjjTabNum: 0,
          lists: [],
          listsLen: '',
          showListNum: 3,
          // 党建统领
          djtlData: [],
          // 数字政府左边
          szzfLeftData: [
            {
              name: '地区生产总值',
              value: '1288.47',
              unit: '亿元',
            },
            {
              name: '金融机构存款余量',
              value: '13169.32',
              unit: '亿元',
            },
            {
              name: '限额以上社会销售品零售额',
              value: '2521214',
              unit: '万元',
            },
            {
              name: '规模以上工业增加值',
              value: '4238818',
              unit: '万元',
            },
            {
              name: '固定资产投资占比',
              value: '14.9',
              unit: '%',
            },
            {
              name: '金融机构贷款余额',
              value: '12598.9',
              unit: '亿元',
            },
          ],
          // 数字政府右边
          szzfRightData: [
            {
              name: '整体智治',
            },
            {
              name: '城镇化',
            },
            {
              name: '营商环境',
            },
            {
              name: '现代乡村',
            },
            {
              name: '科技创新',
            },
          ],
          // 数字经济右边页面
          szjjTitle1: '',
          szjjTitle2: '',
          szjjLeftData: [
            {
              unit: '亿元',
              mkmc: '产业大脑-2021年GDP',
              ymbq: '总量',
              value: '5355',
            },
            {
              unit: '%',
              mkmc: '产业大脑-2021年GDP',
              ymbq: '增速',
              value: '9.8',
            },
            {
              unit: '',
              mkmc: '产业大脑-2021年GDP',
              ymbq: '排名',
              value: '1',
            },
            {
              unit: '亿元',
              mkmc: '产业大脑-数字经济核心产业增加值',
              ymbq: '总量',
              value: '377.87',
            },
            {
              unit: '%',
              mkmc: '产业大脑-数字经济核心产业增加值',
              ymbq: '增速',
              value: '23.2',
            },
            {
              unit: '',
              mkmc: '产业大脑-数字经济核心产业增加值',
              ymbq: '排名',
              value: '1',
            },
          ],
          // 数字经济的tab切换
          sjzjTabData: [
            {
              name: '三次产业基础稳固',
            },
            {
              name: '数字化改造成效良好',
            },
            {
              name: '电子商务引领全国',
            },
            {
              name: '民营经济活力迸发',
            },
          ],
          // 数字经济右边的数据
          szjjRightData: [],
          szjjRightData1: [],
          szjjRightData2: [],
          szjjRightData3: [],
          szjjRightData4: [],
          szjjRigthData4Top:[]
        },
        computed: {
          list() {
            let newArr = []
            this.listsLen = this.szjjRightData.length
            for (
              let i = 0;
              i < this.szjjRightData.length;
              i += this.showListNum
            ) {
              newArr.push(this.szjjRightData.slice(i, i + this.showListNum))
            }
            return newArr
          },
        },
        mounted() {
          this.getTime()
          this.initFun()
        },
        methods: {
          getTime() {
            let data = new Date()
            let yesterday = new Date(data.setDate(data.getDate() - 1))
            this.nowTime =
              yesterday.getFullYear() +
              '年' +
              (yesterday.getMonth() + 1) +
              '月' +
              yesterday.getDate() +
              '日'
          },
          initFun() {
            let that = this
            $api('sy0830_left11').then((res) => {
              let arr = []
              for (let item of res) {
                item.name.indexOf('党员总数') > -1
                  ? (arr[0] = item)
                  : item.name.indexOf('女') > -1
                  ? (arr[1] = item)
                  : item.name.indexOf('少数民族') > -1
                  ? (arr[2] = item)
                  : item.name.indexOf('大专') > -1
                  ? (arr[3] = item)
                  : item.name.indexOf('基层党组织') > -1
                  ? (arr[4] = item)
                  : item.name.indexOf('基层党委') > -1
                  ? (arr[5] = item)
                  : item.name.indexOf('总支部') > -1
                  ? (arr[6] = item)
                  : item.name.indexOf('支部') > -1
                  ? (arr[7] = item)
                  : ''
              }
              this.djtlData = arr
              let dySun = +arr[0].value
              let jcdwData = +arr[1].value
              let ssmzData = +arr[2].value
              let dzysData = +arr[3].value
              this.$nextTick(() => {
                that.jdtEcharts('echarts1', dySun, jcdwData)
                that.jdtEcharts('echarts2', dySun, ssmzData)
                that.jdtEcharts('echarts3', dySun, dzysData)
              })
            })
            $api('szjj_firstpage_left1').then((res) => {
              this.szjjTitle1 = res[0].mkmc.split('-')[1]
              this.szjjTitle2 = res[3].mkmc.split('-')[1]
              this.szjjLeftData = res
            })
            $api("sy0830_left32",{code:1}).then(res=>{
                this.szjjRightData1=this.szjjRightData=res.sort((a, b) => {
                  return a.oid > b.oid ? 1 : -1;
                  })
            })
            $api("sy0830_left32",{code:2}).then(res=>{
                this.szjjRightData2=res.sort((a, b) => {
                  return a.oid > b.oid ? 1 : -1;
                  })
            })
            $api("sy0830_left32",{code:3}).then(res=>{
                this.szjjRightData3=res.sort((a, b) => {
                  return a.oid > b.oid ? 1 : -1;
                  })
            })
            $api("sy0830_left32",{code:4}).then(res=>{
                let arr=res.sort((a, b) => {
                  return a.oid > b.oid ? 1 : -1;
                })
                this.szjjRightData4=arr.slice(1,)
                this.szjjRigthData4Top=arr[0]
                console.log("this.szjjRightData4==>",this.szjjRigthData4Top);
            })
          },

          to_prev() {
            this.$refs.part1_bottom.prev()
          },
          to_next() {
            this.$refs.part1_bottom.next()
          },

          szjjRightTab(item, index) {
            this.szjjTabNum = index
            this.showListNum = index == 1 ? 4 : 3
            let name = 'szjjRightData' + (index + 1)
            this.szjjRightData = this[name]
          },
          jdtEcharts(dom, sum, data) {
            let echartsDom = echarts.init(document.getElementById(dom))

            let bfbData = Math.ceil((data / sum) * 100)

            let option = {
              grid: {
                top: 0,
                bottom: 0,
                left: '0',
                right: '35%',
              },
              xAxis: {
                show: false,
                type: 'value',
                boundaryGap: [0, 0],
              },
              yAxis: [
                {
                  type: 'category',
                  data: [''],
                  axisLine: { show: false },
                  axisTick: [
                    {
                      show: false,
                    },
                  ],
                },
              ],
              series: [
                {
                  name: '金额',
                  type: 'bar',
                  zlevel: 1,
                  itemStyle: {
                    normal: {
                      barBorderRadius: 30,
                      color: new echarts.graphic.LinearGradient(1, 0, 0, 1, [
                        {
                          offset: 1,
                          color: '#2D8DED',
                        },
                        {
                          offset: 0,
                          color: '#5ED8FB',
                        },
                      ]),
                    },
                  },
                  barWidth: 10,
                  data: [bfbData],
                },
                {
                  name: '背景',
                  type: 'bar',
                  barWidth: 10,
                  barGap: '-100%',
                  data: [sum],
                  label: {
                    normal: {
                      show: true,
                      position: 'right',
                      color: '#00C0FF',
                      fontSize: 28,
                      offset: [0, 5],
                      formatter: function (param) {
                        return bfbData + '%'
                      },
                    },
                  },
                  itemStyle: {
                    normal: {
                      color: 'rgba(28, 128, 213, 0.19)',
                      barBorderRadius: 30,
                    },
                  },
                },
              ],
            }

            echartsDom.setOption(option)
            echartsDom.getZr().on('mousemove', (param) => {
              echartsDom.getZr().setCursorStyle('default')
            })
          },
        },
        beforeDestory() {},
      })
    </script>
  </body>
</html>
