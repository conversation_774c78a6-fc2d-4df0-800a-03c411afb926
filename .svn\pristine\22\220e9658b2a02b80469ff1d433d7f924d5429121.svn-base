<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>数字法治指标分析</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <style>
            * {
                padding: 0;
                margin: 0;
            }
            #szfzzbfx-right {
                width: 1050px;
                height: 1930px;
                background: url("/img/right-bg.png") no-repeat;
                background-size: 100% 100%;
            }

            .el-input__inner {
                width: 150px;
                font-size: 30px;
                background-color: #2e405a;
                color: #fff;
            }
            .el-scrollbar__wrap {
                background-color: #2e405a;
            }
            .el-select-dropdown__item.hover,
            .el-select-dropdown__item:hover {
                background-color: #2e405a;
            }
            .select {
                float: right;
                right: 50px;
                z-index: 9;
            }
            .el-select-dropdown__item {
                font-size: 30px;
                color: #fff;
            }
        </style>
    </head>

    <body>
        <div id="szfzzbfx-right">
            <div class="content">
                <div class="title">
                    <nav style="padding: 20px 45px">
                        <s-header-title-2
                            style="width: 100%"
                            title="社会矛盾纠纷调处化解情况"
                            htype="2"
                        ></s-header-title-2>
                    </nav>
                </div>

                <div id="barEcharts01" style="height: 530px; width: 100%"></div>

                <div class="title">
                    <nav style="padding: 20px 45px">
                        <s-header-title-2 style="width: 100%" title="平安金华建设情况分析" htype="2"></s-header-title-2>
                    </nav>
                </div>

                <el-select class="select" v-model="value" placeholder="请选择" @change="change">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <div id="barEcharts02" style="height: 530px"></div>
                <div id="lineEcharts01" style="height: 530px"></div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#szfzzbfx-right",
        data: {
            sjcm: [],
            xzcf: [],
            options: [
                {
                    value: "7月",
                    label: "7月",
                },
                {
                    value: "8月",
                    label: "8月",
                },
                {
                    value: "9月",
                    label: "9月",
                },
                {
                    value: "10月",
                    label: "10月",
                },
            ],
            value: "7月",
            infoList: [],
            barName:"",
            info: [
            {
              name: "金东区煤气泄漏",
              type: "安全风险",
              main: "2022年9月10日金东区阳光路20号发生煤气泄漏",
            },
            {
              name: "兰溪市煤气泄漏",
              type: "安全风险",
              main: "2022年9月10日兰溪市阳光路20号发生煤气泄漏",
            },
            {
              name: "婺城区煤气泄漏",
              type: "安全风险",
              main: "2022年9月10日婺城区阳光路20号发生煤气泄漏",
            },
            {
              name: "武义县煤气泄漏",
              type: "安全风险",
              main: "2022年9月10日武义县阳光路20号发生煤气泄漏",
            },
            {
              name: "浦江县煤气泄漏",
              type: "安全风险",
              main: "2022年9月10日浦江县阳光路20号发生煤气泄漏",
            },
            {
              name: "磐安县煤气泄漏",
              type: "安全风险",
              main: "2022年9月10日磐安县阳光路20号发生煤气泄漏",
            },
            {
              name: "义乌市煤气泄漏",
              type: "安全风险",
              main: "2022年9月10日义乌市阳光路20号发生煤气泄漏",
            },
            {
              name: "东阳市煤气泄漏",
              type: "安全风险",
              main: "2022年9月10日东阳市阳光路20号发生煤气泄漏",
            },
            {
              name: "永康市煤气泄漏",
              type: "安全风险",
              main: "2022年9月10日永康市阳光路20号发生煤气泄漏",
            },
          ],
        },
        mounted() {
            this.initFun();
        },
        methods: {
            initFun() {
                $api("ldst_szhgg_szfzzbfx", { type: 10 }).then((res) => {
                    this.getEcharts01(res);
                });
                $api("ldst_szhgg_szfzzbfx", { type: 11 }).then((res) => {
                    this.getEcharts02(res);
                });
                $api("ldst_szhgg_szfzzbfx", { type: 12 }).then((res) => {
                    this.getEcharts03(res);
                });
            },
            change(item) {
                if (item === "7月") {
                    $api("ldst_szhgg_szfzzbfx", { type: 11 }).then((res) => {
                        this.getEcharts02(res);
                    });
                } else if (item === "8月") {
                    $api("ldst_szhgg_szfzzbfx", { type: "11-1" }).then((res) => {
                        this.getEcharts02(res);
                    });
                } else if (item === "9月") {
                    $api("ldst_szhgg_szfzzbfx", { type: "11-2" }).then((res) => {
                        this.getEcharts02(res);
                    });
                } else if (item === "10月") {
                    $api("ldst_szhgg_szfzzbfx", { type: "11-3" }).then((res) => {
                        this.getEcharts02(res);
                    });
                }
            },
            getEcharts01(res) {
                let myCharts = echarts.init(document.getElementById("barEcharts01"));
                let xData = res.map((item) => item.name);
                let value = res.map((item) => item.value);
                let value1 = res.map((item) => item.value1);
                let value2 = res.map((item) => item.value2);
                let option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    legend: {
                        icon: "rectangle",
                        textStyle: {
                            color: "#ffffff",
                            fontSize: 30,
                        },
                    },
                    xAxis: {
                        data: xData,
                        axisLine: {
                            show: true, //隐藏X轴轴线
                            lineStyle: {
                                color: "#2e415e",
                            },
                        },
                        axisTick: {
                            show: false, //隐藏X轴刻度
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                fontSize: 30,
                                color: "#fff", //X轴文字颜色
                            },
                        },
                    },
                    yAxis: [
                        {
                            type: "value",
                            name: "单位：起",
                            nameTextStyle: {
                                color: "#fff",
                                fontSize: 30,
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: true,
                                lineStyle: {
                                    color: "#2e415e",
                                },
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 30,
                                },
                            },
                        },
                        {
                            type: "value",

                            position: "right",
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                            },
                            axisLabel: {
                                show: true,
                                formatter: "{value}%", //右侧Y轴文字显示
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 30,
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "调处化解率",
                            type: "line",
                            yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                            smooth: true, //平滑曲线显示
                            showAllSymbol: true, //显示所有图形。
                            symbol: "circle", //标记的图形为实心圆
                            symbolSize: 10, //标记的大小
                            itemStyle: {
                                //折线拐点标志的样式
                                color: "#058cff",
                            },
                            lineStyle: {
                                color: "#058cff",
                            },
                            areaStyle: {
                                color: "rgba(5,140,255, 0.2)",
                            },
                            data: value2,
                        },
                        {
                            name: "调处化解事件数",
                            type: "bar",
                            barWidth: 30,
                            color: "#54c7ea",
                            itemStyle: {
                                borderRadius: [15, 15, 0, 0],
                            },
                            data: value,
                        },
                        {
                            name: "新增事件数",
                            itemStyle: {
                                borderRadius: [15, 15, 0, 0],
                            },
                            type: "bar",
                            barWidth: 30,
                            color: "#8080ff",
                            data: value1,
                        },
                    ],
                };

                myCharts.setOption(option);
                tools.loopShowTooltip(myCharts, option, { loopSeries: true });
            },
            getEcharts02(res) {
                let myCharts = echarts.init(document.getElementById("barEcharts02"));
                let xData = res.map((item) => item.name);
                let value = res.map((item) => item.value);
                let value1 = res.map((item) => item.value1);

                let option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    legend: {
                        icon: "rectangle",
                        textStyle: {
                            color: "#ffffff",
                            fontSize: 30,
                        },
                    },
                    xAxis: {
                        data: xData,
                        axisLine: {
                            show: true, //隐藏X轴轴线
                            lineStyle: {
                                color: "#2e415e",
                            },
                        },
                        axisTick: {
                            show: false, //隐藏X轴刻度
                        },
                        axisLabel: {
                            show: true,
                            textStyle: {
                                fontSize: 30,
                                color: "#fff", //X轴文字颜色
                            },
                        },
                    },
                    yAxis: [
                        {
                            type: "value",
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: true,
                                lineStyle: {
                                    color: "#2e415e",
                                },
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 30,
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "基层治理",
                            type: "bar",
                            barWidth: 30,
                            itemStyle: {
                                borderRadius: [15, 15, 0, 0],
                            },
                            color: "#2391ff",
                            data: value,
                        },
                        {
                            name: "矛盾调处",
                            type: "bar",
                            barWidth: 30,
                            itemStyle: {
                                borderRadius: [15, 15, 0, 0],
                            },
                            color: "#ffc328",
                            data: value1,
                        },
                    ],
                };

                myCharts.setOption(option);
                tools.loopShowTooltip(myCharts, option, { loopSeries: true });
                myCharts.getZr().on("click", (params) => {
                    let pointInPixel = [params.offsetX, params.offsetY];
                    if (myCharts.containPixel("grid", pointInPixel)) {
                        //点击第几个柱子
                        let pointInGrid = myCharts.convertFromPixel(
                            { seriesIndex: 0 },
                            pointInPixel
                        );
                        // console.log(pointInGrid)
                        // 也可以通过params.offsetY 来判断鼠标点击的位置是否是图表展示区里面的位置
                        // 也可以通过name[xIndex] != undefined，name是x轴的坐标名称来判断是否还是点击的图表里面的内容
                        // x轴数据的索引
                        let xIndex = pointInGrid[0];
                        // y轴数据的索引
                        let yIndex = pointInGrid[1];
                        let arr = this.info[xIndex];
                        this.infoList = [];
                        this.infoList.push(arr);
                        if(this.barName==xData[xIndex]){
                            this.barName=""
                            top.document.getElementById("map").contentWindow.Work.funChange(
                                JSON.stringify({
                                funcName: "rmPop",
                                })
                            );
                        }else{
                            this.popFun(xData[xIndex]);
                        }
                        
                    }
                });
            },
            // 加载弹窗
            async popFun(name) {
                this.barName=name
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "rmPop",
                    })
                );

                var textData = await $get("personInTime");
                let maxValueArr = [];
                let maxValue = "";
                textData.map((item) => {
                // 所有值中的最大值
                let a = +item.value[0];
                let b = +item.value[1];
                maxValueArr.push(a);
                maxValueArr.push(b);
                });
                maxValue = Math.max(...maxValueArr);
                let arr = textData.filter(
                (v) => v.name == (name == "金东区" ? "金义新区" : name)
                );
                for (let i = 0; i < arr.length; i++) {
                let a1 = parseInt(Number(arr[i].value[0]));
                let a2 = parseInt(Number(arr[i].value[1]));
                let a1_res = (a1 / maxValue).toFixed(2);
                let a2_res = (a2 / maxValue).toFixed(2);
                
                const url = `${baseURL.url}/static/citybrain/hjbh/img/rkzt/rkpc_bg.png`;
                let objData = {
                    funcName: "customPop",
                    coordinates: arr[i].pos,
                    closeButton: false,
                    html: `
                            <div style="min-width: 440px;overflow: hidden; height: 300px; position: relative;background: url('${url}') no-repeat;background-size: 100% 100%;">
                                <p style="position: absolute;
                                right: 45px;top: 30px;
                font-size: 28px;
                background-image: linear-gradient(180deg, #f7ad47, #fff, #ffd8a1);
                background-clip: text;
                -webkit-background-clip: text;
                color: transparent;
                font-weight: bold;
                font-style: italic;
                width: 160px;
                height: 30px;
                line-height: 30px;
                text-align: center;">${
                    arr[i].name == "金东区" ? "金义新区" : arr[i].name
                }</p>
                                <div style="width: 345px;overflow: hidden;
                position: absolute;
                top: 79px;
                left: 24px;
                height: 180px;
                padding: 0 20px;
                color: #fff;">
                                    <div style="font-size: 20px;height:33px">
                                        <span>基层治理：</span>
                                        <span style=" font-size: 20px;
                font-weight: 600;
                background-image: linear-gradient(180deg, #f7ad47, #fff, #ffd8a1);
                background-clip: text;
                -webkit-background-clip: text;
                color: transparent;">${arr[i].value[0]}人</span>
                                    </div>
                                    <div style="width: 100%;
                height: 8px;
                background-color: #144363;
                position: relative;
                border-radius: 12px;
                margin-bottom: 15px;">
                                        <div style="height: 8px !important;
                position: absolute;
                top: -1px;
                left: 0;
                border-radius: 12px;
                z-index: 100;background-image: linear-gradient(10deg, #ff4c4c, #ffa1a1);
                width:${
                    // a1 > 310 ? '100%' : a1 + 'px'
                    // a1_res * 310
                    a1_res * 100
                }%
                "></div>
                                    </div>
                                    <div style="font-size: 20px;height:33px">
                                        <span>矛盾调处：</span>
                                        <span style=" font-size: 20px;
                font-weight: 600;
                background-image: linear-gradient(180deg, #f7ad47, #fff, #ffd8a1);
                background-clip: text;
                -webkit-background-clip: text;
                color: transparent;">${arr[i].value[1]}人</span>
                                    </div>
                                    <div style="width: 100%;
                height: 8px;
                background-color: #144363;
                position: relative;
                border-radius: 12px;
                margin-bottom: 15px;">
                                        <div style="height: 8px !important;
                position: absolute;
                top: -1px;
                left: 0;
                border-radius: 12px;
                z-index: 100;background-image: linear-gradient(10deg, #e9a53a, #e7aba3);width: ${
                    // a2 > 310 ? '100%' : a2 + 'px'
                    a2_res * 100
                }%"></div>
                                    </div>
                                    
                                    <div style="font-size: 20px;height:33px;display: flex;padding-top:12px;">                                       
                                        <span style=" font-size: 20px;white-space: nowrap;
                font-weight: 600;
                color:#00C0FF">分析:较上月同期下降2%</span>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        `,
                };
                window.parent.document
                    .getElementById("map")
                    .contentWindow.Work.funChange(JSON.stringify(objData));
                }
            },
            getEcharts03(res) {
                let myCharts = echarts.init(document.getElementById("lineEcharts01"));
                let yData = res.map((item) => {
                    return item.name;
                });
                let value = res.map((item) => {
                    return item.value;
                });

                let option = {
                    legend: {
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    tooltip: {
                        trigger: "item",

                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    grid: {
                        bottom: "20%",
                        left: "12%",
                        right: "12%",
                    },
                    yAxis: [
                        {
                            type: "value",

                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                    ],

                    xAxis: {
                        type: "category",
                        data: yData,
                        offset: 15,
                        axisLabel: {
                            fontSize: 30,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            smooth: true, //平滑曲线显示
                            showAllSymbol: true, //显示所有图形。
                            symbolSize: 10, //标记的大小

                            type: "line",
                            data: value,
                            color: "#c6e086",
                        },
                    ],
                };
                myCharts.setOption(option);
                tools.loopShowTooltip(myCharts, option, { loopSeries: true });
            },
        },
    });
</script>
