[v-cloak] {
    display: none;
  }
  html,body,ul,p{
    padding:0;
    margin:0;
    list-style: none;
  }
  .container{
    width:1050px;
    height:1930px;
    background: url("/img/left-bg.png") no-repeat;
    background-size: 100% 100%;
    border-radius: 10px;
    padding: 30px 40px;
    box-sizing: border-box;
    position: relative;
  }
  .btn{
    width: 100px;
    height: 45px;
    background-color: #5EB0F5;
    color: #fff;
    font-size: 28px;
    text-align: center;
    line-height: 45px;
    border-radius: 10px;
    position: absolute;
    right: 40px;
  }
  #jgzf-chart{
    width: 100%;
    height: 23%;

  }
  #sq-chart{
    width: 100%;
    height: 10%;
  }
  
  #scjg-chart{
    width: 100%;
    height: 23%;
   
  }
  .table-con{
    width: 100%;
    height: 570px;
    margin-bottom: 20px;
    overflow-y: scroll;
  }
  .table-con li{
    width: 100%;
    height: 80px;
    padding:20px 40px;
    box-sizing: border-box;
    display: flex;
    font-size: 28px;
    color: #fff;
    border-bottom: 1px dashed #ccc;
  }
  .table-con:hover {
    overflow-y: auto;
  }
  
  .table-con::-webkit-scrollbar {
    width: 4px;
    /*滚动条整体样式*/
    height: 4px;
    /*高宽分别对应横竖滚动条的尺寸*/
  }
  
  .table-con::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #20aeff;
    height: 8px;
  }


.top-con{
    width: 75%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    margin: 15px auto;
}
.top-con li{
    /* width: 20%; */
    color: #fff;
    height: 100%;
    font-size: 28px;
    text-align: center;
    line-height: 45px;
    cursor: pointer;
}

.active{
    border-bottom: 4px solid #1989fa!important;
    color: #1989FA!important;
   
}




  /* 表格 */
.table {
    width: 100%;
    height: 280px;
    padding: 10px;
    box-sizing: border-box;
    overflow-y: auto;
  }
  
  .table .th {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-style: italic;
    font-weight: 700;
    font-size:28px;
    line-height: 60px;
    background: #00396f;
    color: #FFFFFF;
  }
  
  .table .th_td {
    letter-spacing: 0px;
    text-align: center;
    flex: 0.25;
  }
  
  .table .tbody {
    width: 100%;
    height: calc(100% - 80px);
    overflow: hidden;
  }
  
  .table .tbody:hover {
    overflow-y: auto;
  }
  
  .table .tbody::-webkit-scrollbar {
    width: 4px;
    /*滚动条整体样式*/
    height: 4px;
    /*高宽分别对应横竖滚动条的尺寸*/
  }
  
  .table .tbody::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #20aeff;
    height: 8px;
  }
  .table .tr:nth-child(2n) {
    background: rgb(0 57 111 / 30%);
  }

  .table .tr:nth-child(2n+1) {
    /* background: #035b86; */
  }

  .table .tr {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;
    line-height: 60px;
    font-size: 28px;
    color: #FFFFFF;
    cursor: pointer;
  }
  
  /* .table .tr:nth-child(2n) {
    background: #00396f;
  }
  
  .table .tr:nth-child(2n+1) {
    background: #035b86;
  }
   */
  .table .tr:hover {
    background-color: #6990b6;
  }
  
  .table .tr_td {
    letter-spacing: 0px;
    text-align: center;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
 