<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>数字法治指标分析</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <style>
            * {
                padding: 0;
                margin: 0;
            }

            #szfzzbfx-middle {
                width: 1700px;
                height: 500px;
                background-color: #0a2443;
                padding: 0px 40px;
                box-sizing: border-box;
            }

            .value {
                color: #0087ec;
                font-size: 40px;
            }

            .jdt {
                font-size: 30px;
                color: #fff;
            }

            .image {
                width: 150px;
                height: 150px;
            }

            .xzcf {
                font-size: 30px;
                margin-top: 80px;
                color: #fff;
                text-align: center;
            }

            .el-input__inner {
                width: 150px;
                font-size: 30px;
                background-color: #2e405a;
                color: #fff;
            }

            .el-scrollbar__wrap {
                background-color: #2e405a;
            }

            .el-select-dropdown__item.hover,
            .el-select-dropdown__item:hover {
                background-color: #2e405a;
            }

            .select {
                float: right;
                right: 256px;
                z-index: 9;
                top: -57px;
            }

            .el-select-dropdown__item {
                font-size: 30px;
                color: #fff;
            }

            .el-progress-bar__innerText {
                font-size: 25px;
                margin-bottom: 9px;
            }

            .pm {
                font-size: 28px;
                color: #ffff;
                background-color: #3d8cdd;
                width: 240px;
                height: 65px;
                text-align: center;
                line-height: 70px;
                position: absolute;
                top: 17px;
                right: 37px;
            }
        </style>
    </head>

    <body>
        <div id="szfzzbfx-middle">
            <div class="content">
                <nav style="padding: 20px 45px 0">
                    <s-header-title-2
                        style="width: 100%"
                        title="数字法治多跨场景建设推进情况"
                        htype="1"
                    ></s-header-title-2>
                </nav>
                <div class="pm">全省排名:第二名</div>
                <div style="display: flex">
                    <div style="flex: 1; display: flex">
                        <div style="width: 50%">
                            <div class="jdt" v-for="item in jdt">
                                <div>{{item.name}}</div>
                                <el-progress
                                    :text-inside="true"
                                    :stroke-width="24"
                                    :percentage="Number(item.value)"
                                    status="success"
                                ></el-progress>
                            </div>
                        </div>
                        <div id="pieEcharts01" style="height: 350px; width: 100%"></div>
                    </div>
                    <div style="flex: 1; position: relative">
                        <el-select class="select" v-model="value" placeholder="请选择" @change="change">
                            <el-option
                                v-for="item in options"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                        <div id="barEcharts02" style="height: 380px"></div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#szfzzbfx-middle",
        data: {
            jdt: [],

            options: [
                {
                    value: "7月",
                    label: "7月",
                },
                {
                    value: "8月",
                    label: "8月",
                },
                {
                    value: "9月",
                    label: "9月",
                },
                {
                    value: "10月",
                    label: "10月",
                },
            ],
            value: "7月",
        },
        mounted() {
            this.initFun();
        },
        methods: {
            initFun() {
                $api("ldst_szhgg_szfzzbfx", { type: 7 }).then((res) => {
                    this.jdt = res;
                });
                $api("ldst_szhgg_szfzzbfx", { type: 8 }).then((res) => {
                    this.getEcharts01(res);
                });
                $api("ldst_szhgg_szfzzbfx", { type: 9 }).then((res) => {
                    this.getEcharts02(res);
                });
            },
            change(item) {
                if (item === "7月") {
                    $api("ldst_szhgg_szfzzbfx", { type: 9 }).then((res) => {
                        this.getEcharts02(res);
                    });
                } else if (item === "8月") {
                    $api("ldst_szhgg_szfzzbfx", { type: "9-1" }).then((res) => {
                        this.getEcharts02(res);
                    });
                } else if (item === "9月") {
                    $api("ldst_szhgg_szfzzbfx", { type: "9-2" }).then((res) => {
                        this.getEcharts02(res);
                    });
                } else if (item === "10月") {
                    $api("ldst_szhgg_szfzzbfx", { type: "9-3" }).then((res) => {
                        this.getEcharts02(res);
                    });
                }
            },
            getEcharts01(res) {
                let myCharts = echarts.init(document.getElementById("pieEcharts01"));
                let option = {
                    tooltip: {
                        trigger: "item",
                        formatter: "{b}\n{c}%",
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    legend: {
                        orient: "vertical",
                        right: "right",
                        top: 70,
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    series: [
                        {
                            type: "pie",
                            center: ["25%", "50%"],
                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderRadius: 10,
                                borderColor: "#2b516f",
                                borderWidth: 2,
                            },
                            label: {
                                show: false,
                            },

                            data: res,
                        },
                    ],
                };
                myCharts.setOption(option);
                tools.loopShowTooltip(myCharts, option, { loopSeries: true });
            },

            getEcharts02(res) {
                let myCharts = echarts.init(document.getElementById("barEcharts02"));
                let yData = res.map((item) => {
                    return item.name;
                });
                let value = res.map((item) => {
                    return item.value;
                });
                let value1 = res.map((item) => {
                    return item.value1;
                });
                let value2 = res.map((item) => {
                    return item.value2;
                });

                let option = {
                    legend: {
                        left: 80,
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    grid: {
                        top: "20%",
                        bottom: "15%",
                        left: "12%",
                        right: "5%",
                    },
                    yAxis: [
                        {
                            type: "value",
                            name: "",
                            nameTextStyle: {
                                color: "#fff",
                                fontSize: 30,
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: true,
                                lineStyle: {
                                    color: "#2e415e",
                                },
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 30,
                                },
                            },
                        },
                        {
                            type: "value",

                            position: "right",
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                            },
                            axisLabel: {
                                show: true,
                                formatter: "{value}%", //右侧Y轴文字显示
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 30,
                                },
                            },
                        },
                    ],
                    xAxis: {
                        type: "category",
                        data: yData,
                        offset: 15,
                        axisLabel: {
                            fontSize: 30,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            name: "全省最佳应用",
                            color: "#2391ff",
                            type: "bar",
                            itemStyle: {
                                borderRadius: [15, 15, 0, 0],
                            },
                            data: value,
                        },
                        {
                            name: "全市最佳应用",
                            color: "#ffc328",
                            type: "bar",
                            itemStyle: {
                                borderRadius: [15, 15, 0, 0],
                            },
                            data: value1,
                        },
                        {
                            name: "理论和制度成果",
                            type: "line",
                            yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                            smooth: true, //平滑曲线显示
                            showAllSymbol: true, //显示所有图形。
                            symbol: "circle", //标记的图形为实心圆
                            symbolSize: 10, //标记的大小
                            itemStyle: {
                                //折线拐点标志的样式
                                color: "#058cff",
                            },
                            lineStyle: {
                                color: "#058cff",
                            },
                            areaStyle: {
                                color: "rgba(5,140,255, 0.2)",
                            },
                            data: value2,
                        },
                    ],
                };
                myCharts.setOption(option);
                tools.loopShowTooltip(myCharts, option, { loopSeries: true });
            },
        },
    });
</script>
