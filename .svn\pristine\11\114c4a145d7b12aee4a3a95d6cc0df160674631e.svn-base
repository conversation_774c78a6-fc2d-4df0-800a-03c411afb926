<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>应急管理-左</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        #app {
            width: 1050px;
            height: 1930px;
            background: url("/img/left-bg.png") no-repeat;
            background-size: 100% 100%;
            padding: 30px;
            box-sizing: border-box;
            overflow: hidden;
        }

        .header-title2[data-v-4d0d1712] {
            width: 100% !important;
        }

        .tabs {
            display: grid;
            padding: 50px 0;
            box-sizing: border-box;
        }

        .tab_box {
            width: 300px;
            height: 150px;
            text-align: center;
            padding: 20px 0;
            box-sizing: border-box;
            background-color: #5fa8e0;
            color: #fff;
        }

        .tab_box>p {
            font-size: 30px;
            margin: 10px 0;
        }
    </style>
</head>

<body>
    <div id="app">
        <nav>
            <s-header-title title="重大危险源分析" htype="2"></s-header-title>
        </nav>
        <div class="s-flex s-row-between s-flex-wrap">
            <div id="pie1" style="width: 50%; height: 320px"></div>
            <div id="pie2" style="width: 50%; height: 320px"></div>
            <div id="pie3" style="width: 50%; height: 320px"></div>
            <div id="pie4" style="width: 50%; height: 320px"></div>
        </div>

        <nav>
            <s-header-title-2 title="各区域危险源分布" htype="0"></s-header-title-2>
        </nav>

        <div id="bar1" style="width: 100%; height: 480px"></div>

        <nav>
            <s-header-title-2 title="重大危险监测和危险预警信息接入" htype="0"></s-header-title-2>
        </nav>

        <div class="s-flex">
            <div class="tabs" style="width: 30%; height: 480px">
                <div class="tab_box" v-for="item in listData">
                    <p>{{item.name}}</p>
                    <p>{{item.value + " " + item.unit}}</p>
                </div>
            </div>
            <div id="bar2" style="width: 70%; height: 480px"></div>
        </div>
    </div>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
        let vm = new Vue({
            el: "#app",
            data: {
                listData: [],
            },
            mounted() {
                this.initApi();
                this.initIframe();
                // this.initMap();
            },
            methods: {
                initIframe() {
                    let iframe = {
                        type: 'openIframe',
                        name: 'yjgl-map',
                        src: '/static/citybrain3840/shgl/pages/yjgl/yjgl-map.html',
                        width: '355px',
                        height: '415px',
                        left: '1090px',
                        top: '230px',
                        zIndex: '10',
                    }
                    window.parent.postMessage(JSON.stringify(iframe), '*')
                },
                initMap() {
                    top.document.getElementById("map").contentWindow.Work.change3D(9);
                    top.document.getElementById("map").contentWindow.Work.funChange(
                        JSON.stringify({
                            funcName: "flyto", //功能名称
                            flyData: {
                                center: [119.98478050597587, 29.18613226366889],
                                zoom: 9,
                                pitch: 28,
                                bearing: 0,
                                duration: 4000, //飞行时间（建议加上）
                            },
                        })
                    )
                   },
                    initApi() {
                        $api("ldst_shgl_yjgl", { type: "1" }).then((res) => {
                            res.map((ele, i) => {
                                this.getPie("pie" + (i + 1), ele);
                            });
                        });
                        $api("ldst_shgl_yjgl", { type: "2" }).then((res) => {
                            this.barEchart1("bar1", res);
                        });
                        $api("ldst_shgl_yjgl", { type: "3" }).then((res) => {
                            this.listData = res;
                        });
                        $api("ldst_shgl_yjgl", { type: "4" }).then((res) => {
                            this.barEchart2("bar2", res);
                        });
                    },

                    getPie(id, data) {
                        const myChartsRun = echarts.init(document.getElementById(id));
                        let pieData = [data];
                        var titleArr = [],
                            seriesArr = [];

                        pieData.forEach(function (item, index) {
                            titleArr.push({
                                text: item.name,
                                left: "50%",
                                top: "87%",
                                textAlign: "center",
                                textStyle: {
                                    fontWeight: "normal",
                                    fontSize: "24",
                                    color: "white",
                                    textAlign: "center",
                                },
                            });
                            seriesArr.push(
                                {
                                    type: "pie",
                                    name: "外层细圆环",
                                    radius: ["72%", "65%"],
                                    center: ["50%", "45%"],
                                    hoverAnimation: false,
                                    clockWise: false,
                                    itemStyle: {
                                        normal: {
                                            color: "#6e7175",
                                        },
                                    },
                                    label: {
                                        show: false,
                                    },
                                    data: [100],
                                },
                                {
                                    type: "pie",
                                    name: "内层层细圆环",
                                    radius: ["46%", "45%"],
                                    center: ["50%", "45%"],
                                    hoverAnimation: false,
                                    clockWise: false,
                                    itemStyle: {
                                        normal: {
                                            color: "#6e7175",
                                        },
                                    },
                                    label: {
                                        show: false,
                                    },
                                    data: [100],
                                },
                                {
                                    type: "pie",
                                    zlevel: 3,
                                    silent: true,
                                    radius: ["43%", "41%"],
                                    center: ["50%", "45%"],
                                    label: {
                                        normal: {
                                            show: false,
                                        },
                                    },
                                    labelLine: {
                                        normal: {
                                            show: false,
                                        },
                                    },
                                    data: dotArr(),
                                }
                            );
                        });

                        seriesArr.push({
                            name: pieData[0].name,
                            type: "pie",
                            clockWise: false,
                            radius: ["60%", "50%"],
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#0ff",
                                        },
                                        {
                                            offset: 1,
                                            color: "#5467df",
                                        },
                                    ]),
                                    label: {
                                        show: false,
                                    },
                                    labelLine: {
                                        show: false,
                                    },
                                },
                            },
                            hoverAnimation: false,
                            center: ["50%", "45%"],
                            data: [
                                {
                                    value: pieData[0].value,
                                    label: {
                                        normal: {
                                            formatter: function (params) {
                                                return params.value + "%";
                                            },
                                            position: "center",
                                            show: true,
                                            textStyle: {
                                                fontSize: "40",
                                                color: "#1cc7ff",
                                            },
                                        },
                                    },
                                },
                            ],
                        });

                        let option = {
                            grid: {
                                left: "5%",
                                right: "2%",
                                bottom: "0%",
                                top: "0%",
                                containLabel: true,
                            },
                            title: titleArr,
                            series: seriesArr,
                        };

                        function dotArr() {
                            let dataArr = [];
                            for (var i = 0; i < 80; i++) {
                                if (i % 2 === 0) {
                                    dataArr.push({
                                        name: (i + 1).toString(),
                                        value: 1,
                                        itemStyle: {
                                            normal: {
                                                color: "#676a6c",
                                                borderWidth: 1,
                                                borderColor: "#676a6c",
                                            },
                                        },
                                    });
                                } else {
                                    dataArr.push({
                                        name: (i + 1).toString(),
                                        value: 2,
                                        itemStyle: {
                                            normal: {
                                                color: "rgba(0,0,0,0)",
                                                borderWidth: 0,
                                                borderColor: "rgba(0,0,0,0)",
                                            },
                                        },
                                    });
                                }
                            }
                            return dataArr;
                        }
                        myChartsRun.setOption(option);
                    },

                    barEchart1(id, barData) {
                        const myCharts = echarts.init(document.getElementById(id));
                        var legend = ["低风险源", "一般风险源", "较大风险源", "重大风险源"];
                        var colorList = ["#5087EC", "#68BBC4", "#58A55C", "#e9e918"];
                        var data = [];
                        let x = barData.map((item) => {
                            return item.name;
                        });
                        let y1 = barData.map((item) => {
                            return item.one;
                        });
                        let y2 = barData.map((item) => {
                            return item.two;
                        });
                        let y3 = barData.map((item) => {
                            return item.three;
                        });
                        let y4 = barData.map((item) => {
                            return item.four;
                        });
                        data.push(y1, y2, y3, y4);
                        let option = {
                            tooltip: {
                                trigger: "axis",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                textStyle: {
                                    color: "white",
                                    fontSize: "30",
                                },
                            },
                            // color: colors,
                            legend: {
                                x: "center",
                                y: "15",
                                itemWidth: 20,
                                itemHeight: 20,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                data: legend,
                            },
                            grid: {
                                left: "3%",
                                right: "4%",
                                bottom: "0%",
                                top: "20%",
                                containLabel: true,
                            },
                            xAxis: {
                                type: "category",
                                axisLabel: {
                                    color: "#fff",
                                    fontSize: 24,
                                    rotate: 30,
                                },
                                axisLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#bbb",
                                    },
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#195384",
                                    },
                                },
                                data: x,
                            },
                            yAxis: {
                                type: "value",
                                name: "",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                axisLabel: {
                                    formatter: "{value}",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: "#fff",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#11366e",
                                    },
                                },
                            },
                            series: [],
                        };
                        for (var i = 0; i < legend.length; i++) {
                            option.series.push({
                                name: legend[i],
                                type: "bar",
                                stack: "总量",
                                barWidth: 50,
                                itemStyle: {
                                    normal: {
                                        color: colorList[i],
                                    },
                                },
                                label: {
                                    show: true,
                                    position: "inside",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 24,
                                    },
                                },
                                data: data[i],
                            });
                        }
                        myCharts.setOption(option);
                        myCharts.getZr().on("mousemove", (param) => {
                            myCharts.getZr().setCursorStyle("default");
                        });
                    },

                    barEchart2(id, barData) {
                        const myCharts = echarts.init(document.getElementById(id));
                        var legend = ["实时预警"];
                        var colorList = ["#5087EC", "#68BBC4", "#58A55C", "#e9e918"];
                        var data = [];
                        let x = barData.map((item) => {
                            return item.name;
                        });
                        let y1 = barData.map((item) => {
                            return item.value;
                        });

                        data.push(y1);
                        let option = {
                            tooltip: {
                                trigger: "axis",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                textStyle: {
                                    color: "white",
                                    fontSize: "30",
                                },
                            },
                            // color: colors,
                            legend: {
                                x: "center",
                                y: "15",
                                itemWidth: 20,
                                itemHeight: 20,
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                data: legend,
                            },
                            grid: {
                                left: "3%",
                                right: "4%",
                                bottom: "0%",
                                top: "20%",
                                containLabel: true,
                            },
                            xAxis: {
                                type: "category",
                                axisLabel: {
                                    color: "#fff",
                                    fontSize: 24,
                                    rotate: 30,
                                },
                                axisLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#bbb",
                                    },
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#195384",
                                    },
                                },
                                data: x,
                            },
                            yAxis: {
                                type: "value",
                                name: "",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                axisLabel: {
                                    formatter: "{value}",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: "#fff",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#11366e",
                                    },
                                },
                            },
                            series: [],
                        };
                        for (var i = 0; i < legend.length; i++) {
                            option.series.push({
                                name: legend[i],
                                type: "bar",
                                stack: "总量",
                                barWidth: 50,
                                itemStyle: {
                                    normal: {
                                        color: colorList[i],
                                    },
                                },
                                label: {
                                    show: true,
                                    position: "inside",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 24,
                                    },
                                },
                                data: data[i],
                            });
                        }
                        myCharts.setOption(option);
                        myCharts.getZr().on("mousemove", (param) => {
                            myCharts.getZr().setCursorStyle("default");
                        });
                    },
                },
            });
    </script>
</body>

</html>