<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title></title>
    <!-- <script src="./static/citybrain/csdn/static/citybrain/csdn/static/citybrain/jhpro/Vue/vue.js"></script>
    <script src="./static/citybrain/csdn/static/citybrain/csdn/static/citybrain/jhpro/jquery/jquery-3.4.1.min.js"></script>
    <script src="./static/citybrain/csdn/static/citybrain/csdn/static/citybrain/jhpro/echarts/echarts.js"></script>
    <link rel="stylesheet" href="./static/citybrain/csdn/static/citybrain/csdn/static/citybrain/jhpro/elementui/css/elementui.css">
    <link rel="stylesheet" href="./static/citybrain/csdn/static/citybrain/csdn/static/citybrain/hjbh-right/css/common.css">
    <script src="./static/citybrain/csdn/static/citybrain/csdn/static/citybrain/jhpro/elementui/js/elementui.js"></script> -->
    <script src="./static/citybrain/csdn/Vue/vue.js"></script>
    <script src="./static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="./static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="./static/citybrain/csdn/elementui/css/elementui.css">
    <link rel="stylesheet" href="./static/citybrain/csdn/css/common.css">
    <script src="./static/citybrain/csdn/elementui/js/elementui.js"></script>
</head>
<style>
    #app{
        position: absolute;
        left: 2506px;
        top: 1270px;
    }
  .yqfkCont {
    width: 2756px;
    height: 651px;
    background-image: url('./static/citybrain/csdn/img/yqfk/矩形1972.png');
    background-size: 100%;
    background-repeat: no-repeat;
  }
  .yqfkHea {
    width: 2693px;
    height: 120px;
    margin: 0 auto;
    background-image: url('./static/citybrain/csdn/img/yqfk/矩形1971.png');
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
  }
  .yqfkHea>h3 {
    font-size: 47px;
    padding-left: 56px;
    background-image: -webkit-linear-gradient(top, #d5e7f9, #c3e5ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
  }
  .yqfkList {
    width: 100%;
    box-sizing: border-box;
    padding: 0 56px;
    display: flex;
  }
  .yqfkListItem>h4 {
    font-size: 30px;
    background-image: -webkit-linear-gradient(top, #f2bf74, #f7d7aa, #fefaf4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
    padding: 25px 0 42px 0;
  }
  .yqfkList .yqfkListItem:nth-child(1) {
    width: 30%;
  }
  .yqfkList .yqfkListItem:nth-child(2) {
    width: 35%;
    padding: 0 20px;
    box-sizing: border-box;
  }
  .yqfkList .yqfkListItem:nth-child(3) {
    width: 35%;
    padding: 0 20px;
    box-sizing: border-box;
  }
  .jrGk {

  }
  .jrGkItem {
    display: flex;
    height: 53px;
    margin-bottom: 10px;
    align-items: center;
    justify-content: center;
  }
  .jrGkItem:last-child {
    margin-bottom: 10px;
  }
  .jrGkItemL {
    width: 50%;
    height: 100%;
    background-color: rgb(16, 59, 103, 0.6);
    text-align: center;
    font-size: 28px;
    color: #d6e7f9;
    line-height: 53px;
  }
  .jrGkItemR {
    width: 50%;
    height: 100%;
    background-color: rgb(14, 41, 73, 0.6);
    margin-left: 10px;
  }
  .jrGkItemR>h6 {
    width: 100%;
    height: 100%;
    line-height: 53px;
    text-align: center;
    font-size: 37px;
    background-image: -webkit-linear-gradient(top, #bcaaff, #ece7ff, #977cff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
    
  }
</style>

<body>
    <div id="app" v-cloak>
        <div class="yqfkCont">
          <div class="yqfkHea">
            <h3>金华站</h3>
          </div>
          <div class="yqfkList">
            <div class="yqfkListItem">
              <h4>
                <img src="./static/citybrain/csdn/img/yqfk/椭圆.png" alt="">
                <span>今日概况</span>
              </h4>
              <ul class="jrGk">
                <li class="jrGkItem">
                  <div class="jrGkItemL">今日流入人员</div>
                  <div class="jrGkItemR"><h6>2588<span>人</span></h6></div>
                </li>
                <li class="jrGkItem">
                  <div class="jrGkItemL">健康码红码人员</div>
                  <div class="jrGkItemR"><h6>1<span>人</span></h6></div>
                </li>
                <li class="jrGkItem">
                  <div class="jrGkItemL">健康码黄码人员</div>
                  <div class="jrGkItemR"><h6>15<span>人</span></h6></div>
                </li>
                <li class="jrGkItem">
                  <div class="jrGkItemL">行程码标星人员</div>
                  <div class="jrGkItemR"><h6>20<span>人</span></h6></div>
                </li>
                <li class="jrGkItem">
                  <div class="jrGkItemL">体温异常人员</div>
                  <div class="jrGkItemR"><h6>5<span>人</span></h6></div>
                </li>
                <li class="jrGkItem">
                  <div class="jrGkItemL">中高风险地区输入</div>
                  <div class="jrGkItemR"><h6>5<span>人</span></h6></div>
                </li>
              </ul>
            </div>
            <div class="yqfkListItem">
              <h4>
                <img src="./static/citybrain/csdn/img/yqfk/椭圆.png" alt="">
                <span>金华站输入趋势</span>
              </h4>
              <div id="srqs" ref="srqs" style="height: 390px;width: 100%;"></div>
            </div>
            <div class="yqfkListItem">            
              <div id="yqfkTable" style="height: 390px;width: 100%;margin-top:100px;"></div>
            </div>
          </div>
        </div>
    </div>

    <script>
        var vm = new Vue({
            el: '#app',
            data: {
                
            },
            mounted() {
              this.loadsatellitedata()  
              this.getAjallcontent1()
            },
            methods: {
              
            // echarts实例
            setupEChart(id, options) {
                const chart = echarts.init(document.getElementById(id));
                chart.clear();
                chart.setOption(options);
                window.onresize = () => {
                    chart.resize();
                };
            },
              getAjallcontent1() {
              var charts = {
                    unit: '单位: 人',
                    names: ['中高风险地区','体温异常人数'],
                    lineX: ['12.13','12.14','12.15','12.16','12.17','12.18'],
                    value: [
                        [4500, 1000, 2500, 6000, 5000, 4500, 6500, 4500,6000,200],
                        [8000, 4000, 5500, 6000, 4000, 2500, 2000, 6000,5000,2000],
                    ]
                }
                var color = ['#2ba5f7', '#ffeb7a']
                var lineY = []

                for (var i = 0; i < charts.names.length; i++) {
                    var x = i
                    if (x > color.length - 1) {
                        x = color.length - 1
                    }
                    var data = {
                        name: charts.names[i],
                        type: 'line',
                        color: color[x] ,                      
                        smooth: false,
                        symbol: 'emptyCircle',
                        symbolSize: 10,
                        data: charts.value[i], 
                    }
                    lineY.push(data)
                }

                lineY[0].markLine = {
                    silent: true,                    
                }
                let ajallOption = {
                  color : ['#2ba5f7', '#ffeb7a'],
                  title: {
                      textStyle: {
                          fontWeight: 'normal',
                          fontSize: 30,
                          color: '#fff'
                      },
                      left: '6%',
                      top: '4%'
                  },
                  tooltip: {
                      trigger: 'axis',
                      "textStyle": { "fontSize": 28 },
                  },
                  legend: {
                      top: '0%',
                      right: '10%',
                      icon:'circle',
                      itemGap:50,
                      data: charts.names,
                      textStyle: {
                          fontSize: 30,
                          color: 'rgb(255,255,255,0.8)',
                      },
                  },
                  grid: {
                    left: "0%", //图表距边框的距离
                    right: "8%",
                    top: "20%",
                    bottom: "5%",
                    containLabel: true,
                  },
                  xAxis: {
                      show: true,
                      type: 'category',
                      boundaryGap: true,
                      data: charts.lineX,  
                      axisLabel: {
                        margin:20,
                          textStyle: {
                            color: '#fff',
                            fontSize: 32
                          },
                      },
                    },
                  yAxis: {
                    max:8000,
                      nameTextStyle: {
                          color: "#fff",
                          fontSize: 30,
                      },
                      show: true,
                      splitArea: {
                      show: false,
                      },
                      name: charts.unit,
                      nameTextStyle:{
                          padding:[0,0,10,160],
                          fontSize:32,
                          color:"#fff"                          
                      },
                      type: 'value',
                      axisLabel: {
                          formatter: '{value}',
                          textStyle: {
                              color: '#fff',
                              fontSize: 26,
                          }
                      },
                      splitLine: {
                          lineStyle: {
                              color: '#77b3f110'
                          }
                      },
                  },
                  series: lineY
                }
                this.setupEChart("yqfkTable", ajallOption);
            },


              loadsatellitedata() {
                myChart = echarts.init(this.$refs.srqs)
                let option = {
                  grid: {
                    left: "0%", //图表距边框的距离
                    right: "8%",
                    top: "18%",
                    bottom: "5%",
                    containLabel: true,
                  },
                  legend: {
                    type: 'plain', // 图列类型，默认为 'plain'
                    top: '0', // 图列相对容器的位置 top\bottom\left\right
                    right: '5%',
                    data: [ // 图列内容
                      {
                        name: '流入人数',
                        textStyle: {
                          color: '#fff', //颜色
                          fontStyle: 'normal', //风格
                          fontWeight: 'normal', //粗细
                          fontSize: 32, //大小
                          align: 'left', //水平对齐
                        },

                      }
                    ]
                  },
                  title: [{
                      text: '单位：人',
                      x: '20',
                      textStyle: {
                        color: '#fff', //颜色
                        fontStyle: 'normal', //风格
                        fontWeight: 'normal', //粗细
                        fontSize: 32, //大小
                        align: 'right', //水平对齐
                      },
                    }
                  ],

                  xAxis: [{
                      type: 'category',
                      data: ["12.13", "12.14", "12.15", "12.16", "12.17", "12.18"],
                      axisPointer: {
                        type: 'shadow'
                      },
                      axisTick: {
                        show: true,
                        interval: 0
                      },
                      axisLabel: {
                        textStyle: {
                          color: '#fff', //坐标值得具体的颜色
                        },
                        fontSize: 32,
                      }
                    },

                  ],
                  yAxis: [{
                      type: 'value',
                      // name: '数量',
                      axisLabel: {
                        textStyle: {
                          color: '#fff', //坐标值得具体的颜色
                        },
                        fontSize: 26,
                      },
                      splitLine: { // 虚线
                        show: true,
                        lineStyle: {
                          type: 'dashed',
                          color: 'rgb(66,106,149)', //左边线的
                        }
                      },
                      axisLine: {
                        lineStyle: {
                          type: 'solid',
                          color: 'rgb(66,106,149)', //左边线的颜色
                          width: '0' //坐标线的宽度
                        },
                      },
                    },
                  ],
                  series: [{
                      name: '流入人数',
                      type: 'bar',
                      data: [4200, 6500, 4800, 7900, 6500, 4500],
                      barWidth: '25%',
                      barWidth: 25,
                      itemStyle: {
                        normal: {
                          barBorderRadius: [5, 5, 0, 0],
                          color: new echarts.graphic.LinearGradient(
                            0, 0, 0, 1, [{
                                offset: 0,
                                color: 'rgb(242,198,80)'
                              }, //柱图渐变色
                              {
                                offset: 1,
                                color: 'rgb(156,120,59)'
                              }, //柱图渐变色
                            ],

                          )
                        },
                      }
                    },
                  ]

                };
                myChart.setOption(option)
              },


            },

        })
    </script>
</body>

</html>