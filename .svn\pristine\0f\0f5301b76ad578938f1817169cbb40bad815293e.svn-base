<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>数字政府-左侧</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css">
    <link rel="stylesheet" href="/static/citybrain/szhgg/css/dzjg-left.css">
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script> <!-- 轮播toolTip -->
    <script src="/static/js/jslib/echarts-wordcloud.min.js"></script>
</head>

<style>
    .yzPar {
        position: relative;
    }

    .yzicon {
        width: 15px;
        height: 40px;
        background-color: rgb(12, 112, 211);
        position: absolute;
        right: 40px;
        top: 38px;
        z-index: 10;

    }
</style>

<body>
    <div id="app" class="container" v-cloak>
        <div class="first-con">
            <nav>
                <s-header-title htype="1" title="总体情况" :data-time="nowTime"></s-header-title>
            </nav>
            <div class="content">
                <div class="left-part">
                    <nav>
                        <s-header-title-2 htype="1" title="任务进展">
                            </s-header-title>
                    </nav>
                    <div id="rwjz-chart" style="position: relative">
                        <li class="process yzPar" v-for="(item,index) in processList" :key="index">
                            <div>
                                <div
                                    style="position: absolute;height: 28px;width:8px;background-color:rgb(12, 112, 211);z-index:1;left: 80%;top:45px">
                                </div>
                                <div
                                    style="position: absolute;height: 28px;width:8px;background-color:rgb(241, 4, 48);z-index:1;left: 60%;top:45px">
                                </div>
                            </div>
                            <div class="s-c-bul-light s-font-28 s-m-b-10">
                                <span style="display: inline-block;">{{item.name}}</span>
                                <span style="margin-left: 110px;">{{item.state}}</span>
                            </div>
                            <el-progress :text-inside="true" :stroke-width="26" :percentage="Number(item.rate)"
                                :color="item.color"></el-progress>
                        </li>
                        <div class="s-c-white s-font-25"
                            style="position: absolute;left:325px;top:440px;color:rgb(241, 4, 48);">阈值</div>
                        <div class="s-c-white s-font-25"
                            style="position: absolute;left:425px;top:440px;color:rgb(12, 112, 211);">平均值</div>
                    </div>
                </div>
                <div class="right-part">
                    <nav>
                        <s-header-title-2 htype="1" title="核心指标">
                            </s-header-title>
                    </nav>
                    <div class="hxzb-con">
                        <div class="table table1">
                            <div class="th">
                                <div class="th_td" style="flex: 0.45" v-for="(item,index) in theadList" :key="index">
                                    {{item}}
                                </div>
                            </div>
                            <div class="tbody" id="tbody1">
                                <div class="tr" v-for="(item ,i) in tbodyList" :key="i" @click="change(i)">
                                    <div class="tr_td" style="flex: 0.45">{{item.zblx}}</div>
                                    <div class="tr_td" style="flex: 0.45">{{item.zbs}}</div>
                                    <div class="tr_td" style="flex: 0.45">{{item.wtfxzs}}</div>
                                </div>
                            </div>
                        </div>
                        <div id="hxzb-chart"></div>
                    </div>
                </div>
            </div>
            <div class="content">
                <div class="left-part">
                    <nav>
                        <s-header-title-2 htype="1" title="晾晒台">
                            </s-header-title>
                    </nav>
                    <div class="lst-con">
                        <div class="top-tab">
                            <li v-for="(item,index) in TabList" :key="index"
                                :class="currentIndex===index?'tab-active':''" @click="changeTab(index,item)">
                                <span>{{item.name}}</span>
                            </li>
                        </div>
                        <div class="table table1" style="width: 100%;height: 89%;">
                            <div class="th">
                                <div class="th_td" style="flex: 0.35" v-for="(item,index) in theadList1" :key="index">
                                    {{item}}
                                </div>
                            </div>
                            <div class="tbody" id="tbody" @mouseover="mouseenterEvent()"
                                @mouseleave="mouseleaveEvent()">
                                <div class="tr" v-for="(item ,i) in tbodyList1" :key="i">
                                    <div class="tr_td" style="flex: 0.35">{{item.bm}}</div>
                                    <div class="tr_td" style="flex: 0.35">{{item.wczb}}</div>
                                    <div class="tr_td" style="flex: 0.35">{{item.wcsj}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="right-part">
                    <nav>
                        <s-header-title-2 htype="1" title="数字法治领域媒体宣传报道情况">
                            </s-header-title>
                    </nav>

                    <div class="bdqk-con">
                        <div id="bdqx-chart"></div>
                        <div id="bdqx-chart1"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="second-con">
            <nav>
                <s-header-title htype="1" title="应用驾驶舱接入情况" :data-time="nowTime"></s-header-title>
            </nav>
            <nav>
                <s-header-title-2 htype="1" title="工业固废一件事">
                    </s-header-title>
            </nav>
            <div class="bottom-con">
                <div class="left-con">
                    <!-- <li v-for="(item,index) in djtlData" :key="index">
                        <span>{{item.name}}</span><i class="el-icon-arrow-right"></i>
                    </li> -->
                    <div class="axyb">
                        <img src="/static/citybrain/szhgg/img/icon.png" alt="">
                        <div class="s-c-bul-light s-font-32 title">"无废城市"应用城市</div>
                        <div class="btn" @click="openXq()">点击查看</div>
                    </div>
                </div>
                <div class="right-con">
                    <li v-for="(item,index) in djtlList" :key="index" style="height: 31%;">
                        <div class="icon">
                            <img :src="`/static/citybrain/szhgg/img/${item.icon}.svg`" alt="">
                        </div>
                        <div class="txt">
                            <span>{{item.name}}</span>
                            <div><span>{{item.value}}</span><span>{{item.unit}}</span></div>
                        </div>

                    </li>
                </div>
            </div>
        </div>
    </div>
</body>
<script type="module">

    new Vue({
        el: '#app',
        data() {
            return {
                nowTime: '',//当前时间
                djtlData: [],//党建统领七张图清单左侧数据
                djtlList: [],//党建统领七张图清单右侧数据
                theadList: ['指标类型', '管控指数排名', '问题发现总数'],
                tbodyList: [],
                time: null,
                dom: null,
                theadList1: ['部门', '完成率（%）', '完成时间'],
                tbodyList1: [],
                TabList: [
                    {
                        name: '最佳实践',
                        id: '1',
                    },
                    {
                        name: '进度滞后',
                        id: '2'
                    }
                ],
                currentIndex: 0,
                processList: [],
            }
        },
        mounted() {
            this.getTime();
            this.init();
            this.scroll();
        },
        methods: {
            change(index) {
                if (index == 0) {
                    $api("szggh_szzf_hxzb02").then((res) => {
                        let data = res.filter(item => {
                            return item.type === "1"
                        });
                        this.getChart02(data);
                    });
                } else {
                    $api("szggh_szzf_hxzb02").then((res) => {
                        let data = res.filter(item => {
                            return item.type === "2"
                        });
                        this.getChart02(data);
                    });
                }
            },
            openXq() {
                top.commonObj.funOpenIframe({
                    width: "2130px",
                    height: "1270px",
                    zIndex: "999",
                    src: "/static/citybrain/szhgg/pages/szzf-dialogL.html",
                    left: "2945px",
                    top: "275px",
                    name: "szzf-dialogL",
                });
            },
            scroll() {
                this.dom = document.getElementById('tbody')
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },
            mouseenterEvent() {
                clearInterval(this.time)
            },
            mouseleaveEvent() {
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },
            //获取当前时间
            getTime() {
                var data = new Date();
                var yesterday = new Date(data.setDate(data.getDate() - 1));
                this.nowTime =
                    yesterday.getFullYear() +
                    "年" +
                    (yesterday.getMonth() + 1) +
                    "月" +
                    yesterday.getDate() +
                    "日";
            },
            changeTab(index, data) {
                this.currentIndex = index;
                if (this.currentIndex === 0) {
                    $api("szggh_szzf_lst").then((res) => {
                        this.tbodyList1 = res.filter(item => {
                            return item.type === "1"
                        });
                    });
                } else {
                    $api("szggh_szzf_lst").then((res) => {
                        this.tbodyList1 = res.filter(item => {
                            return item.type === "2"
                        });
                    });
                }
            },
            //数据初始化
            init() {
                $api("szggh_szzf_rwjz").then((res) => {
                    this.processList = res;
                });
                $api("szggh_szzf_dzjg01").then((res) => {
                    this.djtlData = res;
                });
                $api("szggh_szzf_dzjg02").then((res) => {
                    this.djtlList = res;
                });
                $api("szggh_szzf_hxzb01").then((res) => {
                    this.tbodyList = res;
                });
                $api("szggh_szzf_hxzb02").then((res) => {
                    let data = res.filter(item => {
                        return item.type === "1"
                    });
                    this.getChart02(data);
                });
                $api("szggh_szzf_lst").then((res) => {
                    this.tbodyList1 = res.filter(item => {
                        return item.type === "1"
                    });
                });

                $api("szggh_szzf_bdqk01").then((res) => {
                    this.getChart03(res);
                });
                $api("szggh_szzf_bdqk02").then((res) => {
                    this.getBarCharts(res);
                });
            },
            getChart02(data) {
                const myCharts = echarts.init(document.getElementById('hxzb-chart'))
                var myColor = [
                    "#34da62",

                    "#c81515"

                ];
                let option = {
                    grid: {
                        left: "8%",
                        top: "12%",
                        right: "0%",
                        bottom: "8%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            axisTick: "none",
                            axisLine: "none",
                            // offset: "82",
                            axisLabel: {
                                textStyle: {
                                    color: "#fff",
                                    fontSize: "30",
                                },
                            },
                            boundaryGap: ["20%", "20%"],
                            data: data.map((item) => { return item.name }),
                        },
                        {
                            axisTick: "none",
                            axisLine: "none",
                            // bottom:20,
                            axisLabel: {
                                textStyle: {
                                    color: "#fff",
                                    fontSize: "28",
                                },
                            },
                            // boundaryGap: ["20%", "20%"],
                            data: [],
                        },
                        {
                            axisLine: {
                                lineStyle: {
                                    color: "rgba(0,0,0,0)",
                                },
                            },
                            data: [],
                        },
                    ],
                    yAxis: [
                        {
                            show: false,
                        },
                    ],
                    series: [
                        {
                            name: "条",
                            // type: 'bar',
                            xAxisIndex: 0,
                            data: data.map((item) => { return item.value }),
                            type: "pictorialBar",
                            symbol: "rect",
                            symbolRepeat: "fixed",
                            symbolMargin: "10%",
                            symbolClip: true,
                            symbolSize: [20, 7],

                            label: {
                                normal: {
                                    show: true,
                                    position: "top",
                                    formatter: function (param) {
                                        return param.value + "%";
                                    },
                                    color: "#ffffff",
                                    textStyle: {
                                        normal: {
                                            // color: function (params) {
                                            //     console.log(params)
                                            //     var num = myColor.length;
                                            //     return myColor[params.dataIndex];
                                            // },
                                            color: "#ffffff"
                                        },
                                        fontSize: "28",
                                    },
                                },
                            },
                            barWidth: 80,
                            itemStyle: {
                                normal: {
                                    color: function (params) {
                                        var num = myColor.length;
                                        return myColor[params.dataIndex % num];
                                    },
                                },
                            },
                            z: 2,
                        },
                        {
                            name: "黑框",
                            type: "bar",
                            xAxisIndex: 1,
                            barGap: "-100%",
                            data: [99.5, 99.5],
                            barWidth: 30,
                            itemStyle: {
                                normal: {
                                    color: "#021224",
                                    opacity: 0.3,
                                    barBorderRadius: 45,
                                },
                            },
                            z: 1,
                        },
                        {
                            name: "外框",
                            type: "bar",
                            xAxisIndex: 2,
                            barGap: "-100%",
                            data: [100, 100],
                            barWidth: 34,
                            itemStyle: {
                                normal: {
                                    color: function (params) {
                                        var num = myColor.length;
                                        return myColor[params.dataIndex % num];
                                    },
                                    barBorderRadius: 45,
                                },
                            },
                            z: 0,
                        },
                    ],
                };

                myCharts.setOption(option);
                myCharts.getZr().on('mousemove', param => {
                    myCharts.getZr().setCursorStyle('default')
                })
            },
            getChart03(data) {
                echarts.init(document.getElementById('bdqx-chart')).dispose();
                let myEc = echarts.init(document.getElementById('bdqx-chart'));

                let option = {
                    tooltip: {
                        trigger: 'item',
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
                        },
                        borderWidth: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.6)',
                        textStyle: {
                            color: 'white',
                            fontSize: '28',
                        },
                    },
                    grid: {
                        left: "2%",
                        top: "1%",
                        bottom: 10,
                        right: "1%",
                        containLabel: true,
                    },
                    legend: {
                        orient: "vertical",
                        x: "65%",
                        y: "30%",
                        textStyle: {
                            color: "#fff",
                            fontSize: 28,
                        },
                        itemWidth: 30,
                        itemHeight: 20,
                        data: data.map((item) => { return item.name }),
                    },
                    series: [
                        {
                            name: "职务构成分析",
                            type: "pie",
                            radius: ["40%", "60%"],
                            center: ["35%", "50%"],
                            avoidLabelOverlap: false,
                            label: {
                                normal: {
                                    show: false,
                                    position: "center",
                                },
                                emphasis: {
                                    show: true,
                                    textStyle: {
                                        fontSize: "30",
                                        fontWeight: "bold",
                                        color: "#fff"
                                    },
                                },
                            },
                            labelLine: {
                                normal: {
                                    show: false,
                                },
                            },
                            data: data
                        },
                    ],
                };

                myEc.setOption(option)
                tools.loopShowTooltip(myEc, option, { loopSeries: true });
            },
            getBarCharts(data) {
                echarts.init(document.getElementById('bdqx-chart1')).dispose();
                let myEc = echarts.init(document.getElementById('bdqx-chart1'));
                var option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
                        },
                        borderWidth: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.6)',
                        textStyle: {
                            color: 'white',
                            fontSize: '28',
                        },
                    },
                    legend: {
                        orient: 'horizontal',
                        // itemWidth: 18,
                        // itemHeight: 18,
                        top: '0',
                        // icon: 'rect',
                        itemGap: 25,
                        textStyle: {
                            color: '#D6E7F9',
                            fontSize: 30,
                        },
                    },
                    grid: {
                        left: '2%',
                        right: '5%',
                        bottom: '10%',
                        top: '10%',
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: data.map((item) => { return item.name }),
                            offset: 20,
                            axisLine: {
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.3,
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 1,
                                // rotate: -30,
                                textStyle: {
                                    fontSize: 30,
                                    color: 'white',
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: "",
                            type: 'value',
                            // max: 800,
                            min: 0,
                            nameTextStyle: {
                                fontSize: 30,
                                color: '#D6E7F9',
                                padding: [0, 0, 20, 0],
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 30,
                                    color: '#D6E7F9',
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "",
                            type: 'bar',
                            barWidth: 35,
                            yAxisIndex: 0,
                            // smooth: true, //加这个
                            center: ['0%', '45%'],
                            radius: ['0%', '45%'],
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: '#00C0FF',
                                        },
                                        {
                                            offset: 0.2,
                                            color: '#00C0FF',
                                        },
                                        {
                                            offset: 1,
                                            color: '#004F69',
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            data: data.map((item) => { return item.value }),
                        },
                    ]
                }
                myEc.setOption(option)
                tools.loopShowTooltip(myEc, option, { loopSeries: true });
            },
        }
    })


</script>

</html>