<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>领域5</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <script src="/static/js/jslib/echarts-wordcloud.min.js"></script>
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <script src="/jquery/jquery-3.6.1.min.js"></script>
    <style>
      ::-webkit-scrollbar {
        width: 0 !important;
      }

      #ly5 {
        width: 3840px;
        height: 1930px;
        background: url("/img/left-bg.png") no-repeat;
        background-size: 100% 100%;
        overflow: hidden;
        display: flex;
        /* justify-content: space-evenly; */
        /* flex-direction: column; */
        /* align-items: center; */
      }

      .ly5-left {
        width: 50%;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        overflow: hidden;
      }

      .ly5-right {
        width: 50%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .lbCon {
        width: 932px;
        height: 935px;
        /* background-image: url("../img/lbbg.png"); */
        background-color: rgb(0, 57, 111, 0.5);
        border: 1px solid #20aeff;
        border-radius: 20px;
        background-size: cover;
        margin: 10px;
        margin-bottom: 0;
        overflow: hidden;
      }

      .lbConBig {
        width: 98%;
        height: 1890px;
        /* background-image: url("../img/lbbg.png"); */
        background-color: rgb(0, 57, 111, 0.5);
        border: 1px solid #20aeff;
        border-radius: 20px;
        background-size: cover;
        margin: 10px;
        margin-bottom: 0;
        overflow: hidden;
      }

      .title {
        font-size: 50px;
        margin-top: 50px;
        margin-left: 50px;
        font-family: Arial;
        color: rgba(2, 193, 215, 1);
        font-style: normal;
        letter-spacing: 0px;
        text-decoration: none;
        font-weight: 600;
      }

      /* 轮播样式 */
      .block {
        width: 92%;
        margin-left: 5%;
        margin-top: 20px;
      }

      .title1 {
        font-size: 40px;
        color: #fff;
        margin-top: 20px;
        margin-left: 20px;
        font-family: Arial;
        color: rgba(2, 193, 215, 1);
        font-style: normal;
        letter-spacing: 0px;
        text-decoration: none;
        display: flex;
        align-items: center;
        padding-right: 20px;
      }

      .el-carousel__container {
        height: 680px;
      }

      .el-carousel__item h3 {
        color: #254089;
        font-size: 14px;
        opacity: 0.75;
        line-height: 150px;
        margin: 0;
      }

      .el-carousel__item {
        background-color: rgb(34, 81, 146, 0.7);
      }

      .el-carousel__item.is-animating {
        -webkit-transition: -webkit-transform 2s ease-in-out;
        transition: -webkit-transform 2s ease-in-out;
        transition: transform 2s ease-in-out;
        transition: transform 2s ease-in-out, -webkit-transform 2s ease-in-out;
      }

      .el-carousel__indicators--outside button {
        opacity: 1;
      }

      .el-carousel__button {
        width: 50px;
        height: 5px;
      }

      .is-active .el-carousel__button {
        background-color: #20aeff;
        width: 50px;
        height: 5px;
      }

      .pmt {
        display: flex;
        align-items: center;
      }

      .pmt > div {
        width: 50%;
      }

      .ldtmsg {
        display: flex;
        align-items: center;
        color: #fff;
        font-size: 26px;
        padding-left: 20px;
        box-sizing: border-box[];
      }

      .ldtmsg > div {
        margin-right: 15px;
      }

      /* 表格 */
      .table {
        width: 100%;
        height: 100%;
        padding: 10px;
        box-sizing: border-box;
        overflow-y: auto;
      }

      .table .th {
        width: 100%;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-style: italic;
        font-weight: 700;
        font-size: 20px;
        line-height: 60px;
        background: #00396f;
        color: #ffffff;
        padding: 0 12px;
        box-sizing: border-box;
      }

      .table .th_td {
        letter-spacing: 0px;
        text-align: center;
        flex: 0.25;
      }

      .table .tbody {
        width: 100%;
        height: 500px;
        overflow: hidden;
      }

      .table .tbody:hover {
        overflow-y: auto;
      }

      .table .tbody::-webkit-scrollbar {
        width: 4px;
        /*滚动条整体样式*/
        height: 4px;
        /*高宽分别对应横竖滚动条的尺寸*/
      }

      .table .tbody::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: #20aeff;
        height: 8px;
      }

      .table .tr:nth-child(2n) {
        background: rgb(0 57 111 / 30%);
      }

      .table .tr:nth-child(2n + 1) {
        /* background: #035b86; */
      }

      .table .tr {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 80px;
        line-height: 80px;
        font-size: 30px;
        color: #ffffff;
        cursor: pointer;
        padding: 0 12px;
        box-sizing: border-box;
      }

      /* .table .tr:nth-child(2n) {
            background: #00396f;
        }
  
            .table .tr:nth-child(2n+1) {
            background: #035b86;
        }
        */
      .table .tr:hover {
        background-color: #6990b6;
      }

      .table .tr_td {
        letter-spacing: 0px;
        text-align: center;
        box-sizing: border-box;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .ywtbldiv {
        display: flex;
        /* height: 800px; */
        align-items: center;
        justify-content: center;
      }

      .ywtbldiv > div:first-child {
        flex: 1;
        width: 240px;
        /* height: 800px; */
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
      }

      .ywtbldiv > div > div:first-child {
        width: 240px;
        height: 240px;
        background-image: url("../img/b1.png");
        background-size: cover;
        display: flex;
        align-items: center;
        justify-content: center;
        /* margin-top: 50px;
    margin-left: 50px; */
      }

      .title2 {
        font-size: 30px;
        color: #fff;
        text-align: center;
        line-height: 30px;
        /* margin-top: 10px;
    margin-left: 50px; */
      }

      .ywtbldiv > div:nth-child(2) {
        flex: 1;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
      }

      .ywtbldiv > div:nth-child(2) > div {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
        width: 130px;
        height: 150px;
        margin-right: 30px;
        background-image: url("../img/b2.png");
        background-size: cover;
        margin-bottom: 10px;
      }

      .tabs {
        width: 100%;
        height: 489px;
        margin-top: 29px;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
      }

      .tabsColumn {
        width: 50%;
        height: 489px;
      }

      .tabsTitle {
        font-family: SourceHanSansCN-Regular;
        font-size: 40px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        background-image: linear-gradient(
          0deg,
          rgb(255, 255, 255) 0%,
          #01a3dd 50%,
          rgb(255, 255, 255) 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-left: 30px;
      }

      .tabsItem {
        /* width: 430px; */
        height: 62px;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        margin-top: 17px;
      }

      .icon1 {
        width: 61px;
        height: 61px;
        background: url("../img/icon1.png") no-repeat;
        background-size: cover;
        position: relative;
        font-family: BebasNeue;
        font-size: 40px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: rgba(255, 255, 255, 0.81);
        text-align: center;
      }

      .icon2 {
        /* width: 450px; */
        height: 62px;
        line-height: 62px;
        background: url("../img/icon2.png") no-repeat;
        background-size: cover;
        position: relative;
        right: -15px;
        /* top: 15px; */
        font-family: BebasNeue;
        font-size: 30px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: rgba(255, 255, 255, 0.81);
        /* text-align: center; */
        text-indent: 10px;
      }

      .mbox5 {
        background-color: #00396f;
        width: 400px;
        height: 120px;
        border-radius: 10px;
        margin: 20px;
        border: 1px solid #00396f;
      }

      .mbox51 {
        display: flex;
        margin-top: 20px;
        flex-wrap: wrap;
      }

      .mbox51 > div {
        background-image: url("../img/img10.png");
        background-size: cover;
        width: 240px;
        height: 171px;
        margin: 20px;
        margin-top: 40px;
        /* display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center; */
        padding-top: 40px;
        box-sizing: border-box;
      }

      .mbox51-4 > div {
        width: 380px;
        height: 221px;
      }

      .mbox51-5 > div {
        width: 410px;
        height: 271px;
        padding-top: 60px;
      }

      .videoDiv {
        display: flex;
      }

      .videoDiv > div:first-child {
        width: 510px;
        height: 500px;
        background-image: url("../img/sp1.png");
        background-size: cover;
        margin-top: 40px;
        margin-left: 40px;
      }

      .videoDiv > div:nth-child(2) {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 20px;
      }

      .videoDiv > div:nth-child(2) > div {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 60px;
        cursor: pointer;
        line-height: 60px;
        width: 200px;
        text-align: center;
        background-color: #142434;
        font-size: 38px;
        margin-top: 20px;
      }

      .box7-t {
        display: flex;
        padding-left: 20px;
      }

      .select {
        /* position: absolute; */
        z-index: 2;
        /* top: -15px; */
        margin-top: 10px;
        right: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .el-input__inner {
        font-size: 30px;
        /* width: 130px; */
        height: 50px;
        line-height: 50px;
        color: #fff;
        background-color: #011040b3;
      }

      .el-select-dropdown__item.hover,
      .el-select-dropdown__item:hover {
        background-color: #011040b3;
      }

      .el-input__icon {
        line-height: 48px;
      }

      .el-select-dropdown {
        background-color: #011040b3;
      }

      .el-select-dropdown__item {
        font-size: 30px;
        color: #fff;
      }

      .el-select .el-input .el-select__caret {
        font-size: 30px;
      }

      .lbConBig .block {
        width: 97%;
        margin-left: 1.5%;
      }

      .lbConBig .el-carousel__container {
        height: 1640px;
      }

      .tab-c {
        padding: 4px 12px;
        background-color: #00396f;
        color: #fff;
        font-size: 28px;
        cursor: pointer;
      }

      .tab-a {
        background-color: #01a3dd;
      }
    </style>
  </head>

  <body>
    <div id="ly5">
      <div class="ly5-left">
        <div class="lbCon">
          <div class="title">城市金融汇聚展示</div>
          <div class="block">
            <el-carousel
              ref="carousel0"
              indicator-position="outside"
              :interval="5000"
            >
              <el-carousel-item v-for="(item,i) in data0" :key="i">
                <div class="title1">
                  <div style="flex: 1">{{item[0].name0}}</div>
                  <div
                    class="tab-c"
                    v-show="i==5"
                    :class="{'tab-a':tabnum0==0}"
                    @click="clickTab0(0)"
                  >
                    小微
                  </div>
                  <div
                    class="tab-c"
                    v-show="i==5"
                    :class="{'tab-a':tabnum0==1}"
                    @click="clickTab0(1)"
                  >
                    民营
                  </div>

                  <div
                    class="tab-c"
                    v-show="i==6"
                    :class="{'tab-a':tabnum1==0}"
                    @click="clickTab1(0)"
                  >
                    存款
                  </div>
                  <div
                    class="tab-c"
                    v-show="i==6"
                    :class="{'tab-a':tabnum1==1}"
                    @click="clickTab1(1)"
                  >
                    贷款
                  </div>
                </div>
                <div :id="'csjrhjEcharts'+i" style="height: 660px"></div>
              </el-carousel-item>
            </el-carousel>
            <div class="select">
              <el-select
                v-model="value0"
                placeholder="请选择"
                @change="setActiveItem0(value0)"
              >
                <el-option
                  v-for="item in options0"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div class="lbCon">
          <div class="title">科技创新指标分析</div>
          <div class="block">
            <el-carousel
              ref="carousel1"
              indicator-position="outside"
              :interval="5000"
            >
              <el-carousel-item v-for="(item,i) in data1" :key="i">
                <div class="title1">
                  <div style="flex: 1">{{item[0].name0}}</div>
                  <!-- <div class="tab-c" v-show="i==5" :class="{'tab-a':tabnum0==0}" @click="clickTab0(0)">小微</div>
                <div class="tab-c" v-show="i==5" :class="{'tab-a':tabnum0==1}" @click="clickTab0(1)">民营</div>

                <div class="tab-c" v-show="i==6" :class="{'tab-a':tabnum1==0}" @click="clickTab1(0)">存款</div>
                <div class="tab-c" v-show="i==6" :class="{'tab-a':tabnum1==1}" @click="clickTab1(1)">贷款</div> -->
                </div>
                <div v-show="i==0">
                  <div class="title2">
                    {{item[0].name+": "+item[0].value +"%"}}
                  </div>
                </div>
                <div
                  v-show="i!=1"
                  :id="'kjcxzbEcharts'+i"
                  style="height: 660px"
                ></div>
                <div v-show="i==1" class="table table1">
                  <div class="th">
                    <div
                      class="th_td"
                      :style="{'flex':index==0?0.8:0.2,'text-align':'left'}"
                      v-for="(item,index) in theadList"
                      :key="index"
                    >
                      {{item}}
                    </div>
                  </div>
                  <div class="tbody">
                    <div
                      class="tr"
                      style="display: flex; justify-content: unset"
                      v-for="(item1 ,i) in tbodyList"
                      :key="i"
                    >
                      <!-- <div v-for="(item2 ,k) in item1" :key="k" :style="{'flex':k=='name'?0.4:0.2,'text-align':'center'}">
                      {{item2}}
                    </div> -->
                      <div style="flex: 0.8">{{item1.name}}</div>
                      <div style="flex: 0.2">{{item1.value}}</div>
                    </div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
            <div class="select">
              <el-select
                v-model="value1"
                placeholder="请选择"
                @change="setActiveItem1(value1)"
              >
                <el-option
                  v-for="item in options1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div class="lbCon">
          <div class="title">工业发展指标分析</div>
          <div class="block">
            <el-carousel
              ref="carousel2"
              indicator-position="outside"
              :interval="5000"
            >
              <el-carousel-item v-for="(item,i) in data2" :key="i">
                <div class="title1">
                  <div style="flex: 1">{{item[0].name0}}</div>
                  <div
                    class="tab-c"
                    v-show="i==4"
                    :class="{'tab-a':tabnum2==0}"
                    @click="clickTab2(0)"
                  >
                    规上工业增加值增速
                  </div>
                  <div
                    class="tab-c"
                    v-show="i==4"
                    :class="{'tab-a':tabnum2==1}"
                    @click="clickTab2(1)"
                  >
                    主营业务收入总量
                  </div>

                  <div
                    class="tab-c"
                    v-show="i==5"
                    :class="{'tab-a':tabnum3==0}"
                    @click="clickTab3(0)"
                  >
                    年度
                  </div>
                  <div
                    class="tab-c"
                    v-show="i==5"
                    :class="{'tab-a':tabnum3==1}"
                    @click="clickTab3(1)"
                  >
                    季度
                  </div>
                  <div
                    class="tab-c"
                    v-show="i==5"
                    :class="{'tab-a':tabnum3==2}"
                    @click="clickTab3(2)"
                  >
                    月度
                  </div>
                </div>
                <div v-show="i==1" class="box7-t">
                  <div
                    v-for="(item,i) in data2t"
                    :key="i"
                    style="margin-top: 20px"
                  >
                    <div
                      class="title2"
                      style="font-size: 22px; margin-right: 30px"
                    >
                      {{item.name+"："+item.value}}
                    </div>
                    <!-- <div class="title2" style="font-size:22px;margin-right: 10px;">{{}}</div> -->
                  </div>
                </div>
                <div v-show="i==3" class="box7-t">
                  <div
                    v-for="(item,i) in data3t"
                    :key="i"
                    style="margin-top: 20px"
                  >
                    <div
                      class="title2"
                      style="font-size: 22px; margin-right: 30px"
                    >
                      {{item.name+"："+item.value}}
                    </div>
                    <!-- <div class="title2" style="font-size:22px;margin-right: 10px;">{{}}</div> -->
                  </div>
                </div>
                <div
                  v-show="i!=2"
                  :id="'gyfzzbEcharts'+i"
                  style="height: 660px"
                ></div>
                <!-- <div v-show="i==2" class="mbox51">
                <div v-for="(m,n) in item">
                  <div class="title2" style="font-size:35px;line-height: 40px;margin-top:20px;flex: 0.5;">{{m.name}}</div>
                  <div class="title2" style="font-size:35px;line-height: 40px;margin-top:20px;">{{m.value}}</div>
                </div>
              </div> -->
                <div v-show="i==2" class="table table1">
                  <div class="th">
                    <div
                      class="th_td"
                      :style="{'flex':index==0?0.8:0.2,'text-align':'left'}"
                      v-for="(item,index) in theadList"
                      :key="index"
                    >
                      {{item}}
                    </div>
                  </div>
                  <div class="tbody">
                    <div
                      class="tr"
                      style="display: flex; justify-content: unset"
                      v-for="(item1 ,i) in item"
                      :key="i"
                    >
                      <!-- <div v-for="(item2 ,k) in item1" :key="k" :style="{'flex':k=='name'?0.4:0.2,'text-align':'center'}">
                      {{item2}}
                    </div> -->
                      <div style="flex: 0.8">{{item1.name}}</div>
                      <div style="flex: 0.2">{{item1.value}}</div>
                    </div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
            <div class="select">
              <el-select
                v-model="value2"
                placeholder="请选择"
                @change="setActiveItem2(value2)"
              >
                <el-option
                  v-for="item in options2"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div class="lbCon">
          <div class="title">产业经济指标分析</div>
          <div class="block">
            <el-carousel
              ref="carousel3"
              indicator-position="outside"
              :interval="5000"
            >
              <el-carousel-item v-for="(item,i) in data3" :key="i">
                <div class="title1">
                  <div style="flex: 1">{{item[0].name0}}</div>
                  <!-- <div class="tab-c" v-show="i==4" :class="{'tab-a':tabnum2==0}" @click="clickTab2(0)">规上工业增加值增速</div>
                <div class="tab-c" v-show="i==4" :class="{'tab-a':tabnum2==1}" @click="clickTab2(1)">主营业务收入总量</div>

                <div class="tab-c" v-show="i==5" :class="{'tab-a':tabnum3==0}" @click="clickTab1(0)">年度</div>
                <div class="tab-c" v-show="i==5" :class="{'tab-a':tabnum3==1}" @click="clickTab1(1)">季度</div>
                <div class="tab-c" v-show="i==5" :class="{'tab-a':tabnum3==2}" @click="clickTab1(2)">月度</div> -->
                </div>
                <div v-show="i==0" class="box7-t">
                  <div
                    v-for="(item,i) in data4t"
                    :key="i"
                    style="margin-top: 20px"
                  >
                    <div
                      class="title2"
                      style="font-size: 22px; margin-right: 30px"
                    >
                      {{item.name+"："+item.value}}
                    </div>
                    <!-- <div class="title2" style="font-size:22px;margin-right: 10px;">{{}}</div> -->
                  </div>
                </div>
                <div
                  v-show="i!=3"
                  :id="'cyjjzbEcharts'+i"
                  style="height: 660px"
                ></div>
                <div v-show="i==3" class="mbox51 mbox51-4">
                  <div v-for="(m,n) in item">
                    <div
                      class="title2"
                      style="
                        font-size: 34px;
                        line-height: 40px;
                        margin-top: 20px;
                        flex: 0.5;
                      "
                    >
                      {{m.name}}
                    </div>
                    <div
                      class="title2"
                      style="
                        font-size: 46px;
                        line-height: 40px;
                        margin-top: 20px;
                      "
                    >
                      {{m.value}}
                    </div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
            <div class="select">
              <el-select
                v-model="value3"
                placeholder="请选择"
                @change="setActiveItem3(value3)"
              >
                <el-option
                  v-for="item in options3"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
          </div>
        </div>
      </div>
      <div class="ly5-right">
        <div class="lbConBig">
          <div class="title">统计大数据指标分析</div>
          <div class="block">
            <el-carousel
              ref="carousel4"
              indicator-position="outside"
              :interval="5000"
            >
              <el-carousel-item v-for="(item,i) in data4" :key="i">
                <div class="title1">
                  <div style="flex: 1">{{item[0].name0}}</div>
                  <div
                    class="tab-c"
                    v-show="i==1"
                    :class="{'tab-a':tabnum4==0}"
                    @click="clickTab4(0)"
                  >
                    按地区
                  </div>
                  <div
                    class="tab-c"
                    v-show="i==1"
                    :class="{'tab-a':tabnum4==1}"
                    @click="clickTab4(1)"
                  >
                    按年份
                  </div>

                  <div
                    class="tab-c"
                    v-show="i==2"
                    :class="{'tab-a':tabnum5==0}"
                    @click="clickTab5(0)"
                  >
                    按地区
                  </div>
                  <div
                    class="tab-c"
                    v-show="i==2"
                    :class="{'tab-a':tabnum5==1}"
                    @click="clickTab5(1)"
                  >
                    按年份
                  </div>

                  <div
                    class="tab-c"
                    v-show="i==3"
                    :class="{'tab-a':tabnum6==0}"
                    @click="clickTab6(0)"
                  >
                    按地区
                  </div>
                  <div
                    class="tab-c"
                    v-show="i==3"
                    :class="{'tab-a':tabnum6==1}"
                    @click="clickTab6(1)"
                  >
                    按年份
                  </div>
                  <div class="tab-c" v-show="i==4" @click="topMap">
                    <span v-show="mapShow">地图</span>
                    <span v-show="!mapShow">返回</span>
                  </div>
                </div>
                <div
                  v-if="i!=4"
                  :id="'tjdsjzbEcharts'+i"
                  style="height: 1560px"
                ></div>
                <div
                  v-show="i==4 && mapShow"
                  :id="'tjdsjzbEcharts'+i"
                  style="height: 860px;"
                ></div>
                <div
                  v-show="i==4 && mapShow==false"
                  id="mapChart" 
                  style="height: 860px;background: url('/static/citybrain3840/sldstjc/img/map.png');background-size: 100% 100%;"
                ></div>
                <div v-if="i==4" class="mbox51 mbox51-5">
                  <div v-for="(m,n) in data5t">
                    <div
                      class="title2"
                      style="
                        font-size: 34px;
                        line-height: 40px;
                        margin-top: 20px;
                        flex: 0.5;
                        position: relative;
                      "
                    >
                      <i class="el-icon-video-camera" 
                          style="position: absolute;right:30px;top:-50px;cursor: pointer;" 
                          v-show="n==4"
                          @click="openvideo()"
                      ></i>
                      {{m.name}}
                    </div>
                    <div
                      class="title2"
                      style="
                        font-size: 46px;
                        line-height: 40px;
                        margin-top: 20px;
                      "
                    >
                      {{m.value}}
                    </div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
            <div class="select">
              <el-select
                v-model="value4"
                placeholder="请选择"
                @change="setActiveItem4(value4)"
              >
                <el-option
                  v-for="item in options4"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
<script src="/static/js/jslib/$min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  var vm = new Vue({
    el: "#ly5",
    data: {
      theadList: ["名称", "数据"],
      tbodyList: [],
      data0: [],
      data1: [],
      data2: [],
      data3: [],
      data4: [],
      data5: [],
      data6: [],
      data7: [],
      data8: [],
      config: {
        data: [98.83],
        shape: "round",
        colors: ["#00C0FF"],
      },
      geren: [],
      qiye: [],
      yjdata1: [],
      yjdata2: [],
      yjdata3: [],
      data2t: [],
      data3t: [],
      data4t: [],
      data5t: [],
      options0: [],
      value0: 0,
      options1: [],
      value1: 0,
      options2: [],
      value2: 0,
      options3: [],
      value3: 0,
      options4: [],
      value4: 0,
      options5: [],
      value5: 0,
      options6: [],
      value6: 0,
      options7: [],
      value7: 0,
      tabnum0: 0,
      tabnum1: 0,
      tabnum2: 0,
      tabnum3: 0,
      tabnum4: 0,
      tabnum5: 0,
      tabnum6: 0,
      gyjjfasdMsgData: [],
      jgcdkChart: undefined,
      ws:top.DHWsInstance,
      mapShow:true,
    },
    created() {},
    mounted() {
      this.init0();
      this.init1();
      this.init2();
      this.init3();
      this.init4();
    },
    methods: {
      async setMap() {},
      topMap(){
        this.mapShow = !this.mapShow
      },
      openvideo(){
        this.ws.openVideo(['33079952001321083431'])
      },
      // 城市金融汇聚展示
      async init0() {
        // await $api("ldst_shgl_ly5", { type1: 1 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly5001").then((res) => {
          this.data0.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 2 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly5002").then((res) => {
          this.data0.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 3 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly5003").then((res) => {
          this.data0.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 4 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly5004").then((res) => {
          this.data0.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 5 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly5005").then((res) => {
          this.data0.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 6 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly5006").then((res) => {
          this.data0.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 7 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly5007").then((res) => {
          this.data0.push(res);
          console.log(this.data0);
          setTimeout(() => {
            this.data0.forEach((item, i) => {
              this.options0.push({ label: item[0].name0, value: i });

              if (i == 0) {
                this.barEcgarts1(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  item.map((a) => {
                    return a.value1;
                  }),
                  item.map((a) => {
                    return a.value2;
                  }),
                  item.map((a) => {
                    return a.value3;
                  }),
                  "存款",
                  "贷款",
                  "存款同比增速",
                  "贷款同比增速",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "csjrhjEcharts" + i,
                  "30",
                  "单位：亿元"
                );
              } else if (i == 1) {
                this.barEcgarts1(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  item.map((a) => {
                    return a.value1;
                  }),
                  item.map((a) => {
                    return a.value2;
                  }),
                  item.map((a) => {
                    return a.value3;
                  }),
                  "保费收入",
                  "赔付支出",
                  "收入同比增速",
                  "支出同比增速",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "csjrhjEcharts" + i,
                  "30",
                  "单位：亿元"
                );
              } else if (i == 2) {
                this.barEcgarts1(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  item.map((a) => {
                    return a.value1;
                  }),
                  item.map((a) => {
                    return a.value2;
                  }),
                  item.map((a) => {
                    return a.value3;
                  }),
                  "个人贷款",
                  "企业贷款",
                  "个人贷款同比增长",
                  "企业贷款同比增速",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "csjrhjEcharts" + i,
                  "30",
                  "单位：亿元"
                );
              } else if (i == 3) {
                this.barEcgarts1(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  item.map((a) => {
                    return a.value1;
                  }),
                  item.map((a) => {
                    return a.value2;
                  }),
                  [],
                  "本月余额",
                  "比年初新增额",
                  "比去年同期增减幅度",
                  "",
                  "bar",
                  "bar",
                  "line",
                  "",
                  "csjrhjEcharts" + i,
                  "30",
                  "单位：亿元"
                );
              } else if (i == 4) {
                this.barEcgarts2(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  item.map((a) => {
                    return a.value1;
                  }),
                  item.map((a) => {
                    return a.value2;
                  }),
                  item.map((a) => {
                    return a.value3;
                  }),
                  "大型企业贷款",
                  "中型企业贷款",
                  "小型企业贷款",
                  "微型企业贷款",
                  "bar",
                  "bar",
                  "bar",
                  "bar",
                  "csjrhjEcharts" + i,
                  "30",
                  "单位：亿元"
                );
              } else if (i == 5) {
                this.barEcgarts1(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  item.map((a) => {
                    return a.value1;
                  }),
                  item.map((a) => {
                    return a.value2;
                  }),
                  [],
                  "比年初新增额",
                  "比今年年初增长",
                  "同比增长",
                  "",
                  "bar",
                  "line",
                  "line",
                  "",
                  "csjrhjEcharts" + i,
                  "30",
                  "单位：亿元"
                );
              } else if (i == 6) {
                this.barEcgarts3(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  item.map((a) => {
                    return a.value1;
                  }),
                  item.map((a) => {
                    return a.value2;
                  }),
                  item.map((a) => {
                    return a.value3;
                  }),
                  item.map((a) => {
                    return a.value4;
                  }),
                  item.map((a) => {
                    return a.value5;
                  }),
                  item.map((a) => {
                    return a.value6;
                  }),
                  item.map((a) => {
                    return a.value7;
                  }),
                  "政策性银行",
                  "大型国有银行",
                  "股份制商业银行",
                  "地方法人金融机构",
                  "政策性银行比今年年初增速",
                  "大型国有银行比今年年初增速",
                  "股份制商业银行比今年年初增速",
                  "地方法人金融机构比今年年初增速 ",
                  "bar",
                  "bar",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "line",
                  "line",
                  "csjrhjEcharts" + i,
                  "30",
                  "单位：亿元"
                );
              }
            });
          });
        });
      },
      // 科技创新指标分析
      async init1() {
        // await $api("ldst_shgl_ly5", { type1: 8 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly5008").then((res) => {
          this.data1.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 9 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly5009").then((res) => {
          this.data1.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 10 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50010").then((res) => {
          this.data1.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 11 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50011").then((res) => {
          this.data1.push(res);
          setTimeout(() => {
            this.data1.forEach((item, i) => {
              this.options1.push({ label: item[0].name0, value: i });

              if (i == 0) {
                this.barEcgarts4(
                  item.filter((item) => {
                    return item.type == 2;
                  }),
                  "kjcxzbEcharts" + i
                );
              } else if (i == 1) {
                this.tbodyList = item;
              } else if (i == 2) {
                this.barEcgarts1(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  [],
                  [],
                  [],
                  "",
                  "",
                  "",
                  "",
                  "bar",
                  "",
                  "",
                  "",
                  "kjcxzbEcharts" + i,
                  "30",
                  "单位：万"
                );
              } else if (i == 3) {
                this.barEcgarts1(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  item.map((a) => {
                    return a.value1;
                  }),
                  [],
                  [],
                  "技术合同登记额",
                  "增长百分比",
                  "",
                  "",
                  "bar",
                  "line",
                  "",
                  "",
                  "kjcxzbEcharts" + i,
                  "30",
                  "单位：万"
                );
              }
            });
          });
        });
      },
      // 工业发展指标分析
      async init2() {
        // await $api("ldst_shgl_ly5", { type1: 12 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50012").then((res) => {
          this.data2.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 13 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50013").then((res) => {
          this.data2.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 14 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50014").then((res) => {
          this.data2.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 15 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50015").then((res) => {
          this.data2.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 16 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50016").then((res) => {
          this.data2.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 17 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50017").then((res) => {
          this.data2.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 18 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50018").then((res) => {
          this.data2.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 19 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50019").then((res) => {
          this.data2.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 20 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50020").then((res) => {
          this.data2.push(res);
          setTimeout(() => {
            this.data2.forEach((item, i) => {
              this.options2.push({ label: item[0].name0, value: i });

              if (i == 0) {
                this.barEcgarts5(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  item.map((a) => {
                    return a.value1;
                  }),
                  item.map((a) => {
                    return a.value2;
                  }),
                  "金华市",
                  "浙江省",
                  "全国",
                  "gyfzzbEcharts" + i,
                  "30",
                  "    发展速度(%)"
                );
              } else if (i == 1) {
                this.data2t = item.filter((a) => {
                  return a.type == 2;
                });
                var item1 = item.filter((a) => {
                  return a.type == 1;
                });
                this.barEcgarts1(
                  item1.map((a) => {
                    return a.name;
                  }),
                  item1.map((a) => {
                    return a.value;
                  }),
                  [],
                  item1.map((a) => {
                    return a.value1;
                  }),
                  [],
                  "总量",
                  "",
                  "增速",
                  "",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "gyfzzbEcharts" + i,
                  "20",
                  "%"
                );
              } else if (i == 2) {
              } else if (i == 3) {
                this.data3t = item.filter((a) => {
                  return a.type == 2;
                });
                var item1 = item.filter((a) => {
                  return a.type == 1;
                });
                this.barEcgarts5(
                  item1.map((a) => {
                    return a.name;
                  }),
                  item1.map((a) => {
                    return a.value;
                  }),
                  item1.map((a) => {
                    return a.value1;
                  }),
                  [],
                  "本年累计投资额",
                  "去年同期",
                  "",
                  "gyfzzbEcharts" + i,
                  "20",
                  "%"
                );
              } else if (i == 4) {
                this.barEcgarts6(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  [],
                  "",
                  "",
                  "bar",
                  "bar",
                  "gyfzzbEcharts" + i,
                  "%"
                );
              } else if (i == 5) {
                this.barEcgarts4(item, "gyfzzbEcharts" + i);
              } else if (i == 6) {
                this.barEcgarts1(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  [],
                  [],
                  [],
                  "商品房统计",
                  "",
                  "",
                  "",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "gyfzzbEcharts" + i,
                  "20",
                  "单位：万平"
                );
              } else if (i == 7) {
                this.barEcgarts1(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  item.map((a) => {
                    return a.value1;
                  }),
                  [],
                  [],
                  "一网通办率",
                  "网办率",
                  "",
                  "",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "gyfzzbEcharts" + i,
                  "20",
                  "%"
                );
              } else if (i == 8) {
                this.barEcgarts1(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  item.map((a) => {
                    return a.value1;
                  }),
                  [],
                  [],
                  "立案数",
                  "结案数",
                  "",
                  "",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "gyfzzbEcharts" + i,
                  "20",
                  "单位：万人"
                );
              }
            });
          });
        });
      },
      // 科技创新指标分析
      async init3() {
        // await $api("ldst_shgl_ly5", { type1: 21 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50021").then((res) => {
          this.data3.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 22 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50022").then((res) => {
          this.data3.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 23 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50023").then((res) => {
          this.data3.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 24 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50024").then((res) => {
          this.data3.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 25 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50025").then((res) => {
          this.data3.push(res);
          setTimeout(() => {
            this.data3.forEach((item, i) => {
              this.options3.push({ label: item[0].name0, value: i });

              if (i == 0) {
                this.data4t = item.filter((a) => {
                  return a.type == 2;
                });
                let item1 = item.filter((a) => {
                  return a.type == 1;
                });
                this.barEcgarts1(
                  item1.map((a) => {
                    return a.name;
                  }),
                  item1.map((a) => {
                    return a.value;
                  }),
                  item1.map((a) => {
                    return a.value1;
                  }),
                  item1.map((a) => {
                    return a.value2;
                  }),
                  item1.map((a) => {
                    return a.value3;
                  }),
                  "服务业增加值",
                  "GDP",
                  "服务业增加值增速",
                  "GDP增速",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "cyjjzbEcharts" + i,
                  "30",
                  "单位：亿元"
                );
              } else if (i == 1) {
                this.barEcgarts1(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  [],
                  item.map((a) => {
                    return a.value1;
                  }),
                  [],
                  "服务进出口总额",
                  "",
                  "离岸服务外包执行金额",
                  "",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "cyjjzbEcharts" + i,
                  "30",
                  "单位：亿元"
                );
              } else if (i == 2) {
                this.barEcgarts1(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  item.map((a) => {
                    return a.value1;
                  }),
                  item.map((a) => {
                    return a.value2;
                  }),
                  item.map((a) => {
                    return a.value3;
                  }),
                  "货物进口总额",
                  "货物出口总额",
                  "货物进口总额增速",
                  "货物出口总额增速",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "cyjjzbEcharts" + i,
                  "30",
                  "单位：亿"
                );
              } else if (i == 3) {
              } else if (i == 4) {
                this.barEcgarts1(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  [],
                  [],
                  [],
                  "",
                  "",
                  "",
                  "",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "cyjjzbEcharts" + i,
                  "30",
                  "单位：亿"
                );
              }
            });
          });
        });
      },
      // 统计大数据指标分析
      async init4() {
        // await $api("ldst_shgl_ly5", { type1: 26 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50026").then((res) => {
          this.data4.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 27 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50027").then((res) => {
          this.data4.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 28 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50028").then((res) => {
          this.data4.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 29 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50029").then((res) => {
          this.data4.push(res);
        });
        // await $api("ldst_shgl_ly5", { type1: 30 }).then((res) => {
        await $get("3840/xsqstjc/ly5/ly50030").then((res) => {
          this.data4.push(res);
          setTimeout(() => {
            this.data4.forEach((item, i) => {
              this.options4.push({ label: item[0].name0, value: i });

              if (i == 0) {
                this.barEcgarts1right(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  [],
                  item.map((a) => {
                    return a.value1;
                  }),
                  [],
                  "指标",
                  "",
                  "产值",
                  "",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "tjdsjzbEcharts" + i,
                  "30",
                  "单位：万"
                );
              } else if (i == 1) {
                this.barEcgarts1right(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  [],
                  item.map((a) => {
                    return a.value1;
                  }),
                  [],
                  "指标",
                  "",
                  "产值",
                  "",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "tjdsjzbEcharts" + i,
                  "30",
                  "单位：万元"
                );
              } else if (i == 2) {
                this.barEcgarts1right(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  [],
                  item.map((a) => {
                    return a.value1;
                  }),
                  [],
                  "指标",
                  "",
                  "产值",
                  "",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "tjdsjzbEcharts" + i,
                  "30",
                  "单位：万元"
                );
              } else if (i == 3) {
                this.barEcgarts1right(
                  item.map((a) => {
                    return a.name;
                  }),
                  item.map((a) => {
                    return a.value;
                  }),
                  [],
                  item.map((a) => {
                    return a.value1;
                  }),
                  [],
                  "指标",
                  "",
                  "产值",
                  "",
                  "bar",
                  "bar",
                  "line",
                  "line",
                  "tjdsjzbEcharts" + i,
                  "30",
                  "单位：万元"
                );
              } else if (i == 4) {
                this.data5t = item.filter((a) => {
                  return a.type == 2;
                });
                this.barEcgarts7(
                  item.filter((a) => {
                    return a.type == 1;
                  }),
                  "tjdsjzbEcharts" + i
                );
              }
            });
          });
        });
      },

      setActiveItem0(value) {
        this.$refs.carousel0.setActiveItem(value);
      },
      setActiveItem1(value) {
        this.$refs.carousel1.setActiveItem(value);
      },
      setActiveItem2(value) {
        this.$refs.carousel2.setActiveItem(value);
      },
      setActiveItem3(value) {
        this.$refs.carousel3.setActiveItem(value);
      },
      setActiveItem4(value) {
        this.$refs.carousel4.setActiveItem(value);
      },
      setActiveItem5(value) {
        this.$refs.carousel5.setActiveItem(value);
      },
      setActiveItem6(value) {
        this.$refs.carousel6.setActiveItem(value);
      },
      setActiveItem7(value) {
        this.$refs.carousel7.setActiveItem(value);
      },
      clickTab0(i) {
        console.log("clickTab0", i);
        this.tabnum0 = i;
        const item = this.data0[5];
        var chart = echarts.getInstanceByDom(
          document.getElementById("csjrhjEcharts5")
        );

        var option = chart.getOption();
        let xdata,
          ydata1,
          ydata2,
          ydata3,
          ydata4,
          name1,
          name2,
          name3,
          name4,
          type1,
          type2,
          type3,
          type4,
          dom,
          rotate,
          dw;
        if (i === 0) {
          xdata = item.map((a) => {
            return a.name;
          });
          ydata1 = item.map((a) => {
            return a.value;
          });
          ydata2 = item.map((a) => {
            return a.value1;
          });
          ydata3 = item.map((a) => {
            return a.value2;
          });
        } else {
          xdata = item.map((a) => {
            return a.name;
          });
          ydata1 = item.map((a) => {
            return a.value2;
          });
          ydata2 = item.map((a) => {
            return a.value;
          });
          ydata3 = item.map((a) => {
            return a.value1;
          });
        }
        ydata4 = [];
        name1 = "比年初新增额";
        name2 = "比今年年初增长";
        name3 = "同比增长";
        name4 = "";
        type1 = "bar";
        type2 = "line";
        type3 = "line";
        type4 = "";
        rotate = "30";
        dw = "单位：亿元";
        option = {
          color: [
            "#00C0FF",
            "#22E8E8",
            "#FFD461",
            "#A9DB52",
            "#B76FD8",
            "#FD852E",
            "#FF4949",
            "#0594C3",
            "#009D9D",
            "#A47905",
          ],
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "28",
            },
            // formatter: function (params) {
            //     console.log(params);
            //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
            //     for (var i = 0; i < params.length; i++) {
            //       if (params[i].data != undefined) {
            //         res +=
            //           "<p>" +
            //           params[i].marker +
            //           params[i].seriesName+" "+
            //           params[i].data +
            //           "万件" +
            //           "</p>";
            //       }
            //     }
            //     return res;
            //   },
          },
          legend: {
            orient: "horizontal",
            // icon: "circle",
            top: 40,
            itemGap: 20,
            textStyle: {
              color: "#D6E7F9",
              fontSize: 24,
            },
          },
          grid: {
            left: "5%",
            right: "5%",
            top: "24%",
            bottom: "15%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: xdata,
              axisLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)", // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                rotate: rotate | 0,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: dw,
              type: "value",
              nameTextStyle: {
                fontSize: 28,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: "",
              type: "value",
              // max: 100,
              nameTextStyle: {
                fontSize: 28,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: "{value}%",
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              name: name1,
              type: type1,
              barWidth: "20%",
              // smooth: true,
              yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#00C0FF",
                    },
                    {
                      offset: 1,
                      color: "rgba(0,192,255,0)",
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata1,
              areaStyle: {
                //填充
                color: "#00C0FF",
                opacity: 1,
              },
            },
            {
              name: name2,
              type: type2,
              barWidth: "20%",
              smooth: true,
              yAxisIndex: type2 == "bar" ? 0 : 1,
              itemStyle: {
                normal: {
                  color:
                    type2 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#2DF09F",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#2DF09F",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata2,
              // areaStyle:"",
            },
            {
              name: name3,
              type: type3,
              barWidth: "20%",
              smooth: true,
              yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type3 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#fff849",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#fff849",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata3,
              // areaStyle:"",
            },
            {
              name: name4,
              type: type4,
              barWidth: "20%",
              smooth: true,
              yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type4 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#B76FD8",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#B76FD8",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata4,
              // areaStyle:"",
            },
          ],
        };
        chart.setOption(option);
      },
      clickTab1(i) {
        this.tabnum1 = i;
        const item = this.data0[6];
        console.log("item", item);
        if (i === 1) {
          this.barEcgarts3(
            item.map((a) => {
              return a.name;
            }),
            item.map((a) => {
              return a.value3;
            }),
            item.map((a) => {
              return a.value;
            }),

            item.map((a) => {
              return a.value2;
            }),
            item.map((a) => {
              return a.value1;
            }),
            item.map((a) => {
              return a.value7;
            }),
            item.map((a) => {
              return a.value4;
            }),
            item.map((a) => {
              return a.value6;
            }),
            item.map((a) => {
              return a.value5;
            }),

            "政策性银行",
            "大型国有银行",
            "股份制商业银行",
            "地方法人金融机构",
            "政策性银行比今年年初增速",
            "大型国有银行比今年年初增速",
            "股份制商业银行比今年年初增速",
            "地方法人金融机构比今年年初增速 ",
            "bar",
            "bar",
            "bar",
            "bar",
            "line",
            "line",
            "line",
            "line",
            "csjrhjEcharts" + i,
            "30",
            "单位：亿元"
          );
        } else {
          this.barEcgarts3(
            item.map((a) => {
              return a.name;
            }),
            item.map((a) => {
              return a.value;
            }),
            item.map((a) => {
              return a.value1;
            }),
            item.map((a) => {
              return a.value2;
            }),
            item.map((a) => {
              return a.value3;
            }),
            item.map((a) => {
              return a.value4;
            }),
            item.map((a) => {
              return a.value5;
            }),
            item.map((a) => {
              return a.value6;
            }),
            item.map((a) => {
              return a.value7;
            }),
            "政策性银行",
            "大型国有银行",
            "股份制商业银行",
            "地方法人金融机构",
            "政策性银行比今年年初增速",
            "大型国有银行比今年年初增速",
            "股份制商业银行比今年年初增速",
            "地方法人金融机构比今年年初增速 ",
            "bar",
            "bar",
            "bar",
            "bar",
            "line",
            "line",
            "line",
            "line",
            "csjrhjEcharts" + i,
            "30",
            "单位：亿元"
          );
        }
      },
      clickTab2(i) {
        this.tabnum2 = i;
        const item = this.data2[4];
        const myChart = echarts.getInstanceByDom(
          document.getElementById("gyfzzbEcharts4")
        );

        let option = myChart.getOption();
        let xdata, ydata1, ydata2, name1, name2, type1, type2, dom, dw;
        xdata = item.map((a) => {
          return a.name;
        });

        name1 = "";
        name2 = "";
        type2 = "bar";
        type2 = "bar";
        dw = "%";
        if (i == 0) {
          ydata1 = item.map((a) => {
            return a.value;
          });
          ydata2 = [];
        } else {
          ydata2 = item.map((a) => {
            return a.value;
          });
          ydata1 = [];
        }
        option = {
          color: ["#D6E7F9", "#2DF09F"],
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            formatter: (paramss) => {
              var htmlStr = "";
              var seriesName = paramss[0].axisValueLabel; //图例名称
              htmlStr += seriesName + "<br/>";
              paramss.forEach((params, i) => {
                // var params = paramss[0]
                var color = params.color; //图例颜色

                var xName = params.seriesName; //x轴的名称
                var value = Math.abs(params.value); //y轴值
                // var htmlStr = '<div>';

                htmlStr +=
                  '<span style="margin-right: 5px; font-size: 16pt; font-family: Consolas;display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color:' +
                  color +
                  ' ;"></span>';
                htmlStr += "<span style='min-height: 20pt; font-size: 20pt'>";
                htmlStr += xName + " " + value + dw;
                htmlStr += "</span>";
                htmlStr +=
                  "<span style='min-height: 20pt; font-size: 20pt; margin-left: 20px'>";
                // console.log(params.data.length);
                // if (!value) {
                //   htmlStr += value + '%';
                // } else {
                //   htmlStr += value[params.seriesIndex + 1] + '万元';//选择对应value的坐标
                // }
                htmlStr += "</span>";
                htmlStr += "</div>";
              });
              return htmlStr;
            },

            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "28",
            },
            // formatter: function (params) {
            //     console.log(params);
            //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
            //     for (var i = 0; i < params.length; i++) {
            //       if (params[i].data != undefined) {
            //         res +=
            //           "<p>" +
            //           params[i].marker +
            //           params[i].seriesName+" "+
            //           params[i].data +
            //           "万件" +
            //           "</p>";
            //       }
            //     }
            //     return res;
            //   },
          },
          legend: {
            orient: "horizontal",
            // icon: "circle",
            top: 10,
            itemGap: 45,
            textStyle: {
              color: "#D6E7F9",
              fontSize: 36,
            },
          },
          grid: {
            left: "5%",
            right: "24%",
            top: "10%",
            bottom: "10%",
            containLabel: true,
          },
          xAxis: [
            {
              name: "   单位：" + dw,
              nameTextStyle: {
                fontSize: 36,
                color: "#D6E7F9",
                padding: 12,
              },
              type: "value",
              // data: xdata,
              nameTextStyle: {
                fontSize: 36,
                color: "#D6E7F9",
                padding: 10,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                // formatter: function (value) {
                //   return Math.abs(value);   //负数取绝对值变正数
                // },

                textStyle: {
                  fontSize: 36,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          yAxis: [
            {
              type: "category",
              data: xdata,
              nameTextStyle: {
                fontSize: 36,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                textStyle: {
                  fontSize: 36,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: "",
              type: "category",
              max: 10,
              nameTextStyle: {
                fontSize: 36,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: "{value}%",
                textStyle: {
                  fontSize: 36,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              name: name1,
              type: type1,
              barWidth: "40%",
              stack: "total",
              label: {
                show: true,
                position: "inside",
                formatter: function (params) {
                  return Math.abs(params.value) + dw;
                },
              },
              // smooth: true,
              // yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color: "#00C0FF",
                  barBorderRadius: 0,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata1,
              areaStyle: {
                //填充
                color: "#00C0FF",
                opacity: 1,
              },
            },
            {
              name: name2,
              type: type2,
              barWidth: "40%",
              label: {
                show: true,
                position: "inside",
                formatter: function (params) {
                  return Math.abs(params.value) + dw;
                },
              },
              stack: "total",
              // smooth: true,
              // yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color: "#2DF09F",
                  barBorderRadius: 0,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata2,
              // areaStyle:"",
            },
          ],
        };
        myChart.setOption(option);
      },
      clickTab3(i) {
        this.tabnum3 = i;
        const myChart = echarts.getInstanceByDom(
          document.getElementById("gyfzzbEcharts5")
        );
        let option = myChart.getOption();
        let data;
        if (i == 0) {
          data = this.data2[5];
        } else if (i == 1) {
          data = [this.data2[5][2], this.data2[5][0], this.data2[5][1]];
        } else {
          data = [this.data2[5][2], this.data2[5][1], this.data2[5][0]];
        }
        option = {
          color: [
            "#00C0FF",
            "#22E8E8",
            "#FFD461",
            "#A9DB52",
            "#B76FD8",
            "#FD852E",
            "#FF4949",
            "#0594C3",
            "#009D9D",
            "#A47905",
          ],

          tooltip: {
            trigger: "item",
            // formatter: '{b}: <br/> {d}%',
            formatter: "{b}: <br/> {c}个<br/> {d}%",
            borderWidth: 0,
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "25",
            },
          },
          legend: {
            orient: "vertical",
            left: "40%",
            top: "5%",
            bottom: "0%",
            icon: "circle",
            itemGap: 30,
            textStyle: {
              rich: {
                name: {
                  fontSize: 25,
                  color: "#ffffff",
                  padding: [0, 20, 0, 15],
                },
                value: {
                  fontSize: 25,
                  color: "#2CC6F9",
                  // padding: [10, 0, 0, 15]
                },
              },
            },
            formatter: function (name) {
              var data = option.series[0].data; //获取series中的data
              var total = 0;
              var tarValue = 0;
              for (var i = 0, l = data.length; i < l; i++) {
                total += Number(data[i].value);
                if (data[i].name == name) {
                  tarValue = data[i].value;
                }
              }
              this.serverNum = total;
              var p = ((tarValue / total) * 100).toFixed(2);
              return "{name|" + name + "}{value|" + tarValue + "  " + p + "%}";
            },
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: ["30%", "40%"],
              center: ["22%", "50%"],
              roseType: "",
              label: {
                show: false,
              },
              data: data,
              // emphasis: {
              //   itemStyle: {
              //     shadowBlur: 10,
              //     shadowOffsetX: 0,
              //     shadowColor: 'rgba(0, 0, 0, 0.5)'
              //   }
              // },
              // label: {
              //   normal: {
              //     formatter: "{b} {d}%  ",
              //     textStyle: {
              //       color: "#fff",
              //       fontSize: 22,
              //     }
              //   }
              // }
            },
          ],
        };
        myChart.setOption(option);
      },
      clickTab4(i) {
        this.tabnum4 = i;
        const item = this.data4[1];
        let xdata,
          ydata1,
          ydata2,
          ydata3,
          ydata4,
          name1,
          name2,
          name3,
          name4,
          type1,
          type2,
          type3,
          type4,
          rotate,
          dw,
          dom;

        dom = "tjdsjzbEcharts1";
        name1 = "指标";
        name2 = "";
        name3 = "产值";
        name4 = "";
        type1 = "bar";
        type2 = "bar";
        type3 = "line";
        type4 = "line";
        rotate = "30";
        dw = "单位：万元";

        if (i == 0) {
          xdata = item.map((a) => {
            return a.name;
          });
          ydata1 = item.map((a) => {
            return a.value;
          });
          ydata2 = [];
          ydata3 = item.map((a) => {
            return a.value1;
          });
          ydata4 = [];
        } else {
          xdata = item.map((a) => {
            return a.name;
          });
          ydata1 = item.map((a) => {
            return a.value1;
          });
          ydata2 = [];
          ydata3 = item.map((a) => {
            return a.value;
          });
          ydata4 = [];
        }
        this.changeData4Chart(item, {
          xdata,
          ydata1,
          ydata2,
          ydata3,
          ydata4,
          name1,
          name2,
          name3,
          name4,
          type1,
          type2,
          type3,
          type4,
          rotate,
          dw,
          dom,
        });
      },
      clickTab5(i) {
        this.tabnum5 = i;
        const item = this.data4[2];
        let xdata,
          ydata1,
          ydata2,
          ydata3,
          ydata4,
          name1,
          name2,
          name3,
          name4,
          type1,
          type2,
          type3,
          type4,
          rotate,
          dw,
          dom;

        dom = "tjdsjzbEcharts2";
        name1 = "指标";
        name2 = "";
        name3 = "产值";
        name4 = "";
        type1 = "bar";
        type2 = "bar";
        type3 = "line";
        type4 = "line";
        rotate = "30";
        dw = "单位：万元";

        if (i == 0) {
          xdata = item.map((a) => {
            return a.name;
          });
          ydata1 = item.map((a) => {
            return a.value;
          });
          ydata2 = [];
          ydata3 = item.map((a) => {
            return a.value1;
          });
          ydata4 = [];
        } else {
          xdata = item.map((a) => {
            return a.name;
          });
          ydata1 = item.map((a) => {
            return a.value1;
          });
          ydata2 = [];
          ydata3 = item.map((a) => {
            return a.value;
          });
          ydata4 = [];
        }
        this.changeData4Chart(item, {
          xdata,
          ydata1,
          ydata2,
          ydata3,
          ydata4,
          name1,
          name2,
          name3,
          name4,
          type1,
          type2,
          type3,
          type4,
          rotate,
          dw,
          dom,
        });
      },
      clickTab6(i) {
        this.tabnum6 = i;
        const item = this.data4[3];
        let xdata,
          ydata1,
          ydata2,
          ydata3,
          ydata4,
          name1,
          name2,
          name3,
          name4,
          type1,
          type2,
          type3,
          type4,
          rotate,
          dw,
          dom;

        dom = "tjdsjzbEcharts3";
        name1 = "指标";
        name2 = "";
        name3 = "产值";
        name4 = "";
        type1 = "bar";
        type2 = "bar";
        type3 = "line";
        type4 = "line";
        rotate = "30";
        dw = "单位：万元";

        if (i == 0) {
          xdata = item.map((a) => {
            return a.name;
          });
          ydata1 = item.map((a) => {
            return a.value;
          });
          ydata2 = [];
          ydata3 = item.map((a) => {
            return a.value1;
          });
          ydata4 = [];
        } else {
          xdata = item.map((a) => {
            return a.name;
          });
          ydata1 = item.map((a) => {
            return a.value1;
          });
          ydata2 = [];
          ydata3 = item.map((a) => {
            return a.value;
          });
          ydata4 = [];
        }
        this.changeData4Chart(item, {
          xdata,
          ydata1,
          ydata2,
          ydata3,
          ydata4,
          name1,
          name2,
          name3,
          name4,
          type1,
          type2,
          type3,
          type4,
          rotate,
          dw,
          dom,
        });
      },
      changeData4Chart(item, options) {
        const {
          xdata,
          ydata1,
          ydata2,
          ydata3,
          ydata4,
          name1,
          name2,
          name3,
          name4,
          type1,
          type2,
          type3,
          type4,
          rotate,
          dw,
          dom,
        } = options;
        const myChart = echarts.getInstanceByDom(document.getElementById(dom));

        let option = myChart.getOption();
        option = {
          color: [
            "#00C0FF",
            "#22E8E8",
            "#FFD461",
            "#A9DB52",
            "#B76FD8",
            "#FD852E",
            "#FF4949",
            "#0594C3",
            "#009D9D",
            "#A47905",
          ],
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "28",
            },
            // formatter: function (params) {
            //     console.log(params);
            //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
            //     for (var i = 0; i < params.length; i++) {
            //       if (params[i].data != undefined) {
            //         res +=
            //           "<p>" +
            //           params[i].marker +
            //           params[i].seriesName+" "+
            //           params[i].data +
            //           "万件" +
            //           "</p>";
            //       }
            //     }
            //     return res;
            //   },
          },
          legend: {
            orient: "horizontal",
            // icon: "circle",
            top: 40,
            itemGap: 30,
            textStyle: {
              color: "#D6E7F9",
              fontSize: 34,
            },
          },
          grid: {
            left: "5%",
            right: "5%",
            top: "14%",
            bottom: "0%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: xdata,
              axisLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)", // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                rotate: rotate | 0,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 34,
                },
              },
            },
          ],
          yAxis: [
            {
              name: dw,
              type: "value",
              nameTextStyle: {
                fontSize: 34,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 34,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: dom == "tjdsjzbEcharts1" ? "万元" : "",
              type: "value",
              // max: 100,
              nameTextStyle: {
                fontSize: 34,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: dom == "tjdsjzbEcharts1" ? "{value}" : "{value}%",
                textStyle: {
                  fontSize: 34,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              name: name1,
              type: type1,
              barWidth: "10%",
              // smooth: true,
              yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#00C0FF",
                    },
                    {
                      offset: 1,
                      color: "rgba(0,192,255,0)",
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata1,
              areaStyle: {
                //填充
                color: "#00C0FF",
                opacity: 1,
              },
            },
            {
              name: name2,
              type: type2,
              barWidth: "10%",
              smooth: true,
              yAxisIndex: type2 == "bar" ? 0 : 1,
              itemStyle: {
                normal: {
                  color:
                    type2 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#2DF09F",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#2DF09F",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata2,
              // areaStyle:"",
            },
            {
              name: name3,
              type: type3,
              barWidth: "10%",
              smooth: true,
              yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type3 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#fff849",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#fff849",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata3,
              // areaStyle:"",
            },
            {
              name: name4,
              type: type4,
              barWidth: "10%",
              smooth: true,
              yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type4 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#B76FD8",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#B76FD8",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata4,
              // areaStyle:"",
            },
          ],
        };
        myChart.setOption(option);
      },
      // 两柱两折线
      barEcgarts1(
        xdata,
        ydata1,
        ydata2,
        ydata3,
        ydata4,
        name1,
        name2,
        name3,
        name4,
        type1,
        type2,
        type3,
        type4,
        dom,
        rotate,
        dw
      ) {
        let myChart = echarts.init(document.getElementById(dom));
        let option = {
          color: [
            "#00C0FF",
            "#22E8E8",
            "#FFD461",
            "#A9DB52",
            "#B76FD8",
            "#FD852E",
            "#FF4949",
            "#0594C3",
            "#009D9D",
            "#A47905",
          ],
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "28",
            },
            // formatter: function (params) {
            //     console.log(params);
            //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
            //     for (var i = 0; i < params.length; i++) {
            //       if (params[i].data != undefined) {
            //         res +=
            //           "<p>" +
            //           params[i].marker +
            //           params[i].seriesName+" "+
            //           params[i].data +
            //           "万件" +
            //           "</p>";
            //       }
            //     }
            //     return res;
            //   },
          },
          legend: {
            orient: "horizontal",
            // icon: "circle",
            top: 40,
            itemGap: 20,
            textStyle: {
              color: "#D6E7F9",
              fontSize: 24,
            },
          },
          grid: {
            left: "5%",
            right: "5%",
            top: "24%",
            bottom: "15%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: xdata,
              axisLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)", // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                rotate: rotate | 0,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: dw,
              type: "value",
              nameTextStyle: {
                fontSize: 28,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: "",
              type: "value",
              // max: 100,
              nameTextStyle: {
                fontSize: 28,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: "{value}%",
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              name: name1,
              type: type1,
              barWidth: "20%",
              // smooth: true,
              yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#00C0FF",
                    },
                    {
                      offset: 1,
                      color: "rgba(0,192,255,0)",
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata1,
              areaStyle: {
                //填充
                color: "#00C0FF",
                opacity: 1,
              },
            },
            {
              name: name2,
              type: type2,
              barWidth: "20%",
              smooth: true,
              yAxisIndex: type2 == "bar" ? 0 : 1,
              itemStyle: {
                normal: {
                  color:
                    type2 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#2DF09F",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#2DF09F",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata2,
              // areaStyle:"",
            },
            {
              name: name3,
              type: type3,
              barWidth: "20%",
              smooth: true,
              yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type3 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#fff849",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#fff849",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata3,
              // areaStyle:"",
            },
            {
              name: name4,
              type: type4,
              barWidth: "20%",
              smooth: true,
              yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type4 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#B76FD8",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#B76FD8",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata4,
              // areaStyle:"",
            },
          ],
        };
        myChart.setOption(option);
        myChart.getZr().on("mousemove", (param) => {
          myChart.getZr().setCursorStyle("default");
        });
      }, // 横向柱图
      // 两柱两折线-右侧大图
      barEcgarts1right(
        xdata,
        ydata1,
        ydata2,
        ydata3,
        ydata4,
        name1,
        name2,
        name3,
        name4,
        type1,
        type2,
        type3,
        type4,
        dom,
        rotate,
        dw
      ) {
        let myChart = echarts.init(document.getElementById(dom));
        let option = {
          color: [
            "#00C0FF",
            "#22E8E8",
            "#FFD461",
            "#A9DB52",
            "#B76FD8",
            "#FD852E",
            "#FF4949",
            "#0594C3",
            "#009D9D",
            "#A47905",
          ],
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "28",
            },
            // formatter: function (params) {
            //     console.log(params);
            //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
            //     for (var i = 0; i < params.length; i++) {
            //       if (params[i].data != undefined) {
            //         res +=
            //           "<p>" +
            //           params[i].marker +
            //           params[i].seriesName+" "+
            //           params[i].data +
            //           "万件" +
            //           "</p>";
            //       }
            //     }
            //     return res;
            //   },
          },
          legend: {
            orient: "horizontal",
            // icon: "circle",
            top: 40,
            itemGap: 30,
            textStyle: {
              color: "#D6E7F9",
              fontSize: 34,
            },
          },
          grid: {
            left: "5%",
            right: "5%",
            top: "14%",
            bottom: "0%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: xdata,
              axisLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)", // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                rotate: rotate | 0,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 34,
                },
              },
            },
          ],
          yAxis: [
            {
              name: dw,
              type: "value",
              nameTextStyle: {
                fontSize: 34,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 34,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: dom == "tjdsjzbEcharts1" ? "万元" : "",
              type: "value",
              // max: 100,
              nameTextStyle: {
                fontSize: 34,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: dom == "tjdsjzbEcharts1" ? "{value}" : "{value}%",
                textStyle: {
                  fontSize: 34,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              name: name1,
              type: type1,
              barWidth: "10%",
              // smooth: true,
              yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#00C0FF",
                    },
                    {
                      offset: 1,
                      color: "rgba(0,192,255,0)",
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata1,
              areaStyle: {
                //填充
                color: "#00C0FF",
                opacity: 1,
              },
            },
            {
              name: name2,
              type: type2,
              barWidth: "10%",
              smooth: true,
              yAxisIndex: type2 == "bar" ? 0 : 1,
              itemStyle: {
                normal: {
                  color:
                    type2 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#2DF09F",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#2DF09F",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata2,
              // areaStyle:"",
            },
            {
              name: name3,
              type: type3,
              barWidth: "10%",
              smooth: true,
              yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type3 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#fff849",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#fff849",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata3,
              // areaStyle:"",
            },
            {
              name: name4,
              type: type4,
              barWidth: "10%",
              smooth: true,
              yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type4 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#B76FD8",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#B76FD8",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata4,
              // areaStyle:"",
            },
          ],
        };
        myChart.setOption(option);
        myChart.getZr().on("mousemove", (param) => {
          myChart.getZr().setCursorStyle("default");
        });
      }, // 横向柱图

      // 四柱
      barEcgarts2(
        xdata,
        ydata1,
        ydata2,
        ydata3,
        ydata4,
        name1,
        name2,
        name3,
        name4,
        type1,
        type2,
        type3,
        type4,
        dom,
        rotate,
        dw
      ) {
        let myChart = echarts.init(document.getElementById(dom));
        let option = {
          color: [
            "#00C0FF",
            "#22E8E8",
            "#FFD461",
            "#A9DB52",
            "#B76FD8",
            "#FD852E",
            "#FF4949",
            "#0594C3",
            "#009D9D",
            "#A47905",
          ],
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "28",
            },
            // formatter: function (params) {
            //     console.log(params);
            //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
            //     for (var i = 0; i < params.length; i++) {
            //       if (params[i].data != undefined) {
            //         res +=
            //           "<p>" +
            //           params[i].marker +
            //           params[i].seriesName+" "+
            //           params[i].data +
            //           "万件" +
            //           "</p>";
            //       }
            //     }
            //     return res;
            //   },
          },
          legend: {
            orient: "horizontal",
            // icon: "circle",
            top: 40,
            itemGap: 45,
            textStyle: {
              color: "#D6E7F9",
              fontSize: 24,
            },
          },
          grid: {
            left: "5%",
            right: "5%",
            top: "24%",
            bottom: "10%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: xdata,
              axisLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)", // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                rotate: rotate | 0,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: dw,
              type: "value",
              nameTextStyle: {
                fontSize: 28,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: "",
              type: "value",
              max: 100,
              nameTextStyle: {
                fontSize: 28,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: "{value}%",
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              name: name1,
              type: type1,
              barWidth: "20%",
              // smooth: true,
              yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#00C0FF",
                    },
                    {
                      offset: 1,
                      color: "rgba(0,192,255,0)",
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata1,
              areaStyle: {
                //填充
                color: "#00C0FF",
                opacity: 1,
              },
            },
            {
              name: name2,
              type: type2,
              barWidth: "20%",
              smooth: true,
              yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color:
                    type2 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#2DF09F",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#2DF09F",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata2,
              // areaStyle:"",
            },
            {
              name: name3,
              type: type3,
              barWidth: "20%",
              smooth: true,
              // yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type3 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#fff849",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#fff849",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata3,
              // areaStyle:"",
            },
            {
              name: name4,
              type: type4,
              barWidth: "20%",
              smooth: true,
              // yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type4 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#B76FD8",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#B76FD8",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata4,
              // areaStyle:"",
            },
          ],
        };
        myChart.setOption(option);
        myChart.getZr().on("mousemove", (param) => {
          myChart.getZr().setCursorStyle("default");
        });
      }, // 横向柱图
      // 四柱四折线
      barEcgarts3(
        xdata,
        ydata1,
        ydata2,
        ydata3,
        ydata4,
        ydata5,
        ydata6,
        ydata7,
        ydata8,
        name1,
        name2,
        name3,
        name4,
        name5,
        name6,
        name7,
        name8,
        type1,
        type2,
        type3,
        type4,
        type5,
        type6,
        type7,
        type8,
        dom,
        rotate,
        dw
      ) {
        if (!this.jgcdkChart) {
          this.jgcdkChart = echarts.init(document.getElementById(dom));
        }

        let option = {
          color: [
            "#00C0FF",
            "#22E8E8",
            "#FFD461",
            "#A9DB52",
            "#B76FD8",
            "#FD852E",
            "#FF4949",
            "#0594C3",
            "#009D9D",
            "#A47905",
          ],
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "28",
            },
            // formatter: function (params) {
            //     console.log(params);
            //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
            //     for (var i = 0; i < params.length; i++) {
            //       if (params[i].data != undefined) {
            //         res +=
            //           "<p>" +
            //           params[i].marker +
            //           params[i].seriesName+" "+
            //           params[i].data +
            //           "万件" +
            //           "</p>";
            //       }
            //     }
            //     return res;
            //   },
          },
          legend: {
            orient: "horizontal",
            // icon: "circle",
            top: 10,
            itemGap: 4,
            textStyle: {
              color: "#D6E7F9",
              fontSize: 24,
            },
          },
          grid: {
            left: "5%",
            right: "5%",
            top: "24%",
            bottom: "10%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: xdata,
              axisLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)", // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                rotate: rotate | 0,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: dw,
              type: "value",
              nameTextStyle: {
                fontSize: 28,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: "",
              type: "value",
              max: 100,
              nameTextStyle: {
                fontSize: 28,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: "{value}%",
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              name: name1,
              type: type1,
              barWidth: "20%",
              // smooth: true,
              yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#00C0FF",
                    },
                    {
                      offset: 1,
                      color: "rgba(0,192,255,0)",
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata1,
              areaStyle: {
                //填充
                color: "#00C0FF",
                opacity: 1,
              },
            },
            {
              name: name2,
              type: type2,
              barWidth: "20%",
              smooth: true,
              yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color:
                    type2 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#2DF09F",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#2DF09F",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata2,
              // areaStyle:"",
            },
            {
              name: name3,
              type: type3,
              barWidth: "20%",
              smooth: true,
              // yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type3 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#fff849",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#fff849",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata3,
              // areaStyle:"",
            },
            {
              name: name4,
              type: type4,
              barWidth: "20%",
              smooth: true,
              // yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type4 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#B76FD8",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#B76FD8",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata4,
              // areaStyle:"",
            },
            {
              name: name5,
              type: type5,
              barWidth: "20%",
              smooth: true,
              // yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type5 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#FD852E",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#FD852E",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata5,
              // areaStyle:"",
            },
            {
              name: name6,
              type: type6,
              barWidth: "20%",
              smooth: true,
              // yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type6 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#FF4949",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#FF4949",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata6,
              // areaStyle:"",
            },
            {
              name: name7,
              type: type7,
              barWidth: "20%",
              smooth: true,
              // yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type7 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#0594C3",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#0594C3",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata7,
              // areaStyle:"",
            },
            {
              name: name8,
              type: type8,
              barWidth: "20%",
              smooth: true,
              yAxisIndex: 1,
              itemStyle: {
                normal: {
                  color:
                    type8 == "bar"
                      ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                          {
                            offset: 0,
                            color: "#009D9D",
                          },
                          {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                          },
                        ])
                      : "#009D9D",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata8,
              // areaStyle:"",
            },
          ],
        };
        this.jgcdkChart.clear();
        console.log("option", option);
        this.jgcdkChart.setOption(option, true);
        this.jgcdkChart.getZr().on("mousemove", (param) => {
          this.jgcdkChart.getZr().setCursorStyle("default");
        });
      }, // 横向柱图      // 无指示线饼图
      barEcgarts4(data, dom) {
        let myChart = echarts.init(document.getElementById(dom));
        let option = {
          color: [
            "#00C0FF",
            "#22E8E8",
            "#FFD461",
            "#A9DB52",
            "#B76FD8",
            "#FD852E",
            "#FF4949",
            "#0594C3",
            "#009D9D",
            "#A47905",
          ],

          tooltip: {
            trigger: "item",
            // formatter: '{b}: <br/> {d}%',
            formatter: "{b}: <br/> {c}个<br/> {d}%",
            borderWidth: 0,
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "25",
            },
          },
          legend: {
            orient: "vertical",
            left: "40%",
            top: "5%",
            bottom: "0%",
            icon: "circle",
            itemGap: 30,
            textStyle: {
              rich: {
                name: {
                  fontSize: 25,
                  color: "#ffffff",
                  padding: [0, 20, 0, 15],
                },
                value: {
                  fontSize: 25,
                  color: "#2CC6F9",
                  // padding: [10, 0, 0, 15]
                },
              },
            },
            formatter: function (name) {
              var data = option.series[0].data; //获取series中的data
              var total = 0;
              var tarValue = 0;
              for (var i = 0, l = data.length; i < l; i++) {
                total += Number(data[i].value);
                if (data[i].name == name) {
                  tarValue = data[i].value;
                }
              }
              this.serverNum = total;
              var p = ((tarValue / total) * 100).toFixed(2);
              return "{name|" + name + "}{value|" + tarValue + "  " + p + "%}";
            },
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: ["30%", "40%"],
              center: ["22%", "50%"],
              roseType: "",
              label: {
                show: false,
              },
              data: data,
              // emphasis: {
              //   itemStyle: {
              //     shadowBlur: 10,
              //     shadowOffsetX: 0,
              //     shadowColor: 'rgba(0, 0, 0, 0.5)'
              //   }
              // },
              // label: {
              //   normal: {
              //     formatter: "{b} {d}%  ",
              //     textStyle: {
              //       color: "#fff",
              //       fontSize: 22,
              //     }
              //   }
              // }
            },
          ],
        };

        myChart.setOption(option);
        myChart.getZr().on("mousemove", (param) => {
          myChart.getZr().setCursorStyle("default");
        });
      },
      // 三折线
      barEcgarts5(
        xdata,
        ydata1,
        ydata2,
        ydata3,
        name1,
        name2,
        name3,
        dom,
        rotate,
        dw
      ) {
        let myChart = echarts.init(document.getElementById(dom));
        let option = {
          color: [
            "#00C0FF",
            "#22E8E8",
            "#FFD461",
            "#A9DB52",
            "#B76FD8",
            "#FD852E",
            "#FF4949",
            "#0594C3",
            "#009D9D",
            "#A47905",
          ],
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "28",
            },
            // formatter: function (params) {
            //     console.log(params);
            //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
            //     for (var i = 0; i < params.length; i++) {
            //       if (params[i].data != undefined) {
            //         res +=
            //           "<p>" +
            //           params[i].marker +
            //           params[i].seriesName+" "+
            //           params[i].data +
            //           "万件" +
            //           "</p>";
            //       }
            //     }
            //     return res;
            //   },
          },
          legend: {
            orient: "horizontal",
            // icon: "circle",
            top: 40,
            itemGap: 45,
            textStyle: {
              color: "#D6E7F9",
              fontSize: 24,
            },
          },
          grid: {
            left: "5%",
            right: "5%",
            top: "24%",
            bottom: "15%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: xdata,
              axisLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)", // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                rotate: rotate | 0,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: dw,
              type: "value",
              nameTextStyle: {
                fontSize: 28,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: "",
              type: "value",
              // max: 100,
              nameTextStyle: {
                fontSize: 28,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: "{value}%",
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              name: name1,
              type: "line",
              barWidth: "20%",
              // smooth: true,
              yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color: "#00C0FF",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata1,
              // areaStyle: { //填充
              //   color: "#00C0FF",
              //   opacity: 1
              // }
            },
            {
              name: name2,
              type: "line",
              barWidth: "20%",
              smooth: true,
              yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color: "#2DF09F",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata2,
              // areaStyle:"",
            },
            {
              name: name3,
              type: "line",
              barWidth: "20%",
              smooth: true,
              yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color: "#fff849",
                  barBorderRadius: 4,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata3,
              // areaStyle:"",
            },
          ],
        };
        myChart.setOption(option);
        myChart.getZr().on("mousemove", (param) => {
          myChart.getZr().setCursorStyle("default");
        });
      }, // 横向柱图
      // 横向两侧展开柱图
      barEcgarts6(xdata, ydata1, ydata2, name1, name2, type1, type2, dom, dw) {
        let myChart = echarts.init(document.getElementById(dom));
        let option = {
          color: ["#D6E7F9", "#2DF09F"],
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            formatter: (paramss) => {
              var htmlStr = "";
              var seriesName = paramss[0].axisValueLabel; //图例名称
              htmlStr += seriesName + "<br/>";
              paramss.forEach((params, i) => {
                // var params = paramss[0]
                var color = params.color; //图例颜色

                var xName = params.seriesName; //x轴的名称
                var value = Math.abs(params.value); //y轴值
                // var htmlStr = '<div>';

                htmlStr +=
                  '<span style="margin-right: 5px; font-size: 16pt; font-family: Consolas;display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color:' +
                  color +
                  ' ;"></span>';
                htmlStr += "<span style='min-height: 20pt; font-size: 20pt'>";
                htmlStr += xName + " " + value + dw;
                htmlStr += "</span>";
                htmlStr +=
                  "<span style='min-height: 20pt; font-size: 20pt; margin-left: 20px'>";
                // console.log(params.data.length);
                // if (!value) {
                //   htmlStr += value + '%';
                // } else {
                //   htmlStr += value[params.seriesIndex + 1] + '万元';//选择对应value的坐标
                // }
                htmlStr += "</span>";
                htmlStr += "</div>";
              });
              return htmlStr;
            },

            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "28",
            },
            // formatter: function (params) {
            //     console.log(params);
            //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
            //     for (var i = 0; i < params.length; i++) {
            //       if (params[i].data != undefined) {
            //         res +=
            //           "<p>" +
            //           params[i].marker +
            //           params[i].seriesName+" "+
            //           params[i].data +
            //           "万件" +
            //           "</p>";
            //       }
            //     }
            //     return res;
            //   },
          },
          legend: {
            orient: "horizontal",
            // icon: "circle",
            top: 10,
            itemGap: 45,
            textStyle: {
              color: "#D6E7F9",
              fontSize: 36,
            },
          },
          grid: {
            left: "5%",
            right: "24%",
            top: "10%",
            bottom: "10%",
            containLabel: true,
          },
          xAxis: [
            {
              name: "   单位：" + dw,
              nameTextStyle: {
                fontSize: 36,
                color: "#D6E7F9",
                padding: 12,
              },
              type: "value",
              // data: xdata,
              nameTextStyle: {
                fontSize: 36,
                color: "#D6E7F9",
                padding: 10,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                // formatter: function (value) {
                //   return Math.abs(value);   //负数取绝对值变正数
                // },

                textStyle: {
                  fontSize: 36,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          yAxis: [
            {
              type: "category",
              data: xdata,
              nameTextStyle: {
                fontSize: 36,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                textStyle: {
                  fontSize: 36,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: "",
              type: "category",
              max: 10,
              nameTextStyle: {
                fontSize: 36,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: "{value}%",
                textStyle: {
                  fontSize: 36,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              name: name1,
              type: type1,
              barWidth: "40%",
              stack: "total",
              label: {
                show: true,
                position: "inside",
                formatter: function (params) {
                  return Math.abs(params.value) + dw;
                },
              },
              // smooth: true,
              // yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color: "#00C0FF",
                  barBorderRadius: 0,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata1,
              areaStyle: {
                //填充
                color: "#00C0FF",
                opacity: 1,
              },
            },
            {
              name: name2,
              type: type2,
              barWidth: "40%",
              label: {
                show: true,
                position: "inside",
                formatter: function (params) {
                  return Math.abs(params.value) + dw;
                },
              },
              stack: "total",
              // smooth: true,
              // yAxisIndex: 0,
              itemStyle: {
                normal: {
                  color: "#2DF09F",
                  barBorderRadius: 0,
                },
              },
              // label: {
              //   show: true,
              //   fontSize: 18,
              //   fontWeight: "bold",
              //   color: "#fff",
              //   marginTop: 15,
              //   position: "top",
              // },
              data: ydata2,
              // areaStyle:"",
            },
          ],
        };
        myChart.setOption(option);
        myChart.getZr().on("mousemove", (param) => {
          myChart.getZr().setCursorStyle("default");
        });
      },
      // 饼图 有指示线
      barEcgarts7(data, dom) {
        let myChart = echarts.init(document.getElementById(dom));
        let option = {
          color: [
            "#00C0FF",
            "#22E8E8",
            "#FFD461",
            "#A9DB52",
            "#B76FD8",
            "#FD852E",
            "#FF4949",
            "#0594C3",
            "#009D9D",
            "#A47905",
          ],

          tooltip: {
            trigger: "item",
            // formatter: '{b}: <br/> {d}%',
            formatter: "{b}: <br/> {c}个<br/> {d}%",
            borderWidth: 0,
            backgroundColor: "rgba(0, 0, 0, 0.6)",
            textStyle: {
              color: "white",
              fontSize: "25",
            },
          },
          legend: {
            orient: "vertical",
            left: "55%",
            top: "30%",
            bottom: "0%",
            icon: "circle",
            itemGap: 30,
            textStyle: {
              rich: {
                name: {
                  fontSize: 36,
                  color: "#ffffff",
                  padding: [0, 20, 0, 15],
                },
                value: {
                  fontSize: 36,
                  color: "#2CC6F9",
                  // padding: [10, 0, 0, 15]
                },
              },
            },
            formatter: function (name) {
              var data = option.series[0].data; //获取series中的data
              var total = 0;
              var tarValue = 0;
              for (var i = 0, l = data.length; i < l; i++) {
                total += Number(data[i].value);
                if (data[i].name == name) {
                  tarValue = data[i].value;
                }
              }
              this.serverNum = total;
              var p = ((tarValue / total) * 100).toFixed(2);
              return (
                "{name|" + name + "}{value|" + tarValue + "个  " + p + "%}"
              );
            },
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: ["30%", "40%"],
              center: ["30%", "50%"],
              roseType: "",
              label: {
                show: false,
              },
              data: data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: "rgba(0, 0, 0, 0.5)",
                },
              },
              label: {
                normal: {
                  formatter: "{b} {d}%  ",
                  textStyle: {
                    color: "#fff",
                    fontSize: 36,
                  },
                },
              },
            },
          ],
        };
        myChart.setOption(option);
        myChart.getZr().on("mousemove", (param) => {
          myChart.getZr().setCursorStyle("default");
        });
      },
    },
  });
</script>
