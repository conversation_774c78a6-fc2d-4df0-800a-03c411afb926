﻿<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>电子地图调用demo</title>
<link href="ol.css"  rel="stylesheet">
<script src="ol.js"></script>
<style type="text/css">
#map {
	position: absolute;
	width: 100%;
	height: 100%;
	overflow: hidden;
	left: 0;
	top: 0;
}
</style>
</head>
<body>
	<div id="map" class="map"></div>
	<script>
		var time=Date.now();
		
		var projection = ol.proj.get("EPSG:4326");
		var projectionExtent = projection.getExtent();
		var size = ol.extent.getWidth(projectionExtent) / 256;
		var resolutions = [];
		var matrixIds = [];
		
		for (var z = 0; z < 21; ++z) {
			resolutions[z] = size / Math.pow(2, z);
			matrixIds[z] = z;
		}
		
		var origin = ol.extent.getTopLeft(projectionExtent);
		
		//电子地图wmts地址
		var dzdt_wmts_Url ='http://*************:9000/tileMap/services/MapServer/other_jh_dark0324/tile/WMTS';	
	

		//电子地图xyz地址
		var dzdt_xyz_Url  ='http://*************:9000/tileMap/services/MapServer/other_jh_dark0324/tile/otherF/{z}/{y}/{x}';
		
		//电子地图图层wmts方式	
		var dzdtWMTSLayer = new ol.layer.Tile({
				source: new ol.source.WMTS({
							name : "电子地图",
							url : dzdt_wmts_Url,
							layer : "img",
							style : "default",
							matrixSet : "c",
							format : "tiles",
							wrapX : true,
							tileGrid : new ol.tilegrid.WMTS(
									{
										origin : origin,
										resolutions : resolutions,
										matrixIds : matrixIds
									})
					}),
		});
		
		
		//电子地图图层XYZ方式		
		var dzdtXYZlayer = new ol.layer.Tile({
			   source: new ol.source.XYZ({
				url:dzdt_xyz_Url,
				crossOrigin: 'anonymous',
				projection: 'EPSG:4326'
			  }),
			  visible: true
		});
		
        
		
		var map = new ol.Map({
			layers: [
			  //dzdtWMTSLayer,
			  dzdtXYZlayer,
								],
			target : "map",
			view : new ol.View({
				//center : [94.1988631773,31.5769936469],
				center : [119.65,29.08],
				projection : projection,
				zoom : 13,
				maxZoom : 20,
				minZoom :1
			})
		});
		
	</script>
</body>
</html>