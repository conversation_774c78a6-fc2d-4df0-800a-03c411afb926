<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <title>城市安全运行指标分析-右</title>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <script src="/Vue/vue.js"></script>
        <script src="/static/js/jslib/vue-count-to.min.js"></script>
        <script src="/echarts/echarts.min.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/datav.min.vue.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <style>
            * {
                margin: 0;
                padding: 0;
            }
            #app {
                width: 1050px;
                height: 1930px;
                background: url("/img/left-bg.png") no-repeat;
                background-size: 100% 100%;
                padding: 30px;
                box-sizing: border-box;
                overflow: hidden;
            }
            .header-title2[data-v-4d0d1712] {
                width: 100% !important;
            }
            .main_list {
                width: 420px !important;
                height: 130px !important;
                margin: 10px 30px;
                background: linear-gradient(
                    to right,
                    rgba(5, 28, 104, 0.527),
                    rgba(33, 62, 143, 0.61),
                    rgba(37, 95, 160, 0.541)
                );
                text-align: center;
                padding-top: 40px;
            } /* 下拉框 */
            .select {
                position: absolute;
                left: 862px;
                top: 420px;
            }
            .el-select {
                width: 106px;
            }
            .el-input__inner {
                height: 50px !important;
                width: 150px !important;
                background-color: #00487f;
                color: #fff;
                font-size: 28px;
            }
            .el-select-dropdown {
                border: 1px solid #2578a6;
                background-color: #032f46d3;
            }
            .el-select-dropdown__item.hover,
            .el-select-dropdown__item:hover {
                background-color: #00487f;
            }
            .el-select-dropdown__item {
                color: #fff;
                background-color: #00487f;
                font-size: 28px;
                height: 50px;
                line-height: 50px;
            }
            .el-select-dropdown__list {
                background-color: #00487f;
            }
            .el-select .el-input .el-select__caret {
                position: relative;
                left: 40px;
                font-size: 28px;
                color: #fff;
            }
            .el-select .el-input__inner {
                /* border-radius: 30px !important; */
            }
            .el-scrollbar {
                width: 150px;
            }
            .el-input.is-disabled .el-input__inner {
                background-color: #2d4a67;
            }
        </style>
    </head>
    <body>
        <div id="app">
            <div class="s-flex s-flex-wrap s-row-between">
                <dv-border-box-8 class="main_list" v-for="(item,index) in list" :key="index">
                    <p class="s-font-30 s-c-white">{{item.name}}</p>
                    <p class="s-font-30 s-c-white">
                        <count-to
                            :start-val="0"
                            :end-val="item.value"
                            :duration="3000"
                            class="s-c-yellow-gradient s-font-40"
                        ></count-to>
                        {{item.unit}}
                    </p>
                </dv-border-box-8>
            </div>
            <nav class="s-m-t-10 s-m-b-10">
                <s-header-title title="城市安全事件变化趋势分析" htype="2"></s-header-title>
            </nav>
            <div class="select">
                <el-select v-model="value" @change="selectMothFun" placeholder="年份">
                    <el-option v-for="item,index in options" :key="index" :label="item" :value="item"> </el-option>
                </el-select>
            </div>
            <div id="bar_eh" style="width: 100%; height: 610px"></div>

            <nav class="s-m-t-10 s-m-b-10">
                <s-header-title title="安全生产事故年度态势分析" htype="2"></s-header-title>
            </nav>
            <div id="line_eh" style="width: 100%; height: 610px"></div>
        </div>

        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script>
            let vm = new Vue({
                el: "#app",
                data: {
                    list: [],
                    value: "2020",
                    options: ["2020", "2021", "2022"],
                },
                mounted() {
                    this.initApi();
                },
                methods: {
                    initApi() {
                        $get("/3840/shgl/csaqyx/csaqyx01.json", { type1: 1 }).then((res) => {
                            this.list = res.slice(4);
                        });
                        $get("/3840/shgl/csaqyx/csaqyx09.json", { year: "2020" }).then((res) => {
                            this.getBar("bar_eh", res);
                        });
                        $get("/3840/shgl/csaqyx/csaqyx06.json", { type1: 6 }).then((res) => {
                            this.getLine("line_eh", res);
                        });
                        // $api("ldst_shgl_csaqyx", { type1: 1 }).then((res) => {
                        //     this.list = res.slice(4);
                        // });
                        // $api("ldst_shgl_csaqyx5", { year: "2020" }).then((res) => {
                        //     this.getBar("bar_eh", res);
                        // });

                        // $api("ldst_shgl_csaqyx", { type1: 6 }).then((res) => {
                        //     this.getLine("line_eh", res);
                        // });
                    },
                    selectMothFun(e) {
                        $api("ldst_shgl_csaqyx5", { year: e }).then((res) => {
                            this.getBar("bar_eh", res);
                        });
                    },

                    getBar(id, echartsData) {
                        const myChartsRun = echarts.init(document.getElementById(id));
                        let xdata = [],
                            ydata1 = [],
                            ydata2 = [],
                            line = [];
                        echartsData.map((ele) => {
                            xdata.push(ele.name);
                            ydata1.push(ele.gfq);
                            ydata2.push(ele.dfq);
                            line.push(ele.qs);
                        });

                        var option = {
                            tooltip: {
                                //提示框组件
                                trigger: "axis",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                textStyle: {
                                    color: "white",
                                    fontSize: "24",
                                },
                            },
                            grid: {
                                left: "1%",
                                right: "2%",
                                bottom: "5%",
                                top: "12%",
                                containLabel: true,
                            },
                            legend: {
                                itemGap: 16,
                                itemWidth: 20,
                                itemHeight: 20,
                                textStyle: {
                                    color: "#fff",
                                    fontStyle: "normal",
                                    fontFamily: "微软雅黑",
                                    fontSize: 24,
                                },
                            },
                            xAxis: [
                                {
                                    type: "category",
                                    boundaryGap: true, //坐标轴两边留白
                                    data: xdata,
                                    axisLabel: {
                                        color: "#fff",
                                        fontSize: "26px",
                                    },
                                    axisLine: {
                                        show: true,
                                        lineStyle: {
                                            color: "#bbb",
                                        },
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#195384",
                                        },
                                    },
                                },
                            ],
                            yAxis: [
                                {
                                    type: "value",
                                    name: "单位：起",
                                    splitNumber: 5,
                                    nameTextStyle: {
                                        color: "#fff",
                                        fontSize: 24,
                                        padding: [10, 0, 20, 0],
                                    },
                                    axisLabel: {
                                        formatter: "{value}",
                                        textStyle: {
                                            color: "#fff",
                                            fontSize: "24px",
                                        },
                                    },
                                    axisLine: {
                                        lineStyle: {
                                            color: "#fff",
                                        },
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#5087EC",
                                        },
                                    },
                                },
                                {
                                    type: "value",
                                    name: "单位：%",
                                    nameTextStyle: {
                                        color: "#fff",
                                        fontSize: 24,
                                        padding: [0, 0, 20, 50],
                                    },
                                    axisLabel: {
                                        formatter: "{value}%",
                                        textStyle: {
                                            color: "#fff",
                                            fontSize: "24px",
                                        },
                                    },
                                    axisLine: {
                                        lineStyle: {
                                            color: "#fff",
                                        },
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#5087EC",
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    name: "高发区",
                                    type: "bar",
                                    data: ydata1,
                                    barWidth: 20,
                                    itemStyle: {
                                        //图形样式
                                        normal: {
                                            barBorderRadius: [5, 5, 0, 0],
                                            color: new echarts.graphic.LinearGradient(
                                                0,
                                                0,
                                                0,
                                                1,
                                                [
                                                    {
                                                        offset: 1,
                                                        color: "rgba(50, 150, 250, 0.1)",
                                                    },
                                                    {
                                                        offset: 0.5,
                                                        color: "rgba(50, 150, 250, 0.5)",
                                                    },
                                                    {
                                                        offset: 0,
                                                        color: "rgba(50, 150, 250, 1)",
                                                    },
                                                ],
                                                false
                                            ),
                                        },
                                    },
                                },
                                {
                                    name: "底发区",
                                    type: "bar",
                                    data: ydata2,
                                    barWidth: 20,
                                    itemStyle: {
                                        //图形样式
                                        normal: {
                                            barBorderRadius: [5, 5, 0, 0],
                                            color: new echarts.graphic.LinearGradient(
                                                0,
                                                0,
                                                0,
                                                1,
                                                [
                                                    {
                                                        offset: 1,
                                                        color: "rgba(62, 208, 178, 0.1)",
                                                    },
                                                    {
                                                        offset: 0.5,
                                                        color: "rgba(62, 208, 178, 0.5)",
                                                    },
                                                    {
                                                        offset: 0,
                                                        color: "rgba(62, 208, 178, 1)",
                                                    },
                                                ],
                                                false
                                            ),
                                        },
                                    },
                                },
                                {
                                    name: "趋势",
                                    type: "line",
                                    data: line,
                                    yAxisIndex: 1,
                                    itemStyle: {
                                        normal: {
                                            color: "#ffd767",
                                        },
                                    },
                                },
                            ],
                        };
                        myChartsRun.setOption(option);
                        tools.loopShowTooltip(myChartsRun, option, {
                            loopSeries: true,
                        }); //轮播
                    },
                    getLine(id, echartsData) {
                        const myChartsRun = echarts.init(document.getElementById(id));

                        let color = ["#0090FF", "#36CE9E", "#FFC005", "#FF515A", "#8B5CFF", "#00CA69"];

                        let xAxisData = echartsData.map((v) => v.name);
                        let yAxisData1 = echartsData.map((v) => v.value);
                        const hexToRgba = (hex, opacity) => {
                            let rgbaColor = "";
                            let reg = /^#[\da-f]{6}$/i;
                            if (reg.test(hex)) {
                                rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
                                    "0x" + hex.slice(3, 5)
                                )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
                            }
                            return rgbaColor;
                        };

                        let option = {
                            color: color,
                            legend: {
                                itemGap: 16,
                                itemWidth: 20,
                                itemHeight: 20,
                                textStyle: {
                                    color: "#fff",
                                    fontStyle: "normal",
                                    fontFamily: "微软雅黑",
                                    fontSize: 24,
                                },
                            },
                            tooltip: {
                                trigger: "axis",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                formatter: function (params) {
                                    let html = "";
                                    params.forEach((v) => {
                                        html += `<div style="color: #fff;font-size: 20px;line-height: 24px">
                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                    color[v.componentIndex]
                };"></span>
                ${v.seriesName}.${v.name}
                <span style="color:${color[v.componentIndex]};font-weight:700;font-size: 24px">${v.value}</span>
                万元`;
                                    });

                                    return html;
                                },
                            },
                            grid: {
                                top: "20%",
                                left: "1%",
                                right: "1%",
                                bottom: "1%",
                                containLabel: true,
                            },
                            xAxis: [
                                {
                                    type: "category",
                                    boundaryGap: true, //坐标轴两边留白
                                    data: xAxisData,
                                    axisLabel: {
                                        color: "#fff",
                                        fontSize: "26px",
                                    },
                                    axisLine: {
                                        show: true,
                                        lineStyle: {
                                            color: "#bbb",
                                        },
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#195384",
                                        },
                                    },
                                },
                            ],
                            yAxis: [
                                {
                                    type: "value",
                                    splitNumber: 5,
                                    name: "单位：起",
                                    nameTextStyle: {
                                        color: "#fff",
                                        fontSize: 24,
                                        padding: [10, 0, 20, 0],
                                    },
                                    axisLabel: {
                                        formatter: "{value}",
                                        textStyle: {
                                            color: "#fff",
                                            fontSize: "24px",
                                        },
                                    },
                                    axisLine: {
                                        lineStyle: {
                                            color: "#fff",
                                        },
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#5087EC",
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    name: "业务量",
                                    type: "line",
                                    smooth: true,
                                    // showSymbol: false,/
                                    symbolSize: 12,
                                    zlevel: 3,
                                    lineStyle: {
                                        normal: {
                                            color: color[0],
                                            shadowBlur: 3,
                                            shadowColor: hexToRgba(color[0], 0.5),
                                            shadowOffsetY: 8,
                                        },
                                    },
                                    areaStyle: {
                                        normal: {
                                            color: new echarts.graphic.LinearGradient(
                                                0,
                                                0,
                                                0,
                                                1,
                                                [
                                                    {
                                                        offset: 0,
                                                        color: hexToRgba(color[0], 0.3),
                                                    },
                                                    {
                                                        offset: 1,
                                                        color: hexToRgba(color[0], 0.1),
                                                    },
                                                ],
                                                false
                                            ),
                                            shadowColor: hexToRgba(color[0], 0.1),
                                            shadowBlur: 10,
                                        },
                                    },
                                    data: yAxisData1,
                                },
                            ],
                        };

                        myChartsRun.setOption(option);
                        tools.loopShowTooltip(myChartsRun, option, {
                            loopSeries: true,
                        }); //轮播
                    },
                },
            });
        </script>
    </body>
</html>
