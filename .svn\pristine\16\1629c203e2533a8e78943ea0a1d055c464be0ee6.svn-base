<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>双随机一公开弹窗</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <!-- <link rel="stylesheet" href="/static/citybrain/ggfw/css/zjfw-dialog.css" /> -->
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
</head>
<style>
    /* 放心消费弹窗 */
    .container {
        width: 1390px;
        height: 970px;
        background-color: #031827;
        box-shadow: -3px 2px 35px 0px #000000;
        border: 1px solid #359cf8;
        border-radius: 60px;
    }

    .container .head {
        width: 100%;
        height: 100px;
        line-height: 100px;
        background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%), linear-gradient(#ffffff, #ffffff);
        background-blend-mode: normal, normal;
        padding: 10px 50px;
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        justify-content: space-between;
        border-top-left-radius: 60px;
        border-top-right-radius: 60px;
    }

    .head span {
        font-size: 48px !important;
        font-weight: 500;
        color: #fff;
        font-weight: bold;
    }

    .head .img {
        display: inline-block;
        margin: 20px;
        float: right;
        width: 34px;
        height: 34px;
        background-image: url(/static/citybrain/csdn/img/cstz2-middle/close-hover.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }

    .content {
        width: 100%;
        height: calc(100% - 100px);
        padding: 60px 20px;
        box-sizing: border-box;
    }

    .tj-con {
        width: 100%;
        height: 230px;
        background: url(/static//citybrain/shgl/img/dwjcy-bottom.png) no-repeat;
        background-size: 100% 30%;
        background-position: 0 125px;
        display: flex;
        justify-content: space-between;
    }

    .tj-con li {
        width: 200px;
        height: 158px;
        text-align: center;
        background: url(/static/citybrain/shgl/img/yqfk_5.png) no-repeat;
        background-size: 100% 100%;
        list-style: none;
    }

    .el-radio-group {
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-top: 20px;
    }

    .el-radio__label {
        font-size: 30px;
        color: rgba(255, 255, 255, 0.568);
    }

    .el-radio__inner {
        width: 30px;
        height: 30px;
        margin-bottom: 8px;
        background-color: #303c57;
    }

    .el-radio__input.is-checked+.el-radio__label {
        color: #fff !important;
    }

    .el-radio__input.is-checked .el-radio__inner {
        background-color: #303c57;
    }

    .el-radio__inner::after {
        width: 12px;
        height: 12px;
        background-color: #1684fc;
    }
</style>

<body>
    <div id="app" class="container">
        <div class="head">
            <span>双随机一公开</span>
            <div class="img" @click="closeDialog"></div>
        </div>
        <div class="content">
            <div class="tj-con">
                <li v-for="(item,index) in tjData" :key="index">
                    <div class="s-c-blue-gradient s-font-35" style="margin-top: 30px">
                        {{item.value}}{{item.unit}}
                    </div>
                    <div class="s-c-blue-gradient s-font-22" style="margin-top: 75px">{{item.name}}</div>
                </li>
            </div>
            <el-radio-group v-model="value" @change="changeRadio" style="margin-bottom: 20px">
                <el-radio v-for="item in radioData" :label="item.value">{{item.name}}</el-radio>
            </el-radio-group>
            <div id="barEcharts02" style="height: 450px; width: 100%"></div>
        </div>
    </div>
</body>
<script type="module">
    new Vue({
            el: "#app",
            data: {
                tjData: [],
                value: "1",
                radioData: [
                    { name: "月度", value: "1" },
                    { name: "季度", value: "2" },
                    { name: "年度", value: "3" },
                ],
            },
            methods: {
                changeRadio(item) {
                    console.log(item);
                    this.getData(item);
                },
                closeDialog() {
                    top.commonObj.funCloseIframe({
                        name: "ssj_dialog",
                    });
                },
                getData(value) {
                    $api("shgl_ssj").then((res) => {
                        let result = res.filter((item) => {
                            return value == item.type;
                        });
                        this.BarchartsShow("barEcharts02", result);
                    });
                },
                init() {
                    $api("shgl_ssjygk").then((res) => {
                        this.tjData = res;
                    });
                    this.getData("1");
                },
                //绘制柱图
                BarchartsShow(id, data) {
                    const myChartsDivine = echarts.init(document.getElementById(id));
                    let x = data.map((item) => {
                        return item.name;
                    });
                    let y = data.map((item) => {
                        return item.value;
                    });
                    let y1 = data.map((item) => {
                        return item.value1;
                    });
                    let option = {
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },

                        grid: {
                            left: "8%",
                            top: "18%",
                            right: "8%",
                            bottom: "10%",
                        },
                        legend: {
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                        },
                        xAxis: {
                            // data: x,
                            data:[2020,2021,2022,2023,2024,2025],
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: "#aaa",
                                    width: 1,
                                },
                            },
                            axisTick: {
                                show: true, //隐藏X轴刻度
                                alignWithLabel: true,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff", //X轴文字颜色
                                    fontSize: 28,
                                },
                                interval: 0,
                                // rotate: 30,
                            },
                        },
                        yAxis: [
                            {
                                type: "value",
                                name: "",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        width: 1,
                                        color: "#3d5269",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                            {
                                type: "value",
                                name: "",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                position: "right",
                                splitLine: {
                                    show: false,
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#fff",
                                        width: 2,
                                    },
                                },
                                axisLabel: {
                                    show: true,
                                    formatter: "{value} ", //右侧Y轴文字显示
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "随机抽查事项数",
                                type: "bar",
                                barWidth: 70,
                                color: "#5087EC",
                                itemStyle: {
                                    normal: {
                                        color: "#5087EC",
                                    },
                                },
                                data: y,
                            },

                            {
                                name: "抽查率",
                                type: "line",
                                yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                                showAllSymbol: true, //显示所有图形。
                                // symbol: "circle", //标记的图形为实心圆
                                symbolSize: 10, //标记的大小
                                itemStyle: {
                                    normal: {
                                        color: "#26D9FF",
                                        lineStyle: {
                                            color: "orange",
                                            width: 4,
                                        },
                                    },
                                },
                                data: y1,
                            },
                            {
                                name: "公示率",
                                type: "line",
                                yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                                showAllSymbol: true, //显示所有图形。
                                // symbol: "circle", //标记的图形为实心圆
                                symbolSize: 10, //标记的大小
                                itemStyle: {
                                    normal: {
                                        color: "#26D9FF",
                                        lineStyle: {
                                            color: "red",
                                            width: 4,
                                        },
                                    },
                                },
                                data: y,
                            },
                        ],
                    };

                    myChartsDivine.setOption(option);
                    tools.loopShowTooltip(myChartsDivine, option, {
                        loopSeries: true,
                    }); //轮播
                },
            },
            //项目生命周期
            mounted() {
                this.init();
            },
        });
    </script>

</html>