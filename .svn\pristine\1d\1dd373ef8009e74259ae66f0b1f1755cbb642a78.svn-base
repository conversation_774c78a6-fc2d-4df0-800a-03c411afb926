/*
 * @Author: hdw <EMAIL>
 * @Date: 2022-05-17 15:38:47
 * @LastEditors: hdw <EMAIL>
 * @LastEditTime: 2022-05-19 14:33:01
 * @FilePath: \cszz1\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
function init(dom, data) {
    let uuid = dom.getAttribute('data-uuid');
    console.log(data);
    // 关闭弹窗
    dom.querySelector('.head .img').onclick = () => {
        Vex.Work.sprite.pointOverlay.removePopup(uuid);
    }
    dom.querySelector('.head span').innerHTML = data.name||'婺城区'
    let domDiv =  dom.querySelector('.county .body ul')
    for (let i = 0; i < data.key.length; i++) {
        domDiv.innerHTML += `
        <li>
            <i class="imgPoint"></i>
            <span class="title">${data.key[i]}：</span>
            <span>${data.value[i]||'--'}</span>
        </li>
        `;
    }
}