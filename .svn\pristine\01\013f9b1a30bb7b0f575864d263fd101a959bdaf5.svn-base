<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>交通管理-弹框1</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/jslib/vue-count-to.min.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/datav.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    </head>
    <style>
        [v-cloak] {
            display: none;
        }
        html,
        body,
        ul,
        p {
            padding: 0;
            margin: 0;
            list-style: none;
        }
        .container {
            width: 400px;
            height: 420px;
            box-sizing: border-box;
            padding: 20px;
            /* background: #131328; */
            background: url("/img/left-bg.png") no-repeat;
            background-size: 100% 100%;
            border-radius: 10px;
        }

        /* 下拉 */
        .select {
            display: inline-block;
            width: 280px;
            height: 40px;
            position: relative;
            left: 30px;
        }

        .flow-icon {
            width: 25px;
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .flow-icon1 {
            margin-top: -5px;
            transform: rotateX(180deg);
        }

        .ul > div {
            width: 100%;
            height: 40px;
            line-height: 40px;
        }

        .select ul {
            width: 90%;
            height: 240px;
            text-align: center;
            font-size: 24px;
            color: #fefefe;
            overflow-y: auto;
            display: none;
            list-style: none;
            margin: 0 15px;
            padding: 0;
            position: absolute;
        }

        .select > span {
            display: block;
            font-size: 26px;
            color: #fff;
            position: absolute;
            top: -40px;
            left: 65px;
        }

        .ul {
            width: 100%;
            height: 40px;
            text-align: center;
            font-size: 26px;
            color: #fefefe;
            background-color: #132c4e;
            border: 1px solid #359cf8;
            border-radius: 40px;
            margin-top: 25px;
        }

        .select ul > li {
            width: 100%;
            height: 40px;
            line-height: 40px;
            background-color: #132c4ec2;
            box-sizing: border-box;
        }

        .select ul > li:hover {
            background-color: #359cf8;
        }

        .ul-active {
            display: block !important;
        }

        .ul-active > li:last-of-type {
            border-radius: 0 0 20px 20px;
        }

        .select ul::-webkit-scrollbar {
            /*滚动条整体样式*/
            width: 6px;
            /*高宽分别对应横竖滚动条的尺寸*/
            height: 1px;
            /* scrollbar-arrow-color: red; */
        }

        .select ul::-webkit-scrollbar-thumb {
            border-radius: 6px;
            /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
            background: #20aeff;
            height: 10px;
        }

        .iconList {
            width: 100%;
            height: 100px;
            margin-top: 20px;
            display: flex;
            justify-content: space-evenly;
        }
        .icon_item {
            width: 100px;
            height: 100px;
            font-size: 28px;
            color: #fff;
            background: #102455;
            border-radius: 8px;
        }
        .icon_item:hover {
            background: #1e439d;
        }
        .icon_item_active {
            background: #1e439d;
        }
        .icon_item > img {
            width: 40px;
            height: 40px;
            margin: 10px 30px;
        }
        .icon_item > p {
            text-align: center;
            line-height: 23px;
        }

        .checkbox-box {
            display: block;
            position: relative;
            height: 50px;
            line-height: 59px;
        }
        .checkbox-box:hover {
            background: #132c4e;
        }
        .el-checkbox {
            margin-right: 0;
        }
        .el-checkbox__label {
            font-size: 28px;
            color: #fff;
        }
        .el-checkbox__input {
            float: right;
            right: 30px;
            top: 18px;
            position: absolute;
        }
        .checkbox-box-img {
            width: 25px;
            height: 25px;
        }
    </style>

    <body>
        <div id="app" class="container" v-cloak>
            <div class="select" @click="showSelct=!showSelct">
                <div class="flow-icon" :class="showSelct?'flow-icon1':''">
                    <img src="/static/citybrain/hjbh/img/rkzt/up.png" alt="" width="25" />
                </div>
                <div class="ul" style="margin-top: 0">
                    <div style="cursor: pointer">{{startName}}</div>
                    <ul :class="[showSelct?'ul-active':'']">
                        <li style="cursor: pointer" v-for="(item,index) in flowList" @click="change(item)">
                            {{item.name}}
                        </li>
                    </ul>
                </div>
            </div>
            <div class="iconList">
                <div
                    :class="['icon_item',activeIcon==index?'icon_item_active':'']"
                    v-for="(item,index) in iconList"
                    :key="index"
                    @click="iconClickHandler(item,index)"
                >
                    <img
                        :src="'/static/citybrain/shgl/img/doing/jtgl-0'+(index+1)+'.png'"
                        alt=""
                        width="40"
                        height="40"
                    />
                    <p>{{item.name}}</p>
                </div>
            </div>
            <div style="margin-top: 20px; padding: 20px; box-sizing: border-box">
                <el-checkbox-group v-model="jtValue">
                    <el-checkbox
                        v-for="(item,index) in jtList"
                        :label="item.lable"
                        :key="index"
                        class="checkbox-box"
                        @change="statusChange(item)"
                    >
                        <img
                            class="checkbox-box-img"
                            :src="'/static/citybrain/shgl/img/doing/jtgl-'+item.lable+'.png'"
                            alt=""
                            width="28"
                            height="28"
                        />
                        <span style="margin-left: 15px">{{item.lable}}</span>
                    </el-checkbox>
                </el-checkbox-group>
            </div>
        </div>
    </body>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>

    <script>
        // window.parent.document.getElementById("map").contentWindow.Work.change3D(7);
        var vm = new Vue({
            el: "#app",
            data() {
                return {
                    activeIcon: 0,
                    showSelct: false,
                    startName: "今天",
                    flowList: [],
                    iconList: [
                        {
                            img: "",
                            name: "点位图",
                        },
                        {
                            img: "",
                            name: "热力图",
                        },
                        {
                            img: "",
                            name: "路况图",
                        },
                    ],
                    jtValue: [],
                    jtList: [],
                    params: {
                        day: "今天",
                        type: 1,
                        value: "",
                    },
                    list: [],
                    checkBoxList: [],
                };
            },
            mounted() {
                this.init();
                // this.addPoint()

                window.addEventListener("message", function (e) {
                    console.log("event_run预警--监听消息", e);
                    if (e.data && e.data.type == "pointClick" && e.data.data && e.data.data.data) {
                        let obj = JSON.parse(e.data.data.data);
                        if (obj.pointId == "point_UP") {
                            let objData = {
                                funcName: "customPop",
                                coordinates: obj.obj.gps.split(","),
                                closeButton: false,
                                html: `<div
                              onclick=" this.style.display = 'none'"
                    class="contain"
                    style="
                    position: absolute;
                      width: max-content;
                      display:inline-block;
                      background-color: rgba(0, 0, 0, 0.8);
                      border: 2px solid #00aae2;
                      box-sizing: border-box;
                      border-style: solid;
                      border-width: 4px;
                      border-image-source: linear-gradient(0deg, #32abe4 0%, #0b5aa4 100%);
                      border-image-slice: 1;
                    "
                  >
                    <div
                      class="title"
                      style="
                        background: linear-gradient(360deg, #096c8d, #073446);
                        width: 100%;
                        height: 60px;
                        font-size: 32px;
                        color: #fff;
                        padding:0 30px;
                        box-sizing: border-box;
                        line-height: 60px;
                      "
                    >
                     ${obj.obj.code}
                    </div>
                    <div
                      class="content"
                      style="
                        align-items: center;
                        font-size: 28px;
                        color: #fff;
                        padding: 20px;
                      "
                    >


                      <span style="display:inline-block;line-hight:30px;align-self: baseline;
                line-height: 55px;
            flex-shrink: 0"> 类型：${obj.obj.name}</span></br>
                  
                      <span style="display:inline-block;line-hight:30px;align-self: baseline;
                line-height: 55px;
            flex-shrink: 0"> 时间：${obj.obj.insert_time}</span></br>
                   
                      <span style="display:inline-block;line-hight:30px;align-self: baseline;
                line-height: 55px;
            flex-shrink: 0"> 事件数：${obj.obj.area_num}</span>
                   
                  </div>`,
                            };
                            window.parent.document
                                .getElementById("map")
                                .contentWindow.Work.funChange(JSON.stringify(objData));
                        }
                    }
                    // if (
                    //   Object.prototype.toString.call(e.data) === '[object Array]' &&
                    //   e.data[0].area_name
                    // ) {
                    //   that.pointYj(e.data)
                    // }
                    // if (e.data && e.data.type == 'gifClick' && e.data.data) {
                    //   if (e.data.data.pointId == 'csrk-gif') {
                    //     that.pointDiong(e.data.data.data)
                    //   }
                    // let dataObj = JSON.parse(e.data.data.data);
                    // console.log(dataObj);
                    // if (dataObj.pointId == "rkyj") {
                    //   that.pointDiong(dataObj.obj);
                    // }
                    // }
                });
            },
            methods: {
                statusChange(item) {
                    if (item.type == 2 && this.jtValue.length > 1) {
                        this.jtValue.splice(0, 1);
                    }

                    if (this.jtValue.includes(item.lable)) {
                        // 选中
                        console.log(111);
                        this.addMapFun(item);
                    } else {
                        // 取消
                        console.log(222);
                        console.log(item);

                        this.rmMapFun(item);
                    }
                },

                addMapFun(item) {
                    if (item.type == 1) {
                        // 参数该处加
                        $get("/shgl/doing/jtgl1_3").then((res) => {
                            res.filter((obj) => {
                                return obj.code == item.lable;
                            }).forEach((el) => {
                                this.addGif(el, item);
                            });
                        });
                    } else if (item.type == 2) {
                        this.hotMap(item);
                    }
                },
                rmMapFun(item) {
                    console.log(item);
                    if (item.type == 1) {
                        $get("/shgl/doing/jtgl1_3").then((res) => {
                            res.filter((obj) => {
                                return obj.code == item.lable;
                            }).forEach((el) => {
                                this.rmGif(item.lable);
                            });
                        });
                    } else if (item.type == 2) {
                        this.rmHotMap(item.name);
                    }
                },
                addGif(ele, item) {
                    console.log(item, ele);
                    // let urlGif =
                    //   baseURL.url + '/static/citybrain/shgl/img/pointIcon/rkyj.gif'
                    // // '../img/pointIcon/rkyj.gif'
                    // console.log(ele)
                    // // 动图预警上点
                    // // console.log(urlGif)
                    // let data = {
                    //   funcName: 'addGif',
                    //   id: 'gif' + item.lable,
                    //   width: 100,
                    //   height: 100,
                    //   url: urlGif,
                    //   coordinates: ele.gps.split(','),
                    //   properties: {
                    //     data: ele,
                    //     pointId: 'jtgl-gif',
                    //   },
                    // }
                    // window.parent.document
                    //   .getElementById('map')
                    //   .contentWindow.Work.funChange(JSON.stringify(data))
                    let pointdata = [{ data: { obj: ele, pointId: "point_UP" }, point: ele.gps }];
                    top.document.getElementById("map").contentWindow.Work.funChange(
                        JSON.stringify({
                            funcName: "pointLoad", //功能名称
                            pointType: "橙色预警事件", //点位类型图标
                            pointId: item.lable,
                            setClick: true,
                            pointData: pointdata,
                            imageConfig: { iconSize: 0.6 },
                        })
                    );
                },
                rmGif(id) {
                    console.log(id);

                    try {
                        // window.parent.document
                        //   .getElementById("map")
                        //   .contentWindow.Work.funChange(
                        //     JSON.stringify({
                        //       funcName: "removeGif", //加载多边形功能
                        //       id: "gif" + id, //dom的id
                        //     })
                        //   );
                        window.parent.document.getElementById("map").contentWindow.Work.funChange(
                            JSON.stringify({
                                funcName: "rmPoint", //加载多边形功能
                                pointId: id, //dom的id
                            })
                        );
                    } catch (error) {}
                },
                hotMap(item) {
                    let timeStr = "2022-09-18 00:00:00";
                    $api("/cstz_sjz_rlt_new", { date: timeStr }).then((res) => {
                        console.log(res);
                        let hotMapData = [];
                        let heatArr = [];
                        let len = res[0].heatmap.length;
                        let sumLen = 20000 - len;
                        if (len >= 20000) {
                            heatArr = res[0].heatmap.slice(0, 20000);
                        } else {
                            heatArr = res[0].heatmap;
                            for (let j = 0; j < sumLen; j++) {
                                let a = {
                                    count: 0,
                                    geohash: 0,
                                    lat: 0,
                                    lng: 0,
                                };
                                heatArr.push(a);
                            }
                        }
                        heatArr.map((item) => {
                            // 画热力图的数据
                            let pointArr = [];
                            pointArr[0] = item.lng;
                            pointArr[1] = item.lat;
                            pointArr[2] = item.count;
                            pointArr[3] = item.geohash;
                            hotMapData.push(pointArr);
                        });
                        console.log(hotMapData);
                        const mapData = {
                            funcName: "hotPowerMap",
                            hotPowerMapData: hotMapData,
                            offset: 256,
                            heatMapId: "bscztHot",
                            threshold: 6000,
                            distance: 800,
                            alpha: 0.3,
                        };
                        window.parent.document
                            .getElementById("map")
                            .contentWindow.Work.funChange(JSON.stringify(mapData));
                    });
                },
                rmAll() {
                    let that = this;
                    this.list.forEach((item) => {
                        if (item.type == 2) {
                            that.rmHotMap(item.label);
                        }
                    });

                    // 清理所有gif
                    this.list.forEach((item) => {
                        if (item.type == 1) {
                            that.rmGif(item.lable);
                        }
                    });
                },
                rmHotMap() {
                    window.parent.document.getElementById("map").contentWindow.Work.funChange(
                        JSON.stringify({
                            funcName: "rmhotPowerMap",
                            heatMapId: "bscztHot",
                        })
                    );
                },
                iconClickHandler(item, index) {
                    this.rmAll();
                    this.jtValue = [];

                    this.activeIcon = index;
                    switch (item.name) {
                        case "点位图":
                            this.params.type = 1;

                            break;
                        case "热力图":
                            this.params.type = 2;
                            break;
                        case "路况图":
                            this.params.type = 3;
                            break;
                        default:
                            break;
                    }
                    this.jtList = this.list.filter((item) => {
                        return item.type == this.params.type;
                    });
                },
                change(item) {
                    this.startName = item.name;

                    this.params.day = "今天";
                },
                init() {
                    $get("/shgl/doing/jtgl1_1").then((res) => {
                        this.flowList = res;
                    });

                    $api("shgl_doing_jtgl1_2").then((res) => {
                        this.jtList = res.filter((item) => {
                            return item.type == 1;
                        });
                        this.list = res;
                    });
                },
                addVideoPoint() {
                    let res = [
                        {
                            gps_x: "119.64748254784801",
                            gps_y: "29.078969905698866",
                        },
                        {
                            gps_x: "119.64848254784801",
                            gps_y: "29.078969905698866",
                        },
                    ];
                    let arr = res.map((item) => {
                        return {
                            data: [
                                {
                                    // pointId:"video",
                                    // obj:item,
                                    // video_code:item.chn_code
                                },
                            ],
                            point: item.gps_x + "," + item.gps_y,
                        };
                    });
                    console.log(arr);
                    top.document.getElementById("map").contentWindow.Work.funChange(
                        JSON.stringify({
                            funcName: "pointLoad",
                            pointType: "camera-load3", // 点位类型（图标名称）
                            pointId: "camera-load-index1", // 点位唯一id
                            height: "0",
                            pointData: arr,
                            setClick: true,
                            imageConfig: { iconSize: 0.5 },
                        })
                    );
                },
            },
        });
    </script>
</html>
