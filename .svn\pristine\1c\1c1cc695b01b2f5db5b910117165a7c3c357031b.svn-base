<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>智慧城管中间</title>
    <style>
        #app_mid {
            position: relative;
            top: 10px;
        }

        #zhcg_mid_box1 {
            width: 1291px;
            height: 322px;
            background-color: #0a2139;
            border-radius: 10px;
            border: solid 1px #008aff;
            position: absolute;
            left: 3199px;
            top: 13px;
        }

        .line {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
        }

        .linetitle {
            font-family: SourceHanSansSC-Regular;
            font-size: 31px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 41px;
            letter-spacing: 1px;
            color: #d6e7f9;
            white-space: nowrap;
        }

        #zhcg_mid_box2 {
            width: 1407px;
            height: 246px;
            background-color: #0a2139;
            border-radius: 10px;
            border: solid 1px #008aff;
            position: absolute;
            left: 3143px;
            display: flex;
            align-items: center;
            top: 1562px;
        }

        #zhcg_mid_box2>div {
            flex: 0.3333;
        }

        #zhcg_mid_box2>div>div:first-child {
            font-family: SourceHanSansCN-Regular;
            font-size: 47px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 47px;
            letter-spacing: 2px;
            color: #d6e7f9;
            text-align: center;
            margin-bottom: 40px;
        }

        #zhcg_mid_box2>div>div:last-child {
            font-family: AdobeHeitiStd-Regular;
            font-size: 82px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 82px;
            letter-spacing: 0px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-image: linear-gradient(360deg, #3883ff, #74b4f4, #f0ffff);
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
        }
    </style>

</head>

<body>
    <div id="app_mid">
        <div id="zhcg_mid_box1">
            <div class="line" style="justify-content: space-evenly;position: relative;top: 35px;">
                <div>
                    <div id="box2" style="width: 231px;height: 180px;position: relative;left: 10px;top: 10px;"></div>
                    <div class="linetitle" style="position: relative;right: -14px;text-align: center">{{data18.name}}
                    </div>
                    <div style="position: relative;bottom: 40px"><img src="./img/Base4.png" alt=""></div>
                    <img src="./static/citybrain/hjbh/img/common/dizuo.png" alt="" style="width: 337px;
                    height: 50px;
                    position: absolute;
                    margin-left: -30px;top: 173px;">
                </div>

                <div>
                    <div id="box3" style="width: 231px;height: 180px;position: relative;left: 10px;top: 10px;"></div>
                    <div class="linetitle" style="position: relative;right: -20px;text-align: center">{{data19.name}}
                    </div>
                    <div style="position: relative;bottom: 40px"><img src="./img/Base4.png" alt=""></div>
                    <img src="./static/citybrain/hjbh/img/common/dizuo.png" alt="" style="width: 337px;
                    height: 50px;
                    position: absolute;
                    margin-left: -30px;top: 173px;">
                </div>

                <div>
                    <div id="box4" style="width: 231px;height: 180px;position: relative;left: 10px;top: 10px;"></div>
                    <div class="linetitle" style="position: relative;right: -20px;text-align: center">{{data20.name}}
                    </div>
                    <div style="position: relative;bottom: 40px"><img src="./img/Base4.png" alt=""></div>
                    <img src="./static/citybrain/hjbh/img/common/dizuo.png" alt="" style="width: 337px;
                    height: 50px;
                    position: absolute;
                    margin-left: -30px;top: 173px;">
                </div>
            </div>
        </div>
        <div id="zhcg_mid_box2">
            <div v-for="item in data0">
                <div>{{item.name}}</div>
                <div>{{item.value}}<div style="font-size: 50px;margin-top: 20px;">起</div>
                </div>
            </div>
        </div>

    </div>



</body>

</html>
<!-- <script src="../js/vue.js"></script>
<script src="../js/echarts.min.js"></script> -->

<!-- index页面打开时 -->
<script src="/static/citybrain/hjbh/js/vue.js"></script>
<script src="/static/citybrain/hjbh/js/echarts.min.js"></script>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>

<script>
    console.log(window.location.pathname)
    window.location.pathname.split('szjh')[0]
    var vm = new Vue({
        el: '#app_mid',
        data: {
            data18: '', //立案率
            data19: '', //按期立案率
            data20: '', //按期结案率
            data0: []
        },
        mounted() {
            this.initFun()
        },
        methods: {

            initFun() {
                let that = this

                $api('/hjbh/zhcg_middle/zhcgMiddle001').then((res) => {
                    that.data0 = res
                })
                $api('/hjbh/zhcg_middle/zhcgMiddle002').then((res) => {
                    that.data18 = res[0]
                    that.data19 = res[1]
                    that.data20 = res[2]

                    this.init()
                })

            },
            init() {

                let myCharts2 = echarts.init(document.getElementById("box2"))
                let myCharts3 = echarts.init(document.getElementById("box3"))
                let myCharts4 = echarts.init(document.getElementById("box4"))
                let option2 = {
                    series: [

                        {

                            axisLabel: {
                                color: "#fff",
                                distance: 1,
                                fontSize: 15
                            },
                            pointer: {
                                show: true
                            },
                            type: 'gauge',
                            center: ['53%', '60%'],
                            radius: "120%",
                            data: [{
                                value: this.data18.value,
                            }],
                            axisLine: { // 坐标轴线，也就是圆圈的边框线
                                show: true, // 默认显示，属性show控制显示与否
                                lineStyle: { // 属性lineStyle控制线条样式
                                    color: [
                                        [1.5, '#2cd8fb']
                                    ],
                                    width: 10,
                                }
                            },
                            detail: { //仪表盘数值
                                textStyle: {
                                    color: "#fff"
                                }
                            },
                        }
                    ]
                }
                let option3 = {
                    series: [

                        {

                            axisLabel: {
                                color: "#fff",
                                distance: 1,
                                fontSize: 15
                            },
                            pointer: {
                                show: true
                            },
                            type: 'gauge',
                            center: ['53%', '60%'],
                            radius: "120%",
                            data: [{
                                value: this.data19.value,
                            }],
                            axisLine: { // 坐标轴线，也就是圆圈的边框线
                                show: true, // 默认显示，属性show控制显示与否
                                lineStyle: { // 属性lineStyle控制线条样式
                                    color: [
                                        [1.5, '#2cd8fb']
                                    ],
                                    width: 10,
                                }
                            },
                            detail: { //仪表盘数值
                                textStyle: {
                                    color: "#fff"
                                }
                            },
                        }
                    ]
                }
                let option4 = {
                    series: [

                        {

                            axisLabel: {
                                color: "#fff",
                                distance: 1,
                                fontSize: 15
                            },
                            pointer: {
                                show: true
                            },
                            type: 'gauge',
                            center: ['53%', '60%'],
                            radius: "120%",
                            data: [{
                                value: this.data20.value,
                            }],
                            axisLine: { // 坐标轴线，也就是圆圈的边框线
                                show: true, // 默认显示，属性show控制显示与否
                                lineStyle: { // 属性lineStyle控制线条样式
                                    color: [
                                        [1.5, '#2cd8fb']
                                    ],
                                    width: 10,
                                }
                            },
                            detail: { //仪表盘数值
                                textStyle: {
                                    color: "#fff"
                                }
                            },
                        }
                    ]
                }

                myCharts2.setOption(option2)
                myCharts3.setOption(option3)
                myCharts4.setOption(option4)
            }
        }




    })
</script>