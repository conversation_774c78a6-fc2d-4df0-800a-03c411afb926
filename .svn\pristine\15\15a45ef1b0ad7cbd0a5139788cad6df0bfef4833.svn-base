﻿<?xml version="1.0" encoding="utf-8"?>
<svg version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="269px" height="89px" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient gradientUnits="userSpaceOnUse" x1="1721" y1="132.5" x2="1452" y2="132.5" id="LinearGradient91">
      <stop id="Stop92" stop-color="#000000" stop-opacity="0" offset="0" />
      <stop id="Stop93" stop-color="#0c94e2" stop-opacity="0.8" offset="0.44" />
      <stop id="Stop94" stop-color="#0c94e2" stop-opacity="0.8" offset="1" />
    </linearGradient>
    <linearGradient gradientUnits="userSpaceOnUse" x1="1721" y1="132.5" x2="1452" y2="132.5" id="LinearGradient95">
      <stop id="Stop96" stop-color="#000000" stop-opacity="0" offset="0" />
      <stop id="Stop97" stop-color="#0c94e2" offset="0.23" />
      <stop id="Stop98" stop-color="#0c94e2" offset="1" />
    </linearGradient>
  </defs>
  <g transform="matrix(1 0 0 1 -1452 -88 )">
    <path d="M 1452 89.5  L 1721 89.5  L 1721 175.5  L 1452 175.5  L 1452 89.5  Z " fill-rule="nonzero" fill="url(#LinearGradient91)" stroke="none" />
    <path d="M 1452 89  L 1721 89  M 1721 176  L 1452 176  " stroke-width="2" stroke="url(#LinearGradient95)" fill="none" />
  </g>
</svg>