/*
 * @Author: CK
 * @email: <EMAIL>
 * @Date: 2022-07-29 17:07:31
 * @LastEditTime: 2022-07-30 16:36:48
 * @FilePath: \2DAnd3D\js\work\removePoint.js
 * @Description: 删除点位
 */

import { pointVariable } from '../globalVariable/mapFor2D.js';
import { popupVariable } from '../globalVariable/mapFor2D.js'

function removePointFunction (pointId,dataId,call) {
    if (pointId == dataId) {
        egis.removeLayer(pointId);
        egis.removeSource(pointId);
        call();
        pointVariable.pointLayer = pointVariable.pointLayer.filter(item => item.id != dataId)
    }
}

function removePoint (data) {
    const length  = pointVariable.pointLayer.length;
    // 判断是否有弹框
    if (popupVariable.tipsPointPopup) {
        popupVariable.tipsPointPopup.remove();
        popupVariable.tipsPointPopup = null;
    }
    if (data && data.id) {
        // 判断id是否为数组
        if (Array.isArray(data.id)) {
            const idLength = data.id.length
            for (let i = 0; i < idLength; i++) {
                for (let j = 0; j < length; j++) {
                    removePointFunction(pointVariable.pointLayer[j].id,data.id[i],() => {
                        pointVariable.pointLayer[j].click && egis.unce('click',pointVariable.pointLayer[j].click,pointVariable.pointLayer[j].id)
                    })
                }
            }
        } else {
            for (let i = 0; i < length; i++) {
                removePointFunction(pointVariable.pointLayer[i].id,data.id,() => {
                    pointVariable.pointLayer[i].click && egis.unce('click',pointVariable.pointLayer[i].click,pointVariable.pointLayer[i].id)
                })
            }
        }
    } else {
        for (let i = 0; i < length; i++) {
            pointVariable.pointLayer[i].click && egis.unce('click',pointVariable.pointLayer[i].click,pointVariable.pointLayer[i].id)
            egis.removeLayer(pointVariable.pointLayer[i].id);
            egis.removeSource(pointVariable.pointLayer[i].id);
            pointVariable.pointLayer = [];
        }
    }
}

export default removePoint
