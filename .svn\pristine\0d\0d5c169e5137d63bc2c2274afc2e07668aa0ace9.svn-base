class Tabs  {
  constructor () {
    this.tabsTigger = document.getElementById('tabs');
    this.mapUrls = mapConfig.bastLayers
    this.init();
  }
  init () {
    this.createdTab()
  }
  createdTab () {
    const length = this.mapUrls.length;
    for (let i = 0; i < length; i++) {
      this.tabsTigger.appendChild(this.getTabDom(this.mapUrls[i]))
    }
    this.getClick();
  }
  getTabDom (item) {
    const dom = document.createElement("span");
    for(let key in item) {
      if (key == 'name') {
        dom.innerText = item.name;
      } else if(key == 'id') {
        dom.setAttribute('usId',item[key])
      } else {
        dom.setAttribute(key,item[key])
      }
    }
    dom.classList.add('tabs-item');
    return dom
  }
  getClick () {
    const dom = document.getElementsByClassName('tabs-item');
    const length = dom.length
		for (let i = 0; i < length; i++) {
			dom[i].addEventListener('click',function () {
        const showId = this.getAttribute('usId')
        const a = new BastMap(egs,map);
				a.toggleBastLayer({
          layerUrl: '',
          id: showId

      })
			})
		}
  }
}
