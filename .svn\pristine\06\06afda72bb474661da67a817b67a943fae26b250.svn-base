<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Document</title>
    <script src="/Vue/vue.js"></script>
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/china.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>

    <style>
      #cstz_map_app {
        width: 3420px;
        height: 1900px;
        position: absolute;
        left: 0;
        z-index: 11;
        background-color: #082a4b;
      }
      #tab-first,
      #tab-second {
        height: 100px;
        font-size: 50px;
        line-height: 100px;
        padding-left: 20px;
      }
      .el-tabs__item,
      .is-top {
        color: #fff;
      }
      /* 省内省外tabs样式 */
      .el-tabs__active-bar {
        background-color: transparent !important;
      }

      /*去掉tabs底部的下划线*/
      .el-tabs__nav-wrap::after {
        position: static !important;
      }

      #tab-second {
        padding-right: 20px;
      }
      .el-tabs__item.is-active {
        color: #fff;
        background-color: #409eff;
      }
      .s-table {
        flex: 1;
        margin-top: 10px;
      }

      .s-table-head {
        /* width: 1309px; */
        height: 70px;
        background-color: #0e3a65;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-right: 4px;
      }

      .s-table-head-cell {
        /* flex: 0.169; */
        font-family: SourceHanSansCN-Regular;
        font-size: 32px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #77b3f1;
        text-align: center;
      }

      .s-table-body {
        height: 320px;
        overflow-y: auto;
      }

      .s-table-row {
        height: 61px;
        display: flex;
        margin-bottom: 4px;
        background-color: #0d223d;
        justify-content: center;
        align-items: center;
      }

      .s-table-body-cell {
        /* flex: 0.169; */
        font-family: SourceHanSansCN-Medium;
        font-size: 30px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #d6e7f9;
        text-align: center;
      }

      .s-table-body::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 4px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 4px;
        /* scrollbar-arrow-color: red; */
      }

      .s-table-body::-webkit-scrollbar-thumb {
        border-radius: 10px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
        background: #20aeff;
        height: 8px;
      }

      /* 表格自动滚动 */
      @keyframes rowUp {
        0% {
          -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
        }

        100% {
          transform: translate3d(0, -100%, 0);
          -webkit-transform: translate3d(0, -100%, 0);
          -moz-transform: translate3d(0, -100%, 0);
          -ms-transform: translate3d(0, -100%, 0);
          -o-transform: translate3d(0, -100%, 0);
        }
      }
    </style>
  </head>

  <body>
    <div id="cstz_map_app" v-cloak>
      <div class="qyt-map">
        <div
        id="mainMap3"
        style="width: 3420px; height:1855px; margin: 0 auto"
      ></div>
      </div>
    </div>

    <script src="/static/js/jslib/http.interceptor.js"></script>
    <!-- <script type="module" src="./js/cstz-left-new.js"></script> -->
    <script>
      var vm = new Vue({
        el: "#cstz_map_app",
        data: {
          qgMapData_in: [],
          qgMapData_out: [],
          qgMapDataPop_in: [],
          qgMapDataPop_out: [],
          peopleIn: [],
          peopleOut: [],
          tableData_in: [],
          tableData_out: [],
          myChart2: {},
          inToMapArr: [],
          outToMapArr: [],
          inProvince: 0,
          outProvince: 0,
          showMap: true,
          activeName: "first",
          top10Data: [],
          qgMapData: [],
          // 全省的地图
          showQxMap: false,
          qxMapData: [
            {
              coord: [119.393576, 30.057459],
              name: "杭州市",
              num: "",
              pm: "",
            },
            {
              coord: [121.409792, 29.700388],
              name: "宁波市",
              num: "",
              pm: "",
            },
            {
              coord: [121.114181, 28.772005],
              name: "台州市",
              num: "",
              pm: "",
            },
            {
              coord: [120.702112, 29.897117],
              name: "绍兴市",
              num: "",
              pm: "",
            },
            {
              coord: [120.672111, 28.000575],
              name: "温州市",
              num: "",
              pm: "",
            },
            {
              coord: [118.769723, 28.921163],
              name: "衢州市",
              num: "",
              pm: "",
            },
            {
              coord: [119.422711, 28.10005],
              name: "丽水市",
              num: "",
              pm: "",
            },
            // {
            //   coord: [120.750865, 30.762653],
            //   name: '嘉兴市',
            //   num: '',
            //   pm: '',
            // },
            // {
            //   coord: [120.102398, 30.867198],
            //   name: '湖州市',
            //   num: '',
            //   pm: '',
            // },
            // {
            //   coord: [122.106863, 30.016028],
            //   name: '舟山市',
            //   num: '',
            //   pm: '',
            // },
            {
              coord: [119.653436, 29.084634],
              name: "金华市",
              num: "",
              pm: "",
            },
          ],
        },
        created() {
          
            this.getInOrOut();
        },
        mounted() {
         
        },
        methods: {
          // 加载全国的echarts的地图
          drawQgMap(dom, chartsMapData, titleText) {
            let that = this;
            this.myChart2 = echarts.init(document.getElementById(dom));
            // this.myChart3 = echarts.init(document.getElementById('mainMap3'))
            let series = [];
            let outData = JSON.parse(JSON.stringify(this.qxMapData));
            let chinaGeoCoordMap = chartsMapData;
            let len = outData.length - 1;
            let chinaDatas = chartsMapData;
            console.log(chartsMapData)
            let option = {
            //   title: {
            //     text: titleText,
            //     left: "center",
            //     top: "50px",
            //     textStyle: {
            //       color: "#fff",
            //       fontSize: "50",
            //     },
            //   },
              tooltip: {
                show: true,
                trigger: "item",
                showDelay: 0,
                hideDelay: 0,
                enterable: true,
                transitionDuration: 0,
                extraCssText: "z-index:100",
                formatter: function (params, ticket, callback) {
                  //根据业务自己拓展要显示的内容
                  var res = "";
                  var name="";
                  var value="";
                  if(params.componentSubType=='effectScatter'){
                    name = params.data.name;
                    value = params.data.num;
                    }else{
                        name="-";
                        value="-"
                    }
                  
                  if (name != "金华") {
                    res = "<span>" + name + "-来金就诊总人数:" + value + "人</br>来金就诊病种:</br>高血压  20人</br>糖尿病  15人</br>冠心病 12人 </span>"  ;
                  } else {
                    res = "<span>" + name + "</span>";
                  }
                  return res;
                },
                textStyle: {
                  fontSize: "40",
                },
              },
              color: ["#e6d85b", "#e6d85b", "#e6d85b", "#e6d85b", "#e6d85b"],
              geo: {
                map: "china",
                zoom: 1.2,
                label: {
                  emphasis: {
                    show: false,
                  },
                },
                roam: true, //是否允许缩放
                itemStyle: {
                  normal: {
                    color: "rgba(51, 69, 89, .5)", //地图背景色
                    borderColor: "#00ffff", //省市边界线00fcff 516a89
                    borderWidth: 2,
                    opacity: 0.8,
                    shadowBlur: 20,
                    shadowColor: "#006dda",
                    shadowOffsetX: 5,
                    shadowOffsetY: 5,
                  },
                  emphasis: {
                    color: "rgba(37, 43, 61, .5)", //悬浮背景
                  },
                },
              },
              series: series,
            };

            [["金华", chinaDatas]].forEach(function (item, i) {
              series.push(
                {
                  type: "lines",
                  zlevel: 2,
                  effect: {
                    show: true,
                    period: 4, //箭头指向速度，值越小速度越快
                    trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
                    symbol: "arrow", //箭头图标
                    symbolSize: 30, //图标大小
                  },
                  lineStyle: {
                    normal: {
                      width: 6, //尾迹线条宽度
                      opacity: 1, //尾迹线条透明度
                      curveness: 0.3, //尾迹线条曲直度
                    },
                  },
                  data: that.convertData(
                    item[1],
                    chinaGeoCoordMap,
                    [119.653436, 29.084634],
                    dom
                  ),
                },
                {
                  type: "effectScatter",
                  coordinateSystem: "geo",
                  zlevel: 2,
                  rippleEffect: {
                    //涟漪特效
                    period: 4, //动画时间，值越小速度越快
                    brushType: "stroke", //波纹绘制方式 stroke, fill
                    scale: 4, //波纹圆环最大限制，值越大波纹越大
                  },
                  label: {
                    normal: {
                      show: true,
                      position: "right", //显示位置
                      // offset: [5, -20], //偏移设置
                      formatter: function (params) {
                        //圆环显示文字
                        // return (
                        //   params.data.pm +
                        //   "" +
                        //   params.data.name +
                        //   "{a| " +
                        //   params.data.num +
                        //   "}人"
                        // );
                        return params.data.name;
                        
                      },
                      color: "#fff",
                      fontSize: 36,
                      rich: {
                        a: {
                          color: "yellow",
                          fontSize: 36,
                        },
                      },
                    },
                    emphasis: {
                      show: true,
                    },
                  },
                  symbol: "circle",
                  symbolSize: function (val) {
                    // return 10 + val[2] * 5; //圆环大小
                    return 20; //圆环大小
                  },
                  data: item[1].map(function (dataItem) {
                    return {
                      name: dataItem.name,
                      value: dataItem.coord,
                      num: dataItem.num,
                      pm: dataItem.pm,
                      // value: chinaGeoCoordMap[dataItem.name].concat([dataItem.num])
                    };
                  }),
                },

                //被攻击点
                {
                  type: "scatter",
                  coordinateSystem: "geo",
                  zlevel: 2,
                  rippleEffect: {
                    period: 4,
                    brushType: "stroke",
                    scale: 4,
                  },
                  label: {
                    normal: {
                      show: true,
                      position: "left",
                      //offset:[5, 0],
                      color: "#fff",
                      formatter: "{b}",
                      textStyle: {
                        color: "#f00",
                        fontSize: 40,
                      },
                    },
                    emphasis: {
                      show: true,
                      color: "#f60",
                    },
                  },
                  symbol: "pin",
                  symbolSize: 60,
                  itemStyle: {
                    normal: {
                      color: "#f44336",
                    },
                  },
                  data: [
                    {
                      name: item[0],
                      value: outData[len].coord,
                      num: outData[len].num,
                      pm: outData[len].pm,
                    },
                  ],
                }
              );
            });
            this.myChart2.setOption(option);
            this.myChart2.getZr().on("mousemove", (param) => {
              this.myChart2.getZr().setCursorStyle("default");
            });
            // this.myChart3.setOption(option)
          },
          getInOrOut() {
            // 0流出 1流入
            $get("/ggfw/yb/ydjz").then((res) => {
              let arr = [];
              res.map((ele, index) => {
                let obj = {
                  name: ele.cityName,
                  num: ele.countNum,
                  coord: [ele.gps.split(",")[0], ele.gps.split(",")[1]],
                  pm: index + 1,
                };
                arr.push(obj);
              });
              this.qgMapData_in = this.qgMapDataPop_in = arr;
              this.drawQgMap(
                "mainMap3",
                this.qgMapData_in,
                "昨日省外城市人员流出情况"
              );
            });
          },
          convertData(data, chinaGeoCoordMap, id, dom) {
            // console.log(data)
            if (dom === "mainMap3") {
              var res = [];
              for (var i = 0; i < data.length; i++) {
                var dataItem = data[i];
                var fromCoord = dataItem.coord;
                var toCoord = id;
                if (fromCoord && toCoord) {
                  res.push([
                    {
                      coord: fromCoord,
                      value: dataItem.num,
                    },
                    {
                      coord: toCoord,
                    },
                  ]);
                }
              }
              return res;
            } else {
              var res = [];
              for (var i = 0; i < data.length; i++) {
                var dataItem = data[i];
                var fromCoord = dataItem.coord;
                var toCoord = id;
                if (fromCoord && toCoord) {
                  res.push([
                    {
                      coord: toCoord,
                      value: dataItem.num,
                    },
                    {
                      coord: fromCoord,
                    },
                  ]);
                }
              }
              return res;
            }
          },
        },
      });
    </script>
  </body>
</html>
