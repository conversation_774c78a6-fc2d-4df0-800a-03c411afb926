<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <title>特种设备详情</title>
  <script src="/Vue/vue.js"></script>
  <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
</head>
<style>
  .container {
    width: 900px;
    height: 1000px;
    background-color: #031827;
    box-shadow: -3px 2px 35px 0px #000000;
    border: 1px solid #359cf8;
    border-radius: 60px;
  }

  .container .head {
    width: 100%;
    height: 100px;
    line-height: 100px;
    background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%), linear-gradient(#ffffff, #ffffff);
    background-blend-mode: normal, normal;
    padding: 10px 50px;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    justify-content: space-between;
    border-top-left-radius: 60px;
    border-top-right-radius: 60px;
  }

  .head span {
    font-size: 48px !important;
    font-weight: 500;
    color: #fff;
    font-weight: bold;
  }

  .head .img {
    display: inline-block;
    margin: 20px;
    float: right;
    width: 34px;
    height: 34px;
    background-image: url(/static/citybrain/csdn/img/cstz2-middle/close-hover.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .content {
    width: 100%;
    height: calc(100% - 100px);
    /* display: flex;
      justify-content: space-between; */
    padding: 20px;
    box-sizing: border-box;
    color: #fff;
    font-size: 44px;
  }
  .table {
      /* width: 640px; */
      font-size: 30px;
      color: #fff;
      border: 2px solid #0075a5;
      border-collapse: collapse;
      margin-top:20px;
    }

    .table th {
      line-height: 70px;
      background-color: #021e4855;
      border-bottom: 1px solid #34445f;
      border-right: 1px solid #34445f;
    }

    .table th:last-child {
      border-right: none;
    }

    .table tbody tr td {
      border-bottom: 1px solid #34445f;
      border-right: 1px solid #34445f;
      padding: 12px;
    }

    .table tbody tr td:last-child {
      border-right: none;
      /* border-bottom: none; */
    }

    .table tbody tr:last-child {
      border-bottom: 2px solid #0075a5 !important;
    }

    .table tbody {
      text-align: center;
      background-color: #021e4855;
      color: #fff;
      line-height: 60px;
    }
</style>

<body>
  <div id="app" class="container">
    <div class="head">
      <span>特种设备详情</span>
      <div class="img" @click="closeDialog"></div>
    </div>
    <div class="content">
      <div>设备ID：{{details.sbID}}</div>
      <div>设备名称：{{details.smmc}}</div>
      <div>设备品牌：{{details.sbpp}}</div>
      <div>出厂日期：{{details.ccrq}}</div>
      <div>当前是否存在故障：{{details.dqsfczgz}}</div>
      <div>是否修理完成：{{details.sfxlwc}}</div>
      <div>所属区域名称：{{details.ssqymc}}</div>
      <table class="table centerTable">
        <thead>
          <tr>
            <th v-for="item in thName">{{item}}</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in tbList" style="cursor: pointer" @click="showDialogs()">
            <td>{{item.sj}}</td>
            <td>{{item.wxxq}}</td>
            <td>{{item.wxr}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</body>
<script type="module">
  new Vue({
      el: "#app",
      data: {
        details:{
          sbID:1109,
          smmc:'电梯',
          sbpp:'奥克斯',
          ccrq:'2019年2月19日',
          dqsfczgz:'否',
          sfxlwc:'是',
          ssqymc:'金华市',
        },
        thName:['维修时间','维修详情','维修人'],
        tbList:[
          {sj:'2022-03-16 19:48',wxxq:'电梯坏掉',wxr:'杨静云'},
          {sj:'2022-04-04 02:58',wxxq:'电梯卡住',wxr:'段问薇'},
          {sj:'2022-07-01 22:11',wxxq:'电梯坏掉',wxr:'潘夏彤'},
          {sj:'2021-12-09 06:19',wxxq:'电梯卡住',wxr:'李天泽'}],
      },
      //项目生命周期
      mounted() {
        this.init()
        var that = this
        top.window.addEventListener("message", function (e) {
            let info = e.data;
            console.log(info);
            let title = info.item
            if(info.status == 'zljsjg'){
                that.details.smmc = title
            }
        });
      },
      methods: {
        init(){
          
        },
        closeDialog() {
          top.commonObj.funCloseIframe({
            name: "zljsjg",
          });
        },
      },
      
    });
  </script>

</html>