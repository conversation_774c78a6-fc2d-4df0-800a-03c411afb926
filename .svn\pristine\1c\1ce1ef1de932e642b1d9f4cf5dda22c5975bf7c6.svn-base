<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>全市医疗机构数量</title>
    <script src="/Vue/vue.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css">
    <link rel="stylesheet" href="/static/citybrain/ggfw/css/common-dialog.css">
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script> <!-- 轮播toolTip -->
</head>

<style>
    .table1 .th .th_td:nth-child(1) {
     flex: 0.35;
    }
    .table1 .th .th_td:nth-child(2) {
     flex: 0.30;
    }
    .table1 .th .th_td:nth-child(3) {
     flex: 0.35;
    }
</style>
<body>
    <div id="app" class="container" v-cloak style="width:1540px">
        <div class="head">
            <span>全市医疗机构数量</span>
            <div class="img" @click="closeDialog"></div>
        </div>
        <div class="content">
             <div class="dgjg">
                <div class="table1" style="height: 700px;">
                    <div class="th">
                        <div class="th_td" v-for="(item,index) in theadList" :key="index">
                            {{item}}
                        </div>
                    </div>
                    <div class="tbody" id="box0" @mouseover="mouseenterEvent()" @mouseleave="mouseleaveEvent()">
                        <div class="tr" v-for="(item ,i) in tableList" :key="i">
                            <div class="tr_td" style="flex: 0.35">{{item.life}}</div>
                            <div class="tr_td" style="flex: 0.30">{{item.unlife}}</div>
                            <div class="tr_td" style="flex: 0.35">{{item.wishCar}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
<script type="module">

    new Vue({
        el: '#app',
        data: {
            theadList: ['类别', '数量', '占比'],
            tableList:[
                {
                    life: "综合医院",
                    unlife: "72",
                    wishCar: "3.95",
                },
                {
                    life: "妇幼保健院",
                    unlife: "15",
                    wishCar: "0.82",
                },
                {
                    life: "社区医院",
                    unlife: "1025",
                    wishCar: "56.22",
                },
                {
                    life: "疗养院",
                    unlife: "92",
                    wishCar: "5.04",
                },
                {
                    life: "诊所",
                    unlife: "451",
                    wishCar: "24.73",
                },
                {
                    life: "专科医院",
                    unlife: "67",
                    wishCar: "3.67",
                },
                {
                    life: "体检机构",
                    unlife: "25",
                    wishCar: "1.37",
                },
                {
                    life: "护理院",
                    unlife: "76",
                    wishCar: "4.16",
                },
            ],//表格数据

        },
        methods: {
            closeDialog(){
                top.commonObj.funCloseIframe({
                    name: 'institution-amounts-dialog'
                })
            },
            mouseenterEvent() {
                clearInterval(this.time)
            },
            mouseleaveEvent() {
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },
            autoScroll() {
                this.dom = document.getElementById('box0')
                // this.scpDom = document.getElementsByClassName('text')
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },
            //数据初始化
            init(){
                $get("/ggfw/sdqfw/price").then((res) => {
                    this.tableList = res;
                });
                
            },
        },
        //项目生命周期
        mounted() {
            this.init();
            this.autoScroll();
        }


    })


</script>

</html>