<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <link rel="stylesheet" href="../css/sdqfw-left.css" />
    <link rel="stylesheet" href="../css/sdqfw-right.css" />
</head>
<style>
    .titleOne {
        /* background: url("/img/bg.png") no-repeat; */
        /* background-size: 100% 100%; */
        font-size: 28px;
        background-color: rgb(13 36 62 / 90%);
        height: 550px;
        width: 7520px;
        display: flex;
        justify-content: space-around;
        padding: 20px 45px;
        box-sizing: border-box;
        /* margin-bottom: 15px; */
        /* line-height: 45px; */
    }

    .top {
        color: #fff;
    }

    .bottom>span {
        background-image: linear-gradient(180deg, rgb(249, 253, 255), #bbe6ff, #007ac0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 35px;
        /* margin-right: 10px; */
        /* margin-left: 10px; */
        font-weight: bold;
    }

    .contentBottom {
        display: flex;

        justify-content: space-around;
        color: #fff;
    }

    .qwTitle>p>span {
        color: #d0c470;
    }

    .qw {
        width: 1650px;
    }

    .trTop th:nth-child(1) {
        width: 120px;
    }

    .trTop th:nth-child(2) {
        width: 200px;
    }

    .trTop th:nth-child(3) {
        width: 220px;
    }

    .trTop th:nth-child(4) {
        width: 220px;
    }

    .trTop th:nth-child(5) {
        width: 400px;
    }
</style>

<body>
    <div id="sdqfw-middle2">
        <div class="titleOne">
            <div>
                <div class="title" style="display: flex;">
                    <nav style="padding: 0px 40px">
                        <s-header-title style="width: 100%" title="电" htype="1"></s-header-title>
                    </nav>
                    <el-button type="primary" style="
                                width: 200px;
                                height: 80px;
                                margin-left: 20px;
                                font-size: 40px;
                            " @click="electricPriceDialog">
                        用电价格
                    </el-button>
                </div>
                <div class="Dbox">
                    <div v-for="item in Ddata">
                        <div>{{item.name}}</div>
                        <div class="value">
                            <span>{{item.value}}</span>
                            {{item.dw}}
                        </div>
                    </div>
                </div>
                <div class="bottomBox">
                    <div>
                        <div class="bottomTitle">
                            <div v-for="(item ,index) in timeList" @click="changeEcharts(index)"
                                :class="{active:isActive===index}">
                                {{item}}
                            </div>
                        </div>
                        <div id="lineEcharts003" style="width: 1050px; height: 350px"></div>
                    </div>
                    <div id="barEcharts00" style="width: 1050px; height: 400px"></div>
                </div>
            </div>
            <div>
                <div class="title">
                    <nav style="padding: 0 45px">
                        <s-header-title2 style="width: 100%" title="累计工商业用电量" htype="1"></s-header-title2>
                    </nav>
                </div>
                <div class="contentBottom">
                    <div class="qwTitle" style="line-height: 50px">
                        <div class="bottom"><span>一般工商业和其他用电:</span></div>
                        <p v-for="(item,index) in gsyElectric" :key="'gsyElectric-'+index">
                            {{item.title1}}
                            <span>{{item.value1}}</span>
                            {{item.title2}}
                            <span>{{item.value2}}</span>
                            {{item.title3}}
                            <span>{{item.value3}}</span>
                            {{item.title4}}
                        </p>
                    </div>
                    <div class="qwTitle qw" style="line-height: 50px; margin-left: 30px">
                        <div class="bottom"><span>大工业用电:</span></div>
                        <p v-for="(item,index) in dgyElectric" :key="'dgyElectric-'+index">
                            {{item.title1}}
                            <span>{{item.value1}}</span>
                            {{item.title2}}
                            <span>{{item.value2}}</span>
                            {{item.title3}}
                            <span>{{item.value3}}</span>
                            {{item.title4}}
                            <span>{{item.value4}}</span>
                            {{item.title5}}
                        </p>

                    </div>
                </div>
            </div>
            <div>
                <div class="gqBox">
                    <div style="height: 80px;">
                        <span>累计农业用电量</span>
                        <el-button type="primary" style="
                                    width: 280px;
                                    height: 80px;
                                    margin-left: 320px;
                                    font-size: 40px;
                                " @click="agricultureElectricDialog">
                            农业生产用电
                        </el-button>
                    </div>
                    <div>
                        <el-select v-model="value" @change="ydlfx">
                            <el-option v-for="(item,index) in options" :key="index" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                        <el-select class="nxBox" v-model="value1" @change="ydlfx">
                            <el-option v-for="(item,index) in options1" :key="index" :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div class="dlBox">
                    <div class="leftYD">
                        <div class="nyydTitle"><span>农业排灌和脱粒用电</span></div>
                        <div class="contentTitle">
                            <p class="pTitle">
                                1kV以下: 每千瓦时<span>{{ydl[0].value1}}</span>元, 高峰时每千瓦时<span>{{ydl[0].value2}}</span>元, 低谷
                            </p>
                            <p class="pTitle">
                                时每千瓦<span>{{ydl[0].value3}}</span>元, 低谷时每千瓦时<span>{{ydl[0].value4}}</span>元; 1-10kV:每千
                            </p>
                            <p class="pTitle">
                                瓦时<span>{{ydl[0].value5}}</span>元, 高峰时每度电<span>{{ydl[0].value6}}</span>元,
                                低谷时每度电<span>{{ydl[0].value7}}</span>元
                            </p>
                            <p class="pTitle">
                                低谷时每度电<span>{{ydl[0].value8}}</span>元, 20kV:每千瓦时<span>{{ydl[0].value9}}</span>元, 高峰时每千
                            </p>
                            <p class="pTitle">
                                瓦时<span>{{ydl[0].value10}}</span>元, 低谷时每千瓦时<span>{{ydl[0].value11}}</span>元,
                                低谷时每千瓦时<span>{{ydl[0].value12}}</span>元;
                            </p>
                        </div>
                    </div>
                    <div class="rightBox">
                        <div><span>电力公司信息公告</span></div>
                        <table class="table" style="height: 300px">
                            <thead>
                                <tr class="trTop">
                                    <th class="thName" v-for="item in thName">{{item}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="trr" v-for="item in dlList">
                                    <td>{{item.sort}}</td>
                                    <td>{{item.sj}}</td>
                                    <td>{{item.lb}}</td>
                                    <td>{{item.time}}</td>
                                    <td>{{item.fw}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#sdqfw-middle2",
        data: {
            Ddata: [],
            timeList: ["年度", "季度", "月份"],
            isActive: 0,
            dlList: [],
            value: "婺城区",
            value1: "2022",
            options: [
                {
                    value: "婺城区",
                    label: "婺城区",
                },
                {
                    value: "义乌市",
                    label: "义乌市",
                },
                {
                    value: "磐安县",
                    label: "磐安县",
                },
                {
                    value: "兰溪市",
                    label: "兰溪市",
                },
                {
                    value: "武义县",
                    label: "武义县",
                },
                {
                    value: "金义新区",
                    label: "金义新区",
                },
                {
                    value: "义乌市",
                    label: "义乌市",
                },
                {
                    value: "东阳市",
                    label: "东阳市",
                },
                {
                    value: "浦江县",
                    label: "浦江县",
                },
            ],
            options1: [
                {
                    value: "2022",
                    label: "2023",
                },
                {
                    value: "2021",
                    label: "2022",
                },
                {
                    value: "2020",
                    label: "2021",
                },
            ],
            timeList: ["年度", "季度", "月份"],
            thName: ["序号", "事件", "类别", "时间", "影响范围"],
            // 工商业用电
            gsyElectric: [],
            // 大工业用电
            dgyElectric: [
                {
                    title1: "1-10kV:每千瓦时",
                    title2: "元, 高峰时每千瓦时",
                    title3: "元,低谷时每千瓦时",
                    title4: "元, 低谷时每千瓦时",
                    title5: "元;",
                    title6: "",
                    value1: "0.6644",
                    value2: "1.0824",
                    value3: "0.9004",
                    value4: "0.4164",
                    value5: "",
                },
                {
                    title1: "20kV:每千瓦时",
                    title2: "元, 高峰时每千瓦时",
                    title3: "元,低谷时每千瓦时",
                    title4: "元, 低谷时每千瓦时",
                    title5: "元;",
                    title6: "",
                    value1: "0.6644",
                    value2: "1.0571",
                    value3: "0.8771",
                    value4: "0.4004",
                    value5: "",
                },
                {
                    title1: "30kV:每千瓦时",
                    title2: "元, 高峰时每千瓦时",
                    title3: "元,低谷时每千瓦时",
                    title4: "元, 低谷时每千瓦时",
                    title5: "元;1-10kV:每千瓦时",
                    title6: "元,",
                    value1: "0.6344",
                    value2: "1.0444",
                    value3: "0.8654",
                    value4: "0.3924",
                    value5: "0.4883",
                },
                {
                    title1: "高峰时每千瓦时",
                    title2: "元, 低谷时每千瓦时",
                    title3: "元, 低谷时每千瓦时",
                    title4: "元;",
                    title5: "",
                    title6: "",
                    value1: "1.0114",
                    value2: "0.8364",
                    value3: "0.3724",
                    value4: "",
                    value5: "",
                },
                {
                    title1: "20千伏及以上: 每千瓦时",
                    title2: "元; 高峰时每千瓦时",
                    title3: "元,低谷时每千瓦时",
                    title4: "元, 低谷时每千瓦时",
                    title5: "元;",
                    title6: "",
                    value1: "0.6124",
                    value2: "1.0014",
                    value3: "0.8284",
                    value4: "0.3684",
                    value5: "",
                },
            ],
            ydl: {},
            ydlData: []
        },
        mounted() {
            this.initFun();
        },
        methods: {
            initFun() {
                $api("/ggfw_sdqfw_sdqfw_middle002").then((res) => {
                    this.gsyElectric = res;
                });
                $api("/ggfw_sdqfw_sdqfw_middle004").then((res) => {
                    this.ydlData = res;
                    this.ydlfx()
                });
                $api("/sdqfw_left006").then((res) => {
                    this.getEcharts04("lineEcharts003", res);
                });
                $api("/sdqfw_left009").then((res) => {
                    this.getEcharts05("barEcharts00", res);
                });
                $api("/sdqfw_right009").then((res) => {
                    this.dlList = res;
                });
            },
            ydlfx() {
                this.ydl = this.ydlData.filter(item => {
                    return item.area == this.value && item.year == this.value1
                })
            },
            electricPriceDialog() {
                let diaog = {
                    type: 'openIframe',
                    name: 'electric-price-dialog',
                    src: baseURL.url + '/static/citybrain/ggfw/commont/electric-price-dialog.html',
                    left: "calc(50% - 700px)",
                    top: "25%",
                    width: "1560px",
                    height: "980px",
                    zIndex: "10",
                    argument: {
                        status: ""
                    }
                }
                top.window.parent.postMessage(JSON.stringify(diaog), '*')
            },
            agricultureElectricDialog() {
                let diaog = {
                    type: 'openIframe',
                    name: 'agriculture-electric-dialog',
                    src: baseURL.url + '/static/citybrain/ggfw/commont/agriculture-electric-dialog.html',
                    left: "calc(50% - 700px)",
                    top: "25%",
                    width: "1560px",
                    height: "980px",
                    zIndex: "10",
                    argument: {
                        status: ""
                    }
                }
                top.window.parent.postMessage(JSON.stringify(diaog), '*')
            },
            getEcharts04(dom, echartData) {
                let echarts4 = echarts.init(document.getElementById(dom));

                const xAxisData = echartData.map((item) => {
                    return item.time;
                });
                const yData = echartData.map((item) => {
                    return item.value;
                });

                let option = {
                    textStyle: {
                        fontFamily: "Din-Light",
                    },
                    legend: {
                        data: [
                            {
                                name: "累计行业用电量",
                                icon: "path://M512 139.81262864a286.42534744 286.42534744 0 1 0 286.42534744 286.42534744 286.42534744 286.42534744 0 0 0-286.42534744-286.42534744z m0 477.3755789a190.95023144 190.95023144 0 1 1 190.95023144-190.95023146 190.95023144 190.95023144 0 0 1-190.95023144 190.95023146z",
                            },
                        ],
                        selected: {
                            累计行业用电量: true,
                        },
                        itemWidth: 20,
                        itemHeight: 20,
                        itemGap: 30,
                        textStyle: {
                            color: "#fff",
                            lineHeight: 15,
                            fontSize: 30,
                        },
                        type: "scroll",
                    },
                    tooltip: {
                        backgroundColor: "#fff05",
                        trigger: "axis",
                        axisPointer: {
                            type: "none",
                        },
                        textStyle: {
                            color: "#ffff",
                            lineHeight: 28,
                            fontSize: 30,
                        },
                        confine: true,
                        padding: 12,
                    },
                    grid: {
                        // right: 0,
                        bottom: 100,
                    },
                    xAxis: {
                        type: "category",
                        boundaryGap: true,
                        offset: 5,
                        data: xAxisData,
                        axisLabel: {
                            interval: 0,
                            align: "left",
                            color: "#fff",
                            fontSize: 30,
                        },
                        axisLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },
                    },
                    yAxis: {
                        type: "value",
                        min: 0,
                        max: 1600,
                        interval: 400,
                        axisLabel: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        splitLine: {
                            lineStyle: {
                                color: "#19365f",
                            },
                        },
                        axisLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },
                    },
                    series: [
                        {
                            name: "累计行业用电量",
                            data: yData,
                            type: "line",
                            smooth: true,
                            smoothMonotone: "x",
                            cursor: "pointer",
                            showSymbol: false,
                            lineStyle: {
                                shadowColor: "rgba(18,61,172,0.5)",
                                color: "#0398d1",
                                shadowBlur: 10,
                            },
                        },
                    ],
                };
                echarts4.setOption(option);
            },
            changeEcharts(index) {
                this.isActive = index;
                if (this.isActive === 0) {
                    $api("/sdqfw_left006").then((res) => {
                        this.getEcharts04("lineEcharts003", res);
                    });
                } else if (this.isActive === 1) {
                    $api("/sdqfw_left007").then((res) => {
                        this.getEcharts04("lineEcharts003", res);
                    });
                } else {
                    $api("/sdqfw_left008").then((res) => {
                        this.getEcharts04("lineEcharts003", res);
                    });
                }
            },
            getEcharts05(dom, echartData) {
                let echarts5 = echarts.init(document.getElementById(dom));

                var datas = echartData;
                let maxArr = new Array(datas.length).fill(200);
                let option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: "30",
                        },
                    },
                    legend: {
                        show: false,
                    },
                    grid: {
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        containLabel: true,
                    },
                    xAxis: {
                        show: false,
                        type: "value",
                    },
                    yAxis: [
                        {
                            type: "category",
                            inverse: true,
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisPointer: {
                                label: {
                                    show: true,
                                    margin: 30,
                                },
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#FFF",
                                    fontSize: 30,
                                },
                            },
                            data: datas.map((item) => item.time),
                        },
                        {
                            type: "category",
                            inverse: true,
                            axisTick: "none",
                            axisLine: "none",
                            show: true,
                            data: datas.map((item) => item.value),
                            axisLabel: {
                                show: true,
                                formatter: function (value) {
                                    let a = value + "kV";
                                    return "{a|" + a + "}";
                                },
                                rich: {
                                    a: {
                                        color: "#35a3d5",
                                        fontSize: 30,
                                        fontWeight: "bold",
                                    },
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            z: 2,
                            type: "bar",
                            barWidth: 25,
                            zlevel: 1,
                            data: datas.map((item, i) => {
                                itemStyle = {
                                    color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                        {
                                            offset: 0,
                                            color: "#1f71a2",
                                        },
                                        {
                                            offset: 1,
                                            color: "#41c1f6",
                                        },
                                    ]),
                                };
                                return {
                                    value: item.value,
                                    itemStyle: itemStyle,
                                };
                            }),
                            label: {
                                show: false,
                                position: "right",
                                color: "#333333",
                                fontSize: 14,
                                offset: [10, 0],
                            },
                        },
                        {
                            type: "bar",
                            barWidth: 25,
                            barGap: "-100%",
                            itemStyle: {
                                normal: {
                                    color: "#052559",
                                },
                            },
                            data: maxArr,
                        },
                    ],
                };
                echarts5.setOption(option);
            },
        },
    });
</script>