<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <title>应急指挥-右</title>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <script src="/Vue/vue.js"></script>
        <script src="/static/js/jslib/vue-count-to.min.js"></script>
        <script src="/echarts/echarts.min.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <style>
            * {
                margin: 0;
                padding: 0;
            }
            #app {
                width: 1050px;
                height: 1930px;
                background: url("/img/right-bg.png") no-repeat;
                background-size: 100% 100%;
                padding: 30px;
                box-sizing: border-box;
                overflow: hidden;
            }
            /* 表格 */
            .table {
                width: 100%;
                height: 100%;
                padding: 10px;
                box-sizing: border-box;
                overflow-y: auto;
            }

            .table .th {
                width: 100%;
                height: 80px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-style: italic;
                font-weight: 700;
                font-size: 28px;
                line-height: 80px;
                background: #00396f;
                color: #ffffff;
            }

            .table .th_td {
                letter-spacing: 0px;
                text-align: center;
                flex: 0.2;
            }

            .table .tbody {
                width: 100%;
                height: calc(100% - 80px);
                overflow: hidden;
            }

            .table .tbody:hover {
                overflow-y: auto;
            }

            .table .tbody::-webkit-scrollbar {
                width: 4px;
                /*滚动条整体样式*/
                height: 4px;
                /*高宽分别对应横竖滚动条的尺寸*/
            }

            .table .tbody::-webkit-scrollbar-thumb {
                border-radius: 10px;
                background: #20aeff;
                height: 8px;
            }

            .table .tr {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 80px;
                line-height: 80px;
                font-size: 28px;
                color: #ffffff;
                cursor: pointer;
            }

            .table .tr:nth-child(2n) {
                background: #00396f;
            }

            .table .tr:nth-child(2n + 1) {
                background: #035b86;
            }

            .table .tr:hover {
                background-color: #6990b6;
            }

            .table .tr_td {
                letter-spacing: 0px;
                text-align: center;
                box-sizing: border-box;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        </style>
    </head>
    <body>
        <div id="app">
            <nav>
                <s-header-title title="事件信息集约化分析" htype="2"></s-header-title>
            </nav>
            <div>
                <div id="lineEchart" style="width: 100%; height: 500px"></div>
                <div style="width: 100%; height: 1200px; margin-top: 30px">
                    <div class="table table1">
                        <div class="th">
                            <div class="th_td" v-for="(item,index) in theadList" :key="index">{{item}}</div>
                        </div>
                        <div class="tbody" id="tbody" @mouseover="mouseenterEvent()" @mouseleave="mouseleaveEvent()">
                            <div class="tr" v-for="(item ,i) in tbodyList" :key="i">
                                <div class="tr_td" style="flex: 0.2">{{item.data}}</div>
                                <div class="tr_td" style="flex: 0.2">{{item.area}}</div>
                                <div class="tr_td" style="flex: 0.2">{{item.name}}</div>
                                <div class="tr_td" style="flex: 0.2">{{item.type}}</div>
                                <div class="tr_td" style="flex: 0.2">{{item.grade}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script>
            let vm = new Vue({
                el: "#app",
                data: {
                    theadList: ["发生日期", "发生地点", "事件名称", "事件分类", "事件等级"],
                    tbodyList: [],
                    dom: null,
                    time: null,
                },
                mounted() {
                    this.init();
                    this.scroll();
                    this.openIframe();
                },
                methods: {
                    init() {
                        $api("ldst_shgl_yjzh", { type1: 1 }).then((res) => {
                            this.lineEchart("lineEchart", res);
                        });
                        $api("ldst_shgl_yjzh", { type1: 2 }).then((res) => {
                            this.tbodyList = res;
                        });
                    },

                    scroll() {
                        this.dom = document.getElementById("tbody");
                        this.time = setInterval(() => {
                            this.dom.scrollTop += 2;
                            if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                                this.dom.scrollTop = 0;
                            }
                        }, 20);
                    },
                    mouseenterEvent() {
                        clearInterval(this.time);
                    },
                    mouseleaveEvent() {
                        this.time = setInterval(() => {
                            this.dom.scrollTop += 2;
                            if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                                this.dom.scrollTop = 0;
                            }
                        }, 20);
                    },

                    lineEchart(id, lineData) {
                        let myEc = echarts.init(document.getElementById(id));
                        let datax = [],
                            datay = [];
                        lineData.map((ele) => {
                            datax.push(ele.name);
                            datay.push(ele.value);
                        });
                        let option = {
                            title: {
                                show: false,
                                text: "用电量",
                            },
                            tooltip: {
                                trigger: "axis",
                            },
                            legend: {
                                show: false,
                                data: ["2018", "2019"],
                            },
                            grid: {
                                left: "3%",
                                right: "4%",
                                bottom: "8%",
                                containLabel: true,
                            },
                            tooltip: {
                                trigger: "axis",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                axisPointer: {
                                    lineStyle: {
                                        color: "rgba(11, 208, 241, 1)",
                                        type: "slider",
                                    },
                                },
                                textStyle: {
                                    color: "rgba(212, 232, 254, 1)",
                                    fontSize: 28,
                                },
                            },
                            xAxis: [
                                {
                                    type: "category",
                                    offset: 20,
                                    axisLine: {
                                        //坐标轴轴线相关设置。数学上的x轴
                                        show: true,
                                        lineStyle: {
                                            color: "rgba(108, 166, 219, 0.3)",
                                        },
                                    },
                                    axisLabel: {
                                        //坐标轴刻度标签的相关设置
                                        // rotate: -30,
                                        textStyle: {
                                            color: "#fff",
                                            fontSize: 24,
                                        },
                                    },
                                    splitLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#192a44",
                                        },
                                    },
                                    axisTick: {
                                        show: false,
                                    },
                                    data: datax,
                                },
                            ],
                            yAxis: [
                                {
                                    name: "",
                                    min: (value) => {
                                        return parseInt(value.min - 1);
                                    },
                                    nameTextStyle: {
                                        fontSize: 24,
                                        color: "#D6E7F9",
                                        padding: [0, 20, 10, 0],
                                    },
                                    splitLine: {
                                        show: true,
                                        lineStyle: {
                                            color: "#77b3f1",
                                            opacity: 0.1,
                                            width: 2,
                                        },
                                    },
                                    axisTick: {
                                        show: true,
                                        lineStyle: {
                                            color: "#77b3f1",
                                            opacity: 0.5,
                                            width: 2,
                                        },
                                    },
                                    axisLabel: {
                                        textStyle: {
                                            fontSize: 24,
                                            color: "#D6E7F9",
                                        },
                                    },
                                    axisLine: {
                                        show: false,
                                        lineStyle: {
                                            color: "#233653",
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    name: "事故",
                                    type: "line",
                                    symbolSize: 10,
                                    itemStyle: {
                                        normal: {
                                            color: "#3A84FF",
                                            lineStyle: {
                                                color: "#1b759c",
                                                width: 4,
                                            },
                                            areaStyle: {
                                                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                                                    {
                                                        offset: 0,
                                                        color: "rgba(2, 92, 131,0.9)",
                                                    },
                                                    {
                                                        offset: 1,
                                                        color: "rgba(2, 92, 131,0.2)",
                                                    },
                                                ]),
                                            },
                                        },
                                    },
                                    data: datay,
                                },
                            ],
                        };
                        myEc.setOption(option);
                        myEc.getZr().on("mousemove", (param) => {
                            myEc.getZr().setCursorStyle("default");
                        });
                    },

                    openIframe() {
                        let Iframe = {
                            type: "openIframe",
                            name: "yjzh-left",
                            src: baseURL.url + "/static/citybrain3840/shgl/pages/yjzh/yjzh-left.html",
                            left: "30px",
                            top: "230px",
                            width: "300px",
                            height: "800px",
                            zIndex: "10",
                            argument: {
                                status: "yjzh-left",
                            },
                        };
                        window.parent.postMessage(JSON.stringify(Iframe), "*");
                    },

                    closeIframe() {
                        top.commonObj.funCloseIframe({
                            name: "yjzh-left",
                        });
                    },
                },
            });
        </script>
    </body>
</html>
