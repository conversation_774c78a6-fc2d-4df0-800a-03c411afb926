<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>战略管理中心-中间</title>
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/xiaoguo.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link rel="stylesheet" href="../css/zlglzx-middle.css" />
    <link rel="stylesheet" href="/static/css/common.css" />
    <script src="/static/citybrain/hjbh/js/vue.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
  </head>
  <body>
    <div id="zlglzx-middle">
      <div class="zlglzx">
        <div class="middle-class">
          <!-- 中间背景 -->
          <div class="middle-circle-bg1">
            <div class="middle-circle-bg2">
              <div class="middle-circle-bg3 middle-content">
                <div class="middle-name">
                  <span>全市</span>
                  生产总值年均增长
                </div>
                <div class="middle-value yel-color">{{list3.value}}</div>
                <div class="middle-unit yel-color">以上</div>
              </div>
              <img src="/static/citybrain/zlglzx/img/zlglzx-middle/m-bg-2.png" alt="" class="img_2">
            </div>
            <img src="/static/citybrain/zlglzx/img/zlglzx-middle/m-bg-1.png" alt="" class="img_1 breath-light">
          </div>
          <!-- 中间的文字 -->
          <div class="m-b-text">
            <div>{{list2.ymbq+''+list2.value+''+list2.unit}}</div>
            <div>{{list1.ymbq+''+list1.value+''+list1.unit}}</div>
          </div>

          <!-- 左右的大标题 -->
          <!-- <div class="title-item">
          <ul class="title-ul">
            <li>
              <span style="margin-bottom: 20px;">国际</span>
              <span>影视文化</span>
              <span>之都</span>
            </li>
            <li>
              <span>世界</span>
              <span>小商品之都</span>
            </li>
            <li>
              <span>创新</span>
              <span>智造基地</span>
            </li>
            <li>
              <span>和美</span>
              <span>宜居福地</span>
            </li>
          </ul>
        </div>
         -->

          <!-- 底部的图片 -->
          <!-- <div class="bottom-item">
          <ul class="bottom-ul">
            <li></li>
            <li></li>
            <li></li>
            <li></li>
          </ul>
        </div> -->
        </div>

        <ul class="top-container breath-light">
          <li class="top-item" @click="openWburl">
            <div>能级提升</div>
          </li>
          <li class="top-item" @click="openWburl2">
            <div>2022年重点工作</div>
          </li>
          <li class="top-item" @click="openCommonWealthurl">
            <div>共同富裕</div>
          </li>
        </ul>

        <ul class="botton-container">
          <li class="botton-item">
            <span>世界</span><br />
            <span>小商品之都</span>
          </li>
          <li class="botton-item">
            <span>国际影视</span><br />
            <span>文化之都</span>
          </li>
          <li class="botton-item">
            <span>创新</span><br />
            <span>创造基地</span>
          </li>
          <li class="botton-item">
            <span>和美</span><br />
            <span>宜居福地</span>
          </li>
        </ul>
      </div>
    </div>
  </body>
  <script>
    var rkmd = new Vue({
      el: "#zlglzx-middle",
      data: {
        iframeName:"",
        isBtn:false,
        list1:[],
        list2:[],
        list3:[],
      },
      created() {},
      mounted() {
        this.initApi()
      },
      methods: {
        openWburl() {
          top.commonObj.openWinHtml("3840","2160", "http://************:8601/v-panel/energy-level-increase")
        },
        openWburl2() {
          // top.commonObj.openWinHtml("3840","2160", "/static/citybrain/zlglzx/img/zlglzx-middle/zdgz-img1.jpg")
          top.commonObj.openWinHtml("3840","2160", "/static/citybrain/djtl/pages/zdgz.html")
        },
        openCommonWealthurl(){
          top.commonObj.openWinHtml("3840","2160", "http://************:8601/v-panel/common-prosperity")
        },
        initApi() {
          $api("/zlglzx_first_mid1").then((res) => {
            this.list1 = res[0]
            this.list2 = res[1]
            this.list3 = res[2]
            this.list3.value = res[2].value.split('%')[0]+'%'
          });
        },
      },
      beforeDestroy() {},
    });
  </script>
</html>
