var login = {
  name: '',
  password: '',
  init: function () {
    login.funInit()
  },
  funInit: function () {
    $.ajax({
      url: 'getAjaxRequestIp',
      dataType: 'json',
      type: 'get',
      async: false,
      success: function (data) {
        login.pageConfig = data.pageConfig
      },
      error: function (e) {
        console.log(e)
      },
    })
    $('.ipt').focus(function () {
      if ($(this).hasClass('ipt-error')) {
        //如果里面有错误信息
        $(this).removeClass('ipt-error')
        $(this).val('')
        if ($(this).attr('name') == 'password') {
          $(this).attr('type', 'password')
        }
      }
    })
    $('.ipt').blur(function () {
      var str = $.trim($(this).val())
      var name = ''
      if (!str) {
        //输入为空
        if ($(this).attr('name') == 'username') {
          //账号为空
          name = '账号/用户名不能为空！'
        } else {
          //密码为空
          name = '密码不能为空！'
          $(this).attr('type', 'text')
        }
        if (!$(this).hasClass('ipt-error')) {
          $(this).addClass('ipt-error')
        }
        $(this).val(name)
      }
    })
    $('#login').click(login.funSubmit)
    $('#username').bind('keyup', function (event) {
      if (event.keyCode == '13') {
        //回车执行查询
        login.funSubmit()
        $(this).blur()
      }
    })
    $('#password').bind('keyup', function (event) {
      if (event.keyCode == '13') {
        //回车执行查询
        login.funSubmit()
        $(this).blur()
      }
    })
  },
  funSubmit: function () {
    // $('#message').append('22'+'<br/>')
    //提交表单
    var name = $.trim($('#username').val()) //账号
    // var password = hex_md5($.trim($("#password").val()));//线上
    var password = login.encode64($.trim($('#password').val())) //本地
    // var password = $.trim($("#password").val());//线上
    var len = $('.ipt-error').length
    if (len != 0) {
      //账号或密码有错
      return
    }
    if (!name) {
      $('#username').addClass('ipt-error')
      $('#username').val('账号/用户名不能为空！')
      return
    }
    if (!password) {
      $('#password').addClass('ipt-error')
      $('#password').val('密码不能为空！')
      $('#password').attr('type', 'text')
      return
    }
    login.name = name
    login.password = password
    var type = { username: name, password: password } //线上
    //var type={indexid:"login/login001"};//本地
    login.funAjaxLogin(type)
  },
  funAjaxLogin: function (type) {
    // login.funLoginHtml();
    $.ajax({
      url: login.pageConfig['loginajaxurl'],
      data: JSON.stringify(type),
      contentType: 'application/json;charset=UTF-8',
      // dataType: 'json',
      //type:"get",
      type: 'post',
      // type: 'post',//本地是get，线上是post
      success: function (data) {
        console.log('接口数据', data)
        if (data.code == '200') {
          console.log('开始调用')

          login.funLoginHtml(data.portToken, data.userId, data.token)
        } else {
          var str = data.msg.substr(0, 2)
          if (str == '用户') {
            login.ShowEorrorMessge('username', data.msg)
          } else {
            //显示在密码框中，显示在账号框中用username
            login.ShowEorrorMessge('password', data.msg)
          }
        }
      },
      error: function (err) {},
    })
  },
  // funLoginHtml:function(data,obj,type,authto,zzdToken,zzdUserInfo){
  //     /*let token = JSON.stringify(data);
  //     let role = JSON.stringify(obj);
  //     let authtoken = JSON.stringify(authto);
  //     sessionStorage.setItem("token",token);
  //     sessionStorage.setItem("role",role);
  //     sessionStorage.setItem("authtoken",authtoken);
  // 	sessionStorage.setItem("zzdToken",zzdToken);
  //     sessionStorage.setItem("zzdUserInfo",zzdUserInfo);*/
  // 	//window.location.href = commonObj.pageConfig["sfwyPage"];
  //     //window.location.href = commonObj.pageConfig["home"];
  //     window.location.href = login.pageConfig["index"];
  // },
  funLoginHtml: function (portToken, userId, authorization) {
    // let token = JSON.stringify(portToken)
    let userid = JSON.stringify(userId)
    // let Authorization = JSON.stringify(authorization)

    sessionStorage.setItem('token', portToken)
    sessionStorage.setItem('Authorization', authorization)

    sessionStorage.setItem('role', userid)
    window.location.href = './index.html'
  },
  encode64: function (val) {
    // base64加密开始
    var keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
    var output = ''
    var chr1,
      chr2,
      chr3 = ''
    var enc1,
      enc2,
      enc3,
      enc4 = ''
    var i = 0
    do {
      chr1 = val.charCodeAt(i++)
      chr2 = val.charCodeAt(i++)
      chr3 = val.charCodeAt(i++)
      enc1 = chr1 >> 2
      enc2 = ((chr1 & 3) << 4) | (chr2 >> 4)
      enc3 = ((chr2 & 15) << 2) | (chr3 >> 6)
      enc4 = chr3 & 63
      if (isNaN(chr2)) {
        enc3 = enc4 = 64
      } else if (isNaN(chr3)) {
        enc4 = 64
      }
      output =
        output +
        keyStr.charAt(enc1) +
        keyStr.charAt(enc2) +
        keyStr.charAt(enc3) +
        keyStr.charAt(enc4)
      chr1 = chr2 = chr3 = ''
      enc1 = enc2 = enc3 = enc4 = ''
    } while (i < val.length)
    return output
  },
  ShowEorrorMessge: function (lab, msg) {
    //lab 指信息显示在账号框还是密码框中，msg要显示的内容
    if (lab == 'password') {
      //显示在密码框中
      if (!$('#password').hasClass('ipt-error')) {
        $('#password').addClass('ipt-error')
        $('#password').val(msg)
        $('#password').attr('type', 'text')
      }
    } else {
      //显示在账号框中
      if (!$('#username').hasClass('ipt-error')) {
        $('#username').addClass('ipt-error')
        $('#username').val(msg)
      }
    }
  },
  getUrlParamValue(paramName) {
    var reg = new RegExp('(^|&)' + paramName + '=([^&]*)(&|$)', 'i')
    var r = window.location.search.substr(1).match(reg)
    if (r != null) {
      return decodeURI(r[2])
    } else {
      return null
    }
  },
}

var topObj = { zzdCode: null, phone: null } //扫码登录传递参数
showDiong = function (top) {
  $.ajax({
    url: login.pageConfig['existsOpenId'],
    data: JSON.stringify(top),
    contentType: 'application/json;charset=UTF-8',
    type: 'post',
    success: function (data) {
      if (!data.flag) {
        $('.diong').show()
        $('.login-content').hide()
      } else {
        $('.diong').hide()
        login.funAjaxLogin(topObj)
      }
    },
    error: function () {},
  })
}
initAjax = function () {
  $.ajax({
    url: 'getAjaxRequestIp',
    dataType: 'json',
    type: 'get',
    async: false,
    success: function (data) {
      login.pageConfig = data.pageConfig
    },
    error: function (e) {
      console.log(e)
    },
  })
}
$(function () {
  $(function () {
    //https://login-pro.ding.zj.gov.cn/oauth2/auth.htm?response_type=code&client_id=jhcsdn_dingoa&redirect_uri=http://jhtest.cn1.utools.club/home-pages/login&scope=get_user_info&authType=QRCODE
    console.log('first')
    var code = login.getUrlParamValue('code')
    var type = { zzdCode: code }
    topObj.zzdCode = code
    initAjax()
    // //市治理中心系统访问本系统单点验证
    if (code != null && code != '') {
      if (window != top) {
        top.location.href = '/login.html?code=' + code
      } else {
        showDiong(topObj)
        login.init()
      }
    } else {
      login.init()
      window.addEventListener(
        'message',
        (event) => {
          // 这里的event.data 就是登录成功的信息
          topObj.zzdCode = event.data.code
          if (event.data.code != null && event.data.code != '') {
            showDiong(topObj)
          } else {
            login.init()
          }
          window.removeEventListener('message')
        },
        true
      )
    }
  })
  // token直接登录
  tokenJump()
})

function tokenJump() {
  var token = login.getUrlParamValue('token')
  if (token !== null && token !== '') {
    $.ajax({
      // url正式环境拼接，测试环境不要adm-api
      url: '/adm-api/loginAuthorize?authorizeToken=' + token,
      dataType: 'json',
      type: 'get',
      async: false,
      success: function (data) {
        login.funLoginHtml(data.portToken, data.userId, data.token)
      },
      error: function (e) {
        console.log(e)
      },
    })
  }
}

var start = true
$('.tab_click').click(() => {
  //登录方式切换
  start = !start
  $('.tab_click').toggleClass('tabTwo')
  if (start) {
    $('.login-content').show()
    $('.ewm_box').hide()
  } else {
    $('.login-content').hide()
    $('.ewm_box').show()
  }
})
$('.img').click(() => {
  //关闭首次绑定手机号
  $('.diong').hide()
})
function codeVal(phone) {
  let phoneRol = /^[1][3,4,5,7,8][0-9]{9}$/
  return phoneRol.test(phone)
}
$('#ponID').focus(function () {
  if ($(this).hasClass('ipt-error')) {
    //如果里面有错误信息
    $(this).removeClass('ipt-error')
    $(this).val('')
  }
})
function issure() {
  //弹窗确定按钮
  topObj.phone = $('#ponID').val()
  if (topObj.phone == '') {
    $('#ponID').val('手机号不能为空')
    $('#ponID').attr('class', 'ipt-error')
  } else if (!codeVal(topObj.phone)) {
    $('#ponID').val('请核对手机号是否正确')
    $('#ponID').attr('class', 'ipt-error')
    return
  }
  login.funAjaxLogin(topObj)
}
