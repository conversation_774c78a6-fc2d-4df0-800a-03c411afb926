<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2022-11-01 10:37:24
 * @LastEditors: wjb
 * @LastEditTime: 2022-11-01 11:25:48
-->
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>市场监管</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <style>
            * {
                padding: 0;
                margin: 0;
            }

            #scjg-right-dialog02 {
                width: 2000px;
                height: 1400px;
                background-color: #0a2443;
                border: 4px solid #17cddd;
                border-radius: 40px;
                box-sizing: border-box;
            }

            .title {
                position: relative;
            }

            .close {
                position: absolute;
                right: 40px;
                top: 20px;
                font-size: 40px;
                color: #fff;
                font-weight: 700;
                cursor: pointer;
            }

            .value {
                color: #0087ec;
            }

            .xfwq {
                font-size: 30px;
                color: #fff;
                margin: 0 60px;
            }

            .xfwqBox {
                background: url("../img/itembg.png") no-repeat;
                background-size: 100% 100%;
                padding: 20px 40px;
                margin-top: 24px;
            }

            .gdbox {
                margin: 24px 60px 0;
            }

            .gdbox .text {
                font-size: 30px;
                color: #fff;
                line-height: 48px;
            }

            .shsqxq-lc-img {
                margin: 60px 60px 0;
            }
        </style>
    </head>

    <body>
        <div id="scjg-right-dialog02">
            <div class="content">
                <div style="display: flex; height: 100px">
                    <div class="close" @click="close">X</div>
                </div>
                <div class="title" style="display: flex">
                    <nav style="padding: 0px; flex: 1; margin-left: 360px">
                        <s-header-title2 style="width: 100%" title="日监管指标趋势" htype="2"></s-header-title2>
                    </nav>
                    <nav style="padding: 0px; flex: 1">
                        <s-header-title2 style="width: 100%" title="全流程监管" htype="2"></s-header-title2>
                    </nav>
                </div>
                <div style="display: flex">
                    <div class="xfwq">
                        <div class="xfwqBox" v-for="item in xfwqList">
                            <div class="value">{{item.value}}{{item.unit}}</div>
                            <div>{{item.name}}</div>
                        </div>
                    </div>
                    <div id="barEcharts03" style="height: 450px; width: 100%; flex: 1"></div>
                    <div id="barEcharts04" style="height: 450px; width: 100%; flex: 1"></div>
                </div>
                <div class="title">
                    <nav style="padding: 20px 0px">
                        <s-header-title2
                            style="width: 100%"
                            title="网络理政平台转办工单与综合执法平台上接收的工单"
                            htype="1"
                        ></s-header-title2>
                    </nav>
                </div>
                <div>
                    <div style="display: flex">
                        <div class="gdbox">
                            <div class="text">工单编号：{{gdDetails.gdbh}}</div>
                            <div class="text">工单名称：{{gdDetails.gdmc}}</div>
                            <div class="text">工单内容：{{gdDetails.gdnr}}</div>
                            <div class="text">工单日期：{{gdDetails.gdrq}}</div>
                            <div class="video">
                                <div class="text">视频：</div>
                                <video
                                    controls
                                    loop
                                    width="525px"
                                    height="298px"
                                    src="https://cdn.modao.cc/Default_video.mp4"
                                    style="margin-top: 10px"
                                ></video>
                            </div>
                        </div>
                        <div class="shsqxq-lc-img">
                            <img src="../img/shsqxq-lc.png" alt="" />
                            <div class="audio" style="margin-top: 80px">
                                <!-- <audio controls style="width: 480px; height: 120px">
                  <source
                    src="https://wwww.runoob.com/try/demo_source/horse.mp3"
                    type="audio/mpeg"
                  />
                </audio> -->
                                <audio controls style="width: 480px; height: 120px">
                                    <source src="/static/citybrain3840/scjg/audio/music.mp3" type="audio/mpeg" />
                                </audio>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#scjg-right-dialog02",
        data: {
            xfwqList: [
                {
                    name: "月监管诉求量",
                    value: 2121,
                    unit: "件",
                },
                {
                    name: "月监管办结量",
                    value: 1921,
                    unit: "件",
                },
            ],
            gdDetails: {
                gdbh: "019011",
                gdmc: "市场价格欺诈",
                gdnr: "金华市一鸣企业存在市场监管欺诈行为",
                gdrq: "2024-03-12",
                gdsp: "",
                gdyp: "",
            },
        },
        mounted() {
            this.initFun();
        },
        methods: {
            initFun() {
                $api("ldst_scjg_scjg", { type: 39 }).then((res) => {
                    this.getEcharts03(res);
                });
                $api("ldst_scjg_scjg", { type: 40 }).then((res) => {
                    this.getEcharts04(res);
                });
            },
            close() {
                top.commonObj.funCloseIframe({
                    name: "scjg-left-dialog01",
                });
            },
            getEcharts03(res) {
                let myCharts = echarts.init(document.getElementById("barEcharts03"));
                let yData = res.map((item) => {
                    return item.name;
                });
                let value = res.map((item) => {
                    return item.value;
                });
                let value1 = res.map((item) => {
                    return item.value1;
                });

                let option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    legend: {
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    grid: {
                        bottom: "16%",
                        left: "18%",
                        right: "0%",
                    },
                    yAxis: [
                        {
                            type: "value",

                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                formatter: "{value}万",
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                    ],

                    xAxis: {
                        type: "category",
                        data: yData,
                        offset: 15,
                        axisLabel: {
                            fontSize: 30,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            name: "诉求量",
                            type: "line",
                            data: value,
                        },
                        {
                            name: "办结量",
                            type: "line",
                            data: value1,
                        },
                    ],
                };
                myCharts.setOption(option);
                tools.loopShowTooltip(myCharts, option, {
                    loopSeries: true,
                });
            },
            getEcharts04(res) {
                let myCharts = echarts.init(document.getElementById("barEcharts04"));
                let yData = res.map((item) => {
                    return item.name
                }).slice(0,4);
                let value = res.map((item) => {
                    return item.value;
                }).slice(0,4);
                let value1 = res.map((item) => {
                    return item.value1;
                }).slice(0,4);
                let value2 = res.map((item) => {
                    return item.value2;
                }).slice(0,4);
                let value3 = res.map((item) => {
                    return item.value3;
                }).slice(0,4);

                let option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    legend: {
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    grid: {
                        bottom: "16%",
                        left: "18%",
                        right: "0%",
                    },
                    yAxis: [
                        {
                            type: "value",

                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                formatter: "{value}万",
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                    ],

                    xAxis: {
                        type: "category",
                        data: yData,
                        offset: 15,
                        axisLabel: {
                            fontSize: 30,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            name: "上报",
                            type: "bar",
                            data: value,
                        },
                        {
                            name: "受理",
                            type: "bar",
                            data: value1,
                        },
                        {
                            name: "办理",
                            type: "bar",
                            data: value2,
                        },
                        {
                            name: "办结",
                            type: "bar",
                            data: value3,
                        },
                    ],
                };
                myCharts.setOption(option);
                tools.loopShowTooltip(myCharts, option, {
                    loopSeries: true,
                });
            },
        },
    });
</script>
