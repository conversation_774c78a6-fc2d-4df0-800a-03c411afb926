<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>详情框3</title>
  <script src="/Vue/vue.js"></script>
  <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
  <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <link rel="stylesheet" href="/static/css/animate.css" />
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <style>
    [v-cloak] {
      display: none;
    }

    html,
    body,
    ul,
    p {
      padding: 0;
      margin: 0;
      list-style: none;
    }

    .container {
      position: relative;
      width: 2200px;
      height: 600px;
      background: url("/static/citybrain/shgl/img/middle-back.svg") no-repeat;
      background-size: 100% 100%;
      padding: 30px;
      box-sizing: border-box;
      border: 1px #1b259f solid;
    }

    .header-title2[data-v-4d0d1712] {
      width: 100% !important;
    }

    .icon {
      font-size: 50px;
      color: white;
      font-weight: 700;
      position: absolute;
      top: 10px;
      right: 10px;
      cursor: pointer;
    }

    .part {
      width: 50%;
      height: 520px;
      position: relative;
      padding: 0 20px;
      box-sizing: border-box;
    }

    .part-con {
      width: 100%;
      height: 447px;
      display: flex;
    }

    .lists {
      width: 40%;
      height: 100%;
    }

    .lists>p {
      color: #fff;
      font-size: 28px;
      text-align: center;
      margin: 20px 0px;
    }

    .lists>div {
      color: #fff;
      font-size: 28px;
      text-align: center;
      border: 1px white solid;
      height: 60px;
      line-height: 60px;
    }
  </style>
</head>

<body>
  <div id="app" class="container" v-cloak>
    <i class="el-icon-close icon" @click="closeIframe"></i>
    <div class="s-flex">
      <div class="part">
        <nav>
          <s-header-title-2 htype="0" title="重点企业关系分析展示 "></s-header-title-2>
        </nav>
        <div class="part-con">
          <div id="chart01" style="width:60%;height:100%"></div>
          <div class="lists">
            <p>同类企业信息</p>
            <div v-for="(item,index) in list1">
              {{item.name}}
            </div>
          </div>
        </div>
      </div>
      <div class="part">
        <nav>
          <s-header-title-2 htype="0" title="集团企业关系分析展示"></s-header-title-2>
        </nav>
        <div class="part-con">
          <div id="chart02" style="width:60%;height:100%"></div>
          <div class="lists">
            <p>集团内其它企业</p>
            <div v-for="(item,index) in list2">
              {{item.name}}
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>

  <script>
    new Vue({
      el: "#app",
      data() {
        return {
          list1: [],
          list2: [],
          chartData:[
            {
              name:'母子关联企业',
              value1:'60',
              value2:'100',
            },
            {
              name:'财务关联企业',
              value1:'10',
              value2:'40',
            },
            {
              name:'其他关联企业',
              value1:'20',
              value2:'80',
            }
          ]
        };
      },
      mounted() {
        this.initApi()
        this.getChart01('chart02', this.chartData)
      },
      methods: {
        initApi() {

          $api("scjd_xyztxyhx_qygjzb4").then((res) => {
            this.getChart01('chart01', res)
          });
          // $api("scjd_xyztxyhx_qygjzb4").then((res) => {
          //   this.getChart01('chart02', res)
          // });
          $api("scjd_xyztxyhx_tlqyxx5").then((res) => {
            this.list1 = res
          });
          $api("scjd_xyztxyhx_tlqyxx6").then((res) => {
            this.list2 = res
          });
        },
        getChart01(id, chartData) {
          const myChart = echarts.init(document.getElementById(id));
          let xdata = []; //横轴data
          let ydata1 = []; //纵轴data
          let ydata2 = []; //纵轴data
          let indicator = [];
          for (item of chartData) {
            let str={
              text: item.name,
              max: 230,
            }
            xdata.push(item.name);
            ydata1.push(item.value1);
            ydata2.push(item.value2);
            indicator.push(str)
          }
          let option = {
            legend: {
              show: true,
              trigger: 'item',
              orient: 'vertical',
              top: '40px',
              left: '50px',
              textStyle: {
                color: '#fff',
                fontSize: '28'
              },
              data: ['2023', '2024'],
            },
            tooltip: {
              trigger: "item",
              borderWidth: 0,
              borderRadius: 5,
              padding: 15,
              backgroundColor: "#384c63",
              textStyle: {
                color: "#d0e1f5",
                fontSize: 28,
              },
            },
            radar: {
              indicator: indicator,
              center: ["54%", "60%"],
              radius: "75%",
              axisName: {
                color: "#fff",
                fontSize: 26,
              },
              splitArea: {
                show: false,
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: "#118def",
                },
              },
              splitLine: {
                lineStyle: {
                  color: "#118def",
                },
              },
            },
            series: [
              {
                name: "2023",
                type: "radar",
                lineStyle: {
                  width: 4,
                },
                data: [
                  {
                    value: ydata1,
                    symbolSize: 16,
                  },
                ],
                symbolColor: "#fff",
                itemStyle: {
                  color: "#0269CB",
                },
              }, {
                name: "2024",
                type: "radar",
                lineStyle: {
                  width: 4,
                },
                data: [
                  {
                    value: ydata2,
                    symbolSize: 16,
                  },
                ],
                symbolColor: "#fff",
                itemStyle: {
                  color: "#68bbc4",
                },
              }
            ],
          };
          myChart.setOption(option);
        },
        closeIframe() {
          top.commonObj.funCloseIframe({
            name: "xyztxyhx-dialog3",
          });
        },

      },
    });
  </script>
</body>

</html>