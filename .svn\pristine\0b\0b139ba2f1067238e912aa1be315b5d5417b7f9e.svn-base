<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>水利设施-中间地图</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <link
      rel="stylesheet"
      href="/static/citybrain3840/shgl/css/map-dialog.css"
    />
    <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
  </head>
  <style></style>

  <body>
    <div id="map-middle" v-cloak>
      <div class="tree">
        <el-tree
          :data="treeData"
          show-checkbox
          node-key="id"
          ref="tree"
          highlight-current
          @check-change="checkChange"
          class="auth-tree"
          :render-after-expand="false"
          icon-class="el-icon-caret-left"
          :default-checked-keys="[1]"
          default-expand-all
        >
          <div
            style="display: flex; align-items: center"
            slot-scope="{ node, data }"
          >
            <div class="node-lable">
              {{ node.label }}
              <span v-if="data.children">({{data.children.length}})</span>
              <img
                v-if="!data.children"
                class="node-img"
                :src="`/static/citybrain3840/shgl/img/slss/${data.label}.png`"
                alt=""
              />
            </div>
          </div>
        </el-tree>
      </div>
    </div>
  </body>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>

  <script>
    var vm = new Vue({
      el: "#map-middle",
      data() {
        return {
          treeData: [
            {
              id: 1,
              label: "水位监测",
            },
            {
              id: 2,
              label: "内涝重点问题",
            },
            {
              id: 3,
              label: "水情",
            },
            {
              id: 4,
              label: "雨情",
            },
            {
              id: 5,
              label: "排水",
            },
            {
              id: 6,
              label: "易涝点",
            },
          ],
        };
      },
      mounted() {
        top.document.getElementById("map").contentWindow.Work.change3D(7);
        this.getPoint({ id: 1, label: "水位监测" });
      },
      methods: {
        checkChange(item, flag) {
          if (flag) {
            this.getPoint(item);
          } else {
            this.rmPoint(item);
          }
        },
        getPoint(item) {
          $get("/3840/shgl/slsszbfx/slssPoint").then((res) => {
            let result = [];
            let pointData = [];
            result = res.filter((el) => {
              return el.name == item.label;
            });
            let key = [];
            let value = [];
            let icon = "";
            result.forEach((obj, index) => {
              icon = obj.icon;
              if (obj.type === "1") {
                key = ["名称", "行政区", "所属河流", "监测站来源", "实时水位"];
                value = [obj.mc, obj.xzq, obj.sshl, obj.jczly, obj.sssw];
              } else if (obj.type === "2") {
                key = [
                  "名称",
                  "行政区",
                  "警戒水位",
                  "保证水位",
                  "所属河流",
                  "监测站来源",
                ];
                value = [
                  obj.mc,
                  obj.xzq,
                  obj.jjsw,
                  obj.bzsw,
                  obj.sshl,
                  obj.jczly,
                ];
              } else if (obj.type === "3") {
                key = [
                  "名称",
                  "行政区",
                  "汛限水位",
                  "正常水位",
                  "所属河流",
                  "监测站来源",
                ];
                value = [
                  obj.mc,
                  obj.xzq,
                  obj.xxsw,
                  obj.zcsw,
                  obj.sshl,
                  obj.jczly,
                ];
              } else {
                key = ["案发时间", "发生地点", "区域", "监督员名称"];
                value = [obj.afsj, obj.fsdd, obj.qy, obj.jdymc];
              }
              let str = {
                data: {
                  title: obj.name + "详情",
                  key: key,
                  value: value,
                },
                point: obj.lnglat,
              };
              pointData.push(str);
            });
            top.document.getElementById("map").contentWindow.Work.funChange(
              JSON.stringify({
                funcName: "pointLoad", //功能名称
                pointType: icon, //点位类型图标
                pointId: "slss-" + item.id,
                setClick: false,
                pointData: pointData,
                imageConfig: { iconSize: 0.9 },
                size: [0.01, 0.01, 0.01, 0.01],
                popup: {
                  offset: [50, 30],
                },
              })
            );
          });
        },
        rmPoint(item) {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmPoint",
              pointId: "slss-" + item.id, //传id清除单类，不传清除所有
            })
          );
        },
        rmAllPoint() {
          top.document.getElementById("map").contentWindow.Work.funChange(
            JSON.stringify({
              funcName: "rmPoint",
              pointId: "", //传id清除单类，不传清除所有
            })
          );
        },
      },
      destroyed() {
        this.rmAllPoint();
      },
    });
  </script>
</html>
