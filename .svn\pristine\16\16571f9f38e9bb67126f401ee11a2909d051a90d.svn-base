<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>重大危险源分类统计</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <link rel="stylesheet" href="../css/shgl-csaq-zdwxyfltj-left.css" />
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    </head>

    <body>
        <div id="shgl-csaq-zdwxyfltj-left">
            <div class="content">
                <div class="title">
                    <nav style="padding: 20px 45px 0 45px">
                        <s-header-title
                            style="width: 100%"
                            title="重大危险源风险态势评估展示"
                            htype="1"
                        ></s-header-title>
                    </nav>
                </div>
                <div class="titleBox">
                    <div id="barEcharts001" style="height: 450px; width: 950px"></div>
                    <div>
                        <div class="zdwxy">
                            <el-select class="wxlx" v-model="value" placeholder="风险类型" @change="change1">
                                <el-option
                                    v-for="item in options"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                            <el-select class="city" v-model="value1" placeholder="请选择" @change="change">
                                <el-option
                                    v-for="item in options1"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                            <el-select class="wxlx" v-model="value2" placeholder="请选择" @change="changeType">
                                <el-option
                                    v-for="item in options2"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </div>

                        <table class="table">
                            <thead>
                                <tr>
                                    <th v-for="item in thName">{{item}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="item in tableList">
                                    <td>{{item.name}}</td>
                                    <td>{{item.value}}</td>
                                    <td>{{item.type}}</td>
                                    <td>{{item.ssgs}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="title" style="position: relative">
                    <nav style="padding: 20px 45px 0 45px">
                        <s-header-title style="width: 100%" title="重大危险源风险统计" htype="1"></s-header-title>
                    </nav>
                </div>
                <div class="titleBox">
                    <div class="title" style="flex: 1">
                        <nav style="padding: 20px 0 20px 0">
                            <s-header-title2 title="风险源区域分布" htype="2"></s-header-title2>
                        </nav>
                        <div id="barEcharts002" style="height: 400px"></div>
                    </div>
                    <div class="title" style="position: relative">
                        <nav style="padding: 20px 0 20px 0">
                            <s-header-title2 title="风险源等级分布" htype="2"></s-header-title2>
                        </nav>
                        <el-button class="btnContent btn" type="primary">导出</el-button>

                        <div id="pieEcharts001" style="height: 400px"></div>
                    </div>
                </div>
                <div class="titleBox">
                    <div class="title" style="flex: 1">
                        <nav style="padding: 20px 0 20px 0">
                            <s-header-title2 title="风险源风险大类分布" htype="2"></s-header-title2>
                        </nav>
                        <div id="pieEcharts002" style="height: 400px"></div>
                    </div>
                    <div class="title">
                        <nav style="padding: 20px 0 20px 0">
                            <s-header-title2 title="产品导出" htype="2"></s-header-title2>
                        </nav>
                        <div>
                            <div class="cpdc" v-for="item in cpdcList">
                                <div class="box-cpdc">
                                    <div class="box-content">{{item}}</div>
                                    <el-button class="btnContent" type="primary">导出</el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#shgl-csaq-zdwxyfltj-left",
        data: {
            thName: ["风险名称", "行政区别", "风险等级", "所属公司"],
            options: [
                {
                    value: "自然风险",
                    label: "自然风险",
                },
                {
                    value: "社会风险",
                    label: "社会风险",
                },
                {
                    value: "政治风险",
                    label: "政治风险",
                },
                {
                    value: "经济风险",
                    label: "经济风险",
                },
            ],
            value: "自然风险",
            options1: [
                {
                    value: "金华市",
                    label: "金华市",
                },
                {
                    value: "金义新区",
                    label: "金义新区",
                },
                {
                    value: "兰溪市",
                    label: "兰溪市",
                },
                {
                    value: "婺城区",
                    label: "婺城区",
                },
            ],
            value1: "金华市",
            options2: [
                {
                    value: "一级",
                    label: "一级",
                },
                {
                    value: "二级",
                    label: "二级",
                },
                {
                    value: "三级",
                    label: "三级",
                },
                {
                    value: "四级",
                    label: "四级",
                },
            ],
            value2: "一级",
            tableList: [],
            cpdcList: ["风险评估报告", "行业风险评估报告", "区域风险评估报告"],
        },
        mounted() {
            this.open();
            this.initFun();
        },
        methods: {
            changeType(item) {
                $api("shgl_zdwxyfltj_zdwxyfltj002", {
                    value: this.value1,
                    type: item,
                    type1: this.value,
                }).then((res) => {
                    this.tableList = res;
                });
            },
            change1(item) {
                $api("shgl_zdwxyfltj_zdwxyfltj002", {
                    value: this.value1,
                    type: this.value2,
                    type1: item,
                }).then((res) => {
                    this.tableList = res;
                });
            },
            change(item) {
                $api("shgl_zdwxyfltj_zdwxyfltj002", {
                    value: item,
                    type: this.value2,
                    type1: this.value,
                }).then((res) => {
                    this.tableList = res;
                });
            },
            initFun() {
                $api("shgl_zdwxyfltj_zdwxyfltj001").then((res) => {
                    this.getEcharts01("barEcharts001", res);
                });
                $api("shgl_zdwxyfltj_zdwxyfltj002", {
                    value: this.value1,
                    type: this.value2,
                    type1: this.value,
                }).then((res) => {
                    this.tableList = res;
                });
                $api("shgl_zdwxyfltj_zdwxyfltj003").then((res) => {
                    this.getEcharts02("barEcharts002", res);
                });
                $api("shgl_zdwxyfltj_zdwxyfltj004").then((res) => {
                    this.getEcharts03("pieEcharts001", res);
                });
                $api("shgl_zdwxyfltj_zdwxyfltj005").then((res) => {
                    this.getEcharts04("pieEcharts002", res);
                });
            },
            open() {
                let iframe1 = {
                    type: "openIframe",
                    name: "shgl-csaq-zdwxyfltj-bottom",
                    src: baseURL.url + "/static/citybrain/shgl/pages/shgl-csaq-zdwxyfltj-bottom.html",
                    width: "5500px",
                    height: "125px",
                    left: "2200px",
                    top: "1950px",
                    zIndex: "555",
                };
                let iframe2 = {
                    type: "openIframe",
                    name: "shgl-csaq-zdwxyfltj-select",
                    src: baseURL.url + "/static/citybrain/shgl/pages/shgl-csaq-zdwxyfltj-select.html",
                    width: "460px",
                    height: "400px",
                    left: "2210px",
                    top: "230px",
                    zIndex: "100",
                };
                window.parent.postMessage(JSON.stringify(iframe1), "*");
                window.parent.postMessage(JSON.stringify(iframe2), "*");
            },
            getEcharts01(id, echartsData) {
                const myChartsRun = echarts.init(document.getElementById(id));
                let option = {
                    tooltip: {
                        trigger: "item",
                        backgroundColor: "rgba(50,50,50,0.7)",
                        formatter: "{b} </br>{a} {c}",
                        borderColor: "rgba(50,50,50,0.7)",
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    xAxis: {
                        type: "category",
                        data: echartsData.map((item) => item.name),
                        offset: 10,
                        axisLabel: {
                            rotate: 30,
                            textStyle: {
                                fontSize: 30,
                                color: "#fff",
                            },
                        },
                    },
                    yAxis: {
                        type: "value",

                        splitLine: {
                            show: false,
                        },
                        axisLabel: {
                            textStyle: {
                                fontSize: 30,
                                color: "#fff",
                            },
                        },
                    },
                    series: [
                        {
                            name: "园林绿化设施数量",
                            data: echartsData.map((item) => item.value),
                            type: "bar",
                            label: {
                                show: true,
                                textStyle: {
                                    color: "#5087ec",
                                    fontSize: 30,
                                },
                                position: "outside",
                            },
                        },
                    ],
                };
                myChartsRun.setOption(option);
                tools.loopShowTooltip(myChartsRun, option, {
                    loopSeries: true,
                }); //轮播
            },
            getEcharts02(id, echartsData) {
                const myChartsRun = echarts.init(document.getElementById(id));
                let option = {
                    tooltip: {
                        trigger: "item",
                        backgroundColor: "rgba(50,50,50,0.7)",
                        formatter: "{b} </br>{a} {c}",
                        borderColor: "rgba(50,50,50,0.7)",
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    xAxis: {
                        type: "category",
                        data: echartsData.map((item) => item.name),
                        offset: 10,
                        axisLabel: {
                            rotate: 30,
                            textStyle: {
                                fontSize: 30,
                                color: "#fff",
                            },
                        },
                    },
                    yAxis: {
                        type: "value",

                        splitLine: {
                            show: false,
                        },
                        axisLabel: {
                            textStyle: {
                                fontSize: 30,
                                color: "#fff",
                            },
                        },
                    },
                    series: [
                        {
                            name: "园林绿化设施数量",
                            data: echartsData.map((item) => item.value),
                            type: "bar",
                            label: {
                                show: true,
                                textStyle: {
                                    color: "#5087ec",
                                    fontSize: 30,
                                },
                                position: "outside",
                            },
                        },
                    ],
                };
                myChartsRun.setOption(option);
                tools.loopShowTooltip(myChartsRun, option, {
                    loopSeries: true,
                }); //轮播
            },
            getEcharts03(id, echartsData) {
                const myChartsRun = echarts.init(document.getElementById(id));
                let option = {
                    tooltip: {
                        trigger: "item",
                        backgroundColor: "rgba(50,50,50,0.7)",
                        borderColor: "rgba(50,50,50,0.7)",
                        formatter: "{b} : {c}",
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    series: [
                        {
                            type: "pie",
                            radius: "80%",
                            data: echartsData,
                            label: {
                                fontSize: 30,
                                color: "#fff",
                                formatter: "{b} \n {c}",
                            },
                            labelLine: {
                                length: 20,
                                length2: 20,
                            },

                            emphasis: {
                                itemStyle: {
                                    fontSize: 30,
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: "rgba(0, 0, 0, 0.5)",
                                },
                            },
                        },
                    ],
                };
                myChartsRun.setOption(option);
                tools.loopShowTooltip(myChartsRun, option, {
                    loopSeries: true,
                }); //轮播
            },
            getEcharts04(id, echartsData) {
                const myChartsRun = echarts.init(document.getElementById(id));
                let option = {
                    tooltip: {
                        trigger: "item",
                        backgroundColor: "rgba(50,50,50,0.7)",
                        borderColor: "rgba(50,50,50,0.7)",
                        formatter: "{b} : {c}",
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    series: [
                        {
                            type: "pie",
                            radius: "80%",
                            data: echartsData,
                            label: {
                                fontSize: 30,
                                color: "#fff",
                                formatter: "{b} \n {c}",
                            },
                            labelLine: {
                                length: 20,
                                length2: 20,
                            },

                            emphasis: {
                                itemStyle: {
                                    fontSize: 30,
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: "rgba(0, 0, 0, 0.5)",
                                },
                            },
                        },
                    ],
                };
                myChartsRun.setOption(option);
                tools.loopShowTooltip(myChartsRun, option, {
                    loopSeries: true,
                }); //轮播
            },
        },
    });
</script>
