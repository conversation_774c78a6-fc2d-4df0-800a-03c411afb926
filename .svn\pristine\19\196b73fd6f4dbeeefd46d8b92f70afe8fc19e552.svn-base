[v-cloak] {
  display: none;
}
html,body,ul,p{
  padding:0;
  margin:0;
  list-style: none;
}
.container{
  width:2070px;
  height:1850px;
  background: url("/img/right-bg.png") no-repeat;
  background-size: 100% 100%;
  border-radius: 10px;
  padding: 30px 40px;
  box-sizing: border-box;
}

.sbxx{
  width:100%;
  height:490px;
}
.jcyj{
  width:100%;
  height:490px;
  display: flex;
}
.jcyj-left,.jcyj-right{
  width:50%;
  height:100%;
}

.bottom{
  display: flex;
}
.bottom-left,.bottom-right{
  width:50%;
}
.glcx,.glwt{
  width:100%;
  height:490px;
}
.glcx{
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
}
.glcx-item-top{
  width: 400px;
  height: 100px;
  background: url('/static/citybrain/csdn/img/ywt3-Right-new/r-002.png') no-repeat 0 70px;
  background-size: 100% 20%;
  display: flex;
  justify-content: space-around;
}
.glcx-item-top>img{
  margin-top:40px;
}
.glcx-item-top>div{
  margin-top:15px;
  font-size: 40px;
}
.glcx-item-bottom{
  width: 400px;
  text-align: center;
  font-size: 30px;
  color: #fff;
}

/* 表格 */
.table {
  width: 100%;
  height: 480px;
  padding: 10px;
  box-sizing: border-box;
  overflow-y: auto;
}

.table .th {
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-style: italic;
  font-weight: 700;
  font-size: 32px;
  line-height: 80px;
  background: #00396f;
  color: #FFFFFF;
}

.table .th_td {
  letter-spacing: 0px;
  text-align: center;
  flex: 0.16;
}

.table .tbody {
  width: 100%;
  height: calc(100% - 80px);
  overflow: hidden;
}

.table .tbody:hover {
  overflow-y: auto;
}

.table .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table .tr {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 76px;
  line-height: 60px;
  font-size: 28px;
  color: #FFFFFF;
  cursor: pointer;
}

.table .tr:nth-child(2n) {
  background: #00396f;
}

.table .tr:nth-child(2n+1) {
  background: #035b86;
}

.table .tr:hover {
  background-color: #6990b6;
}

.table .tr_td {
  letter-spacing: 0px;
  text-align: center;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


.table1 .th_td:nth-child(1) {
  flex: 0.15;
}
.table1 .th_td:nth-child(2) {
  flex: 0.1;
}
.table1 .th_td:nth-child(3) {
  flex: 0.1;
}
.table1 .th_td:nth-child(4) {
  flex: 0.15;
}
.table1 .th_td:nth-child(5) {
  flex: 0.25;
}
.table1 .th_td:nth-child(6) {
  flex: 0.25;
}

.table2 .th_td:nth-child(6) {
  flex: 0.2;
}
