<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>市场监管</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <style>
            * {
                padding: 0;
                margin: 0;
            }
            #scjg-left {
                width: 1050px;
                height: 1930px;
                background: url("/img/left-bg.png") no-repeat;
                background-size: 100% 100%;
            }
            .value {
                color: #0087ec;
            }
            .active {
                color: #0087ec;
                border-bottom: 2px solid #0087ec;
            }
            .xfwq {
                font-size: 30px;
                color: #fff;
                display: flex;
                justify-content: space-around;
                text-align: center;
            }
            .xfwqBox {
                background: url("../img/itembg.png") no-repeat;
                background-size: 100% 100%;
                padding: 20px 40px;
            }
            .tab {
                display: flex;
                justify-content: space-around;
                font-size: 30px;
                color: #fff;
            }
            .tabName {
                cursor: pointer;
            }
        </style>
    </head>

    <body>
        <div id="scjg-left">
            <div class="content">
                <div class="title">
                    <nav style="padding: 20px 45px">
                        <s-header-title style="width: 100%" title="社会诉求监管" htype="2"></s-header-title>
                    </nav>
                </div>
                <div class="title" style="display: flex">
                    <nav style="padding: 0; flex: 1">
                        <s-header-title2 style="width: 100%" title="社会诉求性质分析" htype="2"></s-header-title2>
                    </nav>
                    <nav style="padding: 0; flex: 1">
                        <s-header-title2 style="width: 100%" title="社会诉求内容分析" htype="2"></s-header-title2>
                    </nav>
                </div>
                <div style="display: flex">
                    <div id="barEcharts01" style="height: 300px; width: 100%"></div>
                    <div id="pieEcharts01" style="height: 300px; width: 100%"></div>
                </div>

                <div class="title" style="display: flex">
                    <nav style="padding: 0; flex: 1">
                        <s-header-title2 style="width: 100%" title="投诉问题处理分析" htype="2"></s-header-title2>
                    </nav>
                    <nav style="padding: 0; flex: 1">
                        <s-header-title2 style="width: 100%" title="投诉热点问题分析" htype="2"></s-header-title2>
                    </nav>
                </div>
                <div style="display: flex">
                    <div id="barEcharts02" style="height: 300px; width: 100%"></div>
                    <div id="pieEcharts02" style="height: 300px; width: 100%"></div>
                </div>
                <div class="title">
                    <nav style="padding: 20px 45px">
                        <s-header-title style="width: 100%" title="12315消费维权指标" htype="2"></s-header-title>
                    </nav>
                </div>
                <div class="xfwq">
                    <div class="xfwqBox" v-for="item in xfwqList">
                        <div class="value">{{item.value}}{{item.unit}}</div>
                        <div>{{item.name}}</div>
                    </div>
                </div>
                <div class="title">
                    <nav style="padding: 0">
                        <s-header-title2 style="width: 100%" title="维权信息变化趋势" htype="2"></s-header-title2>
                    </nav>
                </div>
                <div id="barEcharts03" style="height: 300px"></div>
                <div class="tab">
                    <div
                        class="tabName"
                        v-for="(item ,index) in tab"
                        :class="{active:isActive===index}"
                        @click="change(index)"
                    >
                        {{item}}
                    </div>
                </div>
                <div v-show="isActive===0" id="lineEcharts01" style="height: 300px"></div>
                <div v-show="isActive===1" style="display: flex">
                    <div id="pieEcharts03" style="height: 300px; flex: 1; width: 525px"></div>
                    <div id="barEcharts04" style="height: 300px; flex: 1; width: 525px"></div>
                </div>
                <div v-show="isActive===2" id="pieEcharts04" style="height: 300px; width: 1050px"></div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#scjg-left",
        data: {
            xfwqList: [],
            isActive: 0,
            tab: ["投诉量变化日趋势", "投诉企业", "投诉原因"],
        },
        mounted() {
            this.initFun();
            this.openDiaog();
            this.initMap();
        },
        methods: {
            initFun() {
                $api("ldst_scjg_scjg", { type: 1 }).then((res) => {
                    this.getEcharts01(res);
                });
                $api("ldst_scjg_scjg", { type: 2 }).then((res) => {
                    this.getEcharts02(res);
                });
                $api("ldst_scjg_scjg", { type: 3 }).then((res) => {
                    this.getEcharts03(res);
                });
                $api("ldst_scjg_scjg", { type: 4 }).then((res) => {
                    this.getEcharts04(res);
                });
                $api("ldst_scjg_scjg", { type: 5 }).then((res) => {
                    this.xfwqList = res;
                });
                $api("ldst_scjg_scjg", { type: 6 }).then((res) => {
                    this.getEcharts05(res);
                });
                $api("ldst_scjg_scjg", { type: 7 }).then((res) => {
                    this.getEcharts06(res);
                });
                $api("ldst_scjg_scjg", { type: 8 }).then((res) => {
                    this.getEcharts07(res);
                });
                $api("ldst_scjg_scjg", { type: 9 }).then((res) => {
                    this.getEcharts08(res);
                });
                $api("ldst_scjg_scjg", { type: 10 }).then((res) => {
                    this.getEcharts09(res);
                });
            },
            change(index) {
                this.isActive = index;
            },
            getEcharts02(res) {
                let myCharts = echarts.init(document.getElementById("pieEcharts01"));
                let option = {
                    tooltip: {
                        trigger: "item",
                        formatter: "{b}\n{c}%",
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    series: [
                        {
                            type: "pie",

                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderRadius: 10,
                                borderColor: "#2b516f",
                                borderWidth: 2,
                            },
                            label: {
                                formatter: "{b}\n{c}%",
                                textStyle: {
                                    fontSize: 30,
                                    color: "#fff",
                                },
                            },
                            labelLine: {
                                show: true,
                                length: 15,
                                length2: 15,
                            },
                            data: res,
                        },
                    ],
                };
                myCharts.setOption(option);
            },
            getEcharts01(res) {
                let myCharts = echarts.init(document.getElementById("barEcharts01"));
                let yData = res.map((item) => {
                    return item.name;
                });
                let value = res.map((item) => {
                    return item.value;
                });
                let value1 = res.map((item) => {
                    return item.value1;
                });
                let option = {
                    legend: {
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    grid: {
                        bottom: "20%",
                        left: "12%",
                        right: "12%",
                    },
                    yAxis: [
                        {
                            type: "value",
                            position: "left",
                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                        {
                            type: "value",
                            position: "right",
                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                    ],

                    xAxis: {
                        type: "category",
                        data: yData,
                        offset: 15,
                        axisLabel: {
                            fontSize: 30,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            name: "诉求量",
                            type: "bar",
                            data: value,
                        },
                        {
                            yAxisIndex: 1,
                            name: "占比",
                            type: "line",
                            data: value1,
                        },
                    ],
                };
                myCharts.setOption(option);
            },
            getEcharts03(res) {
                let myCharts = echarts.init(document.getElementById("barEcharts02"));
                let yData = res.map((item) => {
                    return item.name;
                });
                let value = res.map((item) => {
                    return item.value;
                });
                let value1 = res.map((item) => {
                    return item.value1;
                });
                let option = {
                    legend: {
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    grid: {
                        bottom: "20%",
                        left: "12%",
                        right: "12%",
                    },
                    yAxis: [
                        {
                            type: "value",
                            position: "left",
                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                        {
                            type: "value",
                            position: "right",
                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                    ],

                    xAxis: {
                        type: "category",
                        data: yData,
                        offset: 15,
                        axisLabel: {
                            fontSize: 30,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            name: "诉求量",
                            type: "bar",
                            data: value,
                        },
                        {
                            yAxisIndex: 1,
                            name: "占比",
                            type: "line",
                            data: value1,
                        },
                    ],
                };
                myCharts.setOption(option);
            },
            getEcharts04(res) {
                let myCharts = echarts.init(document.getElementById("pieEcharts02"));
                let yData = res.map((item) => {
                    return item.name;
                });
                let value = res.map((item) => {
                    return item.value;
                });

                let option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    grid: {
                        bottom: "12%",
                        left: "32%",
                        top: "5%",
                        right: "0%",
                    },
                    xAxis: [
                        {
                            type: "value",

                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                    ],

                    yAxis: {
                        type: "category",
                        data: yData,
                        offset: 15,
                        axisLabel: {
                            fontSize: 30,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            name: "数据",
                            type: "bar",
                            data: value,
                        },
                    ],
                };
                myCharts.setOption(option);
            },
            getEcharts05(res) {
                let myCharts = echarts.init(document.getElementById("barEcharts03"));
                let yData = res.map((item) => {
                    return item.name;
                });
                let value = res.map((item) => {
                    return item.value;
                });
                let value1 = res.map((item) => {
                    return item.value1;
                });
                let option = {
                    legend: {
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    grid: {
                        bottom: "20%",
                        left: "12%",
                        right: "12%",
                    },
                    yAxis: [
                        {
                            type: "value",

                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                formatter: "{value}条",
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                    ],

                    xAxis: {
                        type: "category",
                        data: yData,
                        offset: 15,
                        axisLabel: {
                            fontSize: 30,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            name: "维权信息数",
                            type: "bar",
                            data: value,
                        },
                    ],
                };
                myCharts.setOption(option);
            },
            getEcharts06(res) {
                let myCharts = echarts.init(document.getElementById("lineEcharts01"));
                let yData = res.map((item) => {
                    return item.name;
                });
                let value = res.map((item) => {
                    return item.value;
                });

                let option = {
                    legend: {
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                    },
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    grid: {
                        bottom: "20%",
                        left: "12%",
                        right: "12%",
                    },
                    yAxis: [
                        {
                            type: "value",
                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                formatter: "{value}条",
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                    ],

                    xAxis: {
                        type: "category",
                        data: yData,
                        offset: 15,
                        axisLabel: {
                            fontSize: 30,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            name: "投诉量",
                            type: "line",
                            data: value,
                        },
                    ],
                };
                myCharts.setOption(option);
            },
            getEcharts07(res) {
                let myCharts = echarts.init(document.getElementById("pieEcharts03"));
                let option = {
                    tooltip: {
                        trigger: "item",
                        formatter: "{b}\n{c}%",
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    series: [
                        {
                            type: "pie",
                            radius: ["50%", "80%"],
                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderRadius: 10,
                                borderColor: "#2b516f",
                                borderWidth: 2,
                            },
                            label: {
                                formatter: "{b}\n{c}%",
                                textStyle: {
                                    fontSize: 30,
                                    color: "#fff",
                                },
                            },
                            labelLine: {
                                show: true,
                                length: 15,
                                length2: 15,
                            },
                            data: res,
                        },
                    ],
                };
                myCharts.setOption(option);
            },
            getEcharts08(res) {
                let myCharts = echarts.init(document.getElementById("barEcharts04"));
                let yData = res.map((item) => {
                    return item.name;
                });
                let value = res.map((item) => {
                    return item.value;
                });

                let option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    grid: {
                        bottom: "12%",
                        left: "32%",
                        top: "5%",
                        right: "0%",
                    },
                    xAxis: [
                        {
                            type: "value",

                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                fontSize: 30,
                                color: "#ffff",
                            },
                        },
                    ],

                    yAxis: {
                        type: "category",
                        data: yData,
                        offset: 15,
                        axisLabel: {
                            fontSize: 30,
                            color: "#ffff",
                        },
                    },
                    series: [
                        {
                            name: "数据",
                            type: "bar",
                            data: value,
                        },
                    ],
                };
                myCharts.setOption(option);
            },
            getEcharts09(res) {
                let myCharts = echarts.init(document.getElementById("pieEcharts04"));
                let option = {
                    tooltip: {
                        trigger: "item",
                        formatter: "{b}\n{c}%",
                        textStyle: {
                            fontSize: 30,
                        },
                    },
                    series: [
                        {
                            type: "pie",
                            radius: ["50%", "80%"],
                            avoidLabelOverlap: false,
                            itemStyle: {
                                borderRadius: 10,
                                borderColor: "#2b516f",
                                borderWidth: 2,
                            },
                            label: {
                                formatter: "{b}\n{c}%",
                                textStyle: {
                                    fontSize: 30,
                                    color: "#fff",
                                },
                            },
                            labelLine: {
                                show: true,
                                length: 15,
                                length2: 15,
                            },
                            data: res,
                        },
                    ],
                };
                myCharts.setOption(option);
            },
            openDiaog() {
                let diaog = {
                    type: "openIframe",
                    name: "scjg-dialog",
                    src: baseURL.url + "/static/citybrain3840/scjg/pages/scjg-middle.html",
                    left: "calc(50% - 850px)",
                    top: "55%",
                    width: "1720px",
                    height: "1010px",
                    zIndex: "10",
                    argument: {
                        status: "",
                    },
                };
                top.window.parent.postMessage(JSON.stringify(diaog), "*");
            },
            initMap() {
                top.document.getElementById("map").contentWindow.Work.change3D(9);
                // top.document.getElementById("map").contentWindow.Work.funChange(
                //   JSON.stringify({
                //     funcName: "flyto", //功能名称
                //     flyData: {
                //       center: [119.98478050597587, 29.18613226366889],
                //       zoom: 9,
                //       pitch: 28,
                //       bearing: 0,
                //       duration: 4000, //飞行时间（建议加上）
                //     },
                //   })
                // );
                $get("/textCity.json").then((res) => {
                    let textData = [];
                    top.document.getElementById("map").contentWindow.Work.funChange(
                        JSON.stringify({
                            funcName: "3Dtext", //功能名称
                            textData: res,
                            textSize: 35,
                        })
                    );
                });
            },
        },
    });
</script>
