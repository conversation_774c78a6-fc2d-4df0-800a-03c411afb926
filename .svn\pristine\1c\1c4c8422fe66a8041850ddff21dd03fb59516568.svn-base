<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>市场主体情况分析</title>
        <link rel="stylesheet" href="./css/hjbh-right/common.css" />
        <link rel="stylesheet" href="./css/scztjg-right.css" />
        <script src="js/lib/jquery-3.4.1.min.js"></script>
        <script src="js/lib/vue.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <style>
            .titleContent {
                margin: 0 !important;
            }
            .table {
                /* width: 640px; */
                font-size: 30px;
                color: #fff;
                border: 2px solid #0075a5;
                border-collapse: collapse;
            }

            .table th {
                /* line-height: 50px; */
                background-color: #021e4855;
                border-bottom: 1px solid #34445f;
                border-right: 1px solid #34445f;
            }
            .table th:last-child {
                border-right: none;
            }
            .table tbody tr td {
                border-bottom: 1px solid #34445f;
                border-right: 1px solid #34445f;
            }
            .table tbody tr td:last-child {
                border-right: none;
                /* border-bottom: none; */
            }
            .table tbody tr:last-child {
                border-bottom: 2px solid #0075a5 !important;
            }
            .table tbody {
                text-align: center;
                background-color: #021e4855;
                color: #fff;
                /* line-height: 70px; */
            }
            main {
                display: inline-block;
            }
            .centerBOX {
                display: flex;
                padding: 0 45px;
                flex-direction: row;
                width: 1934px;
                /* height: 400px; */
            }
            .centerBOX .echartsBox {
                width: 400px;
                text-align: center;
            }

            .qyfb {
                font-size: 30px;
                color: #fff;
                text-align: center;
                font-weight: bold;
            }
            .centerTable {
                height: 300px;
                margin-right: 150px;
                margin-left: 100px;
            }
            .active {
                color: #0075a5;
            }
        </style>
    </head>

    <body>
        <main id="sczj">
            <!-- <div class="top">
                <nav style="margin: 20px">
                    <s-header-title title="市场主体历年新增和退出情况" :data-time="time" htype="1"></s-header-title>
                </nav>
                <div class="topContent" id="lineChart1"></div>
            </div>
            <div class="center">
                <nav style="margin: 20px">
                    <s-header-title title="市场主体数量分布情况" :data-time="time" htype="1"></s-header-title>
                </nav>
                <div class="centerContent" id="lineChart2"></div>
            </div> -->
            <div class="bottom">
                <nav style="margin: 0 20px">
                    <s-header-title title="行业分类情况" :data-time="time" htype="1"></s-header-title>
                </nav>
                <div id="lineChart3" style="height: 345px"></div>
            </div>
            <div class="title">
                <nav style="padding: 0 45px">
                    <s-header-title style="width: 100%" title="市场执法" htype="1"></s-header-title>
                </nav>
            </div>
            <div class="centerBOX">
                <table class="table">
                    <thead>
                        <tr>
                            <th v-for="item in zfName">{{item}}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="item in zfList">
                            <td>{{item.name}}</td>
                            <td>{{item.msg}}</td>
                            <td>{{item.jg}}</td>
                        </tr>
                    </tbody>
                </table>
                <div>
                    <div class="qyfb">区域分布</div>
                    <div id="barEcharts001" style="width: 640px; height: 300px"></div>
                </div>
                <div>
                    <div
                        style="
                            display: flex;
                            font-size: 30px;
                            color: #fff;
                            justify-content: space-around;
                            position: absolute;
                            cursor: pointer;
                        "
                    >
                        <div v-for="(item,index) in ny" :class="{active:isActive===index}" @click="change(index)">
                            {{item}}
                        </div>
                    </div>
                    <div class="qyfb">时间趋势</div>
                    <div id="barEcharts002" style="width: 640px; height: 300px"></div>
                </div>
            </div>
            <div class="title">
                <nav style="padding: 0 45px">
                    <s-header-title style="width: 100%" title="经营异常" htype="1"></s-header-title>
                </nav>
            </div>
            <div class="centerBOX">
                <table class="table centerTable">
                    <thead>
                        <tr>
                            <th>企业名称</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="item in zfList">
                            <td>{{item.name}}</td>
                        </tr>
                    </tbody>
                </table>
                <div class="echartsBox">
                    <div class="qyfb">区域分布</div>
                    <div id="barEcharts003" style="width: 640px; height: 300px"></div>
                </div>
                <div class="echartsBox">
                    <div
                        style="
                            display: flex;
                            font-size: 30px;
                            color: #fff;
                            justify-content: space-around;
                            position: absolute;
                            cursor: pointer;
                        "
                    >
                        <div v-for="(item,index) in ny" :class="{active:isActiveOne===index}" @click="changeOne(index)">
                            {{item}}
                        </div>
                    </div>
                    <div class="qyfb">时间趋势</div>
                    <div id="barEcharts004" style="width: 640px; height: 300px"></div>
                </div>
            </div>
            <div class="title">
                <nav style="padding: 0 45px">
                    <s-header-title style="width: 100%" title="投诉处罚数据" htype="1"></s-header-title>
                </nav>
            </div>
            <div class="centerBOX">
                <table class="table centerTable">
                    <thead>
                        <tr>
                            <th>企业名称</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>日立电梯杭州工程公司</td>
                        </tr>

                        <tr>
                            <td>金华点源信息咨询有限公司</td>
                        </tr>
                        <tr>
                            <td>杭州西奥电梯金华分公司</td>
                        </tr>
                        <tr>
                            <td>金华市赫莲娜化妆品有限公司</td>
                        </tr>
                    </tbody>
                </table>
                <div>
                    <div class="qyfb">区域分布</div>
                    <div id="barEcharts005" style="width: 640px; height: 300px"></div>
                </div>
                <div>
                    <div
                        style="
                            display: flex;
                            font-size: 30px;
                            color: #fff;
                            justify-content: space-around;
                            position: absolute;
                            cursor: pointer;
                        "
                    >
                        <div v-for="(item,index) in ny" :class="{active:isActiveTwo===index}" @click="changeTwo(index)">
                            {{item}}
                        </div>
                    </div>
                    <div class="qyfb">时间趋势</div>
                    <div id="barEcharts006" style="width: 640px; height: 300px"></div>
                </div>
            </div>
        </main>
        <script src="./js/lib/echarts.js"></script>
        <script src="./js/lib/echarts-auto-tooltip.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="./js/scztjg-right/lineChart.js"></script>
        <script src="./js/scztjg-right/mixChart1.js"></script>
        <script src="./js/scztjg-right/mixChart2.js"></script>
        <script>
            //指标数据
            var app = new Vue({
                el: "#sczj",
                data: {
                    isActive: 0,
                    isActiveOne: 0,
                    isActiveTwo: 0,
                    time: "2022年4月",
                    zfName: ["企业名称", "违法信息", "处罚结果"],
                    zfList: [],
                    ny: ["年", "月"],
                },
                methods: {
                    changeTwo(index) {
                        this.isActiveTwo = index;
                        if (this.isActiveTwo == 0) {
                            $api("scjgqk-right008", { type: "年" }).then((res) => {
                                this.getEcharts04("barEcharts006", res);
                            });
                        } else {
                            $api("scjgqk-right008", { type: "月" }).then((res) => {
                                this.getEcharts04("barEcharts006", res);
                            });
                        }
                    },
                    changeOne(index) {
                        this.isActiveOne = index;
                        if (this.isActiveOne === 0) {
                            $api("scjgqk-right006", { type: "年" }).then((res) => {
                                this.getEcharts04("barEcharts004", res);
                            });
                        } else {
                            $api("scjgqk-right006", { type: "月" }).then((res) => {
                                this.getEcharts04("barEcharts004", res);
                            });
                        }
                    },
                    change(index) {
                        this.isActive = index;
                        if (this.isActive === 0) {
                            $api("scjgqk-right004", { type: "年" }).then((res) => {
                                this.getEcharts02("barEcharts002", res);
                            });
                        } else {
                            $api("scjgqk-right004", { type: "月" }).then((res) => {
                                this.getEcharts02("barEcharts002", res);
                            });
                        }
                    },
                    initFun() {
                        let that = this;
                        $api("scjgqk-right002").then((res) => {
                            that.zfList = res;
                        });
                        $api("scjgqk-right003").then((res) => {
                            that.getEcharts01("barEcharts001", res);
                        });
                        $api("scjgqk-right004", { type: "年" }).then((res) => {
                            that.getEcharts02("barEcharts002", res);
                        });
                        $api("scjgqk-right005").then((res) => {
                            that.getEcharts03("barEcharts003", res);
                        });
                        $api("scjgqk-right006", { type: "年" }).then((res) => {
                            that.getEcharts04("barEcharts004", res);
                        });
                        $api("scjgqk-right007").then((res) => {
                            that.getEcharts03("barEcharts005", res);
                        });
                        $api("scjgqk-right008", { type: "年" }).then((res) => {
                            that.getEcharts04("barEcharts006", res);
                        });
                        // $api("/hjbh-right/scztjg-right/scztjgRight001").then((res) => {
                        //     let data1 = res.map((o) => {
                        //         return {
                        //             year: o.year,
                        //             value: o.newly_added,
                        //         };
                        //     });
                        //     let data2 = res.map((o) => {
                        //         return {
                        //             year: o.year,
                        //             value: o.sing_out,
                        //         };
                        //     });
                        //     that.showLine1({
                        //         data1,
                        //         data2,
                        //     });
                        // });
                        // $api("/hjbh-right/scztjg-right/scztjgRight002").then((res) => {
                        //     let data1 = res.map((o) => {
                        //         return {
                        //             name: o.type_name,
                        //             value: o.households,
                        //         };
                        //     });
                        //     let data2 = res.map((o) => {
                        //         return {
                        //             name: o.type_name,
                        //             value: o.increase,
                        //         };
                        //     });

                        //     that.showLine2({
                        //         data1,
                        //         data2,
                        //     });
                        // });
                        $api("/hjbh-right/scztjg-right/scztjgRight003").then((res) => {
                            let data1 = res.map((o) => {
                                return {
                                    name: o.type_name,
                                    value: o.households,
                                };
                            });
                            let data2 = res.map((o) => {
                                return {
                                    name: o.type_name,
                                    value: o.increase,
                                };
                            });

                            that.showLine3({
                                data1,
                                data2,
                            });
                        });
                    },
                    // showLine1(res) {
                    //     // 市场主体历年新增和退出情况
                    //     let llxz_xdata = []; //横轴data
                    //     let llxz_ydata1 = []; //纵轴data

                    //     for (let item of res.data1) {
                    //         llxz_xdata.push(item.year);
                    //         llxz_ydata1.push(item.value);
                    //     }

                    //     let llxz_ydata2 = []; //纵轴data
                    //     for (let item of res.data2) {
                    //         llxz_ydata2.push(item.value);
                    //     }

                    //     let llxzMyChart = echarts.init(document.getElementById("lineChart1"));
                    //     llxzOption.xAxis[0].data = llxz_xdata;
                    //     llxzOption.series[0].data = llxz_ydata1;
                    //     llxzOption.series[1].data = llxz_ydata2;
                    //     llxzMyChart.setOption(llxzOption);
                    //     tools.loopShowTooltip(llxzMyChart, llxzOption, {
                    //         loopSeries: true,
                    //     }); //轮播
                    // },
                    // showLine2(res) {
                    //     // 市场主体数量分布情况

                    //     let sjfb_xdata = []; //横轴data
                    //     let sjfb_ydata1 = []; //纵轴data

                    //     for (let item of res.data1) {
                    //         sjfb_xdata.push(item.name);
                    //         sjfb_ydata1.push((item.value / 10000).toFixed(2));
                    //     }
                    //     let sjfb_ydata2 = []; //纵轴data
                    //     for (let item of res.data2) {
                    //         sjfb_ydata2.push(item.value.toFixed(2));
                    //     }

                    //     let sjfbMyChart = echarts.init(document.getElementById("lineChart2"));
                    //     sjfbOption.xAxis[0].data = sjfb_xdata;
                    //     sjfbOption.series[0].data = sjfb_ydata1;
                    //     sjfbOption.series[1].data = sjfb_ydata2;
                    //     sjfbMyChart.setOption(sjfbOption);
                    //     tools.loopShowTooltip(sjfbMyChart, sjfbOption, {
                    //         loopSeries: true,
                    //     }); //轮播
                    // },
                    showLine3(res) {
                        // 市场主体行业分类

                        let hyfl_xdata = []; //横轴data
                        let hyfl_ydata1 = []; //纵轴data

                        for (let item of res.data1) {
                            hyfl_xdata.push(item.name);
                            hyfl_ydata1.push((item.value / 10000).toFixed(2));
                        }
                        let hyfl_ydata2 = []; //纵轴data
                        for (let item of res.data2) {
                            hyfl_ydata2.push(item.value.toFixed(2));
                        }

                        let hyflMyChart = echarts.init(document.getElementById("lineChart3"));
                        hyflOption.xAxis[0].data = hyfl_xdata;
                        hyflOption.series[0].data = hyfl_ydata1;
                        hyflOption.series[1].data = hyfl_ydata2;
                        hyflMyChart.setOption(hyflOption);
                        tools.loopShowTooltip(hyflMyChart, hyflOption, {
                            loopSeries: true,
                        }); //轮播
                    },
                    getEcharts01(dom, echartData) {
                        let echarts1 = echarts.init(document.getElementById(dom));
                        var xData = echartData.map((item) => item.name);
                        var yData = echartData.map((item) => item.value);
                        var yData1 = echartData.map((item) => item.value1);

                        let option = {
                            legend: {
                                x: "center",
                                y: "20px",
                                textStyle: {
                                    color: "#f2f2f2",
                                    fontSize: 30,
                                },
                                itemHeight: 30,
                                itemWidth: 45,
                                itemGap: 20,
                                data: ["违法企业数量", "违法企业占比"],
                            },

                            tooltip: {
                                trigger: "axis",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                axisPointer: {
                                    type: "shadow",
                                },
                                textStyle: {
                                    color: "white",
                                    fontSize: "30",
                                },
                            },
                            xAxis: {
                                type: "category",
                                data: xData,
                                axisLine: {
                                    lineStyle: {
                                        color: "#405886",
                                    },
                                },
                                axisLabel: {
                                    show: true,
                                    rotate: 40,
                                    textStyle: {
                                        color: "#FFF",
                                        fontSize: 28,
                                    },
                                },
                            },
                            grid: {
                                left: "0",
                                right: "0",
                                bottom: "3%",
                                containLabel: true,
                            },
                            yAxis: [
                                {
                                    type: "value",

                                    position: "left",
                                    splitLine: {
                                        show: false,
                                    },
                                    axisLabel: {
                                        show: true,

                                        textStyle: {
                                            color: "#FFF",
                                            fontSize: 28,
                                        },
                                    },
                                },
                                {
                                    type: "value",
                                    position: "right",
                                    min: 0,
                                    max: 10,

                                    splitLine: {
                                        show: false,
                                    },
                                    axisLabel: {
                                        show: true,

                                        textStyle: {
                                            color: "#FFF",
                                            fontSize: 28,
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    name: "违法企业数量",
                                    data: yData,
                                    type: "bar",
                                    color: "#4d83e5",
                                },
                                {
                                    name: "违法企业占比",
                                    type: "line",
                                    yAxisIndex: 1,
                                    symbolSize: 10,
                                    lineStyle: {
                                        width: 5,
                                    },
                                    itemStyle: {
                                        normal: {
                                            color: "#68bbc4",
                                        },
                                    },
                                    data: yData1,
                                },
                            ],
                        };

                        echarts1.setOption(option);
                    },
                    getEcharts02(dom, echartData) {
                        let echarts2 = echarts.init(document.getElementById(dom));
                        var xData = echartData.map((item) => item.name);
                        var yData = echartData.map((item) => item.value);

                        let option = {
                            tooltip: {
                                trigger: "axis",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                axisPointer: {
                                    type: "shadow",
                                },
                                textStyle: {
                                    color: "white",
                                    fontSize: "30",
                                },
                            },
                            xAxis: {
                                type: "category",
                                data: xData,
                                axisLine: {
                                    lineStyle: {
                                        color: "#405886",
                                    },
                                },
                                axisLabel: {
                                    show: true,
                                    rotate: 40,
                                    textStyle: {
                                        color: "#FFF",
                                        fontSize: 28,
                                    },
                                },
                            },
                            grid: {
                                left: "0",
                                right: "0",
                                bottom: "3%",
                                containLabel: true,
                            },
                            yAxis: [
                                {
                                    type: "value",

                                    position: "left",
                                    splitLine: {
                                        show: false,
                                    },
                                    axisLabel: {
                                        show: true,

                                        textStyle: {
                                            color: "#FFF",
                                            fontSize: 28,
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    data: yData,
                                    type: "bar",
                                    color: "#4d83e5",
                                },
                            ],
                        };

                        echarts2.setOption(option);
                    },
                    getEcharts03(dom, echartData) {
                        let echarts3 = echarts.init(document.getElementById(dom));
                        var xData = echartData.map((item) => item.name);
                        var yData = echartData.map((item) => item.value);
                        var yData1 = echartData.map((item) => item.value1);

                        let option = {
                            legend: {
                                x: "center",
                                y: "20px",
                                textStyle: {
                                    color: "#f2f2f2",
                                    fontSize: 30,
                                },
                                itemHeight: 30,
                                itemWidth: 45,
                                itemGap: 20,
                                data: ["异常企业数量", "异常企业占比"],
                            },

                            tooltip: {
                                trigger: "axis",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                axisPointer: {
                                    type: "shadow",
                                },
                                textStyle: {
                                    color: "white",
                                    fontSize: "30",
                                },
                            },
                            xAxis: {
                                type: "category",
                                data: xData,
                                axisLine: {
                                    lineStyle: {
                                        color: "#405886",
                                    },
                                },
                                axisLabel: {
                                    show: true,
                                    rotate: 40,
                                    textStyle: {
                                        color: "#FFF",
                                        fontSize: 28,
                                    },
                                },
                            },
                            grid: {
                                left: "0",
                                right: "0",
                                bottom: "3%",
                                containLabel: true,
                            },
                            yAxis: [
                                {
                                    type: "value",

                                    position: "left",
                                    splitLine: {
                                        show: false,
                                    },
                                    axisLabel: {
                                        show: true,

                                        textStyle: {
                                            color: "#FFF",
                                            fontSize: 28,
                                        },
                                    },
                                },
                                {
                                    type: "value",
                                    position: "right",

                                    splitLine: {
                                        show: false,
                                    },
                                    axisLabel: {
                                        show: true,

                                        textStyle: {
                                            color: "#FFF",
                                            fontSize: 28,
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    name: "异常企业数量",
                                    data: yData,
                                    type: "bar",
                                    color: "#4d83e5",
                                },
                                {
                                    name: "异常企业占比",
                                    type: "line",
                                    yAxisIndex: 1,
                                    symbolSize: 10,
                                    lineStyle: {
                                        width: 5,
                                    },
                                    itemStyle: {
                                        normal: {
                                            color: "#68bbc4",
                                        },
                                    },
                                    data: yData1,
                                },
                            ],
                        };

                        echarts3.setOption(option);
                    },
                    getEcharts04(dom, echartData) {
                        let echarts4 = echarts.init(document.getElementById(dom));
                        var xData = echartData.map((item) => item.name);
                        var yData = echartData.map((item) => item.value);

                        let option = {
                            tooltip: {
                                trigger: "axis",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                axisPointer: {
                                    type: "shadow",
                                },
                                textStyle: {
                                    color: "white",
                                    fontSize: "30",
                                },
                            },
                            xAxis: {
                                type: "category",
                                data: xData,
                                axisLine: {
                                    lineStyle: {
                                        color: "#405886",
                                    },
                                },
                                axisLabel: {
                                    show: true,
                                    rotate: 40,
                                    textStyle: {
                                        color: "#FFF",
                                        fontSize: 28,
                                    },
                                },
                            },
                            grid: {
                                left: "0",
                                right: "0",
                                bottom: "3%",
                                containLabel: true,
                            },
                            yAxis: [
                                {
                                    type: "value",

                                    position: "left",
                                    splitLine: {
                                        show: false,
                                    },
                                    axisLabel: {
                                        show: true,

                                        textStyle: {
                                            color: "#FFF",
                                            fontSize: 28,
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    data: yData,
                                    type: "bar",
                                    color: "#4d83e5",
                                },
                            ],
                        };

                        echarts4.setOption(option);
                    },
                    getEcharts05(dom, echartData) {
                        let echarts5 = echarts.init(document.getElementById(dom));
                        var xData = echartData.map((item) => item.name);
                        var yData = echartData.map((item) => item.value);
                        var yData1 = echartData.map((item) => item.value1);

                        let option = {
                            legend: {
                                x: "center",
                                y: "20px",
                                textStyle: {
                                    color: "#f2f2f2",
                                    fontSize: 30,
                                },
                                itemHeight: 30,
                                itemWidth: 45,
                                itemGap: 20,
                                data: ["异常企业数量", "异常企业占比"],
                            },

                            tooltip: {
                                trigger: "axis",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                axisPointer: {
                                    type: "shadow",
                                },
                                textStyle: {
                                    color: "white",
                                    fontSize: "30",
                                },
                            },
                            xAxis: {
                                type: "category",
                                data: xData,
                                axisLine: {
                                    lineStyle: {
                                        color: "#405886",
                                    },
                                },
                                axisLabel: {
                                    show: true,
                                    rotate: 40,
                                    textStyle: {
                                        color: "#FFF",
                                        fontSize: 28,
                                    },
                                },
                            },
                            grid: {
                                left: "0",
                                right: "0",
                                bottom: "3%",
                                containLabel: true,
                            },
                            yAxis: [
                                {
                                    type: "value",
                                    min: 0,
                                    max: 250,
                                    position: "left",
                                    splitLine: {
                                        show: false,
                                    },
                                    axisLabel: {
                                        show: true,

                                        textStyle: {
                                            color: "#FFF",
                                            fontSize: 28,
                                        },
                                    },
                                },
                                {
                                    type: "value",
                                    position: "right",
                                    min: 0,
                                    max: 250,
                                    splitLine: {
                                        show: false,
                                    },
                                    axisLabel: {
                                        show: true,

                                        textStyle: {
                                            color: "#FFF",
                                            fontSize: 28,
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    name: "异常企业数量",
                                    data: yData,
                                    type: "bar",
                                    color: "#4d83e5",
                                },
                                {
                                    name: "异常企业占比",
                                    type: "line",
                                    yAxisIndex: 0,
                                    symbolSize: 10,
                                    lineStyle: {
                                        width: 5,
                                    },
                                    itemStyle: {
                                        normal: {
                                            color: "#68bbc4",
                                        },
                                    },
                                    data: yData1,
                                },
                            ],
                        };

                        echarts5.setOption(option);
                    },
                    getEcharts06(dom, echartData) {
                        let echarts6 = echarts.init(document.getElementById(dom));
                        var xData = echartData.map((item) => item.name);
                        var yData = echartData.map((item) => item.value);

                        let option = {
                            tooltip: {
                                trigger: "axis",
                                borderWidth: 0,
                                backgroundColor: "rgba(0, 0, 0, 0.6)",
                                axisPointer: {
                                    type: "shadow",
                                },
                                textStyle: {
                                    color: "white",
                                    fontSize: "30",
                                },
                            },
                            xAxis: {
                                type: "category",
                                data: xData,
                                axisLine: {
                                    lineStyle: {
                                        color: "#405886",
                                    },
                                },
                                axisLabel: {
                                    show: true,
                                    rotate: 40,
                                    textStyle: {
                                        color: "#FFF",
                                        fontSize: 28,
                                    },
                                },
                            },
                            grid: {
                                left: "0",
                                right: "0",
                                bottom: "3%",
                                containLabel: true,
                            },
                            yAxis: [
                                {
                                    type: "value",
                                    min: 0,
                                    max: 250,
                                    position: "left",
                                    splitLine: {
                                        show: false,
                                    },
                                    axisLabel: {
                                        show: true,

                                        textStyle: {
                                            color: "#FFF",
                                            fontSize: 28,
                                        },
                                    },
                                },
                            ],
                            series: [
                                {
                                    data: yData,
                                    type: "bar",
                                    color: "#4d83e5",
                                },
                            ],
                        };

                        echarts6.setOption(option);
                    },
                },
                mounted() {
                    this.initFun();
                    //this.showChart()
                },
            });
        </script>
    </body>
</html>
