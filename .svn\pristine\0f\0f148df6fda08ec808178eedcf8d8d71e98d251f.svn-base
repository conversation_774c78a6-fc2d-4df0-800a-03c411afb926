//This file is automatically rebuilt by the Cesium build process.
export default "/**\n\
 * A built-in GLSL floating-point constant for <code>pi/3</code>.\n\
 *\n\
 * @alias czm_piOverThree\n\
 * @glslConstant\n\
 *\n\
 * @see CesiumMath.PI_OVER_THREE\n\
 *\n\
 * @example\n\
 * // GLSL declaration\n\
 * const float czm_piOverThree = ...;\n\
 *\n\
 * // Example\n\
 * float pi = 3.0 * czm_piOverThree;\n\
 */\n\
const float czm_piOverThree = 1.0471975511965976;\n\
";
