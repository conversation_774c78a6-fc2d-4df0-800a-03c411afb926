<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>人事服务</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css">
    <link rel="stylesheet" href="/static/citybrain/ggfw/css/rsefw.css">
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script> <!-- 轮播toolTip -->
</head>


<body>
    <div id="app" class="container" v-cloak>
      <div class="first-con">
          <div class="left-con">
            <div class="mfhx-con">
                <li v-for="(item,index) in rsfwList" :index="index">
                    <img src="/static/citybrain/ggfw/img/list.png" alt="" style="width: 25px;height: 25px;">
                    <div class="img-right">
                        <div class="name">{{item.name}}</div>
                        <div class="value" style="text-align: left;">{{item.value}}<span>{{item.unit}}</span></div>
                    </div>
                </li>
            </div>
            <div class="content">
                <div class="select">
                    <el-select v-model="value" placeholder="请选择">
                        <el-option label="注册建筑师证书" value="1" ></el-option>
                        <el-option label="人力资源管理资格证书" value="2" ></el-option>
                        <el-option label="普通话水平测试等级证书" value="3" ></el-option>
                        <el-option label="注册电气工程师" value="4" ></el-option>
                        <el-option label="审计师" value="5" ></el-option>
                        <el-option label="人力资源管理资格证书" value="6" ></el-option>
                        <el-option label="精算师" value="7" ></el-option>
                        <el-option label="岩土工程师" value="8" ></el-option>
                        <el-option label="特许金融分析师" value="9" ></el-option>
                    </el-select>
                    <el-select v-model="value1" placeholder="请选择">
                        <el-option label="初级" value="1" ></el-option>
                        <el-option label="中级" value="2" ></el-option>
                        <el-option label="高级" value="3" ></el-option>
                    </el-select>
                </div>
                <div id="rsfw-chart"></div>
            </div>
          </div>
          <!-- 职称考试 -->
          <div class="right-con">
            <s-header-title-2 title="职称考试"></s-header-title-2>
            <div class="table1">
                <div class="th">
                    <div class="th_td" style="flex: 0.16" v-for="(item,index) in theadList" :key="index">
                        {{item}}
                    </div>
                </div>
                <div class="tbody" id="box0" @mouseover="mouseenterEvent()" @mouseleave="mouseleaveEvent()">
                    <div class="tr" v-for="(item,i) in tableList" :key="i">
                        <div class="tr_td" style="flex: 0.16">{{i+1}}</div>
                        <div class="tr_td" style="flex: 0.16">{{item.kskm}}</div>
                        <div class="tr_td" style="flex: 0.16">{{item.zclb}}</div>
                        <div class="tr_td" style="flex: 0.16">{{item.gzd}}</div>
                        <div class="tr_td" style="flex: 0.16">{{item.sjxx}}</div>
                        <div class="tr_td" style="flex: 0.16">{{item.kcxx}}</div>
                        <div class="tr_td" style="flex: 0.16">{{item.ksrs}}</div>
                    </div>
                </div>
            </div>
          </div>
      </div>
      <div class="second-con">
        <!-- 公招考试 -->
        <div class="left-con">
            <s-header-title-2 title="公招考试"></s-header-title-2>
            <div class="gzks-tj">
                <li v-for="(item,index) in gzksData" :key="index">
                    <span class="title">{{item.name||'-'}}</span>
                    <div class="num-bg"><span class="num">{{item.value||'-'}}</span><span class="unit">{{item.unit}}</span>
                    </div>
                </li>
            </div>
            <div class="chart-con">
                <div id="gzks-chart"></div>
                <div id="gzks-chart1"></div>
            </div>
            
        </div>
        <!-- 事业单位 -->
        <div class="right-con">
            <s-header-title-2 title="事业单位"></s-header-title-2>
            <div class="sydw-tj">
                <li v-for="(item,index) in sydwData" 
                :key="index">
                    <span class="title">{{item.name||'-'}}</span>
                    <div><span class="s-c-blue2-gradient s-font-32 s-w7">{{item.value||'-'}}</span><span class="s-c-blue2-gradient s-font-20">个</span>
                    </div>
                </li>
            </div>
            <div class="table1" style="height: 62%;">
                <div class="th">
                    <div class="th_td" style="flex: 0.9" v-for="(item,index) in theadList1" :key="index">
                        {{item}}
                    </div>
                </div>
                <div class="tbody" id="box1" >
                    <div class="tr" v-for="(item ,i) in tableList1" :key="i">
                        <!-- <div class="tr_td" style="flex: 0.1">{{i+1}}</div> -->
                        <div class="tr_td" style="flex: 0.8">{{item.name}}</div>
                        <div class="tr_td" style="flex: 0.8">{{item.zbrs}}</div>
                        <div class="tr_td" style="flex: 0.8">{{item.fgdw}}</div>
                        <div class="tr_td" style="flex: 0.8">{{item.nzrs}}</div>
                    </div>
                </div>
            </div>
        </div>
      </div>
      <div class="third-con">
          <!-- 军转服务 -->
        <div class="left-con">
            <s-header-title-2 title="军转服务"></s-header-title-2>
            <div class="mfhx-con">
                <li v-for="(item,index) in jzfwList" :index="index" style="height: 40px;">
                    <img src="/static/citybrain/ggfw/img/list.png" alt="" style="width: 25px;height: 25px;">
                    <div class="name" style="margin: 0 20px;">{{item.name}}</div>
                    <div class="value" style="text-align: left;">{{item.value}}<span>人</span></div>
                </li>
            </div>
             <div id="jzfw-chart"></div>
        </div>
        <!-- 人才市场档案 -->
        <div class="right-con">
            <s-header-title-2 title="人才市场档案" :click-flag="true" @click="openDiaog"></s-header-title-2>
            <div class="gzks-tj" style="margin-top: 10px;width: 85%;position: relative;">
                <li style="width: 50%;display: flex;">
                    <span class="title">档案数量</span>
                    <div class="num-bg"><span class="num">2333</span><span class="unit">万份</span>
                    </div>
                </li>
                <el-select v-model="value2" placeholder="请选择" style="position: absolute;right: 0px;right: -165px;">
                    <el-option label="履历" value="1"></el-option>
                    <el-option label="自传" value="2"></el-option>
                    <el-option label="考察考核鉴定" value="3"></el-option>
                    <el-option label="政审材料" value="4"></el-option>
                    <el-option label="党团材料" value="5"></el-option>
                    <el-option label="表彰奖励活动" value="6"></el-option>
                    <el-option label="其他" value="7"></el-option>
                </el-select>
            </div>
            <div id="rcscda-chart"></div>
        </div>
      </div>
      <!-- 人才服务分析 -->
      <div class="four-con">
        <s-header-title-2 title="人才服务分析" style="width: 100%;"></s-header-title-2>
        <div class="bottom-con">
            <li v-for="(item,index) in rcfwfxList" :index="index">
                <div class="img-right">
                    <div class="name name-bg">{{item.name}}</div>
                    <div class="value">{{item.value}}<span>{{item.unit}}</span></div>
                </div>
            </li>
        </div>
      </div>
    </div>
</body>
<script type="module">

    new Vue({
        el: '#app',
        data: {
            rsfwList:[],//人事服务左侧顶部数据统计
            value:'1',
            value1:'1',
            value2:'1',
            theadList: ['序号', '考试科目', '职称类别', '(高中低)', '时间信息', '考场信息', '考试人数'],
            tableList:[],//劳动模范关系和谐表格数据
            gzksData:[],//公招考试统计数据
            sydwData:[],//事业单位统计数据
            theadList1: [ '单位名称', '在编人数', '上级分管单位', '拟招人数计划'],
            tableList1:[],//劳动模范关系和谐表格数据
            jzfwList:[],//军转服务数据
            rcfwfxList:[],//人才服务分析数据
        },
        methods: {
            mouseenterEvent() {
                clearInterval(this.time)
            },
            mouseleaveEvent() {
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },
            autoScroll() {
                this.dom = document.getElementById('box0')
                // this.scpDom = document.getElementsByClassName('text')
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },
         openDiaog(){
          let diaog = {
                type: 'openIframe',
                name: 'rsfw-dialog',
                src: baseURL.url + '/static/citybrain/ggfw/commont/rsfw-dialog.html',
                left: "calc(50% - 700px)",
                top: "30%",
                width: "1560px",
                height: "980px",
                zIndex:"10",
                argument: {
                    status:""
                }
            }
            top.window.parent.postMessage(JSON.stringify(diaog), '*')
        },
            init(){
                $api("ggfw_rsfw_rshifw").then((res) => {
                    this.rsfwList = res;
                });
                $api("ggfw_rsfw_zcjzs").then((res) => {
                   this.getLinechartsShow('rsfw-chart',res,'注册建筑师证书');
                })
                $api("ggfw_rsfw_zcks").then((res) => {
                this.tableList=res;
                }); 
                $api("ggfw_rsfw_gzks").then((res) => {
                    this.gzksData = res;
                });
                $api("ggfw_rsfw_gzks01").then((res) => {
                     this.getChart01('gzks-chart',res);
                });
                $api("ggfw_rsfw_gzks02").then((res) => {
                     this.getEcharts01(res);
                });
                $api("ggfw_rsfw_sydw01").then((res) => {
                    this.sydwData=res;
                });
                $api("ggfw_rsfw_sydw02").then((res) => {
                    this.tableList1=res;
                });
                $api("ggfw_rsfw_jzfw").then((res) => {
                    this.jzfwList=res;
                });
                $api("ggfw_rsfw_jzfw01").then((res) => {
                   this.getLinechartsShow('jzfw-chart',res,'历年自主择业军转干部总人数');
                })
                $api("ggfw_rsfw_rcscda").then((res) => {
                   this.getEcharts02(res);
                })
                $api("ggfw_rsfw_rcfwfx").then((res) => {
                   this.rcfwfxList=res;
                })
            },
            //绘制注册建筑师资格证与军转服务折线图
            getLinechartsShow(id,data,name) {
                   echarts.init(document.getElementById(id)).dispose();
                    const myChartsRun = echarts.init(document.getElementById(id))
                    var fontColor = "#30eee9";
                    let x =data.map((item) => {
                        return item.name;
                    })
                    let y = data.map((item) => {
                        return item.value;
                    })
                    let option = {
                        // backgroundColor: "#11183c",
                        title: {
                            text: "",
                            x: "center",
                            top: "0",
                            textStyle: { color: "#fff", fontSize: "32" },
                        },
                        grid: {
                            left: "5%",
                            right: "5%",
                            top: "15%",
                            bottom: "0",
                            containLabel: true,
                        },
                        tooltip: {
                        trigger: "item",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "30",
                        },
                        },
                        legend: {
                            show: true,
                            x: "center",
                            y: "5",
                            itemWidth: 20,
                            itemHeight: 20,
                            textStyle: {
                                color: "#fff",
                                fontSize:"28px",
                            },
                        },
                        xAxis: [
                            {
                                type: "category",
                                boundaryGap: false,
                                axisLabel: {
                                    color: '#fff',
                                    // rotate: 45,
                                    fontSize:"28px",
                                },
                                axisLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#bbb",
                                    },
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#195384",
                                    },
                                },
                                data: x,
                            },
                        ],
                        yAxis: [
                            {
                                type: "value",
                                name:"单位:个",
                                min: 0,
                                // max: 1000,
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 23,
                                },
                                axisLabel: {
                                    formatter: "{value}",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize:"28px",
                                    },
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: "#fff",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        color: '#77b3f1',
                                        opacity: 0.5,
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: name,
                                type: "line",
                                stack: "总量",
                                smooth: true, //加这个
                                // symbol: "circle",
                                symbolSize: 10,
                                itemStyle: {
                                    normal: {
                                        color: "#0092f6",
                                        lineStyle: {
                                            color: "#5087EC",
                                            width: 4,
                                        },
                                    },
                                },
                                data: y,
                            },
                        ],
                    };
                    myChartsRun.setOption(option);
             },
            //绘制公招考试环图
            getChart01(id,data) {
                echarts.init(document.getElementById(id)).dispose();
                let myEc = echarts.init(document.getElementById(id));
                let imgUrl = '/static/citybrain/djtl/img/djtl-left/echarts-bg.png'
                const option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{b}: <br/>{d}%',
                        borderWidth: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.6)',
                        textStyle: {
                            color: 'white',
                            fontSize: '30',
                        },
                    },
                    legend: {
                        orient: 'vertical',
                        itemWidth: 18,
                        itemHeight: 18,
                        left: '55%',
                        top: 'center',
                        // icon: 'circle',
                        itemGap: 30,
                        textStyle: {
                            color: '#D6E7F9',
                            fontSize: 28,
                            padding: [0, 0, 0, 10]
                        },
                        // formatter: function (name) {
                        //     var data = option.series[0].data //获取series中的data
                        //     var total = 0
                        //     var tarValue
                        //     for (var i = 0, l = data.length; i < l; i++) {
                        //         total += data[i].value
                        //         if (data[i].name == name) {
                        //             tarValue = data[i].value
                        //         }
                        //     }
                        //     var p = (tarValue / total) * 100
                        //     return name + '  ' + tarValue
                        // },
                    },
                    graphic: [
                        {
                            z: 4,
                            type: "image",
                            id: "logo",
                            left: "29.3%",
                            top: "30.5%",
                            z: -10,
                            bounding: "raw",
                            rotation: 0, //旋转
                            origin: [-140, -50], //中心点
                            scale: [0.5, 0.5], //缩放
                            style: {
                                image: imgUrl,
                                opacity: 1,
                            },
                        },
                    ],
                    series: [
                        {
                            name: "",
                            type: 'pie',
                            radius: ['40%', '55%'],
                            center: ['31.5%', '51.3%'],
                            itemStyle: {
                                normal: {
                                    borderColor: "#0A1934",
                                    // borderWidth: 10
                                },
                            },
                            label: {
                                show: false,
                            },
                            data: data,
                        },
                    ],
                }
                myEc.setOption(option)
            },
            //绘制公招考试折现图
            getEcharts01(data) {
                let myEc = echarts.init(document.getElementById('gzks-chart1'))
                let xData = [],
                    yData = [],
                    y1Data = []

                data.forEach((item) => {
                    xData.push(item.name)
                    yData.push(item.value)
                    y1Data.push(item.value1)
                })
                var option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
                        },
                        borderWidth: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.6)',
                        textStyle: {
                            color: 'white',
                            fontSize: '28',
                        },
                    },
                    legend: {
                        orient: 'horizontal',
                        // itemWidth: 18,
                        // itemHeight: 18,
                        top: 0,
                        // icon: 'rect',
                        itemGap: 45,
                        textStyle: {
                            color: '#D6E7F9',
                            fontSize: 30,
                        },
                    },
                    grid: {
                        left: '2%',
                        right: '5%',
                        bottom: '15%',
                        top: '12%',
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: xData,
                            offset: 20,
                            axisLine: {
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.3,
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                // rotate: -30,
                                textStyle: {
                                    fontSize: 30,
                                    color: 'white',
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name:"人数:人",
                            type: 'value',
                            // max: 800,
                            min: 0,
                            nameTextStyle: {
                                fontSize: 30,
                                color: '#D6E7F9',
                                padding: [0, 0, 20, 0],
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 30,
                                    color: '#D6E7F9',
                                },
                            },
                        },
                        {
                            name: "单位:%",
                            type: 'value',
                            //   max: 30,
                            min: 0,
                            nameTextStyle: {
                                fontSize: 30,
                                color: '#D6E7F9',
                                padding: [0, 0, 20, 0],
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 30,
                                    color: '#D6E7F9',
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "报考人数(人)",
                            type: 'bar',
                            barWidth: 35,
                            yAxisIndex: 0,
                            smooth: true, //加这个
                            center: ['0%', '45%'],
                            radius: ['0%', '45%'],
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: '#00C0FF',
                                        },
                                        {
                                            offset: 0.2,
                                            color: '#00C0FF',
                                        },
                                        {
                                            offset: 1,
                                            color: '#004F69',
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            data: yData,
                        },
                        {
                            name: "录取(%)",
                            type: 'line',
                            smooth: true, //加这个
                            yAxisIndex: 1,
                            itemStyle: {
                                normal: {
                                    color: '#968212',
                                    barBorderRadius: 4,
                                },
                            },
                            data: y1Data,
                        },

                    ],
                }
                myEc.setOption(option)
                tools.loopShowTooltip(myEc, option, { loopSeries: true });
            }, 
            //绘制人才市场档案
            getEcharts02(data) {
                let myEc = echarts.init(document.getElementById('rcscda-chart'))
                let xData = [],
                    yData = []

                data.forEach((item) => {
                    xData.push(item.name)
                    yData.push(item.value)
                })
                var option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
                        },
                        borderWidth: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.6)',
                        textStyle: {
                            color: 'white',
                            fontSize: '28',
                        },
                    },
                    legend: {
                        orient: 'horizontal',
                        // itemWidth: 18,
                        // itemHeight: 18,
                        top: 0,
                        // icon: 'rect',
                        itemGap: 45,
                        textStyle: {
                            color: '#D6E7F9',
                            fontSize: 30,
                        },
                    },
                    grid: {
                        left: '2%',
                        right: '2%',
                        bottom: '5%',
                        top: '22%',
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: xData,
                            offset: 20,
                            axisLine: {
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.3,
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                // rotate: -30,
                                textStyle: {
                                    fontSize: 30,
                                    color: 'white',
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name:"单位:个",
                            type: 'value',
                            // max: 800,
                            min: 0,
                            nameTextStyle: {
                                fontSize: 30,
                                color: '#D6E7F9',
                                padding: [0, 0, 20, 0],
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 30,
                                    color: '#D6E7F9',
                                },
                            },
                        }
                    ],
                    series: [
                        {
                            name: "履历",
                            type: 'bar',
                            barWidth: 35,
                            yAxisIndex: 0,
                            smooth: true, //加这个
                            center: ['0%', '45%'],
                            radius: ['0%', '45%'],
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: '#00C0FF',
                                        },
                                        {
                                            offset: 0.2,
                                            color: '#00C0FF',
                                        },
                                        {
                                            offset: 1,
                                            color: '#004F69',
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            data: yData,
                        }
                    ],
                }
                myEc.setOption(option)
                tools.loopShowTooltip(myEc, option, { loopSeries: true });
            }, 
        },
        //项目生命周期
        mounted() {
           this.init(); 
           this.autoScroll();
        }


    })


</script>

</html>