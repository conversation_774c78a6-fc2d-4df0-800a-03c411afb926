<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>应急指挥-左</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/elementui.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/echarts/echarts.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/elementui/js/elementui.js"></script>
    <script src="/static/citybrain/csdn/js/drawCircleMap.js"></script>
    <script src="/static/js/jslib/turf.js"></script>
    <style>
      [v-cloak] {
        display: none;
      }
      html,body,ul,p{
          padding:0;
          margin:0;
          list-style: none;
      }
      .container{
          width:900px;
          height:400px;
          background-color: #0a2443;
          box-sizing: border-box;
      }
.table {
    width: 100%;
}

.table-th {
    width: 100%;
    height: 60px;
    background-color: #00396f;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.th {
    flex: 0.3;
    font-size: 32px;
    text-align: center;
    color: #77b3f1;
    margin-left: 10px;
}

.table-tr {
    width: 100%;
    height: 220px;
    overflow-y: auto;
}

.table-tr::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 4px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
    /* scrollbar-arrow-color: red; */

}

.table-tr::-webkit-scrollbar-thumb {
    border-radius: 4px;
    /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
    background: #20aeff;
    height: 8px;

}

.tr {
    width: 100%;
    padding: 10px 20px;
    box-sizing: border-box;
    background-color: #0f2b4d;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.td {
    flex: 0.3;
    font-size: 32px;
    color: #fff;
    text-align: center;
    margin-left: 10px;
}
.td>span:last-child{
    margin-left: 10px;
}
ul{
  width: 100%;
  height: 50px;
  line-height: 50px;
  list-style: none;
  color: #fff;
  font-size: 30px;
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
}
li{
  width: 200px;
  text-align: center;
  background-color: #1890FF;
  margin: 0 20px;
}
      
    </style>
  </head>
  <body>
    <div id="app" class="container" v-cloak>
      <div class="table">
        <div class="table-th" >
          <div class="th" v-for="(item,index) in tableTitle" :key="index">{{item}}</div>
        </div>
        <div class="table-tr">
          <div class="tr" v-for="(item,index) in leftTable" :key="index">
            <div class="td" v-for="(el,i) in tableLabel">
              <div>{{item[el]}}</div>
            </div>
          </div>
        </div>
      </div>
      <ul>
        <li v-for="item in list">{{item}}</li>
      </ul>
    </div>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
      let vm = new Vue({
        el: "#app",
        data: {
      tableTitle: ["姓名", "手机号码","姓名","手机号码"],
      leftTable: [
        {a:"顾伦",b:"19612348970",c:"李天策",d:"19333331231"},
        {a:"周天乐",b:"19875783214",c:"张三",d:"19533319000"},
        {a:"纪广",b:"19111321388",c:"王泽",d:"19580121235"}
      ],
      tableLabel: ["a", "b","c","d"],
      list:["一键发送","路线规划","实时路况"]
        },
        mounted() {
          var that = this
        },
        methods: {

          closeIframe() {
            top.commonObj.funCloseIframe({
              name: "yjzh-kx",
            });
          },
        },
      });
    </script>
  </body>
</html>
