<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Document</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
    </head>
    <style>
        * {
            padding: 0;
            margin: 0;
        }

        #shwzbz-left {
            width: 2045px;
            height: 1890px;
            background: url("/img/left-bg.png") no-repeat;
            background-size: 100% 100%;
            display: flex;
            justify-content: center;
        }

        .content {
            width: 1934px;
            height: 100%;
        }

        .title-top {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .title-top > div {
            color: #fff;
            font-size: 30px;
        }

        .title-top > div > span {
            color: #0087ec;
            font-weight: 700;
            font-size: 40px;
            margin-left: 20px;
        }

        .el-radio-group {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin-top: 20px;
        }

        .el-radio__label {
            font-size: 30px;
            color: rgba(255, 255, 255, 0.568);
        }

        .el-radio__inner {
            width: 30px;
            height: 30px;
            margin-bottom: 8px;
            background-color: #303c57;
        }

        .el-radio__input.is-checked + .el-radio__label {
            color: #fff !important;
        }

        .el-radio__input.is-checked .el-radio__inner {
            background-color: #303c57;
        }

        .el-radio__inner::after {
            width: 12px;
            height: 12px;
            background-color: #1684fc;
        }

        .title {
            position: relative;
        }

        .el-input__inner {
            background-color: #041230;
            border: 1px solid #686f7d;
            color: #fff;
            font-size: 30px;
            border-radius: 10%;
            height: 65px;
        }

        .el-range-editor.is-active,
        .el-range-editor.is-active:hover,
        .el-select .el-input__inner:focus,
        .el-select .el-input.is-focus .el-input__inner {
            border-color: #fff;
        }

        .el-select-dropdown__list {
            background-color: #032f46d3;
            color: #fff;
            /* height: 200px; */
            /* width: 300px; */
        }

        .el-select-dropdown__item.hover,
        .el-select-dropdown__item:hover {
            background: none;
        }

        .el-select-dropdown__item {
            color: #fff;
            line-height: 80px;
            font-size: 30px;
            height: 80px;
        }

        .el-select-dropdown {
            /* border: 1px solid #2578a6; */
            background-color: #032f46d3;
        }

        .el-select {
            width: 200px;
            position: absolute;

            right: 138px;
            top: 20px;
        }

        .table {
            width: 1934px;
            font-size: 30px;
            color: #fff;
            border: 2px solid #0075a5;
            border-collapse: collapse;
        }

        .table thead {
            border-bottom: 1px solid #0075a5;
        }

        .table th {
            line-height: 60px;
            background-color: #032661;
        }

        .table tbody {
            text-align: center;
            background-color: #021e4855;
            color: #ccc;
            line-height: 60px;
        }

        .timeONE {
            position: absolute;
            right: 138px;
            top: 20px;
            color: #fff;
        }

        .timeONE .el-input__inner {
            text-align: center;
        }

        .el-input__suffix {
            right: 18px;
            font-size: 30px !important;
        }

        .el-picker-panel__body-wrapper {
            font-size: 30px;
        }

        .el-input__icon {
            line-height: 65px !important;
            /* margin-right: 20px; */
        }

        .el-icon-date:before {
            font-size: 30px;
        }

        .el-date-picker__header-label {
            font-size: 20px;
        }

        .el-month-table {
            font-size: 20px;
        }

        .selectBOX {
            position: absolute;
            right: 370px;
            top: 20px;
        }
    </style>

    <body>
        <div id="shwzbz-left">
            <div class="content">
                <div class="title">
                    <nav style="padding: 20px 45px">
                        <s-header-title style="width: 100%" title="CPI指数分类展示" htype="1"></s-header-title>
                    </nav>
                    <el-select v-model="value3" @change="change">
                        <el-option v-for="item in options3" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
                <div class="title-top">
                    <div>本月CPI <span>99.1</span></div>
                    <div>同比 <span>1.9%</span></div>
                </div>
                <el-radio-group v-model="value" @change="changeRadio">
                    <el-radio v-for="item in radioData" :label="item.name">{{item.value}}</el-radio>
                </el-radio-group>
                <div class="content_line">
                    <div id="barEcharts001" style="width: 100%; height: 350px"></div>
                </div>

                <div class="title">
                    <nav style="padding: 20px 45px">
                        <s-header-title style="width: 100%" title="CPI趋势分析展示" htype="1"></s-header-title>
                    </nav>
                    <el-select class="selectBOX" v-model="value1" @change="changeCenter">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                    <el-select v-model="value5" @change="changeCenterOne">
                        <el-option v-for="item in options5" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
                <div class="content_line">
                    <div id="lineEcharts002" style="width: 100%; height: 400px"></div>
                </div>
                <div class="title">
                    <nav style="padding: 20px 45px">
                        <s-header-title style="width: 100%" title="农产品物价展示" htype="1"></s-header-title>
                    </nav>
                    <el-select v-model="value2" @change="changeCity">
                        <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>

                <table class="table">
                    <thead>
                        <tr>
                            <th v-for="item in tableName">{{item}}</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="item in tableList" @click="showDetails()">
                            <td>{{item.name}}</td>
                            <td>{{item.zb}}</td>
                            <td>{{item.dw}}</td>
                            <td>{{item.cnsc}}</td>
                            <td>{{item.cxsc}}</td>
                            <td>{{item.ljwsc}}</td>
                            <td>{{item.xxxsc}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#shwzbz-left",
        data: {
            value: "上月环比",
            time: "",
            time1: "",
            radioData: [
                { name: "上月环比", value: "上月环比" },
                { name: "上年同月比", value: "上年同月比" },
                { name: "上年同期比", value: "上年同期比" },
            ],
            value1: "食品烟酒",
            value2: "金华市",
            value3: "10月",
            options3: [
                {
                    value: "8月",
                    label: "1月",
                },
                {
                    value: "9月",
                    label: "2月",
                },
                {
                    value: "10月",
                    label: "3月",
                },
                // {
                //     value: "10月",
                //     label: "10月",
                // },
            ],
            value5: "2023",
            options5: [
                {
                    value: "2021",
                    label: "2022",
                },
                {
                    value: "2022",
                    label: "2023",
                },{
                    value: "2023",
                    label: "2024",
                },
            ],
            options: [
                {
                    value: "食品烟酒",
                    label: "食品烟酒",
                },
                {
                    value: "衣着",
                    label: "衣着",
                },
                {
                    value: "居住",
                    label: "居住",
                },
                {
                    value: "生活用品及服务",
                    label: "生活用品及服务",
                },
            ],
            options2: [
                {
                    value: "金华市",
                    label: "金华市",
                },
                {
                    value: "金义新区",
                    label: "金义新区",
                },
                {
                    value: "兰溪市",
                    label: "兰溪市",
                },
                {
                    value: "婺城区",
                    label: "婺城区",
                },
                {
                    value: "浦江县",
                    label: "浦江县",
                },
                {
                    value: "磐安县",
                    label: "磐安县",
                },
                {
                    value: "义乌市",
                    label: "义乌市",
                },
                {
                    value: "东阳市",
                    label: "东阳市",
                },
                {
                    value: "永康市",
                    label: "永康市",
                },
            ],
            tableName: [
                "名称",
                "指标解释",
                "单位",
                "金华市农贸商场",
                "金华市五一路农贸市场",
                "金华市农产品批发市场",
                "金华市城西菜市场",
            ],
            tableList: [],
            month: "8",
            radio: "1",
            year: 2023,
            typeTwo: 1,
        },
        mounted() {
            this.initFun();
            this.initMap();
        },
        methods: {
            initFun() {
                this.change(this.value3)
                this.changeCenter(this.value1)
                // $api("shwzbz-left001", { type: "1", month: "8" }).then((res) => {
                //     this.getEcharts01("barEcharts001", res);
                // });
                // $api("shwzbz-left002", { type: "1", year: 2021 }).then((res) => {
                //     this.getEcharts02("lineEcharts002", res);
                // });
                $api("shwzbz-left003", { type: "金华市" }).then((res) => {
                    this.tableList = res;
                });
            },
            changeCity(item) {
                $api("shwzbz-left003", { type: item }).then((res) => {
                    this.tableList = res;
                });
                if (item === "金华市") {
                    this.tableName = [
                        "名称",
                        "指标解释",
                        "单位",
                        "金华市农贸商场",
                        "金华市五一路农贸市场",
                        "金华市农产品批发市场",
                        "金华市城西菜市场",
                    ];
                } else if (item === "金义新区") {
                    this.tableName = [
                        "名称",
                        "指标解释",
                        "单位",
                        "金义新区农贸商场",
                        "金义新区五一路农贸市场",
                        "金义新区农产品批发市场",
                        "金义新区城西菜市场",
                    ];
                } else if (item === "兰溪市") {
                    this.tableName = [
                        "名称",
                        "指标解释",
                        "单位",
                        "兰溪市农贸商场",
                        "兰溪市五一路农贸市场",
                        "兰溪市农产品批发市场",
                        "兰溪市城西菜市场",
                    ];
                } else if (item === "婺城区") {
                    this.tableName = [
                        "名称",
                        "指标解释",
                        "单位",
                        "婺城区农贸商场",
                        "婺城区五一路农贸市场",
                        "婺城区农产品批发市场",
                        "婺城区城西菜市场",
                    ];
                } else if (item === "浦江县") {
                    this.tableName = [
                        "名称",
                        "指标解释",
                        "单位",
                        "浦江县农贸商场",
                        "浦江县五一路农贸市场",
                        "浦江县农产品批发市场",
                        "浦江县城西菜市场",
                    ];
                } else if (item === "磐安县") {
                    this.tableName = [
                        "名称",
                        "指标解释",
                        "单位",
                        "磐安县农贸商场",
                        "磐安县五一路农贸市场",
                        "磐安县农产品批发市场",
                        "磐安县城西菜市场",
                    ];
                } else if (item === "义乌市") {
                    this.tableName = [
                        "名称",
                        "指标解释",
                        "单位",
                        "义乌市农贸商场",
                        "义乌市五一路农贸市场",
                        "义乌市农产品批发市场",
                        "义乌市城西菜市场",
                    ];
                } else if (item === "东阳市") {
                    this.tableName = [
                        "名称",
                        "指标解释",
                        "单位",
                        "东阳市农贸商场",
                        "东阳市五一路农贸市场",
                        "东阳市农产品批发市场",
                        "东阳市城西菜市场",
                    ];
                } else if (item === "永康市") {
                    this.tableName = [
                        "名称",
                        "指标解释",
                        "单位",
                        "永康市农贸商场",
                        "永康市五一路农贸市场",
                        "永康市农产品批发市场",
                        "永康市城西菜市场",
                    ];
                }
            },
            changeCenter(item) {
                if (item === "食品烟酒") {
                    this.typeTwo = 1;
                    $api("shwzbz-left002", { type: "1", year: this.year }).then((res) => {
                        this.getEcharts02("lineEcharts002", res);
                    });
                } else if (item === "衣着") {
                    this.typeTwo = 2;
                    $api("shwzbz-left002", { type: "2", year: this.year }).then((res) => {
                        this.getEcharts02("lineEcharts002", res);
                    });
                } else if (item === "居住") {
                    this.typeTwo = 3;
                    $api("shwzbz-left002", { type: "3", year: this.year }).then((res) => {
                        this.getEcharts02("lineEcharts002", res);
                    });
                } else if (item === "生活用品及服务") {
                    this.typeTwo = 4;
                    $api("shwzbz-left002", { type: "4", year: this.year }).then((res) => {
                        this.getEcharts02("lineEcharts002", res);
                    });
                }
            },
            changeCenterOne(item) {
                this.year = item;
                $api("shwzbz-left002", { type: this.typeTwo, year: item }).then((res) => {
                    this.getEcharts02("lineEcharts002", res);
                });
            },
            change(item) {
                if (item === "8月") {
                    this.month = "8";
                    $api("shwzbz-left001", { type: this.radio, month: "8" }).then((res) => {
                        this.getEcharts01("barEcharts001", res);
                    });
                    this.changeRadio(item, "8");
                } else if (item === "9月") {
                    $api("shwzbz-left001", { type: this.radio, month: "9" }).then((res) => {
                        this.getEcharts01("barEcharts001", res);
                    });
                    this.month = "9";
                    this.changeRadio(item, "9");
                }else if (item === "10月") {
                    $api("shwzbz-left001", { type: this.radio, month: "10" }).then((res) => {
                        this.getEcharts01("barEcharts001", res);
                    });
                    this.month = "10";
                    this.changeRadio(item, "10");
                }
            },
            changeRadio(item, month) {
                if (item === "上月环比") {
                    this.radio = "1";
                    $api("shwzbz-left001", { type: "1", month: this.month }).then((res) => {
                        this.getEcharts01("barEcharts001", res);
                    });
                } else if (item === "上年同月比") {
                    this.radio = "2";
                    $api("shwzbz-left001", { type: "2", month: this.month }).then((res) => {
                        this.getEcharts01("barEcharts001", res);
                    });
                } else if (item === "上年同期比") {
                    this.radio = "3";

                    $api("shwzbz-left001", { type: "3", month: this.month }).then((res) => {
                        this.getEcharts01("barEcharts001", res);
                    });
                }
            },
            getEcharts01(dom, echartData) {
                let xData = echartData.map((item) => {
                    return item.name;
                });
                let yData = echartData.map((item) => {
                    return item.value;
                });
                let echarts0 = echarts.init(document.getElementById(dom));
                let option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            // color: "white",
                            fontSize: "30",
                        },
                    },
                    grid: {
                        left: "3%",
                        right: "4%",
                        bottom: "3%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: "category",
                            data: xData,
                            axisTick: {
                                show: true,
                                length: 10,
                            },
                            offset: 10,
                            axisLabel: {
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 23,
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            type: "value",
                            // min: "95",
                            // max: "105",
                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 30,
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "数据",
                            type: "bar",
                            barWidth: "50%",
                            data: yData,
                        },
                    ],
                };
                echarts0.setOption(option);
            },
            getEcharts02(dom, echartData) {
                let echarts1 = echarts.init(document.getElementById(dom));
                var xData = echartData.map((item) => item.name);
                var yData1 = echartData.map((item) => item.value);
                var yData2 = echartData.map((item) => item.value1);
                var yData3 = echartData.map((item) => item.value2);

                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            color: "white",
                            fontSize: "30",
                        },
                    },
                    legend: {
                        x: "center",
                        y: "20px",
                        textStyle: {
                            color: "#f2f2f2",
                            fontSize: 30,
                        },
                        itemHeight: 30,
                        itemWidth: 45,
                        itemGap: 20,

                        data: ["上月环比", "上年同月比", "上年同期比"],
                    },
                    grid: {
                        left: "3%",
                        right: "4%",
                        bottom: "3%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: "category",
                            // data: xData,
                            data:['2024.06','2024.07','2024.08','2024.09','2024.10','2024.11','2024.12','2025.1','2025.2','2025.3'],
                            nameTextStyle: {
                                color: "#d4ffff",
                            },
                            axisLine: {
                                lineStyle: {},
                            },
                            offset: 10,
                            axisTick: {
                                show: true,
                                length: 10,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#FFF",
                                    fontSize: 30,
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            type: "value",
                            // min: 95,
                            // max: 106,
                            axisLine: {
                                lineStyle: {
                                    color: "#0B4CA9",
                                },
                            },
                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                color: "#d4ffff",
                                fontSize: 30,
                            },
                        },
                    ],
                    series: [
                        {
                            name: "上月环比",
                            type: "line",
                            yAxisIndex: 0,
                            symbolSize: 12,
                            lineStyle: {
                                width: 5,
                            },
                            itemStyle: {
                                normal: {
                                    color: "#335aa4",
                                },
                            },
                            data: yData1,
                        },
                        {
                            name: "上年同月比",
                            type: "line",
                            yAxisIndex: 0,
                            symbolSize: 12,
                            lineStyle: {
                                width: 5,
                            },
                            itemStyle: {
                                normal: {
                                    color: "#72cdd7",
                                },
                            },
                            data: yData2,
                        },
                        {
                            name: "上年同期比",
                            type: "line",
                            yAxisIndex: 0,
                            symbolSize: 12,
                            lineStyle: {
                                width: 5,
                            },
                            itemStyle: {
                                normal: {
                                    color: "#60b565",
                                },
                            },
                            data: yData3,
                        },
                    ],
                };
                echarts1.setOption(option);
            },
            addBankuai() {
                let _this = this;
                top.mapUtil.flyTo({
                    x: 120.**************,
                    y: 25.***************,
                    z: 357215.**********,
                    heading: 354.**************,
                    tilt: 44.**************,
                });
                top.mapUtil.loadRegionLayer({
                    layerid: "bankuai",
                    data: [
                        { name: "婺城区", color: [78, 107, 221, 1], height: 2800 },
                        { name: "开发区", color: [78, 107, 221, 1], height: 2600 },
                        { name: "金东区", color: [46, 81, 221, 1], height: 2400 },
                        { name: "兰溪市", color: [78, 107, 221, 1], height: 2200 },
                        { name: "浦江县", color: [110, 133, 221, 1], height: 2000 },
                        { name: "义乌市", color: [110, 133, 221, 1], height: 1800 },
                        { name: "东阳市", color: [78, 107, 221, 1], height: 1600 },
                        { name: "磐安县", color: [110, 133, 221, 1], height: 1400 },
                        { name: "永康市", color: [46, 81, 221, 1], height: 1200 },
                        { name: "武义县", color: [110, 133, 221, 1], height: 1000 },
                    ],
                    onclick: function (e) {},
                });
            },
            initMap() {
                this.addBankuai();
                this.Histogram();
            },

            //区划地图柱状图
            Histogram() {
                let data = [
                    {
                        pos: [119.*************, 29.***************, 0],
                        text: "浦江县\n1.2%",
                        color: [255, 255, 255, 1],
                        type: "",
                    },
                    {
                        pos: [119.**************, 29.***************, 0],
                        text: "兰溪市\n1.7%",
                        color: [255, 255, 255, 1],
                        type: "",
                    },
                    {
                        pos: [119.**************, 28.***************, 0],
                        text: "婺城区\n1.2%",
                        color: [255, 255, 255, 1],
                        type: "",
                    },
                    {
                        pos: [119.**************, 29.**************, 0],
                        text: "金义新区\n1.8%",
                        color: [255, 255, 255, 1],
                        type: "",
                    },
                    {
                        pos: [120.00438915928201, 29.28180844822686, 0],
                        text: "义乌市\n2.8%",
                        color: [255, 255, 255, 1],
                        type: "",
                    },
                    {
                        pos: [119.67587615578625, 28.777011171587162, 0],
                        text: "武义县\n3.1%",
                        color: [255, 255, 255, 1],
                        type: "",
                    },
                    {
                        pos: [120.04986352478184, 28.987053762616034, 0],
                        text: "永康市\n1.6%",
                        color: [255, 255, 255, 1],
                        type: "",
                    },
                    {
                        pos: [120.34254935807531, 29.305565856200392, 0],
                        text: "东阳市\n1.4%",
                        color: [255, 255, 255, 1],
                        type: "",
                    },
                    {
                        pos: [120.50638710113705, 29.009431602110855, 0],
                        text: "磐安县\n2.2%",
                        color: [255, 255, 255, 1],
                        type: "",
                    },
                ];
                top.mapUtil.loadTextLayer({
                    layerid: "3Dtext",
                    data: data,
                    style: {
                        size: 35,
                    },
                });
                top.mapUtil.loadHistogram({
                    data: [
                        { name: "浦江县", num: 1.2 },
                        { name: "兰溪市", num: 1.7 },
                        { name: "婺城区", num: 1.2 },
                        { name: "金义新区", num: 1.8 },
                        { name: "义乌市", num: 2.8 },
                        { name: "武义县", num: 3.1 },
                        { name: "永康市", num: 1.6 },
                        { name: "东阳市", num: 1.4 },
                        { name: "磐安县", num: 2.2 },
                    ],
                });
            },

            //清除柱状体
            rmHistogram() {
                top.mapUtil.removeAllLayers();
            },
            showDetails() {
                console.log("1111");
                let Iframe = {
                    type: "openIframe",
                    name: "dialog1",
                    src: baseURL.url + "/static/citybrain/scjg/commont/shwzbz/dialog1.html",
                    left: "450px",
                    top: "1600px",
                    width: "1200px",
                    height: "500px",
                    zIndex: "10",
                    argument: {
                        // status: "openIframe",
                        // title: titleName,
                    },
                };
                window.parent.postMessage(JSON.stringify(Iframe), "*");
            },
        },
        destroyed() {
            this.rmHistogram();
        },
    });
</script>
