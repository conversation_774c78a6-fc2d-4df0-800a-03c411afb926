<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title></title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/citybrain/scjg/css/hjbh-right/common.css" />
  <script src="/Vue/vue.js"></script>
  <script src="/static/citybrain/hjbh/js/echarts.js"></script>
  <script src="/static/js/jslib/Emiter.js"></script>
  <script src="/static/js/jslib/vue-count-to.min.js"></script>
  <link rel="stylesheet" href="/elementui/css/elementui.css" />
  <script src="/elementui/js/elementui.js"></script>

  <style>
    * {
      margin: 0;
      padding: 0;
    }

    .sjzx_middle_left {
      /* position: absolute;
        top: 0;
        left: 48px; */
      width: 450px;
      min-height: 600px;
      background: linear-gradient(179deg, #0e1a40 0%, #064069 100%);
      /* box-shadow: 0px 7px 50px 0px rgba(35, 154, 228, 0.56); */
      /* opacity: 0.85; */
      border-radius: 10px;
    }

    .sjzx_middle_title {
      font-size: 36px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #d6e7f9;
      background: linear-gradient(180deg,
          #aed6ff 0%,
          #74b8ff 47.4853515625%,
          #9ccfff 50%,
          #ddeeff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
    }

    .sjzx_middle_title p {
      margin-top: 10px;
      height: 82px;
      line-height: 83px;
      white-space: nowrap;
    }

    .sjzx_middle_title p:before {
      content: "";
      height: 1px;
      top: -3%;
      position: relative;
      width: 8%;
      height: 1px;
      border-bottom: 3px solid #74b8ff;
      display: inline-block;
      margin-right: 5px;
      margin-bottom: 12px;
    }

    .sjzx_middle_title p:after {
      content: "";
      top: -3%;
      position: relative;
      width: 8%;
      height: 1px;
      border-bottom: 3px solid #74b8ff;
      display: inline-block;
      margin-left: 5px;
      margin-bottom: 12px;
    }

    .sjzx_middle_title .before {
      display: inline-block;
      width: 10px;
      height: 10px;
      background-color: #74b8ff;
      /* transform: rotateZ(90deg); */
      border-radius: 5px;
      margin-bottom: 10px;
    }

    .sjzx_middle_title .after {
      /* display: inline-block; */
      display: inline-block;
      width: 10px;
      height: 10px;
      background-color: #74b8ff;
      /* transform: rotateZ(90deg); */
      border-radius: 5px;
      margin-bottom: 10px;
    }

    .sjzx_middle_title p .tab {
      cursor: pointer;
    }

    .el-checkbox__input {
      float: right;
      margin-right: 30px;
    }

    .el-tree-node__label {
      font-size: 30px;
      font-family: PangMenZhengDao;
      font-weight: bold;

      color: #c0d6ed;
      line-height: 58px;
    }

    .el-tree-node__content {
      height: 50px !important;
      margin-bottom: 10px;
    }

    .is-focusable {
      background-color: unset;
    }

    /* .el-tree-node__expand-icon.expanded {
        display: block;
      } */
    .el-tree-node__content>.el-tree-node__expand-icon {
      /* display: none; */
    }

    /* .el-tree--highlight-current
        .el-tree-node.is-current
        > .el-tree-node__content {
        background-color: unset;
      }
      .el-tree-node__content:hover,
      .el-tree > .el-tree-node.is-current {
        background: linear-gradient(
          94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%
        ) !important;
        border-radius: 0px 30px 30px 0px;
      }

      .el-tree-node.is-focusable.is-checked {
        background: linear-gradient(
          94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%
        ) !important;
        border-radius: 0px 30px 30px 0px;
      } */

    .el-tree-node.is-current>.el-tree-node__content,
    .el-tree-node__content:hover {
      background: linear-gradient(94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%) !important;
      border-radius: 0px 30px 30px 0px;
    }

    .el-checkbox {
      /* position: absolute;
        right: 0; */
      display: block;
      border-radius: 15px;
      margin-bottom: 20px;
      margin-right: 0;
    }

    .el-checkbox-group .el-checkbox:hover {
      background: linear-gradient(94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%) !important;
      border-radius: 0px 30px 30px 0px;
    }

    .el-checkbox-group .is-checked {
      background: linear-gradient(94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%) !important;
      border-radius: 0px 30px 30px 0px;
    }

    .el-checkbox__label {
      font-size: 30px;
      font-family: PangMenZhengDao;
      font-weight: bold;
      /* font-style: italic; */
      color: #c0d6ed;
      line-height: 58px;
    }

    .el-checkbox__inner {
      width: 33px;
      height: 33px;
      margin-top: 15px;
      background-color: #344d67;
    }

    .auth-tree .el-checkbox__inner {
      width: 33px;
      height: 33px;
      margin-top: 21px;
      background-color: #344d67;
    }

    .sjzx_middle_left_container {
      padding: 30px 20px 0 40px;
    }

    .checkbox-box-img {
      width: 42px;
      height: 42px;
      position: relative;
      top: 10px;
    }

    /* .checkbox-box >span{
        position: relative;
        top:-10px;
      } */
    /* .el-checkbox.is-checked {
        background: linear-gradient(
          94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%
        );
        border-radius: 0px 30px 30px 0px;
      } */
    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #252316;
      border-color: #ffc561;
    }

    .el-checkbox__inner::after {
      width: 7px;
      height: 18px;
      left: 10px;
      color: #ffc561 !important;
    }

    .sjzx_middle_right {
      position: absolute;
      left: 2800px;
      top: 0;
      width: 620px;
      height: 1350px;
      background: linear-gradient(179deg, #0e1a40 0%, #064069 100%);
      box-shadow: 0px 7px 50px 0px rgba(35, 154, 228, 0.56);
    }

    .sjzx_middle_right_container {
      margin-top: 10px;
      margin-left: 90px;
      border-left: 8px solid #00ffff;
      /* height: 1200px; */
      /* overflow-y: auto; */
    }

    .sjzx_middle_right_content {
      height: 1000px;
      overflow-y: scroll;
    }

    .sjzx_middle_right_content::-webkit-scrollbar {
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
      /* scrollbar-arrow-color: red; */
    }

    .sjzx_middle_right_content::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }

    .btn_right {
      padding: 0 50px;
      min-width: 250px;
      width: auto;
      height: 53px;
      background-image: url("/static/citybrain/djtl/img/sjzx-middle/btn.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      font-size: 24px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #ffffff;
      line-height: 25px;
      text-shadow: 0px 2px 5px #000000;
      background-color: transparent;
      border: unset;
      margin-left: 40px;
      margin-bottom: 20px;
    }

    .yjyp-item p {
      font-size: 30px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
      margin-left: 50px;
      line-height: 40px;
    }

    .yjyp-item {
      margin-bottom: 70px;
    }

    .red {
      background: linear-gradient(180deg,
          #ffffff 0%,
          #ffcdcd 50.244140625%,
          #ff4949 53.0029296875%,
          #ffcdcd 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .yellow {
      background: linear-gradient(180deg,
          #ffffff 0%,
          #ffeccb 50.244140625%,
          #ffc460 53.0029296875%,
          #ffeccb 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .blue {
      color: #22e8e8 !important;
    }

    .item-s {
      margin-bottom: 20px;
    }

    .item-time {
      position: relative;
    }

    .item-time::before {
      position: absolute;
      left: -65px;
      content: "";
      display: inline-block;
      width: 51px;
      height: 25px;
      background-image: url("/static/citybrain/djtl/img/sjzx-middle/circle.png");
      background-size: 100% 100%;
    }

    .center_bottom {
      position: absolute;
      top: 1365px;
      left: 45px;

      display: flex;
      justify-content: center;
    }

    .shijian {
      width: 549px;
      height: 263px;
      background: url("/static/citybrain/csdn/img/ywt/dwjc-right-bc.png") no-repeat;
      background-size: 100% 100%;
      margin: 20px auto;
    }

    .shijian #eventMain {
      display: inline-block;
      width: 93.5%;
      margin: 25px;
    }

    .shijian .contain {
      overflow-y: auto;
      height: 239px;
      overflow-x: hidden;
      width: 525px;
    }

    .el-tree {
      background-color: unset;
    }

    .sjzx_middle_left_container {
      height: 480px;
      overflow-y: scroll;
    }

    .shijian .contain::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    .shijian .contain::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }

    .sjzx_middle_left_container::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    .sjzx_middle_left_container::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }

    .auth-tree>.el-tree-node>.el-tree-node__content .el-checkbox {
      display: none;
    }

    .el-icon-caret-left:before {
      font-size: 20px;
    }

    .el-tree-node__expand-icon {
      position: absolute;
      right: 0;
    }

    .el-tree-node__label {
      padding-left: 15px;
    }

    /* .el-tree-node__expand-icon.expanded {
        -webkit-transform: rotate(2700deg);
        transform: rotate(270deg);
      } */
    .el-tree-node__expand-icon.expanded {
      -webkit-transform: rotate(-90deg);
      transform: rotate(-90deg);
    }

    .el-checkbox__input.is-checked+.el-checkbox__label {
      color: #c0d6ed;
    }

    .el-tree-node__content>label.el-checkbox {
      position: absolute;
      right: 0;
    }
  </style>
</head>

<body>
  <div id="sjzx-middle">
    <div class="sjzx_middle_left">
      <div class="sjzx_middle_title">
        <p>
          <span class="before"></span>
          <span class="tab" @click="tabShow=true"
            :class="tabShow ? 's-c-yellow-gradient' : 's-c-blue-gradient'">{{areaName}}</span>
          <span class="after"></span>
        </p>
      </div>
      <div class="sjzx_middle_left_container">
        <el-tree :data="treeData" show-checkbox node-key="id" ref="tree" highlight-current :props="defaultProps"
          @check-change="checkChange" class="auth-tree" :render-after-expand="false" icon-class="el-icon-caret-left"
          v-show="tabShow">
          <div style="display: flex; align-items: center" slot-scope="{ node, data }">
            <img style="margin-left: 0.625rem; width: 40px; margin-right: 15px"
              :src="`/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/sjzx-${data.label}.png`" alt="" />
            <div style="
                  line-height: 3.125rem;
                  font-size: 30px;
                  font-family: PangMenZhengDao;
                  font-weight: bold;
                  color: #c0d6ed;
                  line-height: 58px;
                ">
              {{ (node.label.length > 6) ? node.label.slice(0,6)+"..." : node.label}}
              <span v-if="data.children">({{data.children.length}})</span>
            </div>
          </div>
        </el-tree>
      </div>
    </div>
  </div>
</body>

</html>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  var vm = new Vue({
    el: "#sjzx-middle",
    data: {
      areaName: "婺城区",
      treeData: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      tabShow: true,
      planData: {},//板块信息
      pointData: {},//点位信息
      show: true,
    },
    mounted () {
      let that = this
      window.addEventListener("message", function (event) {
        if (event.data.status == "openIframe") {
          console.log('获取标题', event.data.data)
          that.planData = event.data.data
          that.areaName = event.data.data.NAME
          that.getTreeData()
          top.mapUtil.removeAllLayers()
        }
      })
    },

    created () {
      // this.openDialog1();
      // this.initFun();
    },
    methods: {
      initFun () {
        let that = this
        top.mapUtil.loadRegionLayer({
          layerid: 'ybmap_bk',
          data: [
            { name: "婺城区", color: [78, 107, 221, 1], height: 2800 },
            { name: "开发区", color: [78, 107, 221, 1], height: 2600 },
            { name: "金东区", color: [46, 81, 221, 1], height: 2400 },
            { name: "兰溪市", color: [78, 107, 221, 1], height: 2200 },
            { name: "浦江县", color: [110, 133, 221, 1], height: 2000 },
            { name: "义乌市", color: [110, 133, 221, 1], height: 1800 },
            { name: "东阳市", color: [78, 107, 221, 1], height: 1600 },
            { name: "磐安县", color: [110, 133, 221, 1], height: 1400 },
            { name: "永康市", color: [46, 81, 221, 1], height: 1200 },
            { name: "武义县", color: [110, 133, 221, 1], height: 1000 },
          ],
          onclick: function (e) {
          }
        })
        top.mapUtil.flyTo({
          // destination: [119.920083, 29.242604],//飞行中心点
          destination: [119.95478050597587, 29.01613226366889],//飞行中心点
          zoom: 10.5,//飞行层级
          pitch: 40, //倾斜角
        })
        this.add3DText()

      },
      //点击数组件复选框
      checkChange (item, flag) {
        if (flag) {
          this.getPoint(item)
        } else {
          this.rmPoint(item.id)
          top.emiter.emit('yljg')
        }
      },
      // 获取经纬度
      getPoint (item) {
        let that = this
        $get("/ggfw/yb/ybsjd").then((res) => {
          let data = []
          data = res.filter((el) => {
            return item.id === el.id
          })
          that.pointData = data
          let pointData = []
          // let icon = "sjzx-" + item.label;
          let icon = "黄色预警事件"
          data.forEach((obj, index) => {
            let str = {
              data: {
                pointId: "sjrw",
                obj,
              },
              point: obj.lng,
            }
            pointData.push(str)
          })

          that.pointTextMapFun(icon, pointData, item.id)
        })
      },
      // 添加点位方法
      pointTextMapFun (icon, pointData, pointId) {
        let that = this
        top.mapUtil.loadPointLayer({
          data: pointData,
          layerid: "0" + pointId,
          iconcfg: {
            image: `/static/spritesImage/${icon}.png`,
            iconSize: 1,
          },
          popcfg: {
            show: false,
          },
          onclick: function (e) {
            console.log('点位点击事件', e)
            top.emiter.emit('ylhg')
            that.openDialog1()
          }
        })
      },
      //清除点位
      rmPoint (id) {
        top.mapUtil.removeLayer(`0${id}`)
      },
      // 加载3D文字方法
      add3DText () {
        let textData = [
          {
            pos: [119.87315399169922, 29.5030503845215, 11000],
            text: "浦江县",
            id: "330726000000",
          },
          {
            pos: [119.44214447021484, 29.22345558166504, 11000],
            text: "兰溪市",
            id: "330781000000",
          },
          {
            pos: [119.5869204711914, 28.96677101135254, 11000],
            text: "婺城区",
            id: "330702000000",
          },
          {
            pos: [119.8083056640625, 29.128559951782227, 11000],
            text: "金义新区",
            id: "330703000000",
          },
          {
            pos: [120.05206787109375, 29.272123641967773, 11000],
            text: "义乌市",
            id: "330782000000",
          },
          {
            pos: [119.7269204711914, 28.75677101135254, 11000],
            text: "武义县",
            id: "330723000000",
          },
          {
            pos: [120.1069204711914, 28.94677101135254, 11000],
            text: "永康市",
            id: "330784000000",
          },
          {
            pos: [120.4069204711914, 29.20677101135254, 11000],
            text: "东阳市",
            id: "330783000000",
          },
          {
            pos: [120.5999204711914, 29.02677101135254, 11000],
            text: "磐安县",
            id: "330727000000",
          },
        ]
        top.mapUtil.loadTextLayer({
          layerid: "ybmap3Dtext",
          data: textData,
          style: {
            size: 40,
          },
        })
      },

      //3D文字方法
      rm3DText () {
        top.mapUtil.removeLayer(`ybmap3Dtext`)
      },
      //格式化数组件
      tranListToTreeData (list, rootValue) {
        var arr = []
        list.forEach((item) => {
          if (item.parent_id === rootValue) {
            // 找到之后 就要去找 item 下面有没有子节点
            const children = this.tranListToTreeData(list, item.id)
            if (children.length) {
              // 如果children的长度大于0 说明找到了子节点
              item.children = children
            }
            arr.push(item) // 将内容加入到数组中
          }
        })
        return arr
      },
      //获取数组价数据
      getTreeData () {
        $get("/ggfw/yb/ybsjs").then((res) => {
          let data = []
          data = res.filter((item) => {
            return this.planData.NAME === item.fname
          })
          this.treeData = this.tranListToTreeData(data, 1)
        })
      },
      //打开地图弹窗
      openDialog1 () {
        let iframe1 = {
          type: "openIframe",
          name: "yljg",
          src:
            baseURL.url +
            "/static/citybrain/ggfw/commont/yljg.html",
          width: "810px",
          height: "660px",
          left: "4650px",
          top: "1425px",
          zIndex: "10",
          argument: {
            status: "openIframe1",
            data: this.pointData
          },
        }
        top.window.parent.postMessage(JSON.stringify(iframe1), "*")
      },
    },
  });
</script>