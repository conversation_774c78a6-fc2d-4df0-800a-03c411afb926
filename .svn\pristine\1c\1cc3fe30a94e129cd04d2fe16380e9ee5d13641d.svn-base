<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>资源分析评价弹窗</title>
        <script src="/Vue/vue.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain/ggfw/css/common-dialog.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <!-- 轮播toolTip -->
    </head>

    <style>
        .table1 .th .th_td:nth-child(1) {
            flex: 0.05;
        }
        .table1 .th .th_td:nth-child(2) {
            flex: 0.25;
        }
        .table1 .th .th_td:nth-child(3) {
            flex: 0.24;
        }
        .table1 .th .th_td:nth-child(4) {
            flex: 0.21;
        }
        .table1 .th .th_td:nth-child(5) {
            flex: 0.26;
        }
        .table1 .th .th_td:nth-child(6) {
            flex: 0.26;
        }
        .table1 .th .th_td:nth-child(7) {
            flex: 0.26;
        }
        .table1 .th .th_td:nth-child(8) {
            flex: 0.26;
        }
        /* 下拉框 */
        .el-select {
            width: 150px;
            margin-right: 170px;
        }
        .el-input__inner {
            height: 50px !important;
            width: 300px !important;
            background-color: #00487f;
            color: #fff;
            font-size: 28px;
        }
        .el-select-dropdown__item.hover,
        .el-select-dropdown__item:hover {
            background-color: #00487f;
        }
        .el-select-dropdown__item {
            color: #fff;
            background-color: #00487f;
            font-size: 28px;
        }
        .el-select-dropdown__list {
            background-color: #00487f;
        }
        .el-select .el-input .el-select__caret {
            position: relative;
            left: 140px;
            font-size: 28px;
            color: #fff;
        }
        .box2-content {
            display: unset;
        }
        .el-select .el-input__inner {
            /* border-radius: 30px !important; */
        }
        .el-scrollbar {
            width: 300px;
        }
        .el-input.is-disabled .el-input__inner {
            background-color: #2d4a67;
        }
        .content1 {
            width: 100%;
            height: 130px;
            display: flex;
            justify-content: space-between;
            margin: 20px 0px;
        }
        .content1 li {
            width: 30%;
            height: 100%;
            background-color: rgba(51, 51, 51, 0.7529411764705882);
            box-sizing: border-box;
            border-width: 1px;
            border-style: solid;
            border-color: rgba(128, 255, 255, 1);
            color: rgba(128, 255, 255, 1);
            font-size: 28px;
            list-style: none;
            line-height: 45px;
            padding: 20px;
            box-sizing: border-box;
        }
    </style>
    <body>
        <div id="app" class="container" v-cloak style="width: 1540px">
            <div class="head">
                <span>资源分析评价</span>
                <div class="img" @click="closeDialog"></div>
            </div>
            <div class="content">
                <!--资源分析 -->
                <div class="s-c-blue2-gradient s-font-40">智能筛选</div>
                <span class="s-c-yellow-gradient1 s-font-32 s-m-r-20">事件类型</span>
                <el-select v-model="value1" placeholder="请选择">
                    <el-option label="大类" value="大类"></el-option>
                </el-select>
                <span class="s-c-yellow-gradient1 s-font-32 s-m-r-20">事件等级</span>
                <el-select v-model="value2" placeholder="请选择">
                    <el-option label="一级" value="一级"></el-option>
                    <el-option label="二级" value="二级"></el-option>
                    <el-option label="三级" value="三级"></el-option>
                </el-select>
                <span class="s-c-yellow-gradient1 s-font-32 s-m-r-20">伤亡人数</span>
                <el-select v-model="value3" placeholder="请选择">
                    <el-option label="0-10" value="0-10"></el-option>
                    <el-option label="10-30" value="10-30"></el-option>
                    <el-option label="30-50" value="30-50"></el-option>
                </el-select>
                <div style="margin-top: 40px">
                    <span class="s-c-yellow-gradient1 s-font-32 s-m-r-20">资源调度</span>
                    <el-select v-model="value4" placeholder="请选择">
                        <el-option label="生活物资" value="生活物资"></el-option>
                        <el-option label="医疗物资" value="医疗物资"></el-option>
                    </el-select>
                    <el-button type="primary" style="font-size: 32px" size="medium" @click="queryData">搜索</el-button>
                </div>
                <div class="s-c-blue2-gradient s-font-40">推荐案例</div>
                <div class="content1">
                    <li v-for="(item,index) in tjalData" @click="queryTableData(index)">
                        <div>发生时间:{{item.time}}</div>
                        <div>发生地点:{{item.address}}</div>
                    </li>
                </div>
                <div class="dgjg">
                    <div class="s-c-blue2-gradient s-font-40">资源分析</div>
                    <div class="table1" style="height: 700px">
                        <div class="th">
                            <div class="th_td" v-for="(item,index) in theadList" :key="index">{{item}}</div>
                        </div>
                        <div class="tbody" id="box0" @mouseover="mouseenterEvent()" @mouseleave="mouseleaveEvent()">
                            <div class="tr" v-for="(item ,i) in tableList" :key="i">
                                <div class="tr_td" style="flex: 0.05">{{i+1}}</div>
                                <div class="tr_td" style="flex: 0.25">{{item.pjlx}}</div>
                                <div class="tr_td" style="flex: 0.24">{{item.gg}}</div>
                                <div class="tr_td" style="flex: 0.21">{{item.sl}}</div>
                                <div class="tr_td" style="flex: 0.26">{{item.yt}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    <script type="module">
        new Vue({
            el: "#app",
            data: {
                value1: "大类",
                value2: "一级",
                value3: "0-10",
                value4: "生活物资",
                theadList: ["序号", "评价类型", "规格", "数量", "用途"],
                tableList: [], //表格数据
                tjalData: [
                    {
                        time: "2021-09-10",
                        address: "金东区多湖街道",
                    },
                    {
                        time: "2021-09-21",
                        address: "金东区多湖街道",
                    },
                    {
                        time: "2021-10-10",
                        address: "金东区多湖街道",
                    },
                ],
            },
            methods: {
                queryData() {
                    $api("shgl_kbxx_sgkb04").then((res) => {
                        this.tjalData = res.filter((item) => {
                            return (
                                item.type == this.value1 &&
                                item.grade == this.value2 &&
                                item.num == this.value3 &&
                                item.resource == this.value4
                            );
                        });
                    });
                },
                queryTableData(index) {
                    if (index == 0) {
                        $api("shgl_kbxx_sgkb03").then((res) => {
                            this.tableList = res;
                        });
                    } else if (index == 1) {
                        $api("shgl_kbxx_sgkb03_1").then((res) => {
                            this.tableList = res;
                        });
                    } else {
                        $api("shgl_kbxx_sgkb03_2").then((res) => {
                            this.tableList = res;
                        });
                    }
                },
                closeDialog() {
                    top.commonObj.funCloseIframe({
                        name: "zyfx-dialog",
                    });
                },
                mouseenterEvent() {
                    clearInterval(this.time);
                },
                mouseleaveEvent() {
                    this.time = setInterval(() => {
                        this.dom.scrollTop += 2;
                        if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                            this.dom.scrollTop = 0;
                        }
                    }, 20);
                },
                autoScroll() {
                    this.dom = document.getElementById("box0");
                    // this.scpDom = document.getElementsByClassName('text')
                    this.time = setInterval(() => {
                        this.dom.scrollTop += 2;
                        if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                            this.dom.scrollTop = 0;
                        }
                    }, 20);
                },
                //数据初始化
                init() {
                    $api("shgl_kbxx_sgkb03").then((res) => {
                        this.tableList = res;
                    });
                },
            },
            //项目生命周期
            mounted() {
                this.init();
                this.autoScroll();
            },
        });
    </script>
</html>
