/*
 * @Author: CK
 * @email: <EMAIL>
 * @Date: 2022-07-27 17:42:14
 * @LastEditTime: 2022-08-01 16:47:59
 * @FilePath: \2DAnd3D\js\2Dmap\zoningSection.js
 * @Description: 区划版块
 */

// 区划板块geojson数据
import regionalSection from '../../data/regionalSection.js';
// 区划板块边界线geojson数据
import regionalSectionLine from '../../data/regionalSectionLine.js';
import { zoningSectionData } from '../globalVariable/mapFor2D.js';

let isMessage = false; // 是否是postMessage
let call = null; // 回调点击事件


function zoningSectionClick (e) {
    const obj = egis.queryRenderedFeatures(e.point,{
        layers: ['SectionHighlight']
    })[0].properties;
    call && call(obj);
    isMessage && window.parent.postMessage({
        type: 'bankuaiClick',
        data: obj,
    },'*');
}

// 删除区划版块
export function removeZoningSection() {
    if (egis.getLayer('SectionHighlight')) {
        egis.unce('click',zoningSectionClick,'SectionHighlight'); // 清除点击事件
        egis.removeLayer('SectionHighlight');
        egis.removeSource('SectionHighlight');
        egis.removeLayer('SectionHighlightLine');
        egis.removeSource('SectionHighlightLine');
        isMessage = false;
        call = null;
    }
}

// 加载区划板块
export function addZoningSection (data) {
    let SectionColor = null; // 版块颜色
    let SectionLineColor = null; // 边界线颜色
    if (data) {
        if (data.SectionColor) {
            zoningSectionData.SectionColor = SectionColor =  data.SectionColor; // 修改版块颜色
        } else {
            SectionColor =  zoningSectionData.SectionColor;
        }
        if (data.SectionLineColor) {
            zoningSectionData.SectionLineColor = SectionLineColor =  data.SectionLineColor; // 修改版块颜色
        } else {
            SectionLineColor =  zoningSectionData.SectionLineColor;
        }
    }
    const height = data && data.height ? data.height : zoningSectionData.height ; // 修改高度
    const opacity = data && data.opacity ? data.opacity : zoningSectionData.opacity; // 修改透明度
    //判断是否有区划板块
    if (egis.getLayer('SectionHighlight')) {
        // 有，修改区划板块颜色
        egis.setPaintProperty('SectionHighlight', 'fill-extrusion-color', SectionColor); // 修改版块颜色
        egis.setPaintProperty('SectionHighlight', 'fill-extrusion-height', height);// 修改高度
        egis.setPaintProperty('SectionHighlight', 'fill-extrusion-opacity', opacity);// 修改透明度
        egis.setPaintProperty('SectionHighlightLine', 'fill-extrusion-color', SectionLineColor);// 修改边界线颜色
        egis.setPaintProperty('SectionHighlightLine', 'fill-extrusion-height', (height + 10));// 修改边界线颜色
        call = data && data.call; // 获取回调事件
        isMessage = data && data.isMessage
    } else {
        // 没有，创建区划版块
        // 定义区划版块geojson
        const regionalSectionFeatureCollection = JSON.parse(JSON.stringify(regionalSection));
        // 定义 区划板块边界线geojson数据
        const regionalSectionFeatureLineCollection = JSON.parse(JSON.stringify(regionalSectionLine));
        // 添加区划板块图层
        egis.addLayer({
            id: 'SectionHighlight',
            source: {
                type: 'geojson',
                data: regionalSectionFeatureCollection
            },
            type: "fill-extrusion",
            'paint': {
                'fill-extrusion-color': SectionColor,
                'fill-extrusion-height': height,
                'fill-extrusion-base': 1,
                'fill-extrusion-opacity': opacity
            }
        })
        // 添加区划版块边界
        egis.addLayer({
            id: 'SectionHighlightLine',
            source: {
                type: 'geojson',
                data: regionalSectionFeatureLineCollection
            },
            type: "fill-extrusion",
            'paint': {
                'fill-extrusion-color': SectionLineColor,
                'fill-extrusion-height': (height + 10),
                'fill-extrusion-base': 1000,
                'fill-extrusion-opacity': 1
            }
        });
        call = data && data.call; // 获取回调事件
        isMessage = data && data.isMessage; // 获取是否返回postMessage
        egis.once('click',zoningSectionClick,'SectionHighlight'); // 添加点击事件
    }
}