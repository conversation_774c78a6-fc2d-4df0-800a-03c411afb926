<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>事件中心-点位事件详情</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/Vue/vue.js"></script>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="/elementui/css2/index.css" />
    <!-- 引入组件库 -->
    <script src="/elementui/js2/index.js"></script>
    <style>
      body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 2160px;
        background-color: #00000065;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .sjzx_middle_title {
        font-size: 36px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: #d6e7f9;
        background: linear-gradient(
          180deg,
          #aed6ff 0%,
          #74b8ff 47.4853515625%,
          #9ccfff 50%,
          #ddeeff 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-align: center;
      }
      .sjzx_middle_title p {
        margin-top: 10px;
        height: 82px;
        line-height: 83px;
        white-space: nowrap;
      }
      .sjzx_middle_title p:before {
        content: "";
        height: 1px;
        top: -3%;
        position: relative;
        width: 8%;
        height: 1px;
        border-bottom: 3px solid #74b8ff;
        display: inline-block;
        margin-right: 5px;
        margin-bottom: 12px;
      }
      ::-webkit-scrollbar {
        width: 6px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
        /* scrollbar-arrow-color: red; */
      }

      ::-webkit-scrollbar-thumb {
        border-radius: 6px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
        background: #20aeff;
        height: 8px;
      }

      .sjzx_middle_title p:after {
        content: "";
        top: -3%;
        position: relative;
        width: 8%;
        height: 1px;
        border-bottom: 3px solid #74b8ff;
        display: inline-block;
        margin-left: 5px;
        margin-bottom: 12px;
      }
      .sjzx_middle_title .before {
        display: inline-block;
        width: 10px;
        height: 10px;
        background-color: #74b8ff;
        /* transform: rotateZ(90deg); */
        border-radius: 5px;
        margin-bottom: 10px;
      }
      .sjzx_middle_title .after {
        /* display: inline-block; */
        display: inline-block;
        width: 10px;
        height: 10px;
        background-color: #74b8ff;
        /* transform: rotateZ(90deg); */
        border-radius: 5px;
        margin-bottom: 10px;
      }
      .sjzx_middle_right {
        /* position: absolute;
        left: 2800px;
        top: 0; */
        margin: 0;
        padding: 0;
        width: 620px;
        height: 100%;
        /* height: 1350px; */
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .sjzx_middle_right_container {
        margin-top: 10px;
        margin-left: 90px;
        border-left: 8px solid #00ffff;
        /* height: 1200px; */
        /* overflow-y: auto; */
      }
      .sjzx_middle_right_content {
        height: 1250px;
        overflow-y: scroll;
      }
      .sjzx_middle_right_content::-webkit-scrollbar {
        width: 6px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
        /* scrollbar-arrow-color: red; */
      }

      .sjzx_middle_right_content::-webkit-scrollbar-thumb {
        border-radius: 6px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
        background: #20aeff;
        height: 8px;
      }
      .btn_right {
        padding: 0 50px;
        min-width: 250px;
        width: auto;
        height: 53px;
        background-image: url("/static/citybrain/djtl/img/sjzx-middle/btn.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        font-size: 24px;
        font-family: FZZhengHeiS-DB-GB;
        font-weight: 400;
        color: #ffffff;
        line-height: 25px;
        text-shadow: 0px 2px 5px #000000;
        background-color: transparent;
        border: unset;
        margin-left: 40px;
        margin-bottom: 20px;
      }
      .yjyp-item p {
        font-size: 30px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        margin-left: 50px;
        line-height: 40px;
      }
      .yjyp-item {
        margin-bottom: 70px;
      }
      .red {
        background: linear-gradient(
          180deg,
          #ffffff 0%,
          #ffcdcd 50.244140625%,
          #ff4949 53.0029296875%,
          #ffcdcd 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .yellow {
        background: linear-gradient(
          180deg,
          #ffffff 0%,
          #ffeccb 50.244140625%,
          #ffc460 53.0029296875%,
          #ffeccb 100%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .blue {
        color: #22e8e8 !important;
      }
      .item-s {
        margin-bottom: 20px;
      }
      .item-time {
        position: relative;
      }
      .item-time::before {
        position: absolute;
        left: -65px;
        content: "";
        display: inline-block;
        width: 51px;
        height: 25px;
        background-image: url("/static/citybrain/djtl/img/sjzx-middle/circle.png");
        background-size: 100% 100%;
      }
      .center_bottom {
        position: absolute;
        top: 1365px;
        left: 45px;

        display: flex;
        justify-content: center;
      }
      .shijian {
        width: 549px;
        height: 263px;
        background: url("/static/citybrain/csdn/img/ywt/dwjc-right-bc.png")
          no-repeat;
        background-size: 100% 100%;
        margin: 20px auto;
      }

      .shijian #eventMain {
        display: inline-block;
        width: 93.5%;
        margin: 25px;
      }
      .shijian .contain {
        overflow-y: auto;
        height: 239px;
        overflow-x: hidden;
        width: 525px;
      }
      .shijian .contain::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
      }

      .shijian .contain::-webkit-scrollbar-thumb {
        border-radius: 6px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
        background: #20aeff;
        height: 8px;
      }
      .sjzx-middle {
        width: 2168px;
        height: 1617px;
        background-color: #031827;
        box-shadow: -3px 2px 35px 0px #000000;
        border-style: solid;
        border-width: 2px;
        border-image-source: linear-gradient(-32deg, #359cf8 0%, #afdcfb 100%);
        border-image-slice: 1;
      }
      .head {
        width: 100%;
        height: 100px;
        line-height: 100px;
        background-image: linear-gradient(0deg, #073346 0%, #00aae2 100%),
          linear-gradient(#ffffff, #ffffff);
        background-blend-mode: normal, normal;
        padding: 10px 50px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .head > span {
        font-size: 48px !important;
        font-weight: 500;
        color: #fff;
      }

      .img {
        display: inline-block;
        margin: 20px;
        float: right;
        width: 34px;
        height: 34px;
        background-image: url("/static/citybrain/csdn/img/cstz2-middle/close-hover.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
      .sjzx-middle-con {
        width: 100%;
        height: calc(100% - 100px);
        padding: 50px;
        box-sizing: border-box;
      }
      .m-con {
        width: 100%;
        height: 100%;
        border: solid 1px #00c0ff;
        background-color: #043755;
      }
      .table-css {
        width: 100%;
        height: 100%;
        font-size: 40px;
        color: #fff;
      }
      .table-th {
        width: 100%;
        height: 100px;
        background-image: linear-gradient(0deg, #00506a 0%, #0097c8 100%);
        border-bottom: solid 1px #00c0ff;
        display: flex;
      }
      .th-css {
        font-size: 48px;
        line-height: 100px;
        text-align: center;
        font-weight: bold;
      }
      .th-css:first-child {
        flex: 1;
        border-right: 1px solid #00c0ff;
      }
      .table-tr {
        width: 100%;
        display: flex;
        height: 1315px;
      }
      .tr-left {
        width: 1448px;
      }
      .tr-right {
        width: 620px;
        height: 100%;
        border-left: 1px solid #00c0ff;
      }
      .tr-css {
        width: 100%;
        display: flex;
        height: 200px;
        border-bottom: 1px solid #00c0ff;
      }
      .tr-css:last-child {
        border-bottom: 1px solid transparent;
      }
      .tr-css:nth-of-type(2) {
        height: 400px;
      }
      .tr-css:last-child {
        height: 305px;
      }
      .tr-css > div {
        display: flex;
        align-items: center;
        padding-left: 50px;
        box-sizing: border-box;
      }
      .tr-title {
        width: 300px;
        background-color: #004c70;
        border-right: 1px solid #00c0ff;
      }
      .td {
        width: 1144px;
        height: 100%;
        overflow-y: auto;
      }
      .fj-css {
        width: 250px;
        height: 200px;
        border-radius: 10px;
        margin-right: 50px;
      }
      .fj-css > img {
        display: inline-block;
        width: 100%;
        height: 100%;
      }
      .el-image-viewer__actions {
        display: none;
      }
      .el-image-viewer__close {
        display: none;
      }
    </style>
  </head>
  <body>
    <div id="sjzx-main">
      <div class="sjzx-middle">
        <div class="head">
          <span>事件处理详情</span>
          <div class="img" @click="closeMiddleIframe('sjzx_middle_main')"></div>
        </div>
        <div class="sjzx-middle-con">
          <div class="m-con">
            <div class="table-css">
              <div class="table-th">
                <div class="th-css yellow">事件信息</div>
                <div class="th-css yellow" style="width: 620px">处理流程</div>
              </div>
              <div class="table-tr">
                <div class="tr-left">
                  <div class="tr-css">
                    <div class="tr-title">事件名称</div>
                    <div class="td">{{tableData.sjmc}}</div>
                  </div>
                  <div class="tr-css">
                    <div class="tr-title">描述</div>
                    <div class="td">{{tableData.ms}}</div>
                  </div>
                  <div class="tr-css">
                    <div class="tr-title">网格</div>
                    <div class="td">{{tableData.wg}}</div>
                  </div>
                  <div class="tr-css">
                    <div class="tr-title">地址</div>
                    <div class="td">{{tableData.dz}}</div>
                  </div>
                  <div class="tr-css">
                    <div class="tr-title">附件</div>
                    <div class="td" style="display: flex">
                      <div
                        class="fj-css"
                        v-if="fjArr!=''"
                        v-for="item in fjArr"
                      >
                        <!-- <img :src="item" alt=""> -->
                        <el-image
                          style="width: 100%; height: 100%"
                          :src="item"
                          :preview-src-list="[item]"
                        >
                        </el-image>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="tr-right">
                  <div class="sjzx_middle_right">
                    <div class="sjzx_middle_right_content">
                      <div class="sjzx_middle_right_container">
                        <div
                          class="yjyp-item"
                          v-for="(item,i) in dataList"
                          v-if="item.handleTime != '' && item.handleNode != ' '"
                        >
                          <button class="btn_right" v-if="item.handleNode">
                            {{item.handleNode}}
                          </button>
                          <div class="item-s">
                            <p class="item-time">{{item.handleTime}}</p>
                            <p :class="i==1?'yellow':(i==2?'red':'blue')">
                              {{item.handleDescribe}}
                            </p>
                            <div
                              style="
                                display: flex;
                                flex-wrap: wrap;
                                margin-left: 50px;
                              "
                            >
                              <div
                                class="fj-css"
                                style="
                                  width: 120px;
                                  height: 120px;
                                  margin-bottom: 10px;
                                "
                                v-if="item.fjArr!=''"
                                v-for="ele in item.fjArr"
                              >
                                <!-- <img :src="ele" alt=""> -->
                                <el-image
                                  style="width: 100%; height: 100%"
                                  :src="ele"
                                  :preview-src-list="[ele]"
                                  lazy
                                >
                                </el-image>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>
    window.addEventListener("message", function (e) {
      if (e.data && e.data.ly) {
        vm.initApi(e.data);
      }
    });
    var vm = new Vue({
      el: "#sjzx-main",
      data: {
        dataMain: "",
        dataList: [],
        tableData: {
          sjmc: "",
          ms: "",
          wg: "",
          dz: "",
          fj: "",
        },
        fjArr: [],
      },
      created() {},
      mounted() {
        if (window.location.search) {
          let a1 = window.location.search
            .replace("?", "")
            .split("&")[0]
            .split("=")[0];
          let b1 = window.location.search
            .replace("?", "")
            .split("&")[0]
            .split("=")[1];
          let a2 = window.location.search
            .replace("?", "")
            .split("&")[1]
            .split("=")[0];
          let b2 = window.location.search
            .replace("?", "")
            .split("&")[1]
            .split("=")[1];
          let idnew = a1 == "id" ? b1 : b2;
          let lynew = a2 == "ly" ? b2 : b1;
          let obj = { id: idnew, ly: lynew };
          this.initApi(obj);
        }
      },
      methods: {
        initApi(obj) {
          console.log("obj==>", obj);
          $api("/csdnsjrw_middle08", { id: obj.id, ly: obj.ly }).then((res) => {
            this.dataMain = res[0].eventDescribe;
          });
          $api("/csdnsjrw_middle09", { id: obj.id, ly: obj.ly }).then((res) => {
            this.tableData = res[0];
            let arr = res[0].fj.split(",");
            this.fjArr = arr.map((item) => {
              if (item != "" && item.indexOf("http") == -1) {
                item =
                  "http://************:8100/adm-api/common/download/resource?resource=" +
                  item;
              }
              return item;
            });
          });
          $api("/csdnsjrw_middle05", { id: obj.id, ly: obj.ly }).then((res) => {
            let arr = res;
            console.log("res05======>", res);
            for (let i = 0; i < arr.length; i++) {
              if (i < arr.length - 1) {
                if (arr[i].handleNode == arr[i + 1].handleNode) {
                  arr[i + 1].handleNode = "";
                }
              }
              let fileArr = arr[i].file.split(",");
              arr[i].fjArr = fileArr.map((item) => {
                if (item != "" && item.indexOf("http") == -1) {
                  item =
                    "http://************:8100/adm-api/common/download/resource?resource=" +
                    item;
                }
                return item;
              });
            }
            this.dataList = arr;
          });
        },
        // 关闭弹窗
        closeMiddleIframe(name) {
          let data = JSON.stringify({
            type: "closeIframe",
            name: name,
          });
          window.parent.postMessage(data, "*");
        },
      },
    });
  </script>
</html>
