<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>安全生产事故月度态势分析</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <link rel="stylesheet" href="../css/shgl-csaq-aqscsgndtsfx-left.css" />
    </head>

    <body>
        <div id="shgl-csaq-aqscsgndtsfx-left">
            <div class="content">
                <div class="title">
                    <nav style="padding: 20px 45px 0 45px">
                        <s-header-title2 style="width: 100%" title="月度事故总体情况展示" htype="1"></s-header-title2>
                    </nav>
                </div>
                <div class="sjfxTitle">安全生产事件风险分析</div>
                <div class="sjfxBox">
                    <div>
                        <div class="sjBox">
                            <div class="sgkgTitle">
                                <span class="cf"></span>
                                <div>月度事故概况</div>
                            </div>
                            <div class="sgzsTitle">
                                <span class="jsTitle">{{scsgValue}}</span>
                                <div>{{scsg}}</div>
                            </div>
                        </div>
                        <div class="tabTitle">
                            <div
                                v-for="(item ,index) in ndsg"
                                @click="changeEcharts(index)"
                                class="titleSG"
                                :class="{active:isActive===index}"
                            >
                                {{item}}
                            </div>
                        </div>
                        <div v-show="isActive===0" id="barEcharts001" style="width: 560px; height: 350px"></div>
                        <div v-show="isActive===1" id="barEcharts002" style="width: 560px; height: 350px"></div>
                    </div>

                    <div class="center">
                        <div class="sjBox">
                            <div class="sgkgTitle">
                                <span class="cf"></span>
                                <div>事故量变化趋势</div>
                            </div>
                        </div>
                        <div>
                            <div class="sglTitle">
                                <span class="jsTitle">{{scsgValue}}</span>
                                <div>{{scsg}}</div>
                            </div>
                            <div class="bottom">
                                <div class="sglTitle" v-for="item in sgl">
                                    <span class="jsTitle">{{item.value}}</span>
                                    <div>{{item.name}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="sjBox">
                            <div class="sgkgTitle">
                                <span class="cf"></span>
                                <div>月度事故概况</div>
                            </div>
                        </div>
                        <div class="tabTitle">
                            <div class="sgkgTitle" style="margin-top: 20px">
                                <span class="cf"></span>
                                <div style="font-size: 25px">经济损失总量</div>
                            </div>
                            <div
                                v-for="(item ,index) in ndsg"
                                @click="changeEchartsOne(index)"
                                class="titleSG"
                                :class="{active:isActiveOne===index}"
                            >
                                {{item}}
                            </div>
                        </div>
                        <div v-show="isActiveOne===0" id="barEcharts003" style="width: 560px; height: 350px"></div>
                        <div v-show="isActiveOne===1" id="barEcharts004" style="width: 560px; height: 350px"></div>
                    </div>
                    <div>
                        <div class="sjBox">
                            <div class="sgkgTitle">
                                <span class="cf"></span>
                                <div>月度事故概况</div>
                            </div>
                        </div>
                        <div class="tabTitle">
                            <div class="sgkgTitle" style="margin-top: 20px">
                                <span class="cf"></span>
                                <div style="font-size: 25px">死亡人数总量</div>
                            </div>
                            <div
                                v-for="(item ,index) in ndsg"
                                @click="changeEchartsTwo(index)"
                                class="titleSG"
                                :class="{active:isActiveTwo===index}"
                            >
                                {{item}}
                            </div>
                        </div>
                        <div v-show="isActiveTwo===0" id="barEcharts005" style="width: 560px; height: 350px"></div>
                        <div v-show="isActiveTwo===1" id="barEcharts006" style="width: 560px; height: 350px"></div>
                    </div>
                </div>
                <div class="title">
                    <nav style="padding: 0 45px">
                        <s-header-title2 style="width: 100%" title="安全生产企业概况展示" htype="1"></s-header-title2>
                    </nav>
                </div>
                <div class="sjfxBox">
                    <div>
                        <div class="sjBox">
                            <div class="sgkgTitle">
                                <span class="cf"></span>
                                <div>企业数量</div>
                            </div>
                        </div>
                        <div class="firstBox">
                            <div class="qyTitle">
                                <div>企业数量</div>
                                <div class="topTitle">+{{qysl1*10}}%↑</div>
                            </div>
                            <div class="ttNum">
                                <h1>{{qysl*10000}}</h1>
                                <span>家</span>
                            </div>
                            <div id="barEcharts007" style="width: 540px; height: 150px"></div>
                        </div>
                    </div>

                    <div>
                        <div class="sjBox">
                            <div class="sgkgTitle">
                                <span class="cf"></span>
                                <div>企业类型</div>
                            </div>
                        </div>

                        <div id="pieEcharts001" style="width: 640px; height: 500px"></div>
                    </div>
                    <div>
                        <div class="sjBox">
                            <div class="sgkgTitle">
                                <span class="cf"></span>
                                <div>企业分布</div>
                            </div>
                        </div>

                        <div id="sdEcharts001" style="width: 748px; height: 470px"></div>
                    </div>
                </div>
                <div class="title">
                    <nav style="padding: 0 45px">
                        <s-header-title2 style="width: 100%" title="双重预防工作展示" htype="1"></s-header-title2>
                    </nav>
                </div>
                <div class="sjfxBox">
                    <div>
                        <div class="sjBox">
                            <div class="sgkgTitle">
                                <span class="cf"></span>
                                <div>风控管控</div>
                            </div>
                        </div>
                        <div>
                            <div class="tabTitle">
                                <div
                                    v-for="(item ,index) in fxgk"
                                    @click="changeEchartsThree(index)"
                                    class="titleSG"
                                    :class="{active:isActiveThree===index}"
                                >
                                    {{item}}
                                </div>
                            </div>
                            <div
                                v-show="isActiveThree===0"
                                id="barEcharts008"
                                style="width: 560px; height: 400px"
                            ></div>
                            <div
                                v-show="isActiveThree===1"
                                id="barEcharts009"
                                style="width: 560px; height: 400px"
                            ></div>
                        </div>
                    </div>

                    <div>
                        <div class="sjBox yhBox">
                            <div class="sgkgTitle">
                                <span class="cf"></span>
                                <div>风险总数趋势</div>
                            </div>
                        </div>

                        <div class="line" id="lineEcharts001" style="width: 640px; height: 400px"></div>
                    </div>
                    <div>
                        <div class="sjBox yhBox">
                            <div class="sgkgTitle">
                                <span class="cf"></span>
                                <div>隐患总数趋势</div>
                            </div>
                        </div>

                        <div id="lineEcharts002" style="width: 640px; height: 400px"></div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#shgl-csaq-aqscsgndtsfx-left",
        data: {
            ndsg: ["事故等级", "事故类型"],
            fxgk: ["月度", "月度"],
            isActive: 0,
            isActiveOne: 0,
            isActiveTwo: 0,
            isActiveThree: 0,
            qysl: "",
            qysl1: "",
            scsg: "",
            scsgValue: "",
            sgl: [],
        },
        mounted() {
            this.openIframe();
            this.initFun();
            this.initMap();
            
        },
        methods: {
            openIframe() {
                let left = {
                    type: "openIframe",
                    name: "ndts",
                    src: baseURL.url + "/static/citybrain/shgl/commont/csaq-ndfx-middle.html",
                    left: "2160px",
                    top: "230px",
                    width: "350px",
                    height: "500px",
                    zIndex: "10",
                    argument: {
                        status: "",
                    },
                };
                window.parent.postMessage(JSON.stringify(left), "*");
            },
            initFun() {
                $api("shgl_aqscsgydtsfx_scsg_01").then((res) => {
                    this.scsg = res[0].name;
                    this.scsgValue = res[0].value;
                });
                $api("shgl_aqscsgydtsfx_scsg_02").then((res) => {
                    this.sgl = res;
                });
                $api("shgl_aqscsgydtsfx-left001").then((res) => {
                    this.getEcharts01("barEcharts001", res);
                });
                $api("shgl_aqscsgydtsfx-left003").then((res) => {
                    this.getEcharts03("barEcharts003", res);
                });
                $api("shgl_aqscsgydtsfx-left005").then((res) => {
                    this.getEcharts05("barEcharts005", res);
                });
                $api("shgl_aqscsgydtsfx-left007").then((res) => {
                    this.qysl = res[0].value;
                    this.qysl1 = res[0].value1;
                    this.getEcharts07("barEcharts007", res);
                });
                $api("shgl_aqscsgydtsfx-left008").then((res) => {
                    this.getEcharts08("pieEcharts001", res);
                });
                $api("shgl_aqscsgydtsfx-left009").then((res) => {
                    this.getEcharts09("sdEcharts001", res);
                });
                $api("shgl_aqscsgydtsfx-left010").then((res) => {
                    this.getEcharts10("barEcharts008", res);
                });
                $api("shgl_aqscsgydtsfx-left012").then((res) => {
                    this.getEcharts12("lineEcharts001", res);
                });
                $api("shgl_aqscsgydtsfx-left013").then((res) => {
                    this.getEcharts12("lineEcharts002", res);
                });
            },
            changeEcharts(index) {
                this.isActive = index;
                if (this.isActive === 1) {
                    $api("shgl_aqscsgydtsfx-left002").then((res) => {
                        this.getEcharts02("barEcharts002", res);
                    });
                }
            },
            changeEchartsOne(index) {
                this.isActiveOne = index;
                if (this.isActiveOne === 1) {
                    $api("shgl_aqscsgydtsfx-left004").then((res) => {
                        this.getEcharts04("barEcharts004", res);
                    });
                }
            },
            changeEchartsTwo(index) {
                this.isActiveTwo = index;
                if (this.isActiveTwo === 1) {
                    $api("shgl_aqscsgydtsfx-left006").then((res) => {
                        this.getEcharts06("barEcharts006", res);
                    });
                }
            },
            getEcharts01(dom, echartData) {
                let echarts1 = echarts.init(document.getElementById(dom));
                let xData = echartData.map((item) => {
                    return item.time;
                });
                let yData = echartData.map((item) => {
                    return item.value;
                });
                let yData1 = echartData.map((item) => {
                    return item.value1;
                });
                let yData2 = echartData.map((item) => {
                    return item.value2;
                });

                let option = {
                    tooltip: {
                        backgroundColor: "#fff05",
                        trigger: "axis",
                        fontSize: 30,
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                        axisPointer: {
                            type: "shadow",
                            textStyle: {
                                color: "#fff",
                            },
                        },
                    },
                    legend: {
                        textStyle: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        top: 30,
                        icon: "square",
                        itemWidth: 30,
                        itemHeight: 30,
                    },
                    grid: {
                        left: "2%",
                        right: "4%",
                        bottom: "14%",
                        top: "25%",
                        containLabel: true,
                    },
                    calculable: true,
                    xAxis: [
                        {
                            type: "category",
                            offset: 10,
                            axisLine: {
                                lineStyle: {
                                    color: "#19416b",
                                },
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            splitArea: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                color: "rgba(255,255,255,0.7)",
                                fontSize: 30,
                            },
                            data: xData,
                        },
                    ],
                    yAxis: [
                        {
                            type: "value",

                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#19416b",
                                },
                            },
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                color: "rgba(255,255,255,0.5)",
                                fontSize: 30,
                            },
                        },
                    ],
                    series: [
                        {
                            name: "特大隐患",
                            type: "bar",
                            stack: "总量",
                            barMaxWidth: 35,
                            barGap: "10%",
                            itemStyle: {
                                normal: {
                                    color: "#bd352c",
                                },
                            },
                            data: yData,
                        },
                        {
                            name: "重大隐患",
                            type: "bar",
                            stack: "总量",
                            itemStyle: {
                                normal: {
                                    color: "#398912",
                                    barBorderRadius: 0,
                                },
                            },
                            data: yData1,
                        },
                        {
                            name: "一般隐患",
                            type: "bar",
                            stack: "总量",
                            itemStyle: {
                                normal: {
                                    color: "#213ba4",
                                    barBorderRadius: 0,
                                },
                            },
                            data: yData2,
                        },
                    ],
                };
                echarts1.setOption(option);
            },
            getEcharts02(dom, echartData) {
                let echarts2 = echarts.init(document.getElementById(dom));
                let xData = echartData.map((item) => {
                    return item.time;
                });
                let yData = echartData.map((item) => {
                    return item.value;
                });
                let yData1 = echartData.map((item) => {
                    return item.value1;
                });
                let yData2 = echartData.map((item) => {
                    return item.value2;
                });
                var option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                    },
                    grid: {
                        left: "2%",
                        right: "4%",
                        bottom: "14%",
                        top: "25%",
                        containLabel: true,
                    },
                    legend: {
                        data: ["大类", "亚类", "细类"],
                        right: 8,
                        top: 12,
                        textStyle: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        itemWidth: 30,
                        itemHeight: 30,
                        // itemGap: 35
                    },
                    xAxis: {
                        type: "category",
                        data: xData,
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            lineStyle: {
                                color: "#203951",
                            },
                        },
                        axisLabel: {
                            interval: 0,
                            offset: 10,
                            textStyle: {
                                color: "#FFFFFF",
                                fontSize: 30,
                            },
                        },
                    },

                    yAxis: {
                        type: "value",

                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: "white",
                            },
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: "#203951",
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                fontSize: 30,
                            },
                        },
                    },
                    series: [
                        {
                            name: "大类",
                            type: "bar",
                            barWidth: "15%",
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#fccb05",
                                        },
                                        {
                                            offset: 1,
                                            color: "#f5804d",
                                        },
                                    ]),
                                    barBorderRadius: 12,
                                },
                            },
                            data: yData,
                        },
                        {
                            name: "亚类",
                            type: "bar",
                            barWidth: "15%",
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#8bd46e",
                                        },
                                        {
                                            offset: 1,
                                            color: "#09bcb7",
                                        },
                                    ]),
                                    barBorderRadius: 11,
                                },
                            },
                            data: yData1,
                        },
                        {
                            name: "细类",
                            type: "bar",
                            barWidth: "15%",
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#248ff7",
                                        },
                                        {
                                            offset: 1,
                                            color: "#6851f1",
                                        },
                                    ]),
                                    barBorderRadius: 11,
                                },
                            },
                            data: yData2,
                        },
                    ],
                };
                echarts2.setOption(option);
            },
            getEcharts03(dom, echartData) {
                let echarts1 = echarts.init(document.getElementById(dom));
                let xData = echartData.map((item) => {
                    return item.time;
                });
                let yData = echartData.map((item) => {
                    return item.value;
                });
                let yData1 = echartData.map((item) => {
                    return item.value1;
                });
                let yData2 = echartData.map((item) => {
                    return item.value2;
                });

                let option = {
                    tooltip: {
                        backgroundColor: "#fff05",
                        trigger: "axis",
                        fontSize: 30,
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                        axisPointer: {
                            type: "shadow",
                            textStyle: {
                                color: "#fff",
                            },
                        },
                    },
                    legend: {
                        textStyle: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        top: 30,
                        icon: "square",
                        itemWidth: 30,
                        itemHeight: 30,
                    },
                    grid: {
                        left: "2%",
                        right: "4%",
                        bottom: "14%",
                        top: "25%",
                        containLabel: true,
                    },
                    calculable: true,
                    xAxis: [
                        {
                            type: "category",
                            offset: 10,
                            axisLine: {
                                lineStyle: {
                                    color: "#19416b",
                                },
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            splitArea: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                color: "rgba(255,255,255,0.7)",
                                fontSize: 30,
                            },
                            data: xData,
                        },
                    ],
                    yAxis: [
                        {
                            type: "value",

                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#19416b",
                                },
                            },
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                color: "rgba(255,255,255,0.5)",
                                fontSize: 30,
                            },
                        },
                    ],
                    series: [
                        {
                            name: "特大隐患",
                            type: "bar",
                            stack: "总量",
                            barMaxWidth: 35,
                            barGap: "10%",
                            itemStyle: {
                                normal: {
                                    color: "#bd352c",
                                },
                            },
                            data: yData,
                        },
                        {
                            name: "重大隐患",
                            type: "bar",
                            stack: "总量",
                            itemStyle: {
                                normal: {
                                    color: "#398912",
                                    barBorderRadius: 0,
                                },
                            },
                            data: yData1,
                        },
                        {
                            name: "一般隐患",
                            type: "bar",
                            stack: "总量",
                            itemStyle: {
                                normal: {
                                    color: "#213ba4",
                                    barBorderRadius: 0,
                                },
                            },
                            data: yData2,
                        },
                    ],
                };
                echarts1.setOption(option);
            },
            getEcharts04(dom, echartData) {
                let echarts2 = echarts.init(document.getElementById(dom));
                let xData = echartData.map((item) => {
                    return item.time;
                });
                let yData = echartData.map((item) => {
                    return item.value;
                });
                let yData1 = echartData.map((item) => {
                    return item.value1;
                });
                let yData2 = echartData.map((item) => {
                    return item.value2;
                });
                var option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                    },
                    grid: {
                        left: "2%",
                        right: "4%",
                        bottom: "14%",
                        top: "25%",
                        containLabel: true,
                    },
                    legend: {
                        data: ["大类", "亚类", "细类"],
                        right: 8,
                        top: 12,
                        textStyle: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        itemWidth: 30,
                        itemHeight: 30,
                        // itemGap: 35
                    },
                    xAxis: {
                        type: "category",
                        data: xData,
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            lineStyle: {
                                color: "#203951",
                            },
                        },
                        axisLabel: {
                            interval: 0,
                            offset: 10,
                            textStyle: {
                                color: "#FFFFFF",
                                fontSize: 30,
                            },
                        },
                    },

                    yAxis: {
                        type: "value",

                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: "white",
                            },
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: "#203951",
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                fontSize: 30,
                            },
                        },
                    },
                    series: [
                        {
                            name: "大类",
                            type: "bar",
                            barWidth: "15%",
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#fccb05",
                                        },
                                        {
                                            offset: 1,
                                            color: "#f5804d",
                                        },
                                    ]),
                                    barBorderRadius: 12,
                                },
                            },
                            data: yData,
                        },
                        {
                            name: "亚类",
                            type: "bar",
                            barWidth: "15%",
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#8bd46e",
                                        },
                                        {
                                            offset: 1,
                                            color: "#09bcb7",
                                        },
                                    ]),
                                    barBorderRadius: 11,
                                },
                            },
                            data: yData1,
                        },
                        {
                            name: "细类",
                            type: "bar",
                            barWidth: "15%",
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#248ff7",
                                        },
                                        {
                                            offset: 1,
                                            color: "#6851f1",
                                        },
                                    ]),
                                    barBorderRadius: 11,
                                },
                            },
                            data: yData2,
                        },
                    ],
                };
                echarts2.setOption(option);
            },
            getEcharts05(dom, echartData) {
                let echarts1 = echarts.init(document.getElementById(dom));
                let xData = echartData.map((item) => {
                    return item.time;
                });
                let yData = echartData.map((item) => {
                    return item.value;
                });
                let yData1 = echartData.map((item) => {
                    return item.value1;
                });
                let yData2 = echartData.map((item) => {
                    return item.value2;
                });

                let option = {
                    tooltip: {
                        backgroundColor: "#fff05",
                        trigger: "axis",
                        fontSize: 30,
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                        axisPointer: {
                            type: "shadow",
                            textStyle: {
                                color: "#fff",
                            },
                        },
                    },
                    legend: {
                        textStyle: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        top: 30,
                        icon: "square",
                        itemWidth: 30,
                        itemHeight: 30,
                    },
                    grid: {
                        left: "2%",
                        right: "4%",
                        bottom: "14%",
                        top: "25%",
                        containLabel: true,
                    },
                    calculable: true,
                    xAxis: [
                        {
                            type: "category",
                            offset: 10,
                            axisLine: {
                                lineStyle: {
                                    color: "#19416b",
                                },
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            splitArea: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                color: "rgba(255,255,255,0.7)",
                                fontSize: 30,
                            },
                            data: xData,
                        },
                    ],
                    yAxis: [
                        {
                            type: "value",

                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#19416b",
                                },
                            },
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                color: "rgba(255,255,255,0.5)",
                                fontSize: 30,
                            },
                        },
                    ],
                    series: [
                        {
                            name: "特大隐患",
                            type: "bar",
                            stack: "总量",
                            barMaxWidth: 35,
                            barGap: "10%",
                            itemStyle: {
                                normal: {
                                    color: "#bd352c",
                                },
                            },
                            data: yData,
                        },
                        {
                            name: "重大隐患",
                            type: "bar",
                            stack: "总量",
                            itemStyle: {
                                normal: {
                                    color: "#398912",
                                    barBorderRadius: 0,
                                },
                            },
                            data: yData1,
                        },
                        {
                            name: "一般隐患",
                            type: "bar",
                            stack: "总量",
                            itemStyle: {
                                normal: {
                                    color: "#213ba4",
                                    barBorderRadius: 0,
                                },
                            },
                            data: yData2,
                        },
                    ],
                };
                echarts1.setOption(option);
            },
            getEcharts06(dom, echartData) {
                let echarts2 = echarts.init(document.getElementById(dom));
                let xData = echartData.map((item) => {
                    return item.time;
                });
                let yData = echartData.map((item) => {
                    return item.value;
                });
                let yData1 = echartData.map((item) => {
                    return item.value1;
                });
                let yData2 = echartData.map((item) => {
                    return item.value2;
                });
                var option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                    },
                    grid: {
                        left: "2%",
                        right: "4%",
                        bottom: "14%",
                        top: "25%",
                        containLabel: true,
                    },
                    legend: {
                        data: ["大类", "亚类", "细类"],
                        right: 8,
                        top: 12,
                        textStyle: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        itemWidth: 30,
                        itemHeight: 30,
                        // itemGap: 35
                    },
                    xAxis: {
                        type: "category",
                        data: xData,
                        axisTick: {
                            show: false,
                        },
                        axisLine: {
                            lineStyle: {
                                color: "#203951",
                            },
                        },
                        axisLabel: {
                            interval: 0,
                            offset: 10,
                            textStyle: {
                                color: "#FFFFFF",
                                fontSize: 30,
                            },
                        },
                    },

                    yAxis: {
                        type: "value",

                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: "white",
                            },
                        },
                        splitLine: {
                            show: true,
                            lineStyle: {
                                color: "#203951",
                            },
                        },
                        axisLabel: {
                            textStyle: {
                                fontSize: 30,
                            },
                        },
                    },
                    series: [
                        {
                            name: "大类",
                            type: "bar",
                            barWidth: "15%",
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#fccb05",
                                        },
                                        {
                                            offset: 1,
                                            color: "#f5804d",
                                        },
                                    ]),
                                    barBorderRadius: 12,
                                },
                            },
                            data: yData,
                        },
                        {
                            name: "亚类",
                            type: "bar",
                            barWidth: "15%",
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#8bd46e",
                                        },
                                        {
                                            offset: 1,
                                            color: "#09bcb7",
                                        },
                                    ]),
                                    barBorderRadius: 11,
                                },
                            },
                            data: yData1,
                        },
                        {
                            name: "细类",
                            type: "bar",
                            barWidth: "15%",
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#248ff7",
                                        },
                                        {
                                            offset: 1,
                                            color: "#6851f1",
                                        },
                                    ]),
                                    barBorderRadius: 11,
                                },
                            },
                            data: yData2,
                        },
                    ],
                };
                echarts2.setOption(option);
            },
            getEcharts07(dom, echartData) {
                let echarts2 = echarts.init(document.getElementById(dom));
                var baifenbi = echartData.map((item) => {
                    return item.value;
                });
                var grayBar = echartData.map((item) => {
                    return item.value1;
                });
                var city = echartData.map((item) => {
                    return item.time;
                });
                let option = {
                    color: ["#4CC970"],
                    grid: {
                        left: "15%",
                        right: "15%",
                        // bottom: "50%",
                        top: "-10px",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            show: false,
                        },
                        {
                            show: false,
                        },
                    ],
                    yAxis: {
                        type: "category",
                        axisLabel: {
                            show: true, //让Y轴数据不显示
                            interval: 0,
                            color: "#3AC0FC",
                            fontSize: 12,
                            padding: [5, 5, 5, 5],
                        },
                        itemStyle: {},
                        axisTick: {
                            show: false, //隐藏Y轴刻度
                        },
                        axisLine: {
                            show: false, //隐藏Y轴线段
                        },

                        data: city,
                    },
                    series: [
                        //背景色--------------------我是分割线君------------------------------//
                        {
                            show: true,
                            type: "bar",
                            barGap: "-100%",
                            barWidth: "15px", //统计条宽度
                            itemStyle: {
                                normal: {
                                    barBorderRadius: 5,
                                    color: "rgba(0,0,0,0.2)",
                                },
                            },
                            z: 1,
                            data: grayBar,
                        },
                        //蓝条--------------------我是分割线君------------------------------//
                        {
                            show: true,
                            type: "bar",
                            barGap: "-100%",
                            barWidth: "15px", //统计条宽度
                            max: 1,
                            itemStyle: {
                                normal: {
                                    barBorderRadius: 5,
                                    color: "#4bced0",
                                },
                            },
                            label: {
                                show: true,
                                position: "left",
                                formatter: function (param) {
                                    return param.value * 100 + "%";
                                },
                                textStyle: {
                                    color: "#ffffff",
                                    fontSize: "27",
                                },
                            },
                            labelLine: {
                                show: false,
                            },
                            z: 2,
                            data: baifenbi,
                        },
                    ],
                };
                echarts2.setOption(option);
            },
            getEcharts08(dom, echartData) {
                let echarts2 = echarts.init(document.getElementById(dom));
                let option = {
                    tooltip: {
                        trigger: "item",
                    },
                    legend: {
                        orient: "vertical",
                        right: "right",
                        top: "95",
                        icon: "square",
                        itemWidth: 25,
                        itemHeight: 25,
                        textStyle: {
                            color: "#fff",
                            fontSize: 30,
                        },
                    },
                    series: [
                        {
                            labelLine: {
                                show: false,
                            },
                            label: {
                                show: false,
                            },
                            type: "pie",
                            radius: "60%",
                            center: ["25%", "40%"],
                            data: echartData,
                        },
                    ],
                };
                echarts2.setOption(option);
            },
            getEcharts09(dom, echartData) {
                let echarts2 = echarts.init(document.getElementById(dom));
                let xData = echartData.map((item) => {
                    return item.name;
                });
                let yData = echartData.map((item) => {
                    return item.value;
                });
                let yData1 = echartData.map((item) => {
                    return item.value1;
                });
                let yData2 = echartData.map((item) => {
                    return item.value2;
                });
                let option = {
                    xAxis: {
                        scale: true,
                        data: xData,
                        axisLabel: {
                            interval: 0,
                            color: "rgba(255,255,255,0.7)",
                            fontSize: 30,
                            rotate: 30,
                        },
                        splitLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },
                    },
                    yAxis: {
                        scale: true,

                        splitLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },

                        axisLabel: {
                            color: "rgba(255,255,255,0.7)",
                            fontSize: 30,
                        },
                    },
                    series: [
                        {
                            type: "scatter",
                            data: yData,
                        },
                        {
                            type: "scatter",
                            data: yData1,
                        },
                        {
                            type: "scatter",
                            data: yData2,
                        },
                    ],
                };
                echarts2.setOption(option);
            },
            changeEchartsThree(index) {
                this.isActiveThree = index;
                if (this.isActiveThree === 1) {
                    $api("shgl_aqscsgydtsfx-left011").then((res) => {
                        this.getEcharts11("barEcharts009", res);
                    });
                }
            },
            getEcharts10(dom, echartData) {
                let echarts1 = echarts.init(document.getElementById(dom));
                let xData = echartData.map((item) => {
                    return item.time;
                });
                let yData = echartData.map((item) => {
                    return item.value;
                });
                let yData1 = echartData.map((item) => {
                    return item.value1;
                });
                let yData2 = echartData.map((item) => {
                    return item.value2;
                });

                let option = {
                    tooltip: {
                        backgroundColor: "#fff05",
                        trigger: "axis",
                        fontSize: 30,
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                        axisPointer: {
                            type: "shadow",
                            textStyle: {
                                color: "#fff",
                            },
                        },
                    },
                    legend: {
                        textStyle: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        top: 30,
                        icon: "square",
                        itemWidth: 30,
                        itemHeight: 30,
                    },
                    grid: {
                        left: "2%",
                        right: "4%",
                        bottom: "14%",
                        top: "25%",
                        containLabel: true,
                    },
                    calculable: true,
                    xAxis: [
                        {
                            type: "category",
                            offset: 10,
                            axisLine: {
                                lineStyle: {
                                    color: "#19416b",
                                },
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            splitArea: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                color: "rgba(255,255,255,0.7)",
                                fontSize: 30,
                            },
                            data: xData,
                        },
                    ],
                    yAxis: [
                        {
                            type: "value",

                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#19416b",
                                },
                            },
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                color: "rgba(255,255,255,0.5)",
                                fontSize: 30,
                            },
                        },
                    ],
                    series: [
                        {
                            name: "特大隐患",
                            type: "bar",
                            stack: "总量",
                            barMaxWidth: 35,
                            barGap: "10%",
                            itemStyle: {
                                normal: {
                                    color: "#bd352c",
                                },
                            },
                            data: yData,
                        },
                        {
                            name: "重大隐患",
                            type: "bar",
                            stack: "总量",
                            itemStyle: {
                                normal: {
                                    color: "#398912",
                                    barBorderRadius: 0,
                                },
                            },
                            data: yData1,
                        },
                        {
                            name: "一般隐患",
                            type: "bar",
                            stack: "总量",
                            itemStyle: {
                                normal: {
                                    color: "#213ba4",
                                    barBorderRadius: 0,
                                },
                            },
                            data: yData2,
                        },
                    ],
                };
                echarts1.setOption(option);
            },
            getEcharts11(dom, echartData) {
                let echarts1 = echarts.init(document.getElementById(dom));
                let xData = echartData.map((item) => {
                    return item.time;
                });
                let yData = echartData.map((item) => {
                    return item.value;
                });
                let yData1 = echartData.map((item) => {
                    return item.value1;
                });
                let yData2 = echartData.map((item) => {
                    return item.value2;
                });

                let option = {
                    tooltip: {
                        backgroundColor: "#fff05",
                        trigger: "axis",
                        fontSize: 30,
                        textStyle: {
                            fontSize: 30,
                            color: "#fff",
                        },
                        axisPointer: {
                            type: "shadow",
                            textStyle: {
                                color: "#fff",
                            },
                        },
                    },
                    legend: {
                        textStyle: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        top: 30,
                        icon: "square",
                        itemWidth: 30,
                        itemHeight: 30,
                    },
                    grid: {
                        left: "2%",
                        right: "4%",
                        bottom: "14%",
                        top: "25%",
                        containLabel: true,
                    },
                    calculable: true,
                    xAxis: [
                        {
                            type: "category",
                            offset: 10,
                            axisLine: {
                                lineStyle: {
                                    color: "#19416b",
                                },
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            splitArea: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                color: "rgba(255,255,255,0.7)",
                                fontSize: 30,
                            },
                            data: xData,
                        },
                    ],
                    yAxis: [
                        {
                            type: "value",

                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#19416b",
                                },
                            },
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                color: "rgba(255,255,255,0.5)",
                                fontSize: 30,
                            },
                        },
                    ],
                    series: [
                        {
                            name: "特大隐患",
                            type: "bar",
                            stack: "总量",
                            barMaxWidth: 35,
                            barGap: "10%",
                            itemStyle: {
                                normal: {
                                    color: "#bd352c",
                                },
                            },
                            data: yData,
                        },
                        {
                            name: "重大隐患",
                            type: "bar",
                            stack: "总量",
                            itemStyle: {
                                normal: {
                                    color: "#398912",
                                    barBorderRadius: 0,
                                },
                            },
                            data: yData1,
                        },
                        {
                            name: "一般隐患",
                            type: "bar",
                            stack: "总量",
                            itemStyle: {
                                normal: {
                                    color: "#213ba4",
                                    barBorderRadius: 0,
                                },
                            },
                            data: yData2,
                        },
                    ],
                };
                echarts1.setOption(option);
            },
            getEcharts12(dom, echartData) {
                let echarts1 = echarts.init(document.getElementById(dom));
                //数据
                var XName = echartData.map((item) => {
                    return item.time;
                });
                var data1 = echartData.map((item) => {
                    return item.value;
                });
                var Line = [""];
                var img = [
                    "image://data:image/png;base64,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",
                ];
                var color = ["#dcf776"];

                //数据处理
                var datas = [];
                Line.map((item, index) => {
                    datas.push({
                        symbolSize: 150,
                        symbol: img[index],
                        name: item,
                        type: "line",
                        yAxisIndex: 1,
                        data: data1,
                        itemStyle: {
                            normal: {
                                borderWidth: 5,
                                color: color[index],
                            },
                        },
                    });
                });

                let option = {
                    grid: {
                        left: "2%",
                        right: "4%",
                        // bottom: "14%",
                        top: "10%",
                        containLabel: true,
                        // left: "5%",
                        // top: "5%",
                        // bottom: "5%",
                        // right: "5%",
                    },

                    yAxis: [
                        {
                            type: "value",
                            position: "right",
                            splitLine: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                show: false,
                            },
                        },
                        {
                            type: "value",
                            position: "left",
                            nameTextStyle: {
                                color: "#00FFFF",
                            },

                            splitLine: {
                                lineStyle: {
                                    type: "dashed",
                                    color: "rgba(135,140,147,0.8)",
                                },
                            },
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                formatter: "{value}",
                                color: "#fff",
                                fontSize: 30,
                            },
                        },
                    ],
                    xAxis: [
                        {
                            type: "category",
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: "#6A989E",
                                },
                            },
                            offset: 10,
                            axisLabel: {
                                inside: false,

                                textStyle: {
                                    color: "#fff", // x轴颜色
                                    fontWeight: "normal",
                                    fontSize: "30",
                                    lineHeight: 22,
                                },
                            },
                            data: XName,
                        },
                        // {
                        //     type: "category",
                        //     axisLine: {
                        //         show: false,
                        //     },
                        //     axisTick: {
                        //         show: false,
                        //     },
                        //     axisLabel: {
                        //         show: false,
                        //     },
                        //     splitArea: {
                        //         show: false,
                        //     },
                        //     splitLine: {
                        //         show: false,
                        //     },
                        //     //-----
                        //     data: ["1月", "2月", "3月", "4月", "5月", "6月"],
                        // },
                    ],
                    series: datas,
                };
                echarts1.setOption(option);
            },
            getEcharts13(dom, echartData) {
                let echarts1 = echarts.init(document.getElementById(dom));
                //数据
                var XName = echartData.map((item) => {
                    return item.time;
                });
                var data1 = echartData.map((item) => {
                    return item.value;
                });
                var Line = [""];
                var img = [
                    "image://data:image/png;base64,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",
                ];
                var color = ["#dcf776"];

                //数据处理
                var datas = [];
                Line.map((item, index) => {
                    datas.push({
                        symbolSize: 150,
                        symbol: img[index],
                        name: item,
                        type: "line",
                        yAxisIndex: 1,
                        data: data1,
                        itemStyle: {
                            normal: {
                                borderWidth: 5,
                                color: color[index],
                            },
                        },
                    });
                });

                let option = {
                    grid: {
                        left: "2%",
                        right: "4%",
                        // bottom: "14%",
                        top: "10%",
                        containLabel: true,
                        // left: "5%",
                        // top: "5%",
                        // bottom: "5%",
                        // right: "5%",
                    },

                    yAxis: [
                        {
                            type: "value",
                            position: "right",
                            splitLine: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                show: false,
                            },
                        },
                        {
                            type: "value",
                            position: "left",
                            nameTextStyle: {
                                color: "#00FFFF",
                            },

                            splitLine: {
                                lineStyle: {
                                    type: "dashed",
                                    color: "rgba(135,140,147,0.8)",
                                },
                            },
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                formatter: "{value}",
                                color: "#fff",
                                fontSize: 30,
                            },
                        },
                    ],
                    xAxis: [
                        {
                            type: "category",
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: "#6A989E",
                                },
                            },
                            offset: 10,
                            axisLabel: {
                                inside: false,

                                textStyle: {
                                    color: "#fff", // x轴颜色
                                    fontWeight: "normal",
                                    fontSize: "30",
                                    lineHeight: 22,
                                },
                            },
                            data: XName,
                        },
                        // {
                        //     type: "category",
                        //     axisLine: {
                        //         show: false,
                        //     },
                        //     axisTick: {
                        //         show: false,
                        //     },
                        //     axisLabel: {
                        //         show: false,
                        //     },
                        //     splitArea: {
                        //         show: false,
                        //     },
                        //     splitLine: {
                        //         show: false,
                        //     },
                        //     //-----
                        //     data: ["1月", "2月", "3月", "4月", "5月", "6月"],
                        // },
                    ],
                    series: datas,
                };
                echarts1.setOption(option);
            },
            initMap() {
                top.document.getElementById("map").contentWindow.Work.change3D(7);
                this.flyTo();
                // this.addPoint()
            },
            //飞入
            flyTo() {
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "flyto", //功能名称
                        flyData: {
                            center: [119.95478050597587, 29.01613226366889],
                            zoom: 10.5,
                            pitch: 40,
                            bearing: 0,
                        },
                    })
                );
            },

            addPoint() {
                let res = [
                    {
                        title: "浦江县",
                        gps_x: "119.94315399169922",
                        gps_y: "29.5630503845215",
                    },
                    {
                        title: "兰溪市",
                        gps_x: "119.46214447021484",
                        gps_y: "29.31345558166504",
                    },
                    {
                        title: "婺城区",
                        gps_x: "119.5569204711914",
                        gps_y: "29.00677101135254",
                    },
                    {
                        title: "金义新区",
                        gps_x: "119.8483056640625",
                        gps_y: "29.188559951782227",
                    },
                    {
                        title: "义乌市",
                        gps_x: "120.08206787109375",
                        gps_y: "29.322123641967773",
                    },
                    {
                        title: "武义县",
                        gps_x: "119.7269204711914",
                        gps_y: "28.79677101135254",
                    },
                    {
                        title: "永康市",
                        gps_x: "120.1469204711914",
                        gps_y: "28.97677101135254",
                    },
                    {
                        title: "东阳市",
                        gps_x: "120.4169204711914",
                        gps_y: "29.24677101135254",
                    },
                    {
                        title: "磐安县",
                        gps_x: "120.6299204711914",
                        gps_y: "29.06677101135254",
                    },
                    {
                        title: "婺城区",
                        gps_x: "119.64896993689013",
                        gps_y: "29.090742350540673",
                    },
                    {
                        title: "婺城区",
                        gps_x: "119.65305962805735",
                        gps_y: "29.07810148693506",
                    },
                ];
                let arr = res.map((item) => {
                    return {
                        data: {},
                        point: item.gps_x + "," + item.gps_y,
                    };
                });
                console.log(arr);
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "pointLoad",
                        pointType: "digital-green", // 点位类型（图标名称）
                        pointId: "point1", // 点位唯一id
                        setClick: true,
                        pointData: arr,
                        imageConfig: { iconSize: 1 },
                        popup: {
                            offset: [50, -100],
                        },
                    })
                );
            },

            //清除点位
            rmPoint() {
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "rmPoint",
                        pointId: "",
                    })
                );
            },
        },
        destroyed() {
            this.rmPoint();
        },
    });
</script>
