<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>数字政府指标分析-左</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <style scoped>
      #szzf-left {
        width: 1050px;
        height: 1930px;
        background: url('/img/left-bg.png') no-repeat;
        background-size: 100% 100%;
      }

      .content {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
      }

      .content-box {
        width: 100%;
        height: 925px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
      }

      .content-box-line {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        margin-top: 40px;
      }

      .titleContent {
        font-size: 50px !important;
      }

      .select {
        position: absolute;
        z-index: 2;
        top: -15px;
        right: 20px;
      }

      .el-input__inner {
        font-size: 30px;
        width: 130px;
        height: 50px;
        line-height: 50px;
        color: #fff;
        background-color: #011040b3;
      }

      .el-select-dropdown__item.hover,
      .el-select-dropdown__item:hover {
        background-color: #011040b3;
      }

      .el-input__icon {
        line-height: 48px;
      }

      .el-select-dropdown {
        background-color: #011040b3;
      }

      .el-select-dropdown__item {
        font-size: 30px;
        color: #fff;
      }

      .el-select .el-input .el-select__caret {
        font-size: 30px;
      }

      .blue {
        background: linear-gradient(180deg, #e5ffff, #ffffff, #00c0ff, #caffff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .num {
        font-size: 40px;
        margin-top: 25px;
        font-weight: 600;
      }
      .text {
        font-size: 35px;
        margin-top: 5px;
        color: #fff;
      }
      .box1-dataBox-left {
        width: 300px;
        height: 160px;
        text-align: center;
        background: url('/static/citybrain3840/shgl/img/itembg.png') no-repeat;
        background-size: cover;
        border-radius: 20px;
      }
    </style>
  </head>

  <body>
    <div id="szzf-left">
      <div class="content">
        <div class="content-box">
          <nav class="nav0">
            <s-header-title
              style="width: 100%"
              title="省级层面重大任务应用建设进展情况"
              htype="2"
            ></s-header-title>
          </nav>
          <div class="content-box-line">
            <div id="charts1" style="width: 600px; height: 300px"></div>
            <div class="box1-dataBox-left">
              <div class="num blue">{{taskNum}}个</div>
              <div class="text">任务总数</div>
            </div>
          </div>
          <div
            id="charts2"
            style="width: 950px; height: 400px; margin-top: 40px"
          ></div>
        </div>
        <div class="content-box">
          <nav class="nav0">
            <s-header-title
              style="width: 100%"
              title="数字政府多跨场景建设推进情况"
              htype="2"
            ></s-header-title>
          </nav>
          <div style="font-size: 30px; color: #fff">全省排名：{{pm}}</div>
          <div class="content-box-line">
            <div id="charts3" style="width: 480px; height: 300px"></div>
            <div id="charts4" style="width: 800px; height: 300px"></div>
          </div>
          <div
            id="charts5"
            style="width: 1000px; height: 400px; margin-top: 40px"
          ></div>
        </div>
      </div>
    </div>
  </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
  var vm = new Vue({
    el: '#szzf-left',
    data: {
      taskNum: '',
      value: 7,
      pm: '第2名',
      charts1Data: [],
      charts2Data: [],
      charts3Data: [],
      charts4Data: [],
      charts5Data: [],
      options: [
        {
          label: '7月',
          value: 7,
        },
      ],
    },
    mounted() {
      this.initAllCharts()
      this.getTaskNum()

      top.document.getElementById('map').contentWindow.Work.funChange(
        JSON.stringify({
          funcName: 'rmPoint', //功能名称
        })
      )
      this.initIframe()
      this.initMap()
    },
    methods: {
      initIframe() {
        let iframe1 = {
          type: 'openIframe',
          name: 'index-middle-top',
          src: '/static/citybrain3840/szhgg/pages/szzfzbfx/index-middle-top.html',
          width: '930px',
          height: '170px',
          left: '1590px',
          top: '230px',
          zIndex: '10',
        }
        let iframe2 = {
          type: 'openIframe',
          name: 'index-middle-bottom',
          src: '/static/citybrain3840/szhgg/pages/szzfzbfx/index-middle-bottom.html',
          width: '1400px',
          height: '700px',
          left: '1212px',
          top: '1430px',
          zIndex: '10',
        }
        let iframe4 = {
          type: 'openIframe',
          name: 'list',
          src: '/static/citybrain3840/szhgg/commont/list3.html',
          width: '450px',
          height: '244px',
          left: '1090px',
          top: '230px',
          zIndex: '10',
        }
        window.parent.postMessage(JSON.stringify(iframe1), '*')
        window.parent.postMessage(JSON.stringify(iframe2), '*')
        window.parent.postMessage(JSON.stringify(iframe4), '*')
      },
      initMap() {
        top.document.getElementById('map').contentWindow.Work.change3D(9)
        top.document.getElementById('map').contentWindow.Work.funChange(
          JSON.stringify({
            funcName: 'flyto', //功能名称
            flyData: {
              center: [119.98478050597587, 29.18613226366889],
              zoom: 9,
              pitch: 28,
              bearing: 0,
              duration: 4000, //飞行时间（建议加上）
            },
          })
        )
        $get('/textCity.json').then((res) => {
          let textData = []
          top.document.getElementById('map').contentWindow.Work.funChange(
            JSON.stringify({
              funcName: '3Dtext', //功能名称
              textData: res,
              textSize: 35,
            })
          )
        })
      },
      getTaskNum() {
        $get('/3840/szzfzbfx/leftTask.json', { type: 'leftTask' }).then(
          (res) => {
            this.taskNum = res[0].task
          }
        )
        // $api("ldst_szzfzbfx_left", { type: "leftTask" }).then((res) => {
        //     this.taskNum = res[0].task;
        // });
      },
      initAllCharts() {
        this.initTopCharts1()
        this.initTopCharts2()
        this.initBottomCharts1()
        this.initBottomCharts2()
        this.initBottomCharts3()
      },
      initTopCharts1() {
        $get('/3840/szzfzbfx/left1.json', { type: 1 }).then((res) => {
          // $api("ldst_szzfzbfx_left", { type: 1 }).then((res) => {
          this.charts1Data = res
          let that = this
          let myChart = echarts.init(document.getElementById('charts1'))
          let option = {
            color: [
              '#00C0FF',
              '#22E8E8',
              '#FFD461',
              '#A9DB52',
              '#B76FD8',
              '#FD852E',
              '#FF4949',
              '#0594C3',
              '#009D9D',
              '#A47905',
            ],
            tooltip: {
              trigger: 'item',
              // formatter: '{b}: <br/> {d}%',
              formatter: '{b}: <br/> {c}个<br/> {d}%',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '25',
              },
            },
            legend: {
              orient: 'vertical',
              left: '48%',
              top: '4%',
              bottom: '0%',
              icon: 'circle',
              itemGap: 10,
              textStyle: {
                rich: {
                  name: {
                    fontSize: 25,
                    color: '#ffffff',
                    padding: [0, 20, 0, 15],
                  },
                  value: {
                    fontSize: 25,
                    color: '#2CC6F9',
                    // padding: [10, 0, 0, 15]
                  },
                },
              },
              formatter: function (name) {
                var data = option.series[0].data //获取series中的data
                var total = 0
                var tarValue
                for (var i = 0, l = data.length; i < l; i++) {
                  total += Number(data[i].value)
                  if (data[i].name == name) {
                    tarValue = data[i].value
                  }
                }
                that.serverNum = total
                var p = ((tarValue / total) * 100).toFixed(2)
                return '{name|' + name + '}{value|' + p + '%}'
              },
            },
            series: [
              {
                name: '',
                type: 'pie',
                radius: ['50%', '70%'],
                center: ['25%', '45%'],
                roseType: '',
                itemStyle: {
                  borderRadius: 0,
                },
                label: {
                  show: false,
                },
                data: this.charts1Data,
              },
            ],
          }
          myChart.setOption(option)
          myChart.getZr().on('mousemove', (param) => {
            myChart.getZr().setCursorStyle('default')
          })
        })
      },
      initTopCharts2() {
        $get('/3840/szzfzbfx/left2.json', { type: 2 }).then((res) => {
          // $api("ldst_szzfzbfx_left", { type: 2 }).then((res) => {
          this.charts2Data = res
          let myChart = echarts.init(document.getElementById('charts2'))
          let option = {
            tooltip: {
              trigger: 'axis',
              borderWidth: 0,
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
              },
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '28',
              },
            },
            legend: {
              orient: 'horizontal',
              // icon: "circle",
              itemGap: 45,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
            grid: {
              left: '3%',
              right: '6%',
              top: '22%',
              bottom: '1%',
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                data: this.charts2Data.map((item) => item.name),
                axisLine: {
                  lineStyle: {
                    color: 'rgb(119,179,241,.4)', // 颜色
                    width: 1, // 粗细
                  },
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  interval: 0,
                  textStyle: {
                    color: '#D6E7F9',
                    fontSize: 28,
                  },
                },
              },
            ],
            yAxis: [
              {
                name: '单位：起',
                type: 'value',
                nameTextStyle: {
                  fontSize: 24,
                  color: '#D6E7F9',
                  padding: 5,
                },
                splitLine: {
                  lineStyle: {
                    color: 'rgb(119,179,241,.4)',
                  },
                },
                axisLabel: {
                  textStyle: {
                    fontSize: 28,
                    color: '#D6E7F9',
                  },
                },
              },
              {
                name: '单位: %',
                type: 'value',
                max: 100,
                nameTextStyle: {
                  fontSize: 24,
                  color: '#D6E7F9',
                  padding: 5,
                },
                splitLine: {
                  lineStyle: {
                    color: 'rgb(119,179,241,.4)',
                  },
                },
                axisLabel: {
                  formatter: '{value}%',
                  textStyle: {
                    fontSize: 28,
                    color: '#D6E7F9',
                  },
                },
              },
            ],
            series: [
              {
                name: '任务完成量',
                type: 'bar',
                barWidth: '20%',
                yAxisIndex: 0,
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: '#00C0FF',
                      },
                      {
                        offset: 1,
                        color: 'rgba(0,192,255,0)',
                      },
                    ]),
                    barBorderRadius: 4,
                  },
                },
                data: this.charts2Data.map((item) => item.value1),
              },
              {
                name: '完成率',
                type: 'line',
                smooth: 'true',
                symbolSize: 0,
                lineStyle: {
                  width: 3,
                  color: '#22E8E8',
                },
                yAxisIndex: 1,
                data: this.charts2Data.map((item) => item.value2),
              },
            ],
          }
          myChart.setOption(option)
          myChart.getZr().on('mousemove', (param) => {
            myChart.getZr().setCursorStyle('default')
          })
        })
      },
      initBottomCharts1() {
        $get('/3840/szzfzbfx/left3.json', { type: 3 }).then((res) => {
          // $api("ldst_szzfzbfx_left", { type: 3 }).then((res) => {
          this.charts3Data = res
          let myChart = echarts.init(document.getElementById('charts3'))
          let option = {
            tooltip: {
              trigger: 'axis',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '28',
              },
            },
            legend: {
              selectedMode: false,
              data: ['人'],
              right: '4%',
              textStyle: {
                fontSize: 16,
                color: '#fff',
              },
            },
            grid: {
              top: '0%',
              left: '5%',
              right: '5%',
              bottom: '0',
              containLabel: true,
            },
            xAxis: {
              type: 'value',
              show: false,
            },
            yAxis: {
              name: '',
              type: 'category',
              triggerEvent: false,
              inverse: true,
              axisLine: {
                show: false,
              },
              axisLabel: {
                textStyle: {
                  color: '#fff',
                  fontSize: 28,
                },
              },
              axisTick: {
                show: false,
                length: 10,
              },
              data: this.charts3Data.map((item) => item.name),
            },
            series: [
              {
                // cursor:"auto",
                type: 'bar',
                name: '',
                showBackground: true,
                itemStyle: {
                  barBorderRadius: [0, 10, 10, 0],
                  color: function (params) {
                    var colors = [
                      '#4587E7',
                      '#35AB33',
                      '#F5AD1D',
                      '#ff7f50',
                      '#da70d6',
                      '#32cd32',
                      '#6495ed',
                    ]
                    // 通过返回值的下标一一对应将颜色赋给柱子上，return出去什么颜色就是什么颜色，这里可以写判断
                    if (params.dataIndex == 0) {
                      return new echarts.graphic.LinearGradient(
                        1,
                        0,
                        0,
                        0,
                        [
                          {
                            offset: 0,
                            color: '#FF9434', //指0%处的颜色
                          },
                          {
                            offset: 1,
                            color: '#F90808', //指100%处的颜色
                          },
                        ],
                        false
                      )
                    } else if (params.dataIndex == 1) {
                      return new echarts.graphic.LinearGradient(
                        1,
                        0,
                        0,
                        0,
                        [
                          {
                            offset: 0,
                            color: '#FFF220', //指0%处的颜色
                          },
                          {
                            offset: 1,
                            color: '#F98508', //指100%处的颜色
                          },
                        ],
                        false
                      )
                    } else if (params.dataIndex == 2) {
                      return new echarts.graphic.LinearGradient(
                        1,
                        0,
                        0,
                        0,
                        [
                          {
                            offset: 0,
                            color: '#2DF09F', //指0%处的颜色
                          },
                          {
                            offset: 1,
                            color: '#0EB1E5', //指100%处的颜色
                          },
                        ],
                        false
                      )
                    } else {
                      return new echarts.graphic.LinearGradient(
                        1,
                        0,
                        0,
                        0,
                        [
                          {
                            offset: 0,
                            color: '#2BDAFF', //指0%处的颜色
                          },
                          {
                            offset: 1,
                            color: '#078FF7', //指100%处的颜色
                          },
                        ],
                        false
                      )
                    }
                    // return colors[params.dataIndex];
                  },
                },
                label: {
                  show: true,
                  position: [540, -2],
                  color: '#fff',
                  formatter: function (params) {
                    return params.value + ' 个'
                  },
                  fontSize: 28,
                },
                barWidth: 20,
                color: '#539FF7',
                data: this.charts3Data.map((item) => item.value),
              },
            ],
          }
          myChart.setOption(option)
          myChart.getZr().on('mousemove', (param) => {
            myChart.getZr().setCursorStyle('default')
          })
        })
      },
      initBottomCharts2() {
        $get('/3840/szzfzbfx/left4.json', { type: 4 }).then((res) => {
          // $api("ldst_szzfzbfx_left", { type: 4 }).then((res) => {
          this.charts4Data = res
          let that = this
          let myChart = echarts.init(document.getElementById('charts4'))
          let option = {
            color: [
              '#00C0FF',
              '#22E8E8',
              '#FFD461',
              '#A9DB52',
              '#B76FD8',
              '#FD852E',
              '#FF4949',
              '#0594C3',
              '#009D9D',
              '#A47905',
            ],
            tooltip: {
              trigger: 'item',
              // formatter: '{b}: <br/> {d}%',
              formatter: '{b}: <br/> {c}个<br/> {d}%',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '25',
              },
            },
            legend: {
              orient: 'vertical',
              left: '44%',
              top: '18%',
              bottom: '0%',
              icon: 'circle',
              itemGap: 40,
              textStyle: {
                rich: {
                  name: {
                    fontSize: 25,
                    color: '#ffffff',
                    padding: [0, 20, 0, 15],
                  },
                  value: {
                    fontSize: 25,
                    color: '#2CC6F9',
                    // padding: [10, 0, 0, 15]
                  },
                },
              },
              formatter: function (name) {
                var data = option.series[0].data //获取series中的data
                var total = 0
                var tarValue
                for (var i = 0, l = data.length; i < l; i++) {
                  total += Number(data[i].value)
                  if (data[i].name == name) {
                    tarValue = data[i].value
                  }
                }
                that.serverNum = total
                var p = ((tarValue / total) * 100).toFixed(2)
                return '{name|' + name + '}{value|' + p + '%}'
              },
            },
            series: [
              {
                name: '',
                type: 'pie',
                radius: ['50%', '70%'],
                center: ['25%', '45%'],
                roseType: '',
                itemStyle: {
                  borderRadius: 0,
                },
                label: {
                  show: false,
                },
                data: this.charts4Data,
              },
            ],
          }
          myChart.setOption(option)
          myChart.getZr().on('mousemove', (param) => {
            myChart.getZr().setCursorStyle('default')
          })
        })
      },
      initBottomCharts3() {
        $get('/3840/szzfzbfx/left5.json', { type: 5 }).then((res) => {
          // $api("ldst_szzfzbfx_left", { type: 5 }).then((res) => {
          this.charts5Data = res
          let myChart = echarts.init(document.getElementById('charts5'))
          let option = {
            tooltip: {
              trigger: 'axis',
              borderWidth: 0,
              axisPointer: {
                // 坐标轴指示器，坐标轴触发有效
                type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
              },
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '28',
              },
            },
            legend: {
              orient: 'horizontal',
              // icon: "circle",
              itemGap: 45,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
            grid: {
              left: '2%',
              right: '2%',
              top: '22%',
              bottom: '1%',
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                data: this.charts5Data.map((item) => item.name),
                axisLine: {
                  lineStyle: {
                    color: 'rgb(119,179,241,.4)', // 颜色
                    width: 1, // 粗细
                  },
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  interval: 0,
                  textStyle: {
                    color: '#D6E7F9',
                    fontSize: 28,
                  },
                },
              },
            ],
            yAxis: [
              {
                name: '单位：个',
                type: 'value',
                nameTextStyle: {
                  fontSize: 24,
                  color: '#D6E7F9',
                  padding: 5,
                },
                splitLine: {
                  lineStyle: {
                    color: 'rgb(119,179,241,.4)',
                  },
                },
                axisLabel: {
                  textStyle: {
                    fontSize: 28,
                    color: '#D6E7F9',
                  },
                },
              },
            ],
            series: [
              {
                name: '全省最佳应用',
                type: 'bar',
                barWidth: '20%',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: '#00C0FF',
                      },
                      {
                        offset: 1,
                        color: 'rgba(0,192,255,0)',
                      },
                    ]),
                    barBorderRadius: 4,
                  },
                },
                data: this.charts5Data.map((item) => item.value1),
              },
              {
                name: '全市最佳应用',
                type: 'bar',
                barWidth: '20%',
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: '#22E8E8',
                      },
                      {
                        offset: 1,
                        color: 'rgba(0,192,255,0)',
                      },
                    ]),
                    barBorderRadius: 4,
                  },
                },
                data: this.charts5Data.map((item) => item.value2),
              },
              {
                name: '理论和制度成果',
                type: 'line',
                smooth: 'true',
                symbolSize: 0,
                lineStyle: {
                  width: 3,
                  color: '#22E8E8',
                },
                data: this.charts5Data.map((item) => item.value2),
              },
            ],
          }
          myChart.setOption(option)
          myChart.getZr().on('mousemove', (param) => {
            myChart.getZr().setCursorStyle('default')
          })
        })
      },
    },
  })
</script>
