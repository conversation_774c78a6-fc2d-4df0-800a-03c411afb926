<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>安全隐患区域分布统计-右侧</title>
        <script src="/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <!-- 轮播toolTip -->
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain/shgl/css/aqyhqyfx-right.css" />
        <link rel="stylesheet" href="/static/css/animate.css" />
        <style>
            .el-input__inner {
                font-size: 28px;
            }
            .el-select-dropdown__item {
                font-size: 26px;
            }
        </style>
    </head>

    <body>
        <div id="app" class="container" v-cloak>
            <nav>
                <s-header-title htype="1" title="重大危险源安全隐患综合统计展示" :data-time="nowTime"></s-header-title>
            </nav>
            <div class="zdwxy">
                <div class="zdwxy-part">
                    <nav>
                        <s-header-title-2 htype="0" title="危险源展示"></s-header-title-2>
                    </nav>
                    <div class="zdwxy-part-con">
                        <div class="info-text" v-for="(item,index) in wxyzsList" :key="index">
                            <p class="col-blue s-font-30">{{item.name}}</p>
                            <p class="s-c-white s-font-30">
                                <span>{{item.value}}</span>
                                &nbsp;
                                <span v-show="item.sjfd!=0">{{item.sjfd<0 ? -item.sjfd : item.sjfd}}%</span>
                                <span class="info-text-up" v-show="item.sjfd>0">↑</span>
                                <span class="info-text-down" v-show="item.sjfd<0">↓</span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="zdwxy-part">
                    <nav>
                        <s-header-title-2 htype="0" title="近五年分级趋势"></s-header-title-2>
                    </nav>
                    <div class="zdwxy-part-con">
                        <div id="chart01" style="width: 100%; height: 100%"></div>
                    </div>
                </div>
                <div class="zdwxy-part">
                    <nav>
                        <s-header-title-2 htype="0" title="企业报警数TOP5"></s-header-title-2>
                    </nav>
                    <div class="zdwxy-part-con">
                        <div id="chart02" style="width: 100%; height: 100%"></div>
                    </div>
                </div>
                <div class="zdwxy-part">
                    <nav>
                        <s-header-title-2 htype="0" title="预警系统监测"></s-header-title-2>
                    </nav>
                    <div class="zdwxy-part-con">
                        <div id="chart03" style="width: 100%; height: 100%"></div>
                    </div>
                </div>
                <div class="zdwxy-part">
                    <nav>
                        <s-header-title-2 htype="0" title="地市危险源分布"></s-header-title-2>
                    </nav>
                    <div class="zdwxy-part-con">
                        <div id="chart04" style="width: 100%; height: 100%"></div>
                    </div>
                </div>
                <div class="zdwxy-part">
                    <nav>
                        <s-header-title-2 htype="0" title="危险源报警数"></s-header-title-2>
                    </nav>
                    <div class="zdwxy-part-con">
                        <li class="process" v-for="(item,index) in processList" :key="index">
                            <div class="s-c-bul-light s-font-28 s-m-t-10">
                                <span style="display: inline-block">{{item.name}}</span>
                                <span style="margin-left: 300px">{{item.rate}}</span>
                            </div>
                            <el-progress
                                :text-inside="true"
                                :stroke-width="26"
                                :percentage="Number(item.rate)*2"
                                :color="item.color"
                            ></el-progress>
                        </li>
                    </div>
                </div>
                <div class="zdwxy-part">
                    <nav>
                        <s-header-title-2 htype="0" title="地市危险源报警数"></s-header-title-2>
                    </nav>
                    <div class="zdwxy-part-con">
                        <div style="text-align: right; width: 100%">
                            <el-select
                                style="width: 160px"
                                v-model="value"
                                placeholder="请选择"
                                @change="handleMonthChange"
                            >
                                <el-option
                                    v-for="(item,index) in options1"
                                    :key="item.id"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </div>
                        <div class="table table1">
                            <div class="th">
                                <div class="th_td" v-for="(item,index) in theadList1" :key="index">{{item}}</div>
                            </div>
                            <div
                                class="tbody"
                                id="tbody1"
                                @mouseover="mouseenterEvent1()"
                                @mouseleave="mouseleaveEvent1()"
                            >
                                <div class="tr" v-for="(item ,i) in tbodyList1" :key="i">
                                    <div class="tr_td" style="flex: 0.33">{{item.qy}}</div>
                                    <div class="tr_td" style="flex: 0.33">{{item.dj}}</div>
                                    <div class="tr_td" style="flex: 0.33">{{item.czqk}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="zdwxy-part">
                    <nav>
                        <s-header-title-2 htype="0" title="地市危险源报警数"></s-header-title-2>
                    </nav>
                    <div class="zdwxy-part-con">
                        <div style="width: 100%; text-align: right">
                            <el-select v-model="selectOption1" style="width: 50%" @change="handleOption1">
                                <el-option
                                    v-for="item,index in options"
                                    :key="index+'a'"
                                    :label="item"
                                    :value="item"
                                ></el-option>
                            </el-select>
                        </div>
                        <div id="chart05" style="width: 100%; height: 100%"></div>
                    </div>
                </div>
                <div class="zdwxy-part">
                    <nav>
                        <s-header-title-2 htype="0" title="危险源风险评估"></s-header-title-2>
                    </nav>
                    <div class="zdwxy-part-con">
                        <div class="table table2">
                            <div class="th">
                                <div class="th_td" style="flex: 0.5" v-for="(item,index) in theadList2" :key="index">
                                    {{item}}
                                </div>
                            </div>
                            <div
                                class="tbody"
                                id="tbody2"
                                @mouseover="mouseenterEvent2()"
                                @mouseleave="mouseleaveEvent2()"
                            >
                                <div class="tr" v-for="(item ,i) in tbodyList2" :key="i">
                                    <div class="tr_td" style="flex: 0.5">{{item.wxy}}</div>
                                    <div class="tr_td" style="flex: 0.5">{{item.fxpgz}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="zdwxy-part">
                    <nav>
                        <s-header-title-2 htype="0" title="危险源实时监控"></s-header-title-2>
                    </nav>
                    <div class="zdwxy-part-con">
                        <el-carousel style="width: 100%; height: 100%; overflow: hidden; position: relative">
                            <el-carousel-item v-for="(item,index) in 4" :key="index">
                                <img
                                    src="/static/citybrain/shgl/img/u1219.jpg"
                                    alt=""
                                    style="width: 100%; height: 100%"
                                    @click="openVideo"
                                />
                                <div style="position: absolute; top: 0; left: 200px">市政府南门</div>
                            </el-carousel-item>
                        </el-carousel>
                    </div>
                </div>
            </div>
            <nav>
                <s-header-title htype="1" title="非煤矿山安全隐患综合统计展示" :data-time="nowTime"></s-header-title>
            </nav>
            <div class="fmks">
                <div class="fmks-part">
                    <nav>
                        <s-header-title-2 htype="0" title="非煤矿山监管展示"></s-header-title-2>
                    </nav>
                    <div class="fmks-part-con">
                        <div class="info-text" v-for="(item,index) in jgzsList" :key="index">
                            <p class="col-blue s-font-30">{{item.name}}</p>
                            <p class="s-c-white s-font-30">
                                <span>{{item.value}}</span>
                                &nbsp;
                                <span v-show="item.sjfd!=0">{{item.sjfd<0 ? -item.sjfd : item.sjfd}}%</span>
                                <span class="info-text-up" v-show="item.sjfd>0">↑</span>
                                <span class="info-text-down" v-show="item.sjfd<0">↓</span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="fmks-part">
                    <nav>
                        <s-header-title-2 htype="0" title="监测预警"></s-header-title-2>
                    </nav>
                    <div class="fmks-part-con">
                        <div class="info-text" v-for="(item,index) in jcyjList" :key="index">
                            <p class="col-blue s-font-30">{{item.name}}</p>
                            <p class="s-c-white s-font-30">
                                <span>{{item.value}}</span>
                                &nbsp;
                                <span v-show="item.sjfd!=0">{{item.sjfd<0 ? -item.sjfd : item.sjfd}}%</span>
                                <span class="info-text-up" v-show="item.sjfd>0">↑</span>
                                <span class="info-text-down" v-show="item.sjfd<0">↓</span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="fmks-part">
                    <nav>
                        <s-header-title-2 htype="0" title="预警分析"></s-header-title-2>
                    </nav>
                    <div class="fmks-part-con">
                        <div class="info-text" v-for="(item,index) in yjfxList" :key="index">
                            <p class="col-blue s-font-30">{{item.name}}</p>
                            <p class="s-c-white s-font-30">
                                <span>{{item.value}}</span>
                                &nbsp;
                                <span v-show="item.sjfd!=0">{{item.sjfd<0 ? -item.sjfd : item.sjfd}}%</span>
                                <span class="info-text-up" v-show="item.sjfd>0">↑</span>
                                <span class="info-text-down" v-show="item.sjfd<0">↓</span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="fmks-part">
                    <nav>
                        <s-header-title-2 htype="0" title="系统监测"></s-header-title-2>
                    </nav>
                    <div class="fmks-part-con">
                        <div style="width: 100%; text-align: right">
                            <el-select
                                v-model="selectOption3"
                                placeholder=""
                                @change="handleOption2"
                                style="width: 50%"
                            >
                                <el-option
                                    v-for="(item,index) in options2"
                                    :key="index+'b'"
                                    :label="item"
                                    :value="item"
                                ></el-option>
                            </el-select>
                        </div>
                        <div class="info-text" v-for="(item,index) in xtjcList" :key="index" style="width: 50%">
                            <p class="col-blue s-font-30">{{item.name}}</p>
                            <p class="s-c-white s-font-30">
                                <span>{{item.value}}</span>
                                &nbsp;
                                <span v-show="item.sjfd!=0">{{item.sjfd<0 ? -item.sjfd : item.sjfd}}%</span>
                                <span class="info-text-up" v-show="item.sjfd>0">↑</span>
                                <span class="info-text-down" v-show="item.sjfd<0">↓</span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="fmks-part">
                    <nav>
                        <s-header-title-2 htype="0" title="人员安全"></s-header-title-2>
                    </nav>
                    <div class="fmks-part-con">
                        <div class="info-text" v-for="(item,index) in ryaqList" :key="index" style="width: 50%">
                            <p class="col-blue s-font-30">{{item.name}}</p>
                            <p class="s-c-white s-font-30">
                                <span>{{item.value}}</span>
                                &nbsp;
                                <span v-show="item.sjfd!=0">{{item.sjfd<0 ? -item.sjfd : item.sjfd}}%</span>
                                <span class="info-text-up" v-show="item.sjfd>0">↑</span>
                                <span class="info-text-down" v-show="item.sjfd<0">↓</span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="fmks-part">
                    <nav>
                        <s-header-title-2 htype="0" title="告警统计"></s-header-title-2>
                    </nav>
                    <div class="fmks-part-con" style="padding: 70px 0; box-sizing: border-box">
                        <div class="tabs">
                            <div
                                class="tab_item"
                                :class="tabIndex1===index?'tab_active':''"
                                v-for="(item,index) in tabList1"
                                @click="change1(index)"
                            >
                                {{item}}
                            </div>
                        </div>
                        <div class="info-text" v-for="(item,index) in gjtjList" :key="index" style="width: 50%">
                            <p class="col-blue s-font-30">{{item.name}}</p>
                            <p class="s-c-white s-font-30">
                                <span>{{item.value}}</span>
                                &nbsp;
                                <span v-show="item.sjfd!=0">{{item.sjfd<0 ? -item.sjfd : item.sjfd}}%</span>
                                <span class="info-text-up" v-show="item.sjfd>0">↑</span>
                                <span class="info-text-down" v-show="item.sjfd<0">↓</span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="fmks-part">
                    <nav>
                        <s-header-title-2 htype="0" title="风险概况"></s-header-title-2>
                    </nav>
                    <div class="fmks-part-con">
                        <div style="width: 100%; text-align: right">
                            <el-select v-model="selectOption" placeholder="" @change="handleOption" style="width: 50%">
                                <el-option
                                    v-for="(item,index) in options"
                                    :key="index+'b'"
                                    :label="item"
                                    :value="item"
                                ></el-option>
                            </el-select>
                        </div>

                        <div class="info-text" v-for="(item,index) in fxglList" :key="index" style="width: 50%">
                            <p class="col-blue s-font-30">{{item.name}}</p>
                            <p class="s-c-white s-font-30">
                                <span>{{item.value}}</span>
                                &nbsp;
                                <span v-show="item.sjfd!=0">{{item.sjfd<0 ? -item.sjfd : item.sjfd}}%</span>
                                <span class="info-text-up" v-show="item.sjfd>0">↑</span>
                                <span class="info-text-down" v-show="item.sjfd<0">↓</span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="fmks-part">
                    <nav>
                        <s-header-title-2 htype="0" title="隐患概况"></s-header-title-2>
                    </nav>
                    <div class="fmks-part-con" style="padding: 70px 0; box-sizing: border-box">
                        <div class="tabs">
                            <div
                                class="tab_item"
                                :class="tabIndex2===index?'tab_active':''"
                                v-for="(item,index) in tabList2"
                                @click="change2(index)"
                            >
                                {{item}}
                            </div>
                        </div>
                        <div
                            class="info-text animated fadeInLeft"
                            v-show="tabIndex2 == 0"
                            v-for="(item,index) in yhglList"
                            :key="index"
                            style="width: 50%"
                        >
                            <p class="col-blue s-font-30">{{item.name}}</p>
                            <p class="s-c-white s-font-30">
                                <span>{{item.value}}</span>
                                &nbsp;
                                <span v-show="item.sjfd!=0">{{item.sjfd<0 ? -item.sjfd : item.sjfd}}%</span>
                                <span class="info-text-up" v-show="item.sjfd>0">↑</span>
                                <span class="info-text-down" v-show="item.sjfd<0">↓</span>
                            </p>
                        </div>
                        <div
                            class="info-text animated fadeInLeft"
                            v-show="tabIndex2 ==1"
                            v-for="(item,index) in yhglList"
                            :key="index"
                            style="width: 50%"
                        >
                            <p class="col-blue s-font-30">{{item.name}}</p>
                            <p class="s-c-white s-font-30">
                                <span>{{item.value}}</span>
                                &nbsp;
                                <span v-show="item.sjfd!=0">{{item.sjfd<0 ? -item.sjfd : item.sjfd}}%</span>
                                <span class="info-text-up" v-show="item.sjfd>0">↑</span>
                                <span class="info-text-down" v-show="item.sjfd<0">↓</span>
                            </p>
                        </div>
                        <div
                            class="info-text animated fadeInLeft"
                            v-show="tabIndex2 == 2"
                            v-for="(item,index) in yhglList"
                            :key="index"
                            style="width: 50%"
                        >
                            <p class="col-blue s-font-30">{{item.name}}</p>
                            <p class="s-c-white s-font-30">
                                <span>{{item.value}}</span>
                                &nbsp;
                                <span v-show="item.sjfd!=0">{{item.sjfd<0 ? -item.sjfd : item.sjfd}}%</span>
                                <span class="info-text-up" v-show="item.sjfd>0">↑</span>
                                <span class="info-text-down" v-show="item.sjfd<0">↓</span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="fmks-part">
                    <nav>
                        <s-header-title-2 htype="0" title="综合分析"></s-header-title-2>
                    </nav>
                    <div class="fmks-part-con">
                        <div class="info-text" v-for="(item,index) in zhfxList" :key="index">
                            <p class="col-blue s-font-30">{{item.name}}</p>
                            <p class="s-c-white s-font-30">
                                <span>{{item.value}}</span>
                                &nbsp;
                                <span v-show="item.sjfd!=0">{{item.sjfd< 0 ? item.sjfd : item.sjfd}}%</span>
                                <span class="info-text-up" v-show="item.sjfd>0">↑</span>
                                <span class="info-text-down" v-show="item.sjfd<0">↓</span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="fmks-part">
                    <nav>
                        <s-header-title-2 htype="0" title="企业风险意识排名"></s-header-title-2>
                    </nav>
                    <div class="fmks-part-con">
                        <div id="chart06" style="width: 100%; height: 100%"></div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    <script type="module">
        const DHWsInstance = DHWs.getInstance({
            reConnectCount: 2,
            connectionTimeout: 30 * 1000,
            messageEvents: {
                loginState() {
                    console.log("aaaa");
                },
            },
        });
        new Vue({
            el: "#app",
            data: {
                nowTime: "", //当前时间
                processList: [],
                time1: null,
                dom1: null,
                theadList1: ["区域", "等级", "处置情况"],
                tbodyList1: [],
                time2: null,
                dom2: null,
                theadList2: ["危险源", "危险评估值"],
                tbodyList2: [],

                wxyzsList: [],
                jgzsList: [],
                jcyjList: [],
                yjfxList: [],
                xtjcList: [],
                ryaqList: [],
                gjtjList: [],
                fxglList: [],
                yhglList: [],
                zhfxList: [],
                selectOption: "全部",
                selectOption1: "全部",
                selectOption3: "监测系统",
                options: ["全部", "可燃/有毒气体", "储罐", "工艺", "联锁投切信号"],
                options2: ["监测系统", "设备检修", "系统运行", "主提示"],
                month: new Date(),
                tabIndex1: 0,
                tabList1: ["监测告警", "微震监测", "非法超产"],
                tabIndex2: 0,
                tabList2: ["全部", "矿山", "尾矿库"],
                options1: [
                    {
                        value: "10",
                        label: "2022-10",
                        id: 1,
                    },
                    {
                        value: "9",
                        id: 2,
                        label: "2022-09",
                    },
                    {
                        id: 3,
                        value: "8",
                        label: "2022-08",
                    },
                    {
                        id: 4,
                        value: "7",
                        label: "2022-07",
                    },
                    {
                        id: 5,
                        value: "6",
                        label: "2022-06",
                    },
                    {
                        id: 6,
                        value: "5",
                        label: "2022-05",
                    },
                    {
                        id: 7,
                        value: "4",
                        label: "2022-04",
                    },
                    {
                        id: 8,
                        value: "3",
                        label: "2022-03",
                    },
                    {
                        id: 9,
                        value: "2",
                        label: "2022-02",
                    },
                    {
                        id: 10,
                        value: "1",
                        label: "2022-01",
                    },
                ],
                value: "2022-10",
                ws: DHWsInstance,
                isLogin: false,
            },
            //项目生命周期
            mounted() {
                this.getTime();
                this.init();
                this.scroll1();
                this.scroll2();
                this.login();
            },
            methods: {
                handleOption2(item) {
                    $api("shgl_csaq_fmks04", { type: item }).then((res) => {
                        this.xtjcList = res;
                    });
                },
                init() {
                    // $api("shgl_csaq_zdwxy01").then((res) => {
                    //     this.wxyzsList = res;
                    // });
                    $get("/shgl/csaq/zdwxy01").then((res) => {
                        this.wxyzsList = res;
                    });
                    // $api("shgl_csaq_zdwxy02").then((res) => {
                    //     this.getChart01("chart01", res, 0);
                    // });
                    $get("/shgl/csaq/zdwxy02").then((res) => {
                        this.getChart01("chart01", res, 0);
                    });
                    // $api("shgl_csaq_zdwxy03").then((res) => {
                    //     this.getChart02("chart02", res);
                    // });
                    $get("/shgl/csaq/zdwxy03").then((res) => {
                        this.getChart02("chart02", res);
                    });
                    // $api("shgl_csaq_zdwxy04").then((res) => {
                    //     this.getChart04("chart03", res);
                    // });
                    $get("/shgl/csaq/zdwxy04").then((res) => {
                        this.getChart04("chart03", res);
                    });
                    // $api("shgl_csaq_zdwxy05").then((res) => {
                    //     this.getChart03("chart04", res);
                    // });
                    $get("/shgl/csaq/zdwxy05").then((res) => {
                        this.getChart03("chart04", res);
                    });
                    // $api("shgl_csaq_zdwxy06").then((res) => {
                    //     this.processList = res;
                    // });
                    $get("/shgl/csaq/zdwxy06").then((res) => {
                        this.processList = res;
                    });
                    $api("shgl_csaq_zdwxy07", { type: 10 }).then((res) => {
                        console.log(res);
                        this.tbodyList1 = res;
                    });

                    //   $api("shgl_csaq_zdwxy09").then((res) => {
                    //     this.tbodyList2 = res;
                    //   });
                    $get("/shgl/csaq/zdwxy09").then((res) => {
                        this.tbodyList2 = res;
                    });
                    //   $api("shgl_csaq_zdwxy08").then((res) => {
                    //     let names = ["可燃/有毒气体", "储罐", "工艺", "联锁投切信号"];
                    //     this.getChart01("chart05", res, 1, names);
                    //   });
                    $api("shgl_csaq_zdwxy08").then((res) => {
                        let names = ["可燃/有毒气体", "储罐", "工艺", "联锁投切信号"];
                        this.getChart01("chart05", res, 1, names);
                    });

                    //   $api("shgl_csaq_fmks01").then((res) => {
                    //     this.jgzsList = res;
                    //   });
                    $get("/shgl/csaq/fmks01").then((res) => {
                        this.jgzsList = res;
                    });
                    //   $api("shgl_csaq_fmks02").then((res) => {
                    //     this.jcyjList = res;
                    //   });
                    $get("/shgl/csaq/fmks02").then((res) => {
                        this.jcyjList = res;
                    });
                    //   $api("shgl_csaq_fmks03").then((res) => {
                    //     this.yjfxList = res;
                    //   });
                    $get("/shgl/csaq/fmks03").then((res) => {
                        this.yjfxList = res;
                    });
                    $api("shgl_csaq_fmks04", { type: "监测系统" }).then((res) => {
                        this.xtjcList = res;
                    });

                    //   $api("shgl_csaq_fmks05").then((res) => {
                    //     this.ryaqList = res;
                    //   });
                    $get("/shgl/csaq/fmks05").then((res) => {
                        this.ryaqList = res;
                    });
                    //   $api("shgl_csaq_fmks06").then((res) => {
                    //     this.gjtjList = res.slice(0, 4);
                    //   });
                    $get("/shgl/csaq/fmks06").then((res) => {
                        this.gjtjList = res.slice(0, 6);
                    });
                    $api("shgl_csaq_fmks07").then((res) => {
                        this.fxglList = res;
                    });

                    //   $api("shgl_csaq_fmks08").then((res) => {
                    //     this.yhglList = res.slice(0, 4);
                    //   });
                    $get("/shgl/csaq/fmks08").then((res) => {
                        this.yhglList = res.slice(0, 4);
                    });
                    //   $api("shgl_csaq_fmks09").then((res) => {
                    //     this.zhfxList = res;
                    //   });
                    $get("/shgl/csaq/fmks09").then((res) => {
                        this.zhfxList = res;
                    });
                    //   $api("shgl_csaq_fmks010").then((res) => {
                    //     this.getChart02("chart06", res);
                    //   });
                    $get("/shgl/csaq/fmks10").then((res) => {
                        this.getChart02("chart06", res);
                    });
                },
                handleMonthChange(item) {
                    $api("shgl_csaq_zdwxy07", { type: item }).then((res) => {
                        this.tbodyList1 = res;
                    });
                },
                handleOption(item) {
                    console.log(item);
                    if (item === "全部") {
                        $api("shgl_csaq_fmks07").then((res) => {
                            this.fxglList = res;
                        });
                    } else {
                        $api("shgl_csaq_fmks07", { type: item }).then((res) => {
                            this.fxglList = res;
                        });
                    }
                },
                handleOption1(item) {
                    if (item === "可燃/有毒气体") {
                        $api("shgl_csaq_zdwxy08_1").then((res) => {
                            this.getChart01("chart05", res, 1, ["可燃/有毒气体"]);
                        });
                    } else if (item === "储罐") {
                        $api("shgl_csaq_zdwxy08_2").then((res) => {
                            this.getChart01("chart05", res, 1, ["储罐"]);
                        });
                    } else if (item === "工艺") {
                        $api("shgl_csaq_zdwxy08_3").then((res) => {
                            this.getChart01("chart05", res, 1, ["工艺"]);
                        });
                    } else if (item === "联锁投切信号") {
                        $api("shgl_csaq_zdwxy08_4").then((res) => {
                            this.getChart01("chart05", res, 1, ["联锁投切信号"]);
                        });
                    } else {
                        $api("shgl_csaq_zdwxy08").then((res) => {
                            let names = ["可燃/有毒气体", "储罐", "工艺", "联锁投切信号"];
                            this.getChart01("chart05", res, 1, names);
                        });
                    }
                },
                //获取当前时间
                getTime() {
                    var data = new Date();
                    var yesterday = new Date(data.setDate(data.getDate() - 1));
                    this.nowTime =
                        yesterday.getFullYear() + "年" + (yesterday.getMonth() + 1) + "月" + yesterday.getDate() + "日";
                },
                scroll1() {
                    this.dom1 = document.getElementById("tbody1");
                    this.time1 = setInterval(() => {
                        this.dom1.scrollTop += 2;
                        if (this.dom1.scrollTop >= this.dom1.scrollHeight - this.dom1.offsetHeight) {
                            this.dom1.scrollTop = 0;
                        }
                    }, 20);
                },
                mouseenterEvent1() {
                    clearInterval(this.time1);
                },
                mouseleaveEvent1() {
                    this.time1 = setInterval(() => {
                        this.dom1.scrollTop += 2;
                        if (this.dom1.scrollTop >= this.dom1.scrollHeight - this.dom1.offsetHeight) {
                            this.dom1.scrollTop = 0;
                        }
                    }, 20);
                },
                scroll2() {
                    this.dom2 = document.getElementById("tbody2");
                    this.time2 = setInterval(() => {
                        this.dom2.scrollTop += 2;
                        if (this.dom2.scrollTop >= this.dom2.scrollHeight - this.dom2.offsetHeight) {
                            this.dom2.scrollTop = 0;
                        }
                    }, 20);
                },
                mouseenterEvent2() {
                    clearInterval(this.time2);
                },
                mouseleaveEvent2() {
                    this.time2 = setInterval(() => {
                        this.dom2.scrollTop += 2;
                        if (this.dom2.scrollTop >= this.dom2.scrollHeight - this.dom2.offsetHeight) {
                            this.dom2.scrollTop = 0;
                        }
                    }, 20);
                },
                change1(index) {
                    this.tabIndex1 = index;
                    //   $api("shgl_csaq_fmks06").then((res) => {
                    //     if (this.tabIndex1 == 0) {
                    //       this.gjtjList = res.slice(0, 4);
                    //     } else if (this.tabIndex1 == 1) {
                    //       this.gjtjList = res.slice(4, 6);
                    //     } else if (this.tabIndex1 == 2) {
                    //       this.gjtjList = res.slice(6, 10);
                    //     }
                    //   });
                    $get("/shgl/csaq/fmks06").then((res) => {
                        if (this.tabIndex1 == 0) {
                            this.gjtjList = res.slice(0, 6);
                        } else if (this.tabIndex1 == 1) {
                            this.gjtjList = res.slice(6, 8);
                        } else if (this.tabIndex1 == 2) {
                            this.gjtjList = res.slice(8, 12);
                        }
                    });
                },
                change2(index) {
                    this.tabIndex2 = index;
                    //   $api("shgl_csaq_fmks08").then((res) => {
                    //     if (this.tabIndex2 == 0) {
                    //       this.yhglList = res.slice(0, 4);
                    //     } else if (this.tabIndex2 == 1) {
                    //       this.yhglList = res.slice(4, 8);
                    //     } else if (this.tabIndex2 == 2) {
                    //       this.yhglList = res.slice(8, 12);
                    //     }
                    //   });
                    $get("/shgl/csaq/fmks08").then((res) => {
                        if (this.tabIndex2 == 0) {
                            this.yhglList = res.slice(0, 4);
                        } else if (this.tabIndex2 == 1) {
                            this.yhglList = res.slice(4, 8);
                        } else if (this.tabIndex2 == 2) {
                            this.yhglList = res.slice(8, 12);
                        }
                    });
                },
                login() {
                    // 调用登录接口
                    this.ws.detectConnectQt().then((res) => {
                        if (res) {
                            // 连接客户端成功
                            this.ws.login({
                                loginIp: "*************",
                                loginPort: "8001",
                                userName: "yjgl",
                                userPwd: "yjgl1234",
                                token: "",
                                https: 0,
                            });
                            // this.$Message.info('登录中...')
                            console.log("登录中...");
                            this.ws.on("loginState", (res) => {
                                this.isLogin = res;
                                if (res) {
                                    console.log("登录成功");
                                    this.getVideoList();
                                } else {
                                    console.log("登录失败");
                                }
                            });
                        } else {
                            // 连接客户端失败
                            this.$Message.info("请重新安装客户端");
                        }
                    });
                },
                openVideo() {
                    this.ws.openVideo(["33079952001321087131"]);
                },
                getChart01(id, chartData, index, name11) {
                    const myCharts = echarts.init(document.getElementById(id));
                    let x = chartData.map((item) => {
                        return item.name;
                    });
                    let y1 = chartData.map((item) => {
                        return item.value1;
                    });
                    let y2 = chartData.map((item) => {
                        return item.value2;
                    });
                    let y3 = chartData.map((item) => {
                        return item.value3;
                    });
                    let y4 = chartData.map((item) => {
                        return item.value4;
                    });
                    let legend = [
                        {
                            name1: "一级",
                            name2: "二级",
                            name3: "三级",
                            name4: "四级",
                        },
                        {
                            name1: "可燃/有毒气体",
                            name2: "储罐",
                            name3: "工艺",
                            name4: "联锁投切信号",
                        },
                    ];
                    let option = {
                        grid: {
                            left: "5%",
                            right: "5%",
                            top: "20%",
                            bottom: "0%",
                            containLabel: true,
                        },
                        tooltip: {
                            trigger: "axis",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "28",
                            },
                        },
                        legend: {
                            show: true,
                            data: name11,
                            x: "center",
                            y: "15",
                            itemWidth: 10,
                            itemHeight: 10,
                            textStyle: {
                                color: "#fff",
                                fontSize: "24",
                            },
                        },
                        xAxis: [
                            {
                                type: "category",
                                // offset: 20,
                                axisLine: {
                                    //坐标轴轴线相关设置。数学上的x轴
                                    show: true,
                                    lineStyle: {
                                        color: "rgba(108, 166, 219, 0.3)",
                                    },
                                },
                                axisLabel: {
                                    //坐标轴刻度标签的相关设置
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#192a44",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                data: x,
                            },
                        ],
                        yAxis: [
                            {
                                // name: "单位：件",
                                nameTextStyle: {
                                    fontSize: 24,
                                    color: "#D6E7F9",
                                    padding: [0, 20, 10, 0],
                                },
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.1,
                                        width: 2,
                                    },
                                },
                                axisTick: {
                                    show: true,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.5,
                                        width: 2,
                                    },
                                },
                                axisLabel: {
                                    textStyle: {
                                        fontSize: 28,
                                        color: "#D6E7F9",
                                    },
                                },
                                axisLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#233653",
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: legend[index].name1,
                                type: "line",
                                smooth: true,
                                symbolSize: 4,
                                itemStyle: {
                                    normal: {
                                        color: "#5087EC",
                                        lineStyle: {
                                            color: "#5087EC",
                                        },
                                    },
                                },
                                data: y1,
                            },
                            {
                                name: legend[index].name2,
                                type: "line",
                                smooth: true,
                                symbolSize: 4,
                                itemStyle: {
                                    normal: {
                                        color: "#00ca95",
                                        lineStyle: {
                                            color: "#00ca95",
                                        },
                                    },
                                },
                                data: y2,
                            },
                            {
                                name: legend[index].name3,
                                type: "line",
                                smooth: true,
                                symbolSize: 4,
                                itemStyle: {
                                    normal: {
                                        color: "#f1d53c",
                                        lineStyle: {
                                            color: "#f1d53c",
                                        },
                                    },
                                },
                                data: y3,
                            },
                            {
                                name: legend[index].name4,
                                type: "line",
                                smooth: true,
                                symbolSize: 4,
                                itemStyle: {
                                    normal: {
                                        color: "red",
                                        lineStyle: {
                                            color: "red",
                                        },
                                    },
                                },
                                data: y4,
                            },
                        ],
                    };
                    myCharts.setOption(option);
                    myCharts.getZr().on("mousemove", (param) => {
                        myCharts.getZr().setCursorStyle("default");
                    });
                },
                getChart02(id, res) {
                    const myCharts = echarts.init(document.getElementById(id));
                    var data = [];
                    var titlename = [];
                    var valdata = [];
                    res.forEach((item, index) => {
                        data.push(item.value);
                        titlename.push(index + 1 + "." + item.name);
                        valdata.push(item.value);
                    });
                    var myColor = ["#1089E7", "#F57474", "#56D0E3", "#F8B448", "#8B78F6"];
                    let option = {
                        grid: {
                            left: "-30%",
                            right: "0%",
                            top: "10%",
                            bottom: "0%",
                            containLabel: true,
                        },
                        xAxis: {
                            show: false,
                        },
                        yAxis: [
                            {
                                show: false,
                                data: titlename,
                                inverse: true,
                                axisLine: {
                                    show: false,
                                },

                                splitLine: {
                                    show: false,
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLabel: {
                                    textStyle: {
                                        color: function (value, index) {
                                            var num = myColor.length;
                                            return myColor[index % num];
                                        },
                                    },
                                    formatter: function (value, index) {
                                        return ["{title|" + value + "} "].join("\n");
                                    },
                                    rich: {},
                                },
                            },
                            {
                                show: true,
                                inverse: true,
                                data: valdata,
                                axisLabel: {
                                    textStyle: {
                                        fontSize: 30,
                                        color: function (value, index) {
                                            var num = myColor.length;
                                            return myColor[index % num];
                                        },
                                    },
                                },
                                axisLine: {
                                    show: false,
                                },
                                splitLine: {
                                    show: false,
                                },
                                axisTick: {
                                    show: false,
                                },
                            },
                        ],
                        series: [
                            {
                                name: "条",
                                type: "bar",
                                yAxisIndex: 0,
                                data: data,
                                barWidth: 40,
                                itemStyle: {
                                    normal: {
                                        barBorderRadius: 30,
                                        color: function (params) {
                                            var num = myColor.length;
                                            return myColor[params.dataIndex % num];
                                        },
                                    },
                                },
                                label: {
                                    normal: {
                                        show: true,
                                        position: "insideLeft",
                                        padding: [0, 10],
                                        formatter: "{b}",
                                        fontSize: 24,
                                        color: "#fff",
                                    },
                                },
                            },
                        ],
                    };

                    myCharts.setOption(option);
                },
                getChart03(id, chartData) {
                    const myCharts = echarts.init(document.getElementById(id));
                    var legend = ["一级", "二级", "三级", "四级"];
                    var colorList = ["#5087EC", "#68BBC4", "#58A55C"];
                    var data = [];
                    let x = chartData.map((item) => {
                        return item.name;
                    });
                    let y1 = chartData.map((item) => {
                        return item.value1;
                    });
                    let y2 = chartData.map((item) => {
                        return item.value2;
                    });
                    let y3 = chartData.map((item) => {
                        return item.value3;
                    });
                    let y4 = chartData.map((item) => {
                        return item.value4;
                    });
                    data.push(y1, y2, y3, y4);
                    let option = {
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "24",
                            },
                        },
                        // color: colors,
                        legend: {
                            x: "30%",
                            y: "15",
                            itemWidth: 20,
                            itemHeight: 20,
                            textStyle: {
                                color: "#fff",
                                fontSize: 24,
                            },
                            data: legend,
                        },
                        grid: {
                            left: "3%",
                            right: "4%",
                            bottom: "0%",
                            top: "20%",
                            containLabel: true,
                        },
                        xAxis: {
                            type: "category",
                            axisLine: {
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.3,
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                // interval: 0,
                                rotate: -30,
                                textStyle: {
                                    fontSize: 24,
                                    color: "white",
                                },
                            },
                            data: x,
                        },
                        yAxis: [
                            {
                                name: "危险源",
                                type: "value",
                                nameTextStyle: {
                                    fontSize: 24,
                                    color: "#D6E7F9",
                                    // padding:[-15,0]
                                },
                                splitLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.1,
                                        width: 2,
                                    },
                                },
                                axisTick: {
                                    show: true,
                                    lineStyle: {
                                        color: "#77b3f1",
                                        opacity: 0.5,
                                        width: 2,
                                    },
                                },
                                axisLabel: {
                                    textStyle: {
                                        fontSize: 24,
                                        color: "#D6E7F9",
                                    },
                                },
                            },
                        ],
                        series: [],
                    };
                    for (var i = 0; i < legend.length; i++) {
                        option.series.push({
                            name: legend[i],
                            type: "bar",
                            stack: "总量",
                            barWidth: 20,
                            itemStyle: {
                                normal: {
                                    color: colorList[i],
                                },
                            },
                            label: {
                                show: false,
                                position: "inside",
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 24,
                                },
                            },
                            data: data[i],
                        });
                    }
                    option.series[option.series.length - 1].itemStyle.normal.barBorderRadius = [8, 8, 0, 0];
                    myCharts.setOption(option);
                    myCharts.getZr().on("mousemove", (param) => {
                        myCharts.getZr().setCursorStyle("default");
                    });
                },
                getChart04(id, chartData) {
                    let myCharts = echarts.init(document.getElementById(id));
                    let colorList = ["#0090ff", "#06d3c4"];
                    let option = {
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "24",
                            },
                            formatter: function (param) {
                                return param.data.name + ":" + param.value + "个";
                            },
                        },
                        legend: {
                            x: "60%",
                            y: "center",
                            itemWidth: 20,
                            itemHeight: 20,
                            textStyle: {
                                color: "#fff",
                                fontSize: 24,
                            },
                        },
                        series: [
                            {
                                name: "整体分类",
                                type: "pie",
                                radius: [0, "80%"],
                                center: ["30%", "50%"],
                                label: {
                                    position: "inner",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 24,
                                    },
                                    formatter: function (param) {
                                        return param.value + "%";
                                    },
                                },
                                itemStyle: {
                                    normal: {
                                        borderColor: "#fff",
                                        borderWidth: 0,
                                        color: function (params) {
                                            return colorList[params.dataIndex];
                                        },
                                    },
                                },
                                selectedMode: "single",
                                data: chartData,
                            },
                        ],
                    };
                    myCharts.setOption(option);
                    myCharts.getZr().on("mousemove", (param) => {
                        myCharts.getZr().setCursorStyle("default");
                    });
                },
            },
        });
    </script>
</html>
