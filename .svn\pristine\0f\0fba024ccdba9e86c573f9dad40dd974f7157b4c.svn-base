<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>一网通4-左</title>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <script src="/Vue/vue.js"></script>
    <script src="/elementui/js/index.js"></script>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/js/jslib/Emiter.js"></script>

    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <!-- <script src="/static/js/jslib/datav.min.vue.js"></script> -->
    <!-- <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script> -->
    <!-- <link rel="stylesheet" href="/static/css/animate_dn.css" /> -->
    <link rel="stylesheet" href="/static/citybrain/shgl/css/jtys-left.css" />
    <!-- <link rel="stylesheet" href="/static/css/xiaoguo.css" /> -->
    <!-- <script src="/static/citybrain/hjbh/js/date.js"></script> -->
  </head>
  <body>
    <div id="jtys-left" v-cloak>
      <div class="ywt4-left_box">
        <!-- 党建统领 -->
        <div class="box box1">
          <div class="box-title">
            <s-header-title
              title="公路概况"
              htype="1"
              :data-time="nowTime"
            ></s-header-title>
          </div>
          <div class="box-con box1-content">
            <div class="box1-content-item">
              <s-header-title2 title="公路里程"></s-header-title2>
              <div id="echrts1" style="width: 100%; height: 350px"></div>
            </div>
            <div class="box1-content-item">
              <s-header-title2 title="公路划分情况"></s-header-title2>
              <div class="part-top">
                <div v-for="(item ,index) in types" @click="changeDJ(index)">
                  <input
                    type="radio"
                    name="qy"
                    value="item"
                    :checked="checkTypeIndex === index"
                  />
                  <span class="s-c-yellow-gradient">{{item}}</span>
                </div>
              </div>
              <div id="echarts02" style="width: 100%; height: 350px"></div>
            </div>
          </div>
        </div>
        <!--数字政府  -->
        <div class="box box2">
          <div class="box-title">
            <s-header-title
              title="物流分析"
              htype="1"
              :data-time="nowTime"
            ></s-header-title>
          </div>
          <div class="box-con box2-content" style="position: relative">
            <div
              style="display: flex; position: absolute; right: 89px; top: -50px"
            >
              <el-select
                v-model="value3"
                placeholder="请选择"
                @change="changeWL"
              >
                <el-option
                  v-for="item in options0"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <div
                class="text"
                style="
                  width: 36px;
                  color: rgb(255, 255, 255);
                  font-size: 28px;
                  z-index: 999;
                  margin-left: 38px;
                "
              >
                至
              </div>
              <el-select
                v-model="value4"
                placeholder="请选择"
                @change="changeWL"
              >
                <el-option
                  v-for="item in options0"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>

            <div
              id="echarts3"
              style="width: 100%; height: 384px; margin-top: 50px"
            ></div>
          </div>
        </div>

        <!-- 数字经济 -->
        <div class="box box3">
          <div class="box-title">
            <s-header-title
              title="公交地铁情况"
              htype="1"
              data-time="2022年9月20日"
            ></s-header-title>
          </div>
          <div class="box-con box3-content">
            <div class="box3-content-item">
              <div
                style="
                  text-align: center;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                "
              >
                <p class="s-c-grey-light s-font-30">公交车总量</p>
                <div
                  class="number s-c-yellow-gradient"
                  v-for="(item, i) in total"
                  :key="i"
                >
                  <span class="numbg" v-if="item!=','&&item!='.'">
                    <count-to
                      :start-val="0"
                      :end-val="Number(item)"
                      :duration="3000"
                      class="s-c-yellow-gradient"
                    ></count-to>
                  </span>
                  <span v-else>{{item}}</span>
                </div>
                <p class="s-c-yellow-gradient s-font-25">辆</p>
              </div>
              <ul class="pic-box">
                <li v-for="(item,index) in gjqgData" :key="index">
                  <div class="s-c-blue-gradient" style="font-size: 48px">
                    {{item.value}}
                  </div>
                  <div style="color: #fff; font-size: 32px">{{item.name}}</div>
                </li>
              </ul>
              <div class="lendge">
                <div
                  class="lendge-item"
                  v-for="(item,index) in carType"
                  :key="index"
                >
                  <div class="circle"></div>
                  <span>{{item.name}}</span>
                  <span class="s-c-yellow-gradient">{{item.value}}台</span>
                  <span class="s-c-yellow-gradient">{{item.percent}}%</span>
                </div>
              </div>
            </div>
            <div class="box3-content-item">
              <nav style="margin: 40px">
                <ul class="tab_trun">
                  <li
                    :class="tabIndex===index ? 'active_li':''"
                    v-for="(item,index) in navBar"
                    :key="index"
                    @click="tabClick(index)"
                    style="cursor: pointer"
                  >
                    {{item}}
                  </li>
                </ul>
              </nav>
              <div class="part-top part-top2" style="position: relative">
                <div v-show="tabIndex===0" style="display: flex">
                  <div @click="radioChange(0)">
                    <input type="radio" name="sex" value="同比" checked />
                    <span class="s-c-yellow-gradient">同比</span>
                  </div>
                  <div @click="radioChange(1)">
                    <input type="radio" name="sex" value="环比" />
                    <span class="s-c-yellow-gradient">环比</span>
                  </div>
                </div>
                <div style="display: flex; margin-right: 100px">
                  <el-select
                    v-model="value"
                    placeholder="请选择"
                    @change="changeGJ"
                  >
                    <el-option
                      v-for="item in options0"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                  <div
                    class="text"
                    style="
                      width: 20px;
                      color: rgb(255, 255, 255);
                      font-size: 28px;
                      z-index: 999;
                      margin-left: 38px;
                    "
                  >
                    至
                  </div>
                  <el-select
                    v-model="value1"
                    placeholder="请选择"
                    @change="changeGJ"
                  >
                    <el-option
                      v-for="item in options0"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div
                v-show="tabIndex==0"
                id="echart4"
                style="width: 970px; height: 396px"
              ></div>
              <div
                v-show="tabIndex==1"
                id="barechart"
                style="width: 970px; height: 396px"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      var vm = new Vue({
        el: "#jtys-left",
        data: {
          tabIndex: 0,
          selectTimeName: "43343",
          value: "1",
          checkTypeIndex: 0,
          types: ["行政等级", "技术等级"],
          value1: "12",
          value3: "1",
          value4: "12",
          options0: [
            {
              value: "1",
              label: "2021年1月",
            },
            {
              value: "2",
              label: "2021年2月",
            },
            {
              value: "3",
              label: "2021年3月",
            },
            {
              value: "4",
              label: "2021年4月",
            },
            {
              value: "5",
              label: "2021年5月",
            },
            {
              value: "6",
              label: "2021年6月",
            },
            {
              value: "7",
              label: "2021年7月",
            },
            {
              value: "8",
              label: "2021年8月",
            },
            {
              value: "9",
              label: "2021年9月",
            },
            {
              value: "10",
              label: "2021年10月",
            },
            {
              value: "11",
              label: "2021年11月",
            },
            {
              value: "12",
              label: "2021年12月",
            },
          ],
          selectTime: ["43", "43"],
          showSelectTime: false,
          nowTime: "",
          navBar: ["公交地铁客流分析", "乘客分析"],
          gjqgData: [],
          showData: [],
          data1: [],
          carType: [],
          total: "",
          min: "",
          max: "",
          mins: "",
          maxs: "",
          type: 0,
          typesss: "同比",
        },
        computed: {},
        mounted() {
          this.ininEc();
          this.openIframe1();
          this.openIframe2();
          // this.getEcharts02("echarts3");
        },
        methods: {
          changeDJ(index) {
            this.checkTypeIndex = index;
            if (index === 0) {
              $get("/shgl/jtys002").then((res) => {
                this.showData = res;
                this.initCharts1(res);
              });
              // $api("shgl_jtys002", { type: "行政等级" }).then((res) => {
              //   this.showData = res;
              //   this.initCharts1(res);
              // });
            } else {
              $get("/shgl/jtys008").then((res) => {
                this.showData = res;
                this.initCharts1(res);
              });
              // $api("shgl_jtys002", { type: "技术等级" }).then((res) => {
              //   this.showData = res;
              //   this.initCharts1(res);
              // });
            }
          },
          changeWL(item) {
            this.maxs = this.value4;
            this.mins = this.value3;
            $api("shgl_jtys003", { min: this.mins, max: this.maxs }).then(
              (res) => {
                this.getEcharts02("echarts3", res);
              }
            );
          },
          radioChange(type) {
            this.max = this.value1;
            this.min = this.value;
            if (type === 0) {
              this.typesss = "同比";
              $api("shgl_jtys006", {
                type: this.typesss,
                min: this.min,
                max: this.max,
              }).then((res) => {
                this.getEcharts04("echart4", res);
              });
            } else if (type === 1) {
              this.typesss = "环比";
              $api("shgl_jtys006", {
                type: this.typesss,
                min: this.min,
                max: this.max,
              }).then((res) => {
                this.getEcharts04("echart4", res);
              });
            }
          },
          changeGJ(item) {
            this.max = this.value1;
            this.min = this.value;

            $api("shgl_jtys006", {
              type: this.typesss,
              min: this.min,
              max: this.max,
            }).then((res) => {
              this.getEcharts04("echart4", res);
            });
            $api("shgl_jtys007", {
              min: this.min,
              max: this.max,
            }).then((res) => {
              this.getBarEcharts(res);
            });
          },
          getBarEcharts(res) {
            // console.log(res[0].value);
            let myChart = echarts.init(document.getElementById("barechart"));

            let option = {
              tooltip: {
                textStyle: {
                  fontSize: 30,
                },
              },
              grid: {
                right: 0,
              },
              xAxis: {
                type: "category",
                data: res.map((item) => item.name),
                axisLabel: {
                  fontSize: 25,
                  color: "#fff",
                },
              },
              yAxis: {
                type: "value",
                axisLabel: {
                  fontSize: 30,
                  color: "#fff",
                },
              },
              series: [
                {
                  data: res.map((item) => item.value),
                  type: "bar",
                },
              ],
            };
            myChart.setOption(option);
          },
          openIframe1() {
            let left = {
              type: "openIframe",
              name: "jtys-doing1",
              src:
                baseURL.url + "/static/citybrain/shgl/commont/jtys-doing1.html",
              left: "calc(50% - 1250px)",
              top: "230px",
              width: "2500px",
              height: "300px",
              zIndex: "10",
              argument: {
                status: "",
              },
            };
            window.parent.postMessage(JSON.stringify(left), "*");
          },
          openIframe2() {
            let left = {
              type: "openIframe",
              name: "jtys-doing2",
              src:
                baseURL.url + "/static/citybrain/shgl/commont/jtys-doing2.html",
              left: "calc(50% - 1690px)",
              top: "1683px",
              width: "3380px",
              height: "400px",
              zIndex: "10",
              argument: {
                status: "",
              },
            };
            window.parent.postMessage(JSON.stringify(left), "*");
          },

          ininEc() {
            $get("/shgl/jtys001").then((res) => {
              this.initCharts0("echrts1", res);
            });
            // $api("shgl_jtys001").then((res) => {
            //   this.initCharts0("echrts1", res);
            // });
            $get("/shgl/jtys002", { type: "技术等级" }).then((res) => {
              this.showData = res;
              this.initCharts1(res);
            });
            // $api("shgl_jtys002", { type: "技术等级" }).then((res) => {
            //   this.showData = res;
            //   this.initCharts1(res);
            // });

            $api("shgl_jtys003", { min: "1", max: "12" }).then((res) => {
              this.getEcharts02("echarts3", res);
            });
            $get("/shgl/jtys004").then((res) => {
              this.gjqgData = res;
            });
            // $api("shgl_jtys004").then((res) => {
            //   this.gjqgData = res;
            // });
            $get("/shgl/jtys005").then((res) => {
              this.carType = res;
              this.total = res[0].total.toString();
            });
            // $api("shgl_jtys005").then((res) => {
            //   this.carType = res;
            //   this.total = res[0].total.toString();
            // });

            $api("shgl_jtys006", {
              type: "同比",
              min: "1",
              max: "12",
            }).then((res) => {
              // this.carType = res;
              this.getEcharts04("echart4", res);
            });
          },
          tabClick(index) {
            this.tabIndex = index;
            if (this.tabIndex === 0) {
              $api("shgl_jtys006", {
                type: this.typesss,
                min: this.min,
                max: this.max,
              }).then((res) => {
                this.getEcharts04("echart4", res);
              });
            } else {
              $api("shgl_jtys007", {
                min: "1",
                max: "12",
              }).then((res) => {
                this.getBarEcharts(res);
              });
            }
          },
          handleClick(e) {
            console.log(e);
          },
          handleClick1() {},
          initCharts1(data) {
            let that = this;
            let myChart = echarts.init(document.getElementById("echarts02"));
            let option = {
              color: ["#539FF7", "#6AE4B2", "#F5CC53", "#E86056", "#6973EC"],
              title: {
                text: "",
                subtext: "",
                left: "left",
              },
              tooltip: {
                trigger: "item",
                // formatter: '{b}: <br/> {d}%',
                // formatter: '{b}: <br/> {c}',
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "28",
                },
              },
              legend: {
                selectedMode: false,
                orient: "",
                left: 450,
                itemGap: 18,
                top: 100,
                bottom: 20,
                textStyle: {
                  color: "#fff",
                  fontSize: 30,
                },
                formatter: function (name) {
                  let val = "";
                  let sum = 0;
                  that.showData.forEach((item, i) => {
                    sum += Number(item.value);
                  });
                  that.showData.forEach((item, i) => {
                    if (item.name == name) {
                      val =
                        name +
                        "    " +
                        item.value +
                        "公里   " +
                        ((item.value / sum) * 100).toFixed(0) +
                        "%";
                    }
                  });
                  return val;
                },
              },
              series: [
                {
                  cursor: "auto",
                  name: "",
                  type: "pie",
                  radius: ["60%", "90%"],
                  center: ["25%", "50%"],
                  data: data,
                  itemStyle: {
                    normal: {
                      borderRadius: 0,
                      borderColor: "#fff",
                      borderWidth: 0,
                      label: {
                        //此处为指示线文字
                        show: false,
                        textStyle: {
                          color: "#fff",
                          fontWeight: 200,
                          fontSize: 30, //文字的字体大小
                        },
                        formatter: function (p) {
                          //指示线对应文字
                          let data = p.value + "件";
                          return data;
                        },
                      },
                      // labelLine: {
                      //   //指示线状态
                      //   show: true,
                      //   smooth: 1.2,
                      //   length: 10,
                      //   length2: 20,
                      // },
                    },
                  },
                },
              ],
            };
            myChart.setOption(option);
          },
          getEcharts02(dom, data) {
            let myEc = echarts.init(document.getElementById(dom));
            let xData = [],
              yData = [],
              y1Data = [],
              y2Data = [],
              y3Data = [];

            data.forEach((item) => {
              xData.push(item.name);
              yData.push(item.value);
              y1Data.push(item.value1);
              y2Data.push(item.value2);
              y3Data.push(item.value3);
            });
            var option = {
              tooltip: {
                trigger: "axis",
                axisPointer: {
                  // 坐标轴指示器，坐标轴触发有效
                  type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                },
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "28",
                },
              },
              legend: {
                orient: "horizontal",
                itemWidth: 18,
                itemHeight: 18,
                top: "8%",
                icon: "rect",
                itemGap: 45,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 30,
                },
              },
              grid: {
                left: "2%",
                right: "5%",
                bottom: "20%",
                top: "20%",
                containLabel: true,
              },
              xAxis: [
                {
                  type: "category",
                  data: xData,
                  offset: 20,
                  axisLine: {
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.3,
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  axisLabel: {
                    interval: 0,
                    // rotate: -30,
                    textStyle: {
                      fontSize: 30,
                      color: "white",
                    },
                  },
                },
              ],
              yAxis: [
                {
                  name: "单位:万元",
                  type: "value",
                  // max: 800,
                  min: 0,
                  nameTextStyle: {
                    fontSize: 30,
                    color: "#D6E7F9",
                    padding: [0, 0, 20, 0],
                  },
                  splitLine: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.1,
                      width: 2,
                    },
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.5,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 30,
                      color: "#D6E7F9",
                    },
                  },
                },
                {
                  name: "单位：%",
                  type: "value",
                  max: 30,
                  min: 0,
                  nameTextStyle: {
                    fontSize: 30,
                    color: "#D6E7F9",
                    padding: [0, 0, 20, 0],
                  },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.1,
                      width: 2,
                    },
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.5,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 30,
                      color: "#D6E7F9",
                    },
                  },
                },
              ],
              series: [
                {
                  name: "环比",
                  type: "bar",
                  barWidth: "15%",
                  yAxisIndex: 0,
                  smooth: true, //加这个
                  center: ["0%", "45%"],
                  radius: ["0%", "45%"],
                  itemStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: "#CBF2FF",
                        },
                        {
                          offset: 0.5,
                          color: "#00C0FF",
                        },
                        {
                          offset: 1,
                          color: "#004F69",
                        },
                      ]),
                      barBorderRadius: 4,
                    },
                  },
                  data: yData,
                },
                {
                  name: "同比",
                  type: "line",
                  barWidth: "15%",
                  smooth: true, //加这个
                  yAxisIndex: 1,
                  itemStyle: {
                    normal: {
                      color: "#e86056",
                      barBorderRadius: 4,
                    },
                  },
                  data: y1Data,
                },
                {
                  name: "增长率",
                  type: "line",
                  barWidth: "15%",
                  smooth: true, //加这个
                  yAxisIndex: 1,
                  itemStyle: {
                    normal: {
                      color: "#f5cc53",
                      barBorderRadius: 4,
                    },
                  },
                  data: y2Data,
                },
              ],
            };
            myEc.setOption(option);
            myEc.getZr().on("mousemove", (param) => {
              myEc.getZr().setCursorStyle("default");
            });
          },
          initCharts0(dom, data) {
            function random(min, max) {
              return parseInt(Math.random() * (max - min) + min);
            }
            let myChart = echarts.init(document.getElementById(dom));

            let xData = [],
              yData = [],
              barData = [];
            data.map((ele) => {
              xData.push(ele.name);
              yData.push(ele.value);
              barData.push(ele.value1);
            });

            let option = {
              //   backgroundColor: '#062544',
              legend: {
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 30,
                },
              },
              grid: {
                top: "10%",
                left: "-5%",
                bottom: "5%",
                right: "5%",
                containLabel: true,
              },
              tooltip: {
                trigger: "item",
                // formatter: '{b}: <br/> {d}%',
                // formatter: '{b}: <br/> {c}',
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "30",
                },
              },
              animation: false,
              xAxis: [
                {
                  type: "category",
                  data: xData,
                  axisTick: {
                    alignWithLabel: true,
                  },
                  axisLine: {
                    show: false,
                  },
                  axisLabel: {
                    textStyle: {
                      color: "#fff",
                      fontSize: "30",
                    },
                    margin: 30,
                  },
                  interval: 1,
                },
              ],
              yAxis: [
                {
                  show: false,
                  type: "value",
                },
              ],
              series: [
                {
                  name: "",
                  type: "pictorialBar",
                  silent: true,
                  symbolSize: [40, 10],
                  symbolOffset: [0, -6],
                  symbolPosition: "end",
                  z: 12,
                  label: {
                    normal: {
                      show: true,
                      position: "top",
                      fontSize: 20,
                      fontWeight: "bold",
                      color: "#5BFCF4",
                      formatter: "{c}KM",
                    },
                  },
                  color: "#5BFCF4",
                  data: yData,
                },
                {
                  name: "",
                  type: "pictorialBar",
                  silent: true,
                  symbolSize: [40, 10],
                  symbolOffset: [0, 7],
                  z: 12,
                  color: "#5BFCF4",
                  data: yData,
                },
                {
                  name: "",
                  type: "pictorialBar",
                  silent: true,
                  symbolSize: [50, 15],
                  symbolOffset: [0, 12],
                  z: 10,
                  itemStyle: {
                    normal: {
                      color: "transparent",
                      borderColor: "#5BFCF4",
                      borderType: "solid",
                      borderWidth: 8,
                    },
                  },
                  data: yData,
                },
                {
                  name: "",
                  type: "pictorialBar",
                  silent: true,
                  symbolSize: [70, 20],
                  symbolOffset: [0, 18],
                  z: 10,
                  itemStyle: {
                    normal: {
                      color: "transparent",
                      borderColor: "rgba(91,252,244,0.5)",
                      borderType: "solid",
                      borderWidth: 5,
                    },
                  },
                  data: yData,
                },
                {
                  name: "累计里程",
                  type: "bar",
                  barWidth: "40",
                  barGap: "10%", // Make series be overlap
                  barCateGoryGap: "10%",
                  itemStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(0, 0, 0, 0.7, [
                        {
                          offset: 0,
                          color: "rgba(210,210,210,0.3)",
                        },
                        {
                          offset: 1,
                          color: "#5BFCF4",
                        },
                      ]),
                      opacity: 0.6,
                    },
                  },
                  data: yData,
                },
                {
                  name: "增长里程",
                  type: "bar",
                  barWidth: 40,

                  z: 12,
                  barGap: "-100%",
                  itemStyle: {
                    //lenged文本
                    opacity: 0.7,
                    color: function (params) {
                      return new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 0,
                            color: "#062544", // 0% 处的颜色
                          },
                          {
                            offset: 1,
                            color: "#1e7d97", // 100% 处的颜色
                          },
                        ],
                        false
                      );
                    },
                  },

                  data: barData,
                },
                {
                  name: "",
                  type: "pictorialBar",
                  silent: true,
                  symbolSize: [40, 10],
                  symbolOffset: [0, -6],
                  symbolPosition: "end",
                  z: 12,
                  color: "#5BFCF4",
                  data: barData,
                },
                {
                  name: "",
                  type: "pictorialBar",
                  silent: true,
                  symbolSize: [40, 10],
                  symbolOffset: [0, 7],
                  z: 12,
                  color: "#5BFCF4",
                  data: barData,
                },
              ],
            };
            myChart.setOption(option);
          },
          getEcharts04(dom, data) {
            let myEc = echarts.init(document.getElementById(dom));
            let klzl = [];
            let gjkl = [];
            let qgkl = [];
            let klzl1 = [];
            let gjkl1 = [];
            let qgkl1 = [];
            let xData = [];
            data.forEach((item) => {
              klzl.push(item.klzl);
              gjkl.push(item.gjkl);
              qgkl.push(item.qgkl);
              klzl1.push(item.klzl1);
              gjkl1.push(item.gjkl1);
              qgkl1.push(item.qgkl1);
              xData.push(item.name);
            });
            var option = {
              tooltip: {
                trigger: "axis",
                axisPointer: {
                  // 坐标轴指示器，坐标轴触发有效
                  type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                },
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "28",
                },
              },
              legend: {
                orient: "horizontal",
                itemWidth: 18,
                itemHeight: 18,
                top: "10",
                left: "0",
                icon: "rect",
                itemGap: 45,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                },
              },
              grid: {
                left: "2%",
                right: "5%",
                bottom: "10%",
                top: "35%",
                containLabel: true,
              },
              xAxis: [
                {
                  type: "category",
                  data: xData,
                  offset: 20,
                  axisLine: {
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.3,
                    },
                  },
                  axisTick: {
                    show: false,
                  },
                  axisLabel: {
                    interval: 0,
                    // rotate: -30,
                    textStyle: {
                      fontSize: 28,
                      color: "white",
                    },
                  },
                },
              ],
              yAxis: [
                {
                  name: "单位:万人次",
                  type: "value",
                  // max: 800,
                  min: 0,
                  nameTextStyle: {
                    fontSize: 28,
                    color: "#D6E7F9",
                    padding: [0, 0, 20, 0],
                  },
                  splitLine: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.1,
                      width: 2,
                    },
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.5,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 28,
                      color: "#D6E7F9",
                    },
                  },
                },
                {
                  name: "单位：%",
                  type: "value",
                  nameTextStyle: {
                    fontSize: 30,
                    color: "#D6E7F9",
                    padding: [0, 0, 20, 0],
                  },
                  splitLine: {
                    show: false,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.1,
                      width: 2,
                    },
                  },
                  axisTick: {
                    show: true,
                    lineStyle: {
                      color: "#77b3f1",
                      opacity: 0.5,
                      width: 2,
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      fontSize: 30,
                      color: "#D6E7F9",
                    },
                  },
                },
              ],
              series: [
                {
                  name: "客流",
                  type: "bar",
                  barWidth: "15",
                  yAxisIndex: 0,
                  smooth: true, //加这个
                  center: ["0%", "45%"],
                  radius: ["0%", "45%"],
                  itemStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: "#CBF2FF",
                        },
                        {
                          offset: 0.5,
                          color: "#00C0FF",
                        },
                        {
                          offset: 1,
                          color: "#004F69",
                        },
                      ]),
                      barBorderRadius: 4,
                    },
                  },
                  data: klzl,
                },
                {
                  name: "公交",
                  type: "bar",
                  barWidth: "15",
                  yAxisIndex: 0,
                  smooth: true, //加这个
                  center: ["0%", "45%"],
                  radius: ["0%", "45%"],
                  itemStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: "#7da14f",
                        },
                        {
                          offset: 0.5,
                          color: "#3d583a",
                        },
                        {
                          offset: 1,
                          color: "#3d583a",
                        },
                      ]),
                      barBorderRadius: 4,
                    },
                  },
                  data: gjkl,
                },
                {
                  name: "地铁",
                  type: "bar",
                  barWidth: "15",
                  yAxisIndex: 0,
                  smooth: true, //加这个
                  center: ["0%", "45%"],
                  radius: ["0%", "45%"],
                  itemStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: "#5bfcf4",
                        },
                        {
                          offset: 0.5,
                          color: "#1f939c",
                        },
                        {
                          offset: 1,
                          color: "#0a3b4f",
                        },
                      ]),
                      barBorderRadius: 4,
                    },
                  },
                  data: qgkl,
                },
                {
                  name: "总客流",
                  type: "line",
                  barWidth: "15%",
                  smooth: true, //加这个
                  yAxisIndex: 1,
                  itemStyle: {
                    normal: {
                      color: "#6ae4b2",
                      barBorderRadius: 4,
                    },
                  },
                  data: klzl1,
                },
                {
                  name: "公交客流",
                  type: "line",
                  barWidth: "15%",
                  smooth: true, //加这个
                  yAxisIndex: 1,
                  itemStyle: {
                    normal: {
                      color: "#f5cc53",
                      barBorderRadius: 4,
                    },
                  },
                  data: gjkl1,
                },
                {
                  name: "地铁客流",
                  type: "line",
                  barWidth: "15%",
                  smooth: true, //加这个
                  yAxisIndex: 1,
                  itemStyle: {
                    normal: {
                      color: "#e86056",
                      barBorderRadius: 4,
                    },
                  },
                  data: qgkl1,
                },
              ],
            };
            myEc.setOption(option);
            myEc.getZr().on("mousemove", (param) => {
              myEc.getZr().setCursorStyle("default");
            });
          },
        },

        beforeDestory() {},
      });
    </script>
  </body>
</html>
