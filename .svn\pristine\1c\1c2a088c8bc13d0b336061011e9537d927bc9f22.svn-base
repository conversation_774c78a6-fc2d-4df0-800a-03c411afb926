<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>项目申报情况汇总</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/static/js/jslib/echarts-wordcloud.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <style>
            ::-webkit-scrollbar {
                width: 0 !important;
            }

            #app {
                width: 1800px;
                height: 1630px;
                background: url("/img/left-bg.png") no-repeat;
                background-size: 100% 100%;
            }

            .tabpar {
                margin-top: 40px;
                width: 90%;
                display: flex;
                margin-left: 5%;
            }

            .tab-c {
                padding: 8px 12px;
                background-color: #00396f;
                color: #fff;
                font-size: 28px;
                cursor: pointer;
                border-right: 1px solid #01a3dd;
                border: 1px solid #01a3dd;
                /* border-right: 0px solid #01a3dd; */
                /* flex: 1; */
            }

            .tab-a {
                background-color: #01a3dd;
            }

            .tab-a-no {
                background-color: #d9001b;
            }
        </style>
    </head>

    <body>
        <div id="app">
            <nav style="padding: 0px 0; margin-top: 10px">
                <s-header-title2 style="width: 100%" title="项目申报情况" htype="2"></s-header-title2>
            </nav>
            <div style="margin-top: 20px">
                <iframe :src="showSrc" frameborder="0" style="width: 90%; margin-left: 5%; height: 1360px"></iframe>
            </div>
            <div class="tabpar" v-if="isHg=='y'">
                <div
                    v-for="(item,i) in btnData"
                    :key="i"
                    class="tab-c"
                    :class="{'tab-a':tabnum==i}"
                    @click="clickTab(i)"
                >
                    {{item}}
                </div>
            </div>
            <div class="tabpar" v-else>
                <div
                    v-for="(item,i) in btnData"
                    :key="i"
                    class="tab-c"
                    :class="{'tab-a-no':tabnum==i}"
                    @click="clickTab(i)"
                >
                    {{item}}
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/$min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#app",
        data: {
            btnData: [],
            // 项目方案、投资预算、实施情况、安全管理、资料管理、工期进度
            tabnum: 0,
            isHg: "y",
            showSrc: "",
            pdfData: [],
        },
        created() {},
        mounted() {
            // $api("ldst_sdsjjldst_dzzwxm-009").then((res) => {
                $get("3840/dzzwxm/dzzwxm-009").then((res) => {
                this.pdfData = res;
                this.btnData = res.map((item) => {
                    return item.name;
                });
                this.tabPosition = this.btnData[0];
                this.showSrc = this.pdfData[0].src;
            });
        },
        methods: {
            clickTab(i) {
                this.tabnum = i;
                this.showSrc = this.pdfData[i].src;
            },
        },
    });
</script>
