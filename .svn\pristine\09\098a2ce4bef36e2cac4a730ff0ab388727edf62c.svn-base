<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Document</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <link rel="stylesheet" href="../css/sdqfw-left.css" />
    </head>

    <body>
        <div id="sdqfw-left">
            <div class="content">
                <!-- 水 -->
                <div class="title">
                    <nav style="padding: 20px 45px 0 45px">
                        <s-header-title style="width: 100%" title="水" htype="1">
                        </s-header-title>
                    </nav>
                </div>
                <div class="button-two" style="display: flex; width: 100%; height: 80px">
                    <el-button
                        type="primary"
                        style="
                            width: 200px;
                            height: 80px;
                            margin-left: 1260px;
                            font-size: 40px;
                        "
                        @click="supplyDialog1"
                    >
                        生产企业
                    </el-button>
                    <el-button
                        type="primary"
                        style="
                            width: 200px;
                            height: 80px;
                            margin-left: 20px;
                            font-size: 40px;
                        "
                        @click="supplyDialog2"
                    >
                        供应企业
                    </el-button>
                    <el-button
                        type="primary"
                        style="
                            width: 200px;
                            height: 80px;
                            margin-left: 20px;
                            font-size: 40px;
                        "
                        @click="priceDialog"
                    >
                        水价
                    </el-button>
                </div>
                <div class="title">
                    <nav style="padding: 0px 45px">
                        <s-header-title2 style="width: 100%" title="运行分析" htype="1"></s-header-title2>
                    </nav>
                </div>
                <div class="waterBox">
                    <div v-for="item in waterData">
                        <div>{{item.name}}</div>
                        <div class="value">
                            <span>{{item.value}}</span>
                            {{item.dw}}
                        </div>
                    </div>
                </div>

                <div class="titleWater">
                    <nav>
                        <s-header-title2 title="实时供水信息" htype="2"></s-header-title2>
                    </nav>
                    <nav>
                        <s-header-title2 title="用水量分析" htype="2"></s-header-title2>
                    </nav>
                </div>
                <div class="lineBox">
                    <div id="lineEcharts001" style="width: 1050px; height: 350px"></div>
                    <div id="lineEcharts002" style="width: 1050px; height: 350px"></div>
                </div>
                <div class="titleWater">
                    <nav>
                        <s-header-title2 title="水质" htype="2"></s-header-title2>
                    </nav>
                    <nav>
                        <s-header-title2 title="事件公告" htype="2"></s-header-title2>
                    </nav>
                </div>
                <div class="lineBox">
                    <div id="barEcharts001" style="width: 1050px; height: 350px"></div>
                    <div class="notice">
                        <div>
                            <div class="left">
                                <span>计划停水</span>
                            </div>
                            <div class="right" v-for="item in jhdsData">
                                <p>开始时间: <span>{{item.time}}</span></p>
                                <p>结束时间: <span>{{item.jstime}}</span></p>
                                <p>地点: <span>{{item.address}}</span></p>
                            </div>
                        </div>
                        <div>
                            <div class="left leftTwo">
                                <span>重大供水事件</span>
                            </div>
                            <div class="rightTwo" v-for="item in zdgsData">
                                <p>事件地点: <span>{{item.address}}</span></p>
                                <p>开始时间: <span>{{item.time}}</span></p>
                                <p>结束时间: <span>{{item.jstime}}</span></p>
                                <p>问题类型: <span>{{item.msg}}</span></p>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="title">
                    <nav style="padding: 0px 40px">
                        <s-header-title style="width: 100%" title="电" htype="1"></s-header-title>
                    </nav>
                </div>
                <div class="Dbox">
                    <div v-for="item in Ddata">
                        <div>{{item.name}}</div>
                        <div class="value">
                            <span>{{item.value}}</span>
                            {{item.dw}}
                        </div>
                    </div>
                </div>
                <div class="bottomBox">
                    <div>
                        <div class="bottomTitle">
                            <div
                                v-for="(item ,index) in timeList"
                                @click="changeEcharts(index)"
                                :class="{active:isActive===index}"
                            >
                                {{item}}
                            </div>
                        </div>
                        <div id="lineEcharts003" style="width: 1050px; height: 350px"></div>
                    </div>
                    <div id="barEcharts00" style="width: 1050px; height: 400px"></div>
                </div> -->
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    (function openIframe() {
        let iframe1 = {
            type: "openIframe",
            name: "sdqfw-middle1",
            src: baseURL.url + "/static/citybrain/ggfw/commont/sdqfw-middle1.html",
            width: "350px",
            height: "320px",
            left: "2190px",
            top: "215px",
            zIndex: "10",
        };
        window.parent.postMessage(JSON.stringify(iframe1), "*");
    })();
    (function openIframe1() {
        let iframe1 = {
            type: "openIframe",
            name: "sdqfw-dialog2",
            src: baseURL.url + "/static/citybrain/ggfw/pages/sdqfw-dialog2.html",
            width: "1810px",
            height: "120px",
            left: "2940px",
            top: "215px",
            zIndex: "555",
        };
        window.parent.postMessage(JSON.stringify(iframe1), "*");
    })();
    var vm = new Vue({
        el: "#sdqfw-left",
        data: {
            waterData: [],
            Ddata: [],
            jhdsData: [],
            zdgsData: [],
            isActive: 0,
            timeList: ["年度", "季度", "月份"],
        },
        mounted() {
            this.initFun();
        },
        methods: {
            initFun() {
                $api("sdqfw_left001").then((res) => {
                    this.waterData = res;
                });
                $api("sdqfw_left002").then((res) => {
                    this.getEcharts01("lineEcharts001", res);
                });
                $api("sdqfw_left003").then((res) => {
                    this.getEcharts02("lineEcharts002", res);
                });
                $api("sdqfw_left004").then((res) => {
                    this.getEcharts03("barEcharts001", res);
                });
                $api("sdqfw_left005").then((res) => {
                    this.Ddata = res;
                });
                $api("sdqfw_left006").then((res) => {
                    this.getEcharts04("lineEcharts003", res);
                });
                $api("sdqfw_left009").then((res) => {
                    this.getEcharts05("barEcharts00", res);
                });
                $api("sdqfw_left010").then((res) => {
                    this.jhdsData = res;
                });
                $api("sdqfw_left011").then((res) => {
                    this.zdgsData = res;
                });
            },
            supplyDialog1(){
                let diaog = {
                    type: 'openIframe',
                    name: 'supply-dialog1',
                    src: baseURL.url + '/static/citybrain/ggfw/commont/supply-dialog1.html',
                    left: "calc(50% - 700px)",
                    top: "25%",
                    width: "1560px",
                    height: "980px",
                    zIndex:"10",
                    argument: {
                        status:""
                    }
                }
                top.window.parent.postMessage(JSON.stringify(diaog), '*')
            },
            supplyDialog2(){
                let diaog = {
                    type: 'openIframe',
                    name: 'supply-dialog2',
                    src: baseURL.url + '/static/citybrain/ggfw/commont/supply-dialog2.html',
                    left: "calc(50% - 700px)",
                    top: "25%",
                    width: "1560px",
                    height: "980px",
                    zIndex:"10",
                    argument: {
                        status:""
                    }
                }
                top.window.parent.postMessage(JSON.stringify(diaog), '*')
            },
            priceDialog(){
                let diaog = {
                    type: 'openIframe',
                    name: 'price-dialog',
                    src: baseURL.url + '/static/citybrain/ggfw/commont/price-dialog.html',
                    left: "calc(50% - 700px)",
                    top: "25%",
                    width: "1560px",
                    height: "980px",
                    zIndex:"10",
                    argument: {
                        status:""
                    }
                }
                top.window.parent.postMessage(JSON.stringify(diaog), '*')
            },
            getEcharts01(dom, echartData) {
                let echarts0 = echarts.init(document.getElementById(dom));

                const xAxisData = echartData.map((item) => {
                    return item.time;
                });
                const yData = echartData.map((item) => {
                    return item.value;
                });
                const yData1 = echartData.map((item) => {
                    return item.value1;
                });
                const yData2 = echartData.map((item) => {
                    return item.value2;
                });
                let option = {
                    textStyle: {
                        fontFamily: "Din-Light",
                    },
                    legend: {
                        data: [
                            {
                                name: "供水量",
                                icon: "path://M512 139.81262864a286.42534744 286.42534744 0 1 0 286.42534744 286.42534744 286.42534744 286.42534744 0 0 0-286.42534744-286.42534744z m0 477.3755789a190.95023144 190.95023144 0 1 1 190.95023144-190.95023146 190.95023144 190.95023144 0 0 1-190.95023144 190.95023146z",
                            },
                            {
                                name: "用水量",
                                icon: "path://M512 139.81262864a286.42534744 286.42534744 0 1 0 286.42534744 286.42534744 286.42534744 286.42534744 0 0 0-286.42534744-286.42534744z m0 477.3755789a190.95023144 190.95023144 0 1 1 190.95023144-190.95023146 190.95023144 190.95023144 0 0 1-190.95023144 190.95023146z",
                            },
                            {
                                name: "管道水压",
                                icon: "path://M512 139.81262864a286.42534744 286.42534744 0 1 0 286.42534744 286.42534744 286.42534744 286.42534744 0 0 0-286.42534744-286.42534744z m0 477.3755789a190.95023144 190.95023144 0 1 1 190.95023144-190.95023146 190.95023144 190.95023144 0 0 1-190.95023144 190.95023146z",
                            },
                        ],
                        selected: {
                            供水量: true,
                            用水量: true,
                            管道水压: true,
                        },
                        itemWidth: 20,
                        itemHeight: 20,
                        itemGap: 30,
                        textStyle: {
                            color: "#fff",
                            lineHeight: 15,
                            fontSize: 30,
                        },
                        type: "scroll",
                    },
                    tooltip: {
                        backgroundColor: "#fff05",
                        trigger: "axis",
                        axisPointer: {
                            type: "none",
                        },
                        textStyle: {
                            color: "#ffff",
                            lineHeight: 28,
                            fontSize: 30,
                        },
                        confine: true,
                        padding: 12,
                    },
                    grid: {
                        // right: 0,
                        bottom: 100,
                    },
                    xAxis: {
                        type: "category",
                        boundaryGap: true,
                        offset: 5,
                        data: xAxisData,
                        axisLabel: {
                            // interval: 3,
                            align: "left",
                            color: "#fff",
                            fontSize: 30,
                        },
                        axisLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },
                    },
                    yAxis: {
                        type: "value",
                        min: 0,
                        max: 1600,
                        interval: 400,
                        axisLabel: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        splitLine: {
                            lineStyle: {
                                color: "#19365f",
                            },
                        },
                        axisLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },
                    },
                    series: [
                        {
                            name: "供水量",
                            data: yData,
                            type: "line",
                            smooth: true,
                            smoothMonotone: "x",
                            cursor: "pointer",
                            showSymbol: false,
                            lineStyle: {
                                shadowColor: "rgba(18,61,172,0.5)",
                                color: "#0398d1",
                                shadowBlur: 10,
                            },
                        },
                        {
                            name: "用水量",
                            data: yData1,
                            type: "line",
                            smooth: true,
                            smoothMonotone: "x",
                            cursor: "pointer",
                            showSymbol: false,
                            lineStyle: {
                                shadowColor: "rgba(115,226,226,0.5)",
                                shadowBlur: 10,
                                color: "#b7955a",
                            },
                        },
                        {
                            name: "管道水压",
                            data: yData2,
                            type: "line",
                            smooth: true,
                            smoothMonotone: "x",
                            cursor: "pointer",
                            showSymbol: false,
                            lineStyle: {
                                shadowColor: "rgba(255,126,133,0.5)",
                                shadowBlur: 10,
                                color: "#43bd7f",
                            },
                        },
                    ],
                };
                echarts0.setOption(option);
            },
            getEcharts02(dom, echartData) {
                let echarts1 = echarts.init(document.getElementById(dom));

                const xAxisData = echartData.map((item) => {
                    return item.time;
                });
                const yData = echartData.map((item) => {
                    return item.value;
                });
                const yData1 = echartData.map((item) => {
                    return item.value1;
                });
                const yData2 = echartData.map((item) => {
                    return item.value2;
                });
                let option = {
                    textStyle: {
                        fontFamily: "Din-Light",
                    },
                    legend: {
                        data: [
                            {
                                name: "居民生活用水量",
                                icon: "path://M512 139.81262864a286.42534744 286.42534744 0 1 0 286.42534744 286.42534744 286.42534744 286.42534744 0 0 0-286.42534744-286.42534744z m0 477.3755789a190.95023144 190.95023144 0 1 1 190.95023144-190.95023146 190.95023144 190.95023144 0 0 1-190.95023144 190.95023146z",
                            },
                            {
                                name: "非居民用水量",
                                icon: "path://M512 139.81262864a286.42534744 286.42534744 0 1 0 286.42534744 286.42534744 286.42534744 286.42534744 0 0 0-286.42534744-286.42534744z m0 477.3755789a190.95023144 190.95023144 0 1 1 190.95023144-190.95023146 190.95023144 190.95023144 0 0 1-190.95023144 190.95023146z",
                            },
                            {
                                name: "特种行业用水量",
                                icon: "path://M512 139.81262864a286.42534744 286.42534744 0 1 0 286.42534744 286.42534744 286.42534744 286.42534744 0 0 0-286.42534744-286.42534744z m0 477.3755789a190.95023144 190.95023144 0 1 1 190.95023144-190.95023146 190.95023144 190.95023144 0 0 1-190.95023144 190.95023146z",
                            },
                        ],
                        selected: {
                            居民生活用水量: true,
                            非居民用水量: true,
                            特种行业用水量: true,
                        },
                        itemWidth: 20,
                        itemHeight: 20,
                        itemGap: 30,
                        textStyle: {
                            color: "#fff",
                            lineHeight: 15,
                            fontSize: 30,
                        },
                        type: "scroll",
                    },
                    tooltip: {
                        backgroundColor: "#fff05",
                        trigger: "axis",
                        axisPointer: {
                            type: "none",
                        },
                        textStyle: {
                            color: "#ffff",
                            lineHeight: 28,
                            fontSize: 30,
                        },
                        confine: true,
                        padding: 12,
                    },
                    grid: {
                        // right: 0,
                        bottom: 100,
                    },
                    xAxis: {
                        type: "category",
                        boundaryGap: true,
                        offset: 5,
                        data: xAxisData,
                        axisLabel: {
                            interval: 0,
                            align: "left",
                            color: "#fff",
                            fontSize: 30,
                        },
                        axisLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },
                    },
                    yAxis: {
                        type: "value",
                        min: 0,
                        max: 1600,
                        interval: 400,
                        axisLabel: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        splitLine: {
                            lineStyle: {
                                color: "#19365f",
                            },
                        },
                        axisLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },
                    },
                    series: [
                        {
                            name: "居民生活用水量",
                            data: yData,
                            type: "line",
                            smooth: true,
                            smoothMonotone: "x",
                            cursor: "pointer",
                            showSymbol: false,
                            lineStyle: {
                                shadowColor: "rgba(18,61,172,0.5)",
                                color: "#0398d1",
                                shadowBlur: 10,
                            },
                        },
                        {
                            name: "非居民用水量",
                            data: yData1,
                            type: "line",
                            smooth: true,
                            smoothMonotone: "x",
                            cursor: "pointer",
                            showSymbol: false,
                            lineStyle: {
                                shadowColor: "rgba(115,226,226,0.5)",
                                shadowBlur: 10,
                                color: "#b7955a",
                            },
                        },
                        {
                            name: "特种行业用水量",
                            data: yData2,
                            type: "line",
                            smooth: true,
                            smoothMonotone: "x",
                            cursor: "pointer",
                            showSymbol: false,
                            lineStyle: {
                                shadowColor: "rgba(255,126,133,0.5)",
                                shadowBlur: 10,
                                color: "#43bd7f",
                            },
                        },
                    ],
                };
                echarts1.setOption(option);
            },
            getEcharts03(dom, echartData) {
                let echarts2 = echarts.init(document.getElementById(dom));
                let xData = echartData.map((item) => {
                    return item.time;
                });
                let yData = echartData.map((item) => {
                    return item.value;
                });
                let yData1 = echartData.map((item) => {
                    return item.value1;
                });
                let yData2 = echartData.map((item) => {
                    return item.value2;
                });
                let yData3 = echartData.map((item) => {
                    return item.value3;
                });
                let option = {
                    tooltip: {
                        trigger: "axis",
                        fontSize: 30,
                        textStyle: {
                            fontSize: 30,
                        },
                        axisPointer: {
                            type: "shadow",
                            textStyle: {
                                color: "#fff",
                            },
                        },
                    },
                    legend: {
                        textStyle: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        icon: "square",
                        itemWidth: 30,
                        itemHeight: 30,
                    },
                    grid: {
                        borderWidth: 0,
                        top: 110,
                        bottom: 95,
                        textStyle: {
                            color: "#fff",
                        },
                    },
                    calculable: true,
                    xAxis: [
                        {
                            type: "category",
                            offset: 10,
                            axisLine: {
                                lineStyle: {
                                    color: "#19416b",
                                },
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            splitArea: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                color: "rgba(255,255,255,0.7)",
                                fontSize: 30,
                            },
                            data: xData,
                        },
                    ],
                    yAxis: [
                        {
                            type: "value",
                            min: 0,
                            max: 16,
                            interval: 4,
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#19416b",
                                },
                            },
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                color: "rgba(255,255,255,0.5)",
                                fontSize: 30,
                            },
                        },
                    ],
                    series: [
                        {
                            name: "I类",
                            type: "bar",
                            stack: "总量",
                            barMaxWidth: 35,
                            barGap: "10%",
                            itemStyle: {
                                normal: {
                                    color: {
                                        type: "linear",
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [
                                            {
                                                offset: 0,
                                                color: "rgba(64, 188, 241,1)", // 0% 处的颜色
                                            },
                                            {
                                                offset: 1,
                                                color: "rgba(64, 188, 241, 0)", // 100% 处的颜色
                                            },
                                        ],
                                        global: false, // 缺省为 false
                                    },
                                },
                            },
                            data: yData,
                        },
                        {
                            name: "II类",
                            type: "bar",
                            stack: "总量",
                            itemStyle: {
                                normal: {
                                    color: {
                                        type: "linear",
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [
                                            {
                                                offset: 0,
                                                color: "rgba(227, 208, 128, 1)", // 0% 处的颜色
                                            },
                                            {
                                                offset: 1,
                                                color: "rgba(227, 208, 128, 0)", // 100% 处的颜色
                                            },
                                        ],
                                        global: false, // 缺省为 false
                                    },
                                    barBorderRadius: 0,
                                },
                            },
                            data: yData1,
                        },
                        {
                            name: "III类",
                            type: "bar",
                            stack: "总量",
                            itemStyle: {
                                normal: {
                                    color: {
                                        type: "linear",
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [
                                            {
                                                offset: 0,
                                                color: "rgba(62, 233, 176, 1)", // 0% 处的颜色
                                            },
                                            {
                                                offset: 1,
                                                color: "rgba(62, 233, 176, 0)", // 100% 处的颜色
                                            },
                                        ],
                                        global: false, // 缺省为 false
                                    },
                                    barBorderRadius: 0,
                                },
                            },
                            data: yData2,
                        },
                        {
                            name: "IV类",
                            type: "bar",
                            stack: "总量",
                            itemStyle: {
                                normal: {
                                    color: {
                                        type: "linear",
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [
                                            {
                                                offset: 0,
                                                color: "rgba(243, 120, 39, 1)", // 0% 处的颜色
                                            },
                                            {
                                                offset: 1,
                                                color: "rgba(243, 120, 39, 0)", // 100% 处的颜色
                                            },
                                        ],
                                        global: false, // 缺省为 false
                                    },
                                    barBorderRadius: 0,
                                },
                            },
                            data: yData3,
                        },
                    ],
                };

                echarts2.setOption(option);
            },
            getEcharts04(dom, echartData) {
                let echarts4 = echarts.init(document.getElementById(dom));

                const xAxisData = echartData.map((item) => {
                    return item.time;
                });
                const yData = echartData.map((item) => {
                    return item.value;
                });

                let option = {
                    textStyle: {
                        fontFamily: "Din-Light",
                    },
                    legend: {
                        data: [
                            {
                                name: "累计行业用电量",
                                icon: "path://M512 139.81262864a286.42534744 286.42534744 0 1 0 286.42534744 286.42534744 286.42534744 286.42534744 0 0 0-286.42534744-286.42534744z m0 477.3755789a190.95023144 190.95023144 0 1 1 190.95023144-190.95023146 190.95023144 190.95023144 0 0 1-190.95023144 190.95023146z",
                            },
                        ],
                        selected: {
                            累计行业用电量: true,
                        },
                        itemWidth: 20,
                        itemHeight: 20,
                        itemGap: 30,
                        textStyle: {
                            color: "#fff",
                            lineHeight: 15,
                            fontSize: 30,
                        },
                        type: "scroll",
                    },
                    tooltip: {
                        backgroundColor: "#fff05",
                        trigger: "axis",
                        axisPointer: {
                            type: "none",
                        },
                        textStyle: {
                            color: "#ffff",
                            lineHeight: 28,
                            fontSize: 30,
                        },
                        confine: true,
                        padding: 12,
                    },
                    grid: {
                        // right: 0,
                        bottom: 100,
                    },
                    xAxis: {
                        type: "category",
                        boundaryGap: true,
                        offset: 5,
                        data: xAxisData,
                        axisLabel: {
                            interval: 0,
                            align: "left",
                            color: "#fff",
                            fontSize: 30,
                        },
                        axisLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },
                    },
                    yAxis: {
                        type: "value",
                        min: 0,
                        max: 1600,
                        interval: 400,
                        axisLabel: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        splitLine: {
                            lineStyle: {
                                color: "#19365f",
                            },
                        },
                        axisLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },
                    },
                    series: [
                        {
                            name: "累计行业用电量",
                            data: yData,
                            type: "line",
                            smooth: true,
                            smoothMonotone: "x",
                            cursor: "pointer",
                            showSymbol: false,
                            lineStyle: {
                                shadowColor: "rgba(18,61,172,0.5)",
                                color: "#0398d1",
                                shadowBlur: 10,
                            },
                        },
                    ],
                };
                echarts4.setOption(option);
            },
            changeEcharts(index) {
                this.isActive = index;
                if (this.isActive === 0) {
                    $api("sdqfw_left006").then((res) => {
                        this.getEcharts04("lineEcharts003", res);
                    });
                } else if (this.isActive === 1) {
                    $api("sdqfw_left007").then((res) => {
                        this.getEcharts04("lineEcharts003", res);
                    });
                } else {
                    $api("sdqfw_left008").then((res) => {
                        this.getEcharts04("lineEcharts003", res);
                    });
                }
            },
            getEcharts05(dom, echartData) {
                let echarts5 = echarts.init(document.getElementById(dom));

                var datas = echartData;
                let maxArr = new Array(datas.length).fill(200);
                let option = {
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "shadow",
                        },
                        textStyle: {
                            fontSize: "30",
                        },
                    },
                    legend: {
                        show: false,
                    },
                    grid: {
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        containLabel: true,
                    },
                    xAxis: {
                        show: false,
                        type: "value",
                    },
                    yAxis: [
                        {
                            type: "category",
                            inverse: true,
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisPointer: {
                                label: {
                                    show: true,
                                    margin: 30,
                                },
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#FFF",
                                    fontSize: 30,
                                },
                            },
                            data: datas.map((item) => item.time),
                        },
                        {
                            type: "category",
                            inverse: true,
                            axisTick: "none",
                            axisLine: "none",
                            show: true,
                            data: datas.map((item) => item.value),
                            axisLabel: {
                                show: true,
                                formatter: function (value) {
                                    let a = value + "kV";
                                    return "{a|" + a + "}";
                                },
                                rich: {
                                    a: {
                                        color: "#35a3d5",
                                        fontSize: 30,
                                        fontWeight: "bold",
                                    },
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            z: 2,
                            type: "bar",
                            barWidth: 25,
                            zlevel: 1,
                            data: datas.map((item, i) => {
                                itemStyle = {
                                    color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                        {
                                            offset: 0,
                                            color: "#1f71a2",
                                        },
                                        {
                                            offset: 1,
                                            color: "#41c1f6",
                                        },
                                    ]),
                                };
                                return {
                                    value: item.value,
                                    itemStyle: itemStyle,
                                };
                            }),
                            label: {
                                show: false,
                                position: "right",
                                color: "#333333",
                                fontSize: 14,
                                offset: [10, 0],
                            },
                        },
                        {
                            type: "bar",
                            barWidth: 25,
                            barGap: "-100%",
                            itemStyle: {
                                normal: {
                                    color: "#052559",
                                },
                            },
                            data: maxArr,
                        },
                    ],
                };
                echarts5.setOption(option);
            },
        },
    });
</script>
