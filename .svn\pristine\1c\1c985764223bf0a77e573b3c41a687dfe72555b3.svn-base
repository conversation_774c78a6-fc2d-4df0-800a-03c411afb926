{"editCanvasConfig": {"width": 2070, "height": 1900, "hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "background": null, "backgroundImage": "/src/assets/images/canvas/left-bg.png", "selectColor": false, "chartThemeColor": "lingt", "chartThemeSetting": {"title": {"show": true, "textStyle": {"color": "#BFBFBF", "fontSize": 18}, "subtextStyle": {"color": "#A2A2A2", "fontSize": 14}}, "xAxis": {"show": true, "name": "", "nameGap": 15, "nameTextStyle": {"color": "#B9B8CE", "fontSize": 12}, "inverse": false, "axisLabel": {"show": true, "fontSize": 12, "color": "#B9B8CE"}, "position": "bottom", "axisLine": {"lineStyle": {"color": "#B9B8CE", "width": 1}, "onZero": true}, "axisTick": {"show": true, "length": 5}, "splitLine": {"show": false, "lineStyle": {"color": "#484753", "width": 1, "type": "solid"}}}, "yAxis": {"show": true, "name": "", "nameGap": 15, "nameTextStyle": {"color": "#B9B8CE", "fontSize": 12}, "inverse": false, "axisLabel": {"show": true, "fontSize": 12, "color": "#B9B8CE"}, "position": "left", "axisLine": {"show": true, "lineStyle": {"color": "#B9B8CE", "width": 1}, "onZero": true}, "axisTick": {"show": true, "length": 5}, "splitLine": {"show": true, "lineStyle": {"color": "#484753", "width": 1, "type": "solid"}}}, "legend": {"show": true, "top": "5%", "textStyle": {"color": "#B9B8CE"}}, "dataset": null}, "previewScaleType": "fit"}, "componentList": [{"id": "ybthllyth8w000", "isGroup": false, "attr": {"x": 1067, "y": 576, "w": 924, "h": 110, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "animations": []}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestInterval": null, "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "filter": null, "key": "Title01", "chartConfig": {"key": "Title01", "chartKey": "VTitle01", "conKey": "VCTitle01", "title": "标题-01", "category": "Titles", "categoryName": "标题", "package": "Informations", "chartFrame": "naiveUI", "image": "/src/assets/images/chart/informations/title_01.png"}, "option": {"dataset": {"title": "标题", "date": "2022年2月2日"}, "clickFnSting": "", "width1": 1934, "width2": 942}}, {"id": "dvvtsjd55oo0000", "isGroup": false, "attr": {"x": 57, "y": 577, "w": 924, "h": 110, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "animations": []}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestInterval": null, "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "filter": null, "key": "Title01", "chartConfig": {"key": "Title01", "chartKey": "VTitle01", "conKey": "VCTitle01", "title": "标题-01", "category": "Titles", "categoryName": "标题", "package": "Informations", "chartFrame": "naiveUI", "image": "/src/assets/images/chart/informations/title_01.png"}, "option": {"dataset": {"title": "标题", "date": "2022年2月2日"}, "clickFnSting": "window.parent.postMessage(\r\n                  JSON.stringify({\r\n                    type: 'openMenu',\r\n                    data:'zdxm'\r\n                  }),\r\n                  '*'\r\n                )", "width1": 1934, "width2": 942}}, {"id": "2j1cw5sldz20000", "isGroup": false, "attr": {"x": 57, "y": 1250, "w": 1934, "h": 110, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "animations": []}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestInterval": null, "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "filter": null, "key": "Title01", "chartConfig": {"key": "Title01", "chartKey": "VTitle01", "conKey": "VCTitle01", "title": "标题-01", "category": "Titles", "categoryName": "标题", "package": "Informations", "chartFrame": "naiveUI", "image": "/src/assets/images/chart/informations/title_01.png"}, "option": {"dataset": {"title": "重点项目", "date": "2022年2月2日"}, "clickFnSting": "window.parent.postMessage(\r\n                  JSON.stringify({\r\n                    type: 'openMenu',\r\n                    data:'zdxm'\r\n                  }),\r\n                  '*'\r\n                )\r\n", "width1": 1934, "width2": 942}}, {"id": "6gp16rhn3tc0000", "isGroup": false, "attr": {"x": 55, "y": 30, "w": 1934, "h": 110, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "animations": []}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestInterval": null, "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "filter": null, "key": "Title01", "chartConfig": {"key": "Title01", "chartKey": "VTitle01", "conKey": "VCTitle01", "title": "标题-01", "category": "Titles", "categoryName": "标题", "package": "Informations", "chartFrame": "naiveUI", "image": "/src/assets/images/chart/informations/title_01.png"}, "option": {"dataset": {"title": "标题", "date": "2022年2月2日"}, "clickFnSting": "", "width1": 1934, "width2": 942}}, {"id": "7lm1utv0kn40000", "isGroup": false, "attr": {"x": 257, "y": 218, "w": 500, "h": 300, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "animations": []}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestInterval": null, "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "filter": null, "key": "Bar<PERSON><PERSON>mon", "chartConfig": {"key": "Bar<PERSON><PERSON>mon", "chartKey": "VBarCommon", "conKey": "VCBarCommon", "title": "柱状图", "category": "Bars", "categoryName": "柱状图", "package": "Charts", "chartFrame": "echarts", "image": "/src/assets/images/chart/charts/bar_x.png"}, "option": {"legend": {"show": true, "top": "5%", "textStyle": {"color": "#B9B8CE"}}, "xAxis": {"show": true, "name": "", "nameGap": 15, "nameTextStyle": {"color": "#B9B8CE", "fontSize": 12}, "inverse": false, "axisLabel": {"show": true, "fontSize": 12, "color": "#B9B8CE"}, "position": "bottom", "axisLine": {"lineStyle": {"color": "#B9B8CE", "width": 1}, "onZero": true}, "axisTick": {"show": true, "length": 5}, "splitLine": {"show": false, "lineStyle": {"color": "#484753", "width": 1, "type": "solid"}}, "type": "category"}, "yAxis": {"show": true, "name": "", "nameGap": 15, "nameTextStyle": {"color": "#B9B8CE", "fontSize": 12}, "inverse": false, "axisLabel": {"show": true, "fontSize": 12, "color": "#B9B8CE"}, "position": "left", "axisLine": {"show": true, "lineStyle": {"color": "#B9B8CE", "width": 1}, "onZero": true}, "axisTick": {"show": true, "length": 5}, "splitLine": {"show": true, "lineStyle": {"color": "#484753", "width": 1, "type": "solid"}}, "type": "value"}, "tooltip": {"show": true, "trigger": "axis", "axisPointer": {"show": true, "type": "shadow"}}, "dataset": {"dimensions": ["product", "data1", "data2"], "source": [{"product": "Mon", "data1": 120, "data2": 130}, {"product": "<PERSON><PERSON>", "data1": 200, "data2": 130}, {"product": "Wed", "data1": 150, "data2": 312}, {"product": "<PERSON>hu", "data1": 80, "data2": 268}, {"product": "<PERSON><PERSON>", "data1": 70, "data2": 155}, {"product": "Sat", "data1": 110, "data2": 117}, {"product": "Sun", "data1": 130, "data2": 160}]}, "series": [{"type": "bar", "barWidth": null, "itemStyle": {"color": null, "borderRadius": 0}}, {"type": "bar", "barWidth": null, "itemStyle": {"color": null, "borderRadius": 0}}], "backgroundColor": "rgba(0,0,0,0)"}}, {"id": "eddbb351y0g0000", "isGroup": false, "attr": {"x": 1208, "y": 204, "w": 500, "h": 300, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "animations": []}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestInterval": null, "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "filter": null, "key": "PieCommon", "chartConfig": {"key": "PieCommon", "chartKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "conKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "饼图", "category": "Pies", "categoryName": "饼图", "package": "Charts", "chartFrame": "echarts", "image": "/src/assets/images/chart/charts/pie.png"}, "option": {"legend": {"show": true, "top": "5%", "textStyle": {"color": "#B9B8CE"}}, "tooltip": {"show": true, "trigger": "item"}, "dataset": {"dimensions": ["product", "data1"], "source": [{"product": "Mon", "data1": 120}, {"product": "<PERSON><PERSON>", "data1": 200}, {"product": "Wed", "data1": 150}, {"product": "<PERSON>hu", "data1": 80}, {"product": "<PERSON><PERSON>", "data1": 70}, {"product": "Sat", "data1": 110}, {"product": "Sun", "data1": 130}]}, "series": [{"type": "pie", "radius": ["40%", "65%"], "center": ["50%", "60%"], "avoidLabelOverlap": false, "itemStyle": {"show": true, "borderRadius": 10, "borderColor": "#fff", "borderWidth": 2}, "label": {"show": false, "position": "center"}, "emphasis": {"label": {"show": true, "fontSize": "40", "fontWeight": "bold"}}, "labelLine": {"show": false}}], "backgroundColor": "rgba(0,0,0,0)"}}, {"id": "g7xp948p6o80000", "isGroup": false, "attr": {"x": 244, "y": 800, "w": 500, "h": 300, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "animations": []}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestInterval": null, "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "filter": null, "key": "PieCircle", "chartConfig": {"key": "PieCircle", "chartKey": "VPieCircle", "conKey": "VCPieCircle", "title": "饼图-环形", "category": "Pies", "categoryName": "饼图", "package": "Charts", "chartFrame": "echarts", "image": "/src/assets/images/chart/charts/pie-circle.png"}, "option": {"tooltip": {"show": true, "trigger": "item"}, "legend": {"show": true}, "dataset": 0.25, "title": {"text": "25.00%", "x": "center", "y": "center", "textStyle": {"color": "#56B9F8", "fontSize": 30}}, "series": [{"type": "pie", "radius": ["75%", "80%"], "center": ["50%", "50%"], "hoverAnimation": true, "color": ["#00bcd44a", "transparent"], "label": {"show": false}, "data": [{"value": [25], "itemStyle": {"color": "#03a9f4", "shadowBlur": 10, "shadowColor": "#97e2f5"}}, {"value": [75], "itemStyle": {"color": "#00bcd44a", "shadowBlur": 0, "shadowColor": "#00bcd44a"}}]}], "backgroundColor": "rgba(0,0,0,0)"}}, {"id": "1342x3uhbbr4000", "isGroup": false, "attr": {"x": 106, "y": 1482, "w": 1803, "h": 344, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "animations": []}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestInterval": null, "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "filter": null, "key": "LineCommon", "chartConfig": {"key": "LineCommon", "chartKey": "VLineCommon", "conKey": "VCLineCommon", "title": "折线图", "category": "Lines", "categoryName": "折线图", "package": "Charts", "chartFrame": "echarts", "image": "/src/assets/images/chart/charts/line.png"}, "option": {"legend": {"show": true, "top": "5%", "textStyle": {"color": "#B9B8CE"}}, "xAxis": {"show": true, "name": "", "nameGap": 15, "nameTextStyle": {"color": "#B9B8CE", "fontSize": 12}, "inverse": false, "axisLabel": {"show": true, "fontSize": 12, "color": "#B9B8CE"}, "position": "bottom", "axisLine": {"lineStyle": {"color": "#B9B8CE", "width": 1}, "onZero": true}, "axisTick": {"show": true, "length": 5}, "splitLine": {"show": false, "lineStyle": {"color": "#484753", "width": 1, "type": "solid"}}, "type": "category"}, "yAxis": {"show": true, "name": "", "nameGap": 15, "nameTextStyle": {"color": "#B9B8CE", "fontSize": 12}, "inverse": false, "axisLabel": {"show": true, "fontSize": 12, "color": "#B9B8CE"}, "position": "left", "axisLine": {"show": true, "lineStyle": {"color": "#B9B8CE", "width": 1}, "onZero": true}, "axisTick": {"show": true, "length": 5}, "splitLine": {"show": true, "lineStyle": {"color": "#484753", "width": 1, "type": "solid"}}, "type": "value"}, "tooltip": {"show": true, "trigger": "axis", "axisPointer": {"type": "line"}}, "dataset": {"dimensions": ["product", "data1"], "source": [{"product": "Mon", "data1": 120}, {"product": "<PERSON><PERSON>", "data1": 200}, {"product": "Wed", "data1": 150}, {"product": "<PERSON>hu", "data1": 80}, {"product": "<PERSON><PERSON>", "data1": 70}, {"product": "Sat", "data1": 110}, {"product": "Sun", "data1": 130}]}, "series": [{"type": "line", "lineStyle": {"type": "solid", "width": 3, "color": {"type": "linear", "colorStops": [{"offset": 0, "color": "#4992ff"}, {"offset": 1, "color": "#7cffb2"}], "globalCoord": false}, "shadowColor": "rgba(68, 181, 226, 0.3)", "shadowBlur": 10, "shadowOffsetY": 20}}], "backgroundColor": "rgba(0,0,0,0)"}}, {"id": "1uy3fzmrivpc000", "isGroup": false, "attr": {"x": 1252, "y": 824, "w": 500, "h": 300, "offsetX": 0, "offsetY": 0, "zIndex": -1}, "styles": {"hueRotate": 0, "saturate": 1, "contrast": 1, "brightness": 1, "opacity": 1, "rotateZ": 0, "rotateX": 0, "rotateY": 0, "skewX": 0, "skewY": 0, "animations": []}, "request": {"requestDataType": 0, "requestHttpType": "get", "requestUrl": "", "requestInterval": null, "requestIntervalUnit": "second", "requestContentType": 0, "requestParamsBodyType": "none", "requestSQLContent": {"sql": "select * from  where"}, "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}, "filter": null, "key": "WaterPolo", "chartConfig": {"key": "WaterPolo", "chartKey": "VWaterPolo", "conKey": "VCWaterPolo", "title": "水球图", "category": "Mores", "categoryName": "更多", "package": "Charts", "chartFrame": "echarts", "image": "/src/assets/images/chart/charts/water_WaterPolo.png"}, "option": {"dataset": 0.5, "series": [{"type": "liquidFill", "shape": "circle", "radius": "90%", "data": [0.5], "center": ["50%", "50%"], "color": [{"type": "linear", "x": 0, "y": 0, "x2": 0, "y2": 1, "colorStops": [{"offset": 0, "color": "#4992ff"}, {"offset": 1, "color": "#7cffb2"}], "globalCoord": false}], "backgroundStyle": {"borderWidth": 1, "color": "rgba(68, 181, 226, 0.3)"}, "label": {"normal": {"textStyle": {"fontSize": 50, "color": "#fff"}}}, "outline": {"show": false, "borderDistance": 10, "itemStyle": {"borderWidth": 2, "borderColor": "#112165"}}}], "backgroundColor": "rgba(0,0,0,0)"}}], "requestGlobalConfig": {"requestOriginUrl": "", "requestInterval": 30, "requestIntervalUnit": "second", "requestParams": {"Body": {"form-data": {}, "x-www-form-urlencoded": {}, "json": "", "xml": ""}, "Header": {}, "Params": {}}}}