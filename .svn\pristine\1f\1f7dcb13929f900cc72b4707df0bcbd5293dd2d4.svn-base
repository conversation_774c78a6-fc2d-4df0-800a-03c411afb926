<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title></title>
        <script src="/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain3840/scjg/css/sczt-right.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <!-- 轮播toolTip -->
    </head>

    <body>
        <div id="app" class="container" v-cloak>
            <nav>
                <s-header-title-2 htype="1" title="市场主体注册资本规模"></s-header-title-2>
            </nav>
            <div class="table table1">
                <div class="th">
                    <div class="th_td" style="flex: 0.45" v-for="(item,index) in theadList" :key="index">{{item}}</div>
                </div>
                <div class="tbody" id="tbody1">
                    <div class="tr" v-for="(item ,i) in tbodyList" :key="i">
                        <div class="tr_td" style="flex: 0.45">{{item.name}}</div>
                        <div class="tr_td" style="flex: 0.45">{{item.state}}</div>
                        <div class="tr_td" style="flex: 0.45">{{item.time}}</div>
                    </div>
                </div>
            </div>
            <div id="sczt-chart1"></div>
            <nav>
                <s-header-title-2 htype="1" title="市场主体投诉情况"></s-header-title-2>
            </nav>
            <div id="sczt-chart2"></div>
            <div id="sczt-chart3"></div>
        </div>
    </body>
    <script type="module">
        new Vue({
            el: "#app",
            data: {
                nowTime: "", //当前时间
                theadList: ["道路", "路况", "时间"],
                tbodyList: [
                    {
                        name: "八达路",
                        zs: "1.456",
                        sd: "27.28km/h",
                    },
                ],
            },
            methods: {
                init() {
                    // $api("ldst_scjg_sczt", { type: "jtys07" }).then((res) => {
                    //     this.tbodyList = res;
                    // });
                    // $api("ldst_scjg_sczt", { type: 5 }).then((res) => {
                    // this.barchartsShow(
                    //     "sczt-chart1",
                    //     "内资企业",
                    //     "外商投资企业",
                    //     res,
                    //     "个体工商户",
                    //     "农民专业合作社"
                    // );
                    // });
                    // $api("ldst_scjg_sczt", { type: 7 }).then((res) => {
                    //     this.LinechartsShow("sczt-chart3", "市场主体总数", "同比增幅", res);
                    // });
                    // $api("ldst_scjg_sczt", { type: 8 }).then((res) => {
                    // this.LinechartsShow("sczt-chart2", "户数", "增长率", res);
                    // });
                    $get("3840/shgl/jtys/jtys07").then((res) => {
                        this.tbodyList = res;
                    });
                    $get("3840/scjg/sczt/scztjg05").then((res) => {
                        this.barchartsShow(
                            "sczt-chart1",
                            "内资企业",
                            "外商投资企业",
                            res,
                            "个体工商户",
                            "农民专业合作社"
                        );
                    });
                    $get("3840/scjg/sczt/scztjg07").then((res) => {
                        this.LinechartsShow("sczt-chart3", "市场主体总数", "同比增幅", res);
                    });
                    $get("3840/scjg/sczt/scztjg08").then((res) => {
                        this.LinechartsShow("sczt-chart2", "户数", "增长率", res);
                    });
                },
                //绘制柱图
                barchartsShow(id, name1, name2, data, name3, name4) {
                    const myChartsDivine = echarts.init(document.getElementById(id));
                    let x = data.map((item) => {
                        return item.name;
                    });
                    let y = data.map((item) => {
                        return item.value;
                    });
                    let y1 = data.map((item) => {
                        return item.value1;
                    });
                    let y2 = data.map((item) => {
                        return item.value2;
                    });
                    let y3 = data.map((item) => {
                        return item.value3;
                    });

                    let option = {
                        tooltip: {
                            trigger: "axis",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                            axisPointer: {
                                type: "shadow",
                            },
                        },

                        grid: {
                            left: "10%",
                            top: "18%",
                            right: "5%",
                            bottom: "10%",
                        },
                        legend: {
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                        },
                        xAxis: {
                            data: x,
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: "#aaa",
                                    width: 1,
                                },
                            },
                            axisTick: {
                                show: true, //隐藏X轴刻度
                                alignWithLabel: true,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff", //X轴文字颜色
                                    fontSize: 28,
                                },
                                interval: 0,
                                // rotate: 30,
                            },
                        },
                        yAxis: [
                            {
                                type: "value",

                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        width: 1,
                                        color: "#3d5269",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                    formatter: "{value}户",
                                },
                            },
                        ],
                        series: [
                            {
                                name: name1,
                                type: "bar",
                                color: "#5087EC",
                                stack: "总量",
                                itemStyle: {
                                    normal: {
                                        color: "#5087EC",
                                    },
                                },
                                data: y,
                            },
                            {
                                name: name2,
                                stack: "总量",
                                type: "bar",
                                data: y1,
                            },
                            {
                                name: name3,
                                stack: "总量",
                                type: "bar",
                                data: y2,
                            },
                            {
                                name: name4,
                                stack: "总量",
                                type: "bar",
                                data: y3,
                            },
                        ],
                    };

                    myChartsDivine.setOption(option);
                    tools.loopShowTooltip(myChartsDivine, option, {
                        loopSeries: true,
                    }); //轮播
                },
                //绘制柱图
                LinechartsShow(id, name1, name2, data) {
                    const myChartsDivine = echarts.init(document.getElementById(id));
                    let x = data.map((item) => {
                        return item.name;
                    });
                    let y = data.map((item) => {
                        return item.value;
                    });
                    let y1 = data.map((item) => {
                        return item.value1;
                    });

                    let option = {
                        tooltip: {
                            trigger: "axis",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                            axisPointer: {
                                type: "shadow",
                            },
                        },

                        grid: {
                            left: "8%",
                            top: "18%",
                            right: "8%",
                            bottom: "10%",
                        },
                        legend: {
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                        },
                        xAxis: {
                            data: x,
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: "#aaa",
                                    width: 1,
                                },
                            },
                            axisTick: {
                                show: true, //隐藏X轴刻度
                                alignWithLabel: true,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff", //X轴文字颜色
                                    fontSize: 28,
                                },
                                interval: 0,
                                // rotate: 30,
                            },
                        },
                        yAxis: [
                            {
                                type: "value",
                                name: "",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        width: 1,
                                        color: "#3d5269",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                            {
                                type: "value",
                                name: "",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                position: "right",
                                splitLine: {
                                    show: false,
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#fff",
                                        width: 2,
                                    },
                                },
                                axisLabel: {
                                    show: true,
                                    formatter: "{value} ", //右侧Y轴文字显示
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: name1,
                                type: "bar",
                                color: "#5087EC",
                                itemStyle: {
                                    normal: {
                                        color: "#5087EC",
                                    },
                                },
                                data: y,
                            },

                            {
                                name: name2,
                                type: "line",
                                yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                                showAllSymbol: true, //显示所有图形。
                                // symbol: "circle", //标记的图形为实心圆
                                symbolSize: 10, //标记的大小
                                itemStyle: {
                                    normal: {
                                        color: "#26D9FF",
                                        lineStyle: {
                                            color: "#26D9FF",
                                            width: 4,
                                        },
                                    },
                                },
                                data: y1,
                            },
                        ],
                    };

                    myChartsDivine.setOption(option);
                    tools.loopShowTooltip(myChartsDivine, option, {
                        loopSeries: true,
                    }); //轮播
                },
            },
            //项目生命周期
            mounted() {
                this.init();
            },
        });
    </script>
</html>
