<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>防汛防台地图弹窗2-台风</title>
    <style>
        *{padding:0;margin:0}
        .lf{float: left;}
        .panel-tc{
            width: 757px;
            height: 310px;
            border: 1px solid #379df8;
            background: rgba(9,30,53,.9);
            box-shadow: 0 0 30px #000;
        }
        .tc-title{
            width: 100%;
            height: 120px;
            line-height: 120px;
            box-sizing: border-box;
            padding: 0 20px;
            font-size: 48px;
            color: #d6e7f9;
            font-weight: bold;
            background-image: linear-gradient(0deg, rgba(0, 89, 147, 0.9) 0%, rgba(0, 32, 52, 0.9) 100%),
            linear-gradient(0deg, rgba(103, 200, 255, 0.2) 0%, rgba(110, 176, 231, 0.1) 100%);
            background-blend-mode: normal, normal;
        }
        .tc-con{
            width: 100%;
            height: 26%;
            box-sizing: border-box;
            padding: 0 20px;
        }
        .label{
            width: 100%;
            height: 50px;
            font-size: 32px;
            margin-top: 10px;
        }
        .label-left{
            width: 100%;
            float: left;
        }
        .text-left{
            background-image: linear-gradient(180deg, #fff,#fff,#ffb637,#ffb637);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .text-right{
            width: 73%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #d6e7f9;
        }
        .close {
			position: absolute;
			width: 40px;
			height: 40px;
			cursor: pointer;
			top: 40px;
			left: 676px;
			background: url(../../images/popImage/close.png)no-repeat center center;
			background-size: 100% 100%;
		}
    </style>
</head>
<body>
<div class="container">
    <div class="panel-tc">
        <span class="close" onclick="Vex.Work.sprite.pointOverlay.removePopup();"></span>
        <div class="tc-title" id="tcTitle">黑格比时台风</div>
        <div class="tc-con" id="num">
            <div class="label">
                <div class="label-left"><div class="text-left lf">中心位置：</div><div class="text-right lf" data-name="zxwz">114.70°/20/80°</div></div>
            </div>
            <div class="label">
                <div class="label-left"><div class="text-left lf">风速风力：</div><div class="text-right lf" data-name="fsfl">20米/秒  8级热带风暴</div></div>
            </div>
            <div class="label">
                <div class="label-left"><div class="text-left lf">中心气压：</div><div class="text-right lf" data-name="zxqy">997百帕</div></div>
            </div>
        </div>
    </div>
</div>
<script src="./jquery-3.4.1.min.js"></script>
<script type="text/javascript">
    var panelTc={
    	init(){
			panelTc.funSearchInfo({url: "fxft/fxftMap005"}, function (data) {
				$("#tcTitle").html(data.pointData[0].data.name);
                //数字
				panelTc.getData({
					elem: "#num",
					data: data.pointData[0].data.num
				});
			})
        },
		funSearchInfo: function (params, callback) {
			let { url,argument, flag = true, type = "get", opts } = params;
			$.ajax({
				url: './template/fxft_tf_icon_map/'+url+'.json',
				// headers:{"token":common.token,"ptid":common.ptid},
				data: argument,
				dataType: "json",
				type: type,
				async: flag,
				success: function (data) {
					if (data.responsecode == 200) {
						callback(data.data);
					} else {
						console.log(data);
					}
				},
				error: function (e) {
					// console.log("funSearchInfo Ajax Error：");
					// console.log(e);
				},
			});
		},
		getData:function(params) {
			let { data, elem, attr = "data-name",num=false} = params;
			let name = $(`${elem} [${attr}]`);
			name.map((item) => {
				let id = name.eq(item).attr(attr);
				if(num == false){
					name.eq(item).html(panelTc.funFormatStr(data[id]));
				}else{
					name.eq(item).html(panelTc.funFormatStr(data[id].toLocaleString()));
				}
			});
		},
		funFormatStr:function(str){
			if(str){
				return str;
			}
			else{
				return "--";
			}
		}
    }
    function init(){
			panelTc.init();
	}
</script>
</body>
</html>
