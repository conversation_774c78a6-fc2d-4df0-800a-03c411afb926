<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>数字化改革成果展示-右</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>

    <script src="/static/js/jslib/echarts-wordcloud.min.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
      }

      [v-cloak] {
        display: none;
      }

      #app {
        width: 2070px;
        height: 1850px;
        background: url("/img/left-bg.png") no-repeat;
        background-size: 100% 100%;
        border-radius: 10px;
        padding: 30px 40px;
        box-sizing: border-box;
      }

      /* 表格 */
      .table {
        width: 100%;
        height: 530px;
        box-sizing: border-box;
        overflow-y: auto;
      }

      .table1 .th {
        width: 100%;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 32px;
        line-height: 80px;
        background: #00396f;
        color: #ffffff;
      }

      .table1 .th_td {
        letter-spacing: 0px;
        text-align: center;
      }

      .table1 .tbody {
        width: 100%;
        height: calc(100% - 80px);
        /* overflow-y: auto; */
        overflow: hidden;
      }

      .table1 .tbody:hover {
        overflow-y: auto;
      }

      .table1 .tbody::-webkit-scrollbar {
        width: 4px;
        /*滚动条整体样式*/
        height: 4px;
        /*高宽分别对应横竖滚动条的尺寸*/
      }

      .table1 .tbody::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: #20aeff;
        height: 8px;
      }

      .table1 .tr {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 120px;
        line-height: 60px;
        font-size: 28px;
        color: #ffffff;
        cursor: pointer;
      }

      .table1 .tr:nth-child(2n) {
        background: #031827;
      }

      .table1 .tr:nth-child(2n + 1) {
        background: #02223d;
      }

      .table1 .tr:hover {
        background-color: #14457585;
      }

      .table1 .tr_td {
        letter-spacing: 0px;
        text-align: center;
        box-sizing: border-box;
      }

      /* 切换按钮 */
      .click_btn {
        display: flex;
        justify-content: end;
        font-size: 32px;
        color: #2291f9;
      }

      .click_btn span {
        display: inline-block;
        padding: 5px 10px;
        margin-right: 2px;
        border: 1px solid #2291f9;
        cursor: pointer;
      }

      .avtive_click {
        color: #fff;
        background-image: linear-gradient(to bottom, #bfcfdecf, #3fa2ffba);
      }

      /* 词云 */
      #echart_taxt {
        margin-top: 30px;
        background: url("/static/citybrain/csdn/img/kuang.png") no-repeat;
        background-size: 100% 100%;
      }

      /* 卡片 */
      .card_item {
        width: 940px;
        height: 376px;
        margin-top: 24px;
        /* background: rgba(34,145,246,1); */
        font-size: 50px;
        color: #fff;
        text-align: center;
        display: flex;
        align-items: center;
        align-content: center;
        justify-content: space-around;
      }

      /* 底部表格 */
      .table_low .table {
        height: 420px !important;
      }

      .table_low .table1 .tr {
        height: 80px !important;
      }

      .card_box {
        width: 40%;
        /* height: 80%; */
        text-align: center;
        padding: 20px;
        align-items: center;
        background: rgba(34, 145, 246, 1);
      }

      .el-carousel__indicators--outside {
        width: 100%;
        display: flex;
        justify-content: center;
      }

      .el-carousel__button {
        width: 15px;
        height: 15px;
        border-radius: 50%;
        margin: 0 10px;
      }

      .el-carousel__container {
        height: 350px;
      }
    </style>
  </head>

  <body>
    <div id="app" v-cloak>
      <div class="s-flex s-row-between">
        <div class="left" style="position: relative">
          <nav>
            <s-header-title
              htype="2"
              title="应用成果"
              data-time=""
            ></s-header-title>
          </nav>
          <div
            class="s-font-30 s-c-white"
            style="position: absolute; top: 100px; right: 30px"
          >
            总数：806
          </div>
          <div id="pie_echarts" style="width: 100%; height: 350px"></div>
          <div class="s-m-t-20">
            <div class="click_btn">
              <span :class="{'avtive_click':click_index==1}" @click="btnFun(1)"
                >市级</span
              >
              <span :class="{'avtive_click':click_index==2}" @click="btnFun(2)"
                >区级</span
              >
            </div>
            <!-- <div id="echart_taxt" style="width: 100%; height: 440px"></div> -->
            <!-- <div class="card_item">{{click_index==1?"外国人一件事":"疑难复杂矛盾纠纷兜底化解一件事"}}</div> -->
            <div class="card_item" v-if="click_index==1">
              <!-- <div class="card_box">外国人一件事</div>
            <div class="card_box">党政机关事件</div> -->
              <el-carousel
                indicator-position="outside"
                style="height: 370px; width: 100%; overflow: hidden"
              >
                <el-carousel-item v-for="(item,index) in list1" :key="index">
                  <img
                    :src="item.url"
                    alt=""
                    style="width: 100%; height: 100%"
                  />
                </el-carousel-item>
              </el-carousel>
            </div>
            <div class="card_item" v-if="click_index==2">
              <!-- <div class="card_box">疑难复杂矛盾纠纷兜底化解事件</div>
            <div class="card_box">安全事件安全生产事故</div> -->
              <el-carousel
                indicator-position="outside"
                style="height: 370px; width: 100%; overflow: hidden"
                auto
              >
                <el-carousel-item v-for="(item,index) in list2" :key="index">
                  <img
                    :src="item.url"
                    alt=""
                    style="width: 100%; height: 100%"
                  />
                </el-carousel-item>
              </el-carousel>
            </div>
          </div>
          <nav class="s-m-t-20">
            <s-header-title
              htype="2"
              title="制度成果"
              data-time=""
            ></s-header-title>
          </nav>
          <div id="pie_echarts1" style="width: 100%; height: 200px"></div>
          <div class="table_low s-flex-3 s-m-t-20">
            <div class="table table1">
              <div class="th">
                <div class="th_td" style="flex: 0.3">成果名称</div>
                <div class="th_td" style="flex: 0.4">所属单位</div>
                <div class="th_td" style="flex: 0.3">所属领域</div>
              </div>
              <div
                class="tbody"
                id="box0"
                @mouseover="mouseenterEvent()"
                @mouseleave="mouseleaveEvent()"
              >
                <div class="tr" v-for="(item ,i) in table_list1">
                  <div class="tr_td" style="flex: 0.3">{{item.name}}</div>
                  <div class="tr_td" style="flex: 0.4">{{item.work}}</div>
                  <div class="tr_td" style="flex: 0.3">{{item.area}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="right">
          <nav>
            <s-header-title
              htype="2"
              title="理论成果"
              data-time=""
            ></s-header-title>
          </nav>
          <div id="pie_echart" style="width: 100%; height: 350px"></div>
          <div class="table_top s-flex-3 s-m-t-20">
            <div class="table table1">
              <div class="th">
                <div class="th_td" style="flex: 0.3">成果名称</div>
                <div class="th_td" style="flex: 0.4">所属单位</div>
                <div class="th_td" style="flex: 0.3">所属领域</div>
              </div>
              <div
                class="tbody"
                id="box1"
                @mouseover="mouseenterEvent1()"
                @mouseleave="mouseleaveEvent1()"
              >
                <div class="tr" v-for="(item ,i) in table_list">
                  <div class="tr_td" style="flex: 0.3">{{item.name}}</div>
                  <div class="tr_td" style="flex: 0.4">{{item.work}}</div>
                  <div class="tr_td" style="flex: 0.3">{{item.area}}</div>
                </div>
              </div>
            </div>
          </div>
          <nav class="s-m-t-20">
            <s-header-title
              htype="2"
              title="媒体宣传成果"
              data-time=""
            ></s-header-title>
          </nav>
          <div id="pie_echarts2" style="width: 100%; height: 650px"></div>
        </div>
      </div>
    </div>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
      new Vue({
        el: "#app",
        data() {
          return {
            click_index: 1,
            time1: null,
            dom1: null,
            scpDom1: null,
            time: null,
            dom: null,
            scpDom: null,
            table_list: [],
            table_list1: [],
            list1: [
              {
                url: "/static/citybrain/szhgg/img/11.png",
              },
              {
                url: "/static/citybrain/szhgg/img/22.png",
              },
              {
                url: "/static/citybrain/szhgg/img/33.png",
              },
            ],
            list2: [
              {
                url: "/static/citybrain/szhgg/img/33.png",
              },
              {
                url: "/static/citybrain/szhgg/img/11.png",
              },
              {
                url: "/static/citybrain/szhgg/img/22.png",
              },
            ],
          };
        },
        mounted() {
          this.scroll();
          this.initApi();
          this.btnFun(1);
        },
        methods: {
          scroll() {
            this.dom = document.getElementById("box0");
            this.dom1 = document.getElementById("box1");
            this.time = setInterval(() => {
              this.dom.scrollTop += 1.5;
              if (
                this.dom.scrollTop >=
                this.dom.scrollHeight - this.dom.offsetHeight
              ) {
                this.dom.scrollTop = 0;
              }
            }, 30);
            this.time1 = setInterval(() => {
              this.dom1.scrollTop += 1.5;
              if (
                this.dom1.scrollTop >=
                this.dom1.scrollHeight - this.dom1.offsetHeight
              ) {
                this.dom1.scrollTop = 0;
              }
            }, 30);
          },
          mouseenterEvent() {
            clearInterval(this.time);
          },
          mouseleaveEvent() {
            this.time = setInterval(() => {
              this.dom.scrollTop += 2;
              if (
                this.dom.scrollTop >=
                this.dom.scrollHeight - this.dom.offsetHeight
              ) {
                this.dom.scrollTop = 0;
              }
            }, 20);
          },
          mouseenterEvent1() {
            clearInterval(this.time1);
          },
          mouseleaveEvent1() {
            this.time1 = setInterval(() => {
              this.dom1.scrollTop += 2;
              if (
                this.dom1.scrollTop >=
                this.dom1.scrollHeight - this.dom1.offsetHeight
              ) {
                this.dom1.scrollTop = 0;
              }
            }, 20);
          },

          initApi() {
            $api("/szggh_szhgg_szhgg_right_pie01").then((res) => {
              this.getPie("pie_echarts", res);
            });
            $api("/szggh_szhgg_szhgg_right_pie03").then((res) => {
              this.getPie("pie_echart", res);
            });
            $api("/szggh_szhgg_szhgg_right_pie04").then((res) => {
              this.getPie("pie_echarts1", res);
            });
            $api("/szggh_szhgg_szhgg_right_pie02").then((res) => {
              this.getPie("pie_echarts2", res);
            });
            $api("/szggh_szhgg_szhgg_right_table").then((res) => {
              this.table_list1 = res;
            });
            $api("/szggh_szhgg_szhgg_right_table1").then((res) => {
              this.table_list = res;
            });
          },
          btnFun(index) {
            this.click_index = index;
          },
          getPie(id, echartsData) {
            let myEc = echarts.init(document.getElementById(id));
            let option = {
              tooltip: {
                trigger: "item",
                formatter: "{b} : {c} ({d}%)",
                borderWidth: 0,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                textStyle: {
                  color: "white",
                  fontSize: "30",
                },
              },
              series: [
                {
                  type: "pie",
                  radius: "70%",
                  center: ["50%", "60%"],
                  label: {
                    show: true,
                    formatter: "{b}\n{d}%",
                    textStyle: {
                      fontSize: 25,
                      color: "#fff",
                    },
                  },
                  data: echartsData,
                  itemStyle: {
                    borderRadius: 5,
                  },
                },
              ],
            };

            myEc.setOption(option);
            myEc.getZr().on("mousemove", (param) => {
              myEc.getZr().setCursorStyle("default");
            });
          },
          //词云
          getChart08(id, mockData) {
            let echarts0 = echarts.init(document.getElementById(id));

            // 随机颜色
            let randcolor = () => {
                let r = 100 + Math.random() * 100;
                let g = 135 + Math.random() * 100;
                let b = 100 + Math.random() * 100;
                return `rgb(${r}, ${g}, ${b})`;
              },
              option = {
                tooltip: {
                  show: false,
                  trigger: "item",
                  padding: [10, 15],
                  borderWidth: 0,
                  backgroundColor: "rgba(0, 0, 0, 0.6)",
                  textStyle: {
                    fontSize: 50,
                    color: "white",
                  },
                  formatter: (params) => {
                    const { name, value } = params;
                    return `平台：${name} <br/>数量：${value}`;
                  },
                },
                series: [
                  {
                    type: "wordCloud",
                    gridSize: 90,
                    shape: "circle",
                    textStyle: {
                      color: (params) => {
                        return randcolor();
                      },
                      emphasis: {
                        shadowBlur: 30,
                        shadowColor: "#333",
                      },
                    },
                    data: mockData,
                  },
                ],
              };
            echarts0.setOption(option);
            echarts0.getZr().on("mousemove", (param) => {
              echarts0.getZr().setCursorStyle("default");
            });
          },
        },
      });
    </script>
  </body>
</html>
