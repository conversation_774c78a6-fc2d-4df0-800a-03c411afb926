<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>交通管理-弹框3</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/jslib/vue-count-to.min.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/datav.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    </head>
    <style>
        [v-cloak] {
            display: none;
        }
        html,
        body,
        ul,
        p {
            padding: 0;
            margin: 0;
            list-style: none;
        }
        .container {
            width: 3380px;
            height: 400px;
            box-sizing: border-box;
            padding: 20px;
            background: url("/img/left-bg.png") no-repeat;
            background-size: 100% 100%;
            display: flex;
        }
        .box_left {
            width: 10%;
            height: 100%;
            display: grid;
        }
        .box_left > img:nth-child(1) {
            transform: rotate(90deg);
            margin: 0 auto;
        }
        .box_left > img:nth-child(3) {
            transform: rotate(-90deg);
            margin: 0 auto;
        }
        .box_right {
            width: 90%;
            height: 100%;
        }
        .el-carousel__indicators {
            display: none;
        }
        .el-carousel__container {
            height: 360px;
        }
        .el-carousel__item {
            display: flex;
            padding: 0 80px;
            justify-content: space-evenly;
            box-sizing: border-box;
        }
        .el-carousel__arrow--left {
            width: 42px;
            height: 262px;
            background: url("/static/citybrain/shgl/img/doing/btn-left-unClik.png");
            background-size: 100% 100%;
        }
        .el-carousel__arrow--left:hover {
            background: url("/static/citybrain/shgl/img/doing/btn-left-clik.png");
            background-size: 100% 100%;
        }
        .el-carousel__arrow--left:active {
            background: url("/static/citybrain/shgl/img/doing/btn-left-clik.png");
            background-size: 100% 100%;
        }
        .el-carousel__arrow--right {
            width: 42px;
            height: 262px;
            background: url("/static/citybrain/shgl/img/doing/btn-right-unClik.png");
            background-size: 100% 100%;
        }
        .el-carousel__arrow--right:hover {
            background: url("/static/citybrain/shgl/img/doing/btn-right-clik.png");
            background-size: 100% 100%;
        }
        .el-carousel__arrow--right:active {
            background: url("/static/citybrain/shgl/img/doing/btn-right-clik.png");
            background-size: 100% 100%;
        }
        .el-carousel__arrow {
            border-radius: 0;
        }
        .el-carousel__arrow i {
            display: none;
        }
        .left-con-item {
            opacity: 0.6;
            width: 239px;
            height: 278px;
            background: url("/static/citybrain/shgl/img/doing/dfx.png");
            background-size: 100% 100%;
            text-align: center;
            position: relative;
            top: 50px;
        }
        .left-con-item-active {
            opacity: 1 !important;
        }
        .left-con-item > img {
            width: 50px;
            height: 50px;
            top: 40px;
            left: 95px;
            position: absolute;
        }
        .left-con-item > p:nth-child(2) {
            position: absolute;
            top: 160px;
            width: 100%;
            text-align: center;
        }
        .left-con-item > p:nth-child(3) {
            position: absolute;
            top: 200px;
            width: 100%;
            text-align: center;
            font-size: 24px;
        }
        .box-left-con {
            overflow: hidden;
            width: 100%;
            height: 185px;
            padding: 0px 50px;
            box-sizing: border-box;
        }
        .con-item {
            width: 100%;
            height: 60px;
            line-height: 60px;
            text-align: center;
            font-size: 30px;
        }
        .back {
            font-size: 37px;
        }

        .box-left-con .el-carousel__item {
            display: block;
        }
    </style>

    <body>
        <div id="app" class="container" v-cloak>
            <div class="box_left">
                <img @click="upIconFun" src="/static/citybrain/shgl/img/doing/two-title-icon1.png" alt="" />
                <div class="box-left-con">
                    <!-- <template>
            <el-carousel
              height="200px"
              direction="vertical"
              :arrow="never"
              :autoplay="false"
            >
              <el-carousel-item v-for="(item,i) in typeArr" :key="item">
                <div
                  :class="type_index==index?'con-item s-c-yellow-gradient back':'con-item s-c-blue-gradient'"
                  v-for="(item,index) in typeArr[i]"
                  class="medium"
                >
                  {{ item }}
                </div>
              </el-carousel-item>
            </el-carousel>
          </template> -->
                    <div class="typeBox">
                        <div
                            :class="type_index==i?'con-item s-c-yellow-gradient back':'con-item s-c-blue-gradient'"
                            v-for="(item,i) in typePer"
                        >
                            {{item}}
                        </div>
                    </div>
                    <!-- <div class="con-item s-c-blue-gradient">警车</div>
          <div class="con-item s-c-yellow-gradient back">警力</div>
          <div class="con-item s-c-blue-gradient">铁骑</div>
          <div class="con-item s-c-blue-gradient">铁骑</div>
          <div class="con-item s-c-blue-gradient">铁骑</div>
          <div class="con-item s-c-blue-gradient">铁骑</div>
          <div class="con-item s-c-blue-gradient">铁骑</div> -->
                </div>
                <img @click="lowIconFun" src="/static/citybrain/shgl/img/doing/two-title-icon1.png" alt="" />
            </div>
            <div class="box_right">
                <el-carousel arrow="always" :autoplay="false" style="width: 100%; overflow: hidden">
                    <el-carousel-item v-for="(obj,i) in partList" :key="i">
                        <div
                            v-for="(item,index) in partList[i]"
                            :key="item.id"
                            :class="[index===itemIndex?'left-con-item-active':'','left-con-item']"
                            @click="itemClick(item,index)"
                        >
                            <img src="/static/citybrain/shgl/img/doing/jtgl3-1.png" alt="" />
                            <p class="s-c-white s-font-30">{{item.name}}</p>
                            <p class="s-c-white">{{item.value}}</p>
                        </div>
                    </el-carousel-item>
                </el-carousel>
            </div>
        </div>
    </body>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>

    <script>
        var vm = new Vue({
            el: "#app",
            data() {
                return {
                    allList: [],
                    partList1: [],
                    itemIndex: "",
                    type_index: 0,
                    typePer: ["警车", "警力", "铁骑"],
                };
            },
            watch: {},
            computed: {
                partList() {
                    let newArr = [];
                    for (let i = 0; i < this.partList1.length; i += 10) {
                        newArr.push(this.partList1.slice(i, i + 10));
                    }
                    return newArr;
                },
                typeArr() {
                    let newArr = [];
                    for (let i = 0; i < this.typePer.length; i += 3) {
                        newArr.push(this.typePer.slice(i, i + 3));
                    }
                    return newArr;
                },
            },
            mounted() {
                this.init();

                let that = this;

                window.addEventListener("message", function (e) {
                    // console.log(e);
                    // debugger
                    if (!e.data.data.data) return;
                    const item = JSON.parse(e.data.data.data);
                    let coor = e.data.data.point.split(",");

                    if (e.data.type == "pointClick" && item.pointId == "jtglBottom") {
                        // console.log(item);

                        that.getDetail(item, coor);
                    }
                });
            },
            methods: {
                upIconFun() {
                    if (this.type_index == this.typePer.length - 1) {
                        this.type_index = 0;
                        this.partList1 = this.allList;
                    } else {
                        this.type_index++;
                        this.partList1 = this.partList1.slice(this.type_index, 15);
                    }
                    document.querySelector(".typeBox").style.transform = "translateY(" + 60 * -this.type_index + "px)";
                },
                lowIconFun() {
                    if (this.type_index == 0) {
                        this.type_index = this.typePer.length - 1;
                        this.partList1 = this.allList;
                    } else {
                        this.type_index--;
                        this.partList1 = this.partList1.slice(this.type_index, 10);
                    }
                    document.querySelector(".typeBox").style.transform = "translateY(" + 60 * -this.type_index + "px)";
                },
                init() {
                    $api("shgl_doing_jtgl3_1_1").then((res) => {
                        this.partList1 = this.allList = res;
                    });
                },
                getPointData(item) {
                    let that = this;
                    var icon = "";
                    if (item.type == "警力") {
                        icon = "wlgz-网约车";
                    } else {
                        icon = "橙色预警事件";
                    }
                    $api("shgl_doing_jtgl3_2").then((res) => {
                        // console.log('res>>>>',res);
                        let options = res.filter((obj) => {
                            return item.id == obj.id;
                        });

                        let pointData = [];

                        // console.log(options);
                        options.forEach((element) => {
                            let str = {
                                data: {
                                    pointId: "jtglBottom",
                                    obj: element,
                                },
                                point: element.lnglat,
                            };
                            pointData.push(str);
                        });

                        that.addPoint(icon, pointData, item.name, 1);
                    });
                },
                itemClick(item, index) {
                    this.rmPoint();
                    this.rmPop();
                    // console.log(item);
                    this.itemIndex = index;

                    this.getPointData(item);

                    // if(item.type=="警力"){
                    //   let icon = "wlgz-网约车"
                    // }

                    // // let

                    // this.addPoint(icon)
                },
                addPoint(icon, pointData, pointId, iconSize) {
                    top.document.getElementById("map").contentWindow.Work.funChange(
                        JSON.stringify({
                            funcName: "pointLoad", //功能名称
                            pointType: icon, //点位类型图标
                            pointId: pointId,
                            setClick: true,
                            pointData: pointData,
                            size: [0.08, 0.08, 0.08, 0.08],
                            imageConfig: { iconSize: iconSize },
                            popup: {
                                offset: [50, -100],
                            },
                        })
                    );
                },
                rmPoint() {
                    top.document.getElementById("map").contentWindow.Work.funChange(
                        JSON.stringify({
                            funcName: "rmPoint",
                            pointId: "", //传id清除单类，不传清除所有
                        })
                    );
                    if (top.document.getElementById("map").contentWindow.egs1.contentWindow.map.TDT_TITLE_ID) {
                        top.document.getElementById("map").contentWindow.egs1.contentWindow.map.TDT_TITLE_ID.remove();
                    }
                },
                getDetail(item, coor) {
                    let that = this;
                    $api("shgl_doing_jtgl3_3").then((res) => {
                        // console.log(res);
                        let detail = res[0].detail;
                        that.getCustom(res[0].path, coor, detail);
                    });
                },
                getCustom(path, coor, detail) {
                    let pathStr = JSON.stringify(path);

                    //  console.log(pathStr);

                    if (top.document.getElementById("map").contentWindow.egs1.contentWindow.map.TDT_TITLE_ID) {
                        top.document.getElementById("map").contentWindow.egs1.contentWindow.map.TDT_TITLE_ID.remove();
                    }
                    const imgIcon = `${baseURL.url}/static/citybrain/tckz/img/tckz_gj/危货车GPS.png`;

                    // console.log(await getGjList(item))
                    let objData = {
                        funcName: "customPop",
                        coordinates: coor,

                        // coordinates: ['119.607129', '29.068155'],
                        closeButton: true,
                        html: ` <div
                  class="pop"
                 style="
                  width: 1000px;
                  position: absolute;
                  border-radius: 5px;
                  background-color: rgba(10, 31, 53, 0.8);
                  z-index: 999999;
                  -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                  box-shadow: inset 0 0 40px 0 #5ba3fa;
                  padding: 24px;
                "
              >
              <div
                 onclick=" this.parentNode.style.display = 'none'
                 if (
                  window.map.TDT_TITLE_ID
                    ) {
                      window.map.TDT_TITLE_ID.remove()
                    }
                 "

                  style="
                    position: absolute;
                    right: 30px;
                    top: 30px;
                    font-size: 40px;
                    color: #fff;
                    cursor:pointer
                  "
                >
                  x
                </div>
                <div class="container">


                  <div style="font-size: 50px;color: #71d8e3;font-weight:600; height: 44px;
        line-height: 44px;   margin-bottom: 20px;margin-left: 35px;" >事故详情</div>



                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">在线警力:</span>
                    <span style="color: #eccc83; width: 70%">95</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">在线警车:</span>
                    <span style="color: #eccc83; width: 70%">12</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">铁骑在线:</span>
                    <span style="color: #eccc83; width: 70%">35</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">警员名称:</span>
                    <span style="color: #eccc83; width: 70%">吴明</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">警车号码:</span>
                    <span style="color: #eccc83; width: 70%">T20456</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">事故成因:</span>
                    <span style="color: #eccc83; width: 70%">${detail.result}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">发生时间:</span>
                    <span style="color: #eccc83; width: 70%">${detail.time}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">事故类型:</span>
                    <span style="color: #eccc83; width: 70%">${detail.type}</span>
                  </div>
                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                  >
                    <span style="width: 30%; text-align: center">事故地点:</span>
                    <span style="color: #eccc83; width: 70%">${detail.address}</span>
                  </div>

                  <div
                    class="item"
                    style="display: flex; font-size: 40px; color: #fff; line-height: 70px;align-items: center;"
                  >
                    <span style="width: 32%; text-align: center">历史轨迹：</span>
                    <button

                      style="
                        font-size: 28px;
                        color: #fff;
                        background-color:#b98262;
                        border: unset;
                        width: 171px;
                        height: 60%;
                        height: 59px;
                        line-height: 59px;
                      "
                      onclick="

                      console.log('进入了')
                      top.document.getElementById('map').contentWindow.Work.funChange(
                          JSON.stringify({
                            funcName: 'createTrajectory',
                            data: {
                              id:'TDT_TITLE_ID',
                              type:'dynamic',
                               icon:'${imgIcon}',
                               coordinates: ${pathStr},
                                  iconStyle: {
                                 'icon-size':2,
                                 'icon-rotate': 360,
                             },
                             style: {
                                 'line-width': 10,
                             },
                             isGlowLine: false,
                             isBezierCurve: false,
                             color: ['#85d4b1'],
                             loop:false,
                             steps: 100
                            }
                                })
                                     )

                                     "
                    >
                      点击查看
                    </button>
                  </div>
                </div>
              </div>`,
                    };
                    // [
                    //           [119.745613,29.07616],
                    //           [119.74559,29.076169],
                    //           [119.74559,29.076169],
                    //           [119.745582,29.07615],
                    //           [119.74559,29.076139],
                    //           [119.745597,29.07615],
                    //           [119.745597,29.07616],
                    //           [119.74559,29.07616],
                    //           [119.745597,29.076179],
                    //           [119.745613,29.07616],
                    //       ],
                    // <div
                    //           class="item"
                    //           style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                    //         >
                    //           <span style="width: 30%; text-align: center">发生时间:</span>
                    //           <span style="color: #eccc83; width: 70%">${detail.time}</span>
                    //         </div>
                    //         <div
                    //           class="item"
                    //           style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                    //         >
                    //           <span style="width: 30%; text-align: center">事故类型:</span>
                    //           <span style="color: #eccc83; width: 70%">${detail.type}</span>
                    //         </div>
                    //         <div
                    //           class="item"
                    //           style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                    //         >
                    //           <span style="width: 30%; text-align: center">事故地点:</span>
                    //           <span style="color: #eccc83; width: 70%">${detail.address}</span>
                    //         </div>
                    //         <div
                    //           class="item"
                    //           style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
                    //         >
                    //           <span style="width: 30%; text-align: center">事故成因:</span>
                    //           <span style="color: #eccc83; width: 70%">${detail.result}</span>
                    //         </div>
                    top.document.getElementById("map").contentWindow.Work.funChange(JSON.stringify(objData));
                },
                rmPop() {
                    top.document.getElementById("map").contentWindow.Work.funChange(
                        JSON.stringify({
                            funcName: "rmPop",
                        })
                    );
                },
            },
        });
    </script>
</html>
