#shgl-csaq-yhpczltj-right {
    width: 2045px;
    height: 1890px;
    background: url("/img/right-bg.png") no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: center;
}

.content {
    width: 1934px;
    height: 100%;
}

.yhzyBox {
    display: flex;
    font-size: 30px;
    color: #fff;
    justify-content: space-around;
}

.yhzyTitle {
    font-family: 'Arial-BoldMT', 'Arial Bold', 'Arial', sans-serif;
    font-weight: 700;
    color: #66BBF9;
    font-size: 60px;
}

.nameTitle {
    margin-bottom: 15px;
}

.yhzy {
    position: relative;
}

.bottomTitle {
    position: absolute;
    top: 30%;
    right: -60px;
}

.yhzy:first-child .icon {
    font-family: 'Font Awesome 5 Pro ', 'Font Awesome 5 Pro', sans-serif;
    font-weight: 400;
    font-size: 30px;
    color: #19BE6B;
}

.yhzy:last-child .icon {
    font-family: 'Font Awesome 5 Pro ', 'Font Awesome 5 Pro', sans-serif;
    font-weight: 400;
    font-size: 30px;
    color: #F56C6C;
}

.BigBox {
    height: 350px !important;
}

.BigBox-one {
    margin-top: 20px;
}