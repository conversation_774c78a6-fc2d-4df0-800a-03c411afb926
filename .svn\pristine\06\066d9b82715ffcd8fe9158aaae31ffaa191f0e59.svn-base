
  <!DOCTYPE html>
  <html lang="en">

  <head>
    <meta charset="UTF-8" />
    <meta name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>金华•城市大脑运行情况</title>
    <link rel="shortcut icon" href="#" />
    <link rel="stylesheet" href="/static/css/home_services/common.css" />
    <link rel="stylesheet" href="/static/css/home/<USER>" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/common.css" />

    <link rel="stylesheet" href="/static/css/dnyx/dnyx.css">
    <link rel="stylesheet" href="/static/css/animate_dn.css">
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts-liquidfill.js"></script>
    <script src="/static/js/jslib/jquery-3.4.1.min.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>

    <link href="/static/js/jslib/Odometer.css" rel="stylesheet">
    <style type="text/css" scoped>
      [v-cloak] {
        display: none;
      }
    </style>
  </head>

  <body>

    <div class="dnyx" >
      <!-- <div class="home-header" >
        <div class="header">
          <div class="header_img">
            <span class="text_linear_white header_text" onclick="location.href = './home.html'"
              parentUrl="home">金华城市大脑运行情况</span>
          </div>
        </div>

      </div> -->
      <div id="home-main" v-cloak>
        <!-- 左侧 -->
        <div class="left">
          <!-- 标题导航 -->
          <!-- <div class="dnyx_header" style="height: 130px;">
            <h2 class="title"> <i class="title_icon_one"></i>数据</h2>
            <div class="title_hr" style="width: 82%;left: 200px;"></div>
            <img class="titleBg" src="./img/headerimage.png" width="98%">
          </div> -->
          <s-header-title style="height: 130px;width: 98%;" title="数据" htype="1"></s-header-title>
          <!-- 二级导肮 -->
          <div class="title_icon" style="margin-top: 10px;" @click="togUrl">
            <!-- <a href="./bmjr-img.html">
            </a> -->            
            <s-header-title2
            title="数据接入"
            style="cursor: pointer; margin: 0 auto"
            :click-flag="true"
          ></s-header-title2>
          </div>
          <ul class="left_topul">
            <li v-for="(item, i) in leftTopInfo" :key="i">
              <img :src="item.img" class="breath-light" @mouseover="mouseOver(i)" @mouseout="mouseOut">
              <div>
                <i>{{ item.name }}</i><br />
                <i class="s-font-24">{{item.text}}</i><br />
                <span class="col_org">
                  <b>{{ item.num }}</b>{{item.unit}}
                </span>
              </div>
            </li>
          </ul>
          <div class="tc_table" ref="table_tc" v-show="mouseTable">

            <div class="tc_item" >
              <table>
                <caption> <b>{{mouseTableData.title}}</b> </caption>
               <thead>

                  <th>序号</th>
                  <th>二级目录</th>
                  <th>数量</th>

               </thead>
               <tbody>
                <tr v-for="item,index in mouseTableData.arr" :key="index">
                  <th>{{index+1}}</th>
                  <th>{{item.name}}</th>
                  <th>{{item.num}}</th>
              </tr>

               </tbody>
            </table>
            <div class="sjx"></div>


          </div>
          </div>
          <!-- 二级导肮 -->
          <div class="title_icon" style="margin:25px 0 -10px 0;">
            <i class="icon_left"></i>
            <h2>部门数据接入排名</h2>
            <i class="icon_right"></i>
          </div>
          <div class="left_cen">
            <div class="cenBox">
              <span class="titleMent"><i class="point"></i>部门正排名TOP5</i>&nbsp;</span>
               <div style="display: flex;">
                <ul class="icon-ul">
                  <li v-for="(item, i) in 5" :key="i">
                    <img src="/static/images/home_services/dnyx/one01.png" style="margin-bottom:-13px;" v-if="i == 0">
                    <img src="/static/images/home_services/dnyx/two02.png" style="margin-bottom:-13px;" v-if="i == 1">
                    <img src="/static/images/home_services/dnyx/three03.png" style="margin-bottom:-13px;" v-if="i == 2">
                    <span class="s-w7 s-font-40 s-sty" v-if="i != 0 && i != 1 && i != 2">{{ i + 1 }}&nbsp;</span>
                  </li>
                </ul>
                <div id="lineEcharts_New0" style="width: 900px;height: 380px;"></div>
              </div>
              <!-- <ul>
                <li v-for="(item, i) in leftCen" :key="i">
                  <img src="/static/images/home_services/dnyx/one01.png" style="margin-bottom:-13px;" v-if="i == 0">
                  <img src="/static/images/home_services/dnyx/two02.png" style="margin-bottom:-13px;" v-if="i == 1">
                  <img src="/static/images/home_services/dnyx/three03.png" style="margin-bottom:-13px;" v-if="i == 2">
                  <span class="s-w7 s-font-40 s-sty" v-if="i != 0 && i != 1 && i != 2">{{ i + 1 }}&nbsp;</span>
                  <span class="telNameOver" :title="item.name">{{ item.name }}</span>
                  <progress class="progress" max="100" :value="item.value"> </progress>
                  <span>{{ item.num }}</span>
                </li>
              </ul> -->
            </div>
            <div class="cenBox">
              <span class="titleMent"><i class="point"></i>部门倒排名TOP5</i>&nbsp;</span>
              <!-- <ul>
                <li v-for="(item, i) in leftCen1" :key="i">
                  <span class="s-w7 s-font-40 s-sty">{{ i + 1 }}&nbsp;</span>
                  <span class="telNameOver" :title="item.name">{{ item.name }}</span>
                  <progress class="progress" max="100" :value="item.value"></progress>
                  <span>{{ item.num }}</span>
                </li>
              </ul> -->
               <div style="display: flex;">
                <ul class="icon-ul">
                  <li v-for="(item, i) in 5" :key="i">
                    <span class="s-w7 s-font-40 s-sty">{{ i + 1 }}&nbsp;</span>
                  </li>
                </ul>
                <div id="lineEcharts_New1" style="width: 900px;height: 380px;"></div>
              </div>
            </div>
          </div>
          <!-- 二级导肮 -->
          <div class="title_icon" style="margin:25px 0 -10px 0;">
            <i class="icon_left"></i>
            <h2>城市体征接入排名</h2>
            <i class="icon_right"></i>
          </div>
          <div class="two-title">
            <h1>
              <span style="display: flex;justify-content:center;align-items: center;">
              <span style="margin-top: 15px;">共</span>
                  <span v-for="(item,i) of zhibcount" style="margin:0 5px;text-align: center;width: 40px;height: 64px;background:url('/static/images/home_services/dnyx/numbg.png') no-repeat 100% 100%;">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000"
                      class="s-c-blue-gradient s-font-50"></count-to>
                  </span>
              <span style="margin-top: 15px;">项</span>
              </span>
            </h1>
            <h1>
              <span style="display: flex;justify-content:center;align-items: center;">
                <span style="margin-top: 15px;">已接入</span>
                <span v-for="(item,i) of zhibover" style="margin:0 5px;text-align: center;width: 40px;height: 64px;background:url('/static/images/home_services/dnyx/numbg.png') no-repeat 100% 100%;">
                  <count-to :start-val="0" :end-val="Number(item)" :duration="3000"
                    class="s-c-orange-gradient s-font-50"></count-to>
                </span>
                <!-- <span v-for="(item,i) of '130'" style="margin:0 5px;text-align: center;width: 40px;height: 64px;background:url('/static/images/home_services/dnyx/numbg.png') no-repeat 100% 100%;">
                  <count-to :start-val="0" :end-val="Number(item)" :duration="3000"
                    class="s-c-orange-gradient s-font-50"></count-to>
                </span> -->
                <span style="margin-top: 15px;">项</span>
              </span>
            </h1>
          </div>
          <div class="left_cen">
            <div class="cenBox">
              <span class="titleMent"><i class="point"></i>部门正排名TOP5</i>&nbsp;</span>
              <div style="display: flex;">
                <ul class="icon-ul">
                  <li v-for="(item, i) in 5" :key="i">
                    <img src="/static/images/home_services/dnyx/one01.png" style="margin-bottom:-13px;" v-if="i == 0">
                    <img src="/static/images/home_services/dnyx/two02.png" style="margin-bottom:-13px;" v-if="i == 1">
                    <img src="/static/images/home_services/dnyx/three03.png" style="margin-bottom:-13px;" v-if="i == 2">
                    <span class="s-w7 s-font-40 s-sty" v-if="i != 0 && i != 1 && i != 2">{{ i + 1 }}&nbsp;</span>
                  </li>
                </ul>
                <div id="lineEcharts_New2" style="width: 900px;height: 380px;"></div>
              </div>
              <!-- <ul>
                <li v-for="(item, i) in leftCen2" :key="i">
                  <img src="/static/images/home_services/dnyx/one01.png" style="margin-bottom:-13px;" v-if="i == 0">
                  <img src="/static/images/home_services/dnyx/two02.png" style="margin-bottom:-13px;" v-if="i == 1">
                  <img src="/static/images/home_services/dnyx/three03.png" style="margin-bottom:-13px;" v-if="i == 2">
                  <span class="s-w7 s-font-40 s-sty" v-if="i != 0 && i != 1 && i != 2">{{ i + 1 }}&nbsp;</span>
                  <span class="telNameOver" :title="item.name">{{ item.name }}</span>
                  <progress class="progress" max="100" :value="item.value"> </progress>
                  <span>{{ item.num }}</span>
                </li>
              </ul> -->
            </div>
            <div class="cenBox">
              <span class="titleMent"><i class="point"></i>部门倒排名TOP5</i>&nbsp;</span>
              <div style="display: flex;">
                <ul class="icon-ul">
                  <li v-for="(item, i) in 5" :key="i">
                    <span class="s-w7 s-font-40 s-sty">{{ i + 1 }}&nbsp;</span>
                  </li>
                </ul>
                <div id="lineEcharts_New3" style="width: 900px;height: 380px;"></div>
              </div>
              <!-- <ul>
                <li v-for="(item, i) in leftCen3" :key="i">
                   <span class="s-w7 s-font-40 s-sty">{{ i + 1 }}&nbsp;</span>
                  <span class="telNameOver" :title="item.name">{{ item.name }}</span>
                  <progress class="progress" max="100" :value="item.value"></progress>
                  <span>{{ item.num }}</span>
                </li>
              </ul> -->
            </div>
          </div>
          <!-- 二级导航 -->
          <div class="title_icon" style="margin:25px 0 10px;">
            <a href="./bmjr.html">
              <s-header-title2
                title="应用集成中心"
                style="cursor: pointer; margin: 0 auto"
                :click-flag="true"
              ></s-header-title2>
            </a>
          </div>
          <div class="left_low">
            <!-- <div class="low" v-for="(item, i) in xtData" :key="i">
              <img :src="item.img">
              <span>
                <i>{{ item.name }}</i>
                <b class="col_yel"><i>{{ item.nun }}</i>{{ item.util }}</b>
              </span>
            </div> -->
            <div class="low">
              <img src="/static/images/home_services/dnyx/bot-left001.png">
              <span>
                <i>部门驾驶舱</i>
                <b class="col_yel"><i>{{ xtData }}</i>个</b>
              </span>
            </div>
            <div class="low">
              <img src="/static/images/home_services/dnyx/bot-left002.png">
              <span>
                <i>数改应用</i>
                <b class="col_yel"><i>{{ xtData1 }}</i>个</b>
              </span>
            </div>
            <div class="low">
              <img src="/static/images/home_services/dnyx/bot-left003.png">
              <span>
                <i>场景应用</i>
                <b class="col_yel"><i>{{ xtData2 }}</i>个</b>
              </span>
            </div>
            <div class="low">
              <img src="/static/images/home_services/dnyx/bot-left001.png">
              <span>
                <i>业务系统</i>
                <b class="col_yel"><i>{{ xtData3 }}</i>个</b>
              </span>
            </div>
          </div>
        </div>
        <!-- 中-隐藏 -->
        <div class="center" style="display:none">
          <ul class="cenUL">
            <li v-for="(item, i) in cenData">
              <img :src="item.img" class="breath-light">
              <span v-if="item.name == '体征'" style="position: absolute;top: 40px;left: 170px;">
                <span style="margin-right:15px;">{{ item.name }}</span>
                <span class="col_org s-font-38 s-w7">{{ item.num }}</span>
              </span>
              <div v-if="item.name != '体征'">
                <i>{{ item.name }}</i><br>
                <span class="col_org"><b>{{ item.num }}</b>{{ item.unit }}</span>
              </div>
            </li>
          </ul>
          <div class="centerBox">
            <div class="bottom-btn">
              <div class="bottom-btn-item" v-for="(item,index) in btnList" :key="index"
                    @click="btnChange(index)"
                    :class="btnIndex===index?'bottom-btn-item-active':''">
                {{item}}
              </div>
              <!-- <div class="bottom-btn-item">县(市、区)运行情况</div>
              <div class="bottom-btn-item">各部门运行情况</div> -->
            </div>
            <!-- 箭头 -->
            <div class="centerBor-arrow">
              <div class="left-arrow">
                <ul>
                  <li><img src="/static/images/home_services/dnyx/left-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/left-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/left-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/left-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/left-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/left-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/left-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/left-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/left-arrow.png"></li>
                </ul>
              </div>
              <div class="right-arrow">
                <ul>
                  <li><img src="/static/images/home_services/dnyx/right-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/right-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/right-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/right-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/right-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/right-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/right-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/right-arrow.png"></li>
                  <li><img src="/static/images/home_services/dnyx/right-arrow.png"></li>
                </ul>
              </div>
            </div>
            <!-- 内容 -->
            <div class="initial" v-if="showRight">
              <div class="cen-header" style="display: none;">
                <div>
                  <p>入库数</p>
                  <div class="count-flop" :key="compKey2">
                    <div :class="item != '.' ? 'count-flop-box' : 'count-flop-point'" v-for="(item, index) in value2"
                      :key="index" :style="item != '.' ? 'width:50px;height:70px' : ''">
                      <div v-if="item != '.'" class="count-flop-content" :class="['rolling_' + item]"
                        style="font-size: 50px !important;line-height:70px;">
                        <div v-for="(item2, index2) in numberList" :key="index2" class="count-flop-num" style="font-size:50px;">{{ item2 }}
                        </div>
                      </div>
                      <div v-else class="">,</div>
                    </div>
                  </div>

                </div>
                <div id="echarts01" style="height:180px;width: 550px;"></div>
              </div>

              <div style="position:absolute;top: 680px;left: 50px;" class="carouselBtn" @click="cardleft">&lt;</div>
              <el-carousel :autoplay="false" arrow="never" ref="card001" trigger="click" :initial-index="initIndex1" @change="cardleftChange"
                style="position: absolute;top: 277px;width:730px; height: 1200px;overflow: hidden;transform: perspective(14em) rotateY(5deg);">
                <el-carousel-item v-for="index in leftBar" :key="index" style="width: 900px;height:1000px">
                  <ul class="ul01">
                    <li v-for="(item, i) in ulLeft.slice((index - 1) * 7, (index * 7))" :key="i"
                      @click="clickLeftDio(i,item.source_dept)" :title="item.source_dept">
                      <span>
                        <b style="height:70px;overflow: hidden;">{{ item.source_dept }}</b>
                        <img
                          :src="indexId == i ? '/static/images/home_services/dnyx/left-hover.png' : '/static/images/home_services/dnyx/left-inif.png'" />
                      </span>
                      <img src="/static/images/home_services/dnyx/left-after.png" style="margin:50px 0">
                    </li>
                  </ul>
                </el-carousel-item>
              </el-carousel>

              <div class="countAll  s-font-50 s-w7" style="top:1390px;left:280px;transform: perspective(0em) rotateZ(-9deg)">总计：{{departmentCount}}</div>
              <!-- v-show="showLeft" :style="'top:' + (indexId * 100 + 300) + 'px'" -->
              <div class="left-diong" v-show="showLeft">
                <div style="margin: 60px 0 0;display:flex;align-items: center;justify-content: center;">
                  <div class="top-l">
                    <div class="line"></div>
                    <div class="point"></div>
                  </div>
                  <span class="lineName">{{leftBarName}}</span>
                  <div class="top-l">
                    <div class="point"></div>
                    <div class="line"></div>
                  </div>
                </div>
                <ul style="display: flex;justify-content: space-around;width: 100%;margin-top:20px;">
                  <li v-for="(item, i) in leftDio" :key="i" v-if="i < 2" style="display:flex;">
                      <img :src="i===1?'/static/images/home_services/dnyx/dnyx-mid-left01.png':'/static/images/home_services/dnyx/dnyx-mid-left02.png'" alt="">
                      <div style="text-align: left;width: 100%;">
                        <span class="s-c-yellow-gradient s-font-45 s-w7" style="margin: 0">{{item.value}}</span><br>
                        <!-- <span class="s-c-yellow-gradient" style="margin: 0"><span class="s-font-45 s-w7">{{ item.num }}</span>{{ item.util }}</span><br/> -->
                        <span class="s-font-30 s-c-white">{{item.name}}</span>
                      </div>
                    <!-- <i></i>
                    <span class="s-c-orange-gradient">{{ item.name }}:</span>
                    <span v-if="item.whole == ''">{{ item.num }}{{ item.util }}</span> -->
                  </li>
                </ul>
                <div class="cen_box00">
                  <div id="echarts02" style="width: 340px;height:390px"></div>
                  <div class="box002">
                    <ul>
                      <li style="font-size:35px;margin: 20px 0;" v-for="(item, i) in leftDio" :key="i" v-if="i >= 2">
                        <img src="/static/images/home_services/dnyx/dnyx-mid-left-point.png">
                        <span class="s-c-blue-gradient" style="width: 280px;display: inline-block;margin: 0 0 0 5px;">{{ item.name }}</span>
                        <span >{{ item.value }}</span>
                        <!-- <span v-if="item.whole == ''">{{ item.num }}{{ item.util }}</span>
                        <span v-text="item.name== '谋划场景情况'"></span>
                        <span v-if="item.name == '谋划场景情况'">{{ item.num }}，{{ item.whole }}，{{ item.util }}</span>
                        <span v-else-if="item.name == '物联传感设施' || item.name == 'GIS'">{{ item.num }}，{{ item.whole }}</span>
                        <span v-else-if="item.name == '系统接入情况'">业务{{ item.num }}，数改{{ item.whole }}</span>
                        <span v-else-if="item.whole != '' && item.name != '系统接入情况'">{{ item.whole }}/{{ item.num }}</span> -->
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              <div style="position:absolute;top: 680px;right: 150px;" class="carouselBtn" @click="cardright">></div>
              <el-carousel class="carousel002" :autoplay="false" arrow="never" ref="card002" trigger="click" :initial-index="initIndex" @change="cardrightChange"
                style="position: absolute;top: 277px;right:200px;width:730px; height: 1000px;overflow: hidden;transform: perspective(14em) rotateY(-4deg);">
                <el-carousel-item v-for="aa in rightBar" :key="aa" style="width: 900px;height:1000px">
                  <ul class="ul02">
                    <li v-for="(item, i) in ulRight.slice((aa - 1) * 8, (aa * 8))"  @click="showRightFun(item)" :title="item.indicator_name">
                      <img src="/static/images/home_services/dnyx/rig-after.png">
                      <span>
                        <b style="display:inline-block;max-width: 350px;height: 70px;overflow: hidden;">{{ item.indicator_name }}</b>
                      </span>
                    </li>
                  </ul>
                </el-carousel-item>
              </el-carousel>

              <div class="countAll  s-font-50 s-w7" style="top:1398px;left:2362px;transform: perspective(0em) rotateZ(9deg)">总计：{{targetCount}}</div>


              <!-- 中间脑图 -->
              <div class="cen-bg-box">
                  <img src="/static/images/home_services/dnyx/cenNao.png" class="breath-light" style="position: absolute;top: 280px;left: 560px;">
                  <img src="/static/images/home_services/dnyx/cenLine.png" style="position: absolute;top: 270px;left: 186px;">
                  <div class="revolve-box">
                    <!-- <img src="/static/images/home_services/dnyx/cen001.png" alt="" style="top: 410px;left: 80px;">
                    <img src="/static/images/home_services/dnyx/cen002.png" alt=""style="top: 620px;left: 475px;">
                    <img src="/static/images/home_services/dnyx/cen003.png" alt=""style="top: 685px;left: 1050px;">
                    <img src="/static/images/home_services/dnyx/cen004.png" alt=""style="top: 458px;left: 1345px;">
                    <img src="/static/images/home_services/dnyx/cen005.png" alt="" style="top: 96px;left: 0px;"> -->

                    <img src="/static/images/home_services/dnyx/cen001.png" >
                    <img src="/static/images/home_services/dnyx/cen002.png" >
                    <img src="/static/images/home_services/dnyx/cen003.png" >
                    <img src="/static/images/home_services/dnyx/cen004.png" >
                    <img src="/static/images/home_services/dnyx/cen005.png" >
                  </div>
              </div>
            </div>
            <div class="diong" v-if="!showRight">
              <div class="left">
                <p style="text-align: center;width:100%;height: 64px;overflow: hidden;white-space:nowrap;text-overflow: ellipsis;margin:25px 0 0 0;" :title="backTitle.tel">{{backTitle.tel}}</p>
                <p class="s-font-35 s-c-orange-gradient"><span class="s-font-70 s-w7">{{backTitle.num}}</span>{{backTitle.unit}}</p>
              </div>
              <div class="right">
                <ul>
                  <li v-for="(item,i) in diongUl" :key="item.name">
                    <i></i>
                    <div style="display:inline-block;width:1000px;overflow: hidden;white-space:nowrap;text-overflow: ellipsis;">
                       <span class="s-c-orange-gradient">{{ item.name }}</span>
                       <span :title="i==0? item.content : ''">{{ (item.content!=" " && item.content!=null)?item.content:'--' }}</span>
                    </div>
                  </li>
                </ul>
              </div>
              <span class="back" @click="showRight = true"></span>
            </div>
          </div>
        </div>
        <!--新的中间页面-显示-->
        <div class="newCenter" style="display:block">
          <div style="width:100%;height:100%;display:flex">
            <img src="/static/citybrain/dnyx/img/li.png" alt="">
            <div style="width:3030px;height:1800px;margin: 0 auto;font-family: '微软雅黑';">
              <div class="content0_2" style="margin: 46px 0px">
                <div class="content0_2_0">
                  <div class="content0_2_0_0" style="flex: 0.1">部门</div>
                  <div class="content0_2_0_0" style="flex: 0.15">首席数据官</div>
                  <div class="content0_2_0_0" style="flex: 0.08">体征指标</div>
                  <div class="content0_2_0_0" style="flex: 0.08">系统接入</div>
                  <div class="content0_2_0_0" style="flex: 0.08">场景应用</div>
                  <div class="content0_2_0_0" style="flex: 0.16">智能化要素</div>
                  <div class="content0_2_0_0" style="flex: 0.1">GIS图层</div>
                  <div class="content0_2_0_0" style="flex: 0.1">物联传感</div>
                  <div class="content0_2_0_0" style="flex: 0.1">综合贡献度</div>
                </div>
                <div class="content0_2_1">
                  <div class="content0_2_1_0" v-for="(item ,i) in tableData" :key="i" @click="clickLeftDio(i,item.部门)">
                    <div class="content0_2_1_0_0 s-w7" style="flex: 0.1;">
                      {{item.部门}}
                    </div>
                    <div class="content0_2_1_0_0" style="flex: 0.15">
                      {{item.首席数据官}}
                    </div>
                    <div class="content0_2_1_0_0" style="flex: 0.08">
                      {{item.体征指标}}
                    </div>
                    <div class="content0_2_1_0_0" style="flex: 0.08">
                      {{item.系统接入}}个
                    </div>
                    <div class="content0_2_1_0_0" style="flex: 0.08;">
                      {{item.场景应用}}个
                    </div>
                    <div class="content0_2_1_0_0" style="flex: 0.16;">
                      {{item.智能化要素}}
                    </div>
                    <div class="content0_2_1_0_0" style="flex: 0.1">
                      {{item.Gis图层}}图层
                    </div>
                    <div class="content0_2_1_0_0" style="flex: 0.1">
                      {{item.物联传感}}
                    </div>
                    <div class="content0_2_1_0_0" style="flex: 0.1">
                      {{item.综合贡献度}}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <img src="/static/citybrain/dnyx/img/li.png" alt="">
          </div>
          <div class="left-diong" v-show="showLeft">
            <div class="close" @click="backMiddle"><i class="el-icon-close"></i></div>
            <div style="margin: 60px 0 0;display:flex;align-items: center;justify-content: center;">
              <div class="top-l">
                <div class="line"></div>
                <div class="point"></div>
              </div>
              <span class="lineName">{{leftBarName}}</span>
              <div class="top-l">
                <div class="point"></div>
                <div class="line"></div>
              </div>
            </div>
            <ul style="display: flex;justify-content: space-around;width: 100%;margin-top:20px;">
              <li v-for="(item, i) in leftDio" :key="i" v-if="i < 2" style="display:flex;">
                <img :src="i===2 ?'/static/images/home_services/dnyx/dnyx-mid-left01.png':'/static/images/home_services/dnyx/dnyx-mid-left02.png'" alt="">
                <div style="text-align: left;width: 100%;">
                  <span class="s-c-yellow-gradient s-font-45 s-w7" style="margin: 0">{{item.value || '-'}}</span><br>
                  <span class="s-font-30 s-c-white">{{item.name}}</span>
                </div>
              </li>
            </ul>
            <div class="cen_box00">
              <div id="echarts03" style="width: 340px;height:390px"></div>
              <div class="box002">
                <ul>
                  <li style="font-size:35px;margin: 20px 0;" v-for="(item, i) in leftDio" :key="i" v-if="i >= 2">
                    <img src="/static/images/home_services/dnyx/dnyx-mid-left-point.png">
                    <span class="s-c-blue-gradient" style="width: 280px;display: inline-block;margin: 0 0 0 5px;">{{ item.name }}</span>
                    <span class="s-c-white">{{ item.value}}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="bottom-btn">
            <div class="bottom-btn-item" v-for="(item,index) in btnList" :key="index"
                 @click="btnChange(index)"
                 :class="btnIndex===index?'bottom-btn-item-active':''">
              {{item}}
            </div>
          </div>
        </div>
        <!-- 右侧 -->
        <div class="s-abso"
          style="font-family:'思源黑体 CNBOLD';width: 2190px;height: 1930px;left: 5420px;font-weight: 700;background: url('/img/right-bg.png') no-repeat;background-size: 100% 100%;">
          <div class="abso-item" style="width: 100%;height:37.5%;">
            <a>
              <s-header-title width="762px" title="智能化组件" htype="1"></s-header-title>
            </a>
            <div class="abso-con">
              <el-row>

                <el-col :span="12">
                  <div class="title_icon" style="margin:10px 0 -10px 0;">
                    <i class="icon_left"></i>
                    <h2>算法</h2>
                    <i class="icon_right"></i>
                  </div>
                  <div class="item-lists">
                    <div class="s-m-t-30" v-for="(item, index) in sfList" :key="`sf-` + index"
                      @click="changeTab('sf-' + index, item)">
                      <div style="width: 154px;height: 100%;position: relative;">
                        <img class="breath-light" :src="tabIndex === 'sf-' + index ? actBackUrl : backUrl" />
                        <img :src='item.icon' class="item-icon" />
                        <span class="item-text breath-light"
                          :class="tabIndex === 'sf-' + index ? 'item-text-active' : 'item-text'">
                          {{ item.num }}
                        </span>
                        <div class="s-text-center s-rela"
                          style="width: 90%;margin-left: 5%;top: -70px;color: #FFFFFF;font-size: 28px;font-weight: 400;">
                          <span>{{ item.title }}</span><br>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <!-- 二级导肮 -->
                  <div class="title_icon" style="margin:10px 0 -10px 0;">
                    <i class="icon_left"></i>
                    <h2>模型</h2>
                    <i class="icon_right"></i>
                  </div>
                  <div class="item-lists">
                    <div class="s-m-t-30" v-for="(item, index) in mxList" :key="`mx-` + index"
                      @click="changeTab('mx-' + index, item)">
                      <div style="width: 154px;height: 100%;position: relative;">
                        <img class="breath-light" :src="tabIndex === 'mx-' + index ? actBackUrl : backUrl" />
                        <img :src='item.icon' class="item-icon" />
                        <span class="item-text breath-light"
                          :class="tabIndex === 'mx-' + index ? 'item-text-active' : 'item-text'">
                          {{ item.num }}
                        </span>
                        <div class="s-text-center s-rela"
                          style="width: 90%;margin-left: 5%;top: -70px;color: #FFFFFF;font-size: 28px;font-weight: 400;">
                          <span>{{ item.title }}</span><br>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <!-- 二级导肮 -->
                  <div class="title_icon" style="margin:10px 0 -10px 0;">
                    <i class="icon_left"></i>
                    <h2>智能模块</h2>
                    <i class="icon_right"></i>
                  </div>
                  <div class="item-lists">
                    <div class="s-m-t-30" v-for="(item, index) in znList" :key="`zn-` + index"
                      @click="changeTab('zn-' + index, item)">
                      <div style="width: 154px;height: 100%;position: relative;">
                        <img class="breath-light" :src="tabIndex === 'zn-' + index ? actBackUrl : backUrl" />
                        <img :src='item.icon' class="item-icon" />
                        <span class="item-text breath-light"
                          :class="tabIndex === 'zn-' + index ? 'item-text-active' : 'item-text'">
                          {{ item.num }}
                        </span>
                        <div class="s-text-center s-rela"
                          style="width: 90%;margin-left: 5%;top: -70px;color: #FFFFFF;font-size: 28px;font-weight: 400;">
                          <span>{{ item.title }}</span><br>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="title_icon" style="margin:10px 0 -10px 0;">
                    <i class="icon_left"></i>
                    <h2>专题</h2>
                    <i class="icon_right"></i>
                  </div>
                  <div class="item-lists">
                    <div class="s-m-t-30" v-for="(item, index) in ztList" :key="`zt-` + index"
                      @click="changeTab('zt-' + index, item)">
                      <div style="width: 154px;height: 100%;position: relative;">
                        <img class="breath-light" :src="tabIndex === 'zt-' + index ? actBackUrl : backUrl" />
                        <img :src='item.icon' class="item-icon" />
                        <span class="item-text breath-light"
                          :class="tabIndex === 'zt-' + index ? 'item-text-active' : 'item-text'">
                          {{ item.num }}
                        </span>
                        <div class="s-text-center s-rela s-font-30"
                          style="width: 90%;margin-left: 5%;top: -70px;color: #FFFFFF;">
                          <span>{{ item.title }}</span><br>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="abso-item" style="width: 100%;height:62.5%">
            <a>
              <s-header-title width="762px" title="支撑平台" htype="1"></s-header-title>
            </a>
            <div class="abso-con">
              <el-row>
                <el-col :span="12">
                  <!-- 二级导肮 -->
                  <div class="title_icon" style="margin:10px 0 -10px 0;">
                    <i class="icon_left"></i>
                    <h2>高分遥感卫星影像平台</h2>
                    <i class="icon_right"></i>
                  </div>
                  <div class="item-lists-2" style="    margin-top: 40px;">
                    <!-- <div class="lists-2-top" @click="clickLeftDio('showBmyy5','')">
                      <span style="font-size:30px;color: #FFFFFF;line-height: 37px;margin-right:10px">数据支撑同步用户</span>
                      <div class="count-flop" :key="compKey">
                        <div :class="item != '.' ? 'count-flop-box' : 'count-flop-point'" v-for="(item, index) in value"
                          :key="index">
                          <div v-if="item != '.'" class="count-flop-content" :class="['rolling_' + item]">
                            <div v-for="(item2, index2) in numberList" :key="index2" class="count-flop-num">{{ item2 }}
                            </div>
                          </div>
                          <div v-else class="count-flop-content">.</div>
                        </div>
                      </div>
                      <span style="font-size:30px;color: #FFFFFF;line-height: 37px;">户</span>
                    </div> -->
                    <div style="display: flex;">
                      <div style="width:37%">
                        <div class="lists-2-title">
                          <img src="/static/images/home_services/dnyx/list2-title.png" alt="">
                          数据总量
                        </div>
                        <div class="lists-2-con">
                          <img src="/static/images/home_services/dnyx/lists2-1.png" alt="">
                          <span>
                            数据总量<br><span class="s-c-blue-gradient s-font-60">85</span>景
                          </span>
                        </div>
                        <div class="lists-2-con">
                          <img src="/static/images/home_services/dnyx/lists2-1.png" alt="">
                          <span>
                            当月新增<br><span class="s-c-blue-gradient s-font-60">4</span>景
                          </span>
                        </div>
                      </div>
                      <div style="width:60%">
                        <div class="lists-2-title">
                          <img src="/static/images/home_services/dnyx/list2-title.png" alt="">
                          各类型数据占比
                        </div>
                        <div id="echarts10" style="width: 100%;height:280px"></div>
                      </div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="title_icon" style="margin:10px 0 -10px 0;">
                    <i class="icon_left"></i>
                    <h2>物联感知接入平台</h2>
                    <i class="icon_right"></i>
                  </div>
                  <div class="item-lists-2" style="padding:20px;">
                    <!-- <div class="wlgz-title" @click="changeWlgz(1000)" :class="wlgzIndex === 1000 ? 'wlgz-title1' : ''">
                      <img src="/static/images/home_services/dnyx/part-icon-0.png" alt="">
                      数据采集
                      <span>
                        <count-to :start-val="0" :end-val="8088" :duration="3000" class="s-font-45"
                          :class="wlgzIndex === 1000 ? 's-c-yellow-gradient' : 's-c-blue-gradient'">
                        </count-to>
                        <span class="s-font-30">条</span>
                      </span>
                    </div> -->
                    <div class="wlgz-content">
                      <el-row>
                        <el-col :span="12" v-for="(item, index) in jrptList" :key="index">
                          <div class="wlgz-content-part" :class="wlgzIndex === index ? 'wlgz-content-part1' : ''"
                            @click="changeWlgz(index)">
                            <img :src="item.img" alt="">
                            {{ item.title }}
                            <span>
                              <count-to :start-val="0" :end-val="Number(item.num)" :duration="3000" class="s-font-45"
                                :class="wlgzIndex === index ? 's-c-yellow-gradient' : 's-c-blue-gradient'"></count-to>
                              <span class="s-font-30 s-c-blue-gradient">{{ item.unit }}</span>
                            </span>
                          </div>
                        </el-col>
                      </el-row>
                    </div>
                  </div>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="12">
                  <!-- 二级导肮 -->
                  <div class="title_icon" style="margin:10px 0 -10px 0;">
                    <i class="icon_left"></i>
                    <h2>二三维GIS平台</h2>
                    <i class="icon_right"></i>
                  </div>
                  <div class="item-lists-2">
                    <div style="width:100%;height:65%">
                      <div class="lists-2-title">
                        <img src="/static/images/home_services/dnyx/list2-title.png" alt="">
                        数据情况
                      </div>
                      <div class="gis-list">
                        <div class="gis-item" v-for="(item, index) in sjqkList1" :key="`line1-` + index">
                          <img :src="item.icon" alt="">
                          <span style="white-space: nowrap;">
                            {{ item.title }}<br><span class="s-c-blue-gradient">{{ item.num }}{{item.dw}}</span>
                          </span>
                        </div>
                      </div>
                      <div class="gis-list" style="justify-content: space-between;">
                        <div class="gis-item" v-for="(item, index) in sjqkList2" :key="`line2-` + index">
                          <img :src="item.icon" alt="">
                          <span style="white-space: nowrap;">
                            {{ item.title }}<br><span class="s-c-blue-gradient">{{ item.num }}{{item.dw}}</span>
                          </span>
                        </div>
                      </div>
                    </div>
                    <div style="width:100%;height:35%">
                      <div class="lists-2-title">
                        <img src="/static/images/home_services/dnyx/list2-title.png" alt="">
                        服务情况
                      </div>
                      <div class="gis-list">
                        <div class="gis-item gis-item-bottom" style="position: relative;">
                          <img src="/static/images/home_services/dnyx/gis-item-left.png" alt=""
                            style="position: absolute;left:0">
                          <img src="/static/images/home_services/dnyx/gis-item-right.png" alt=""
                            style="position: absolute;right:0">
                          <span>
                            支撑市级部门<br><span class="s-c-yellow-gradient s-font-40">4<span
                                class="s-font-30">个</span></span>
                          </span>
                        </div>
                        <div class="gis-item gis-item-bottom" style="position: relative;">
                          <img src="/static/images/home_services/dnyx/gis-item-left.png" alt=""
                            style="position: absolute;left:0">
                          <img src="/static/images/home_services/dnyx/gis-item-right.png" alt=""
                            style="position: absolute;right:0">
                          <span>
                            支撑市级平台<br><span class="s-c-yellow-gradient s-font-40">4<span
                                class="s-font-30">个</span></span>
                          </span>
                        </div>
                      </div>

                    </div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="title_icon" style="margin:10px 0 -10px 0;">
                    <i class="icon_left"></i>
                    <h2>视频融合平台</h2>
                    <i class="icon_right"></i>
                  </div>
                  <div class="item-lists-2">
                    <div style="width:100%;height:40%">
                      <div class="lists-2-title">
                        <img src="/static/images/home_services/dnyx/list2-title.png" alt="">
                        视频资源
                      </div>
                      <div style="display:flex;justify-content: space-evenly;">
                        <div class="lists-2-top" style="border:none;background:none;">
                          <img src="/static/images/home_services/dnyx/sprh-top.png" alt="" style="margin-right:20px">
                          <span style="font-size:30px;color: #FFFFFF;line-height: 37px;margin-right:10px;">接入数量</span>
                          <div class="count-flop" :key="compKey1">
                            <div :class="item != '.' ? 'count-flop-box' : 'count-flop-point'"
                              v-for="(item, index) in value1" :key="index">
                              <div v-if="item != '.'" class="count-flop-content" :class="['rolling_' + item]">
                                <div v-for="(item2, index2) in numberList" :key="index2" class="count-flop-num">{{ item2
                                }}
                                </div>
                              </div>
                              <div v-else class="count-flop-content">.</div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <div style="display:flex;">
                            <img src="/static/images/home_services/dnyx/sprh-num-1.png" alt="" width="19px"
                              height="19px" style="margin: 10px 10px 0 0;">
                            <span class="s-c-orange-gradient" style="margin-right:10px;">已治理数量</span>
                            <span>{{ handleNum }}</span>
                          </div>
                          <div style="display:flex ;">
                            <img src="/static/images/home_services/dnyx/sprh-num-1.png" alt="" width="19px"
                              height="19px" style="margin: 10px 10px 0 0;">
                            <span class="s-c-orange-gradient" style="margin-right:10px;">待治理数量</span>
                            <span>{{ notHandleNum }}</span>
                          </div>
                        </div>

                      </div>
                    </div>
                    <div style="width:100%;height:60%;display: flex;">
                      <div style="width:65%;height:100%">
                        <div class="lists-2-title">
                          <img src="/static/images/home_services/dnyx/list2-title.png" alt="">
                          部门点位分布
                        </div>
                        <div class="dwfb-item">
                          <ul>
                            <li v-for="(item, index) in dwfbList" :key="index">
                              <span class="dwfb-item-title">{{ item.title }}</span>
                              <progress class="progress" max="100" :value="item.num"></progress>
                              <span class="dwfb-item-value">{{ item.num }}</span>
                              <span class="dwfb-item-value">{{ item.num }}%</span>
                            </li>
                          </ul>
                        </div>
                      </div>
                      <div style="width:35%;height:100%">
                        <div class="lists-2-title">
                          <img src="/static/images/home_services/dnyx/list2-title.png" alt="">
                          识别情况
                        </div>
                        <div class="sbqk-item">
                          <img src="/static/images/home_services/dnyx/sbqk-1.png" alt="">
                          <span>
                            人脸识别<br><span class="s-c-blue-gradient s-font-40">
                              --<span class="s-font-30">个</span></span>
                          </span>
                        </div>
                        <div class="sbqk-item">
                          <img src="/static/images/home_services/dnyx/sbqk-2.png" alt="">
                          <span>
                            车辆识别<br><span class="s-c-blue-gradient s-font-40">
                              --<span class="s-font-30">辆</span></span>
                          </span>
                        </div>
                      </div>

                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <!--数据支撑同步用户弹框-->
            <div class="Bmyy5-diong" v-show="showBmyy5">
              <div class="s-c-blue-gradient Bmyy5-title">部门应用TOP5
                <i class="el-icon-close" style="position: relative;right:-443px;top:0px" @click="showBmyy5 = false"></i>
              </div>

              <ul>
                <li v-for="(item, i) in leftCen2" :key="i">
                  <img src="/static/images/home_services/dnyx/one01.png" style="margin-bottom:-13px;" v-if="i == 0">
                  <img src="/static/images/home_services/dnyx/two02.png" style="margin-bottom:-13px;" v-if="i == 1">
                  <img src="/static/images/home_services/dnyx/three03.png" style="margin-bottom:-13px;" v-if="i == 2">
                  <span class="s-w7 s-font-40 s-sty" v-if="i != 0 && i != 1 && i != 2">{{ i + 1 }}&nbsp;</span>
                  <span style="width: 200px;display: inline-block;">{{ item.name }}</span>
                  <progress class="progress" max="100" :value="item.value"> </progress>
                  <span class="s-c-blue-gradient">{{ item.num }}</span>
                </li>
              </ul>
            </div>
          </div>

        </div>
        <iframe src="/static/citybrain/dnyx/dnyx-diong-wulian.html"
          style="position: absolute; z-index: 99; left: 31.7%; top: 150px" width="2900px" height="1600px"
          frameborder="0" id="dnyxIframe" v-show="showIframe"></iframe>
      </div>
    </div>
  </body>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/js/jslib/Odometer.js"></script>

  <script type="text/javascript" src="https://cdn.bootcss.com/animejs/2.2.0/anime.min.js"></script>

  <script>
  // i=0;
  // DI=document.getElementsByClassName('revolve-box')[0].children;
  // DIL=DI.length;
  // var width = 100;
  // var height = 100;
  // function A(){
  //     for(var index=0;index<DIL;index++){
  //         DIS=DI[index].style;
  //         DIS.position='absolute';
  //         var speed = 0.005;
  //         var interV = 2*Math.PI/speed/DIL;
  //         DIS.left = Math.sin(i*speed+index*interV*speed)*(width/3)+width/2;      // the speed is change's range
  //         DIS.top = Math.cos(i*speed+index*interV*speed)*(height/3)+height/2;
  //     }
  //     i++;
  // }
  // setInterval('A()',1000)


   var vm =  new Vue({
    el: " #home-main",
    data() {
      return {
        mouseData:[
          {
            title:'数据编目',
            arr:[
            {
              name:'今年新增目录数（个）',
              num:6582,

            },
            {
              name:'现有目录数（个）',
              num:16858,

            },
            {
              name:'今年新增目录数据项（万项）',
              num:10.54,

            },
            {
              name:'现有目录数据项（万项）',
              num:25.68,

            }
            ]
          },
          {
            title:'数据归集',
            arr:[{
              name:'现有归集数据量（亿条）',
              num:545,

            },
            {
              name:'现有归集数据表（类）',
              num:11158,

            }
          ]

          },
          {
            title:'数据治理',
            arr:[{
              name:'今年数据治理总量（万条）',
              num:77576,

            },
            {
              name:'上月数据治理量（万条）',
              num:511,

            },
            {
              name:'今年发现问题数据量（万条）',
              num:182.2,

            },
            {
              name:'上月发现问题数据量（万条）',
              num:1.22,

            },
            {
              name:'数据问题及时治理率',
              num:'100%'

            }
          ]
          },
          {
            title:'数据共享',
            arr:[{
              name:'数据共享接口数（个）',
              num:1276,

            },
            {
              name:'今年接口调用总量（万次）',
              num:88592,

            },
            {
              name:'上月接口调用量（万次）',
              num:3711,

            },
            {
              name:'批量数据共享（类）',
              num:2867,

            },

          ]
          },
          {
            title:'数据开放',
            arr:[{
              name:'今年新增开放数据集（个）',
              num:173,

            },
            {
              name:'现有开放数据集（个）',
              num:958,

            },
            {
              name:'累计开放数据总量（万条）',
              num:23767,

            },

          ]
          },
        ],
        mouseTable:false,
        mouseTableData:  {
            title:'数据编目',
            arr:[
            {
              name:'今年新增目录数（个）',
              num:6582,

            },
            {
              name:'现有目录数（个）',
              num:16858,

            },
            {
              name:'今年新增目录数据项（万项）',
              num:10.54,

            },
            {
              name:'现有目录数据项（万项）',
              num:25.68,

            }
            ]
          },

        initIndex:0,
        initIndex1:0,
        leftBarName:"",
        leftBar:'',
        rightBar:'',
        indexId:-1,
        showLeft:false,
        showRight:true,
        showBmyy5:false,
        leftBtn: false,
        leftTopInfo: [],
        leftCen: [],
        leftCen1: [],
        leftCen2: [],
        leftCen3: [],
        leftzb: [],
        leftfw: [],
        cenData: [],
        xtData: null,
        xtData1: null,
        xtData2: null,
        xtData3: null,
        ulLeft:[],
        ulRight:[],
        inNum:'86.686.686',
        leftDio:[],
        satisfaction:0,
        departmentCount:'',
        targetCount:'',
        zhibcount:0,//左侧指标总数
        zhibover:0,//左侧指标已接入
        backTitle:{tel:"",num:"",unit:""},
        diongUl:[
          {name:"指标定义：",content:null},
          {name:"负责部门：",content:null},
          {name:"业务负责人：",content:null},
          {name:"分管领导：",content:null},
          {name:"更新频率：",content:null},
          {name:"接口调用时间：",content:null},
          {name:"数据变动时间：",content:null},
        ],
        mxList:[],
        sfList:[],
        znList:[],
        ztList:[],
        tabIndex:null,
        backUrl:"/static/images/home_services/dnyx/back-mini-bg.png",
        actBackUrl:"/static/images/home_services/dnyx/back-mini-active.png",
        sjqkList1:[],
        sjqkList2:[],
        jrptList:[],
        dwfbList:[],
        flow_num_1: '000',
        flow_num_2: '00000000',
        handleNum:0,
        notHandleNum:0,
        wlgzIndex:null,
        showIframe:false,

        value: ['0','0','0','0'],
        value1: ['0','0','0','0','0'],
        value1_1: ['0','0','0','0','0'],
        value2: ['0','0','0','0'],
        numberList: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
        compKey: 0,
        compKey1: 0,
        compKey2: 0,
        btnList:['市级','县(市、区)'],
        btnIndex:0,
        leftTopInfoText:[
          {
            name:'编目',
            title:'今年新增目录数',
            unit:'个'
          },
          {
            name:'归集',
            title:'今年归集数据量',
            unit:'亿条'

          },
          {
            name:'治理',
            title:'今年数据治理总量',
            unit:'万条'

          },
          {
            name:'共享',
            title:'今年接口调用总量',
            unit:'万次'

          },
          {
            name:'开放',
            title:'今年新增开放数据集',
            unit:'个'

          },

        ],
        //中间表格
        tr_1_data:[],
        tableData: [],
      };
    },
    // watch: {
    //   satisfaction(newVal){
    //     if(newVal===true){
    //       this.getEcharts01("echarts03", ["#0D9BCA", "#0bbef9"], this.satisfaction)
    //     }
    //   }
    // },
    created(){
      let this_ = this
      window.addEventListener('message',function (event){
        this_.showIframe = event.data
      })
    },
    mounted() {
      this.initApi();
      this.revolveRun();
      // this.getEcharts01("echarts03", ["#0D9BCA", "#0bbef9"], 0.55)
      this.getEcharts02("echarts01", "入库率", 0, "#00C0FF", "#005470")
      this.getEcharts10("echarts10")
      this.queryTablaData()

      var this_ = this
      this_.flow_num_2='78123456'
      setTimeout(function () {
        this_.flow_num_1='858'
        this_.flow_num_2='12345678'
      }, 1000);

      var val = 1088
      var val2 = '000.000.000'
      this.value = val.toString().split("");
      this.value2 = val2.toString().split("");

      this.compKey += 1;
      this.compKey1 += 1;
      this.compKey2 += 1;
    },
    methods: {
      
      togUrl() {
        top.commonObj.openWinHtml(7680,2160,"/img/bmjr/ggsjpt-page.png");
      },
      backMiddle(){
        this.showLeft=false
      },
      mouseOver(index){
        this.mouseTableData = this.mouseData[index]

        if(index==0){

          document.querySelector('.tc_table').style.left='0px'
          document.querySelector('.sjx').style.left='100px'

        }else if(index===1){


          document.querySelector('.tc_table').style.left='300px'
          document.querySelector('.sjx').style.left='220px'


        }else if(index===2){


          document.querySelector('.tc_table').style.left='650px'
          document.querySelector('.sjx').style.left='290px'


        }else if(index===3){
          document.querySelector('.tc_table').style.left='1100px'
          document.querySelector('.sjx').style.left='260px'


        }else if(index===4){
          document.querySelector('.tc_table').style.left='1500px'
          document.querySelector('.sjx').style.left='290px'


        }
        this.mouseTable=true
      },
      mouseOut(){
        this.mouseTable=false

      },
      //中间表格数据
      queryTablaData(){
        $api("/khpjCenter121",).then(res=>{
          this.tableData=res
        })
        // $api("/dnyxLeft008").then(res=>{
        //   let data = res
        //   this.tableData=[]
        //   for(let i=0;i<data.length;i++){
        //     $api("/dnyxLeft009",{code:data[i].source_dept}).then(res=>{
        //       let list={
        //         bm:data[i].source_dept,
        //         sxsjg:res[2].value,
        //         tzzb:res[3].value,
        //         xtjr:res[4].value,
        //         cjyy:res[5].value,
        //         znhys:res[6].value,
        //         gistc:res[7].value,
        //         wlcg:res[8].value,
        //         zhgxd:55,
        //       }
        //       this.tableData.push(list)
        //     })
        //   }
        // })
      },
      btnChange(index){
        this.btnIndex=index
        if(this.btnIndex===1){
          top.commonObj.openMenuFun('gbmyxqk')
        }
      },
      revolveRun(){
      //   let box=document.getElementsByClassName('revolve-box')[0].children;
      //  setInterval(()=>{
      //   for (let i = 0; i < box.length; i++) {
          // dis=box[i].style;
          // dis.position='absolute';
          // var speed=0.005;
          // var interV=2*Math.PI/speed/box.length;
          // dis.left=Math.sin(i*speed+i*interV*speed)*(93/3)+93/2;
          // dis.top=Math.cos(i*speed+i*interV*speed)*(96/3)+96/2;
      //   }
      //  },1000)
      },
      cardrightChange(current_index){
        this.initIndex = current_index
      },
      cardleftChange(current_index){
        this.initIndex1 = current_index
      },
      cardleft(){
        this.$refs.card001.next()
      },
      cardright(){
        this.$refs.card002.next()
      },
      openIfram(){
        let leftData = {
          type: 'openIframe',
          name: 'dnyx-diong-wulian',
          src: baseURL.url + '/static/citybrain/dnyx/dnyx-diong-wulian.html',
          width: '2800px',
          height: '1500px',
          left: '2667px',
          top: '264px',
          // argument: name
        }
        window.parent.postMessage(
          JSON.stringify(leftData), '*'
        )
      },
      closeIfram(){
        let data = JSON.stringify({
          type: 'closeIframe',
          name: "dnyx-diong-wulian",
        })
        window.parent.postMessage(data, '*')
      },
      sortByUp(arr) {//降序
        return function (a, b) {
          return b[arr] - a[arr]
        }
      },
      sortByLow(arr) {//升序
        return function (a, b) {
          return a[arr]- b[arr]
        }
      },
      //切换
      changeTab(index,item){
        // this.tabIndex=index
        // this.showIframe=true
        // this.indexId=-1
        // this.showLeft=false;
        // this.showBmyy5=false
        // document.getElementById("dnyxIframe").contentWindow.postMessage(item, "*")
      },

      changeWlgz(i){
        this.wlgzIndex=i
      },
      clickLeftDio(i,name){
        this.leftBarName=name;
        // $api("/dnyxLeft009",{code:name}).then(res=>{
        //   this.leftDio=res;
        // })
        $api("/khpjCenter122",{code:name}).then(res=>{
          this.leftDio=res.slice(0,9);
          this.satisfaction = res[9].value
          this.getEcharts01("echarts03", ["#0D9BCA", "#0bbef9"], this.satisfaction/100)
        })

        // $api("/dnyxLeft012",{code:name}).then(res=>{
        //   this.targetCount=res.length;
        //   this.ulRight=res;
        //   this.rightBar=Math.ceil(res.length/8);
        // })
        if(this.indexId===i){
          this.showLeft=false;
          this.indexId=-1
          this.showBmyy5=false
          this.showIframe=false
        }else if('showBmyy5'=== i){
          this.showBmyy5=true
          this.showIframe=false
          this.indexId=-1
          this.showLeft=false;
        }else{
          this.showLeft=true
          this.indexId=i
          this.showBmyy5=false
          this.showIframe=false
        }
      },
      showRightFun(row){
        let that=this;
          $api("/dnyxLeft013",{code:row.id}).then(res=>{
          that.diongUl[0].content=res[0].description,
          that.diongUl[1].content=res[0].source_dept,
          that.diongUl[2].content=res[0].business_resp,
          that.diongUl[3].content=res[0].business_leader,
          that.diongUl[4].content=res[0].update_freq
          that.backTitle.tel=row.indicator_name
          that.backTitle.num=res[0].current_value
          that.backTitle.unit=res[0].unit
        })
        this.showRight = false
        this.showLeft=false;
        this.indexId=-1
        this.showBmyy5=false
        this.showIframe=false
      },
      async initApi() {
        let that = this;

        //数据接入数据
        $api("/khpjLeft111",{code:1}).then(res=>{
        //$api("/dnyxLeft002").then(res=>{
          let result = []
          res.forEach(item=>{
            that.leftTopInfoText.forEach(obj=>{
              if(item.name==obj.name){
                console.log(item);
                result.push({...item,text:obj.title,unit:obj.unit})
              }
            })
          })
          console.log(result);
          that.leftTopInfo = result



        })

        // $api("/dnyxLeft008").then(res=>{
        //   that.departmentCount=res.length;
        //   that.ulLeft=res;
        //   that.leftBar=Math.ceil(res.length/7);

        //   $api("/dnyxLeft012",{code:that.ulLeft[0].source_dept}).then(res=>{
        //     that.targetCount=res.length;
        //     that.ulRight=res;
        //     that.rightBar=Math.ceil(res.length/8);
        //   })
        // })

        that.mxList =await $api("/khpjRight111",{code:1})
        that.sfList =await $api("/khpjRight111",{code:2})
        that.znList =await $api("/khpjRight111",{code:3})
        that.ztList =await $api("/khpjRight111",{code:4})
        await $api("/khpjRight111",{code:5}).then(res=>{
          that.sjqkList1 = res.slice(0,4)
          that.sjqkList2 = res.slice(4,9)
        });
        await $get('/yndx/yndx011-6').then(res=>{
          that.jrptList =res
          if(res){
            $api("/wlgzLeft001").then(res1=>{
              that.jrptList[2].num = res1[0].count/10000
              that.jrptList[2].unit = "万条"
              // that.jrptList[3].num = res1[0].device_type_num
              that.jrptList[3].num = 20
              that.jrptList[4].num = res1[0].dept_num
              that.jrptList[5].num = res1[0].catalog_num
            })
            $api("/wlgzLeft002").then(res2=>{
              that.jrptList[0].num = res2[0].device_num
              that.jrptList[1].num = res2[0].online
            })
          }
        });


        that.dwfbList =await $get('/yndx/yndx011-7');

        //旧接口
        // await $api("/dnyxLeft003",{code:1}).then(res=>{
        //   let dataNew=res.sort(this.sortByUp('num')).splice(0,5)
        //   this.showLineEcharts(dataNew,'lineEcharts_New0','/static/images/home_services/dnyx/green-icon.png','#caffff','#49fffa')
        // })
        // await $api("/dnyxLeft003",{code:2}).then(res=>{
        //   let dataNew=res.sort(this.sortByLow('num')).splice(0,5)
        //   this.showLineEcharts(dataNew,'lineEcharts_New1','/static/images/home_services/dnyx/red-icon.png','#ffcdcd','#ff4949')
        // })
        // await $api("/dnyxLeft003",{code:3}).then(res=>{
        //   let dataNew=res.sort(this.sortByUp('num')).splice(0,5)
        //   this.showLineEcharts(dataNew,'lineEcharts_New2','/static/images/home_services/dnyx/green-icon.png','#caffff','#49fffa')
        // })
        // await $api("/dnyxLeft003",{code:4}).then(res=>{
        //   let dataNew=res.sort(this.sortByLow('num')).splice(0,5)
        //   this.showLineEcharts(dataNew,'lineEcharts_New3','/static/images/home_services/dnyx/red-icon.png','#ffcdcd','#ff4949')
        // })
        //新接口
        await $api("/khpjLeft121",{code:11}).then(res=>{
          let dataNew=res.sort(this.sortByUp('num')).splice(0,5)
          this.showLineEcharts(dataNew,'lineEcharts_New0','/static/images/home_services/dnyx/green-icon.png','#caffff','#49fffa')
        })
        await $api("/khpjLeft122",{code:11}).then(res=>{
          let dataNew=res.sort(this.sortByLow('num')).splice(0,5)
          this.showLineEcharts(dataNew,'lineEcharts_New1','/static/images/home_services/dnyx/red-icon.png','#ffcdcd','#ff4949')
        })
        await $api("/khpjLeft121",{code:12}).then(res=>{
          let dataNew=res.sort(this.sortByUp('num')).splice(0,5)
          this.showLineEcharts(dataNew,'lineEcharts_New2','/static/images/home_services/dnyx/green-icon.png','#caffff','#49fffa')
        })
        await $api("/khpjLeft122",{code:12}).then(res=>{
          let dataNew=res.sort(this.sortByLow('num')).splice(0,5)
          this.showLineEcharts(dataNew,'lineEcharts_New3','/static/images/home_services/dnyx/red-icon.png','#ffcdcd','#ff4949')
        })


        await $api("/khpjLeft123",{code:1}).then(res=>{
          that.zhibcount = res[0].num.toString()
          that.zhibover = res[1].num.toString()
        })

        // that.leftzb = await $api("/dnyxLeft004",{code:1})
        // // that.leftfw = await $api("/dnyxLeft004",{code:2})
        // that.cenData = await $api("/dnyxCenter011")

        // await $api("/dnyxLeft006",{code:0}).then(res=>{
        //   console.log(res[0]);
        //   that.xtData = res[0].total
        // })
        // await $api("/dnyxLeft006",{code:1}).then(res=>{
        //   that.xtData1 = res[0].total
        // })
        // await $api("/dnyxLeft006",{code:2}).then(res=>{
        //   that.xtData2 = res[0].total
        // })
        await $api("/khpjLeft131",{code:1}).then(res=>{
          that.xtData = res[0].num
          that.xtData1 = res[1].num
          that.xtData2 = res[2].num
          that.xtData3 = res[3].num
        })

        //接入数量
        await $api("/vslLeft001").then(res=>{
          if(res) {
            var val0=res[0].total;
            that.value1 = val0.toString().split("")
            that.value1_1 = that.value1
          }else{
            var val0='0';
            that.value1 = val0.toString().split("")
          }

        })

        //未治理数量
        await $api("/vslRight001").then(res=>{
          if(res) {
            that.notHandleNum = res[0].total
          }
        })
        //已治理数量
        await $api("/vslRight002").then(res=>{
          if(res) {
            that.handleNum = res[0].total
          }
        })

        // that.xtData1 = await $api("/dnyxLeft006",{code:1})
        // that.xtData2 = await $api("/dnyxLeft006",{code:2})
        // that.rightBoxData.model = await $api("/dnyxLeft007",{code:1})
        // that.rightBoxData.algorithm = await $api("dnyxLeft007",{code:2})
        // that.rightBoxData.smart = await $api("/dnyxLeft007",{code:3})
        // that.rightBoxData.Special = await $api("/dnyxLeft007",{code:4})
      },

      // 排行进度条
      showLineEcharts(datas,id,img,textColor,lineColor){
        let echarts0 = echarts.init(document.getElementById(id));

        // let symbolImg = 'image://'+ require(`${img}`)
        let data = datas
        let valueData=data.map(item=>{
          return item.value
        })
        let  sum = eval(valueData.join('+'));
        let bfbData=this.countPercentage(valueData)
        getArrByKey = (data, k) => {
          let key = k || "value";
          let res = [];
          if (data) {
            data.forEach(function (t) {
              res.push(t[key]);
            });
          }
          return res;
        };
        getSymbolData = (data) => {
          let arr = [];
          for (var i = 0; i < data.length; i++) {
            arr.push({
              value: data[i].value,
              symbolPosition: "end",
            });
          }
          return arr;
        };

        let opt = {
          index: 0,
        };
        let color = ["#A71A2B"];
       let option = {

          grid: {
            top: "6%",
            bottom: '-10%',
            left: '2%',
            right:'2%',
            containLabel: true,
          },
          xAxis: {
            show: false,
          },
          yAxis: [
            {
              triggerEvent: true,
              show: true,
              inverse: true,
              data: getArrByKey(data, "name"),
              axisLine: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                show: false,
                interval: 0,
                color: "#fff",
                align: "left",
                fontSize: 30,
                formatter: function (value, index) {
                  return "{title|" + value + "}";
                },
                rich: {
                  title: {
                    width: 100,
                  },
                },
              },
            },
            {
              triggerEvent: true,
              show: true,
              inverse: true,
              data: valueData,
              axisLine: {
                show: false,
              },
              splitLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                shadowOffsetX: "-20px",
                color: textColor,
                align: "right",
                verticalAlign: "bottom",
                lineHeight: 50,
                fontSize: 30,
                formatter: function (value, index) {
                  return value+'条';
                },
              },
            }
          ],
          series: [
            {
              name: "XXX",
              type: "pictorialBar",
              // symbol:
              //   "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAMAAADWZboaAAAAZlBMVEUAAABe3uVe3+Vf3uVf3        +Zf3uVg3+Zg3+Zf3+Vi4OZh4OZg3+Z86/Bh3+Zi4Odj4Odi4OZ86/B76/B86/Bj4ed56+9x5+xn4umB7/N87PB36e+A7/N+7fF/7vJ/7vJ        +7fGA7/OB7/PReX+lAAAAIXRSTlMABQkVDREmIhk3MR10LEFFPHh7cUprXE35h2XnqMLAp        +mHAG9cAAAB5ElEQVRIx83WjU7CMBQFYIoiKMqU/XUboHv/l/Tce7t2XamDNSacETEmX86tlK2rx4py150o       +MstMBLwWRfHKo6JCVxLnvmFGBjFQ58oF1//sUZhGy/ClSTWObgnL4O+bkeN4nY2okfNMbkRt9/vtxz8InoTsWplJSCzFxPmO8      +GpSIByX3YQAuGDWtRKhKjCnxDXhF6Z4yxnZ20Wgko7BMRDmxtSGVaI4kdTIgb      +zTYoJQlIMlDlmUFgrcDWWC201qSayqlTkiCddWWeV62VU0YlnpRi9VOKaSUsiyq/N0krwq2Ugt7lVpZl5BfHNiytjagMi    +XYp0kCR45hMlivVQrE/uU5pXSrCB5bM6d1t2lOZItMqmliT3q5uVxqxzyW/       ccfYLNKx7ZTeykMvNyac2yt2Fbc61MHLSC0rwoxbiNdlQ3GBm1NLHQsHUrtEXppR/       ljNpW6DbSCoqlFiVoN6YdaFlgsSFVPs1BdT8OaB5QyQzVcaqWDows/zepxR8ObLglTrdtCRVuRNj4Rrxh+//0ke2f8KVL     +Kon3GCSbmsJN9OUW3j6g0Ns+LgCij2u0h+Sghc8mlMPBMgdx5DFh59VmOVHrvmDnoNxCz3J7MFWsMuaLyR089xz/       xhlfijvwutR8gv3zk6BLUUeCgAAAABJRU5ErkJggg==",
              symbol:'image://' + img,
              symbolSize: [40, 40],
              symbolOffset: [25, 0],
              z: 12,
              itemStyle: {
                normal: {
                  color: "#14b1eb",
                },
              },
              data: getSymbolData(data),
            },
            {
              name: "条",
              type: "bar",
              showBackground: true,
              barBorderRadius: 30,
              yAxisIndex: 0,
              data: data,
              barWidth: 10,
              // align: left,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    1,
                    0,
                    [
                      {
                        offset: 0,
                        color: "rgba(0,44,44,0.3)",
                      },
                      {
                        offset: 1,
                        color: lineColor,
                      },
                    ],
                    false
                  ),
                  barBorderRadius: 10,
                },
                // color: '#A71A2B',
                barBorderRadius: 4,
              },
              label: {
                normal: {
                  color: "#fff",
                  show: true,
                  position: [0, '-40px'],
                  textStyle: {
                    fontSize: 30,
                  },
                  formatter: function (a, b) {
                    return a.name;
                  },
                },
              },
            },
          ],
        };


        echarts0.setOption(option)
        echarts0.getZr().on('mousemove', param => {
          echarts0.getZr().setCursorStyle('default')
        })

      },
      //计算数组所占百分比
      countPercentage(countArray){
          var j = eval(countArray.join('+'));
          var resultArray = [];
          for (var i = 0 ; i < countArray.length ; i++){
              var k = Math.floor((countArray[i]/j)*100) + "%";
              resultArray.push(k);
          }
          return resultArray;
      },

      getEcharts01(id, col, num) {
                let myEc = echarts.init(document.getElementById(id));
                let option = {
                  title: [
                          {
                            text: "综合贡献度",
                            x: "10%",
                            y: "85%",
                            textStyle: {
                              fontSize:38,
                              fontWeight: "500",
                              color: "#fff",
                            },
                          },
                      ],
                    series: [
                        {
                            type: "liquidFill",
                            radius: "72%",
                            color: col,
                            center: ["48%", "42%"],
                            data: [num, {
                                value: num,
                                phase: Math.PI,
                            }
                            ],
                            label: {
                                normal: {
                                    textStyle: {
                                        fontSize:55,
                                        color: col[1]
                                    },
                                },
                            },
                            itemStyle:{
                              normal:{
                                label:{
                                  show:true,
                                  formatter:(e)=>{
                                    return parseInt(e.data*100)
                                  }
                                }
                              }
                            },
                            backgroundStyle: {
                                  borderWidth: 1,
                                color: "rgba(220, 192, 179, 0.06)",
                            },
                        },
                    ],
                }
                myEc.setOption(option)
                myEc.getZr().on('mousemove', param => {
                  myEc.getZr().setCursorStyle('default')
                })
       },

      getEcharts02(id, tit, num, col, col2) {
                let myEc = echarts.init(document.getElementById(id));
                var chartData = num;
                var gradient = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    {
                        offset: 0,
                        color: col,
                    }
                ]);
                let option = {
                    legend: {
                        show: true,
                    },
                    title: [
                        {
                            text: chartData + "%",
                            x: "39%",
                            y: "28%",
                            textAlign: "center",
                            textStyle: {
                                fontSize: "45",
                                fontWeight: "500",
                                color: col,
                                textAlign: "center",
                            },
                        },
                        {
                            text: tit,
                            left: "16%",
                            top: "30%",
                            textAlign: "center",
                            textStyle: {
                                fontSize: "30",
                                fontWeight: "400",
                                color: "#fff",
                                textAlign: "center",
                            },
                        },
                    ],
                    series: [
                        {
                            type: "pie",
                            clockwise: false,
                            radius: ["65%", "72%"],
                            center: ["40%", "40%"],
                            zlevel: 3,
                            hoverAnimation: false,
                            label: {
                                show: false,
                            },
                            data: [
                                {
                                    value: 100 - chartData,
                                    itemStyle: {
                                        color: "transparent",
                                    },
                                },
                                {
                                    value: chartData,
                                    itemStyle: {
                                        normal: {
                                            borderWidth: 7,
                                            borderColor: gradient,
                                            color: gradient,
                                        },
                                    },
                                },
                            ],
                        },
                        {
                            type: "pie",
                            clockwise: false,
                            radius:["65%", "70%"],
                            center: ["40%", "40%"],
                            hoverAnimation: false,
                            zlevel: 2,
                            label: {
                                show: false,
                            },
                            itemStyle: {
                                color: "#DBE9F6",
                                borderWidth: 7,
                                borderColor: col2,
                            },
                            data: [100],
                        },
                    ],
                };
                myEc.setOption(option)
                myEc.getZr().on('mousemove', param => {
                  myEc.getZr().setCursorStyle('default')
                })
        },

      getEcharts10(id){
        let echarts10 = echarts.init(document.getElementById(id));
        option = {
          tooltip: {
            trigger: "item",
            formatter: "{b} :<br/> {c} (占比{d}%)",
            textStyle:{
              fontSize:16,

            },
          },
          legend: {
            orient: 'vertical',
            right: 50,
            top:50,
            icon: "circle",
            textStyle:{
              color:"#D6E7F9",
              fontSize:30,
            },
            formatter: function(name) {
                    var data = option.series[0].data;//获取series中的data
                    var total = 0;
                    var tarValue;
                    for (var i = 0, l = data.length; i < l; i++) {
                        total += data[i].value;
                        if (data[i].name == name) {
                            tarValue = data[i].value;
                        }
                    }
                    var p = ((tarValue / total) * 100);
                    return name + " " + " " + p.toFixed(2) + "%";
                },
          },
          series: [
            {
              name: 'Nightingale Chart',
              type: 'pie',
              radius: [50, 120],
              center: ['20%', '50%'],
              roseType: 'area',
              itemStyle: {
                borderRadius: 5
              },
              label:{
                show:false,
              },
              data: [
                { value: 12, name: '高分辨率影像'},
                { value: 6, name: '多光谱影像'},
                { value: 24, name: '雷达影像'},
                { value: 36, name: '遥感专题图'},
              ]
            }
          ]
        };
        echarts10.setOption(option)
        echarts10.getZr().on('mousemove', param => {
          echarts10.getZr().setCursorStyle('default')
        })
      }
    },
  })
  </script>
  <style>
  .close{
    position: absolute;
    right: 30px;
    color: white;
    font-size: 50px;
    top: 30px;
    cursor: pointer;
  }
  .el-carousel__indicators{
    width: 100%;
    height: 100px;
    left: 205px;
    top: 987px;
    text-align: center;
    display: flex;
    transform: perspective(9em) rotateY(6deg);
  }
  .carousel002  .el-carousel__indicators{
    width: 100%;
    height: 100px;
    left: 300px;
    top: 985px;
    text-align: center;
    display: flex;
    transform: perspective(9em) rotateY(1deg);
  }

  .el-carousel__indicator {
    margin: 10px 10px 0 10px;
  }

  .el-carousel__indicators {
    /* top: 500px;
                    left: 200px; */
  }

  /* 右侧-new */
  .abso-item {
    padding: 40px 80px;
    box-sizing: border-box;
  }

  .item-lists {
    width: 100%;
    height: 210px;
    display: flex;
    justify-content: space-evenly;
  }

  .item-lists-2 {
    width: 100%;
    height: 390px;
    font-size: 28px;
    color: #D6E7F9;
    padding: 0 20px;
    box-sizing: border-box;
  }


  .item-text {
    position: absolute;
    left: 62px;
    top: 14px;
    font-size: 40px;
    background: linear-gradient(180deg, #FFFFFF 0%, #00C0FF 50.244140625%, #FFFFFF 53.0029296875%, #CBF2FF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .item-text-active {
    background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .item-icon {
    position: absolute;
    left: 53px;
    top: 22px;
    width: 43px;
    opacity: 0.1;
  }

  .lists-2-top {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
    width: 60%;
    margin: 20px auto 10px;
    background: url("/static/images/home_services/dnyx/gfyg-1.png");
    /* border-top: 1px solid #56A7FB;border-top: 1px solid ; */
    /* border-image: linear-gradient(60deg, rgba(255,255,255,0.00) 0%,#56A7FB 50%, rgba(255,255,255,0.00) 99%) 2 2 2 2; */
    border-bottom: 1px solid #56A7FB;
    border-bottom: 1px solid;
    border-image: linear-gradient(30deg, rgba(255, 255, 255, 0.00) 0%, #56A7FB 50%, rgba(255, 255, 255, 0.00) 99%) 2 2 2 2;
    /* background: linear-gradient(to right, rgba(22, 208, 255, 0.1) 0%, rgba(22, 208, 255, 0.05) 25%, rgba(22, 208, 255, 0.5) 50%, rgba(22, 208, 255, 0.1) 75%); */
  }

  .lists-2-title {
    font-size: 38px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #D6E7F9;
    height: 60px;
    line-height: 60px;
  }

  .lists-2-title img {
    width: 25px;
    height: 25px;
    margin-right: 10px
  }

  .lists-2-con {
    display: flex;
  }

  .lists-2-con img {
    width: 116px;
    height: 90px;
    margin: 40px 40px 0 40px;
  }

  .lists-2-con span {
    margin-top: 20px;
  }

  .wlgz-title {
    width: 531px;
    height: 74px;
    margin: 10px auto;
    line-height: 74px;
    text-align: center;
    background-image: url(/static/images/home_services/dnyx/title-1.png);
    position: relative;
    padding-left: 40px;
    box-sizing: border-box;
  }

  .wlgz-title1 {
    background-image: url(/static/images/home_services/dnyx/title-1-select.png);
  }

  .wlgz-title img {
    position: absolute;
    top: 20px;
    left: 20px;
  }

  .wlgz-content-part {
    width: 431px;
    height: 74px;
    margin: 30px auto 20px;
    line-height: 74px;
    text-align: center;
    background: url(/static/images/home_services/dnyx/part-1.png) no-repeat;
    position: relative;
    padding-left: 40px;
    box-sizing: border-box;
  }

  .wlgz-content-part1 {
    background: url(/static/images/home_services/dnyx/part-1-select.png) no-repeat;
  }

  .wlgz-content-part img {
    position: absolute;
    top: 20px;
    left: 20px;
  }

  .gis-list {
    display: flex;
    width: 100%;
    justify-content: space-evenly;
  }

  .gis-item {
    display: flex;
  }

  .gis-item img {
    width: 64px;
    height: 62px;
    margin: 20px 5px 0 0px;
  }

  .gis-item span {
    margin-top: 10px;
  }

  .gis-item-bottom {
    width: 286px;
    height: 110px;
    text-align: center;
    background: url(/static/images/home_services/dnyx/gis-item-bottom.png) 0 68px;
    background-repeat: no-repeat;
  }

  .gis-item-bottom img {
    width: 21px;
    height: 3px;
  }

  .gis-item-bottom span {
    margin: 0 auto;
  }

  .dwfb-item {
    padding: 10px 20px;
    box-sizing: border-box;
  }

  .dwfb-item-title {
    font-size: 28px;
    font-weight: 400;
    color: #4EBBEC;
    /* text-shadow: 0px 2px 27px rgba(0, 0, 0, 0.67); */
  }

  .dwfb-item .progress {
    position: relative;
    top: -10px;
    width: 53%;
    height: 15px;
  }

  .dwfb-item-value {
    font-size: 28px;
    font-weight: 400;
    color: #30B8D5;
    background: linear-gradient(180deg, #AED6FF 0%, #74B8FF 47.4853515625%, #9CCFFF 50%, #DDEEFF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .sbqk-item {
    display: flex;
  }

  .sbqk-item img {
    width: 92px;
    height: 98px;
    margin: 20px 20px 0 40px;
  }

  .sbqk-item span {
    margin-top: 20px;
  }

  .Bmyy5-diong {
    position: relative;
    top: -1000px;
    left: -876px;
    width: 800px;
    height: 530px;
    background: rgba(0, 15, 34, 0.9);
    border: 1px solid #06729F;
    border-radius: 4px;
    z-index: 20;
    /* display: flex; */
    font-family: "思源黑体 CNBOLD";
    font-size: 30px;
  }

  .Bmyy5-title {
    width: 100%;
    height: 80px;
    font-size: 40px;
    padding: 0 20px;
    box-sizing: border-box;
    color: #D6E7F9;
    line-height: 80px;
    background: linear-gradient(0deg, #00AAE2, #073446);

  }

  .Bmyy5-diong progress {
    position: relative;
    top: -10px;
    width: 53%;
    height: 15px;
  }

  .Bmyy5-diong ul {
    padding: 20px 20px;
    box-sizing: border-box;
    font-size: 30px;
    color: #D6E7F9;
  }

  .Bmyy5-diong li {
    padding: 10px 0;
    box-sizing: border-box;

  }

  .number {
    background: url("/static/images/home_services/dnyx/numbg.png");
    background-size: 100% 100%;
    margin-right: 10px;
  }
  .bottom-btn{
    width:100%;
    display: flex;
    position: absolute;
    /*bottom: -182px;*/
    bottom: 25px;
    left: 37.5%;
    z-index: 99;
  }
  .bottom-btn-item{
    width:400px;
    height:61px;
    background:url('/static/citybrain/djtl/img/qsskdw/sprhpt-2.png') no-repeat;
    background-size: 100% 100%;
    font-size: 36px;
    color: #FFFFFF;
    line-height: 52px;
    text-align: center;
    cursor: pointer;
  }
  .bottom-btn-item-active{
    background:url('/static/citybrain/djtl/img/qsskdw/sprhpt-3.png') no-repeat;
    background-size: 100% 100%;
  }
  .low{
    width:470px;
  }
  .low >img{
    width:470px;
  }

  </style>

  <style>
/* 数字滚动 */
.count-flop {
  display: inline-block;
  font-size: 0;
  /* 可更改 */
  height: 50px;
  line-height: 50px;
  font-size: 44px;
  font-weight: 700;
  color: #4898f1;
}

.count-flop>div {
  position: relative;
  display: inline-block;
  overflow: hidden;
  height: 100%;
}

.count-flop-box {
  /* 可更改 */
  margin-right: 5px;
  width: 36px;
  border: 0px solid rgba(72, 152, 241, 0.3);
  line-height: 48px;
  border-radius: 6px;
  background: url("/static/images/home_services/dnyx/numbg.png");
  background-size: 100% 100%;
  font-size: 40px;
  color: yellow;
}

.count-flop-point {
  /* 可更改 */
  margin-right: 5px;
  width: 10px;
}

.count-flop-content {
  font-family: MicrosoftYaHei-Bold;
  text-align: center;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  animation-fill-mode: forwards !important;

  color: #9AA9BF;
  background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.rolling_0 {
  animation: rolling_0 2.1s ease;
}

@keyframes rolling_0 {
  from {
    transform: translateY(-90%);
  }

  to {
    transform: translateY(0);
  }
}

.rolling_1 {
  animation: rolling_1 3s ease;
}

@keyframes rolling_1 {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-10%);
  }
}

.rolling_2 {
  animation: rolling_2 2.1s ease;
}

@keyframes rolling_2 {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-20%);
  }
}

.rolling_3 {
  animation: rolling_3 3s ease;
}

@keyframes rolling_3 {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-30%);
  }
}

.rolling_4 {
  animation: rolling_4 2.1s ease;
}

@keyframes rolling_4 {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-40%);
  }
}

.rolling_5 {
  animation: rolling_5 3s ease;
}

@keyframes rolling_5 {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-50%);
  }
}

.rolling_6 {
  animation: rolling_6 2.1s ease;
}

@keyframes rolling_6 {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-60%);
  }
}

.rolling_7 {
  animation: rolling_7 3.1s ease;
}

@keyframes rolling_7 {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-70%);
  }
}

.rolling_8 {
  animation: rolling_8 2.1s ease;
}

@keyframes rolling_8 {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-80%);
  }
}

.rolling_9 {
  animation: rolling_9 3.6s ease;
}

@keyframes rolling_9 {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-90%);
  }
}

.tc_table {
  position: absolute;
  left: 0;
  top: 400px;
  z-index: 999;
  border: unset;
  background-color: #053856;

}
.tc_table caption{
  font-family: '思源黑体 CNBOLD';
    background-image: linear-gradient(to bottom, #f0ffff, #74b4f4, #83b8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
  font-size: 35px;
  border: 1px solid #2f537e;
  border-bottom: unset;
  height: 80px;
  line-height: 80px;
  background: linear-gradient(to bottom, #ccf4ff, #ffffff, #00baf8, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}
.tc_item {
  padding: 20px;
  background: linear-gradient(to bottom, #073c64, #0a2c54, #0e1c41);

}
.tc_item table{
  /* width: 500px; */
  /* height: 400px; */
  /* background-color: #01bbf9; */
  border-collapse: collapse;
}
.tc_item th{
  font-size: 25px;
  color: #fff;
  font-weight: 400;
  font-family: '思源黑体 CNBOLD';
  /* background-color: #0c2349; */
  padding: 18px ;
  border: 1px solid #2f537e;

}


.tc_item tr th:nth-child(1){
  width: 100px;
}
.tc_item tr th:nth-child(2){
  width: 340px;
}

.sjx {
  position: absolute;
        left: 100px;
        top: -48px;
        /* width: 50px; */
        width: 0px;
        height: 0px;
        border: 25px solid #000;
        border-bottom-color: #073c64;
        border-right-color: transparent;
        border-left-color: transparent;
        border-top-color: transparent;
      }

      .title_icon a{
        display: flex;
      }
</style>

  </html>
