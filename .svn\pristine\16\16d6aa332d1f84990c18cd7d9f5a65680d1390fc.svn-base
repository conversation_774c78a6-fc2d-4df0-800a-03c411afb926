<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>数字社会指标分析左侧面板</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <link
      rel="stylesheet"
      href="/static/citybrain3840/szhgg/css/szshzbfx-left.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts-liquidfill.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
    <!-- 轮播toolTip -->
  </head>

  <body>
    <div id="app" class="container" v-cloak>
      <nav>
        <s-header-title-2
          htype="1"
          title="社会事业领域业务梳理情况"
        ></s-header-title-2>
      </nav>
      <div class="btn">任务总数:{{rwzs}}</div>
      <div class="content">
        <div id="shsy-chart1"></div>
        <div id="shsy-chart2"></div>
      </div>
      <div class="table table1">
        <div class="th">
          <div
            class="th_td"
            style="flex: 0.35"
            v-for="(item,index) in theadList"
            :key="index"
          >
            {{item}}
          </div>
        </div>
        <div class="tbody" id="box1">
          <div class="tr" v-for="(item ,i) in tbodyList" :key="i">
            <div class="tr_td" style="flex: 0.35">{{i+1}}</div>
            <div class="tr_td" style="flex: 0.35">{{item.name}}</div>
            <div class="tr_td" style="flex: 0.35">{{item.value}}</div>
          </div>
        </div>
      </div>
      <div id="shsy-chart3"></div>
      <div id="shsy-chart4"></div>
    </div>
  </body>
  <script type="module">
    new Vue({
      el: '#app',
      data: {
        rwzs: '',
        theadList: ['序号', '事项清单', '总数'],
        tbodyList: [],
      },
      methods: {
        initIframe() {
          let iframe1 = {
            type: 'openIframe',
            name: 'index-middle-top',
            src: '/static/citybrain3840/szhgg/pages/szzfzbfx/index-middle-top.html',
            width: '930px',
            height: '170px',
            left: '1590px',
            top: '230px',
            zIndex: '10',
          }
          let iframe4 = {
            type: 'openIframe',
            name: 'list',
            src: '/static/citybrain3840/szhgg/commont/list2.html',
            width: '450px',
            height: '125px',
            left: '1090px',
            top: '230px',
            zIndex: '10',
          }
          window.parent.postMessage(JSON.stringify(iframe1), '*')
          window.parent.postMessage(JSON.stringify(iframe4), '*')
        },
        initMap() {
          top.mapUtil.loadRegionLayer({
            layerid: 'szjjzbfx_bk',
            data: [
              { name: '婺城区', color: [78, 107, 221, 1], height: 2800 },
              { name: '开发区', color: [78, 107, 221, 1], height: 2600 },
              { name: '金东区', color: [46, 81, 221, 1], height: 2400 },
              { name: '兰溪市', color: [78, 107, 221, 1], height: 2200 },
              { name: '浦江县', color: [110, 133, 221, 1], height: 2000 },
              { name: '义乌市', color: [110, 133, 221, 1], height: 1800 },
              { name: '东阳市', color: [78, 107, 221, 1], height: 1600 },
              { name: '磐安县', color: [110, 133, 221, 1], height: 1400 },
              { name: '永康市', color: [46, 81, 221, 1], height: 1200 },
              { name: '武义县', color: [110, 133, 221, 1], height: 1000 },
            ],
          })
          top.mapUtil.flyTo({
            x: 120.42947964091805,
            y: 26.653373263516,
            z: 326456.6924844431,
            heading: 350.39657276492284,
            tilt: 38.15583652942755,
          })
          $get('/city.json').then((res) => {
            top.mapUtil.loadTextLayer({
              layerid: 'szjj_3Dtext',
              data: res,
              style: {
                size: 32,
                color: [242, 242, 242, 1],
              },
            })
          })
        },
        init() {
          $api('ldst_szhgg_szsh', { type: 1 }).then((res) => {
            this.rwzs = res[0].value
          })
          $api('ldst_szhgg_szsh', { type: 4 }).then((res) => {
            this.tbodyList = res
          })
          $api('ldst_szhgg_szsh', { type: 2 }).then((res) => {
            this.PiechartsShow(res)
          })
          $api('ldst_szhgg_szsh', { type: 3 }).then((res) => {
            this.PiechartsShow1(res)
          })
          $api('ldst_szhgg_szsh', { type: 5 }).then((res) => {
            this.BarchartsShow(res)
          })
          $api('ldst_szhgg_szsh', { type: 6 }).then((res) => {
            this.LinechartsShow(res)
          })
        },
        //绘制饼图
        PiechartsShow(data) {
          const myChartsPerson = echarts.init(
            document.getElementById('shsy-chart1')
          )
          var fontColor = '#30eee9'
          let option = {
            grid: {
              // left: "15%",
              right: '2%',
              // top: "30%",
              // bottom: "15%",
              containLabel: true,
            },
            tooltip: {
              trigger: 'item',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
            },
            color: ['#5087EC', '#F2BD42', '#58A55C', '#F2BD42', '#EE752F'],
            series: [
              {
                name: '业务梳理情况',
                type: 'pie',
                radius: '80%',
                center: ['50%', '50%'],
                data: data,
                itemStyle: {
                  normal: {
                    label: {
                      show: true,
                      color: '#fff',
                      fontSize: 25,
                      position: 'inside',
                      formatter: '{b}:\n{d}%',
                    },
                  },
                  labelLine: { show: false },
                },
              },
            ],
          }

          myChartsPerson.setOption(option)
          // tools.loopShowTooltip(myChartsPerson, option, {
          //     loopSeries: true,
          // }); //轮播
        },
        //绘制饼图2
        PiechartsShow1(data) {
          const myChartsPerson = echarts.init(
            document.getElementById('shsy-chart2')
          )
          var fontColor = '#30eee9'
          let option = {
            grid: {
              left: '5%',
              right: '2%',
              // top: "30%",
              // bottom: "15%",
              containLabel: true,
            },
            legend: {
              orient: 'vertical',
              x: 'right',
              y: 'center',
              itemWidth: 14,
              itemHeight: 14,
              align: 'left',
              textStyle: {
                color: '#fff',
                fontSize: 28,
              },
            },
            tooltip: {
              trigger: 'item',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
            },
            color: ['#5087EC', '#F2BD42', '#58A55C', '#F2BD42', '#EE752F'],
            series: [
              {
                name: '业务梳理情况',
                type: 'pie',
                radius: '80%',
                center: ['30%', '50%'],
                data: data,
                itemStyle: {
                  normal: {
                    label: {
                      show: true,
                      color: '#fff',
                      fontSize: 25,
                      position: 'inside',
                      formatter: '{b}:\n{d}%',
                    },
                  },
                  labelLine: { show: false },
                },
              },
            ],
          }

          myChartsPerson.setOption(option)
          // tools.loopShowTooltip(myChartsPerson, option, {
          //     loopSeries: true,
          // }); //轮播
        },
        //绘制柱图
        BarchartsShow(data) {
          const myChartsDivine = echarts.init(
            document.getElementById('shsy-chart3')
          )
          let x = data.map((item) => {
            return item.name
          })
          let y = data.map((item) => {
            return item.value
          })
          let option = {
            tooltip: {
              trigger: 'item',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
            },

            grid: {
              left: '8%',
              top: '18%',
              right: '5%',
              bottom: '20%',
            },
            legend: {
              top: '3%',
              textStyle: {
                color: '#fff',
                fontSize: 28,
              },
            },
            xAxis: {
              data: x,
              axisLine: {
                show: true, //隐藏X轴轴线
                lineStyle: {
                  color: '#aaa',
                  width: 1,
                },
              },
              axisTick: {
                show: true, //隐藏X轴刻度
                alignWithLabel: true,
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#fff', //X轴文字颜色
                  fontSize: 28,
                },
                interval: 0,
                rotate: 30,
              },
            },
            yAxis: [
              {
                type: 'value',
                name: '单位:起',
                nameTextStyle: {
                  color: '#fff',
                  fontSize: 28,
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    width: 1,
                    color: '#3d5269',
                  },
                },
                axisTick: {
                  show: false,
                },
                axisLine: {
                  show: false,
                },
                axisLabel: {
                  show: true,
                  textStyle: {
                    color: '#fff',
                    fontSize: 28,
                  },
                },
              },
            ],
            series: [
              {
                name: '经济总量',
                type: 'bar',
                barWidth: 70,
                color: '#5087EC',
                label: {
                  show: true,
                  position: 'top',
                  textStyle: {
                    color: '#fff',
                    fontSize: 28,
                  },
                },
                itemStyle: {
                  normal: {
                    color: '#5087EC',
                  },
                },
                data: y,
              },
            ],
          }

          myChartsDivine.setOption(option)
          tools.loopShowTooltip(myChartsDivine, option, {
            loopSeries: true,
          }) //轮播
        },
        //绘制折线图
        LinechartsShow(data) {
          const myChartsDivine = echarts.init(
            document.getElementById('shsy-chart4')
          )
          let x = data.map((item) => {
            return item.name
          })
          let y = data.map((item) => {
            return item.value
          })
          let option = {
            tooltip: {
              trigger: 'item',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
            },

            grid: {
              left: '8%',
              top: '18%',
              right: '8%',
              bottom: '20%',
            },
            legend: {
              data: ['业务完成率'],
              top: '3%',
              textStyle: {
                color: '#fff',
                fontSize: 28,
              },
            },
            xAxis: {
              data: x,
              axisLine: {
                show: true, //隐藏X轴轴线
                lineStyle: {
                  color: '#aaa',
                  width: 1,
                },
              },
              axisTick: {
                show: true, //隐藏X轴刻度
                alignWithLabel: true,
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#fff', //X轴文字颜色
                  fontSize: 28,
                },
                interval: 0,
                rotate: 30,
              },
            },
            yAxis: [
              {
                type: 'value',
                name: '单位:起',
                nameTextStyle: {
                  color: '#fff',
                  fontSize: 28,
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    width: 1,
                    color: '#3d5269',
                  },
                },
                axisTick: {
                  show: false,
                },
                axisLine: {
                  show: false,
                },
                axisLabel: {
                  show: true,
                  textStyle: {
                    color: '#fff',
                    fontSize: 28,
                  },
                },
              },
            ],
            series: [
              {
                name: '业务完成率',
                type: 'bar',
                stack: '总量',
                // symbol: "circle",
                symbolSize: 10,
                label: {
                  show: true,
                  position: 'top',
                  textStyle: {
                    color: '#fff',
                    fontSize: 28,
                  },
                },
                itemStyle: {
                  normal: {
                    color: '#5087EC',
                  },
                },

                data: y,
              },
            ],
          }

          myChartsDivine.setOption(option)
          tools.loopShowTooltip(myChartsDivine, option, {
            loopSeries: true,
          }) //轮播
        },
      },

      //项目生命周期
      mounted() {
        this.init()
        this.initIframe()
        this.initMap()
      },
    })
  </script>
</html>
