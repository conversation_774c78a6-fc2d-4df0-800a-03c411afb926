<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>事件详情</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <link rel="stylesheet" href="/static/citybrain/ggfw/css/zwfw-event-loop-dialog.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <!-- 轮播toolTip -->
</head>

<body>
    <div id="app" class="container">
        <div class="head">
            <span>事件详情</span>
            <div class="img" @click="closeDialog"></div>
        </div>
        <div class="content">
            <div class="sjxq-top" style="width: 100%">
                <div class="jbxx">
                    <div class="jbxx-title">基本信息</div>
                    <div class="jbxx-item">
                        <div class="jbxx-icon"></div>
                        <div class="jbxx-mrck">事件名称:</div>
                        <el-input v-model="value1" class="search" placeholder="杂物清理">
                        </el-input>
                    </div>
                    <div class="jbxx-item">
                        <div class="jbxx-icon"></div>
                        <div class="jbxx-mrck">事件类别:</div>
                        <el-select v-model="value2">
                            <el-option key="1" label="社区环境卫生" value="1">
                            </el-option>
                        </el-select>
                    </div>
                    <div class="jbxx-item">
                        <div class="jbxx-icon"></div>
                        <div class="jbxx-mrck">重要程度:</div>
                        <el-select v-model="value3">
                            <el-option key="1" label="一般" value="1">
                            </el-option>
                            <el-option key="2" label="重要" value="2">
                            </el-option>
                        </el-select>
                    </div>
                    <div class="jbxx-item">
                        <div class="jbxx-icon"></div>
                        <div class="jbxx-rksj jbxx-mrck">信息入库时间:</div>
                        <el-input v-model="value4" class="search" placeholder="2022-02-25 13：29：01">
                        </el-input>
                    </div>
                    <div class="jbxx-item">
                        <div class="jbxx-icon"></div>
                        <div class="jbxx-hfsj jbxx-mrck">回访时间:</div>
                        <el-input v-model="value5" class="search" placeholder="2022-03-04 13：39：24">
                        </el-input>
                    </div>
                    <div class="jbxx-item">
                        <div class="jbxx-icon"></div>
                        <div class="jbxx-nrms jbxx-mrck">事件内容描述:</div>
                        <el-input :rows="3" type="textarea" v-model="value6" class="search">
                        </el-input>
                    </div>
                </div>
                <div class="lzqk">
                    <div class="lzqk-title">流转情况</div>
                    <div class="lzqk-fq"> 
                        <div class="lzqk-time">
                            2022-02-25 13:29:00
                        </div>
                        <div class="lzqk-desc">
                          网络员巡查发现该院落杂物已经联系网络中心协助清理
                        </div>
                        <img class="desc-img" style="width:150px" src="../img/lzqk-desc.png" alt="">
                    </div>
                    <div class="lzqk-arrow">
                        <img style="width:100%" src="../img/lzqk-arrow.png" alt="">
                    </div>
                    <div class="lzqk-lc"> </div>
                </div>
            </div>
            <div class="sjxq-btn">
                <el-button  style="width:150px"  type="primary" @click="closeDialog">保存</el-button>
                <div style="width:30px;"></div>
                <el-button  style="width:150px" type="primary" plain @click="closeDialog">关闭</el-button>
            </div>
        </div>
    </div>
</body>
<script type="module">
    new Vue({
        el: "#app",
        data: {
            value1: "",
            value2: "1",
            value3: "1",
            value4: "",
            value5: "",
            value6: "网络员巡查发现该院落杂物已经联系网络中心协助清理",
        },
        //项目生命周期
        mounted() {

        },
        methods: {
            closeDialog() {
                let data = JSON.stringify({
                    type: "closeIframe",
                    name: "zwfw-event-loop-dialog",
                });
                window.parent.postMessage(data, "*");
            },

        },
    });
</script>

</html>