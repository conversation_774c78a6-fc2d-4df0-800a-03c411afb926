<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>事件中心-右</title>
     <link rel="stylesheet" href="/static/css/animate_dn.css">
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/citybrain/hjbh/js/vue.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts.js"></script>
    <script src="/static/js/jslib/vue-count-to.min.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <style>
         *{
            margin: 0;
            padding: 0;
        }
        #leftApp{
            width: 2045px;
            height: 1890px;
            background:url("/img/right-bg.png") no-repeat;
            background-size: 100%;
        }

        .right_2 {
        margin-top: 40px;
        max-height:400px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        overflow: hidden;
        overflow-y: scroll;
        }
        .right_2::-webkit-scrollbar {
        width: 10px;
      }

      .right_2::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: #20aeff;
        height: 8px;
      }

        .right_2_0 {
        width: 80%;
        margin-bottom: 15px;
        }

        .right_2_0_0 {
        display: flex;
        }

        .right_2_0_0_0 {
        display: flex;
        font-family: SourceHanSansCN-Medium;
        font-size: 32px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0;
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        flex: 1;
        }

        .right_2_0_0_0>div {
        color: #d6e7f9;
        font-size: 32px;
        margin-left: 20px;
        }

        .right_2_0_0_1 {
        font-family: BebasNeue;
        font-size: 30px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 48px;
        letter-spacing: 1px;
        color: #c0cedd;
        }

        .right_2_0_1 {
        width: 100%;
        height: 6px;
        background-color: #2e3f53;
        }

        .right_2_0_1>div {
        max-width: 850px;
        height: 8px;
        background-image: linear-gradient(360deg, #df3c30, #ff9b78);
        margin-top: -1px;
        }

        .right_2_0_0_2 {
        font-size: 24px !important;
        font-weight: 700;
        color: #00aaf8 !important;
        }
        .ul01{
            list-style: none;
            width: 100%;
            display: flex;
            justify-content:space-around;
            color: #999;
            font-size: 36px;
            border-bottom: 3px solid #17436A;
            padding-bottom: 15px;
            margin-top: 30px;
        }
        .avtive{
            padding:0 20px;
            margin-bottom: -17px;
            color: #fff;
            border-bottom: 1px solid #fff;
        }

         .part-table{
        width:100%;
        margin: 10px 0 10px 20px;
        box-sizing: border-box;
      }
      .part-table-thead{
        width: 928px;
        height:80px;
        background: #00396F;
        font-size:38px;
        color: #77B3F1;
        line-height: 80px;
        display: flex;
        text-align: center;
        /* justify-content: space-around; */
      }
      .part-table-tbody{
        width:928px;
        height:350px;
        overflow-y: auto;
        overflow-x: hidden;
        text-align: center;
      }
      .part-table-tbody::-webkit-scrollbar-thumb {
        border-radius: 6px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
        background: #20aeff;
        height: 8px;

      }

      .part-table-tbody::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 6px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
        /* scrollbar-arrow-color: red; */
      
      }
      .part-table-td{
        width: 928px;
        height: 113px;
        background: #0F2B4D;
        font-size: 30px;
        color: #ccc;
        line-height: 47px;
        display: flex;
        justify-content: space-evenly;
        margin-top:5px;
        padding: 10px 0;
        box-sizing: border-box;
      }
      .part-table-td :nth-child(4){
        text-align: center;
      }

       .content0_2_0 {
        width: 100%;
        height: 80px;
        background-color: #0e3a65;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-right: 4px;
      }

      .content0_2_0_0 {
        /* flex: 0.169; */
        font-family: SourceHanSansCN-Regular;
        font-size: 36px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #77b3f1;
        text-align: center;
      }

      .content0_2_1 {
        height: 400px;
        overflow-y: auto;
      }
      .content0_2_1_0:hover {
        background-color: #035b86;
      }

      .content0_2_1::-webkit-scrollbar {
        /*滚动条整体样式*/
        width: 4px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 4px;
        /* scrollbar-arrow-color: red; */
      }

      .content0_2_1::-webkit-scrollbar-thumb {
        border-radius: 10px;
        /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */

        background: #20aeff;
        height: 8px;
      }

      .content0_2_1_0 {
        height: 70px;
        display: flex;
        margin-top: 10px;
        background-color: #0f2b4d;
        justify-content: center;
        align-items: center;
      }

      .content0_2_1_0_0 {
        /* flex: 0.169; */
        font-family: SourceHanSansCN-Medium;
        font-size: 30px;
        font-weight: normal;
        font-stretch: normal;
        letter-spacing: 0px;
        color: #8caac9;
        text-align: center;
      }
       /* 表格自动滚动 */
        @keyframes rowUp {
        0% {
          -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
        }

        100% {
          transform: translate3d(0, -100%, 0);
          -webkit-transform: translate3d(0, -100%, 0);
          -moz-transform: translate3d(0, -100%, 0);
          -ms-transform: translate3d(0, -100%, 0);
          -o-transform: translate3d(0, -100%, 0);
        }
      }
    .right_2_0 {
        animation: 10s rowUp linear infinite normal;
        -webkit-animation: 10s rowUp linear infinite normal;
      } 
      /*.content0_2_1_0 {
        animation: 10s rowUp linear infinite normal;
        -webkit-animation: 10s rowUp linear infinite normal;
      } */
    </style>
</head>
<body>

    <div id="leftApp">
        <nav style="padding: 20px 40px;">
            <s-header-title title="处置效能" data-time="2022年7月22日"></s-header-title>
        </nav>

        <div class="s-flex" style="height:500px;">
            <div style="width:50%;height: 100%;">
                <s-header-title2 title="今日超时未处理事件"></s-header-title2>
                <!-- 表格 -->
                 <div class="part-table">
                    <div class="part-table-thead">
                        <div style="flex:1;">责任部门</div>
                        <div style="flex:1;">名称</div>
                        <div style="flex:1;">原因</div>
                        <div style="flex:1;">时间</div>
                    </div>
                    <div class="part-table-tbody">
                    <div class="part-table-td" v-for="item in 4">
                        <div style="flex:1;">消防救援支队</div>
                        <div style="flex:1;">设备报警:<br> 烟雾报警</div>
                        <div style="flex:1;">事件超时15分钟<br>未处理</div>
                        <div style="flex:1;">2022-6-10 <br> 23:00:00</div>
                    </div>
                    </div>
                    
                </div>
            </div>
            <div style="width: 50%;height: 100%;">
                <s-header-title2 title="近一周超时未处理事件"></s-header-title2>
                <div class="right_2">
                        <div class="right_2_0" v-for="(item,i) in top10Data" :key="i">
                          <div class="right_2_0_0">
                            <div class="right_2_0_0_0" :style="{backgroundImage: item.b}">
                              {{item.top}}
                              <div>{{item.a}}</div>
                            </div>
                            <div class="right_2_0_0_1">{{item.c}}件</div>
                          </div>
                          <div class="right_2_0_1">
                            <div
                              :style="{ backgroundImage: item.d, width: item.c < 54000 ? item.c/100 + 'px' : '100%' }"
                            ></div>
                          </div>
                        </div>
                </div>
            </div>
        </div>

        <nav style="margin:50px">
            <s-header-title title="一件事集成" data-time="2022年7月22日"></s-header-title>
        </nav>
        <div class="s-flex" style="height:350px;">
            <div id="echarts01" style="flex: 1;height:100%;width: 100%;"></div>
            <div id="echarts02" style="flex: 1;height:100%;width: 100%;"></div>
            <div id="echarts03" style="flex: 1;height:100%;width: 100%;"></div>
        </div>

        <ul class="ul01">
            <li v-for="(item,i) in tabList" @click="tabClick(i)" :class="[tabIndex==i?'avtive':'']">{{item.name}}</li>
        </ul>
        <div style="width: 1983px;margin-left: 20px;">
                <div class="content0_2" style="margin: 46px 10px">
                    <div class="content0_2_0">
                      <div class="content0_2_0_0" style="flex: 0.15">排名</div>
                      <div class="content0_2_0_0" style="flex: 0.5">事件名称</div>
                      <div class="content0_2_0_0" style="flex: 0.3">数量</div>
                      <div class="content0_2_0_0" style="flex: 0.3">涉及部门</div>
                    </div>
                    <div class="content0_2_1">
                      <div
                        class="content0_2_1_0"
                        v-for="(item ,i) in tableList"
                      >
                        <div class="content0_2_1_0_0" style="flex: 0.15;">
                            <span v-if="i==0"><img src="/static/citybrain/csdn/img/ywt/icontop01.png" alt=""></span>
                            <span v-else-if="i==1"><img src="/static/citybrain/csdn/img/ywt/icontop02.png" alt=""></span>
                            <span v-else-if="i==2"><img src="/static/citybrain/csdn/img/ywt/icontop03.png" alt=""></span>
                            <span v-else class="s-c-blue-gradient s-font-38  s-w7">{{item.top}}</span>
                        </div>
                        <div class="content0_2_1_0_0" style="flex: 0.5">
                          {{item.a}}
                        </div>
                        <div class="content0_2_1_0_0" style="flex: 0.3">
                          {{item.b}}
                        </div>
                        <div class="content0_2_1_0_0" style="flex: 0.3">
                          {{item.c}}
                        </div>
                        </div>
                      </div>
                    </div>
                  </div>
        </div>
    </div>
     


    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script>
         new Vue({
             el: "#leftApp",
             data:{
                tabIndex:1,
                tabList:[
                    {name:"单一事件数TOP5(近一周)"},
                    {name:"行业循环事件数TOP5(近一周)"},
                    {name:"多跨联动事件数TOP5(近一周)"},
                ],
                  tableList:[
                    {top:1,a:"婺城区***路**写字楼烟雾报警器预警",b:"25件",c:"***部门"},
                    {top:2,a:"婺城区***路**写字楼烟雾报警器预警",b:"25件",c:"***部门"},
                    {top:3,a:"婺城区***路**写字楼烟雾报警器预警",b:"25件",c:"***部门"},
                    {top:4,a:"婺城区***路**写字楼烟雾报警器预警",b:"25件",c:"***部门"},
                    {top:5,a:"婺城区***路**写字楼烟雾报警器预警",b:"25件",c:"***部门"},
                ],
                top10Data:[
                {
                top: '周一',
                a: '7月18日',
                b: 'linear-gradient(360deg, #df8f30, #faff78)',
                c: '60851',
                d: 'linear-gradient(360deg, #df3c30, #ff9b78)',
                },
                {
                top: '周二',
                a: '7月19日',
                b: 'linear-gradient(360deg, #df8f30, #faff78)',
                c: '52238',
                d: 'linear-gradient(360deg, #df8f30, #faff78)',
                },
                {
                top: '周三',
                a: '7月20日',
                b: 'linear-gradient(360deg, #df8f30, #faff78)',
                c: '36048',
                d: 'linear-gradient(360deg, #304ddf, #7882ff)',
                },
                {
                top: '周四',
                a: '7月21日',
                b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                c: '28256',
                d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                },
                {
                top: '周五',
                a: '7月22日',
                b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                c: '25688',
                d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                },
                {
                top: '周六',
                a: '7月23日',
                b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                c: '25688',
                d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                },
                {
                top: '周日',
                a: '7月24日',
                b: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                c: '25688',
                d: 'linear-gradient(360deg, #30a9df, #78d5ff)',
                },
                ]
           
             },
             mounted(){
               $api('/csdnsjrw_right21').then(res=>{
                 res.map((ele,i)=>{
                    this.getEcharts('echarts0'+(i+1),ele.value,ele.ymbq)
                 })
               })
             },
             methods:{
                tabClick(index){
                   this.tabIndex=index 
                },
                getEcharts(dom, num, tel1, tel) {
                let myChart = echarts.init(document.getElementById(dom))
                let col =
                    num < 20
                    ? '#760443'
                    : num < 60
                    ? new echarts.graphic.LinearGradient(1, 0, 0, 1, [
                        {
                            offset: 1,
                            color: '#760443',
                        },
                        {
                            offset: 0,
                            color: '#12DE83',
                        },
                        ])
                    : new echarts.graphic.LinearGradient(1, 0, 0, 1, [
                        {
                            offset: 1,
                            color: '#760443',
                        },
                        {
                            offset: 0.5,
                            color: '#FAD276',
                        },
                        {
                            offset: 0,
                            color: '#12DE83',
                        },
                        ])

                var datas = {
                    value: num,
                    title: tel1,
                }
                let startAngle = 180,
                    endAngle = 0
                var fontColor = '#00f6f7'
                var seriesName = ''
                let noramlSize = 16
                let state = ''
                let center = ['50%', '70%']
                let wqradius = 0,
                    nqradius = 0,
                    kdradius
                wqradius = '140%'
                nqradius = '118%'
                kdradius = '140%'
                let min = 0,
                    max = 100
                let nqColor = [[datas.value / 100, col]]

                let wqColor = 'rgba(22, 138, 255, 0.9)'
                let circleLineW = 2
                myChart.setOption({
                    // title: {
                    //   //标题
                    //   show: true,
                    //   x: 'center',
                    //   top: '5%',
                    //   text: tel,
                    //   textStyle: {
                    //     fontWeight: '500',
                    //     fontSize: 24,
                    //     color: '#fff',
                    //   },
                    // },

                    series: [
                    {
                        type: 'gauge',
                        radius: '142%',
                        startAngle,
                        endAngle,
                        center,
                        pointer: {
                        show: false,
                        },
                        // data: dataArr,
                        title: {
                        show: false,
                        },
                        axisLine: {
                        show: false,
                        lineStyle: {
                            //   color: "rgb(4, 145, 139)",
                            color: '#fff',
                            width: 2,
                            shadowOffsetX: 0,
                            shadowOffsetY: 0,
                            opacity: 1,
                        },
                        },
                        axisTick: {
                        show: true,
                        splitNumber: 4,
                        length: 8,
                        lineStyle: {
                            width: 1,
                            color: '#20c998',
                        },
                        },
                        splitLine: {
                        length: 15, //刻度节点线长度
                        lineStyle: {
                            width: 2,
                            color: '#20c998',
                        }, //刻度节点线
                        },
                        axisLabel: {
                        show: false,
                        },
                        detail: {
                        show: 0,
                        },
                        animationDuration: 4000,
                    },
                    {
                        name: '白色圈刻度',
                        type: 'gauge',
                        radius: kdradius,
                        center,
                        startAngle, //刻度起始
                        endAngle, //刻度结束
                        z: 7,
                        splitNumber: 10,
                        axisTick: {
                        show: false,
                        },
                        splitLine: {
                        show: false,
                        },
                        axisLabel: {
                        show: false,
                        color: fontColor,
                        fontSize: noramlSize,
                        formatter: '{value}%',
                        }, //刻度节点文字颜色
                        pointer: {
                        show: false,
                        },
                        axisLine: {
                        show: false,
                        },
                        detail: {
                        show: false,
                        },
                    },
                    {
                        name: '外层圈', //刻度背景
                        type: 'gauge',
                        z: 2,
                        radius: wqradius,
                        startAngle,
                        endAngle,
                        center, //整体的位置设置
                        axisLine: {
                        // 坐标轴线
                        lineStyle: {
                            // 属性lineStyle控制线条样式
                            color: [[1, wqColor]],
                            width: circleLineW,
                            opacity: 1, //刻度背景宽度
                        },
                        },
                        splitLine: {
                        show: false,
                        },
                        axisLabel: {
                        show: false,
                        },
                        pointer: {
                        show: false,
                        },
                        axisTick: {
                        show: false,
                        },
                        detail: {
                        show: 0,
                        },
                    },
                    {
                        name: '指针',
                        type: 'gauge',
                        z: 9,
                        radius: '140%',
                        startAngle,
                        endAngle,
                        center, //整体的位置设置
                        axisLine: {
                        show: false,
                        },
                        axisTick: {
                        show: false,
                        },
                        splitLine: {
                        show: false,
                        },
                        axisLabel: {
                        show: false,
                        },
                        min,
                        max,
                        //指针样式位置
                        pointer: {
                        show: true,
                        width: 4,
                        length: '50%',
                        offsetCenter: [0, -5],
                        },
                        itemStyle: {
                        normal: {
                            color: wqColor,
                        },
                        },
                        detail: {
                        show: true,
                        offsetCenter: [0, -50],
                        formatter: [
                            '{value|' +
                            datas.value +
                            '%}\n' +
                            '{tel|' +
                            datas.title +
                            '}',
                        ].join('\n'),
                        rich: {
                            value: {
                            fontSize:45,
                            lineHeight:145,
                            color: fontColor,
                            fontWeight: '700',
                            },
                            tel: {
                            fontSize: 30,
                            lineHeight: 20,
                            color: '#fff',
                            fontWeight: '500',
                            },
                        },
                        },
                        data: [datas.value], //指针位置
                    },
                    {
                        name: '内层盘',
                        type: 'gauge',
                        z: 6,
                        radius: nqradius,
                        startAngle,
                        endAngle,
                        center, //整体的位置设置
                        axisLine: {
                        lineStyle: {
                            // 属性lineStyle控制线条样式//控制外圈位置
                            color: nqColor,
                            width: 15,
                            opacity: 0.9, //控制外圈位置，颜色，宽度，透明度
                        },
                        },
                        axisTick: {
                        show: false,
                        },
                        splitLine: {
                        show: false,
                        },
                        axisLabel: {
                        show: false,
                        },
                        pointer: {
                        show: false,
                        },
                        detail: {
                        show: 0,
                        },
                    },
                    {
                        name: '内层小环',
                        type: 'gauge',
                        z: 6,
                        radius: '105%',
                        startAngle,
                        endAngle,
                        center: center, //整体的位置设置
                        axisLine: {
                        lineStyle: {
                            // 属性lineStyle控制线条样式//控制外圈位置
                            color: [[1, wqColor]],
                            width: circleLineW,
                            // opacity: 0.9 //控制外圈位置，颜色，宽度，透明度
                        },
                        },
                        axisTick: {
                        show: false,
                        },
                        splitLine: {
                        show: false,
                        },
                        axisLabel: {
                        show: false,
                        },
                        pointer: {
                        show: false,
                        },
                        detail: {
                        show: 0,
                        },
                    },
                    ],
                })
                },
      
             }
        })
    </script>
</body>
</html>