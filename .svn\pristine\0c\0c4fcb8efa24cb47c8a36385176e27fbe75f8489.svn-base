html,
body {
  width: 100%;
  /* height: 1080px; */
  /* "Noto Sans SC", */
  font-family:  "STHeiti Light", "Source Han Sans CN", "SimHei",
    "Times New Roman", Georgia, Serif, SimHei;
  user-select: none;
}

* {
  margin: 0;
  padding: 0;
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
}

.lf {
  float: left;
}

/*左浮动*/
.rt {
  float: right;
}

/*右浮动*/
.clear {
  clear: both;
}

/*清除浮动*/
.cursor {
  cursor: pointer;
}

/*鼠标hover*/
.fw-n {
  font-weight: normal;
}

.fw-b {
  font-weight: bold;
}

/*字体加粗*/
.italic {
  font-style: italic;
}

/*倾斜字体*/
.text-center {
  text-align: center;
}

/*字体居中*/
.text-right {
  text-align: right;
}

/*字体居右*/
.text-left {
  text-align: left;
}

/*字体居左*/
/*文字超出显示省略号*/
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/*弹性盒子居中space-around布局*/
.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

/*弹性盒子居中space-between布局*/
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/*弹性盒子居中center布局*/
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.align-center {
  display: flex;
  align-items: center;
}

.fs-14 {
  font-size: 14px;
}

/* 颜色 */
.color-f93232 {
  color: #f93232;
}

.color-f9b600 {
  color: #f9b600;
}

.color-fff {
  color: #fff;
}

.color-0ff {
  color: #0ff;
}

.color-ffd800 {
  color: #ffd800;
}

.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

.clearfix {
  *zoom: 1;
}

.com_app_box {
  position: relative;
  box-sizing: border-box;
  width: 2045px;
  height: 1850px;
  background-image: url("../img/bg.png");
  background-size: 100% 100%;
  padding: 10px 55px 30px;
}
.com_header {
  width: 1935px;
  display: flex;
  height: 130px;
  align-items: center;
  justify-content: space-between;
  background: url("../img/一级标题3.png") no-repeat;
  background-position: 0 55px;
}

.com_header .title {
  font-size: 54px;
  background-image: -webkit-linear-gradient(top, #ffffff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  padding-bottom: 10px;
}

.com_header .title .title_icon_one {
  display: inline-block;
  margin-right: 10px;
  width: 78px;
  height: 75px;
  vertical-align: bottom;
  background-image: url("../img/一级标题1.png");
  background-size: 100% 100%;
}

.com_header .title_hr {
  position: relative;
  left: 30px;
  width: 70%;
  height: 21px;
  background-image: url("../img/一级标题2.png");
  background-size: 100% 100%;
}
