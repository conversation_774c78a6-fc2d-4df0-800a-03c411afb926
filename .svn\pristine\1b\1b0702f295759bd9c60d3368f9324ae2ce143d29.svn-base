<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>项目评审情况汇总</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <script src="/static/js/jslib/echarts-wordcloud.min.js"></script>
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <style>
        ::-webkit-scrollbar {
            width: 0 !important;
        }

        #app {
            width: 1800px;
            height: 1630px;
            background: url("/img/left-bg.png") no-repeat;
            background-size: 100% 100%;
        }

        .tabpar {
            margin-top: 40px;
            width: 90%;
            display: flex;
            margin-left: 5%;

        }

        .tab-c {
            padding: 8px 12px;
            background-color: #00396f;
            color: #fff;
            font-size: 28px;
            cursor: pointer;
            border-right: 1px solid #01a3dd;
            border: 1px solid #01a3dd;
            /* border-right: 0px solid #01a3dd; */
            /* flex: 1; */
        }

        .tab-a {
            background-color: #01a3dd;
        }

        .tab-a-no {
            background-color: #d9001b;
        }

        /* 表格 */
        .table {
            width: 95%;
            /* height: 100%; */
            padding: 10px;
            margin-left: 2.5%;
            box-sizing: border-box;
            overflow-y: auto;
        }

        .table .th {
            width: 100%;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-style: italic;
            font-weight: 700;
            font-size: 20px;
            line-height: 80px;
            background: #00396f;
            color: #ffffff;
        }

        .table .th_td {
            letter-spacing: 0px;
            text-align: center;
            flex: 0.25;
        }

        .table .tbody {
            width: 100%;
            height: 900px;
            overflow: hidden;
        }

        .table .tbody:hover {
            overflow-y: auto;
        }

        .table .tbody::-webkit-scrollbar {
            width: 4px;
            /*滚动条整体样式*/
            height: 4px;
            /*高宽分别对应横竖滚动条的尺寸*/
        }

        .table .tbody::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: #20aeff;
            height: 8px;
        }

        .table .tr:nth-child(2n) {
            background: rgb(0 57 111 / 30%);
        }

        .table .tr:nth-child(2n + 1) {
            /* background: #035b86; */
        }

        .table .tr {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100px;
            line-height: 100px;
            font-size: 28px;
            color: #ffffff;
            cursor: pointer;
            text-align: center;
        }

        /* .table .tr:nth-child(2n) {
            background: #00396f;
        }
  
            .table .tr:nth-child(2n+1) {
            background: #035b86;
        }
        */
        .table .tr:hover {
            background-color: #6990b6;
        }

        .table .tr_td {
            letter-spacing: 0px;
            text-align: center;
            box-sizing: border-box;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }


        .title1 {
            font-size: 38px;
            color: #fff;
            margin-top: 20px;
            /* margin-left: 20px; */
            font-family: Arial;
            color: rgba(2, 193, 215, 1);
            font-style: normal;
            letter-spacing: 0px;
            text-decoration: none;
            margin-top: 40px;
            width: 100%;
            border-top: 1px solid #fff;
            padding-top: 20px;
        }
        .psyjmsg{
            width: 100%;
            height: 400px;
            margin-top: 20px;
            border-radius: 20px;
            box-sizing: border-box;
            overflow-y: auto;
            padding: 20px;
            border: 1px solid #6990b6;
            font-size: 28px;
    color: #ffffff;
    cursor: pointer;
    line-height: 40px;
        }
        .hoverBg{
            background-color: #4087cf !important;
        }
    </style>
</head>

<body>
    <div id="app">
        <nav style="padding: 0px 0; margin-top: 10px">
            <s-header-title2 style="width: 100%" title="项目评审情况" htype="2"></s-header-title2>
        </nav>
        <div class="table table1">
            <div class="th">
                <div class="th_td" style="flex:1" v-for="(item,index) in theadList" :key="index">
                    {{item}}
                </div>
            </div>
            <div class="tbody">
                <div class="tr" style="display: flex; justify-content: unset" v-for="item1 ,i) in tbodyList" :key="i"
                   @mouseover="selectStyle(item1,i)" :class="{'hoverBg':showAllYjNum==i}">
                    <div v-for="(item2 ,k) in item1" :key="k" style="flex:1">
                        {{(item2&&item2.length>6)?item2.slice(0,6)+"...":item2}}
                    </div>
                </div>
            </div>
            <div class="title1">{{showItem.xmmc+"评审意见："}}</div>
            <div class="psyjmsg">{{showItem.psyj}}</div>
        </div>
    </div>
</body>

</html>
<script src="/static/js/jslib/$min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#app",
        data: {
            theadList: ['项目名称', '评审进度', '项目进度', '评审专家', '评审意见', '可行性', '项目金额', '造价情况'],
            // 项目名称,评审进度,项目进度,评审专家,评审意见,可行性,项目金额,造价情况
            tbodyList: [],
            showItem:"",
            showAllYjNum:0,
        },
        created() { },
        mounted() {
            axios.get("/static/data/3840/dzzwxm/dzzwxm-006.json").then((res) => {
                console.log(res)
                this.tbodyList = res.data.data
                this.showItem = this.tbodyList[0]
                
            })
        },
        methods: {
            selectStyle(item,i){
                this.showAllYjNum = i
                this.showItem = item
            }
        },
    });
</script>