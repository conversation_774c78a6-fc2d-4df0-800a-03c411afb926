<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>党政机关整体智治-左</title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <style scoped>
        #dzjgztzz-left {
            width: 1050px;
            height: 1930px;
            background: url("/img/left-bg.png") no-repeat;
            background-size: 100% 100%;
        }

        .leftbox {
            width: 100%;
            /* flex: 1; */
            width: 1050px;
            display: flex;
            /* align-items: center; */
        }

        .leftbox>div {
            width: 50%;
        }

        .e-box {
            /* position: relative; */
        }

        .e-msg {
            text-align: center;
            color: #fff;
            font-size: 26px;
            right: 30px;
            width: 100%;
            margin: 10px 0;
        }

        .leftbox1 {
            position: relative;
            margin-bottom: 20px;
        }

        .select {
            position: absolute;
            z-index: 2;
            top: -15px;
            right: 20px;
        }

        .el-input__inner {
            font-size: 30px;
            width: 130px;
            height: 50px;
            line-height: 50px;
            color: #fff;
            background-color: #011040b3;
        }

        .el-select-dropdown__item.hover,
        .el-select-dropdown__item:hover {
            background-color: #011040b3;
        }

        .el-input__icon {
            line-height: 48px;
        }

        .el-select-dropdown {
            background-color: #011040b3;
        }

        .el-select-dropdown__item {
            font-size: 30px;
            color: #fff;
        }

        .el-select .el-input .el-select__caret {
            font-size: 30px;
        }

        /* 表格 */
        .table {
            width: 100%;
            height: 100%;
            padding: 10px;
            box-sizing: border-box;
            overflow-y: auto;
        }

        .table .th {
            width: 100%;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-style: italic;
            font-weight: 700;
            font-size: 20px;
            line-height: 40px;
            background: #00396f;
            color: #ffffff;
        }

        .table .th_td {
            letter-spacing: 0px;
            text-align: center;
            flex: 0.25;
        }

        .table .tbody {
            width: 100%;
            height: 120px;
            overflow: hidden;
        }

        .table .tbody:hover {
            overflow-y: auto;
        }

        .table .tbody::-webkit-scrollbar {
            width: 4px;
            /*滚动条整体样式*/
            height: 4px;
            /*高宽分别对应横竖滚动条的尺寸*/
        }

        .table .tbody::-webkit-scrollbar-thumb {
            border-radius: 10px;
            background: #20aeff;
            height: 8px;
        }

        .table .tr:nth-child(2n) {
            background: rgb(0 57 111 / 30%);
        }

        .table .tr:nth-child(2n + 1) {
            /* background: #035b86; */
        }

        .table .tr {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 40px;
            line-height: 40px;
            font-size: 22px;
            color: #ffffff;
            cursor: pointer;
        }

        /* .table .tr:nth-child(2n) {
            background: #00396f;
        }
  
            .table .tr:nth-child(2n+1) {
            background: #035b86;
        }
        */
        .table .tr:hover {
            background-color: #6990b6;
        }

        .table .tr_td {
            letter-spacing: 0px;
            text-align: center;
            box-sizing: border-box;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>

<body>
    <div id="dzjgztzz-left">
        <nav style="padding: 20px 45px">
            <s-header-title style="width: 100%" title="省级层面主要领域" htype="2"></s-header-title>
        </nav>

        <div class="leftbox">
            <div>
                <nav style="padding: 0px 0px; margin-top: -40px">
                    <s-header-title2 style="width: 100%" title="任务完成情况" htype="2"></s-header-title2>
                </nav>
                <div class="e-box">
                    <div class="e-msg">任务数：12个</div>
                    <div id="leftEcharts1" style="width: 100%; height: 180px"></div>
                </div>
            </div>
            <div>
                <nav style="padding: 0px 0px; margin-top: -20px">
                    <s-header-title2 style="width: 100%" title="核心业务事件组" htype="2"></s-header-title2>
                </nav>
                <div class="e-box">
                    <div id="leftEcharts2" style="width: 100%; height: 180px"></div>
                </div>
            </div>
        </div>

        <div class="leftbox1" style="margin-top: -30px">
            <div class="select">
                <el-select v-model="value0" placeholder="请选择" @change="change1">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div>
            <div id="leftEcharts3" style="width: 100%; height: 260px; margin-top: 0px"></div>
        </div>
        <div class="leftbox1">
            <div class="select">
                <el-select v-model="value1" placeholder="请选择" @change="change2">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div>
            <div id="leftEcharts4" style="width: 100%; height: 260px; margin-top: 0px"></div>
        </div>

        <nav style="padding: 20px 30px">
            <s-header-title style="width: 100%" title="四攻坚四争先任务贯彻落实情况" htype="2"></s-header-title>
        </nav>

        <div class="leftbox">
            <div>
                <nav style="padding: 0px 0px; margin-top: -20px">
                    <s-header-title2 style="width: 100%" title="任务完成情况" htype="2"></s-header-title2>
                </nav>
                <div class="e-box">
                    <div class="e-msg">任务数：12个</div>
                    <div id="leftEcharts5" style="width: 100%; height: 180px"></div>
                </div>
            </div>
            <div>
                <nav style="padding: 0px 0px; margin-top: -20px">
                    <s-header-title2 style="width: 100%" title="任务清单" htype="2"></s-header-title2>
                </nav>
                <div class="table table1">
                    <div class="th">
                        <div class="th_td" :style="{'flex':index==0?0.3:0.7}" v-for="(item,index) in theadList"
                            :key="index">
                            {{item}}
                        </div>
                    </div>
                    <div class="tbody" id="tbody1">
                        <div class="tr" v-for="(item ,i) in tbodyList" :key="i">
                            <div class="tr_td" style="flex: 0.3">{{i+1}}</div>
                            <div class="tr_td" style="flex: 0.7">{{item.name}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="leftbox1">
            <div class="select">
                <el-select v-model="value2" placeholder="请选择" @change="change3">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </div>
            <div id="leftEcharts6" style="width: 100%; height: 260px; margin-top: 0px"></div>
        </div>

        <div class="leftbox1">
            <div class="select">
                <!-- <el-select v-model="value3" placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select> -->
            </div>
            <div id="leftEcharts7" style="width: 100%; height: 260px; margin-top: 0px"></div>
        </div>
    </div>
</body>

</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#dzjgztzz-left",
        data: {
            theadList: ["序号", "任务清单"],
            tbodyList: [],
            value0: 3,
            value1: 3,
            value2: 3,
            options: [
                {
                    label: "1月",
                    value: 1,
                },
                {
                    label: "2月",
                    value: 2,
                },
                {
                    label: "3月",
                    value: 3,
                },
            ],
        },
        mounted() {
            $api("ldst_szhgg_dzjgztzz_left", { type: 1 }).then((res) => {
                this.initChartsLeft1(res, "leftEcharts1");
            });
            this.initChartsLeft2();

            $api("ldst_szhgg_dzjgztzz_left", { type: 3 }).then((res) => {
                let data = res;
                this.arrSortByKey(data, "value", false);
                this.initChartsLeft3(
                    data.map((item) => {
                        return item.name;
                    }),
                    data.map((item) => {
                        return item.value;
                    }),
                    data.map((item) => {
                        return item.value1;
                    }),
                    "任务完成数",
                    "完成率",
                    "bar",
                    "line",
                    "leftEcharts3"
                );
            });
            $api("ldst_szhgg_dzjgztzz_left", { type: 4 }).then((res) => {
                let data = res;
                this.arrSortByKey(data, "value", false);
                this.initChartsLeft3(
                    data.map((item) => {
                        return item.name;
                    }),
                    data.map((item) => {
                        return item.value;
                    }),
                    data.map((item) => {
                        return item.value1;
                    }),
                    "任务数",
                    "完成率",
                    "bar",
                    "line",
                    "leftEcharts4"
                );
            });

            $api("ldst_szhgg_dzjgztzz_left", { type: 5 }).then((res) => {
                let data = res;
                this.initChartsLeft1(data, "leftEcharts5");
            });
            $api("ldst_szhgg_dzjgztzz_left", { type: 6 }).then((res) => {
                this.tbodyList = res;
            });

            $api("ldst_szhgg_dzjgztzz_left", { type: 7 }).then((res) => {
                let data = res;
                this.arrSortByKey(data, "value", false);
                this.initChartsLeft3(
                    data.map((item) => {
                        return item.name;
                    }),
                    data.map((item) => {
                        return item.value;
                    }),
                    data.map((item) => {
                        return item.value1;
                    }),
                    "任务数",
                    "完成率",
                    "bar",
                    "line",
                    "leftEcharts6"
                );
            });
            $api("ldst_szhgg_dzjgztzz_left", { type: 8 }).then((res) => {
                let data = res;
                this.arrSortByKey(data, "value", false);
                this.initChartsLeft3(
                    data.map((item) => {
                        return item.name;
                    }),
                    data.map((item) => {
                        return item.value;
                    }),
                    data.map((item) => {
                        return item.value1;
                    }),
                    "任务数",
                    "完成率",
                    "bar",
                    "line",
                    "leftEcharts7"
                );
            });
        },
        methods: {
            /*
          array: 需要进行排序的数组
          key: 根据某个属性进行排序
          order: 升序/降序  true：升序 false:降序
      */
            arrSortByKey(array, key, order) {
                return array.sort((a, b) => {
                    console.log(a);
                    let value1 = a[key],
                        value2 = b[key];
                    if (order) {
                        //升序
                        return value1 - value2;
                    } else {
                        //降序
                        return value2 - value1;
                    }
                });
            },

            // list.sort(sortId)
            change1(item) {
                if (item === 1) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: 3 }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务完成数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts3"
                        );
                    });
                } else if (item === 2) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: "3-1" }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务完成数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts3"
                        );
                    });
                } else if (item === 3) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: "3-2" }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务完成数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts3"
                        );
                    });
                } else if (item === 4) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: "3-3" }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务完成数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts3"
                        );
                    });
                } else if (item === 5) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: "3-4" }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务完成数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts3"
                        );
                    });
                }
            },
            change2(item) {
                console.log(item);
                if (item === 1) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: 4 }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts4"
                        );
                    });
                } else if (item === 2) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: "4-1" }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts4"
                        );
                    });
                } else if (item === 3) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: "4-2" }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts4"
                        );
                    });
                } else if (item === 4) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: "4-3" }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts4"
                        );
                    });
                } else if (item === 5) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: "4-4" }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts4"
                        );
                    });
                }
            },
            change3(item) {
                if (item === 1) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: 7 }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts6"
                        );
                    });
                } else if (item === 2) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: "7-1" }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts6"
                        );
                    });
                } else if (item === 3) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: "7-2" }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts6"
                        );
                    });
                } else if (item === 4) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: "7-3" }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts6"
                        );
                    });
                } else if (item === 5) {
                    $api("ldst_szhgg_dzjgztzz_left", { type: "7-4" }).then((res) => {
                        let data = res;
                        this.arrSortByKey(data, "value", false);
                        this.initChartsLeft3(
                            data.map((item) => {
                                return item.name;
                            }),
                            data.map((item) => {
                                return item.value;
                            }),
                            data.map((item) => {
                                return item.value1;
                            }),
                            "任务数",
                            "完成率",
                            "bar",
                            "line",
                            "leftEcharts6"
                        );
                    });
                }
            },
            initChartsLeft1(data, dom) {
                let that = this;
                let myChart = echarts.init(document.getElementById(dom));
                // let myChart = echarts.init(document.getElementById("myEcharts3"));
                let imgUrl = "";
                let option = {
                    title: {
                        text: "",
                        subtext: "",
                        left: "center",
                    },
                    tooltip: {
                        trigger: "item",
                        // formatter: '{b}: <br/> {d}%',
                        formatter: "{b}: <br/> {c}个<br/> {d}%",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "22",
                        },
                    },
                    legend: {
                        orient: "vertical",
                        left: "left",
                        show: false,
                    },
                    series: [
                        {
                            name: "",
                            type: "pie",
                            radius: "50%",
                            center: ["48%", "35%"],
                            data: data,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: "rgba(0, 0, 0, 0.5)",
                                },
                            },
                            label: {
                                normal: {
                                    formatter: "{b} {c}%  ",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 22,
                                    },
                                },
                            },
                        },
                    ],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },
            initChartsLeft2() {
                $api("ldst_szhgg_dzjgztzz_left", { type: 2 }).then((res) => {
                    let charts1Data = res;
                    let that = this;
                    let myChart = echarts.init(document.getElementById("leftEcharts2"));
                    let option = {
                        color: [
                            "#00C0FF",
                            "#22E8E8",
                            "#FFD461",
                            "#A9DB52",
                            "#B76FD8",
                            "#FD852E",
                            "#FF4949",
                            "#0594C3",
                            "#009D9D",
                            "#A47905",
                        ],

                        tooltip: {
                            trigger: "item",
                            // formatter: '{b}: <br/> {d}%',
                            formatter: "{b}: <br/> {c}个<br/> {d}%",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "25",
                            },
                        },
                        legend: {
                            orient: "vertical",
                            left: "40%",
                            top: "24%",
                            bottom: "0%",
                            icon: "circle",
                            itemGap: 30,
                            textStyle: {
                                rich: {
                                    name: {
                                        fontSize: 25,
                                        color: "#ffffff",
                                        padding: [0, 20, 0, 15],
                                    },
                                    value: {
                                        fontSize: 25,
                                        color: "#2CC6F9",
                                        // padding: [10, 0, 0, 15]
                                    },
                                },
                            },
                            formatter: function (name) {
                                var data = option.series[0].data; //获取series中的data
                                var total = 0;
                                var tarValue;
                                for (var i = 0, l = data.length; i < l; i++) {
                                    total += data[i].value;
                                    if (data[i].name == name) {
                                        tarValue = data[i].value;
                                    }
                                }
                                that.serverNum = total;
                                var p = ((tarValue / total) * 100).toFixed(2);
                                return "{name|" + name + "}{value|" + tarValue + "个  " + p + "%}";
                            },
                        },
                        series: [
                            {
                                name: "",
                                type: "pie",
                                radius: ["40%", "60%"],
                                center: ["20%", "50%"],
                                roseType: "",
                                label: {
                                    show: false,
                                },
                                data: charts1Data,
                            },
                        ],
                    };
                    myChart.setOption(option);
                    myChart.getZr().on("mousemove", (param) => {
                        myChart.getZr().setCursorStyle("default");
                    });
                });
            },
            initChartsLeft3(xdata, ydata1, ydata2, name1, name2, type1, type2, dom) {
                let myChart = echarts.init(document.getElementById(dom));
                var seriousAuto = [
                    {
                        name: name1,
                        type: type1,
                        barWidth: "20%",
                        // smooth: true,
                        yAxisIndex: 0,
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    {
                                        offset: 0,
                                        color: "#00C0FF",
                                    },
                                    {
                                        offset: 1,
                                        color: "rgba(0,192,255,0)",
                                    },
                                ]),
                                barBorderRadius: 4,
                            },
                        },
                        // label: {
                        //   show: true,
                        //   fontSize: 18,
                        //   fontWeight: "bold",
                        //   color: "#fff",
                        //   marginTop: 15,
                        //   position: "top",
                        // },
                        data: ydata1,
                        areaStyle: {
                            //填充
                            color: "#00C0FF",
                            opacity: 1,
                        },
                    },
                    {
                        name: name2,
                        type: type2,
                        barWidth: "20%",
                        // smooth: true,
                        yAxisIndex: 1,
                        itemStyle: {
                            normal: {
                                color:
                                    type2 !== "line"
                                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                            {
                                                offset: 0,
                                                color: "#2DF09F",
                                            },
                                            {
                                                offset: 1,
                                                color: "rgba(0,192,255,0)",
                                            },
                                        ])
                                        : "#2DF09F",
                                barBorderRadius: 4,
                            },
                        },
                        // label: {
                        //   show: true,
                        //   fontSize: 18,
                        //   fontWeight: "bold",
                        //   color: "#fff",
                        //   marginTop: 15,
                        //   position: "top",
                        // },
                        data: ydata2,
                        // areaStyle:"",
                    },
                ];
                if (type2 == "line") {
                    delete seriousAuto[2];
                }

                if (ydata2.length <= 0) {
                    seriousAuto = [
                        {
                            name: name1,
                            type: type1,
                            barWidth: "20%",
                            smooth: true,
                            yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#00C0FF",
                                        },
                                        {
                                            offset: 1,
                                            color: "rgba(0,192,255,0)",
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            label: {
                                show: true,
                                fontSize: 22,
                                fontWeight: "bold",
                                color: "#fff",
                                marginTop: 15,
                                position: "top",
                            },
                            data: ydata1,
                            areaStyle: {
                                //填充
                                color: "#00C0FF",
                                opacity: 1,
                            },
                        },
                    ];
                }
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                        // formatter: function (params) {
                        //     console.log(params);
                        //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
                        //     for (var i = 0; i < params.length; i++) {
                        //       if (params[i].data != undefined) {
                        //         res +=
                        //           "<p>" +
                        //           params[i].marker +
                        //           params[i].seriesName+" "+
                        //           params[i].data +
                        //           "万件" +
                        //           "</p>";
                        //       }
                        //     }
                        //     return res;
                        //   },
                    },
                    legend: {
                        orient: "horizontal",
                        // icon: "circle",
                        itemGap: 45,
                        textStyle: {
                            color: "#D6E7F9",
                            fontSize: 22,
                        },
                    },
                    grid: {
                        left: "5%",
                        right: "6%",
                        top: "20%",
                        bottom: "1%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: "category",
                            data: xdata,
                            axisLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)", // 颜色
                                    width: 1, // 粗细
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                // rotate: 30,
                                textStyle: {
                                    color: "#D6E7F9",
                                    fontSize: 22,
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: "单位：个",
                            type: "value",
                            nameTextStyle: {
                                fontSize: 22,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 22,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                        {
                            name: "",
                            type: "value",
                            max: 100,
                            nameTextStyle: {
                                fontSize: 22,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                formatter: "{value}%",
                                textStyle: {
                                    fontSize: 22,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    series: seriousAuto,
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },
        },
    });
</script>