<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title></title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <style>
            #xsqstjc-left {
                width: 1050px;
                height: 1930px;
                background: url("/img/left-bg.png") no-repeat;
                background-size: 100% 100%;
                /* display: flex;
      justify-content: space-evenly;
      flex-direction: column;
      align-items: center; */
            }

            .leftbox {
                position: relative;
            }

            .detBtn {
                width: 114px;
                height: 49px;
                border-style: none;
                border-color: unset;
                color: rgb(255, 255, 255);
                border-radius: 4px;
                font-size: 24px;
                text-align: center;
                font-weight: normal;
                font-style: normal;
                background: rgb(0, 135, 236);
                line-height: 49px;
                position: absolute;
                right: 40px;
                top: -100px;
                cursor: pointer;
            }
        </style>
    </head>

    <body>
        <div id="xsqstjc-left">
            <div class="content">
                <div class="box">
                    <div class="title" style="font-size: 18px">
                        <nav style="padding: 20px 45px">
                            <s-header-title style="width: 100%" title="经济调节领域汇集展示" htype="2"></s-header-title>
                        </nav>
                    </div>
                    <nav style="padding: 0px 0">
                        <s-header-title2 style="width: 100%" title="经济调节" htype="2"></s-header-title2>
                    </nav>
                    <div class="leftbox">
                        <div class="detBtn">详情</div>
                        <div
                            id="leftCharts1"
                            style="width: 95%; height: 310px; margin-top: 10px; margin-left: 3%"
                        ></div>
                    </div>
                    <nav style="padding: 0px 0; margin-top: 10px">
                        <s-header-title2 style="width: 100%" title="经济调节" htype="2"></s-header-title2>
                    </nav>
                    <div class="leftbox">
                        <div
                            id="leftCharts2"
                            style="width: 95%; height: 310px; margin-top: 10px; margin-left: 3%"
                        ></div>
                    </div>
                </div>
                <div class="box">
                    <div class="title" style="font-size: 18px">
                        <nav style="padding: 20px 45px">
                            <s-header-title style="width: 100%" title="市场监管领域汇集展示" htype="2"></s-header-title>
                        </nav>
                    </div>
                    <nav style="padding: 0px 0">
                        <s-header-title2 style="width: 100%" title="市场监管" htype="2"></s-header-title2>
                    </nav>
                    <div class="leftbox">
                        <div class="detBtn">详情</div>
                        <div
                            id="leftCharts3"
                            style="width: 95%; height: 310px; margin-top: 10px; margin-left: 3%"
                        ></div>
                    </div>
                    <nav style="padding: 0px 0">
                        <s-header-title2 style="width: 100%" title="食品药品监管" htype="2"></s-header-title2>
                    </nav>
                    <div class="leftbox">
                        <div
                            id="leftCharts4"
                            style="width: 95%; height: 310px; margin-top: 10px; margin-left: 3%"
                        ></div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#xsqstjc-left",
        data: {},
        mounted() {
            $api("ldst_xsqstjc_index_xsqstjc", { type: "left001" }).then((res) => {
                let data = res;
                this.initChartleft1(
                    data.map((item) => {
                        return item.name;
                    }),
                    data.map((item) => {
                        return item.value;
                    }),
                    [],
                    "地区生产总值",
                    "",
                    "bar",
                    "",
                    "leftCharts1"
                );
            });
            $api("ldst_xsqstjc_index_xsqstjc", { type: "left002" }).then((res) => {
                let data = res;
                this.initChartleft1(
                    data.map((item) => {
                        return item.name;
                    }),
                    data.map((item) => {
                        return item.value;
                    }),
                    [],
                    "疫苗接种数",
                    "",
                    "bar",
                    "",
                    "leftCharts2"
                );
            });
            $api("ldst_xsqstjc_index_xsqstjc", { type: "left003" }).then((res) => {
                let data = res;
                this.initChartleft2(data, "leftCharts3");
            });

            $api("ldst_xsqstjc_index_xsqstjc", { type: "left004" }).then((res) => {
                let data = res;
                this.initChartleft1(
                    data.map((item) => {
                        return item.name;
                    }),
                    data.map((item) => {
                        return item.value;
                    }),
                    data.map((item) => {
                        return item.value1;
                    }),
                    "不合格企业数",
                    "同比增长率",
                    "bar",
                    "line",
                    "leftCharts4"
                );
            });
        },
        methods: {
            initChartleft1(xdata, ydata1, ydata2, name1, name2, type1, type2, dom) {
                let myChart = echarts.init(document.getElementById(dom));
                var seriousAuto = [
                    {
                        name: name1,
                        type: type1,
                        barWidth: "20%",
                        // smooth: true,
                        yAxisIndex: 0,
                        itemStyle: {
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    {
                                        offset: 0,
                                        color: "#00C0FF",
                                    },
                                    {
                                        offset: 1,
                                        color: "rgba(0,192,255,0)",
                                    },
                                ]),
                                barBorderRadius: 4,
                            },
                        },
                        // label: {
                        //   show: true,
                        //   fontSize: 18,
                        //   fontWeight: "bold",
                        //   color: "#fff",
                        //   marginTop: 15,
                        //   position: "top",
                        // },
                        data: ydata1,
                        areaStyle: {
                            //填充
                            color: "#00C0FF",
                            opacity: 1,
                        },
                    },
                    {
                        name: name2,
                        type: type2,
                        barWidth: "20%",
                        // smooth: true,
                        yAxisIndex: 0,
                        itemStyle: {
                            normal: {
                                color:
                                    type2 !== "line"
                                        ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                              {
                                                  offset: 0,
                                                  color: "#2DF09F",
                                              },
                                              {
                                                  offset: 1,
                                                  color: "rgba(0,192,255,0)",
                                              },
                                          ])
                                        : "#2DF09F",
                                barBorderRadius: 4,
                            },
                        },
                        // label: {
                        //   show: true,
                        //   fontSize: 18,
                        //   fontWeight: "bold",
                        //   color: "#fff",
                        //   marginTop: 15,
                        //   position: "top",
                        // },
                        data: ydata2,
                        // areaStyle:"",
                    },
                ];
                if (type2 == "line") {
                    delete seriousAuto[2];
                }

                if (ydata2.length <= 0) {
                    seriousAuto = [
                        {
                            name: name1,
                            type: type1,
                            barWidth: "20%",
                            smooth: true,
                            yAxisIndex: 0,
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: "#00C0FF",
                                        },
                                        {
                                            offset: 1,
                                            color: "rgba(0,192,255,0)",
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            label: {
                                show: true,
                                fontSize: 22,
                                fontWeight: "bold",
                                color: "#fff",
                                marginTop: 15,
                                position: "top",
                            },
                            data: ydata1,
                            areaStyle: {
                                //填充
                                color: "#00C0FF",
                                opacity: 1,
                            },
                        },
                    ];
                }
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                        // formatter: function (params) {
                        //     console.log(params);
                        //     var res = "<div style='margin:0;padding:0;height:28px;line-height:28px;'><p>" + params[0].name + "</p></div>";
                        //     for (var i = 0; i < params.length; i++) {
                        //       if (params[i].data != undefined) {
                        //         res +=
                        //           "<p>" +
                        //           params[i].marker +
                        //           params[i].seriesName+" "+
                        //           params[i].data +
                        //           "万件" +
                        //           "</p>";
                        //       }
                        //     }
                        //     return res;
                        //   },
                    },
                    legend: {
                        orient: "horizontal",
                        // icon: "circle",
                        itemGap: 45,
                        textStyle: {
                            color: "#D6E7F9",
                            fontSize: 22,
                        },
                    },
                    grid: {
                        left: "0%",
                        right: "6%",
                        top: "15%",
                        bottom: "1%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: "category",
                            data: xdata,
                            axisLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)", // 颜色
                                    width: 1, // 粗细
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                rotate: 30,
                                textStyle: {
                                    color: "#D6E7F9",
                                    fontSize: 22,
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: "单位：个",
                            type: "value",
                            nameTextStyle: {
                                fontSize: 22,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 22,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                        {
                            name: "",
                            type: "value",
                            max: 100,
                            nameTextStyle: {
                                fontSize: 22,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                formatter: "{value}%",
                                textStyle: {
                                    fontSize: 22,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    series: seriousAuto,
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },
            initChartleft2(data, dom) {
                let myChart = echarts.init(document.getElementById(dom));

                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "22",
                        },
                    },
                    legend: {
                        orient: "horizontal",
                        // icon: "circle",
                        itemGap: 45,
                        textStyle: {
                            color: "#D6E7F9",
                            fontSize: 22,
                        },
                    },
                    grid: {
                        left: "0%",
                        right: "4%",
                        bottom: "3%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: "category",
                            data: data.map((item) => {
                                return item.name;
                            }),
                            axisLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)", // 颜色
                                    width: 1, // 粗细
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                rotate: 30,
                                textStyle: {
                                    color: "#D6E7F9",
                                    fontSize: 22,
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            nameTextStyle: {
                                fontSize: 22,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 22,
                                    color: "#D6E7F9",
                                },
                            },
                            type: "value",
                        },
                    ],
                    series: [
                        {
                            name: "外资企业",
                            type: "bar",
                            stack: "Ad",
                            barWidth: 35,
                            // itemStyle: {
                            //   normal: {
                            //     color: "#2DF09F",
                            //   },
                            // },

                            data: data.map((item) => {
                                return item.value;
                            }),
                        },
                        {
                            name: "农村合作社",
                            type: "bar",
                            stack: "Ad",
                            data: data.map((item) => {
                                return item.value1;
                            }),
                        },
                        {
                            name: "个体工商户",
                            type: "bar",
                            stack: "Ad",
                            data: data.map((item) => {
                                return item.value2;
                            }),
                        },
                        {
                            name: "内资企业",
                            type: "bar",
                            stack: "Ad",
                            data: data.map((item) => {
                                return item.value3;
                            }),
                        },
                    ],
                };
                myChart.setOption(option);
                myChart.getZr().on("mousemove", (param) => {
                    myChart.getZr().setCursorStyle("default");
                });
            },
        },
    });
</script>
