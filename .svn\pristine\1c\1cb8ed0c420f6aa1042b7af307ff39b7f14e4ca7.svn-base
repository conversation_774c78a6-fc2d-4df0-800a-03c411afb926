<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <title>一网通管弹窗</title>
  <script src="./Vue/vue.js"></script>
  <script src="elementui/js/elementui.js"></script>
  <link rel="stylesheet" href="elementui/css/elementui.css" />
  <!--<script src="../csdn/echarts/echarts.min.js"></script>-->
  <!--<script src="http://unpkg.com/@jiaminghi/data-view/dist/datav.min.vue.js"></script>-->
  <!--<script src="./jquery/jquery-3.4.1.min.js"></script>-->
  <!--<script src="/static/js/jslib/echarts-wordcloud.min.js"></script>-->
  <!--<link rel="stylesheet" href="/static/css/sigma.css" />-->
  <!-- <script src="/static/js/jslib/umap2d.min.js"></script> -->
  <!--<script src="https://cdn.bootcdn.net/ajax/libs/mapbox-gl/2.7.0/mapbox-gl.min.js"></script>-->
  <!--<script src="http://*************:9090/umap2.0/mapbox/umap2d_mb.js"></script>-->
  <!--<link rel="stylesheet" href="/static/css/animate.css" />-->
  <!--<script src="/static/citybrain/hjbh/js/echarts.js"></script>-->
</head>

<body>
<div id="app">
  <div class="Dialog">
    <div class="Title">
      <div class="Title-name">重大应用贯通业务运行情况</div>
      <i class="Title-close" onclick="top.commonObj.funCloseIframe({name:'ywtg-dialog1'})"></i>
    </div>
    <div class="Table">
      <el-table
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column
          type="index"
          label="序号"
          width="160">
        </el-table-column>
        <el-table-column
          prop="county"
          label="区县 "
          width="200">
        </el-table-column>
        <el-table-column label="七张问题清单">
          <el-table-column
            prop="num1"
            label="界面调用数"
            width="330">
          </el-table-column>
        </el-table-column>
        <el-table-column label="防汛防台">
          <el-table-column
            prop="num2"
            label="界面调用数(PC端)"
            width="250">
          </el-table-column>
          <el-table-column
            prop="num3"
            label="界面调用数(移动端)"
            width="250">
          </el-table-column>
        </el-table-column>
        <el-table-column label="社区矫正">
          <el-table-column
            prop="num4"
            label="任务执行反馈数"
            width="310">
          </el-table-column>
          <el-table-column
            prop="num5"
            label="任务执行完成率"
            width="310">
          </el-table-column>
        </el-table-column>
        <el-table-column label="e行在线">
          <el-table-column
            prop="num6"
            label="任务执行反馈数(维修企业)"
            width="280">
          </el-table-column>
          <el-table-column
            prop="num7"
            label="任务执行完成率"
            width="280">
          </el-table-column>
          <el-table-column
            prop="num8"
            label="任务执行反馈数(销售企业)"
            width="280">
          </el-table-column>
          <el-table-column
            prop="num9"
            label="任务执行完成率"
            width="280">
          </el-table-column>
          <el-table-column
            prop="num10"
            label="任务执行反馈数(回收企业)"
            width="280">
          </el-table-column>
          <el-table-column
            prop="num11"
            label="任务执行完成率"
            width="280">
            <template slot-scope="scope">
              <div :class="{active:scope.row.num11 != '100.00%' && scope.row.num11 != '/'}">{{scope.row.num11}}</div>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
  </div>
</div>
<!--<script src="/static/js/jslib/axios.min.js"></script>-->
<!--&lt;!&ndash;<script src="/static/js/jslib/http.interceptor.js"></script>&ndash;&gt;-->
<!--<script src="/static/citybrain/csdn/js/mapx.js"></script>-->
<!--<script src="/static/js/jslib/Emiter.js"></script>-->
<script>
  var vm = new Vue({
    el: '#app',
    data: {
      tableData:[
        {
          county: '市辖区',
          num1: '0',
          num2: '/',
          num3: '/',
          num4: '0',
          num5: '/',
          num6: '0',
          num7: '/',
          num8: '0',
          num9: '/',
          num10: '0',
          num11: '/',
        },
        {
          county: '婺城区',
          num1: '36514',
          num2: '57',
          num3: '3627',
          num4: '1142',
          num5: '99.83%',
          num6: '297',
          num7: '100.00%',
          num8: '287',
          num9: '100.00%',
          num10: '198',
          num11: '100.00%',
        },
        {
          county: '金义新区',
          num1: '289',
          num2: '25',
          num3: '3639',
          num4: '401',
          num5: '100.00%',
          num6: '113',
          num7: '90.40%',
          num8: '114',
          num9: '90.48%',
          num10: '71',
          num11: '89.87%',
        },
        {
          county: '武义县',
          num1: '6760',
          num2: '193',
          num3: '4956',
          num4: '270',
          num5: '100.00%',
          num6: '158',
          num7: '100.00%',
          num8: '165',
          num9: '100.00%',
          num10: '106',
          num11: '100.00%',
        },
        {
          county: '浦江县',
          num1: '1841',
          num2: '756',
          num3: '4751',
          num4: '295',
          num5: '99.33%',
          num6: '160',
          num7: '100.00%',
          num8: '110',
          num9: '100.00%',
          num10: '74',
          num11: '100.00%',
        },
        {
          county: '磐安县',
          num1: '30109',
          num2: '105',
          num3: '3728',
          num4: '119',
          num5: '100.00%',
          num6: '52',
          num7: '100.00%',
          num8: '60',
          num9: '100.00%',
          num10: '22',
          num11: '100.00%',
        },
        {
          county: '兰溪市',
          num1: '4772',
          num2: '62',
          num3: '1026',
          num4: '329',
          num5: '100.00%',
          num6: '181',
          num7: '100.00%',
          num8: '193',
          num9: '100.00%',
          num10: '91',
          num11: '100.00%',
        },
        {
          county: '义乌市',
          num1: '5993',
          num2: '84',
          num3: '2773',
          num4: '1051',
          num5: '99.15%',
          num6: '439',
          num7: '100.00%',
          num8: '433',
          num9: '100.00%',
          num10: '195',
          num11: '100.00%',
        },
        {
          county: '东阳市',
          num1: '17753',
          num2: '134',
          num3: '2997',
          num4: '480',
          num5: '99.79%',
          num6: '403',
          num7: '100.00%',
          num8: '368',
          num9: '100.00%',
          num10: '277',
          num11: '100.00%',
        },
        {
          county: '永康市',
          num1: '36996',
          num2: '172',
          num3: '90',
          num4: '634',
          num5: '99.69%',
          num6: '219',
          num7: '100.00%',
          num8: '254',
          num9: '100.00%',
          num10: '183',
          num11: '100.00%',
        },]
    },
    watch: {},
    computed: {},
    mounted() {},
    methods: {},
  })
</script>

<style>

  ::-webkit-scrollbar {
    height: 0;
  }

  .Dialog {
    width: 3597px;
    height: 1511px;
    background-color: #031827;
    box-shadow: 0px 3px 35px 0px
    #000000;
    border: 2px solid #359cf8;
    border-image-source: linear-gradient(-32deg,
    #359cf8 0%,
    #afdcfb 100%);
    border-image-slice: 1;
  }

  .Title {
    width: 3597px;
    height: 100px;
    background-image: linear-gradient(0deg,
    #073346 0%,
    #00aae2 100%),
    linear-gradient(
      #ffffff,
      #ffffff);
    background-blend-mode: normal,
    normal;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .Title-name {
    font-family: SourceHanSansCN-Bold;
    font-size: 48px;
    font-weight: 800;
    font-stretch: normal;
    letter-spacing: 1px;
    color: #ffffff;
    rgba(0, 0, 0, 0.6);
    margin-left: 56px;
    line-height: 45px;
  }

  .Title-close {
    width: 34px;
    height: 34px;
    background: url("YWT2-img/close.png") no-repeat;
    background-size: cover;
    margin-right: 60px;
    cursor: pointer;
  }

  .active {
    background: linear-gradient(to bottom, #ffcdcd, #ffffff, #ff4949, #ffffff);
    -webkit-background-clip: text;
    color: transparent;
  }

  .Table {
    width: 3490px;
    height: 1299px;
    margin: 60px 0 0 59px;
  }

  .Table .el-table {
    border-top: 2px solid #00c0ff;
    border-left: 2px solid #00c0ff;
  }

  .Table .el-table .cell {
    overflow: unset;
    line-height: unset;
  }

  .Table .el-table--enable-row-hover .el-table__body tr:hover > td {
    background-color: #031827 !important;
  }

  .Table .el-table thead.is-group th{
    background: #004c70;
    color: #ffffff;
    text-align: center;
    border-right: solid 1px #00c0ff;
    border-bottom: solid 1px #00c0ff;
    height: 149.5px;
    font-size: 40px;
    font-weight: 800;
  }

  .Table .el-table--border::after, .el-table--group::after, .el-table::before {
    background: #00c0ff;
  }

  /*序号列*/
  .Table .el-table_1_column_1 {
    background: #004c70;
    color: #ffffff;
    text-align: center;
    border-right: solid 1px #00c0ff;
    border-bottom: solid 1px #00c0ff;
    font-size: 40px;
    height: 100px;
    font-weight: 800;
  }

  /*区县列*/
  .Table .el-table_1_column_2 {
    background: #004c70;
    color: #ffffff;
    text-align: center;
    border-right: solid 1px #00c0ff;
    border-bottom: solid 1px #00c0ff;
    font-size: 40px;
    height: 100px;
    font-weight: 800;
  }





  .Table .el-table_1_column_3_column_4 {
    background: #043755;
    font-family: SourceHanSansCN-Regular;
    font-size: 40px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    text-align: center;
    border-right: solid 1px #00c0ff;
    border-bottom: solid 1px #00c0ff;
  }

  .Table .el-table_1_column_5_column_6 {
    background: #043755;
    font-family: SourceHanSansCN-Regular;
    font-size: 40px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    text-align: center;
    border-right: solid 1px #00c0ff;
    border-bottom: solid 1px #00c0ff;
  }

  .Table .el-table_1_column_5_column_7 {
    background: #043755;
    font-family: SourceHanSansCN-Regular;
    font-size: 40px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    text-align: center;
    border-right: solid 1px #00c0ff;
    border-bottom: solid 1px #00c0ff;
  }

  .Table .el-table_1_column_8_column_9 {
    background: #043755;
    font-family: SourceHanSansCN-Regular;
    font-size: 40px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    text-align: center;
    border-right: solid 1px #00c0ff;
    border-bottom: solid 1px #00c0ff;
  }

  .Table .el-table_1_column_8_column_10 {
    background: #043755;
    font-family: SourceHanSansCN-Regular;
    font-size: 40px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    text-align: center;
    border-right: solid 1px #00c0ff;
    border-bottom: solid 1px #00c0ff;
  }

  .Table .el-table_1_column_11_column_12 {
     background: #043755;
     font-family: SourceHanSansCN-Regular;
     font-size: 40px;
     font-weight: normal;
     font-stretch: normal;
     letter-spacing: 0px;
     color: #ffffff;
     text-align: center;
     border-right: solid 1px #00c0ff;
     border-bottom: solid 1px #00c0ff;
   }

  .Table .el-table_1_column_11_column_13 {
    background: #043755;
    font-family: SourceHanSansCN-Regular;
    font-size: 40px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    text-align: center;
    border-right: solid 1px #00c0ff;
    border-bottom: solid 1px #00c0ff;
  }

  .Table .el-table_1_column_11_column_14 {
    background: #043755;
    font-family: SourceHanSansCN-Regular;
    font-size: 40px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    text-align: center;
    border-right: solid 1px #00c0ff;
    border-bottom: solid 1px #00c0ff;
  }

  .Table .el-table_1_column_11_column_15 {
    background: #043755;
    font-family: SourceHanSansCN-Regular;
    font-size: 40px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    text-align: center;
    border-right: solid 1px #00c0ff;
    border-bottom: solid 1px #00c0ff;
  }

  .Table .el-table_1_column_11_column_16 {
    background: #043755;
    font-family: SourceHanSansCN-Regular;
    font-size: 40px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    text-align: center;
    border-right: solid 1px #00c0ff;
    border-bottom: solid 1px #00c0ff;
  }

  .Table .el-table_1_column_11_column_17 {
    background: #043755;
    font-family: SourceHanSansCN-Regular;
    font-size: 40px;
    font-weight: normal;
    font-stretch: normal;
    letter-spacing: 0px;
    color: #ffffff;
    text-align: center;
    border-right: solid 1px #00c0ff;
    border-bottom: solid 1px #00c0ff;
  }
</style>
</body>
</html>
