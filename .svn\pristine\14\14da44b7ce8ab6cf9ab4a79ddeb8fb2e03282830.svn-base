@charset "UTF-8";
.dqwr_app_box {
  position: relative;
  box-sizing: border-box;
  width: 2045px;
  height: 1850px;
  background-image: url("../img/common/bg.png");
  background-size: 100% 100%;
  padding: 10px 55px 30px;
}

.dqwr_app_box .dqwr_header {
  width: 1935px;
  display: flex;
  height: 130px;
  align-items: center;
  background: url("../img/common/一级标题3.png") no-repeat;
  background-position: 0 55px;
}

.dqwr_app_box .dqwr_header .title {
  font-size: 54px;
  background-image: -webkit-linear-gradient(top, #ffffff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.dqwr_app_box .dqwr_header .title_icon_one {
  width: 78px;
  height: 75px;
  position: relative;
  left: 0;
  top: 5px;
  background-image: url("../img/common/一级标题1.png");
  background-size: 100% 100%;
}

.dqwr_app_box .dqwr_header .title_hr {
  margin: 10px 30px 0;
  width: 1110px;
  height: 21px;
  background-image: url("../img/common/一级标题2.png");
  background-size: 100% 100%;
}

.dqwr_app_box .dqwr_header span {
  font-size: 32px;
  background-image: -webkit-linear-gradient(bottom, #ffffff, #83b8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dqwr_app_box .dqwr_container .dqwr_container_head {
  margin: 30px auto 20px;
  width: 584px;
  display: flex;
  align-items: center;
}

.dqwr_app_box .dqwr_container .dqwr_container_head h2 {
  font-size: 40px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.dqwr_app_box .dqwr_container .dqwr_container_head i {
  display: inline-block;
}

.dqwr_app_box .dqwr_container .dqwr_container_head .icon_left {
  width: 142px;
  height: 53px;
  background: url("../img/common/二级标题左.png") no-repeat center;
  background-size: 100% 100%;
  margin-right: 30px;
}

.dqwr_app_box .dqwr_container .dqwr_container_head .icon_right {
  width: 142px;
  height: 53px;
  background: url("../img/common/二级标题右.png") no-repeat center;
  background-size: 100% 100%;
  margin-left: 30px;
}

.dqwr_app_box .dqwr_container .sskqzl {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28px;
  color: #ffffff;
  text-align: center;
}

.dqwr_app_box .dqwr_container .sskqzl .left_list {
  width: 292px;
  height: 375px;
}

.dqwr_app_box .dqwr_container .sskqzl .right_canvas {
  height: 375px;
  display: flex;
}

.dqwr_app_box .dqwr_container .sskqzl .right_canvas .main_opt {
  width: 765px;
  padding-top: 60px;
  box-sizing: border-box;
  margin: 0 30px 0;
}

.dqwr_app_box .dqwr_container .sskqzl .right_canvas .main_opt ul {
  overflow: hidden;
}

.dqwr_app_box .dqwr_container .sskqzl .right_canvas .main_opt ul li {
  float: left;
  width: 230px;
  height: 120px;
  box-sizing: border-box;
  margin-right: 35px;
}

.dqwr_app_box .dqwr_container .sskqzl .right_canvas .main_opt ul li .pm_title {
  font-size: 20px;
  text-align: left;
}

.dqwr_app_box .dqwr_container .sskqzl .right_canvas .main_opt ul li .pm_yl {
  display: flex;
  justify-content: space-between;
  font-size: 32px;
  padding: 8px 10px 18px;
  overflow: hidden;
}

.dqwr_app_box .dqwr_container .sskqzl .right_canvas .main_opt ul li:nth-child(3) {
  margin-right: 0;
}

.dqwr_app_box .dqwr_container .sskqzl .right_canvas .main_opt ul li:nth-child(6) {
  margin-right: 0;
}

.dqwr_app_box .dqwr_container .sskqzl .right_canvas .main_opt .bottom_title {
  margin-top: 18px;
}

.dqwr_app_box .dqwr_container .kqzlkh {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20px;
}

.dqwr_app_box .dqwr_container .kqzlkh .aqi_item {
  position: relative;
}

.dqwr_app_box .dqwr_container .kqzlkh .aqi_item .title {
  font-size: 38px;
  color: #d6e7f9;
}

.dqwr_app_box .dqwr_container .kqzlkh .aqi_item .title .three_title {
  display: inline-block;
  width: 25px;
  height: 25px;
  background: url("../img/common/三级标题图标.png");
  background-size: 100% 100%;
}

.dqwr_app_box .dqwr_container .kqzlkh .aqi_item .aqi_item_price {
  display: flex;
  font-size: 30px;
  color: #ffffff;
  width: 580px;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 30px 60px 0 0px;
  margin-bottom: -35px;
}

.dqwr_app_box .dqwr_container .kqzlkh .aqi_item .aqi_item_price span {
  display: inline-block;
  color: #e2ba43;
}

.dqwr_app_box .dqwr_container .kqzlkh .aqi_item .compare_model {
  position: absolute;
  top: 35%;
  right: 10%;
  text-align: center;
  color: #ffffff;
}

.dqwr_app_box .dqwr_container .kqzlph {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20px;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .kqzlph_item_title {
  width: 940px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .kqzlph_item_title .title {
  font-size: 38px;
  color: #d6e7f9;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .kqzlph_item_title .title .three_title {
  display: inline-block;
  width: 25px;
  height: 25px;
  background: url("../img/common/三级标题图标.png");
  background-size: 100% 100%;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .kqzlph_item_title .kqzlph_select {
  outline: none;
  position: relative;
  font-size: 35px;
  width: 250px;
  height: 55px;
  border-radius: 40px;
  border: 1px #3fa1f8 solid;
  box-sizing: border-box;
  padding: 0 30px;
  background: #04214e;
  color: #ffffff;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .item_tit_two {
  margin-top: 40px;
  display: flex;
  justify-content: space-between;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .item_tit_two .tit_one {
  width: 465px;
  height: 75px;
  background: url("../img/dqhj/矩形3拷贝17.png");
  line-height: 75px;
  color: #ffffff;
  box-sizing: border-box;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .item_tit_two .tit_one h3 {
  font-weight: normal;
  font-size: 38px;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .item_tit_two .tit_one .item_price {
  font-size: 26px;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .item_tit_two .tit_one .item_price span {
  font-size: 40px;
  font-weight: 700;
  background-image: -webkit-linear-gradient(top, #ffffff, #f2be73);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .item_tit_three {
  display: flex;
  color: #d6e7f9;
  font-size: 27px;
  width: 940px;
  box-sizing: border-box;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .item_tit_three .item {
  display: flex;
  width: 48%;
  box-sizing: border-box;
  padding: 0 5% 0 0;
  align-items: center;
  justify-content: space-between;
  margin-bottom: -20px;
  margin-top: 20px;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .item_tit_three .item .item_kq2 {
  display: inline-block;
  width: 21px;
  height: 21px;
  background: url("../img/dqhj/yellow.png") no-repeat;
  vertical-align: middle;
  background-size: 100%;
  margin-right: 10px;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .item_tit_three .item .item_kq1 {
  display: inline-block;
  width: 21px;
  height: 21px;
  background: url("../img/dqhj/blue.png") no-repeat;
  vertical-align: middle;
  background-size: 100%;
  margin-right: 10px;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .item_tit_three .item .item_compare1 {
  display: inline-block;
  width: 40px;
  height: 21px;
  background: url("../img/dqhj/yellowline.png") no-repeat;
  vertical-align: middle;
  background-size: 100%;
  margin-right: 10px;
}

.dqwr_app_box .dqwr_container .kqzlph .kqzlph_item .item_tit_three .item .item_compare2 {
  display: inline-block;
  width: 40px;
  height: 21px;
  background: url("../img/dqhj/blueline.png") no-repeat;
  vertical-align: middle;
  background-size: 100%;
  margin-right: 10px;
}

img {
  width: 100%;
  height: 100%;
  display: block;
}

.progressContainer {
  position: relative;
  height: 15px;
  width: 225px;
  background-color: #105e7c;
  border-radius: 2px;
}

.progressItem {
  position: absolute;
  height: 15px;
  width: 30%;
  background-color: #66ccab;
  border-radius: 2px;
}

.progressItemQd {
  position: absolute;
  height: 15px;
  width: 30%;
  background-color: #dccb69;
  border-radius: 2px;
}

.progressLabel {
  position: relative;
  top: 6px;
  display: block;
  width: 100%;
  color: #999999;
}

.progressLabel .left {
  position: absolute;
  left: 0px;
}

.progressLabel .right {
  position: absolute;
  right: 0px;
}

.btn-area {
  margin-top: 30px;
}

.aqitooltip {
  width: 125px;
  height: 78px;
  background: url("../img/dqhj/矩形1414拷贝.png");
  background-size: 100%;
  font-size: 29px;
}

.aqi_icon {
  display: inline-block;
  width: 19px;
  height: 27px;
  background: url("../img/dqhj/zengchang.png");
}

.aqi_icondown {
  display: inline-block;
  width: 19px;
  height: 27px;
  background: url("../img/dqhj/xiajiang.png");
}
