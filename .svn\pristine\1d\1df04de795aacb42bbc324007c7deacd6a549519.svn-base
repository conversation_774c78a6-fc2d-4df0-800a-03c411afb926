@charset "UTF-8";
.csgl_app_box {
  position: relative;
  box-sizing: border-box;
  width: 2045px;
  height: 1850px;
  background-image: url("../img/common/bg.png");
  background-size: 100% 100%;
  padding: 10px 55px 30px;
}

.csgl_app_box .csgl_header {
  width: 1935px;
  display: flex;
  height: 130px;
  align-items: center;
  justify-content: space-between;
  background: url("../img/common/一级标题3.png") no-repeat;
  background-position: 0 55px;
}

.csgl_app_box .csgl_header .title {
  font-size: 54px;
  background-image: -webkit-linear-gradient(top, #ffffff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  padding-bottom: 10px;
}

.csgl_app_box .csgl_header .title .title_icon_one {
  display: inline-block;
  margin-right: 10px;
  width: 78px;
  height: 75px;
  vertical-align: bottom;
  background-image: url("../img/common/一级标题1.png");
  background-size: 100% 100%;
}

.csgl_app_box .csgl_header .title_hr {
  position: relative;
  left: 30px;
  width: 50%;
  height: 21px;
  background-image: url("../img/common/一级标题2.png");
  background-size: 100% 100%;
}

.csgl_app_box .csgl_container .ajallfx {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.csgl_app_box .csgl_container .ajallfx .ajallfx_head {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.csgl_app_box .csgl_container .ajallfx .ajallfx_head h2 {
  font-size: 40px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.csgl_app_box .csgl_container .ajallfx .ajallfx_head i {
  display: inline-block;
}

.csgl_app_box .csgl_container .ajallfx .ajallfx_head .icon_left {
  width: 142px;
  height: 53px;
  background: url("../img/common/二级标题左.png") no-repeat center;
  background-size: 100% 100%;
  margin-right: 30px;
}

.csgl_app_box .csgl_container .ajallfx .ajallfx_head .icon_right {
  width: 142px;
  height: 53px;
  background: url("../img/common/二级标题右.png") no-repeat center;
  background-size: 100% 100%;
  margin-left: 30px;
}

.csgl_app_box .csgl_container .qsfx {
  padding-top: 15px;
  padding-bottom: 15px;
}

.csgl_app_box .csgl_container .qsfx .qsfx_head {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.csgl_app_box .csgl_container .qsfx .qsfx_head h2 {
  font-size: 40px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.csgl_app_box .csgl_container .qsfx .qsfx_head i {
  display: inline-block;
}

.csgl_app_box .csgl_container .qsfx .qsfx_head .icon_left {
  width: 142px;
  height: 53px;
  background: url("../img/common/二级标题左.png") no-repeat center;
  background-size: 100% 100%;
  margin-right: 30px;
}

.csgl_app_box .csgl_container .qsfx .qsfx_head .icon_right {
  width: 142px;
  height: 53px;
  background: url("../img/common/二级标题右.png") no-repeat center;
  background-size: 100% 100%;
  margin-left: 30px;
}

.csgl_app_box .csgl_container .cdjdyfx {
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: center;
}

.csgl_app_box .csgl_container .cdjdyfx .cdjdyfx_item {
  width: 50%;
  box-sizing: border-box;
  margin-top: 20px;
}

.cdjdyfxList {
  display: flex;
  flex-wrap: wrap;
  padding-top: 50px;
}
.cdjdyfxList .xisList {
  background-color: rgba(16, 38, 70);
  width: 49%;
  margin-bottom: 100px;
  margin-right: 1%;
}
.cdjdyfxList .xisList>h4 {
  background: rgba(38, 71, 107);
  height: 80px;
  font-size: 36px;
  line-height: 80px;
  color: #ffffff;
}
.cdjdyfxList .xisList>p {
  height: 100px;
  font-size: 36px;
  line-height: 100px;
  font-size: 40px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.cdjdyfx_item_titles {
  align-items: center;
  justify-content: space-around;
  margin-top: 20px;
}

.cdjdyfx_item_titles
  .point {
  color: #ffffff;
  font-size: 32px;
  white-space: nowrap;
}

  .cdjdyfx_item_titles
  .jdbackground {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 360px;
  height: 70px;
  background: url("../img/common/组2400.png");
  background-size: 100% 100%;
}


  .cdjdyfx_item_titles
  .jdbackground
  .normplant {
  font-size: 54px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  margin-top: -20px;
}


  .cdjdyfx_item_titles
  .jdbackground
  .normplant
  span {
  font-size: 28px;
}

.csgl_app_box .csgl_container .cdjdyfx .cdjdyfx_item .wtputopt_title {
  width: 940px;
  box-sizing: border-box;
  padding: 40px 25px 0;
  color: #ffffff;
  font-size: 28px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.csgl_app_box .csgl_container .cdjdyfx .zhcg_table {
  color: #ffffff;
  font-size: 28px;
  margin-top: 40px;
}

.csgl_app_box .csgl_container .cdjdyfx .zhcg_table i {
  display: inline-block;
}

.csgl_app_box .csgl_container .cdjdyfx .zhcg_table .online {
  color: #ffffff;
}

.csgl_app_box .csgl_container .cdjdyfx .zhcg_table .online i {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #ffffff;
  vertical-align: middle;
  margin-right: 10px;
}

.csgl_app_box .csgl_container .cdjdyfx .zhcg_table .outline {
  color: red;
}

.csgl_app_box .csgl_container .cdjdyfx .zhcg_table .outline i {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: red;
  vertical-align: middle;
  margin-right: 10px;
}

.csgl_app_box .csgl_container .cdjdyfx .zhcg_table .blue {
  height: 60px;
  background: #26476b;
}

.csgl_app_box .csgl_container .cdjdyfx .zhcg_table .black {
  height: 60px;
  background: #0c2948;
}

.csgl_app_box .csgl_container .cdjdyfx .zhcg_table tr {
  margin-bottom: 20px;
}

.csgl_app_box .csgl_container .cdjdyfx .zhcg_table .cell {
  font-weight: normal;
}

.csgl_app_box .csgl_container .cdjdyfx .zhcg_table th {
  width: 20%;
}

.csgl_app_box .csgl_container .cdjdyfx .zhcg_table .el-table__row td {
  width: 20%;
}

.csgl_app_box .csgl_container .cdjdyfx .zhcg_table .el-table__row .right_cell {
  width: 40%;
}

.el-table__body-wrapper::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  background-color: skyblue;
  background-image: -webkit-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #ededed;
  border-radius: 5px;
}
.qsfenxbac{
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 360px;
  height: 270px;
  background: url('../img/common/组1062076.png');
  background-size: 80% 48%;
  background-repeat: no-repeat;
  background-position: 50% 80%;
}

.zlContent p {
  width: 300px;
  color: #d6e7f9!important;
  font-size: 33px;
  padding: 20px;
}

.weekList {
  display: flex;
  padding: 0 70px;
}
.weekList .weekCont {
  width: 33%;
  display: flex;
}
.weekList .weekCont .contLis {
  
}
.weekList .weekCont .contLis h6{
  font-size: 28px;
  color: #d6e7f9!important;
  width: 180px;
}
.weekList .weekCont .contLis p{
  width: 180px;
  font-size: 40px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  padding: 0;
}

.botList {
  padding: 0 70px;
}

.botList ul li{
  display: flex; 
}
.botList ul li div {
  margin-bottom: 18px;
  margin-right: 18px;
  border-top: 6px solid #29608f;
  display: flex;
  width: 50%;
  background: rgba(14, 37, 66, 0.2);
}
.botList ul li div p:nth-child(2){
  width: 500px;
  font-size: 40px;
  line-height: 92px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  padding: 0;
}

.matter_cont {
  display: flex;
}
.matter_cont .matter_list {
  padding: 0 60px;
  line-height: 222px;
  display: flex;
  width: 800px;
  height: 222px;
  background: url('../img/szsh/supervise_left/1.png');
  background-size: 100% 80%;
  background-repeat: no-repeat;
  background-position: 100% 100%;
}
.matter_list p{
  width: 50%;
  font-size: 40px;
  color: #d6e7f9;
  text-align: center;
}
.matter_list p:nth-child(2) span{
  font-size: 60px;
  background-image: -webkit-linear-gradient(top, white, #ffbb45);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}
.matter_list p span {
  font-size: 60px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}
.taskTop {
  
}
.taskTop .taskTop_item {
  display: flex;
}
.taskTop_item li {
  display: flex;
  padding: 15px;
}
.taskTop_item li:nth-child(1) {
  width: 40%;
}
.taskTop_item li:nth-child(2) {
  width: 20%;
}
.taskTop_item li:nth-child(3) {
  width: 40%;
}
.taskTop_item li p{
  font-size: 40px;
  color: #d6e7f9;
  margin-bottom: 6px;
}
.titleBck {
  padding: 0 10px;
}
.titleBck i{
  margin-left: 5px;
  font-style:normal;
  font-size: 60px;
  text-align: center;
  color: #ffbc47;
  font-weight: bold;
  line-height: 107px;
  display: inline-block;
  width: 80px;
  height: 117px;
  background-image: url('../img/szsh/supervise_left/矩形6.png');
}
.item_title {
  padding: 0 10px;
}
.item_title h5 {
  font-size: 40px;
  color: #d6e7f9;
  margin-bottom: 6px;
}
.item_title h5 p{
  display: inline-block;
  font-size: 52px;
  background-image: -webkit-linear-gradient(top, white, #ffbb45);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}
.item_title b {
  display: block;
  font-size: 32px;
  color: #d6e7f9;
}
.item_title b span {
  font-size: 42px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.taskBottom {
  display: flex;
}
.bottomList {
  background-image: url('../img/szsh/supervise_left/组1062091.png');
  background-repeat: no-repeat;
}
.bottomList h4 {
  padding-right: 72px;
  background-image: url('../img/szsh/supervise_left/组2.png');
  background-position: 30% 68%;
  background-repeat: no-repeat;
  font-size: 40px;
  color: #d6e7f9;
  text-align: center;
}
.bottomList ul {
  padding: 10px 0;
}
.bottomList ul li{
  padding: 15px 52px;
}
.bottomList span {
  padding-left: 88px;
  font-size: 40px;
  background-image: -webkit-linear-gradient(top, white, #ffbb45);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}
.bottomList img{
  padding-bottom: 2px;
  padding-right: 10px;
}
.bottomList p{
  font-size: 32px;
  color: #d6e7f9;
  display: inline-block;
}
.autonomyC_cont {
  display: flex;
  flex-wrap: wrap;
}
.autonomyC_cont li {
  width: 50%;
  padding: 80px 0;
  display: flex;
}
.autonomyC_cont li div:nth-child(2) {
  padding: 0 10px;
}
.autonomyC_cont img {

}
.autonomyC_cont p {
  text-align: left;
  font-size: 40px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}
.autonomyC_cont p span {
  font-size: 28px;
}
.autonomyC_cont h4 {
  font-size: 32px;
  color: #d6e7f9;
}