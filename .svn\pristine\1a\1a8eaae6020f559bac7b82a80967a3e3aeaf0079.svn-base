<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>站点名称</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
    <link rel="stylesheet" href="/static/citybrain/ggfw/css/ggfw-dialog copy.css" />
    <link rel="stylesheet" href="/static/citybrain/ggfw/css/sdqfw-dialog.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <!-- 轮播toolTip -->
</head>

<body>
    <div id="app" class="container">
        <div class="head">
            <span>站点名称</span>
            <div class="img" @click="closeDialog"></div>
        </div>
        <div class="content">
            <div style="width: 100%; padding: 30px">
                <div class="TitleTop">
                    <img class="image" src="../img/right.png" alt="" />
                    <span>用量趋势统计</span>
                </div>
                <div id="lineEcharts002" style="width: 1350px; height: 350px"></div>

                <div class="TitleTop">
                    <img class="image" src="../img/right.png" alt="" />
                    <span>历史案件清单</span>
                </div>
                <div class="table">
                    <div class="thName">
                        <div v-for="item in thName">{{item}}</div>
                    </div>
                    <div>
                        <div class="tbody" v-for="item in tableList">
                            <div>{{item.sort}}</div>
                            <div>{{item.value}}</div>
                            <div>{{item.value1}}</div>
                            <div>{{item.value2}}</div>
                            <div>{{item.value3}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
<script type="module">
    new Vue({
        el: "#app",
        data: {
            thName: ["序号", "事件", "类别", "时间", "影响范围"],
            tableList: [],
            detailData:[],
            id:"",
        },
        //项目生命周期
        mounted() {
            this.init();
            let that=this;
            window.addEventListener("message", function (event) {
                 if (event.data.status == "openIframe") {
                    that.detailData = JSON.parse(event.data.data);
                    that.id=that.detailData.obj.id;
                }
            });
        },
        methods: {
            closeDialog() {
                let data = JSON.stringify({
                    type: "closeIframe",
                    name: "sdqfw-dialog",
                });
                window.parent.postMessage(data, "*");
            },
            init() {
                $api("sdqfw-dialog001").then((res) => {
                    this.getEcharts02("lineEcharts002", res);
                });
                $api("sdqfw-dialog002").then((res) => {
                    this.tableList = res;
                });
            },
            getEcharts02(dom, echartData) {
                let echarts1 = echarts.init(document.getElementById(dom));

                const xAxisData = echartData.map((item) => {
                    return item.time;
                });
                const yData = echartData.map((item) => {
                    return item.value;
                });

                let option = {
                    textStyle: {
                        fontFamily: "Din-Light",
                    },
                    legend: {
                        data: [
                            {
                                name: "累计用电量",
                                icon: "path://M512 139.81262864a286.42534744 286.42534744 0 1 0 286.42534744 286.42534744 286.42534744 286.42534744 0 0 0-286.42534744-286.42534744z m0 477.3755789a190.95023144 190.95023144 0 1 1 190.95023144-190.95023146 190.95023144 190.95023144 0 0 1-190.95023144 190.95023146z",
                            },
                        ],
                        selected: {
                            累计用电量: true,
                        },
                        itemWidth: 20,
                        itemHeight: 20,
                        itemGap: 30,
                        textStyle: {
                            color: "#fff",
                            lineHeight: 15,
                            fontSize: 30,
                        },
                        type: "scroll",
                    },
                    tooltip: {
                        backgroundColor: "#fff05",
                        trigger: "axis",
                        axisPointer: {
                            type: "none",
                        },
                        textStyle: {
                            color: "#ffff",
                            lineHeight: 28,
                            fontSize: 30,
                        },
                        confine: true,
                        padding: 12,
                    },
                    grid: {
                        // right: 0,
                        bottom: 100,
                    },
                    xAxis: {
                        type: "category",
                        boundaryGap: true,
                        offset: 5,
                        data: xAxisData,
                        axisLabel: {
                            interval: 0,
                            align: "left",
                            color: "#fff",
                            fontSize: 30,
                        },
                        axisLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },
                    },
                    yAxis: {
                        type: "value",
                        min: 0,
                        max: 1600,
                        interval: 400,
                        axisLabel: {
                            color: "#fff",
                            fontSize: 30,
                        },
                        splitLine: {
                            lineStyle: {
                                color: "#19365f",
                            },
                        },
                        axisLine: {
                            show: false,
                        },
                        axisTick: {
                            show: false,
                        },
                    },
                    series: [
                        {
                            name: "累计用电量",
                            data: yData,
                            type: "line",
                            smooth: true,
                            smoothMonotone: "x",
                            cursor: "pointer",
                            showSymbol: false,
                            lineStyle: {
                                shadowColor: "rgba(18,61,172,0.5)",
                                color: "#0398d1",
                                shadowBlur: 10,
                            },
                        },
                    ],
                };
                echarts1.setOption(option);
            },
        },
    });
</script>

</html>