<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title></title>
    <script src="/static/citybrain/csdn/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <script src="/elementui/js/index.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/elementui/css/index.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    <style>
        #xsqstjc-right {
            width: 1050px;
            height: 1930px;
            background: url("/img/left-bg.png") no-repeat;
            background-size: 100% 100%;
            /* display: flex;
            justify-content: space-evenly;
            flex-direction: column;
            align-items: center; */
        }

        .header-title2 .text {
            font-size: 30px !important;
        }

        .rightTop {
            width: 1050px;
            height: 800px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .question {
            width: 500px;
            height: 50px;
            background: url("../../../images/components/first-title.png") no-repeat;
            background-size: 100% 100%;
            text-align: center;
            padding-top: 4px;
            box-sizing: border-box;
            margin-top: 20px;
        }

        .question span {
            font-size: 30px;
            font-weight: 800;
            font-stretch: normal;
            letter-spacing: 3px;
            background: linear-gradient(to bottom, #f0ffff, #74b4f4, #83b8ff);
            -webkit-background-clip: text;
            color: transparent;
        }

        .check {
            margin-top: 40px;
            width: 990px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .checkBox {
            width: 990px;
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            border-top: solid 1px #4fc3ff;
            border-left: solid 1px #4fc3ff;
            border-bottom: solid 1px #4fc3ff;
            border-radius: 6px;
        }

        .checkAtive {
            background-color: #5dbcff !important;
            color: #ffffff !important;
        }

        .checkItem {
            /* width: fit-content; */
            width: 100%;
            height: 34px;
            background-color: #142c4c;
            text-align: center;
            color: #afbdcf;
            font-size: 25px;
            padding: 5px;
            border-right: solid 1px #4fc3ff;
            cursor: pointer;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }

        .el-tooltip__popper {
            width: fit-content;
            font-size: 25px;
        }

        .rightTop .itemContent {
            width: 990px;
            height: 450px;
            margin-top: 30px;
            /* background-color: #fff; */
            display: flex;
            flex-wrap: wrap;
            justify-content: space-evenly;
        }

        .rightTop .itemContent .itemdetail {
            width: 240px;
            height: 200px;
            text-align: center;
            background: url("../../shgl/img/itembg.png") no-repeat;
            background-size: 100% 100%;
        }

        .rightTop .itemContent .itemdetail .value {
            margin-top: 30px;
            font-size: 50px;
            background: linear-gradient(to bottom, #ccf4ff, #ffffff, #00baf8, #ffffff);
            -webkit-background-clip: text;
            color: transparent;
        }

        .rightTop .itemContent .itemdetail .name {
            margin-top: 10px;
            font-size: 30px;
            color: white;
        }

        .rightBottom {
            width: 1050px;
            height: 1120px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .piecharts {
            display: flex;
            margin-top: 30px;
            width: 100%;
            align-items: center;
        }

        .piecharts .pie1 {
            width: 400px;
            height: 400px;
            position: relative;
        }

        .piecharts .pie1 .pie1bg {
            width: 400px;
            height: 400px;
            background: url("../../../citybrain/csdn/img/cstz3/digital-society-left-bg4.png") no-repeat;
            background-size: 100% 100%;
            animation: rotation 8s linear infinite;
        }

        @keyframes rotation {
            0% {
                transform: rotate(0);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .piecharts .pie1 .pie1detail {
            position: absolute;
            top: 120px;
            left: 125px;
            width: 150px;
            height: 150px;
            text-align: center;
        }

        .piecharts .pie1 .name {
            margin-top: 15px;
            font-size: 40px;
            color: #fff;
        }

        .piecharts .pie1 .value {
            font-size: 50px;
            background-image: linear-gradient(rgb(255, 215, 155) 10%,
                    rgb(255, 255, 255) 60%,
                    rgb(255, 197, 89) 10%);
            -webkit-background-clip: text;
            color: transparent;
        }

        .piecharts .pie2 {
            width: 500px;
            height: 400px;
        }

        .monthSelector {
            width: 1000px;
            height: 50px;
            text-align: right;
        }

        .el-input__inner {
            font-size: 25px;
            width: 130px;
            height: 50px;
            line-height: 50px;
            color: #fff;
            background-color: #011040b3;
        }

        .el-select-dropdown__item.hover,
        .el-select-dropdown__item:hover {
            background-color: #011040b3;
        }

        .el-input__icon {
            line-height: 48px;
        }

        .el-select-dropdown {
            background-color: #011040b3;
        }

        .el-select-dropdown__item {
            font-size: 30px;
            color: #fff;
        }

        .el-select .el-input .el-select__caret {
            font-size: 30px;
        }

        #barChart {
            margin-top: 20px;
            width: 900px;
            height: 400px;
        }
    </style>
</head>

<body>
    <div id="xsqstjc-right">
        <div class="content">
            <div class="rightTop">
                <nav style="padding: 20px 45px 0 45px">
                    <s-header-title style="width: 100%" title="七张问题清单" htype="2"></s-header-title>
                </nav>
                <div class="question">
                    <span>问题数&nbsp;&nbsp;&nbsp;{{num}}个</span>
                </div>
                <div class="check">
                    <div class="checkBox">
                        <div class="checkItem" :class="{checkAtive:tabActive==0}" @click="clickTab(0)">
                            <el-tooltip content="问题总览" effect="light">
                                <span>问题总览</span>
                            </el-tooltip>
                        </div>
                        <div class="checkItem" :class="{checkAtive:tabActive==1}" @click="clickTab(1)">
                            <el-tooltip content="全省管控力指数排行" effect="light">
                                <span>全省管控力指数排行</span>
                            </el-tooltip>
                        </div>
                        <div class="checkItem" :class="{checkAtive:tabActive==2}" @click="clickTab(2)">
                            <el-tooltip content=" 区县问题管控力指数排名" effect="light">
                                <span>区县问题管控力指数排名</span>
                            </el-tooltip>
                        </div>
                        <div class="checkItem" :class="{checkAtive:tabActive==3}" @click="clickTab(3)">
                            <el-tooltip content="部门量化评价" effect="light">
                                <span>部门量化评价</span>
                            </el-tooltip>
                        </div>
                        <div class="checkItem" :class="{checkAtive:tabActive==4}" @click="clickTab(4)">
                            <el-tooltip content="重点关注" effect="light">
                                <span>重点关注</span>
                            </el-tooltip>
                        </div>
                        <div class="checkItem" :class="{checkAtive:tabActive==5}" @click="clickTab(5)">
                            <el-tooltip content="重点评价" effect="light">
                                <span>重点评价</span>
                            </el-tooltip>
                        </div>
                    </div>
                </div>
                <div class="itemContent">
                    <div class="itemdetail" v-for="(item,index) in rightTopData">
                        <div class="value">{{item.value}}</div>
                        <div class="name">{{item.name}}</div>
                    </div>
                </div>
            </div>
            <div class="rightBottom">
                <nav style="padding: 20px 45px 0 45px">
                    <s-header-title style="width: 100%" title="新闻宣传与舆论分析" htype="2"></s-header-title>
                </nav>
                <div class="question"><span>问题数&nbsp;&nbsp;&nbsp;12个</span></div>
                <div class="piecharts">
                    <div class="pie1">
                        <div class="pie1bg"></div>
                        <div class="pie1detail">
                            <div class="name">指数</div>
                            <div class="value">{{indexValue}}</div>
                        </div>
                    </div>
                    <div style="width: 50px"></div>
                    <div style="flex: 1" class="pie2" id="piechart"></div>
                </div>
                <div class="monthSelector">
                    <el-select v-model="month" placeholder="请选择" @change="change">
                        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
                <div class="bar" id="barChart"></div>
            </div>
        </div>
    </div>
</body>

</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#xsqstjc-right",
        data: {
            tabActive: 0,
            rightTopData: "",
            indexValue: 0,
            month: 4,
            num: 0,
            options: [
                {
                    label: "1月",
                    value: 1,
                },
                {
                    label: "2月",
                    value: 2,
                },
                {
                    label: "3月",
                    value: 3,
                },
                {
                    label: "4月",
                    value: 4,
                },
            ],
        },
        mounted() {
            $api("ldst_szhgg_dzjgztzz_right", { type: 1 }).then((res) => {
                this.rightTopData = res;
                this.num =
                    Number(res[0].value) +
                    Number(res[1].value) +
                    Number(res[2].value) +
                    Number(res[3].value) +
                    Number(res[4].value) +
                    Number(res[5].value) +
                    Number(res[6].value);
            });
            $api("ldst_szhgg_dzjgztzz_right", { type: 2 }).then((res) => {
                this.indexValue = res[0].value;
            });
            this.initPieChart();

            $api("ldst_szhgg_dzjgztzz_right", { type: "4-3" }).then((res) => {
                // this.arrSortByKey(res, 'value1', false)
                this.initBarChart(res);
            });
        },
        methods: {
            /*
        array: 需要进行排序的数组
        key: 根据某个属性进行排序
        order: 升序/降序  true：升序 false:降序
    */
            arrSortByKey(array, key, order) {
                return array.sort((a, b) => {
                    console.log(a);
                    let value1 = a[key],
                        value2 = b[key];
                    if (order) {
                        //升序
                        return value1 - value2;
                    } else {
                        //降序
                        return value2 - value1;
                    }
                });
            },
            change(item) {
                if (item === 1) {
                    $api("ldst_szhgg_dzjgztzz_right", { type: 4 }).then((res) => {
                        this.arrSortByKey(res, "value1", false);
                        this.initBarChart(res);
                    });
                } else if (item === 2) {
                    $api("ldst_szhgg_dzjgztzz_right", { type: "4-1" }).then((res) => {
                        this.arrSortByKey(res, "value1", false);
                        this.initBarChart(res);
                    });
                } else if (item === 3) {
                    $api("ldst_szhgg_dzjgztzz_right", { type: "4-2" }).then((res) => {
                        this.arrSortByKey(res, "value1", false);
                        this.initBarChart(res);
                    });
                } else if (item === 4) {
                    $api("ldst_szhgg_dzjgztzz_right", { type: "4-3" }).then((res) => {
                        this.arrSortByKey(res, "value1", false);
                        this.initBarChart(res);
                    });
                } else if (item === 5) {
                    $api("ldst_szhgg_dzjgztzz_right", { type: "4-4" }).then((res) => {
                        this.arrSortByKey(res, "value1", false);
                        this.initBarChart(res);
                    });
                }
            },
            clickTab(i) {
                this.tabActive = i;
                if (i == 0) {
                    $api("ldst_szhgg_dzjgztzz_right", { type: 1 }).then((res) => {
                        this.rightTopData = res;
                        this.num =
                            Number(res[0].value) +
                            Number(res[1].value) +
                            Number(res[2].value) +
                            Number(res[3].value) +
                            Number(res[4].value) +
                            Number(res[5].value) +
                            Number(res[6].value);
                    });
                } else if (i == 1) {
                    $api("ldst_szhgg_dzjgztzz_right", { type: "1-1" }).then((res) => {
                        this.rightTopData = res;
                        this.num =
                            Number(res[0].value) +
                            Number(res[1].value) +
                            Number(res[2].value) +
                            Number(res[3].value) +
                            Number(res[4].value) +
                            Number(res[5].value) +
                            Number(res[6].value);
                    });
                } else if (i == 2) {
                    $api("ldst_szhgg_dzjgztzz_right", { type: "1-2" }).then((res) => {
                        this.rightTopData = res;
                        this.num =
                            Number(res[0].value) +
                            Number(res[1].value) +
                            Number(res[2].value) +
                            Number(res[3].value) +
                            Number(res[4].value) +
                            Number(res[5].value) +
                            Number(res[6].value);
                    });
                } else if (i == 3) {
                    $api("ldst_szhgg_dzjgztzz_right", { type: "1-3" }).then((res) => {
                        this.rightTopData = res;
                        this.num =
                            Number(res[0].value) +
                            Number(res[1].value) +
                            Number(res[2].value) +
                            Number(res[3].value) +
                            Number(res[4].value) +
                            Number(res[5].value) +
                            Number(res[6].value);
                    });
                } else if (i == 4) {
                    $api("ldst_szhgg_dzjgztzz_right", { type: "1-4" }).then((res) => {
                        this.rightTopData = res;
                        this.num =
                            Number(res[0].value) +
                            Number(res[1].value) +
                            Number(res[2].value) +
                            Number(res[3].value) +
                            Number(res[4].value) +
                            Number(res[5].value) +
                            Number(res[6].value);
                    });
                } else if (i == 5) {
                    $api("ldst_szhgg_dzjgztzz_right", { type: "1-5" }).then((res) => {
                        this.rightTopData = res;
                        this.num =
                            Number(res[0].value) +
                            Number(res[1].value) +
                            Number(res[2].value) +
                            Number(res[3].value) +
                            Number(res[4].value) +
                            Number(res[5].value) +
                            Number(res[6].value);
                    });
                }
            },
            initPieChart() {
                let myChart = echarts.init(document.getElementById("piechart"));
                $api("ldst_szhgg_dzjgztzz_right", { type: 3 }).then((res) => {
                    let data = res;
                    let option = {
                        tooltip: {
                            trigger: "item",
                            // formatter: '{b}: <br/> {d}%',
                            formatter: "{b}: <br/> {c}个<br/> {d}%",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: 30,
                            },
                        },
                        series: [
                            {
                                type: "pie",
                                radius: "65%",
                                color: ["#5087EC", "#58A55C", "#68BBC4"],
                                labelLine: {
                                    show: true,
                                },
                                label: {
                                    color: "#fff",
                                    fontSize: 30,
                                },
                                itemStyle: {
                                    borderRadius: 5,
                                },
                                data: data,
                            },
                        ],
                    };
                    myChart.setOption(option);
                });
            },
            initBarChart(res) {
                let myChart = echarts.init(document.getElementById("barChart"));

                let data = res.slice(0, 3);

                let color1 = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                        offset: 0,
                        color: "#00C0FF",
                    },
                    {
                        offset: 1,
                        color: "rgba(0,192,255,0)",
                    },
                ]);
                let color2 = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                        offset: 0,
                        color: "#2DF09F",
                    },
                    {
                        offset: 1,
                        color: "rgba(0,192,255,0)",
                    },
                ]);
                let option = {
                    color: [color1, color2],
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                    },
                    legend: {
                        textStyle: {
                            color: "#fff",
                            fontSize: 20,
                        },
                        itemGap: 30,
                    },
                    grid: {
                        top: 45,
                        left: 40,
                        right: 0,
                        bottom: 10,
                        containLabel: true,
                    },
                    itemStyle: {
                        borderRadius: 5,
                    },
                    xAxis: [
                        {
                            type: "category",
                            data: data.map((item) => item.name),
                            axisLabel: {
                                color: "#fff",
                                fontSize: 30,
                            },
                            axisPointer: {
                                type: "shadow",
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: "涉金新闻舆情：个",
                            type: "value",
                            nameTextStyle: {
                                fontSize: 22,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 22,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                        {
                            name: "",
                            type: "value",
                            // max: 100,
                            nameTextStyle: {
                                fontSize: 22,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                // formatter: "{value}%",
                                textStyle: {
                                    fontSize: 22,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "舆情概况",
                            type: "bar",
                            data: data.map((item) => item.value1),
                            barGap: "20%",
                            // barCategoryGap: 45
                            barWidth: 18,
                        },
                        {
                            name: "预警",
                            type: "bar",
                            data: data.map((item) => item.value2),
                            barWidth: 18,
                        },
                        {
                            name: "媒体关注",
                            type: "line", // 直线ss
                            yAxisIndex: 1,
                            smooth: false,
                            symbolSize: 10,
                            itemStyle: {
                                normal: {
                                    color: "#ffc460",
                                },
                            },
                            data: data.map((item) => item.value),
                        },
                    ],
                };
                myChart.setOption(option);
            },
        },
    });
</script>