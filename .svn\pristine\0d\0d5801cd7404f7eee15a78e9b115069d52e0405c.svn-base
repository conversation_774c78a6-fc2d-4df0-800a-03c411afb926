<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>统计分析</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css">
    <link rel="stylesheet" href="/static/citybrain/ggfw/css/tjfx.css">
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script> <!-- 轮播toolTip -->
    <script src="/static/js/jslib/echarts-wordcloud.min.js"></script>
</head>
<style>
    .el-button {
        position: absolute;
        top: 27px;
        right: 25px;
        background: linear-gradient(to bottom,
                rgba(0, 89, 147, 0.9),
                rgba(0, 32, 52, 0.9));
        color: #ffffff;
        width: 55px;
        height: 283px;
        border: #359cf8 1xp solid;
        font-size: 30px;
        white-space: inherit;
        padding: 5px;
    }
</style>

<body>
    <div id="app" class="container" v-cloak>
        <nav>
            <s-header-title-2 htype="1" :click-flag="true" @click="openDiaog" title="城乡就业">
                </s-header-title>
        </nav>
        <div class="cxjy">
            <div class="csjy-left">
                <div class="csjy-left-top">
                    <div class="csjy-left-item" v-for="(item,index) in cxjyList" :key="index">
                        <img src="/static/citybrain/ggfw/img/list.png" alt="">
                        <span class="s-c-white s-font-30">{{item.name}}</span><br>
                        <span class="s-c-blue-gradient s-font-35 s-w7" style="margin-left:25px">{{item.value}}</span>
                        <span class="s-c-blue-gradient s-font-30">{{item.unit}}</span>
                    </div>
                </div>
                <div class="select">
                    <el-select v-model="value" placeholder="区域">
                        <el-option v-for="item in options0" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                    <el-select v-model="value1" placeholder="行业">
                        <el-option label="渔业" value="1"></el-option>
                        <el-option label="林业" value="2"></el-option>
                        <el-option label="农业" value="3"></el-option>
                        <el-option label="工业" value="4"></el-option>

                    </el-select>
                    <el-select v-model="value2" placeholder="时间">
                        <el-option label="2022-10" value="1" key="1">
                        </el-option>
                    </el-select>
                    <div class="jyzjzc-tab" style="position: absolute;right: 0px;">
                        <li class="djsy-item" v-for="(item,index) in tabList" :key="index"
                            :class="tabIndex===index?'active1':''" @click="changeTab(index)">
                            {{item.name}}
                        </li>
                    </div>
                </div>
                <div id="chart01" style="width:100%;height:350px;margin-top:30px;"></div>
            </div>
            <div class="csjy-right" style="position: relative;">
                <el-button @click="openTableDiaog" style="font-size: 24px;line-height: 32px;">就业保障资金投入</el-button>
                <div class="csjy-right-item">
                    <dv-border-box-12>
                        <nav>
                            <s-header-title-3 title="城镇长期失业人员" />
                        </nav>
                        <div id="chart02" style="width:100%;height:230px;"></div>
                    </dv-border-box-12>
                </div>
                <div class="csjy-right-item">
                    <dv-border-box-12>
                        <nav>
                            <s-header-title-3 title="失地农民就业问题分析" />
                        </nav>
                        <div id="chart03" style="width:100%;height:230px;"></div>
                    </dv-border-box-12>
                </div>
            </div>
        </div>
        <div class="middle">
            <div class="middle-left">
                <nav>
                    <s-header-title-2 htype="0" title="社保分析">
                        </s-header-title>
                </nav>
                <div>
                    <div class="table table1">
                        <div class="th">
                            <div class="th_td" v-for="(item,index) in theadList" :key="index">
                                {{item}}
                            </div>
                        </div>
                        <div class="tbody" id="tbody" @mouseover="mouseenterEvent()" @mouseleave="mouseleaveEvent()">
                            <div class="tr" v-for="(item ,i) in tbodyList" :key="i">
                                <div class="tr_td" style="flex: 0.25">{{item.xz}}</div>
                                <div class="tr_td" style="flex: 0.25">{{item.se}}</div>
                                <div class="tr_td" style="flex: 0.25">{{item.zz}}</div>
                                <div class="tr_td" style="flex: 0.25;">{{item.zc}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="middle-right">
                <nav>
                    <s-header-title-2 htype="0" title="社保缴纳">
                        </s-header-title>
                </nav>
                <div class="sbjn">
                    <div class="sbjn-title">
                        <span class="s-font-35 s-c-blue-gradient s-w7">全市城乡社保</span>
                    </div>
                    <div class="sbjn-con">
                        <div class="sbjn-item" v-for="(item,index) in sbjnList" :key="index">
                            <p class="s-c-white s-font-30">{{item.name}}</p>
                            <div>
                                <dv-border-box-8 style="padding-top: 10px;">
                                    <span class="s-c-blue-gradient s-font-40 s-w7">{{item.value}}</span>
                                    <span class="s-c-blue-gradient s-font-30">{{item.unit}}</span>
                                </dv-border-box-8>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="bottom">
            <div class="bottom-left">
                <nav>
                    <s-header-title-2 htype="0" title="劳动合同">
                        </s-header-title>
                </nav>
                <div id="chart04" style="width:100%;height:calc(100% - 73px)"></div>
            </div>
            <div class="bottom-right">
                <nav>
                    <s-header-title-2 htype="0" title="人社统计">
                        </s-header-title>
                </nav>
                <div class="jyzjzc-tab" style="position: absolute;right: 0px;">
                    <li class="djsy-item" v-for="(item,index) in tabList1" :key="index"
                        :class="tabIndex1===index?'active1':''" @click="changeTab1(index)">
                        {{item.name}}
                    </li>
                </div>
                <div id="chart05" style="width:100%;height:400px;margin-top:30px;"></div>
            </div>
        </div>
    </div>
</body>
<script type="module">

    new Vue({
        el: '#app',
        data() {
            return {
                cxjyList: [],
                value: '',
                value1: '',
                value2: '',
                options0: [
                    {
                        value: '1',
                        label: '金东区',
                    },
                    {
                        value: '2',
                        label: '兰溪市',
                    },
                    {
                        value: '3',
                        label: '婺城区',
                    },
                    {
                        value: '4',
                        label: '武义县',
                    },
                    {
                        value: '5',
                        label: '浦江县',
                    },
                    {
                        value: '6',
                        label: '磐安县',
                    },
                    {
                        value: '7',
                        label: '义乌市',
                    },
                    {
                        value: '8',
                        label: '东阳市',
                    },
                    {
                        value: '9',
                        label: '永康市',
                    },
                ],
                tabIndex: 0,
                tabIndex1: 0,
                tabList: [
                    {
                        name: '近六月',
                        type: "1"
                    },
                    {
                        name: '近四季度',
                        type: "2"
                    },
                    {
                        name: '近五年',
                        type: "3"
                    }
                ],
                tabList1: [
                    {
                        name: '近六月',
                        type: "1"
                    },
                    {
                        name: '近四季度',
                        type: "2"
                    },
                    {
                        name: '近五年',
                        type: "3"
                    }
                ],
                time: null,
                dom: null,
                theadList: ['险种', '数额', '增长', '支出'],
                tbodyList: [],
                sbjnList: [],
            }
        },
        //项目生命周期
        mounted() {
            this.init()
            this.scroll()
        },
        methods: {
            init() {
                $api("ggfw_rsfw_tjfx01").then((res) => {
                    this.cxjyList = res
                });
                $api("ggfw_rsfw_tjfx02").then((res) => {
                    this.getChart01('chart01', res)
                });
                $api("ggfw_rsfw_tjfx03_1").then((res) => {
                    this.getChart02('chart02', res)
                });
                $api("ggfw_rsfw_tjfx03_2").then((res) => {
                    this.getChart02('chart03', res)
                });
                $api("ggfw_rsfw_tjfx04").then((res) => {
                    this.tbodyList = res
                });
                $api("ggfw_rsfw_tjfx05").then((res) => {
                    this.sbjnList = res
                });
                $api("ggfw_rsfw_tjfx06").then((res) => {
                    this.getChart03('chart04', res)
                });

                $api("ggfw_rsfw_tjfx07").then((res) => {
                    this.getChart04('chart05', res)
                });
            },
            openDiaog() {
                let diaog = {
                    type: 'openIframe',
                    name: 'tjfx-dialog',
                    src: baseURL.url + '/static/citybrain/ggfw/commont/tjfx-dialog.html',
                    left: "calc(50% - 515px)",
                    top: "30%",
                    width: "1032px",
                    height: "935px",
                    zIndex: "10",
                    argument: {
                        status: ""
                    }
                }
                top.window.parent.postMessage(JSON.stringify(diaog), '*')
            },
            openTableDiaog() {
                let diaog = {
                    type: 'openIframe',
                    name: 'tjfx-table-dialog',
                    src: baseURL.url + '/static/citybrain/ggfw/commont/tjfx-table-dialog.html',
                    left: "calc(50% - 500px)",
                    top: "30%",
                    width: "1515px",
                    height: "575px",
                    zIndex: "1000",
                    argument: {
                        status: ""
                    }
                }
                top.window.parent.postMessage(JSON.stringify(diaog), '*')
            },
            changeTab(index) {
                this.tabIndex = index
            },
            changeTab1(index) {
                this.tabIndex1 = index
            },

            scroll() {
                this.dom = document.getElementById('tbody')
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },
            mouseenterEvent() {
                clearInterval(this.time)
            },
            mouseleaveEvent() {
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },

            getChart01(id, chartData) {
                const myCharts = echarts.init(document.getElementById(id))
                let x = chartData.map((item) => {
                    return item.name;
                })
                let y1 = chartData.map((item) => {
                    return item.value1;
                })
                let y2 = chartData.map((item) => {
                    return item.value2;
                })
                let option = {
                    grid: {
                        left: "5%",
                        right: "10%",
                        top: "20%",
                        bottom: "0%",
                        containLabel: true,
                    },
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                    },
                    legend: {
                        show: true,
                        x: "center",
                        y: "15",
                        itemWidth: 10,
                        itemHeight: 10,
                        textStyle: {
                            color: "#fff",
                            fontSize: "24",
                        },
                    },
                    xAxis: [
                        {
                            type: "category",
                            // offset: 20,
                            axisLine: {
                                //坐标轴轴线相关设置。数学上的x轴
                                show: true,
                                lineStyle: {
                                    color: "rgba(108, 166, 219, 0.3)",
                                },
                            },
                            axisLabel: {
                                //坐标轴刻度标签的相关设置
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: "#192a44",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            data: x,
                        },
                    ],
                    yAxis: [
                        {
                            name: "单位：件",
                            nameTextStyle: {
                                fontSize: 28,
                                color: "#D6E7F9",
                                padding: [0, 20, 10, 0],
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 28,
                                    color: "#D6E7F9",
                                },
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: "#233653",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "就业人数",
                            type: "line",
                            smooth: true,
                            symbolSize: 4,
                            itemStyle: {
                                normal: {
                                    color: "#5087EC",
                                    lineStyle: {
                                        color: "#5087EC",
                                    },
                                },
                            },
                            data: y1,
                        }, {
                            name: "失业人数",
                            type: "line",
                            smooth: true,
                            symbolSize: 4,
                            itemStyle: {
                                normal: {
                                    color: "#00ca95",
                                    lineStyle: {
                                        color: "#00ca95",
                                    },
                                },
                            },
                            data: y2,
                        }
                    ],
                };
                myCharts.setOption(option);
                myCharts.getZr().on('mousemove', param => {
                    myCharts.getZr().setCursorStyle('default')
                })
            },

            getChart02(id, data) {
                let echarts0 = echarts.init(document.getElementById(id))
                let mockData = data
                // 随机颜色
                let randcolor = () => {
                    let r = 100 + (Math.random() * 100);
                    let g = 135 + (Math.random() * 100);
                    let b = 100 + (Math.random() * 100);
                    return `rgb(${r}, ${g}, ${b})`;
                },
                    option = {
                        tooltip: {
                            trigger: "item",
                            padding: [10, 15],
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                fontSize: 20,
                                color: 'white'
                            },
                            formatter: (params) => {
                                const { name, value } = params;
                                return `平台：${name} <br/>数量：${value}`;
                            },
                        },
                        series: [
                            {
                                type: "wordCloud",
                                gridSize: 20,
                                sizeRange: [12, 50],
                                rotationRange: [0, 0],
                                shape: "circle",
                                textStyle: {
                                    color: (params) => {
                                        return randcolor();
                                    },
                                    emphasis: {
                                        shadowBlur: 10,
                                        shadowColor: "#333",
                                    },
                                },
                                data: mockData,
                            },
                        ],
                    };
                echarts0.setOption(option)
            },

            getChart03(id, data) {
                const myChartsDivine = echarts.init(document.getElementById(id))
                let x = data.map((item) => {
                    return item.name;
                })
                let y = data.map((item) => {
                    return item.value;
                })
                let index = data.map((item) => {
                    return item.rate;
                })
                let option = {
                    tooltip: {
                        trigger: "item",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "30",
                        },
                    },
                    grid: {
                        left: "10%",
                        top: "18%",
                        right: "10%",
                        bottom: "10%",
                    },
                    legend: {
                        data: ["预测客流量", "预测增长数"],
                        top: "30",
                        textStyle: {
                            color: "#fff",
                            fontSize: 28,
                        },
                    },
                    xAxis: [
                        {
                            type: "category",
                            // offset: 20,
                            axisLine: {
                                //坐标轴轴线相关设置。数学上的x轴
                                show: true,
                                lineStyle: {
                                    color: "rgba(108, 166, 219, 0.3)",
                                },
                            },
                            axisLabel: {
                                //坐标轴刻度标签的相关设置
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: "#192a44",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            data: x,
                        },
                    ],
                    yAxis: [
                        {
                            type: "value",
                            name: "单位:个",
                            nameTextStyle: {
                                fontSize: 28,
                                color: "#D6E7F9",
                                // padding: [0, 20, 10, 0],
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 28,
                                    color: "#D6E7F9",
                                },
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: "#233653",
                                },
                            },
                        },
                        {
                            type: "value",
                            name: "单位:万元",
                            nameTextStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                            position: "right",
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: "#fff",
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                show: true,
                                formatter: "{value} ", //右侧Y轴文字显示
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "劳动合同纠纷数量",
                            type: "bar",
                            barWidth: 30,
                            color: '#5087EC',
                            itemStyle: {
                                normal: {
                                    color: '#5087EC'
                                },
                            },
                            data: y,
                        },

                        {
                            name: "涉及金额",
                            type: "line",
                            yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                            showAllSymbol: true, //显示所有图形。
                            // symbol: "circle", //标记的图形为实心圆
                            // symbolSize: 4, //标记的大小
                            smooth: true,
                            itemStyle: {
                                normal: {
                                    color: "#26D9FF",
                                    lineStyle: {
                                        color: "#26D9FF",
                                        // width: 1,
                                    },
                                },
                            },
                            data: index,
                        },
                    ],
                };
                myChartsDivine.setOption(option);
            },

            getChart04(id, chartData) {
                const myCharts = echarts.init(document.getElementById(id))
                let x = chartData.map((item) => {
                    return item.name;
                })
                let y1 = chartData.map((item) => {
                    return item.value1;
                })
                let y2 = chartData.map((item) => {
                    return item.value2;
                })
                let y3 = chartData.map((item) => {
                    return item.value3;
                })
                let y4 = chartData.map((item) => {
                    return item.value4;
                })
                let y5 = chartData.map((item) => {
                    return item.value5;
                })
                let option = {
                    grid: {
                        left: "5%",
                        right: "10%",
                        top: "30%",
                        bottom: "0%",
                        containLabel: true,
                    },
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                    },
                    legend: {
                        show: true,
                        left: "20%",
                        y: "10",
                        right: "10%",
                        itemGap: 20,
                        itemWidth: 16,
                        itemHeight: 16,
                        textStyle: {
                            color: "#fff",
                            fontSize: "24",
                        },
                    },
                    xAxis: [
                        {
                            type: "category",
                            // offset: 20,
                            axisLine: {
                                //坐标轴轴线相关设置。数学上的x轴
                                show: true,
                                lineStyle: {
                                    color: "rgba(108, 166, 219, 0.3)",
                                },
                            },
                            axisLabel: {
                                //坐标轴刻度标签的相关设置
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: "#192a44",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            data: x,
                        },
                    ],
                    yAxis: [
                        {
                            name: "单位：件",
                            nameTextStyle: {
                                fontSize: 28,
                                color: "#D6E7F9",
                                // padding: [0, 20, 10, 0],
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 28,
                                    color: "#D6E7F9",
                                },
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: "#233653",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "就业人数",
                            type: "line",
                            smooth: true,
                            symbolSize: 4,
                            itemStyle: {
                                normal: {
                                    color: "#5087EC",
                                    lineStyle: {
                                        color: "#5087EC",
                                    },
                                },
                            },
                            data: y1,
                        }, {
                            name: "社保参保人数",
                            type: "line",
                            smooth: true,
                            symbolSize: 4,
                            itemStyle: {
                                normal: {
                                    color: "#24d718",
                                    lineStyle: {
                                        color: "#24d718",
                                    },
                                },
                            },
                            data: y2,
                        }, {
                            name: "医保参保人数",
                            type: "line",
                            smooth: true,
                            symbolSize: 4,
                            itemStyle: {
                                normal: {
                                    color: "#fff700",
                                    lineStyle: {
                                        color: "#fff700",
                                    },
                                },
                            },
                            data: y3,
                        }, {
                            name: "人才储备人数",
                            type: "line",
                            smooth: true,
                            symbolSize: 4,
                            itemStyle: {
                                normal: {
                                    color: "#1853d7",
                                    lineStyle: {
                                        color: "#1853d7",
                                    },
                                },
                            },
                            data: y4,
                        }, {
                            name: "劳动保障人数",
                            type: "line",
                            smooth: true,
                            symbolSize: 4,
                            itemStyle: {
                                normal: {
                                    color: "#18d7b4",
                                    lineStyle: {
                                        color: "#18d7b4",
                                    },
                                },
                            },
                            data: y5,
                        }
                    ],
                };
                myCharts.setOption(option);
                myCharts.getZr().on('mousemove', param => {
                    myCharts.getZr().setCursorStyle('default')
                })
            },

        },


    })


</script>

</html>