<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts.js"></script>
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <link rel="stylesheet" href="/static/css/animate_dn.css" />
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>

    <title>right</title>
  </head>
  <body>
    <div id="szwhRight">
      <div class="content">
        <div class="title">
          <nav style="padding: 20px 45px">
            <s-header-title
              title="文化服务"
              data-time="2022年7月22日"
              htype="1"
            ></s-header-title>
          </nav>
        </div>
        <div class="content_line content_line1">
          <div
            style="
              display: flex;
              justify-content: space-evenly;
              align-items: center;
              margin-top: 30px;
            "
          >
            <div class="content_line_LeftRight">
              <div class="info">
                <s-header-title2 title="文化产业增加值"></s-header-title2>

                <div id="charts1" style="width: 931px; height: 365px"></div>
              </div>
            </div>
            <div class="content_line_LeftRight">
              <div class="info">
                <s-header-title2 title="文化发展指数"></s-header-title2>

                <div id="charts2" style="width: 931px; height: 365px"></div>
              </div>
            </div>
          </div>
        </div>
        <s-header-title2 title="热门场馆排行" htype="1"></s-header-title2>

        <div class="content_line content_line2">
          <div
            style="
              display: flex;
              justify-content: space-evenly;
              align-items: center;
              margin-top: -80px;
            "
          >
            <div class="line_2_left">
              <div
                class="big_item"
                style="display: flex"
                v-for="(item,i) in Venue"
                :key="i"
              >
                <div class="wbg-class">
                  <img
                    style="
                      width: 150px;
                      height: 120px;
                      margin-top: 93px;
                      margin-right: 30px;
                    "
                    :src="item.pic"
                    alt=""
                  />
                  <div class="item">
                    <div class="name" style="font-size: 36px; margin: 0">
                      {{item.ymbq}}
                    </div>
                    <div class="number Gold">
                      {{item.sysValue}}
                      <span class="unit">{{item.sysUnit}}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="line_2_right">
              <!-- <p style="margin:100px 0 10px 50px;color: #ccc;font-size: 34px;"><i class="sanicon"></i>热门场馆排行</i></p> -->
              <div
                id="line_2_right_charts"
                style="width: 100%; height: 600px"
              ></div>
            </div>
          </div>
        </div>
        <s-header-title2 title="图书馆借阅情况" htype="1"></s-header-title2>

        <div class="content_line">
          <div class="bottomBox">
            <div
              class="bottom-item"
              v-for="(item,i) in library"
              :key="i"
              style="display: flex; margin: 30px 0"
            >
              <img
                :src="item.pic"
                style="margin-right: 40px; width: 148px"
                alt=""
              />
              <div
                class="item"
                style="width: 354px; height: 122px; text-align: center"
              >
                <div class="name" style="font-size: 36px; margin: 0">
                  {{item.name}}
                </div>
                <div class="number Gold">
                  {{item.num}}
                  <span class="unit">{{item.unit}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      new Vue({
        el: "#szwhRight",
        data: {
          Venue: [],

          library: [
            {
              name: "图书馆",
              num: 10,
              pic: "/static/citybrain/djtl/img/szwh/tsg.png",
              unit: "家",
            },
            {
              name: "总藏量",
              num: 132.8,
              pic: "/static/citybrain/djtl/img/szwh/jsz.png",
              unit: "万册",
            },
            {
              name: "电子图书藏量",
              num: 52.5,
              pic: "/static/citybrain/djtl/img/szwh/jsr.png",
              unit: "万册",
            },
            {
              name: "有效借书证",
              num: 14.6,
              pic: "/static/citybrain/djtl/img/szwh/jsz.png",
              unit: "万张",
            },
            {
              name: "总流通",
              num: 754.82,
              pic: "/static/citybrain/djtl/img/szwh/jsr.png",
              unit: "万人次",
            },
            {
              name: "书刊文献外借",
              num: 2520.3,
              pic: "/static/citybrain/djtl/img/szwh/jsr.png",
              unit: "万册",
            },
          ],
        },
        created() {},
        mounted() {
          this.init();
        },
        methods: {
          init() {
            let that = this;
            $api("yxzl_szwh_right001").then((res) => {
              that.initEcharts1(res);
            });
            $api("yxzl_szwh_right002").then((res) => {
              that.initEcharts2(res);
            });
            $api("yxzl_szwh_right004").then((res) => {
              that.setEcharts3("line_2_right_charts", res);
            });
            $api("yxzl_szwh_left001",{code:3}).then((res) => {
              that.Venue=[]
              res.map(item=>{
                let url=item.ymbq.indexOf("文化馆")>-1?'whcg':item.ymbq.indexOf("文保单位")>-1?'wbdw':"bwg"
                let str={
                  ymbq: item.ymbq,
                  pic: `/static/citybrain/djtl/img/szwh/${url}.png`,
                  sysUnit: item.unit,
                  sysValue: item.VALUE
                }
                if(item.ymbq.indexOf("图书馆")==-1){
                  that.Venue.push(str)
                }
              })
            });
            $api("yxzl_szwh_right005").then((res) => {
              this.library=res.map(item=>{
                let url=item.ymbq.indexOf("总藏量")>-1?'jsz':
                item.ymbq.indexOf("图书馆")>-1?'tsg':
                item.ymbq.indexOf("借书证")>-1?'jsz':"jsr"
                let str={
                  name:item.ymbq,
                  num:item.VALUE,
                  unit:item.unit,
                  pic:`/static/citybrain/djtl/img/szwh/${url}.png`
                }
                return str
              })
            });
          },
          initEcharts1(data) {
            let myChart = echarts.init(document.getElementById("charts1"));
            let xData = [];
            let textDw = "";
            let seriesData = [];
            for (let item of data) {
              xData.push(item.x_value);
              textDw = item.unit;
              seriesData.push(item.y_value);
            }
            // 指定图表的配置项和数据
            let option = {
              color: ["#e86056", "#F5CC53", "#6AE4B2"],
              title: {
                text: "单位:" + textDw,
                top: "5px",
                left: "100px",
                textStyle: {
                  color: "#fff",
                  fontSize: 28,
                  fontFamily: "SourceHanSansCN-Medium",
                },
              },
              tooltip: {
                textStyle: {
                  fontSize: 36,
                  color: "#fff",
                },
                backgroundColor: "rgba(51, 51, 51, 0.7)",
                borderWidth: 0,
                trigger: "item",
                formatter: function (val) {
                  let point =
                    '<span style="display:inline-block;margin-right:5px;border-radius:100px;width:24px;height:24px;background-color:#00c0ff"></span>';
                  let res = `<div>
                            <div style="margin-top: 10px">${point}${val.value}亿元</div>
                        </div>`;
                  return res;
                },
              },
              legend: {
                selectedMode: false,
                top: "0px",
                right: "24%",
                textStyle: {
                  color: "#fff",
                  fontSize: 28,
                  fontFamily: "SourceHanSansCN-Medium",
                },
                itemWidth: 18,
                itemHeight: 8,
              },
              grid: {
                top: "20%",
                left: "10%",
                right: "2%",
                bottom: "2%",
                containLabel: true,
              },
              xAxis: [
                {
                  type: "category",
                  data: xData,
                  splitLine: { show: false },
                  axisTick: {
                    //y轴刻度线
                    show: false,
                  },
                  axisLine: {
                    lineStyle: {
                      color: "rgb(119,179,241,.4)", // 颜色
                      width: 1, // 粗细
                    },
                  },
                  axisLabel: {
                    interval: 0,
                    // rotate: 40,
                    textStyle: {
                      color: "#fff",
                      fontSize: 28,
                      fontFamily: "SourceHanSansCN-Medium",
                    },
                  },
                },
              ],
              yAxis: [
                {
                  type: "value",
                  splitLine: {
                    lineStyle: {
                      color: "rgb(119,179,241,.4)",
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      color: "#fff",
                      fontSize: 28,
                      fontFamily: "SourceHanSansCN-Medium",
                    },
                  },
                },
              ],
              series: [
                {
                  cursor: "auto",
                  name: "",
                  type: "bar",
                  data: seriesData,
                  // itemStyle: {
                  //   normal: {
                  //     color: "#e86056",
                  //     label: {
                  //       show: true, //开启显示
                  //       position: "top", //在上方显示
                  //       textStyle: {
                  //         //数值样式
                  //         color: "#fff",
                  //         fontSize: 16,
                  //       },
                  //     },
                  //   },
                  // },
                  barWidth: 20,
                  itemStyle: {
                    normal: {
                      // barBorderRadius: [10, 10, 10, 10,6],
                      color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                        {
                          // 四个数字分别对应 数组中颜色的开始位置，分别为 右，下，左，上。例如（1,0,0,0 ）代表从右边开始渐
                          // 变。offset取值为0~1，0代表开始时的颜色，1代表结束时的颜色，柱子表现为这两种颜色的渐变。
                          offset: 0,
                          color: "#004f69",
                        },
                        {
                          offset: 1,
                          color: "#00c0ff",
                        },
                      ]),
                    },
                  },
                  label: {
                    show: false, //开启显示
                    position: "top", //在上方显示
                    textStyle: {
                      //数值样式
                      color: "#FFFFFF",
                      fontFamily: "SourceHanSansCN-Regular",
                      fontSize: 28,
                    },
                  },
                },
              ],
            };
            myChart.setOption(option, true);
            myChart.getZr().on("mousemove", (param) => {
              myChart.getZr().setCursorStyle("default");
            });
          },
          initEcharts2(data) {
            let myChart = echarts.init(document.getElementById("charts2"));
            let xData = [];
            let textDw = "";
            let seriesData = [];
            for (let item of data) {
              xData.push(item.x_value);
              textDw = item.unit;
              seriesData.push(item.y_value);
            }
            // 指定图表的配置项和数据
            let option = {
              color: ["#e86056", "#F5CC53", "#6AE4B2"],
              title: {
                text: "",
                top: "5px",
                left: "100px",
                textStyle: {
                  color: "#fff",
                  fontSize: 28,
                  fontFamily: "SourceHanSansCN-Medium",
                },
              },
              tooltip: {
                textStyle: {
                  fontSize: 36,
                  color: "#fff",
                },
                backgroundColor: "rgba(51, 51, 51, 0.7)",
                borderWidth: 0,
                trigger: "item",
                formatter: function (val) {
                  let point =
                    '<span style="display:inline-block;margin-right:5px;border-radius:100px;width:24px;height:24px;background-color:#00c0ff"></span>';
                  let res = `<div>
                            <div style="margin-top: 10px">${point}${val.value}%</div>
                        </div>`;
                  return res;
                },
              },
              legend: {
                selectedMode: false,
                top: "0px",
                right: "24%",
                textStyle: {
                  color: "#fff",
                  fontSize: 28,
                  fontFamily: "SourceHanSansCN-Medium",
                },
                itemWidth: 18,
                itemHeight: 8,
              },
              grid: {
                top: "20%",
                left: "10%",
                right: "2%",
                bottom: "2%",
                containLabel: true,
              },
              xAxis: [
                {
                  type: "category",
                  data: xData,
                  splitLine: { show: false },
                  axisTick: {
                    //y轴刻度线
                    show: false,
                  },
                  axisLine: {
                    lineStyle: {
                      color: "rgb(119,179,241,.4)", // 颜色
                      width: 1, // 粗细
                    },
                  },
                  axisLabel: {
                    interval: 0,
                    // rotate: 40,
                    textStyle: {
                      color: "#fff",
                      fontSize: 28,
                      fontFamily: "SourceHanSansCN-Medium",
                    },
                  },
                },
              ],
              yAxis: [
                {
                  type: "value",
                  splitLine: {
                    lineStyle: {
                      color: "rgb(119,179,241,.4)",
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      color: "#fff",
                      fontSize: 28,
                      fontFamily: "SourceHanSansCN-Medium",
                    },
                  },
                },
              ],
              series: [
                {
                  cursor: "auto",
                  name: "",
                  symbol: "circle", //设定为实心点
                  symbolSize: 15, //设定实心点的大小
                  type: "line",
                  data: seriesData,
                  // itemStyle: {
                  //   normal: {
                  //     color: "#e86056",
                  //     label: {
                  //       show: true, //开启显示
                  //       position: "top", //在上方显示
                  //       textStyle: {
                  //         //数值样式
                  //         color: "#fff",
                  //         fontSize: 16,
                  //       },
                  //     },
                  //   },
                  // },
                  barWidth: 20,
                  itemStyle: {
                    normal: {
                      // barBorderRadius: [10, 10, 10, 10,6],
                      color: "rgb(255,255,255,.8)",
                      lineStyle: {
                        color: "#ffc460",
                      },
                    },
                  },
                  label: {
                    show: false, //开启显示
                    position: "top", //在上方显示
                    textStyle: {
                      //数值样式
                      color: "#FFFFFF",
                      fontFamily: "SourceHanSansCN-Regular",
                      fontSize: 28,
                    },
                  },
                },
              ],
            };
            myChart.setOption(option, true);
            myChart.getZr().on("mousemove", (param) => {
              myChart.getZr().setCursorStyle("default");
            });
          },
          //   柱状图
          setEcharts3(dom, data) {
            let echarts0 = echarts.init(document.getElementById(dom));
            let that = this;

            let xData = [];
            let yData = [];
            for (let item of data) {
              xData.push(item.ymbq);
              yData.push(item.VALUE);
            }
            let option = {
              grid: {
                top: "5%",
                left: "0%",
                bottom: "5%",
                right: "0%",
                containLabel: true,
              },
              tooltip: {
                show: true,
                textStyle: {
                  fontSize: 30,
                },
              },
              animation: false,
              xAxis: [
                {
                  type: "category",
                  data: xData,
                  axisTick: {
                    alignWithLabel: true,
                  },
                  nameTextStyle: {
                    color: "#82b0ec",
                  },
                  axisLine: {
                    show: false,
                    lineStyle: {
                      color: "#82b0ec",
                    },
                  },
                  axisLabel: {
                    textStyle: {
                      color: "#fff",
                      fontSize: 28,
                    },
                    margin: 30,
                  },
                },
              ],
              yAxis: [
                {
                  show: false,
                  type: "value",
                  axisLabel: {
                    textStyle: {
                      color: "#fff",
                    },
                  },
                  splitLine: {
                    lineStyle: {
                      color: "#0c2c5a",
                    },
                  },
                  axisLine: {
                    show: false,
                  },
                },
              ],
              series: [
                {
                  name: "",
                  type: "pictorialBar",
                  symbolSize: [70, 10],
                  symbolOffset: [0, -6], // 上部椭圆
                  symbolPosition: "end",
                  z: 12,
                  // "barWidth": "0",
                  label: {
                    normal: {
                      show: true,
                      position: "top",
                      formatter: "{c}人",
                      fontSize: 28,
                      fontWeight: "bold",
                      color: "#34DCFF",
                    },
                  },
                  color: "#2DB1EF",
                  data: yData,
                },
                {
                  name: "",
                  type: "pictorialBar",
                  symbolSize: [70, 10],
                  symbolOffset: [0, 7], // 下部椭圆
                  // "barWidth": "20",
                  z: 12,
                  color: "#2DB1EF",
                  data: yData,
                },
                {
                  name: "",
                  type: "pictorialBar",
                  symbolSize: function (d) {
                    return d > 0 ? [110, 20] : [0, 0];
                  },
                  symbolOffset: [-2, 18], // 下部内环
                  z: 10,
                  itemStyle: {
                    normal: {
                      color: "transparent",
                      borderColor: "#25759c",
                      borderType: "solid",
                      borderWidth: 4,
                    },
                  },
                  data: yData,
                },
                {
                  name: "",
                  type: "pictorialBar",
                  symbolSize: [150, 30],
                  symbolOffset: [-2, 25], // 下部外环
                  z: 10,
                  itemStyle: {
                    normal: {
                      color: "transparent",
                      borderColor: "#25759c",
                      borderType: "solid",
                      borderWidth: 4,
                    },
                  },
                  data: yData,
                },
                {
                  type: "bar",
                  //silent: true,
                  barWidth: "70",
                  barGap: "10%", // Make series be overlap
                  barCateGoryGap: "10%",
                  itemStyle: {
                    normal: {
                      color: new echarts.graphic.LinearGradient(0, 0, 0, 0.7, [
                        {
                          offset: 0,
                          color: "#0B3147",
                        },
                        {
                          offset: 1,
                          color: "#38B2E6",
                        },
                      ]),
                      opacity: 0.8,
                    },
                  },
                  data: yData,
                },
              ],
            };
            echarts0.setOption(option);
            echarts0.getZr().on("mousemove", (param) => {
              echarts0.getZr().setCursorStyle("default");
            });
          },
        },
      });
    </script>
  </body>
  <style>
    #szwhRight {
      width: 2045px;
      height: 1890px;
      background: url("/img/right-bg.png") no-repeat;
      background-size: 100% 100%;
      display: flex;
      justify-content: center;
    }

    .content {
      width: 1934px;
      height: 100%;
      /* margin-top: 79px; */
    }
    .content_line2 {
      height: 680px !important;
      margin-top: 68px !important;
    }
    .wbg-class {
      height: 100%;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      flex-wrap: wrap;
    }
    .title {
      width: 100%;
      height: 86px;
    }

    .title_text {
      font-size: 54px;
      letter-spacing: 3px;
      background: linear-gradient(
        to bottom,
        #caffff,
        #e5ffff,
        #ffffff,
        #00c0ff
      );
      font-weight: 800;
      -webkit-background-clip: text;
      color: transparent;
      white-space: nowrap;
      margin: 0 17px 0 17px;
    }

    .title_line_1 {
      width: 1860px;
      height: 51px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
    }

    .title_line_2 {
      width: 1934px;
      position: relative;
      bottom: 30px;
    }

    /*翻转*/
    .rotate {
      -webkit-transform: rotateY(180deg);
    }

    .content_title {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
    }

    .content_title_text {
      background: linear-gradient(
        to bottom,
        #caffff,
        #e5ffff,
        #ffffff,
        #00c0ff
      );
      font-size: 40px;
      letter-spacing: 0;
      font-weight: 800;
      -webkit-background-clip: text;
      color: transparent;
      font-family: AdobeHeitiStd-Regular;
    }

    .content_line {
      width: 100%;
      margin-top: 11px;
    }

    .content_line_LeftRight {
      width: 966.5px;
      height: 461px;
      margin-top: 24px;
    }
    .content_line1 {
      height: 523px !important;
    }
    .chartsName {
      font-family: AdobeHeitiStd-Regular;
      font-size: 38px;
      letter-spacing: 0px;
      color: #d6e7f9;
      margin-left: 380px;
    }

    .line_2_left {
      width: 25%;
      height: fit-content;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      flex-wrap: wrap;
      margin-left: 30px;
    }

    .line_2_right {
      width: 71%;
      /* height: fit-content; */
      height: 652px;
      display: flex;
      /* justify-content: space-evenly; */
      align-items: center;
      flex-wrap: wrap;
    }

    .item {
      width: 300px;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-top: 83px;
    }
    .bottomBox .item {
      margin-top: 0px;
    }
    .number {
      font-size: 60px;
      letter-spacing: 0;
      /*margin-top: 31px;*/
      white-space: nowrap;
      /* font-weight: 800; */
      display: flex;
      justify-content: space-evenly;
      align-items: center;
    }

    .Gold {
      background: linear-gradient(
        to bottom,
        #ffeccb,
        #ffffff,
        #ffc460,
        #ffe2b0,
        #ffffff
      );
      -webkit-background-clip: text;
      color: transparent;
    }

    .unit {
      font-size: 32px;
      margin-top: 10px;
    }

    .name {
      width: fit-content;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      font-family: AdobeHeitiStd-Regular;
      font-size: 38px;
      letter-spacing: 0;
      color: #d6e7f9;
      margin-left: 12px;
      text-align: center;
    }

    ::-webkit-scrollbar {
      width: 0;
    }

    .bottomBox {
      /* width: 100%; */
      height: 300px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      flex-wrap: wrap;
      box-sizing: border-box;
    }
    .sanicon {
      display: inline-block;
      width: 40px;
      height: 27px;
      background: url("/static/citybrain/csdn/img/ywt/三级标题图标.png")
        no-repeat;
    }
  </style>
</html>
