<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Document</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/static/js/jslib/jquery-3.4.1.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <link rel="stylesheet" href="/static/citybrain/shgl/css/jtys-left-copy.css" />
    </head>
    <style>
        #zhzf-right {
            width: 2045px;
            height: 1890px;
            background: url("/img/left-bg.png") no-repeat;
            background-size: 100% 100%;
            display: flex;
            justify-content: center;
        }
        .content {
            width: 1934px;
            height: 100%;
        }
        .box-top {
            display: flex;
        }
        .top {
            display: flex;
        }
        .select {
            margin-left: 785px;
        }
        .el-select {
            width: 100px;
        }
        .el-scrollbar {
            width: 150px !important;
        }
        .el-input__inner {
            width: 150px !important;
        }
        .tabName {
            position: absolute;
            display: flex;
            color: #fff;
            left: 620px;
        }
        .tab {
            cursor: pointer;
            margin-right: 15px;
            font-size: 30px;
        }
        .active {
            color: #0e56a7;
        }
        .egs-popup-anchor-bottom .egs-popup-tip {
            display: none;
        }
    </style>
    <body>
        <div id="zhzf-right">
            <div class="content">
                <div class="top">
                    <div class="title">
                        <nav style="padding: 20px 15px 20px 0">
                            <s-header-title
                                style="width: 100%"
                                title="案件趋势分析"
                                data-time="2022年7月22日"
                                htype="1"
                            ></s-header-title>
                        </nav>
                    </div>
                    <div class="title">
                        <nav style="padding: 20px 0 20px 15px">
                            <s-header-title
                                style="width: 100%"
                                title="案件来源分析"
                                data-time="2022年7月22日"
                                htype="1"
                            ></s-header-title>
                        </nav>
                    </div>
                </div>
                <div class="tabName">
                    <div
                        class="tab"
                        v-for="(item,index) in tabName"
                        :class="{active:isActive===index}"
                        @click="change(index)"
                    >
                        {{item}}
                    </div>
                </div>
                <div class="select" style="display: flex">
                    <el-select v-model="value" placeholder="请选择">
                        <el-option v-for="item in options0" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </div>
                <div class="content_line">
                    <div class="box-top">
                        <div id="barEcharts001" style="width: 100%; height: 450px"></div>
                        <div id="pieEcharts001" style="width: 100%; height: 450px"></div>
                    </div>
                </div>
                <div class="title">
                    <nav style="padding: 20px 45px">
                        <s-header-title
                            style="width: 100%"
                            title="案件关联性分析"
                            data-time="2022年7月22日"
                            htype="1"
                        ></s-header-title>
                    </nav>
                </div>
                <div class="content_line">
                    <div id="barEcharts002" style="width: 100%; height: 450px"></div>
                </div>
                <div class="title">
                    <nav style="padding: 20px 45px">
                        <s-header-title
                            style="width: 100%"
                            title="案件区域分析"
                            data-time="2022年7月22日"
                            htype="1"
                        ></s-header-title>
                    </nav>
                </div>
                <div class="content_line">
                    <div id="barEcharts003" style="width: 100%; height: 500px"></div>
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#zhzf-right",
        data: {
            value: "全部",
            value1: "2021年3月",
            value3: "2021年1月",
            value4: "2021年3月",
            tabName: ["年", "季度", "月"],
            isActive: 0,
            options0: [
                {
                    value: "全部",
                    label: "全部",
                },
            ],

            pointData: [
                {
                    pos: [119.8314905582194, 29.623060984755377, 15000],
                    name: "浦江县",
                    key: ["待处理事件", "在线人员"],
                    value: ["33件", "33人"],
                },
                {
                    pos: [119.70440936200465, 29.10010792774123, 15000],
                    name: "金义新区",
                    key: ["待处理事件", "在线人员"],
                    value: ["33件", "33人"],
                },
                {
                    pos: [119.32419295719222, 29.219102728348943, 15000],
                    name: "兰溪市",
                    key: ["待处理事件", "在线人员"],
                    value: ["33件", "33人"],
                },
                {
                    pos: [119.44160138147026, 28.908609191923716, 15000],
                    name: "婺城区",
                    key: ["待处理事件", "在线人员"],
                    value: ["33件", "33人"],
                },
                {
                    pos: [120.02438915928201, 29.18180844822686, 15000],
                    name: "义乌市",
                    key: ["待处理事件", "在线人员"],
                    value: ["33件", "33人"],
                },
                {
                    pos: [119.67487615578625, 28.707011171587162, 15000],
                    name: "武义县",
                    key: ["待处理事件", "在线人员"],
                    value: ["33件", "33人"],
                },
                {
                    pos: [120.10886352478184, 28.887053762616034, 15000],
                    name: "永康市",
                    key: ["待处理事件", "在线人员"],
                    value: ["33件", "33人"],
                },
                {
                    pos: [120.39254935807531, 29.215565856200392, 15000],
                    name: "东阳市",
                    key: ["待处理事件", "在线人员"],
                    value: ["33件", "33人"],
                },
                {
                    pos: [120.53638710113705, 28.986431602110855, 15000],
                    name: "磐安县",
                    key: ["待处理事件", "在线人员"],
                    value: ["33件", "33人"],
                },
            ],
        },
        mounted() {
            this.initFun();
            this.initMap();
            this.openCustomPop();
        },
        methods: {
            initFun() {
                $api("shgl_zhzf-left001", { type: "年" }).then((res) => {
                    this.getEcharts01("barEcharts001", res);
                });
                $api("shgl_zhzf-left002").then((res) => {
                    this.getEcharts02("pieEcharts001", res);
                });
                $api("shgl_zhzf-left003").then((res) => {
                    this.getEcharts03("barEcharts002", res);
                });
                $api("shgl_zhzf-left004").then((res) => {
                    this.getEcharts04("barEcharts003", res);
                });
            },
            change(index) {
                this.isActive = index;
                if (this.isActive === 0) {
                    $api("shgl_zhzf-left001", { type: "年" }).then((res) => {
                        this.getEcharts01("barEcharts001", res);
                    });
                } else if (this.isActive === 1) {
                    $api("shgl_zhzf-left001", { type: "季" }).then((res) => { 
                        this.getEcharts01("barEcharts001", res);
                    });
                } else if (this.isActive === 2) {
                    $api("shgl_zhzf-left001", { type: "月" }).then((res) => {
                        this.getEcharts01("barEcharts001", res);
                    });
                }
            },
            getEcharts01(dom, echartData) {
                let echarts0 = echarts.init(document.getElementById(dom));
                var colors = ["#ffff", "#ffff"];

                var xData = echartData.map((item) => item.mounth);
                var barData = echartData.map((item) => item.value);

                var lineData = echartData.map((item) => item.value1);

                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "30",
                        },
                    },
                    grid: {
                        top: 80,
                        left: 80,
                    },
                    legend: {
                        data: ["增长率"],
                        align: "left",
                        left: 400,
                        textStyle: {
                            fontSize: 35,
                            color: "#fff",
                        },
                        itemHeight: 30,
                        itemWidth: 30,
                        top: 30,
                    },
                    calculable: true,
                    xAxis: [
                        {
                            type: "category",
                            axisLine: {
                                lineStyle: {
                                    color: "#fff",
                                },
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            splitArea: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                textStyle: {
                                    fontSize: 30,
                                },
                            },
                            data: xData,
                        },
                    ],
                    yAxis: [
                        {
                            type: "value",
                            name: "",

                            position: "left",
                            axisLine: {
                                lineStyle: {
                                    color: colors[0],
                                },
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#22364f",
                                },
                            },
                            axisLabel: {
                                fontSize: 35,
                                formatter: "{value}",
                            },
                        },
                        {
                            type: "value",
                            name: "",
                            position: "right",

                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#22364f",
                                },
                            },
                            axisLine: {
                                lineStyle: {
                                    color: colors[1],
                                },
                            },
                            axisLabel: {
                                fontSize: 35,
                                formatter: "{value}%",
                            },
                        },
                    ],
                    series: [
                        {
                            name: "",
                            type: "bar",
                            color: "#ff484a",
                            barWidth: 30,
                            data: barData,
                        },
                        {
                            name: "增长率",
                            color: "#03c3fb",
                            yAxisIndex: 1,
                            type: "line",
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 3, //折线宽度
                                    },
                                    opacity: 0.4,
                                },
                            },
                            data: lineData,
                        },
                    ],
                };
                echarts0.setOption(option);
            },
            getEcharts02(dom, echartData) {
                let echarts1 = echarts.init(document.getElementById(dom));
                let nameValue = echartData.map((item) => item.name);
                let option = {
                    tooltip: {
                        trigger: "item",
                        formatter: "{b}：{d}%",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "30",
                        },
                    },
                    legend: {
                        left: "center",
                        top: "5%",
                        textStyle: {
                            color: "#fff",
                            fontSize: 35,
                        },
                        itemHeight: 30,
                        itemWidth: 40,
                        itemGap: 20,
                        data: nameValue,
                    },
                    series: [
                        {
                            emphasis: {
                                //使用emphasis
                                disable: false,
                                scale: false, //不缩放
                                scaleSize: 0, //为了防止失效直接设置未0
                            },
                            label: {
                                normal: {
                                    formatter: "{d|{b}}\n{hr|}\n{d|{d}%}",
                                    rich: {
                                        b: {
                                            fontSize: 30,
                                            color: "#fff",
                                            align: "left",
                                            padding: 4,
                                        },
                                        hr: {
                                            width: "100%",
                                            borderWidth: 1,
                                            height: 0,
                                        },
                                        d: {
                                            fontSize: 30,
                                            color: "#fff",
                                            align: "left",
                                            padding: 1,
                                        },
                                        c: {
                                            fontSize: 30,
                                            color: "#fff",
                                            align: "center",
                                        },
                                    },
                                },
                            },
                            type: "pie",
                            radius: "70%",
                            center: ["50%", "60%"],
                            data: echartData,
                            itemStyle: {
                                emphasis: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: "rgba(0, 0, 0, 0.5)",
                                },
                            },
                        },
                    ],
                };
                echarts1.setOption(option);
            },
            getEcharts03(dom, echartData) {
                let echarts2 = echarts.init(document.getElementById(dom));
                let cityName = echartData.map((item) => item.name);
                let barData = echartData.map((item) => item.value);
                let lineData = echartData.map((item) => item.value1);
                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "30",
                        },
                    },
                    grid: {
                        top: 80,
                    },
                    legend: {
                        data: ["关联案件数", "关联案件增长率"],
                        align: "left",
                        textStyle: {
                            fontSize: 35,
                            color: "#fff",
                        },
                        itemHeight: 30,
                        itemWidth: 40,
                        itemGap: 20,
                        top: 25,
                    },
                    calculable: true,
                    xAxis: [
                        {
                            type: "category",
                            axisLine: {
                                lineStyle: {
                                    color: "#fff",
                                },
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: true,
                            },
                            splitArea: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                textStyle: {
                                    fontSize: 35,
                                },
                            },
                            data: cityName,
                        },
                    ],
                    yAxis: [
                        {
                            type: "value",
                            name: "单位：件",
                            nameTextStyle: {
                                color: "#ffffff",
                                align: "left",
                                fontSize: 35,
                            },
                            min: 0,
                            max: 1200,
                            position: "left",
                            axisLine: {
                                lineStyle: {
                                    color: "#ffff",
                                },
                            },
                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                fontSize: 35,
                                formatter: "{value} ",
                            },
                        },
                        {
                            type: "value",
                            name: "单位：%",
                            nameTextStyle: {
                                color: "#ffffff",
                                align: "left",
                                fontSize: 35,
                            },
                            min: 0,
                            max: 10,
                            position: "right",
                            axisLine: {
                                lineStyle: {
                                    color: "#ffff",
                                },
                            },
                            axisLabel: {
                                fontSize: 35,
                                formatter: "{value}",
                            },
                            splitLine: {
                                show: false,
                            },
                        },
                    ],
                    series: [
                        {
                            name: "关联案件数",
                            type: "bar",
                            color: "#5087ec",
                            barWidth: 50,
                            data: barData,
                        },
                        {
                            name: "关联案件增长率",
                            color: "#03c3fb",
                            yAxisIndex: 1,
                            type: "line",
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 3, //折线宽度
                                    },
                                    opacity: 0.4,
                                },
                            },
                            data: lineData,
                        },
                    ],
                };
                echarts2.setOption(option);
            },
            getEcharts04(dom, echartData) {
                let echarts3 = echarts.init(document.getElementById(dom));
                let cityName = echartData.map((item) => item.name);
                let barData = echartData.map((item) => item.value);
                let barValue = echartData.map((item) => item.value1);
                let lineData = echartData.map((item) => item.value);

                let option = {
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "30",
                        },
                    },
                    grid: {
                        top: 80,
                    },
                    legend: {
                        data: ["立案率", "结案率", "按时结案率"],
                        align: "left",
                        textStyle: {
                            fontSize: 35,
                            color: "#fff",
                        },
                        itemHeight: 30,
                        itemWidth: 40,
                        itemGap: 20,
                        top: 25,
                    },
                    calculable: true,
                    xAxis: [
                        {
                            type: "category",
                            axisLine: {
                                lineStyle: {
                                    color: "#fff",
                                },
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: true,
                            },
                            splitArea: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                textStyle: {
                                    fontSize: 35,
                                },
                            },
                            data: cityName,
                        },
                    ],
                    yAxis: [
                        {
                            type: "value",
                            name: "单位：件",
                            nameTextStyle: {
                                color: "#ffffff",
                                align: "center",
                                fontSize: 35,
                            },
                            min: 0,
                            max: 250,
                            position: "left",
                            axisLine: {
                                lineStyle: {
                                    color: "#ffff",
                                    fontSize: 35,
                                },
                            },
                            axisLabel: {
                                fontSize: 35,
                                formatter: "{value}",
                            },
                            splitLine: {
                                show: false,
                            },
                        },
                        {
                            type: "value",
                            name: "单位：%",
                            min: 0,
                            max: 250,
                            nameTextStyle: {
                                color: "#ffffff",
                                align: "left",
                                fontSize: 35,
                            },
                            position: "right",
                            axisLine: {
                                lineStyle: {
                                    color: "#ffff",
                                },
                            },
                            axisLabel: {
                                fontSize: 35,
                                formatter: "{value} ",
                            },
                            splitLine: {
                                show: false,
                            },
                        },
                    ],
                    series: [
                        {
                            name: "立案率",
                            type: "bar",
                            color: "#5087ec",
                            barWidth: 50,
                            data: barData,
                        },
                        {
                            name: "结案率",
                            type: "bar",
                            color: "#68bbc4",
                            barWidth: 50,
                            data: barValue,
                        },
                        {
                            name: "按时结案率",
                            color: "#03c3fb",
                            yAxisIndex: 1,
                            type: "line",
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        width: 3, //折线宽度
                                    },
                                    opacity: 0.4,
                                },
                            },
                            data: lineData,
                        },
                    ],
                };
                echarts3.setOption(option);
            },

            initMap() {
                top.document.getElementById("map").contentWindow.Work.change3D(9);
                this.flyTo();
                this.add3DText();
            },

            //飞入
            flyTo() {
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "flyto", //功能名称
                        flyData: {
                            center: [119.95478050597587, 29.01613226366889],
                            zoom: 10.5,
                            pitch: 40,
                            bearing: 0,
                        },
                    })
                );
            },
            // 加载3D文字方法
            add3DText() {
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "3Dtext", //3D文字功能
                        textData: [
                            // pos文字的位置  //text 展示的文字
                            { pos: [119.94315399169922, 29.5630503845215, 11000], text: "浦江县" },
                            { pos: [119.46214447021484, 29.31345558166504, 11000], text: "兰溪市" },
                            { pos: [119.5569204711914, 29.00677101135254, 11000], text: "婺城区" },
                            { pos: [119.8483056640625, 29.188559951782227, 11000], text: "金义新区" },
                            { pos: [120.08206787109375, 29.322123641967773, 11000], text: "义乌市" },
                            { pos: [119.7269204711914, 28.79677101135254, 11000], text: "武义县" },
                            { pos: [120.1469204711914, 28.97677101135254, 11000], text: "永康市" },
                            { pos: [120.4169204711914, 29.24677101135254, 11000], text: "东阳市" },
                            { pos: [120.6299204711914, 29.06677101135254, 11000], text: "磐安县" },
                        ],
                        textSize: 40,
                        id: "text1",
                        // zoomShow: true,
                        color: [255, 255, 255, 1],
                    })
                );
            },

            openCustomPop() {
                this.pointData.forEach((item) => {
                    this.customPop(item);
                });
            },
            //添加自定义弹框
            customPop(item) {
                console.log("111", item.key);
                let p = [];

                item.key.forEach((el, index) => {
                    p.push(
                        `<p>
                        <span > ${el}:</span>
                        <span classs="s-c-yellow-gradient"> ${item.value[index]}</span>
                    </p>`
                    );
                });
                console.log(p);
                let objData = {
                    funcName: "customPop",
                    coordinates: item.pos,
                    closeButton: true,
                    html: `<div class="contain" id="customPop"
                      style="
                        position: absolute;
                        height: 160px;
                        width: max-content;
                        display:inline-block;
                        background-color: rgba(0, 0, 0, 0.8);
                        border: 2px solid #00aae2;
                        box-sizing: border-box;
                        border-style: solid;
                        border-width: 4px;
                        border-image-source: linear-gradient(0deg, #32abe4 0%, #0b5aa4 100%);
                        border-image-slice: 1;">
                        <div
                            class="content"
                            style="font-size: 28px;color: #fff;padding: 20px;line-height: 55px;">
                            ${p.join("")}
                        </div>
                    </div>`,
                };
                top.document.getElementById("map").contentWindow.Work.funChange(JSON.stringify(objData));
            },
        },
    });
</script>
