<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <script src="../js/vue.js"></script>
    <script src="../js/echarts.js"></script>
    <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Sans+SC:100,300,400,500,700,900"> -->
    <link rel="stylesheet" href="../css/city.css" />
    <link rel="stylesheet" href="../css/common.css" />
    <script src="../js/vue.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/jquery-3.4.1.min.js"></script>
    <style>
        .cityCont {
            display: flex;
        }

        .cityCont .cityLeft {
            width: 49%;
        }

        .fjfk-body-middle-header {
            text-align: center;
            margin-top: 20px;
            margin-bottom: 40px;
        }

        .fjfk-body-middle-header span {
            font-family: SourceHanSansSC-Medium;
            font-size: 40px;
            letter-spacing: 5px;
            color: #fff;
        }

        .fjfk-body-middle-header .number {
            display: inline-block;
            position: relative;
            top: 21px;
            width: 59px;
            height: 90px;
            margin: 0 4px;
            background: url('../img/common/组\ 2767.png') no-repeat;
            background-size: cover;
            text-align: center;
        }

        .fjfk-body-middle-header .number b {
            display: inline-block;
            position: absolute;
            top: -4px;
            left: 11px;
            font-family: BebasNeue;
            font-size: 70px;
            background: linear-gradient(to bottom, #ffd8a1, #fffcf3, #ffb637, #ffdb9b, #ffffff);
            -webkit-background-clip: text;
            color: transparent;
            font-weight: 600;
        }

        .fjfk-body-middle-header .numberSpan {
            background: linear-gradient(to bottom, #ffd8a1, #fffcf3, #ffb637, #ffdb9b, #ffffff);
            -webkit-background-clip: text;
            color: transparent;
            font-weight: 600;
            margin-left: 10px;
        }

        .cityRight {
            margin-left: 1%;
            width: 50%;
            margin-right: 0;
        }

        .cityRight ul {
            margin-top: 70px;
            display: flex;
            height: 300px;
        }

        .cityRight ul li {
            flex: 1;
        }

        .cityRight ul li div {
            position: relative;
            text-align: center;
        }

        .cityRight ul li div .span_li_1 {
            font-size: 38px;
            color: #07D9FF;
            position: absolute;
            top: 78px;
            left: 120px;
        }

        .cityRight ul li div .span_li_2 {
            font-size: 35px;
            color: #fff;
            position: absolute;
            top: 227px;
            left: 95px;
        }

        .yntab {
            display: flex;
            align-items: center;
            position: absolute;
            right: 120px;
            z-index: 10;
            top: 40px;
        }

        .yntab>div {
            font-size: 28px;
            padding: 2px;
            color: #fff;
            margin-right: 10px;
            cursor: pointer;
        }

        .yntabActive {
            color: #0087EC !important;
        }

        .r1top {
            font-size: 40px;
            font-family: "思源黑体 CNBOLD";
            color: #fff;
            width: 100%;
            text-align: center;
            line-height: 50px;
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
</head>

<body>
    <div id="csgl_app">
        <div class="csgl_app_box">
            <nav style="margin: 20px 0;">
                <s-header-title title="城市交通" data-time="数据至:2022年2月" htype="1"></s-header-title>
            </nav>
            <div class="csgl_container">
                <div class="r1top">交通总量趋势分析</div>
                <div class="yntab" style="top:160px;">
                    <div :class="{'yntabActive':yntabActiveNum1==0}" @click="yntabActiveNum1=0">月</div>
                    <div :class="{'yntabActive':yntabActiveNum1==1}" @click="yntabActiveNum1=1">年</div>
                </div>
                <div id="topEcharts0" style="height:400px;width:100%"></div>
                <!-- <nav style="margin: 20px 0;">
                    <s-header-title title="城市交通" data-time="数据至:2022年2月" htype="1"></s-header-title>
                </nav> -->

                <div class="ajallfx cdjdyfx" style="align-items: start;">
                    <div class="cdjdyfx_item" style="position:relative">
                        <div class="ajallfx_head">
                            <i class="icon_left"></i>
                            <h2>客货运流量</h2>
                            <i class="icon_right"></i>
                        </div>
                        <div class="yntab">
                            <div :class="{'yntabActive':yntabActiveNum==0}" @click="yntabActiveNum=0">月</div>
                            <div :class="{'yntabActive':yntabActiveNum==1}" @click="yntabActiveNum=1">年</div>
                        </div>
                        <div class="flowCont">
                            <div class="flowContBtn">
                                <div class="ContBtn">
                                    <p @click="clickAct(1)" :class="{'active':isActive !='1'}"
                                        style="float: right;margin-right: 10px;">客运流量</p>
                                </div>
                                <div class="ContBtn">
                                    <p @click="clickAct(2)" :class="{'active':isActive !='2'}"
                                        style="float: left;margin-left: 10px;">货运流量</p>
                                </div>
                            </div>
                            <div class="passenger" v-if="isActive=='1'">
                                <div class="backImg">
                                    <div class="flowContLeft">
                                        <h3 class="h3title">{{kyData.value}}<span>万人次</span></h3>
                                        <p>{{kyData.name}}</p>
                                    </div>
                                </div>
                                <div class="flowContRight">
                                    <ul>
                                        <li>
                                            <div class="rightListTit">
                                                <img src="../img/adm/公路.png" alt="">公路
                                            </div>
                                            <div class="rightListCont">
                                                <p>{{kyData.gl}}<span>万人</span></p>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="rightListTit">
                                                <img src="../img/adm/铁路.png" alt="">铁路
                                            </div>
                                            <div class="rightListCont">
                                                <p>{{kyData.tl}}<span>万人</span></p>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="rightListTit">
                                                <img src="../img/adm/水路.png" alt="">水路
                                            </div>
                                            <div class="rightListCont">
                                                <p>{{kyData.sl}}<span>万人</span></p>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="freight" v-if="isActive=='2'">
                                <div class="backImg">
                                    <div class="flowContLeft">
                                        <h3 class="h3title">{{hyData.value}}<span>万吨</span></h3>
                                        <p>{{hyData.name}}</p>
                                    </div>
                                </div>
                                <div class="flowContRight">
                                    <ul>
                                        <li>
                                            <div class="rightListTit">
                                                <img src="../img/adm/公路.png" alt="">公路
                                            </div>
                                            <div class="rightListCont">
                                                <p>{{hyData.gl}}<span>万吨</span></p>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="rightListTit">
                                                <img src="../img/adm/铁路.png" alt="">铁路
                                            </div>
                                            <div class="rightListCont">
                                                <p>{{hyData.tl}}<span>万吨</span></p>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="rightListTit">
                                                <img src="../img/adm/水路.png" alt="">水路
                                            </div>
                                            <div class="rightListCont">
                                                <p>{{hyData.sl}}<span>万吨</span></p>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="cdjdyfx_item">
                        <div class="ajallfx_head">
                            <i class="icon_left"></i>
                            <h2>交通事故趋势分析</h2>
                            <i class="icon_right"></i>
                        </div>
                        <div id="ajallopt" style="width: 945px;height: 370px;">
                        </div>
                    </div>
                </div>
                <div class="cdjdy">
                    <nav style="margin: 20px 0;">
                        <s-header-title title="城市安全" data-time="数据至:2022年2月" htype="1"></s-header-title>
                    </nav>
                </div>
                <div class="ajallfx cdjdyfx">
                    <div class="cdjdyfx_item">
                        <div class="ajallfx_head">
                            <i class="icon_left"></i>
                            <h2>2021年安全生产事故</h2>
                            <i class="icon_right"></i>
                        </div>
                        <div id="lyfxOption" style="width: 945px;height: 370px;">
                        </div>

                    </div>
                    <div class="cdjdyfx_item">
                        <div class="ajallfx_head ">
                            <i class="icon_left"></i>
                            <h2>2021年自然灾害受损失情况</h2>
                            <i class="icon_right"></i>
                        </div>
                        <div class="disasterCont">
                            <div class="contList" v-for="(item,index) in zrzhData">
                                <h5>{{item.h5}}</h5>
                                <div class="disasterList">
                                    <h4>{{item.value}}</h4>
                                    <p>{{item.value1}}
                                        <span v-if="index==0">间</span>
                                        <span v-if="index==1">万亩</span>
                                        <span v-if="index==2">公顷</span>
                                    </p>
                                    <h4>{{item.value2}}</h4>
                                    <p>{{item.value3}}
                                        <span v-if="index==0">亿元</span>
                                        <span v-if="index==1">万元</span>
                                        <span v-if="index==2">公顷</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>


            </div>
        </div>

    </div>




    </div>



</body>

</html>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>

<script>
    var vm = new Vue({
        el: '#csgl_app',
        data: {
            isActive: 1,
            // 城市告警
            cityGj: [],
            // 城市告警 的table
            cityList: [],
            // 客运流量
            kyData: {},
            // 货运流量
            hyData: {},
            // 2021年自然灾害受损情况
            zrzhData: [],
            yntabActiveNum: 0,
            yntabActiveNum1: 0,

        },
        mounted() {
            this.initFun();
            this.getAnEcharts01()
            // this.getlyfxOption()

        },
        methods: {
            todwrh() {
                top.window.commonObj.funSearchInfo(
                    { argument: `indexid=index001&url=shgl&userId=1` },
                    (data) => {
                        top.window.commonObj.menuTree.unshift('dwrh-sy')
                        top.window.commonObj.funGetMenu(data, 'dwrh-sy')
                    }
                )

            },
            initFun() {
                let that = this
                // 交通总量趋势分析
                $api("hjbh_shgl-right_shglRight008").then((res) => {
                    //这个data就是json数据
                    this.getrightzt("topEcharts0", res)
                });
                // 客运流量
                $api("hjbh_shgl-right_shglRight003").then((res) => {
                    //这个data就是json数据
                    that.kyData = res[0]
                });
                // 货运流量
                $api("hjbh_shgl-right_shglRight004").then((res) => {
                    //这个data就是json数据
                    that.hyData = res[0]
                });
                // 交通事故趋势分析
                $api("hjbh_shgl-right_shglRight006").then((res) => {
                    //这个data就是json数据
                    that.getAjallcontent(res)
                });

                // 2021年安全生产事故
                $api("hjbh_shgl-right_shglRight005").then((res) => {
                    //这个data就是json数据
                    that.getlyfxOption(res)
                });

                // 自然灾害
                $api("hjbh_shgl-right_shglRight007").then((res) => {
                    //这个data就是json数据
                    that.zrzhData = res
                });
                // $get('/hjbh/shgl-right/shglRight001').then((res) => {
                //     that.cityList = res
                // })
                // $get('/hjbh/shgl-right/shglRight002').then((res) => {
                //     that.cityGj = res
                // })
                // $get('/hjbh/shgl-right/shglRight003', { code: 1 }).then((res) => {
                //     that.kyData = res[0]
                // })
                // $get('/hjbh/shgl-right/shglRight003', { code: 2 }).then((res) => {
                //     console.log(res);
                //     that.hyData = res[0]
                // })
                // $get('/hjbh/shgl-right/shglRight004').then((res) => {
                //     that.getAjallcontent(res)
                // })
                // $get('/hjbh/shgl-right/shglRight006').then((res) => {
                //     let result = {
                //         "unit": "单位(起)",
                //         "names": ["事故"],
                //         "lineX": res.map(o => {
                //             return o.time
                //         }),
                //         "value": [res.map(o => {
                //             return o.value
                //         })]
                //     }

                //     that.getlyfxOption(result)
                // })
                // $get('/hjbh/shgl-right/shglRight005').then((res) => {
                //     that.zrzhData = res
                // })
            },
            /*drump(){
                top.window.location.href = "http://web.dcyun.com:48433/#/router1/watershed"
            },*/
            clickAct(id) {
                this.isActive = id
            },
            // echarts实例
            setupEChart(id, options) {
                console.log('11')
                const chart = echarts.init(document.getElementById(id));
                chart.clear();
                chart.setOption(options);
                window.onresize = () => {
                    chart.resize();
                };
            },
            getAjallcontent(res) {
                var color = ['rgba(205, 173, 62)', 'rgba(93, 255, 255)']
                let xData = res.map(item=>item.name)
                let data = res.map(item=>item.value)
                // for (let item of res) {
                //     xData.push(item.time)
                //     data.push(item.num)
                // }

                // lineY[0].markLine = {
                //     silent: true,
                //     data: [{
                //         yAxis: 5
                //     }, {
                //         yAxis: 100
                //     }, {
                //         yAxis: 200
                //     }, {
                //         yAxis: 300
                //     }, {
                //         yAxis: 400
                //     }]
                // }
                let ajallOption = {
                    title: {
                        textStyle: {
                            fontWeight: 'normal',
                            fontSize: 16,
                            color: '#F1F1F3'
                        },
                        left: '6%',
                        top: '4%'
                    },
                    tooltip: {
                        trigger: 'axis',
                        "textStyle": {
                            "fontSize": 28
                        }
                    },

                    legend: {
                        top: '4%',
                        data: "事故",
                        textStyle: {
                            fontSize: 28,
                            color: 'F1F1F3'
                        },
                        right: '4%'
                    },
                    grid: {
                        top: '12%',
                        left: '4%',
                        right: '4%',
                        bottom: '10%',
                        containLabel: true
                    },
                    xAxis: {
                        show: true,
                        type: 'category',
                        boundaryGap: false,
                        data: xData,
                        nameTextStyle: {
                            fontSize: 28
                        },
                        axisLabel: {
                            textStyle: {
                                color: 'rgb(0,253,255,0.6)',
                                fontSize: 28
                            },
                            //   formatter: function(params) {
                            //       return params.split(' ')[0] + '\n' + params.split(' ')[1]
                            //   }
                        }
                    },
                    yAxis: {
                        show: true,
                        nameTextStyle: {
                            fontSize: 28
                        },
                        splitArea: {
                            show: false,
                            // areaStyle: {
                            //     color:"rgba(1, 22, 53, 1)"
                            //   }
                        },
                        name: "单位(起)",
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}',
                            textStyle: {
                                color: 'rgb(0,253,255,0.6)',
                                fontSize: 28
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: 'rgb(23,255,243,0.3)'
                            }
                        },

                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: 'rgb(0,253,255,0.6)'
                            }
                        }
                    },
                    series: [
                        {
                            name: "事故",
                            type: 'line',
                            color: 'rgba(205, 173, 62)',
                            smooth: false,
                            areaStyle: {
                                normal: {

                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 2, [{
                                        offset: 0,
                                        color: 'rgba(205, 173, 62,0.3)'
                                    }, {
                                        offset: 0.8,
                                        color: 'rgba(205, 173, 62,0)'
                                    }], false),
                                    shadowColor: 'rgba(205, 173, 62, 0.1)',
                                    shadowBlur: 10
                                }
                            },
                            symbol: 'circle',
                            symbolSize: 5,
                            data: data
                        }
                    ]
                }
                this.setupEChart("ajallopt", ajallOption);
            },
            getlyfxOption(res) {
                var charts = {
                    unit: '单位(起)',
                    names: ['事故'],
                    lineX: res.map(item=>item.date),
                    value: [res.map(item=>item.value)]
                }
                var color = ['rgba(76, 134, 143', 'rgba(182, 206, 209']
                var lineY = []

                for (var i = 0; i < charts.names.length; i++) {
                    var x = i
                    if (x > color.length - 1) {
                        x = color.length - 1
                    }
                    var data = {
                        name: charts.names[i],
                        type: 'line',
                        color: color[x] + ')',

                        smooth: false,
                        areaStyle: {
                            normal: {

                                color: new echarts.graphic.LinearGradient(0, 0, 0, 2, [{
                                    offset: 0,
                                    color: color[x] + ', 0.3)'
                                }, {
                                    offset: 0.8,
                                    color: color[x] + ', 0)'
                                }], false),
                                shadowColor: 'rgba(0, 0, 0, 0.1)',
                                shadowBlur: 10
                            }
                        },
                        symbol: 'circle',
                        symbolSize: 5,
                        data: charts.value[i]
                    }
                    lineY.push(data)
                }

                lineY[0].markLine = {
                    silent: true,
                    data: [{
                        yAxis: 5
                    }, {
                        yAxis: 100
                    }, {
                        yAxis: 200
                    }, {
                        yAxis: 300
                    }, {
                        yAxis: 400
                    }]
                }
                let lyfxOption = {
                    title: {
                        textStyle: {
                            fontWeight: 'normal',
                            fontSize: 28,
                            color: '#F1F1F3'
                        },
                        left: '6%',
                        top: '4%'
                    },
                    tooltip: {
                        trigger: 'axis',
                        "textStyle": {
                            "fontSize": 28
                        }
                    },

                    legend: {
                        top: '4%',
                        data: charts.names,
                        textStyle: {
                            fontSize: 28,
                            color: 'F1F1F3'
                        },
                        right: '4%'
                    },
                    grid: {
                        top: '12%',
                        left: '4%',
                        right: '4%',
                        bottom: '10%',
                        containLabel: true
                    },
                    xAxis: {
                        show: true,
                        type: 'category',
                        boundaryGap: false,
                        data: charts.lineX,
                        nameTextStyle: {
                            fontSize: 28
                        },
                        axisLabel: {
                            textStyle: {
                                color: 'rgb(0,253,255,0.6)',
                                fontSize: 28
                            }
                            //   formatter: function(params) {
                            //       return params.split(' ')[0] + '\n' + params.split(' ')[1]
                            //   }
                        }
                    },
                    yAxis: {
                        show: true,
                        nameTextStyle: {
                            fontSize: 26
                        },
                        splitArea: {
                            show: false,
                            // areaStyle: {
                            //     color:"rgba(1, 22, 53, 1)"
                            //   }
                        },
                        name: charts.unit,
                        type: 'value',
                        axisLabel: {
                            formatter: '{value}',
                            textStyle: {
                                color: 'rgb(0,253,255,0.6)',
                                fontSize: 28
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: 'rgb(23,255,243,0.3)'
                            }
                        },

                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: 'rgb(0,253,255,0.6)'
                            }
                        }
                    },
                    series: lineY
                }
                this.setupEChart("lyfxOption", lyfxOption);
            },
            getAnEcharts01(res) {
                let datas = [
                    {
                        name: "婺城区",
                        value: 3018,
                        sum: 10,
                    },
                    {
                        name: "金东区",
                        value: 2176,
                        sum: 10,
                    },
                    {
                        name: "武义县",
                        value: 1954,
                        sum: 10,
                    },
                    {
                        name: "浦江县",
                        value: 1733,
                        sum: 50,
                    }
                ];
                getArrByKey = (datas, k) => {
                    let key = k || "value";
                    let res = [];
                    if (datas) {
                        datas.forEach(function (t) {
                            res.push(t[key]);
                        });
                    }
                    return res;
                };
                getSymbolData = (datas) => {
                    let arr = [];
                    for (var i = 0; i < datas.length; i++) {
                        arr.push({
                            value: datas[i].value,
                            symbolPosition: "end",
                        });
                    }
                    return arr;
                };
                opt = {
                    index: 0,
                };
                color = ["#A71A2B"];
                datas = datas.sort((a, b) => {
                    return b.value - a.value;
                });
                let option = {
                    backgroundColor: "transparent",
                    grid: {
                        top: "2%",
                        bottom: -15,
                        right: 0,
                        left: 120,
                        containLabel: true,
                    },
                    xAxis: {
                        show: false,
                    },
                    yAxis: [
                        {
                            triggerEvent: true,
                            show: true,
                            inverse: true,
                            data: getArrByKey(datas, "name"),
                            axisLine: {
                                show: false,
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                show: false,
                                interval: 0,
                                color: "#fff",
                                align: "left",
                                margin: 80,
                                fontSize: 25,
                                formatter: function (value, index) {
                                    return "{title|" + value + "}";
                                },
                                rich: {
                                    title: {
                                        width: 165,
                                    },
                                },
                            },
                        },
                        {
                            triggerEvent: true,
                            show: true,
                            inverse: true,
                            data: getArrByKey(datas, "name"),
                            axisLine: {
                                show: false,
                            },
                            splitLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                shadowOffsetX: "-20px",
                                color: '#fff',
                                align: "right",
                                verticalAlign: "center",
                                lineHeight: 30,
                                fontSize: 23,
                                formatter: function (value, index) {
                                    return (datas[index].value) + "件";
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "XXX",
                            type: "pictorialBar",
                            symbol:
                                "image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAMAAADWZboaAAAAZlBMVEUAAABe3uVe3+Vf3uVf3+Zf3uVg3+Zg3+Zf3+Vi4OZh4OZg3+Z86/Bh3+Zi4Odj4Odi4OZ86/B76/B86/Bj4ed56+9x5+xn4umB7/N87PB36e+A7/N+7fF/7vJ/7vJ+7fGA7/OB7/PReX+lAAAAIXRSTlMABQkVDREmIhk3MR10LEFFPHh7cUprXE35h2XnqMLAp+mHAG9cAAAB5ElEQVRIx83WjU7CMBQFYIoiKMqU/XUboHv/l/Tce7t2XamDNSacETEmX86tlK2rx4py150o+MstMBLwWRfHKo6JCVxLnvmFGBjFQ58oF1//sUZhGy/ClSTWObgnL4O+bkeN4nY2okfNMbkRt9/vtxz8InoTsWplJSCzFxPmO8+GpSIByX3YQAuGDWtRKhKjCnxDXhF6Z4yxnZ20Wgko7BMRDmxtSGVaI4kdTIgb+zTYoJQlIMlDlmUFgrcDWWC201qSayqlTkiCddWWeV62VU0YlnpRi9VOKaSUsiyq/N0krwq2Ugt7lVpZl5BfHNiytjagMi+XYp0kCR45hMlivVQrE/uU5pXSrCB5bM6d1t2lOZItMqmliT3q5uVxqxzyW/ccfYLNKx7ZTeykMvNyac2yt2Fbc61MHLSC0rwoxbiNdlQ3GBm1NLHQsHUrtEXppR/ljNpW6DbSCoqlFiVoN6YdaFlgsSFVPs1BdT8OaB5QyQzVcaqWDows/zepxR8ObLglTrdtCRVuRNj4Rrxh+//0ke2f8KVL+Kon3GCSbmsJN9OUW3j6g0Ns+LgCij2u0h+Sghc8mlMPBMgdx5DFh59VmOVHrvmDnoNxCz3J7MFWsMuaLyR089xz/xhlfijvwutR8gv3zk6BLUUeCgAAAABJRU5ErkJggg==",
                            symbolSize: [50, 50],
                            symbolOffset: [20, 0],
                            z: 12,
                            itemStyle: {
                                normal: {
                                    color: "#14b1eb",
                                },
                            },
                            data: getSymbolData(datas),
                        },
                        {
                            name: "条",
                            type: "bar",
                            showBackground: true,
                            barBorderRadius: 10,
                            yAxisIndex: 0,
                            data: datas,
                            barWidth: 10,
                            // align: left,
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(
                                        0,
                                        0,
                                        1,
                                        0,
                                        [
                                            {
                                                offset: 0,
                                                color: "#00c0ff",
                                            },
                                            {
                                                offset: 0.7,
                                                color: "#00c0ff",
                                            },
                                            {
                                                offset: 1,
                                                color: "#5EDEE5",
                                            },
                                        ],
                                        false
                                    ),
                                    barBorderRadius: 10,
                                },
                                // color: '#A71A2B',
                                barBorderRadius: 4,
                            },
                            label: {
                                normal: {
                                    color: "#46AAD8",
                                    show: true,
                                    position: ['-100px', "-4px"],
                                    textStyle: {
                                        fontSize: 26,
                                    },
                                    formatter: function (a, b) {
                                        return a.name;
                                    },
                                },
                            },
                        },
                    ],
                };
                this.setupEChart("anEcharts01", option);
            },
            getrightzt(id, res) {
                let myChart = echarts.init(document.getElementById(id));
                let option = {
                    tooltip: {
                        trigger: "axis",
                        backgroundColor: "rgba(51, 51, 51, 0.7)",
                        borderWidth: 0,
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                        },
                        textStyle: {
                            color: "white",
                            fontSize: "24",
                        },
                    },
                    legend: {
                        orient: "horizontal",
                        // icon: "circle",
                        itemGap: 45,
                        textStyle: {
                            color: "#D6E7F9",
                            fontSize: 28,
                        },
                    },
                    grid: {
                        left: "5%",
                        right: "6%",
                        top: "20%",
                        bottom: "1%",
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: "category",
                            data: res.map((item) => { return item.name }),
                            axisLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)", // 颜色
                                    width: 1, // 粗细
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,

                                textStyle: {
                                    color: "#D6E7F9",
                                    fontSize: 28,
                                },
                                formatter: function (params) {
                                    var newParamsName = '' // 最终拼接成的字符串
                                    var paramsNameNumber = params.length // 实际标签的个数
                                    var provideNumber = 2 // 每行能显示的字的个数
                                    var rowNumber = Math.ceil(paramsNameNumber / provideNumber) // 换行的话，需要显示几行，向上取整
                                    /**
                                     * 判断标签的个数是否大于规定的个数， 如果大于，则进行换行处理 如果不大于，即等于或小于，就返回原标签
                                     */
                                    // 条件等同于rowNumber>1
                                    if (paramsNameNumber > provideNumber) {
                                        /** 循环每一行,p表示行 */
                                        for (var p = 0; p < rowNumber; p++) {
                                            var tempStr = ''// 表示每一次截取的字符串
                                            var start = p * provideNumber // 开始截取的位置
                                            var end = start + provideNumber // 结束截取的位置
                                            // 此处特殊处理最后一行的索引值
                                            if (p === rowNumber - 1) {
                                                // 最后一次不换行
                                                tempStr = params.substring(start, paramsNameNumber)
                                            } else {
                                                // 每一次拼接字符串并换行
                                                tempStr = params.substring(start, end) + '\n'
                                            }
                                            newParamsName += tempStr // 最终拼成的字符串
                                        }
                                    } else {
                                        // 将旧标签的值赋给新标签
                                        newParamsName = params
                                    }
                                    return newParamsName
                                }
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: "     单位：起",
                            type: "value",
                            nameTextStyle: {
                                fontSize: 24,
                                color: "#D6E7F9",
                                padding: 5,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.4)",
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 28,
                                    color: "#D6E7F9",
                                },
                            },
                        },
                    ],
                    series: [{
                        name: "交通事故总数",
                        type: "bar", // 直线ss
                        yAxisIndex: 0,
                        smooth: false,
                        barWidth: '20%',
                        symbolSize: 10,
                        // barGap: "-100%",//实现两个数据在一个柱子上面显示
                        itemStyle: {
                            normal: {
                                color: "#0087EC",
                            },
                        },
                        data: res.map((item) => { return item.value0 }),
                    }, {
                        name: "经济损失",
                        type: "bar", // 直线ss
                        yAxisIndex: 0,
                        barWidth: '20%',
                        smooth: false,
                        symbolSize: 10,
                        itemStyle: {
                            normal: {
                                color: "#68BBC4",
                            },
                        },
                        data: res.map((item) => { return item.value1 }),
                    }, {
                        name: "重大交通事故总数",
                        type: "bar", // 直线ss
                        yAxisIndex: 0,
                        barWidth: '20%',
                        smooth: false,
                        symbolSize: 10,
                        itemStyle: {
                            normal: {
                                color: "#58A55C",
                            },
                        },
                        data: res.map((item) => { return item.value2 }),
                    }
                    ],
                };
                myChart.setOption(option)
                myChart.getZr().on('mousemove', param => {
                    myChart.getZr().setCursorStyle('default')
                })
            },

        }
    })
</script>