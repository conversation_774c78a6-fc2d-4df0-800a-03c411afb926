@charset "UTF-8";
[v-cloak]{
display: none;

}
/* 隔离点 */

.glnr-container .top{
  width: 100%;
  height: 200px;
  display: flex;
  justify-content: space-between;
  margin: 50px 0;
}
.glnr-container .top .top-item{
  width: 48%;
	height: 165px;
  background-image: url(../img/fxry-gldBg.png);
  background-repeat: no-repeat;
  display: flex;
  padding: 20px;
  box-sizing: border-box;
}
.top-item .left{
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: solid 1px #4b6c8b;
}
.top-item .left .text{
  display: flex;
  flex-direction: column;
  margin-left: 20px;
}
.top-item .left .num{
  font-size: 27px;
  background-image: linear-gradient(180deg, rgb(249, 253, 255),#bbe6ff, #06a4ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.top-item .left .num>span{  
  background-image: linear-gradient(180deg, rgb(249, 253, 255), #bbe6ff,#007ac0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 66px;
  margin-right: 10px;
  font-weight: bold;
}

.top-item .right{  
  width: 50%;
  padding-left: 50px;
}

.top-item .right .text{
  width: 100%;
  height: 50%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.top-item .right .text>span{
  font-size: 32px;
  color: #77b3f1;
}
.top-item .right .text .num{
  font-size: 24px;
  background-image: linear-gradient(180deg, rgb(249, 253, 255), #bbe6ff,#81d1ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.top-item .right .text .num>span{
  font-size: 40px;
  font-weight: bold;
}


/* 跨省协助 */
.ksxz-container{
  width: 100%;
  margin: 20px 0;
}
.ksxz-container .top{
  width: 100%;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 50px;
}
.ksxz-container .bottom ,.fxry_container .bottom{
  width: 100%;
  height: 510px;
}
.R1_title p {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #d6e7f9;
  font-size: 32px;
}

.R1_title .title_content {
  margin-right: 30px;
}
.R1_title .font_content{
  background-image: linear-gradient(180deg, rgb(218, 244, 254), rgb(255, 184, 62));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}
.red-font{
  background-image: linear-gradient(180deg, rgb(235, 249, 255), rgba(255, 204, 204, 0.76),rgba(255, 38, 38, 0.76),rgba(255, 0, 0, 0.76)) !important;
}
.R1_title .bg_num {
  display: inline-block;
  width: 50px;
  height: 74px;
  font-size: 70px;
  margin: 0 10px 0 0;
  text-align: center;
  line-height: 80px;
  padding-bottom: 16px;
  background-color: #1b3a5d;
}

.R1_title .circle {
  font-size: 40px;
  margin: 0 14px 0 4px;
}

.R1_title .bg_title {
  /* width: 145px; */
  height: 60px;
  font-style: normal;
  text-align: center;
  line-height: 60px;
  margin-left: 21px;
  /* background: url(../img/traffic_local/4级.png) no-repeat left center; */
}

.left_2 {
  width: 100%;
  height: 78px;
  background-color: #0f3c67;
  display: flex;
  align-items: center;
}

.left_2>div {
  font-family: SourceHanSansCN-Medium;
  font-size: 34px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 78px;
  letter-spacing: 0px;
  color: #77b3f1;
  flex: 0.333;
  text-align: center;
}

.par_box {
  height: calc(100% - 78px);
  overflow: hidden;
  width: 100%;
}

.left_b_box  {
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

.left_b_box::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */

}

.left_b_box::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;

}

.left_3 {
  width: 100%;
  height: 70px;
  background-color: #153256;
  margin-top: 2px;
  display: flex;
  align-items: center;
  display: flex;
  justify-content: center;
}

.left_3>div {
  font-family: SourceHanSansCN-Medium;
  font-size: 36px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #d6e7f9;
  flex: 0.33333;
  text-align: center;
}


/* 原先的内容 */
.fxry_app_box {
  position: relative;
  box-sizing: border-box;
  width: 2045px;
  height: 1850px;
  background-image: url("../img/bg.png");
  background-size: 100% 100%;
  padding: 10px 55px 30px;
}

.fxry_app_box .fxry_header {
  width: 1935px;
  display: flex;
  height: 130px;
  align-items: center;
  justify-content: space-between;
  background: url("../img/一级标题3.png") no-repeat;
  background-position: 0 55px;
}

.fxry_app_box .fxry_header .title {
  font-size: 54px;
  /**background-image: -webkit-linear-gradient(top, #ffffff, #3883ff);**/
  background: linear-gradient(to bottom, #ffffff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  padding-bottom: 10px;
}

.fxry_app_box .fxry_header .title .title_icon_one {
  display: inline-block;
  margin-right: 10px;
  width: 78px;
  height: 75px;
  vertical-align: bottom;
  background-image: url("../img/一级标题1.png");
  background-size: 100% 100%;
}

.fxry_app_box .fxry_header .title_hr {
  position: relative;
  /**left: 30px;**/
  width: 50%;
  height: 21px;
  background-image: url("../img/一级标题2.png");
  background-size: 100% 100%;
}


.fxry_app_box .fxry_container .qsfx {
  padding-top: 15px;
  padding-bottom: 15px;
}

.fxry_app_box .fxry_container .qsfx .qsfx_head {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fxry_app_box .fxry_container .qsfx .qsfx_head h2 {
  font-size: 40px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.fxry_app_box .fxry_container .qsfx .qsfx_head i {
  display: inline-block;
}

.fxry_app_box .fxry_container .qsfx .qsfx_head .icon_left {
  width: 142px;
  height: 53px;
  background: url("../img/二级标题左.png") no-repeat center;
  background-size: 100% 100%;
  margin-right: 30px;
}

.fxry_app_box .fxry_container .qsfx .qsfx_head .icon_right {
  width: 142px;
  height: 53px;
  background: url("../img/二级标题右.png") no-repeat center;
  background-size: 100% 100%;
  margin-left: 30px;
}





.cdjdyfxList {
  display: flex;
  flex-wrap: wrap;
  /* padding-top: 50px; */
}
.cdjdyfxList .xisList {
  background-color: rgba(16, 38, 70);
  width: 49%;
  /* margin-bottom: 100px; */
  margin-right: 1%;
}
.cdjdyfxList .xisList>h4 {
  background: rgba(38, 71, 107);
  height: 80px;
  font-size: 36px;
  line-height: 80px;
  color: #ffffff;
}
.cdjdyfxList .xisList>p {
  height: 100px;
  font-size: 36px;
  line-height: 100px;
  font-size: 40px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.fxry_app_box .fxry_container .cdjdyfx .cdjdyfx_item .cdjdyfx_item_title {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 20px;
}

.fxry_app_box
  .fxry_container
  .cdjdyfx
  .cdjdyfx_item
  .cdjdyfx_item_title
  .point {
  color: #ffffff;
  font-size: 32px;
  white-space: nowrap;
}

.fxry_app_box
  .fxry_container
  .cdjdyfx
  .cdjdyfx_item
  .cdjdyfx_item_title
  .jdbackground {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 360px;
  height: 70px;
  background: url("../img/zhcg/组2400.png");
  background-size: 100% 100%;
}

.fxry_app_box
  .fxry_container
  .cdjdyfx
  .cdjdyfx_item
  .cdjdyfx_item_title
  .jdbackground
  .normplant {
  font-size: 54px;
  background-image: -webkit-linear-gradient(top, #ffffff, #ffcf7b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  margin-top: -20px;
}

.fxry_app_box
  .fxry_container
  .cdjdyfx
  .cdjdyfx_item
  .cdjdyfx_item_title
  .jdbackground
  .normplant
  span {
  font-size: 28px;
}

.fxry_app_box .fxry_container .cdjdyfx .cdjdyfx_item .wtputopt_title {
  width: 940px;
  box-sizing: border-box;
  padding: 40px 25px 0;
  color: #ffffff;
  font-size: 28px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table {
  color: #ffffff;
  font-size: 28px;
  margin-top: 40px;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table i {
  display: inline-block;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .online {
  color: #ffffff;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .online i {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #ffffff;
  vertical-align: middle;
  margin-right: 10px;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .outline {
  color: red;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .outline i {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: red;
  vertical-align: middle;
  margin-right: 10px;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .blue {
  height: 60px;
  background: #26476b;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .black {
  height: 60px;
  background: #0c2948;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table tr {
  margin-bottom: 20px;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .cell {
  font-weight: normal;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table th {
  width: 20%;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .el-table__row td {
  width: 20%;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .el-table__row .right_cell {
  width: 40%;
}

.el-table__body-wrapper::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  background-color: skyblue;
  background-image: -webkit-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #ededed;
  border-radius: 5px;
}
.qsfenxbac{
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 360px;
  height: 270px;
  background: url('../img/adm/组1062076.png');
  background-size: 80% 48%;
  background-repeat: no-repeat;
  background-position: 50% 80%;
}

.zlContent p {
  width: 300px;
  color: #d6e7f9!important;
  font-size: 33px;
  padding: 20px;
}

.weekList {
  display: flex;
  padding: 0 70px;
}
.weekList .weekCont {
  width: 33%;
  display: flex;
}
.weekList .weekCont .contLis {
  
}
.weekList .weekCont .contLis h6{
  font-size: 28px;
  color: #d6e7f9!important;
  width: 180px;
}
.weekList .weekCont .contLis p{
  width: 180px;
  font-size: 40px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  padding: 0;
}

.botList {
  padding: 0 70px;
}

.botList ul li{
  display: flex; 
}
.botList ul li div {
  margin-bottom: 18px;
  margin-right: 18px;
  border-top: 6px solid #29608f;
  display: flex;
  width: 50%;
  background: rgba(14, 37, 66, 0.2);
}
.botList ul li div p:nth-child(2){
  width: 500px;
  font-size: 40px;
  line-height: 92px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  padding: 0;
}