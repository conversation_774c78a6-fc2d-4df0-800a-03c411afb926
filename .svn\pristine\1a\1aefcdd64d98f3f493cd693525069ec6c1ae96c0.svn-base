<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>交通运输-弹框2</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/jslib/vue-count-to.min.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/datav.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
    </head>
    <style>
        [v-cloak] {
            display: none;
        }

        html,
        body,
        ul,
        p {
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .container {
            width: 3380px;
            height: 400px;
            box-sizing: border-box;
            padding: 20px;
            background: url("/img/left-bg.png") no-repeat;
            background-size: 100% 100%;
            display: flex;
        }

        .box {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: space-evenly;
        }

        .box-item {
            width: 239px;
            height: 278px;
            background: url("/static/citybrain/shgl/img/doing/dfx.png");
            background-size: 100% 100%;
            position: relative;
            opacity: 0.6;
        }

        .active {
            opacity: 1;
        }

        .box-item > img {
            width: 50px;
            height: 50px;
            top: 40px;
            left: 95px;
            position: absolute;
        }

        .box-item > p:nth-child(2) {
            position: absolute;
            top: 180px;
            width: 100%;
            text-align: center;
        }

        .box-item:nth-child(1),
        .box-item:nth-child(9) {
            top: 100px;
        }

        .box-item:nth-child(2),
        .box-item:nth-child(8) {
            top: 75px;
        }

        .box-item:nth-child(3),
        .box-item:nth-child(7) {
            top: 50px;
        }

        .box-item:nth-child(4),
        .box-item:nth-child(6) {
            top: 25px;
        }

        .box-item:nth-child(5) {
            top: 5px;
        }
    </style>

    <body>
        <div id="app" class="container">
            <!-- 底部地图切换 -->
            <div class="box">
                <div
                    class="box-item"
                    v-for="(item,index) in partList"
                    :key="index"
                    :class="currentTab===index?'active':''"
                    @click="clickTab(index,item)"
                >
                    <img
                        :src="`/static/citybrain/shgl/img/doing/${item.icon1}.png`"
                        alt=""
                        style="width: 50px; height: 50px"
                    />
                    <p class="s-c-white s-font-30">{{item.name}}</p>
                </div>
            </div>
        </div>
    </body>

    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>

    <script>
        var vm = new Vue({
            el: "#app",
            data() {
                return {
                    partList: [],
                    currentTab: 0,
                    currentType: 1,
                    currentIcon: "公交站",
                    isShow: true,
                };
            },
            mounted() {
                this.init();
                this.getStation();
            },
            methods: {
                init() {
                    top.document.getElementById("map").contentWindow.Work.change3D(7);

                    $api("shgl_doing_jtys2").then((res) => {
                        this.partList = res;
                    });
                },
                //点击底部点位切换
                clickTab(index, data) {
                    this.currentTab = index;
                    this.currentType = data.type;
                    this.currentIcon = data.icon;
                    if (top.window.map.TDT_TITLE_ID) {
                        top.window.map.TDT_TITLE_ID.remove();
                    }
                    this.getStation();
                },
                //根据点击type查询点位
                getStation() {
                    this.rmPoint();
                    $api("shgl_doing_jtysdw").then((res) => {
                        console.log(res);
                        let data;
                        data = res.filter((item) => {
                            return item.type === this.currentType;
                        });
                        let pointData = [];
                        data.forEach((item) => {
                            let str = {
                                data: {
                                    pointId: "jtys",
                                    obj: item,
                                },
                                point: item.lnglat,
                            };
                            pointData.push(str);
                        });
                        console.log(this.currentIcon);
                        top.document.getElementById("map").contentWindow.Work.funChange(
                            JSON.stringify({
                                funcName: "pointLoad", //功能名称
                                pointType: this.currentIcon, //点位类型图标
                                pointId: "00000" + this.currentType,
                                setClick: true,
                                pointData: pointData,
                                imageConfig: { iconSize: 1 },
                                size: [0.01, 0.01, 0.01, 0.01],
                                popup: {
                                    offset: [50, -100],
                                },
                            })
                        );
                    });
                },
                //根据点位点击查询详情
                getStationDetail() {},
                rmPoint() {
                    top.document.getElementById("map").contentWindow.Work.funChange(
                        JSON.stringify({
                            funcName: "rmPoint",
                            pointId: "", //传id清除单类，不传清除所有
                        })
                    );
                    if (top.document.getElementById("map").contentWindow.egs1.contentWindow.map.TDT_TITLE_ID) {
                        top.document.getElementById("map").contentWindow.egs1.contentWindow.map.TDT_TITLE_ID.remove();
                    }
                },
            },
        });
    </script>
</html>
