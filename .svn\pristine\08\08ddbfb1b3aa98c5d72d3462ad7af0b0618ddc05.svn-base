<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>党建统领右边</title>
  <script src="/static/citybrain/csdn/Vue/vue.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/jquery-3.4.1.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
  <link rel="stylesheet" href="../css/djtl-popup-right.css">
  <script src="/static/js/comjs/s.min.vue.js"></script>


</head>

<body>
  <div id="djtl-left">
    <!-- 标题 -->
    <!-- <div class="djtl-title">
      <span class="blue-color">统战工作</span>
      <span style="font-size: 48px;opacity: 0.86;position: absolute;right: 50px;">更多</span>
    </div> -->
    <nav style="padding: 30px 0 0 10px;">
      <s-header-title title="统战工作" htype="1" style="width: 95%;"></s-header-title>
    </nav>
    <!-- 总人数 -->
    <div class="sum-text">
      <!-- {{tzgzTopData[0].ymbq}}:<span> {{tzgzTopData[0].value}} </span>{{tzgzTopData[0].unit}} -->
      <div style="margin-top:-50px">{{tzgzTopData[0].ymbq}}:</div>
      <div class="main-top-span" v-for="(item,i) in tzgzTopData[0].value.split('')">
        <p class="sum-p s-c-yellow-gradient">{{item}}</p>
      </div>
      <div  style="margin-top:-50px">{{tzgzTopData[0].unit}}</div>
      <!-- <div class="btn-class"><span class="blue-color">教职人员</span></div> -->
    </div>
    <!--左右的图表 -->
    <div class="content">
      <div class="left">
        <p style="margin-bottom: 20px;color: #fff;font-size: 32px;"><i class="sanicon"></i>各民族党派人数</p>

        <!-- <div class="two-title"><span class="blue-color">全市民主党派男、女比例</span></div> -->
        <!-- 表格 -->
        <!-- <div class="table">
          <div class="table-th">
            <div class="th">名称</div>
            <div class="th">人数</div>
          </div>
          <div class="table-tr">
            <div class="tr " v-for="item in tableData">
              <div class="td blue-color">{{item.name}}</div>
              <div class="td blue-color">{{item.value}}</div>
            </div>
          </div>

        </div> -->
        <div class="rightecharts01" id="rightecharts01" style="width: 90%; height: 380px"></div>
      </div>
      <div class="right">
        <p style="margin-bottom: 20px;color: #fff;font-size: 32px;"><i class="sanicon"></i>各民族党派占比</i></p>

        <!-- <div class="two-title"><span class="blue-color">各民主党派比例</span></div> -->
        <div id="pieCharts0" style="width:1108px;height: 350px;"></div>

      </div>
    </div>

    <!-- 下面 -->
    <!-- 标题 -->
    <!-- <div class="djtl-title" style="margin-top: 80px;">
      <span class="blue-color">团建发展</span>
      <span style="font-size: 48px;opacity: 0.86;position: absolute;right: 50px;">更多</span>
    </div> -->
    <nav style="padding: 30px 0 0 10px;">
      <s-header-title title="团建发展" htype="1" style="width: 95%;"></s-header-title>
    </nav>
    <!-- 总人数 -->
    <!-- <div class="sum-text">
      <div style="display: flex;justify-content: space-around;">
        <span style="display: inline-block;" v-for="item in tjfzTopData">
          {{item.ymbq}}：<span> {{item.value}} </span>{{item.unit}}
        </span>
      </div>
    </div> -->
    <!--左右的图表 -->
    <div class="content">
      <div class="left" style="padding-top: 50px;box-sizing: border-box;">
        <div class="sum-text" style='left:119px;margin-top:0'>
          <!-- {{tzgzTopData[0].ymbq}}:<span> {{tzgzTopData[0].value}} </span>{{tzgzTopData[0].unit}} -->
          <div style="margin-top:-50px;">{{tjfzTopData[0].ymbq}}:</div>
          <div class="main-top-span" v-for="(item,i) in tjfzTopData[0].value.split('')">
            <p class="sum-p s-c-yellow-gradient">{{item}}</p>
          </div>
          <div style="margin-top:-50px;">{{tjfzTopData[0].unit}}</div>
          <!-- <div class="btn-class"><span class="blue-color">教职人员</span></div> -->
        </div>
        <p style="margin-bottom: 20px;color: #fff;font-size: 32px;"><i class="sanicon"></i>各领域团员分布</p>

        <!-- <div class="two-title"><span class="blue-color">全领域团员比例</span></div> -->
        <div id="pieCharts1" style="width:1108px;height: 350px;"></div>
      </div>
      <div class="right" style="padding-top: 50px;box-sizing: border-box;">
        <div class="sum-text" style='left:82px;margin-top:0'>
          <!-- {{tzgzTopData[0].ymbq}}:<span> {{tzgzTopData[0].value}} </span>{{tzgzTopData[0].unit}} -->
          <div style="margin-top:-50px;">{{tjfzTopData[1].ymbq}}:</div><div class="main-top-span" v-for="(item,i) in tjfzTopData[1].value.split('万')[0]">
            <p class="sum-p s-c-yellow-gradient">{{item}}</p>
          </div>
          <div style="margin-top:-50px;">{{tjfzTopData[1].unit}}</div>
          
          <!-- <div class="btn-class"><span class="blue-color">教职人员</span></div> -->
        </div>
        <p style="margin-bottom: 20px;color: #fff;font-size: 32px;"><i class="sanicon"></i>各领域团组织分布</p>

        <!-- <div class="two-title"><span class="blue-color">各县（市、区）团员比例</span></div> -->
        <div id="pieCharts2" style="width:1108px;height: 350px;"></div>

      </div>
    </div>



  </div>
  <script>
    new Vue({
      el: '#djtl-left',
      data: {
        tzgzTopData: [{
          unit: "",
          value: "",
          ymbq: "",
        }],
        tjfzTopData: [{
          unit: "",
          value: "",
          ymbq: "",
        },{
          unit: "",
          value: "",
          ymbq: "",
        }],
        chartData0: [
          {
            name: '民革',
            text: '数据',
            value: 60
          },
          {
            name: '民盟',
            text: '数据',
            value: 60
          },
          {
            name: '民建',
            text: '2万  30%',
            value: 50
          },
          {
            name: '民进',
            text: '数据',
            value: 60
          },
          {
            name: '农工党',
            text: '数据',
            value: 60
          },
          {
            name: '致公党',
            text: '数据',
            value: 40
          },
          {
            name: '九三学社',
            text: '数据',
            value: 60
          },
          {
            name: '台盟',
            text: '数据',
            value: 60
          },
        ],
        chartData1: [
          {
            name: '学生团员',
            text: '数据',
            value: 60
          },
          {
            name: '企业团员',
            text: '数据',
            value: 60
          },
          {
            name: '乡镇(村)团员',
            text: '2万  30%',
            value: 50
          },
          {
            name: '机关事业单位',
            text: '数据',
            value: 60
          },
          {
            name: '城市街道(社区)团员',
            text: '数据',
            value: 60
          },
          {
            name: '社会组织和其他领域',
            text: '数据',
            value: 40
          }
        ],
        chartData2: [
          {
            name: '学生团组织',
            text: '数据',
            value: 60
          },
          {
            name: '企业团组织',
            text: '数据',
            value: 60
          },
          {
            name: '乡镇(村)团组织',
            text: '2万  30%',
            value: 50
          },
          {
            name: '机关事业单位团组织',
            text: '数据',
            value: 60
          },
          {
            name: '城市街道(社区)团组织',
            text: '数据',
            value: 60
          },
          {
            name: '社会组织和其他领域团组织',
            text: '数据',
            value: 40
          }
        ],
        tableData: []
      },
      mounted() {
        this.initFun()
        // this.pieChart('pieCharts2', this.chartData2,25,'15%',"5%","个")
      },
      methods: {
        initFun() {
          let that = this
          $api('djtlLeft001', { code: '统战工作' }).then((res) => {
            that.tzgzTopData = res
          })
          $api('djtlLeft001', { code: '团建发展' }).then((res) => {
            that.tjfzTopData = res
          })
          $api('djtlLeft005', { belong_to: "各民族党派人数" }).then((res) => {
            let unit = ""
            let data = res.map(item => {
              unit = item.total.substr(item.total.length - 1, 1)
              let str = {
                name: item.name,
                value: item.total.replace(unit, "")
              }
              return str
            })
            that.pieChart('pieCharts0', data, 60, '15%', "20%", unit)
          })
          $api('djtlLeft006', { belong_to: "各领域团员分布" }).then((res) => {
            let unit = ""
            let data = res.map(item => {
              unit = item.total.substr(item.total.length - 2, 2)
              let str = {
                name: item.name,
                value: item.total.replace(unit, "")
              }
              return str
            })
            that.pieChart('pieCharts1', data, 25, '25%', "5%", unit)
          })
          $api('djtlLeft006', { belong_to: "各领域团员分布" }).then((res) => {
            let unit = ""
            let data = res.map(item => {
              unit = item.total.substr(item.total.length - 2, 2)
              let str = {
                name: item.name,
                value: item.total.replace(unit, "")
              }
              return str
            })
            that.pieChart('pieCharts1', data, 25, '25%', "5%", unit)
          })
          $api('djtlLeft006', { belong_to: "各领域团组织分布" }).then((res) => {
            let unit = ""
            let data = res.map(item => {
              unit = item.total.substr(item.total.length - 2, 2)
              let str = {
                name: item.name,
                value: item.total.replace(unit, "")
              }
              return str
            })
            // that.getEcharts003("pieCharts2", data, unit);
            that.pieChart('pieCharts2', data, 25, '10%', "5%", unit)
          })
          $api('djtlLeft008', { belong_to: "各民族党派人数" }).then((res) => {
            that.tableData = res
            that.getEcharts03(that.tableData)
          })
        },
        getEcharts003(dom, dataNew, unit) {
          let myEc = echarts.init(document.getElementById(dom));
          const option = {
            tooltip: {
              trigger: 'item',
              formatter: '{b}: <br/> {d}%',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
            },
            legend: {
              orient: 'vertical',
              left: '20%',
              bottom: '12%',
              icon: 'circle',
              itemGap: 50,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 26,
              },
              formatter: function (name) {
                var data = option.series[0].data //获取series中的data
                var total = 0
                var tarValue
                for (var i = 0, l = data.length; i < l; i++) {
                  total += data[i].value
                  if (data[i].name == name) {
                    tarValue = data[i].value
                  }
                }
                var p = (tarValue / total) * 100
                return name + ': ' + tarValue + unit
              },
            },
            series: [
              {
                name: dom === 'charts_lxfl' ? '类型分类' : '事项分类',
                type: 'pie',
                radius: ['30%', '50%'],
                center: ['10%', '45%'],
                roseType: 'area',
                itemStyle: {
                  borderRadius: 5,
                },
                label: {
                  show: false,
                },
                data: dataNew,
              },
            ],
          }
          myEc.setOption(option)
          myEc.getZr().on('mousemove', param => {
            myEc.getZr().setCursorStyle('default')
          })
        },

        getEcharts03(data) {
          let echarts0 = echarts.init(document.getElementById('rightecharts01'));
          let that = this;

          let xData = [];
          let yData = [];
          for (let item of data) {
            xData.push(item.name);
            yData.push(item.value.split("人")[0]);
          }
          let option = {
            grid: {
              top: "7%",
              left: "1%",
              bottom: "2%",
              right: "1%",
              containLabel: true,
            },
            tooltip: {
              show: true,
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color:"#fff",
                fontSize: 30,
              },
              formatter: "{b}: <br/> {c}万人",
            },
            animation: false,
            xAxis: [
              {
                type: "category",
                data: xData,
                axisTick: {
                  alignWithLabel: true,
                },
                nameTextStyle: {
                  color: "#82b0ec",
                },
                axisLine: {
                  show: false,
                  lineStyle: {
                    color: "#82b0ec",
                  },
                },
                axisLabel: {
                  textStyle: {
                    color: "#fff",
                    fontSize: 28,
                  },
                  margin: 30,
                },
              },
            ],
            yAxis: [
              {
                show: false,
                type: "value",
                axisLabel: {
                  textStyle: {
                    color: "#fff",
                  },
                },
                splitLine: {
                  lineStyle: {
                    color: "#0c2c5a",
                  },
                },
                axisLine: {
                  show: false,
                },
              },
            ],
            series: [
              {
                name: "",
                type: "pictorialBar",
                symbolSize: [70, 10],
                symbolOffset: [0, -6], // 上部椭圆
                symbolPosition: "end",
                z: 12,
                // "barWidth": "0",
                label: {
                  normal: {
                    show: true,
                    position: "top",
                    formatter: "{c}人",
                    fontSize: 28,
                    fontWeight: "bold",
                    color: "#34DCFF",
                  },
                },
                color: "#2DB1EF",
                data: yData,
              },
              {
                name: "",
                type: "pictorialBar",
                symbolSize: [70, 10],
                symbolOffset: [0, 7], // 下部椭圆
                // "barWidth": "20",
                z: 12,
                color: "#2DB1EF",
                data: yData,
              },
              {
                name: "",
                type: "pictorialBar",
                symbolSize: function (d) {
                  return d > 0 ? [110, 20] : [0, 0];
                },
                symbolOffset: [-2, 18], // 下部内环
                z: 10,
                itemStyle: {
                  normal: {
                    color: "transparent",
                    borderColor: "#25759c",
                    borderType: "solid",
                    borderWidth: 4,
                  },
                },
                data: yData,
              },
              {
                name: "",
                type: "pictorialBar",
                symbolSize: [150, 30],
                symbolOffset: [-2, 25], // 下部外环
                z: 10,
                itemStyle: {
                  normal: {
                    color: "transparent",
                    borderColor: "#25759c",
                    borderType: "solid",
                    borderWidth: 4,
                  },
                },
                data: yData,
              },
              {
                type: "bar",
                //silent: true,
                barWidth: "70",
                barGap: "10%", // Make series be overlap
                barCateGoryGap: "10%",
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 0.7, [
                      {
                        offset: 0,
                        color: "#0B3147",
                      },
                      {
                        offset: 1,
                        color: "#38B2E6",
                      },
                    ]),
                    opacity: 0.8,
                  },
                },
                data: yData,
              },
            ],
          };
          echarts0.setOption(option);
          echarts0.getZr().on("mousemove", (param) => {
            echarts0.getZr().setCursorStyle("default");
          });
        },
        pieChart(dom, dataNew, itemGap, legendRight, legendTop, dataNuit) {
          const myCharts2 = echarts.init(document.getElementById(dom))
          let imgUrl = '../img/djtl-left/echarts-bg.png'
          const option = {
            tooltip: {
              trigger: 'item',
              formatter: '{b}:{d}%',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
            },
            legend: {
              orient: 'vertical',
              right: legendRight,
              top: legendTop,
              icon: 'circle',
              itemGap: itemGap,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 30,
                rich: {
                  percent: {
                    color: "#ffd79b",
                    fontSize: 28,
                  },
                },
              },
              // formatter: function (name) {
              //   for(let i=0;i<dataNew.length;i++){
              //     if(dataNew[i].name==name){
              //       return name + ' ' + '    ' +'{percent|'+dataNew[i].text+'}'+' '
              //     }
              //   }
              // },
              formatter: function (name) {
                var data = option.series[0].data //获取series中的data
                var total = 0
                var tarValue
                for (var i = 0, l = data.length; i < l; i++) {
                  total += Number(data[i].value)
                  if (data[i].name == name) {
                    tarValue = data[i].value
                  }
                }
                var p = (tarValue / total) * 100
                if (dom == 'pieCharts0') {
                  return name + ' ' + '{percent|' + p.toFixed(2) + '%}'
                }
                return name + ' ' + '{percent|' + tarValue + dataNuit + '}' + ' '
              },
            },
            graphic: [
              {
                z: 4,
                type: "image",
                id: "logo",
                left: "8.5%",
                top: "14%",
                z: -10,
                bounding: "raw",
                rotation: 0, //旋转
                origin: [50, 50], //中心点
                scale: [0.7, 0.7], //缩放
                style: {
                  image: imgUrl,
                  opacity: 1,
                },
              },
            ],
            series: [
              {
                name: "",
                type: 'pie',
                radius: ['50%', '70%'],
                center: ['20%', '50%'],
                itemStyle: {
                  normal: {
                    borderColor: "#0A1934",
                    borderWidth: 10
                  },
                },
                // tooltip: {
                //   trigger: "item",
                //   formatter: function (params) {
                //     return (
                //       params.marker+
                //       params.name +
                //       ":" +params.value
                //     );
                //   },
                // },
                label: {
                  show: false,
                },
                data: dataNew,
              },
            ],
          }
          myCharts2.setOption(option)
          myCharts2.getZr().on('mousemove', param => {
            myCharts2.getZr().setCursorStyle('default')
          })
        },

      }

    })
  </script>
</body>

</html>