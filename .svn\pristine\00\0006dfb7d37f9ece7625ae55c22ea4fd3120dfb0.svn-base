<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <script src="/static/citybrain/scjg/js/lib/vue.js"></script>
    <link
      rel="stylesheet"
      href="/static/citybrain/scjg/css/hjbh-right/common.css"
    />
    <link
      rel="stylesheet"
      href="/static/citybrain/scjg/css/hjbh-right/sigma.css"
    />
    <link
      rel="stylesheet"
      href="/static/citybrain/scjg/css/scztjg-middle.css"
    />
    <link rel="stylesheet" href="/elementui/css/elementui.css" />
    <script src="./echarts/echarts.js"></script>
    <script src="js/lib/jquery-3.4.1.min.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/elementui/js/elementui.js"></script>

    <style>
      .main {
        position: absolute;
        left: 2100px;
        top: 250px;
      }

      #left {
        position: absolute;
        left: 50px;
        width: 500px;
        font-size: 60px;
        height: 100px;
        line-height: 100px;
        letter-spacing: 2px;
        color: #d6e7f9;
      }

      input {
        width: 30px;
        height: 30px;
      }

      #right {
        position: absolute;
        left: 2750px;
        font-size: 60px;
        letter-spacing: 2px;
        color: #d6e7f9;
        width: 700px;
      }

      #right span {
        background: linear-gradient(to top, #f0b55f, #ffffff);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        font-size: 80px;
        margin-left: 20px;
        font-family: "DIN-Bold";
      }

      .middle-header {
        position: absolute;
        top: 0px;
        left: 615px;
        display: flex;
        width: 2093px;
        height: 144px;
        background-color: #03101f;
        border-style: solid;
        border-width: 2px;
        border-image-source: linear-gradient(0deg, #3975bb 0%, #22436a 100%);
        border-image-slice: 1;
        opacity: 0.8;
        align-items: center;
      }

      .middle-header .line {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 33%;
        padding: 20px 0 20px 40px;
      }

      .middle-header .line .text {
        font-size: 40px;
        margin-left: 20px;
        color: #fff;
      }

      .middle-header .line .number {
        font-family: SourceHanSansCN-Medium;
        font-size: 60px;
        background: linear-gradient(to bottom, #ffffff, #ffc2c2, #ff4949);
        -webkit-background-clip: text;
        color: transparent;
        font-weight: 600;
        margin-left: 20px;
      }

      .bottomEcharts {
        position: absolute;
        /* top: 0px; */
        bottom: -1900px;
        left: 200px;
        display: flex;
        justify-content: space-around;
        width: 3000px;
        /* height: 144px; */
        background-color: #03101f;
        border-style: solid;
        border-width: 2px;
        border-image-source: linear-gradient(0deg, #3975bb 0%, #22436a 100%);
        border-image-slice: 1;
        opacity: 0.8;
        align-items: center;
      }

      .table {
        /* width: 640px; */
        font-size: 30px;
        color: #fff;
        border: 2px solid #0075a5;
        border-collapse: collapse;
      }

      .table th {
        line-height: 70px;
        background-color: #021e4855;
        border-bottom: 1px solid #34445f;
        border-right: 1px solid #34445f;
      }

      .table th:last-child {
        border-right: none;
      }

      .table tbody tr td {
        border-bottom: 1px solid #34445f;
        border-right: 1px solid #34445f;
      }

      .table tbody tr td:last-child {
        border-right: none;
        /* border-bottom: none; */
      }

      .table tbody tr:last-child {
        border-bottom: 2px solid #0075a5 !important;
      }

      .table tbody {
        text-align: center;
        background-color: #021e4855;
        color: #fff;
        line-height: 60px;
      }

      .titleContent {
        margin: 0 !important;
      }
    </style>
  </head>

  <body>
    <div class="main">
      <div id="app">
        <div class="toolbar">
          <el-tree
            ref="treeForm"
            node-key="id"
            :check-strictly="true"
            :data="treeData"
            :props="defaultProps"
            :check-on-click-node="true"
            show-checkbox
            @check="treeCheck"
            @check-change="checkChange"
          >
          </el-tree>
        </div>

        <div class="middle-header">
          <div class="line" v-for="item in rightData">
            <div class="text">{{item.name}}</div>
            <div
              style="
                display: flex;
                justify-content: flex-end;
                margin-top: 15px;
                align-items: center;
              "
            >
              <div class="number">
                {{item.num}}
                <!-- <dv-digital-flop
                  :config="{number:[item.num],style:{
                        fontSize: 80,
                        gradientColor: ['#f0b55f', '#ffffff'],
                        gradientParams: [0, 80, 0, 0],
                        gradientWith: 'fill',
                        fontWeight: 800
                  }}"
                  style="height: 80px"
                /> -->
              </div>
              <div class="text">{{item.unit}}</div>
            </div>
          </div>
        </div>
        <!-- <div id="left">
                    <div>
                        <input type="radio" name="main_market" value="all" id="all" />
                        <label for="all">全部市场主体</label>
                    </div>
                    <div>
                        <input type="radio" name="main_market" value="new" id="new" />
                        <label for="new">新增市场主体</label>
                    </div>
                    <div>
                        <input type="radio" name="main_market" value="quit" id="quit" />
                        <label for="quit">退出市场主体</label>
                    </div>
                </div>
                <div id="right">
                    <div v-for="item in rightData">
                        {{item.name}}
                        <span>{{item.num}}</span>
                        {{item.unit}}
                    </div>
                </div> -->

        <!-- 中间的弹窗 企业风险预测-->
        <div class="alt-qyfx" style="width: 3380px; height: 1804px; left: 30px">
          <div class="alt-top">
            <span>企业风险预测</span>
            <span class="del" @click="closeWindow()"></span>
          </div>

          <div class="s-flex s-rela" style="height: 1954px; top: 50px">
            <div class="s-flex-1 s-m-l-100" style="height: 100%">
              <header>
                <div class="s-flex s-col-center" style="height: 61px">
                  <img
                    class=""
                    src="/static/citybrain/scjg/img/scztjg/topTitle1.png"
                    alt="topTitle1"
                  />
                  <div class="titleContent" style="margin-top: 0">
                    区域预警企业数
                  </div>
                  <img
                    class="s-m-l-30"
                    src="/static/citybrain/scjg/img/scztjg/topTitle2.png"
                    alt="topTitle2"
                    style="width: 264px; height: 16px"
                  />
                  <!-- <img
                    class="s-m-l-15"
                    src="/static/citybrain/scjg/img/scztjg/topTitle3.png"
                    alt="topTitle3"
                  /> -->
                </div>
                <img
                  src="/static/citybrain/scjg/img/scztjg/topTitle4S.png"
                  alt="topTitle4"
                />
              </header>

              <div>
                <div class="table s-flex s-flex-wrap">
                  <div
                    class="td s-m-l-79"
                    :class="yjindex == index ?'td-active':''"
                    v-for="(item,index) in nameData"
                    :key="index"
                  >
                    <!-- <div
                    @click="getYjdetail(index,item.name)"
                    class="td s-m-l-79"
                    :class="yjindex == index ?'td-active':''"
                    v-for="(item,index) in nameData"
                    :key="index"
                  > -->
                    <span
                      :class="yjindex == index ?'num-active':'num-unactive'"
                      class="num"
                    >
                      {{item.num}}
                    </span>
                    <span class="name">{{item.name}}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="s-flex-2 s-m-l-60" style="height: 100%">
              <header>
                <div class="s-flex s-col-center" style="height: 61px">
                  <img
                    class=""
                    src="/static/citybrain/scjg/img/scztjg/topTitle1.png"
                    alt="topTitle1"
                  />
                  <div class="titleContent" style="margin-top: 0">
                    企业风险预警模型
                  </div>
                  <img
                    class="s-m-l-30"
                    src="/static/citybrain/scjg/img/scztjg/topTitle2.png"
                    alt="topTitle2"
                    style="height: 16px; width: 920px"
                  />
                  <!-- <img
                    class="s-m-l-15"
                    src="/static/citybrain/scjg/img/scztjg/topTitle3.png"
                    alt="topTitle3"
                  /> -->
                  <div
                    class="titleContent"
                    style="margin-top: 0; margin-left: 50px"
                  >
                    {{searchTableName}}
                  </div>
                </div>
                <img
                  class=""
                  src="/static/citybrain/scjg/img/scztjg/topTitle4.png"
                  alt="topTitle4"
                />
              </header>
              <div class="s-flex s-rela" style="left: -100px">
                <div class="s-flex-1 s-flex s-flex-wrap s-row-center">
                  <header class="subTitle">
                    <img
                      src="/static/citybrain/scjg/img/scztjg/subTitleLeft.png"
                      alt="subTitleLeft"
                    />
                    <div class="subTitleContent" style="margin: 0">
                      经营风险预警（次）
                    </div>
                    <img
                      src="/static/citybrain/scjg/img/scztjg/subTitleRight.png"
                      alt="subTitleRight"
                    />
                  </header>

                  <div
                    id="radarChart1"
                    style="width: 700px; height: 600px; margin-top: 20px"
                  ></div>
                </div>
                <div class="s-flex-1 s-flex s-flex-wrap s-row-center">
                  <header class="subTitle">
                    <img
                      src="/static/citybrain/scjg/img/scztjg/subTitleLeft.png"
                      alt="subTitleLeft"
                    />
                    <div class="subTitleContent" style="margin: 0">
                      信用风险预警（次）
                    </div>
                    <img
                      src="/static/citybrain/scjg/img/scztjg/subTitleRight.png"
                      alt="subTitleRight"
                    />
                  </header>
                  <div
                    id="radarChart3"
                    style="width: 700px; height: 600px; margin-top: 20px"
                  ></div>
                </div>
              </div>
              <header class="subTitle s-rela" style="left: -50px">
                <img
                  src="/static/citybrain/scjg/img/scztjg/subTitleLeft.png"
                  alt="subTitleLeft"
                />
                <div class="subTitleContent" style="margin: 0">
                  企业三色预警分数
                </div>
                <img
                  src="/static/citybrain/scjg/img/scztjg/subTitleRight.png"
                  alt="subTitleRight"
                />
              </header>

              <div class="s-flex s-rela s-text-center" style="left: -50px">
                <div class="s-flex-1 h-8">
                  <div class="table-title">60分及以下</div>
                  <div class="table-th">
                    <div class="s-flex-3">企业名称</div>
                    <div class="s-flex-1">分数</div>
                  </div>
                  <div class="table-tr" style="height: 550px">
                    <div class="tr" v-for="(item,index) in table1">
                      <div
                        class="s-flex-3 s-c-white"
                        :class="activeId == item.id ?'num-active':''"
                        @click="showXysjbtn(item.id )"
                      >
                        {{item.name}}
                      </div>
                      <div class="s-flex-1 s-c-white">{{item.num}}</div>
                    </div>
                  </div>
                </div>

                <div class="s-m-l-15 s-flex-1 h-8">
                  <div
                    class="table-title"
                    style="
                      background-image: url(/static/citybrain/scjg/img/scztjg/scztjg-60+.png);
                    "
                  >
                    60分-80分
                  </div>
                  <div class="table-th s-text-center">
                    <div class="s-flex-3">企业名称</div>
                    <div class="s-flex-1">分数</div>
                  </div>
                  <div class="table-tr" style="height: 550px">
                    <div
                      class="tr s-text-center"
                      v-for="(item,index) in table2"
                    >
                      <div
                        class="s-flex-3 s-c-white"
                        :class="activeId == item.id ?'num-active':''"
                        @click="showXysjbtn(item.id )"
                      >
                        {{item.name}}
                      </div>
                      <div class="s-flex-1 s-c-white">{{item.num}}</div>
                    </div>
                  </div>
                </div>
                <div class="s-m-l-15 s-flex-1 s-m-r-79 h-8">
                  <div
                    class="table-title"
                    style="
                      background-image: url(/static/citybrain/scjg/img/scztjg/scztjg-80+.png);
                    "
                  >
                    80分及以上
                  </div>
                  <div class="table-th">
                    <div v-for="(item,index) in tableName" class="th">
                      {{item.name}}
                    </div>
                  </div>
                  <div class="table-tr" style="height: 550px">
                    <div class="tr" v-for="(item,index) in table3">
                      <div
                        class="s-flex-3 s-c-white"
                        :class="activeId == item.id ?'num-active':''"
                        @click="showXysjbtn(item.id )"
                      >
                        {{item.name}}
                      </div>
                      <div class="s-flex-1 s-c-white">{{item.num}}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中间的弹窗 企业风险预测-->
        <!-- v-show="showQyfx1" -->
        <div class="alt-qyfx-table" v-show="showQyfx1">
          <div class="alt-top">
            <span>企业风险预测</span>
            <span class="del" @click="showQyfx1=false"></span>
          </div>
          <div class="cont">
            <div class="table-container">
              <div class="item-table" v-for="(item,index) in zxt_data">
                <header class="subTitle">
                  <img
                    src="/static/citybrain/scjg/img/scztjg/subTitleLeft.png"
                    alt="subTitleLeft"
                  />
                  <div class="subTitleContent">{{item.name}}</div>
                  <img
                    src="/static/citybrain/scjg/img/scztjg/subTitleRight.png"
                    alt="subTitleRight"
                  />
                </header>
                <div
                  v-if="index==0"
                  id="cross1"
                  style="width: 100%; height: 400px"
                ></div>
                <div
                  v-if="index==1"
                  id="cross2"
                  style="width: 100%; height: 400px"
                ></div>
                <div
                  v-if="index==2"
                  id="cross3"
                  style="width: 100%; height: 400px"
                ></div>
                <div
                  v-if="index==3"
                  id="cross4"
                  style="width: 100%; height: 400px"
                ></div>
                <div
                  v-if="index==4"
                  id="cross5"
                  style="width: 100%; height: 400px"
                ></div>
                <div
                  v-if="index==5"
                  id="cross6"
                  style="width: 100%; height: 400px"
                ></div>
                <div
                  v-if="index==6"
                  id="cross7"
                  style="width: 100%; height: 400px"
                ></div>
              </div>
            </div>
            <div class="button" @click="showQyfx1=false">返回企业雷达图</div>
          </div>
        </div>
        <!-- 企业信用事件 弹窗   v-show="showXysj"-->
        <div class="alt-xysj" v-show="showXysj">
          <div class="alt-top">
            <span>企业风险预测</span>
            <span class="del" @click="showXysj=false"></span>
          </div>
          <div class="cont">
            <!-- 头部 -->
            <div class="msg-top">
              <img src="/static/citybrain/scjg/img/scztjg/title.png" alt="" />
              <div class="text-box">
                <div class="title">金华市卓盛红木有限公司</div>
                <ul>
                  <li>
                    <div class="icon"><span></span></div>
                    <div class="tit">联系电话:</div>
                    <span>18689985892</span>
                  </li>
                  <li>
                    <div class="icon"><span></span></div>
                    <div class="tit">官方网站:</div>
                    <span>暂无</span>
                  </li>
                  <li>
                    <div class="icon"><span></span></div>
                    <div class="tit">地址:</div>
                    <span>浙江省金华市东阳市东阳江镇256号</span>
                  </li>
                </ul>
              </div>
            </div>
            <!--中间  -->
            <div class="xysj-center">
              <div class="xysj-title">
                <img
                  src="/static/citybrain/scjg/img/scztjg/tertiaryTitle.png"
                  alt=""
                />
                <span>企业标签</span>
              </div>
              <ul class="item-center">
                <li>社保人数减少</li>
                <li
                  style="
                    background-image: linear-gradient(
                      180deg,
                      #23c1fb 0%,
                      #4e88ff 100%
                    );
                  "
                >
                  营收骤降
                </li>
                <li
                  style="
                    background-image: linear-gradient(
                      180deg,
                      #915aff 0%,
                      #5b23fb 100%
                    );
                  "
                >
                  环境违法
                </li>
                <li
                  style="
                    background-image: linear-gradient(
                      180deg,
                      #ff699a 0%,
                      #ff2f73 100%
                    );
                  "
                >
                  税收违法
                </li>
                <li
                  style="
                    background-image: linear-gradient(
                      180deg,
                      #ff6363 0%,
                      #ff0c23 100%
                    );
                  "
                >
                  劳动仲裁
                </li>
              </ul>
            </div>
            <!-- 底部 -->
            <div class="table-bottom">
              <div class="title-list">
                <div
                  v-for="(item,index) in titleList"
                  :class="[titleListNum==index ? 'active' :'']"
                  @click="togListNumFun(index)"
                >
                  {{item.name}}
                </div>
              </div>
              <div class="ul-list" v-if="titleListNum==0">
                <ul>
                  <li v-for="item in gsData">
                    <img
                      src="/static/citybrain/scjg/img/scztjg/list-9.png"
                      alt=""
                    />
                    <span>{{item.name}}:</span>
                    <span>{{item.value}}</span>
                  </li>
                </ul>
              </div>

              <div class="echarts-item">
                <!-- 经营信息 -->
                <div class="jy-class" v-show="showJy">
                  <div class="echarts-item-left">
                    <div class="xysj-title">
                      <img
                        src="/static/citybrain/scjg/img/scztjg/tertiaryTitle.png"
                        alt=""
                      />
                      <span>企业营收</span>
                    </div>
                    <div
                      style="width: 1100px; height: 440px"
                      id="gsEchart0"
                    ></div>
                  </div>
                  <div class="echarts-item-right">
                    <div class="xysj-title">
                      <img
                        src="/static/citybrain/scjg/img/scztjg/tertiaryTitle.png"
                        alt=""
                      />
                      <span>企业税收</span>
                    </div>
                    <div
                      style="width: 1100px; height: 440px"
                      id="gsEchart1"
                    ></div>
                  </div>
                </div>
                <!-- 人才信息 -->
                <div class="rc-class" v-show="showRc">
                  <div class="rc-item-left">
                    <div class="xysj-title">
                      <img
                        src="/static/citybrain/scjg/img/scztjg/tertiaryTitle.png"
                        alt=""
                      />
                      <span>社保缴交人数</span>
                    </div>
                    <div
                      style="width: 1100px; height: 440px"
                      id="rcEchart0"
                    ></div>
                  </div>
                  <div class="rc-item-center">
                    <div class="xysj-title">
                      <img
                        src="/static/citybrain/scjg/img/scztjg/tertiaryTitle.png"
                        alt=""
                      />
                      <span>员工学历分布</span>
                    </div>
                    <div class="bottom-echart">
                      <div
                        style="width: 340px; height: 440px"
                        id="rcEchart1"
                      ></div>
                      <ul class="pie-text">
                        <li v-for="(item,index) in pieTextData">
                          <span v-if="index==0" class="icon"></span>
                          <span
                            v-if="index==1"
                            style="background-color: #af75cb"
                            class="icon"
                          ></span>
                          <span
                            v-if="index==2"
                            style="background-color: #32d8d9"
                            class="icon"
                          ></span>
                          <span
                            v-if="index==3"
                            style="background-color: #ffb637"
                            class="icon"
                          ></span>
                          <span
                            v-if="index==4"
                            style="background-color: #ff4949"
                            class="icon"
                          ></span>
                          <div>
                            <span>{{item.name}}</span>
                            <br />
                            <span>{{item.value}}</span>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div class="rc-item-right">
                    <div class="xysj-title">
                      <img
                        src="/static/citybrain/scjg/img/scztjg/tertiaryTitle.png"
                        alt=""
                      />
                      <span>员工年龄分布</span>
                    </div>
                    <div class="bottom-echart">
                      <div
                        style="width: 340px; height: 440px"
                        id="rcEchart2"
                      ></div>
                      <ul class="pie-text">
                        <li v-for="(ele,num) in pieTextData1">
                          <span v-if="num==0" class="icon"></span>
                          <span
                            v-if="num==1"
                            style="background-color: #af75cb"
                            class="icon"
                          ></span>
                          <span
                            v-if="num==2"
                            style="background-color: #32d8d9"
                            class="icon"
                          ></span>
                          <span
                            v-if="num==3"
                            style="background-color: #ffb637"
                            class="icon"
                          ></span>
                          <span
                            v-if="num==4"
                            style="background-color: #ff4949"
                            class="icon"
                          ></span>
                          <span
                            v-if="num==5"
                            style="background-color: #cad55d"
                            class="icon"
                          ></span>
                          <div>
                            <span>{{ele.name}}</span>
                            <br />
                            <span>{{ele.value}}</span>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <!-- 能耗信息 -->
                <div class="nh-class" v-show="showNh">
                  <div class="nh-item-left">
                    <div class="xysj-title">
                      <img
                        src="/static/citybrain/scjg/img/scztjg/tertiaryTitle.png"
                        alt=""
                      />
                      <span>用电量</span>
                    </div>
                    <div
                      style="width: 830px; height: 440px"
                      id="nhEchart0"
                    ></div>
                  </div>
                  <div class="nh-item-center">
                    <div class="xysj-title">
                      <img
                        src="/static/citybrain/scjg/img/scztjg/tertiaryTitle.png"
                        alt=""
                      />
                      <span>用水量</span>
                    </div>
                    <div
                      style="width: 830px; height: 440px"
                      id="nhEchart1"
                    ></div>
                  </div>
                  <div class="nh-item-right">
                    <div class="xysj-title">
                      <img
                        src="/static/citybrain/scjg/img/scztjg/tertiaryTitle.png"
                        alt=""
                      />
                      <span>用气量</span>
                    </div>
                    <div
                      style="width: 830px; height: 440px"
                      id="nhEchart2"
                    ></div>
                  </div>
                </div>
                <!-- 信用事件 -->
                <div class="xh-class" v-show="showXh">
                  <div class="xh-item-left">
                    <div class="xysj-title">
                      <img
                        src="/static/citybrain/scjg/img/scztjg/tertiaryTitle.png"
                        alt=""
                      />
                      <span>信用事件历年折线图</span>
                    </div>
                    <div
                      style="width: 1100px; height: 440px"
                      id="xhEchart0"
                    ></div>
                  </div>
                  <div class="xh-item-right">
                    <div class="xysj-title">
                      <img
                        src="/static/citybrain/scjg/img/scztjg/tertiaryTitle.png"
                        alt=""
                      />
                      <span>信用违约分布图</span>
                    </div>
                    <div class="bottom-echart">
                      <div
                        style="width: 500px; height: 440px"
                        id="xhEchart1"
                      ></div>
                      <ul
                        class="pie-text"
                        style="height: 400px; margin-left: 50px"
                      >
                        <li v-for="(item,index) in xhTextData">
                          <span v-if="index==0" class="icon"></span>
                          <span
                            v-if="index==1"
                            style="background-color: #af75cb"
                            class="icon"
                          ></span>
                          <span
                            v-if="index==2"
                            style="background-color: #32d8d9"
                            class="icon"
                          ></span>
                          <span
                            v-if="index==3"
                            style="background-color: #ffb637"
                            class="icon"
                          ></span>
                          <span
                            v-if="index==4"
                            style="background-color: #ff4949"
                            class="icon"
                          ></span>
                          <span
                            v-if="index==5"
                            style="background-color: #edff49"
                            class="icon"
                          ></span>
                          <span
                            v-if="index==6"
                            style="background-color: #49ffff"
                            class="icon"
                          ></span>
                          <span
                            v-if="index==7"
                            style="background-color: #e149ff"
                            class="icon"
                          ></span>
                          <span
                            v-if="index==8"
                            style="background-color: #ff8c49"
                            class="icon"
                          ></span>
                          <div>
                            <span>{{item.name}}</span>
                            <br />
                            <span>{{item.value}}</span>
                          </div>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div class="table" v-if="titleListNum!=0">
                <div class="xysj-title" v-if="titleListNum==1">
                  <img
                    src="/static/citybrain/scjg/img/scztjg/tertiaryTitle.png"
                    alt=""
                  />
                  <span>融资信息</span>
                </div>
                <div class="xysj-title" v-if="titleListNum==2">
                  <img
                    src="/static/citybrain/scjg/img/scztjg/tertiaryTitle.png"
                    alt=""
                  />
                  <span>员工报表</span>
                </div>
                <div class="table-th" v-if="titleListNum!=4">
                  <div class="th" v-for="item in tableTitle">{{item}}</div>
                </div>

                <div class="table-th" v-if="titleListNum==4">
                  <div class="th">{{tableTitle[0]}}</div>
                  <div class="th">{{tableTitle[1]}}</div>
                  <div class="th">{{tableTitle[2]}}</div>
                  <div class="th" style="flex: 0.9">{{tableTitle[3]}}</div>
                </div>
                <div class="table-tr" style="height: 300px">
                  <div
                    class="tr"
                    v-for="item in tableData"
                    v-if="titleListNum!=4"
                  >
                    <div>{{item.a}}</div>
                    <div>{{item.b}}</div>
                    <div>{{item.c}}</div>
                    <div>{{item.d}}</div>
                    <div v-if="titleListNum==3">{{item.e}}</div>
                    <div v-if="titleListNum==3">{{item.f}}</div>
                    <div v-if="titleListNum==2">{{item.e}}</div>
                    <div v-if="titleListNum==2">{{item.f}}</div>
                    <div v-if="titleListNum==2">{{item.g}}</div>
                  </div>
                  <div
                    class="tr"
                    v-for="(item,index) in tableData"
                    v-if="titleListNum==4"
                  >
                    <div>{{index+1}}</div>
                    <div>{{item.a}}</div>
                    <div>{{item.b}}</div>
                    <div style="flex: 0.9">{{item.c}}</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- <div class="button" @click="backFun()">返回</div> -->
          </div>
        </div>

        <div class="bottomEcharts">
          <div>
            <div class="title">
              <nav style="padding: 20 45px">
                <s-header-title
                  style="width: 1000px; height: 100px"
                  title="经营异常"
                  htype="1"
                >
                </s-header-title>
              </nav>
            </div>
            <div id="barEcharts001" style="width: 1000px; height: 350px"></div>
          </div>
          <div id="radarChart5" style="width: 460px; height: 400px"></div>
          <div id="radarChart6" style="width: 500px; height: 400px"></div>
          <table class="table centerTable">
            <thead>
              <tr>
                <th v-for="item in thName">{{item}}</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in tbList"
                style="cursor: pointer"
                @click="showDialogs()"
              >
                <td>{{item.name}}</td>
                <td>{{item.lx}}</td>
                <td>{{item.qy}}</td>
                <td>{{item.time}}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </body>

  <script src="/static/citybrain/scjg/js/lib/echarts.js"></script>
  <script src="/static/citybrain/scjg/js/lib/vue-seamless-scroll.min.js"></script>
  <!-- <script src="/static/js/jslib/axios.min.js"></script> -->
  <!-- <script src="/static/js/jslib/http.interceptor.js"></script> -->
  <script src="/static/js/jslib/datav.min.vue.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>

  <script>
    //指标数据
    var app = new Vue({
      el: "#app",
      data: {
        mapObj: {},
        yjindex: 0,
        activeId: 0,
        thName: ["风险企业名称", "风险预警类型", "所属区域", "预警时间"],
        tbList: [],
        showQyfx1: false,
        showXysj: false,
        // 企业信用事件弹窗数据
        titleListNum: 0,
        showJy: false,
        showRc: false,
        showNh: false,
        showXh: false,
        titleList: [
          {
            name: "工商信息",
          },
          {
            name: "经营信息",
          },
          {
            name: "人才信息",
          },
          {
            name: "能耗信息",
          },
          {
            name: "信用事件",
          },
        ],
        rightData: [],
        // 风险画像
        fxhxData: [],
        tableName: [],
        table1: [],
        table2: [],
        table3: [],
        allTable1: [],
        allTable2: [],
        allTable3: [],
        searchTableName: "金华市",
        // 九个圈的数据
        nameData: [],
        // 雷达图的数据
        // 投资资金来源
        radar1: [],
        radar2: [],
        radar3: [],
        radar4: [],
        radar5: [],
        radar6: [],
        // 折线图的数据
        zxt_data: [],
        // 用电量的数据
        ydl_cross: {},
        // 用水量的数据
        ysl_cross: {},
        // 用气量的数据
        yql_cross: {},
        // 营收骤降的数据
        yszj_cross: {},
        // 社保基金减少的数据
        sbjs_cross: {},
        // 社保人的数据
        sbrjs_cross: {},
        // 税收骤降的数据
        sszj_cross: {},
        // table数据
        tableTitle: [],
        tableData: [],
        // 信用弹窗数据
        // 工商信息
        gsData: [],

        // 经营信息
        jyData: [],
        jyEchart1: [],
        jyEchart2: [],
        jyTitle: ["日期", "轮次", "金额", "出资方"],
        jyTable: [],
        // 人才信息
        rcEchartData: [],
        rcPieData: [],
        rcPieData1: [],
        pieTextData: [],
        pieTextData1: [],
        rcTitle: [
          "月份",
          "员工数",
          "员工数同比增幅",
          "新增员工数",
          "流失员工数",
          "净增加员工数",
          "发布次数",
        ],
        rcTable: [],
        // 能耗信息
        nhEchart0: [],

        nhEchart1: [],
        nhEchart2: [],

        nhTitle: [
          "抄表时间",
          "用水量(吨)",
          "水费",
          "用电量(度)",
          "电费(元)",
          "用气量(立方)",
        ],
        nhTable: [],
        // 信用事件
        xhEchart2: [],
        xhPieData1: [],
        xhTextData: [],
        xhTitle: ["序号", "日期", "信用违约类型", "事件名称"],
        xhTable: [],

        treeData: [],
        defaultProps: {
          children: "children",
          label: "label",
        },
      },

      methods: {
        // 雷达图5
        getRadar5() {
          const myChart = echarts.init(document.getElementById("radarChart5"));
          let option;
          let data = this.radar5;
          let xdata = this.radar5.map((item) => {
            return item.text;
          }); //横轴data
          let ydata = this.radar5.map((item) => {
            return item.value;
          }); //纵轴data

          option = {
            tooltip: {
              trigger: "item",
              borderWidth: 0,
              borderRadius: 5,
              padding: 15,
              backgroundColor: "#384c63",
              textStyle: {
                color: "#d0e1f5",
                fontSize: 35,
              },
              formatter: `${xdata[0]}：${ydata[0]}<br>
                        ${xdata[1]}：${ydata[1]}<br>
                        ${xdata[2]}：${ydata[2]}<br>`,
            },
            radar: {
              indicator: [
                {
                  text: "业营税收",
                  max: 230,
                },
                {
                  text: "人社参保",
                  max: 230,
                },
                {
                  text: "水电燃气数据",
                  max: 230,
                },
              ],
              center: ["54%", "60%"],
              radius: "75%",
              axisName: {
                color: "#fff",
                fontSize: 26,
              },
              splitArea: {
                show: false,
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: "#118def",
                },
              },
              splitLine: {
                lineStyle: {
                  color: "#118def",
                },
              },
            },
            series: [
              {
                name: "经营风险预警",
                type: "radar",
                lineStyle: {
                  width: 4,
                },
                data: [
                  {
                    value: ydata,
                    symbolSize: 16,
                  },
                ],
                symbolColor: "#fff",
                itemStyle: {
                  color: "#0269CB",
                },
              },
            ],
          };

          myChart.setOption(option);
        },
        // 雷达图6
        getRadar6() {
          const myChart = echarts.init(document.getElementById("radarChart6"));
          let option;
          let data = this.radar6;
          let xdata = []; //横轴data
          let ydata = []; //纵轴data
          for (item of data) {
            xdata.push(item.text);
            ydata.push(item.value);
          }
          option = {
            tooltip: {
              trigger: "item",
              borderWidth: 0,
              borderRadius: 5,
              padding: 15,
              backgroundColor: "#384c63",
              textStyle: {
                color: "#d0e1f5",
                fontSize: 35,
              },
              formatter: `${xdata[0]}：${ydata[0]}<br>
                        ${xdata[1]}：${ydata[1]}<br>
                        ${xdata[2]}：${ydata[2]}<br>
                        ${xdata[3]}：${ydata[3]}<br>
                        ${xdata[4]}：${ydata[4]}<br>
                        ${xdata[5]}：${ydata[5]}<br>
                        ${xdata[6]}：${ydata[6]}<br>
                        ${xdata[7]}：${ydata[7]}<br>`,
            },
            radar: {
              indicator: [
                {
                  text: "劳动仲裁",
                  max: 230,
                },
                {
                  text: "环境违法",
                  max: 230,
                },
                {
                  text: "失信人",
                  max: 230,
                },
                {
                  text: "行政处罚",
                  max: 230,
                },
                {
                  text: "失信违约企业",
                  max: 230,
                },
                {
                  text: "金融风险",
                  max: 230,
                },
                {
                  text: "税收违法",
                  max: 230,
                },
                {
                  text: "非安全生产",
                  max: 230,
                },
              ],
              center: ["54%", "60%"],
              radius: "75%",
              axisName: {
                color: "#fff",
                fontSize: 26,
              },
              splitArea: {
                show: false,
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: "#118def",
                },
              },
              splitLine: {
                lineStyle: {
                  color: "#118def",
                },
              },
            },
            series: [
              {
                name: "风险预警模型三色预警企业",
                type: "radar",
                lineStyle: {
                  width: 4,
                },
                data: [
                  {
                    value: ydata,
                    symbolSize: 16,
                  },
                ],
                symbolColor: "#fff",
                itemStyle: {
                  color: "#0269CB",
                },
              },
            ],
          };

          myChart.setOption(option);
        },
        showDialogs() {
          window.parent.postMessage(
            JSON.stringify({
              type: "openIframe",
              name: "qyyj_dialog",
              src:
                baseURL.url +
                "/static/citybrain/scjg/commont/scztjg/qyyj_dialog.html",
              left: "2650px",
              top: "225px",
              width: "2510px",
              height: "1290px",
              zIndex: "666",
              // argument: {},
            }),
            "*"
          );
        },
        getYjdetail(index, name) {
          this.yjindex = index;
          this.searchTableName = name;
        },
        showXysjbtn(itemId) {
          this.activeId = itemId;
          if (itemId == 10) {
            this.showXysj = true;
            this.togListNumFun(0);
          }
        },
        // 过滤表格中的值
        filterTable(data, name) {
          let arr = data.filter((item) => {
            return item.name.indexOf(name) !== -1;
          });
          return arr;
        },
        //经营异常弹窗
        showDetails() {
          let Iframe = {
            type: "openIframe",
            name: "qyyj_dialog",
            src:
              baseURL.url +
              "/static/citybrain/scjg/commont/scztjg/qyyj_dialog.html",
            left: "2200px",
            top: "225px",
            width: "2000px",
            height: "900px",
            zIndex: "10",
            argument: {
              status: "openIframe",
              title: titleName,
            },
          };
          window.parent.postMessage(JSON.stringify(Iframe), "*");
        },
        initFun() {
          let that = this;
          $api("hjbh_right_scztjgMiddle001").then((res) => {
            that.rightData = res;
            that.getRadar5();
          });
          $api("hjbh_right_scztjgMiddle002").then((res) => {
            that.fxhxData = res;
            that.getRadar6();
          });
          $api("hjbh_right_scztjgMiddle003").then((res) => {
            that.tableName = res;
          });
          $api("hjbh_right_scztjgMiddle004").then((res) => {
            that.allTable1 = res;
            that.table1 = res;
          });
          $api("hjbh_right_scztjgMiddle005").then((res) => {
            that.allTable2 = res;
            that.table2 = res;
          });
          $api("hjbh_right_scztjgMiddle006").then((res) => {
            that.allTable3 = res;
            that.table3 = res;
          });
          $api("hjbh_right_scztjgMiddle007").then((res) => {
            that.nameData = res;
          });
          $api("hjbh_right_scztjgMiddle008").then((res) => {
            that.radar1 = res;
            that.getRadar1(that.radar1);
          });
          $api("hjbh_right_scztjgMiddle0010").then((res) => {
            that.zxt_data = res;
          });
          $api("hjbh_right_scztjgMiddle0011").then((res) => {
            that.ydl_cross = res;
          });
          $api("hjbh_right_scztjgMiddle0012").then((res) => {
            that.ysl_cross = res;
          });
          $api("hjbh_right_scztjgMiddle0013").then((res) => {
            that.yql_cross = res;
          });
          $api("hjbh_right_scztjgMiddle0014").then((res) => {
            that.yszj_cross = res;
          });
          $api("hjbh_right_scztjgMiddle0015").then((res) => {
            that.sbjs_cross = res;
          });
          $api("hjbh_right_scztjgMiddle0016").then((res) => {
            that.sbrjs_cross = res;
          });
          $api("hjbh_right_scztjgMiddle0017").then((res) => {
            that.sszj_cross = res;
          });
          // 临时数据
          $api("hjbh_right_scztjgMiddle0018").then((res) => {
            that.radar3 = res;
            that.getRadar3();
          });
          $api("scjgqk-middle001").then((res) => {
            that.getEcharts01("barEcharts001", res);
          });
          $api("scjgqk-middle002").then((res) => {
            that.tbList = res;
          });
        },
        getEcharts01(dom, echartData) {
          let echarts1 = echarts.init(document.getElementById(dom));
          var xData = echartData.map((item) => item.name);
          var yData = echartData.map((item) => item.value);
          var yData1 = echartData.map((item) => item.value1);
          var yData2 = echartData.map((item) => item.value2);
          var TmpData = (function () {
            var data = [];
            Date.prototype.getHM = function (format) {
              return this.getHours() + ":" + this.getMinutes();
            };
            var date = new Date();
            for (var i = 1; i < 21; i++) {
              var tmp = {};
              date.setMinutes(date.getMinutes() + 1);
              tmp["time"] = date.getHM();
              tmp["client"] = Math.floor(Math.random() * 20) + 30; //30~50个
              tmp["fair"] = Math.floor(tmp["client"] * (Math.random() / 10)); //不大于10%
              tmp["good"] = Math.floor(
                (tmp["client"] - tmp["fair"]) * (Math.random() / 3)
              ); //剩余部分不大于1/3
              tmp["best"] = tmp["client"] - tmp["good"] - tmp["fair"]; //剩下的
              data.push(tmp);
            }
            return data;
          })();
          option = {
            //backgroundColor: "#344b58",

            tooltip: {
              trigger: "axis",

              backgroundColor: "rgba(0, 0, 0, 0.6)",
              axisPointer: {
                type: "shadow",
              },
              textStyle: {
                color: "white",
                fontSize: "30",
              },
            },

            grid: {
              borderWidth: 0,
              // top: "25%",
              right: "0",
              // left: "0",
              // bottom: "15%",
              textStyle: {
                color: "#fff",
                fontSize: 30,
              },
            },
            legend: {
              textStyle: {
                color: "#fff",
                fontSize: 30,
              },
              data: ["低风险", "中风险", "高风险"],
            },

            calculable: true,
            xAxis: [
              {
                type: "category",
                axisLine: {
                  lineStyle: {
                    color: "#475065",
                  },
                },
                axisLabel: {
                  show: true,
                  rotate: 30,
                  interval: 0,

                  textStyle: {
                    color: "#FFF",
                    fontSize: 28,
                  },
                },
                splitLine: {
                  show: false,
                },
                axisTick: {
                  show: true,
                  length: 7,
                },

                data: xData,
              },
            ],
            yAxis: [
              {
                type: "value",
                splitLine: {
                  show: false,
                },
                // min: 0,
                // max: 700,
                axisLine: {
                  lineStyle: {
                    color: "#90979c",
                  },
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  show: true,

                  interval: 0,

                  textStyle: {
                    color: "#FFF",
                    fontSize: 28,
                  },
                },
              },
            ],

            series: [
              {
                name: "低风险",
                type: "bar",
                stack: "总数",
                barMaxWidth: 60,
                barGap: "20%",
                itemStyle: {
                  normal: {
                    color: "#5087ec",
                    label: {
                      show: true,
                      textStyle: {
                        color: "#fff",
                        fontSize: 25,
                      },
                      position: "insideTop",
                      formatter: function (p) {
                        return p.value > 0 ? p.value : "";
                      },
                    },
                  },
                },
                data: yData,
              },
              {
                name: "中风险",
                type: "bar",
                stack: "总数",
                itemStyle: {
                  normal: {
                    color: "#68bbc4",

                    barBorderRadius: 0,
                    label: {
                      show: true,
                      fontSize: 25,
                      color: "#fff",

                      position: "inside",
                      formatter: function (p) {
                        return p.value > 0 ? p.value : "";
                      },
                    },
                  },
                },
                data: yData1,
              },
              {
                name: "高风险",
                type: "bar",
                stack: "总数",
                itemStyle: {
                  normal: {
                    color: "#2CA02C",
                    barBorderRadius: 0,
                    label: {
                      textStyle: {
                        color: "#fff",
                        fontSize: 25,
                      },
                      show: true,
                      position: "inside",
                      formatter: function (p) {
                        return p.value > 0 ? p.value : "";
                      },
                    },
                  },
                },
                data: yData2,
              },
            ],
          };
          echarts1.setOption(option);
        },
        // 企业信用事件 tab切换
        togListNumFun(index) {
          this.titleListNum = index;
          let echarts0 = document.getElementById("gsEchart0");
          let echarts1 = document.getElementById("gsEchart1");
          let rcEcharts1 = document.getElementById("rcEchart0");
          let rcEcharts2 = document.getElementById("rcEchart1");
          let rcEcharts3 = document.getElementById("rcEchart2");
          let nhEcharts0 = document.getElementById("nhEchart0");
          let nhEcharts1 = document.getElementById("nhEchart1");
          let nhEcharts2 = document.getElementById("nhEchart2");
          let xhEcharts0 = document.getElementById("xhEchart0");
          let xhEcharts1 = document.getElementById("xhEchart1");
          if (index == 0) {
            this.showJy = false;
            this.showRc = false;
            this.showNh = false;
            this.showXh = false;
          }

          if (index == 1) {
            this.showJy = true;
            this.showRc = false;
            this.showNh = false;
            this.showXh = false;
            this.tableTitle = this.jyTitle;
            this.tableData = this.jyTable;
            this.$nextTick(() => {
              this.xyLineFun(echarts0, this.jyEchart1);
              this.xyLineFun(echarts1, this.jyEchart2);
            });
          } else if (index == 2) {
            this.showRc = true;
            this.showJy = false;
            this.showNh = false;
            this.showXh = false;
            this.pieTextData = [];
            this.pieTextData1 = [];
            this.tableTitle = this.rcTitle;
            this.tableData = this.rcTable;
            this.$nextTick(() => {
              this.xyLineFun(rcEcharts1, this.rcEchartData);
              this.getCarCanvas(rcEcharts2, this.rcPieData, 1);
              this.getCarCanvas(rcEcharts3, this.rcPieData1, 2);
            });
          } else if (index == 3) {
            this.showNh = true;
            this.showRc = false;
            this.showJy = false;
            this.showXh = false;
            this.tableTitle = this.nhTitle;
            this.tableData = this.nhTable;
            this.$nextTick(() => {
              this.xyLineFun(nhEcharts0, this.nhEchart0);
              this.xyLineFun(nhEcharts1, this.nhEchart1);
              this.xyLineFun(nhEcharts2, this.nhEchart2);
            });
          } else if (index == 4) {
            this.showXh = true;
            this.showNh = false;
            this.showRc = false;
            this.showJy = false;
            this.tableTitle = this.xhTitle;
            this.tableData = this.xhTable;
            this.xhTextData = [];
            this.$nextTick(() => {
              this.xyLineFun(xhEcharts0, this.xhEchart2);
              this.getXhCanvas(xhEcharts1, this.xhPieData1);
            });
          }
        },
        closeWindow() {
          // document.getElementsByClassName('alt-qyfx')[0].style.display = 'block'
          document.getElementsByClassName("alt-qyfx")[0].style.display = "none";
        },
        // 经营信息面积图
        xyLineFun(dom, data) {
          const cross1 = echarts.init(dom);
          let crossData = {
            data0: [],
            data1: [],
            data2: [],
          };
          for (item of data) {
            crossData.data0.push(item.data0);
            crossData.data1.push(item.data1);
            crossData.data2.push(item.data2);
          }
          let option = {
            // color: ['#34C1DD', '#AF9953', '#47346E'],
            color: ["#34C1DD", "#AF75C9", "#AF9953"],
            tooltip: {
              trigger: "axis",
              textStyle: {
                fontSize: 30,
              },
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
            },
            title: {
              text: "单位:人",
              padding: [40, 10, 10, 60],
              textStyle: {
                fontSize: 28,
                color: "#d6e7f9",
              },
            },
            legend: {
              icon: "circle",
              data: ["2020", "2021", "2022"],
              itemGap: 100,
              textStyle: {
                color: "#fff",
                fontSize: 28,
              },
            },
            grid: {
              left: "130px",
              right: "10px",
              top: "25%",

              // containLabel: true
            },
            xAxis: [
              {
                type: "category",
                boundaryGap: true,
                data: [
                  "1月",
                  "2月",
                  "3月",
                  "4月",
                  "5月",
                  "6月",
                  "7月",
                  "8月",
                  "9月",
                  "10月",
                  "11月",
                  "12月",
                ],
                axisLabel: {
                  interval: 0,
                  textStyle: {
                    color: "#d6e7f9",
                    fontSize: "28", //字体大小
                  },
                },
              },
            ],
            yAxis: {
              type: "value",
              splitLine: {
                lineStyle: {
                  color: "#77b3f169",
                },
              },
              axisLabel: {
                margin: 2,
                textStyle: {
                  color: "#d6e7f9",
                  fontSize: "28", //字体大小
                },
              },
            },
            series: [
              {
                name: "2020",
                type: "line",
                // stack: 'Total',
                // smooth: true,
                lineStyle: {
                  width: 2,
                },
                symbolSize: 10,
                // showSymbol: false,
                // areaStyle: {
                //   opacity: 0.8,
                //   color: '#34C1DD',
                // },
                // emphasis: {
                //   focus: 'series',
                // },
                data: crossData.data0,
              },
              {
                name: "2021",
                type: "line",
                // stack: 'Total',
                // smooth: true,
                lineStyle: {
                  width: 2,
                },
                symbolSize: 10,
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#AF75C9",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data1,
              },
              {
                name: "2022",
                type: "line",
                // stack: 'Total',
                // smooth: true,
                lineStyle: {
                  width: 2,
                },
                symbolSize: 10,
                // showSymbol: false,
                // areaStyle: {
                //   opacity: 0.8,
                //   color: '#AF9953',
                // },
                // emphasis: {
                //   focus: 'series',
                // },
                data: crossData.data2,
              },
            ],
          };
          cross1.setOption(option);
        },
        // 人才信息的饼图
        getCarCanvas(dom, data, index) {
          const cross1 = echarts.init(dom);
          const colors = [
            "#00C0FF",
            "#b76fd8",
            "#26f5f5",
            "#FFB460",
            "#ff4949",
            "#a9db52",
          ];

          let crossData = {
            name: [],
            value: [],
          };
          for (item of data) {
            crossData.name.push(item.name);
            crossData.value.push(item.value);
          }
          // 设置饼图图例

          let target;
          let total = 0;
          for (let i = 0; i < crossData.value.length; i++) {
            total += crossData.value[i];
          }
          for (let i = 0; i < crossData.value.length; i++) {
            let value = ((crossData.value[i] / total) * 100).toFixed(2) + " %";
            let str = {
              name: crossData.name[i],
              value: value,
            };
            if (index == 1) {
              this.pieTextData.push(str);
            } else if (index == 2) {
              this.pieTextData1.push(str);
            }
          }

          let option = {
            tooltip: {
              trigger: "item",
              textStyle: {
                fontSize: 28,
              },
              formatter: "{a} <br/>{b} : {c} ({d}%)",
            },
            toolbox: {
              show: true,
              feature: {
                mark: {
                  show: true,
                },
                dataView: {
                  show: true,
                  readOnly: false,
                },
                magicType: {
                  show: true,
                  type: ["pie", "funnel"],
                },
                restore: {
                  show: true,
                },
                saveAsImage: {
                  show: true,
                },
              },
            },
            calculable: true,
            series: [
              {
                name: "半径模式",
                type: "pie",
                radius: [50, 150],
                center: ["40%", "50%"],
                roseType: "radius",
                color: colors,
                label: {
                  normal: {
                    show: false,
                  },
                  emphasis: {
                    show: true,
                  },
                },
                lableLine: {
                  normal: {
                    show: false,
                  },
                  emphasis: {
                    show: true,
                  },
                },
                data: data,
              },
            ],
          };

          cross1.setOption(option);
        },
        // 信用事件的饼图
        getXhCanvas(dom, data) {
          const cross1 = echarts.init(dom);
          const colors = [
            "#00C0FF",
            "#AF75CB",
            "#32D8D9",
            "#FFB637",
            "#FF4949",
            "#CAD55D",
          ];

          let crossData = {
            name: [],
            value: [],
            // alis:[]
          };
          for (item of data) {
            crossData.name.push(item.alis);
            crossData.value.push(item.value);
            // crossData.alis.push(item.alis)
          }
          // 设置饼图图例

          let target;
          let total = 0;
          let seriesData = [];
          for (let i = 0; i < crossData.value.length; i++) {
            total += crossData.value[i];
          }
          for (let i = 0; i < crossData.value.length; i++) {
            let value = ((crossData.value[i] / total) * 100).toFixed(2) + " %";
            let str = {
              name: crossData.name[i],
              value: value,
            };
            let str2 = {
              name: crossData.name[i],
              value: crossData.value[i],
            };
            seriesData.push(str2);
            this.xhTextData.push(str);
          }

          let option = {
            tooltip: {
              trigger: "item",
              textStyle: {
                fontSize: 28,
              },
              formatter: "{a} <br/>{b} : {c} ({d}%)",
            },
            // legend: {
            // icon:"circle",
            // top:"center",
            // left:"450",
            //   itemGap:20,
            //   "textStyle": {
            //       "fontSize": 28,
            //       color:"#fff",

            //   },
            //   formatter: function (name) {
            //     let target;
            //     let total=0;
            //     for (let i = 0, l = crossData.value.length; i < l; i++) {
            //         if (crossData.name[i] == name) {
            //             target = crossData.value[i];
            //         }
            //         total+=crossData.value[i]
            //     }
            //     let arr = [
            //         name + "\n" + ((target / total) * 100).toFixed(1) + ' %'
            //     ]
            //     return arr
            //   },
            // },
            toolbox: {
              show: true,
              feature: {
                mark: {
                  show: true,
                },
                dataView: {
                  show: true,
                  readOnly: false,
                },
                magicType: {
                  show: true,
                  type: ["pie", "funnel"],
                },
                restore: {
                  show: true,
                },
                saveAsImage: {
                  show: true,
                },
              },
            },
            calculable: true,
            series: [
              {
                name: "信用违约分布图",
                type: "pie",
                radius: [100, 220],
                center: ["50%", "60%"],
                roseType: "radius",
                color: colors,
                label: {
                  normal: {
                    show: false,
                  },
                  emphasis: {
                    show: true,
                  },
                },
                lableLine: {
                  normal: {
                    show: false,
                  },
                  emphasis: {
                    show: true,
                  },
                },
                data: seriesData,
              },
            ],
          };

          cross1.setOption(option);
        },
        // 雷达图1
        getRadar1() {
          const myChart = echarts.init(document.getElementById("radarChart1"));
          let option;
          let data = this.radar1;

          let xdata = []; //横轴data
          let ydata = []; //纵轴data
          for (item of data) {
            xdata.push(item.text);
            ydata.push(item.value);
          }

          option = {
            tooltip: {
              trigger: "item",
              borderWidth: 0,
              borderRadius: 5,
              padding: 15,
              backgroundColor: "#384c63",
              textStyle: {
                color: "#d0e1f5",
                fontSize: 35,
              },
              formatter: `${xdata[0]}：${ydata[0]}<br>
                                    ${xdata[1]}：${ydata[1]}<br>
                                    ${xdata[2]}：${ydata[2]}<br>
                                    ${xdata[3]}：${ydata[3]}<br>
                                    ${xdata[4]}：${ydata[4]}<br>
                                    ${xdata[5]}：${ydata[5]}<br>
                                    ${xdata[6]}：${ydata[6]}<br>`,
            },
            radar: {
              indicator: [
                {
                  text: "*用电降低,1316",
                  max: 1500,
                },
                {
                  text: "社保人数减少,992",
                  max: 1500,
                },
                {
                  text: "社保基数减少,688",
                  max: 1500,
                },
                {
                  text: "*营收骤降,627",
                  max: 1500,
                },
                {
                  text: "*用气降低,1539",
                  max: 1500,
                },
                {
                  text: "*收税骤降,1093",
                  max: 1500,
                },
                {
                  text: "*用水降低,1174",
                  max: 1500,
                },
              ],
              center: ["54%", "50%"],
              radius: "60%",
              axisName: {
                color: "#fff",
                fontSize: 26,
                formatter: function (text) {
                  let arr = text.split(",");
                  // if(arr[0]=='社保基数减少' || arr[0]=='用电降低'){
                  //   return arr[0] + '{a|'+arr[1]+'}'
                  // }else{
                  return arr[0] + "\n" + "{a|" + arr[1] + "}";
                  // }
                },
                rich: {
                  a: {
                    color: "#FFD48C",
                    // color:this.getColorBySpeed(speed),
                    fontSize: 26,
                    padding: [10, 0, 0, 0],
                  },
                  speed: {
                    fontSize: 26,
                    padding: [10, 0, 0, 0],
                  },
                },
              },
              splitArea: {
                show: false,
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: "#118def",
                },
              },
              splitLine: {
                lineStyle: {
                  color: "#118def",
                },
              },
            },
            series: [
              {
                name: "标准产品",
                type: "radar",
                lineStyle: {
                  width: 4,
                },
                data: [
                  {
                    value: ydata,
                    symbolSize: 16,
                    // areaStyle: {
                    //   color: '#E8D40444'
                    // }
                  },
                ],
                // symbol: 'none',
                symbolColor: "#fff",
                itemStyle: {
                  color: "#0269CB",
                },
                // areaStyle: {
                //     color: '#09719A',
                // }
              },
            ],
          };

          myChart.setOption(option);
        },

        // 雷达图2
        getRadar2() {
          const myChart = echarts.init(document.getElementById("radar2"));
          let option;
          let data = this.radar2;

          let xdata = []; //横轴data
          let ydata1 = []; //2020
          let ydata2 = []; //2021
          let ydata3 = []; //上个月
          for (item of data) {
            xdata.push(item.text);
            ydata1.push(item.value);
            ydata2.push(item.value1);
            ydata3.push(item.value2);
          }

          option = {
            color: ["#0884DC", "#ECDF05", "#03FF08"],
            tooltip: {
              borderWidth: 0,
              borderRadius: 5,
              padding: 15,
              backgroundColor: "#384c63",
              textStyle: {
                color: "#d0e1f5",
                fontSize: 35,
              },
            },
            radar: {
              indicator: [
                {
                  text: "社保人数减少",
                  max: 240,
                },
                {
                  text: "*用水量",
                  max: 240,
                },
                {
                  text: "*用气量",
                  max: 240,
                },
                {
                  text: "*营收骤降",
                  max: 240,
                },
                {
                  text: "社保基数减少",
                  max: 240,
                },
                {
                  text: "*用电量",
                  max: 240,
                },
                {
                  text: "*税收骤降",
                  max: 240,
                },
              ],
              center: ["53%", "52%"],
              radius: "58%",
              axisName: {
                color: "#fff",
                fontSize: 28,
                padding: [5, 0, 0, 0],
              },
              splitArea: {
                show: false,
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: "#118def",
                },
              },
              splitLine: {
                lineStyle: {
                  color: "#118def",
                },
              },
            },
            legend: {
              data: ["2020", "2021", "上个月"],
              itemHeight: 24,
              itemGap: 100,
              textStyle: {
                color: "#fff",
                fontSize: 34,
              },
            },
            series: [
              {
                name: "2020",
                type: "radar",
                // lineStyle: {
                //     width: 2,
                // },
                data: [
                  {
                    value: ydata1,
                    name: "2020",
                    symbolSize: 16,
                    // areaStyle: {
                    //   color: '#E8D40444'
                    // }
                  },
                  {
                    value: ydata2,
                    name: "2021",
                    symbolSize: 16,
                    // areaStyle: {
                    //   color: '#AD533869'
                    // }
                  },
                  {
                    value: ydata3,
                    name: "上个月",
                    symbolSize: 16,
                    // areaStyle: {
                    //   color: '#62509469'
                    // }
                  },
                ],
                // symbol: 'none',
                // symbolColor:"#fff",
                // itemStyle: {
                //     color: "#0269CB",
                // },
                // areaStyle: {
                //     color: '#09719A',
                // }
              },
            ],
          };

          myChart.setOption(option);
        },
        // 雷达图3
        getRadar3() {
          const myChart = echarts.init(document.getElementById("radarChart3"));
          let option;
          let data = this.radar3;

          let xdata = []; //横轴data
          let ydata = []; //纵轴data
          for (item of data) {
            xdata.push(item.text);
            ydata.push(item.value);
          }

          option = {
            tooltip: {
              trigger: "item",
              borderWidth: 0,
              borderRadius: 5,
              padding: 15,
              backgroundColor: "#384c63",
              textStyle: {
                color: "#d0e1f5",
                fontSize: 35,
              },
              formatter: `${xdata[0]}：${ydata[0]}<br>
                                    ${xdata[1]}：${ydata[1]}<br>
                                    ${xdata[2]}：${ydata[2]}<br>
                                    ${xdata[3]}：${ydata[3]}<br>
                                    ${xdata[4]}：${ydata[4]}<br>
                                    ${xdata[5]}：${ydata[5]}<br>
                                    ${xdata[6]}：${ydata[6]}<br>`,
            },
            radar: {
              indicator: [
                {
                  text: "劳动仲裁,321",
                  max: 400,
                },
                {
                  text: "*安全生产违约,140",
                  max: 400,
                },
                {
                  text: "*税收违法,230",
                  max: 400,
                },
                {
                  text: "*金融风险,351",
                  max: 400,
                },
                {
                  text: "*失信违法企业,79",
                  max: 400,
                },
                {
                  text: "行政处罚,345",
                  max: 400,
                },
                {
                  text: "*失信被执行人,93",
                  max: 400,
                },
                {
                  text: "*环境违法,88",
                  max: 400,
                },
              ],
              center: ["55%", "50%"],
              radius: "58%",
              axisName: {
                color: "#fff",
                fontSize: 26,
                formatter: function (text) {
                  let arr = text.split(",");
                  // if(arr[0]=='社保基数减少' || arr[0]=='用电降低'){
                  //   return arr[0] + '{a|'+arr[1]+'}'
                  // }else{
                  return arr[0] + "\n" + "{a|" + arr[1] + "}";
                  // }
                },
                rich: {
                  a: {
                    color: "#FFD48C",
                    // color:this.getColorBySpeed(speed),
                    fontSize: 26,
                    padding: [10, 10, 10, 10],
                  },
                  speed: {
                    fontSize: 26,
                    padding: [10, 10, 10, 10],
                  },
                },
              },
              splitArea: {
                show: false,
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: "#118def",
                },
              },
              splitLine: {
                lineStyle: {
                  color: "#118def",
                },
              },
            },
            series: [
              {
                name: "标准产品",
                type: "radar",
                lineStyle: {
                  width: 4,
                },
                data: [
                  {
                    value: ydata,
                    symbolSize: 16,
                    // areaStyle: {
                    //   color: '#E8D40444'
                    // }
                  },
                ],
                // symbol: 'none',
                symbolColor: "#fff",
                itemStyle: {
                  color: "#0269CB",
                },
                // areaStyle: {
                //     color: '#09719A',
                // }
              },
            ],
          };

          myChart.setOption(option);
        },

        // 雷达图4
        getRadar4() {
          const myChart = echarts.init(document.getElementById("radar4"));
          let option;
          let data = this.radar4;

          let xdata = []; //横轴data
          let ydata = []; //纵轴data
          for (item of data) {
            xdata.push(item.text);
            ydata.push(item.value);
          }

          option = {
            tooltip: {
              trigger: "item",
              borderWidth: 0,
              borderRadius: 5,
              padding: 15,
              backgroundColor: "#384c63",
              textStyle: {
                color: "#d0e1f5",
                fontSize: 35,
              },
              formatter: `${xdata[0]}：${ydata[0]}<br>
                                    ${xdata[1]}：${ydata[1]}<br>
                                    ${xdata[2]}：${ydata[2]}<br>
                                    ${xdata[3]}：${ydata[3]}<br>
                                    ${xdata[4]}：${ydata[4]}<br>
                                    ${xdata[5]}：${ydata[5]}<br>
                                    ${xdata[6]}：${ydata[6]}<br>`,
            },
            radar: {
              indicator: [
                {
                  text: "劳动仲裁",
                  max: 200,
                },
                {
                  text: "*非安全生产",
                  max: 200,
                },
                {
                  text: "*税收违法",
                  max: 200,
                },
                {
                  text: "*金融风险",
                  max: 200,
                },
                {
                  text: "*失信违法企业",
                  max: 200,
                },
                {
                  text: "行政处罚",
                  max: 200,
                },
                {
                  text: "*失信人",
                  max: 200,
                },
                {
                  text: "*环境违法",
                  max: 200,
                },
              ],
              center: ["55%", "52%"],
              radius: "58%",
              axisName: {
                color: "#fff",
                fontSize: 28,
                padding: [5, 0, 0, 0],
                // rich:{
                //     a:{
                //       color: '#FFD48C',
                //       // color:this.getColorBySpeed(speed),
                //       fontSize:26,
                //       padding:[10,10,10,10]
                //     },
                //     speed:{
                //       fontSize:26,
                //       padding:[10,10,10,10]
                //     }
                // }
              },
              splitArea: {
                show: false,
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: "#118def",
                },
              },
              splitLine: {
                lineStyle: {
                  color: "#118def",
                },
              },
            },
            series: [
              {
                name: "标准产品",
                type: "radar",
                lineStyle: {
                  width: 4,
                },
                data: [
                  {
                    value: ydata,
                    symbolSize: 16,
                    // areaStyle: {
                    //   color: '#E8D40444'
                    // }
                  },
                ],
                // symbol: 'none',
                symbolColor: "#fff",
                itemStyle: {
                  color: "#0269CB",
                },
                // areaStyle: {
                //     color: '#09719A',
                // }
              },
            ],
          };

          myChart.setOption(option);
        },

        // 用电量面积图1
        getCross1(data) {
          const cross1 = echarts.init(document.getElementById("cross1"));

          let crossData = data;
          let option = {
            color: ["#34C1DD", "#AF9953", "#47346E"],
            tooltip: {
              trigger: "axis",
              textStyle: {
                fontSize: 30,
              },
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
            },
            title: {
              text: "单位:度",
              padding: [40, 10, 10, 60],
              textStyle: {
                fontSize: 28,
                color: "#d6e7f9",
              },
            },
            legend: {
              icon: "circle",
              data: ["2020", "2021", "2022"],
              itemGap: 100,
              textStyle: {
                color: "#fff",
                fontSize: 28,
              },
            },
            grid: {
              left: "80px",
              right: "30px",
              // bottom: '3%',
              top: "25%",

              // containLabel: true
            },
            xAxis: [
              {
                type: "category",
                boundaryGap: true,
                data: [
                  "1月",
                  "2月",
                  "3月",
                  "4月",
                  "5月",
                  "6月",
                  "7月",
                  "8月",
                  "9月",
                  "10月",
                  "11月",
                  "12月",
                ],
                axisLabel: {
                  interval: 0,
                  textStyle: {
                    color: "#d6e7f9",
                    fontSize: "28", //字体大小
                  },
                },
              },
            ],
            yAxis: {
              type: "value",
              splitLine: {
                lineStyle: {
                  color: "#77b3f169",
                },
              },
              axisLabel: {
                textStyle: {
                  color: "#d6e7f9",
                  fontSize: "28", //字体大小
                },
              },
            },
            series: [
              {
                name: "2020",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#34C1DD",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data0,
              },
              {
                name: "2021",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#AF9953",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data1,
              },
              {
                name: "2022",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#47346E",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data2,
              },
            ],
          };
          cross1.setOption(option);
        },
        // 用水量面积图2
        getCross2(data) {
          const cross1 = echarts.init(document.getElementById("cross2"));

          let crossData = data;
          let option = {
            color: ["#34C1DD", "#AF9953", "#47346E"],
            tooltip: {
              trigger: "axis",
              textStyle: {
                fontSize: 30,
              },
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
            },
            title: {
              text: "单位:m3/d",
              padding: [40, 10, 10, 60],
              textStyle: {
                fontSize: 28,
                color: "#d6e7f9",
              },
            },
            legend: {
              icon: "circle",
              data: ["2020", "2021", "2022"],
              itemGap: 100,
              textStyle: {
                color: "#fff",
                fontSize: 28,
              },
            },
            grid: {
              left: "80px",
              right: "30px",
              // bottom: '3%',
              top: "25%",

              // containLabel: true
            },
            xAxis: [
              {
                type: "category",
                boundaryGap: true,
                data: [
                  "1月",
                  "2月",
                  "3月",
                  "4月",
                  "5月",
                  "6月",
                  "7月",
                  "8月",
                  "9月",
                  "10月",
                  "11月",
                  "12月",
                ],
                axisLabel: {
                  interval: 0,
                  textStyle: {
                    color: "#d6e7f9",
                    fontSize: "28", //字体大小
                  },
                },
              },
            ],
            yAxis: {
              type: "value",
              splitLine: {
                lineStyle: {
                  color: "#77b3f169",
                },
              },
              axisLabel: {
                textStyle: {
                  color: "#d6e7f9",
                  fontSize: "28", //字体大小
                },
              },
            },
            series: [
              {
                name: "2020",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#34C1DD",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data0,
              },
              {
                name: "2021",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#AF9953",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data1,
              },
              {
                name: "2022",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#47346E",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data2,
              },
            ],
          };
          cross1.setOption(option);
        },
        // 用气量面积图3
        getCross3(data) {
          const cross1 = echarts.init(document.getElementById("cross3"));

          let crossData = data;
          let option = {
            color: ["#34C1DD", "#AF9953", "#47346E"],
            tooltip: {
              trigger: "axis",
              textStyle: {
                fontSize: 30,
              },
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
            },
            title: {
              text: "单位:Nm³",
              padding: [40, 10, 10, 60],
              textStyle: {
                fontSize: 28,
                color: "#d6e7f9",
              },
            },
            legend: {
              icon: "circle",
              data: ["2020", "2021", "2022"],
              itemGap: 100,
              textStyle: {
                color: "#fff",
                fontSize: 28,
              },
            },
            grid: {
              left: "80px",
              right: "30px",
              // bottom: '3%',
              top: "25%",

              // containLabel: true
            },
            xAxis: [
              {
                type: "category",
                boundaryGap: true,
                data: [
                  "1月",
                  "2月",
                  "3月",
                  "4月",
                  "5月",
                  "6月",
                  "7月",
                  "8月",
                  "9月",
                  "10月",
                  "11月",
                  "12月",
                ],
                axisLabel: {
                  interval: 0,
                  textStyle: {
                    color: "#d6e7f9",
                    fontSize: "28", //字体大小
                  },
                },
              },
            ],
            yAxis: {
              type: "value",
              splitLine: {
                lineStyle: {
                  color: "#77b3f169",
                },
              },
              axisLabel: {
                textStyle: {
                  color: "#d6e7f9",
                  fontSize: "28", //字体大小
                },
              },
            },
            series: [
              {
                name: "2020",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#34C1DD",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data0,
              },
              {
                name: "2021",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#AF9953",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data1,
              },
              {
                name: "2022",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#47346E",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data2,
              },
            ],
          };
          cross1.setOption(option);
        },

        // 营收骤降 面积图4
        getCross4(data) {
          const cross1 = echarts.init(document.getElementById("cross4"));

          let crossData = data;
          let option = {
            color: ["#34C1DD", "#AF9953", "#47346E"],
            // color: ['', '#AF9953', '#34C1DD'],
            tooltip: {
              trigger: "axis",
              textStyle: {
                fontSize: 30,
              },
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
            },
            title: {
              text: "单位:元",
              padding: [40, 10, 10, 60],
              textStyle: {
                fontSize: 28,
                color: "#d6e7f9",
              },
            },
            legend: {
              icon: "circle",
              data: ["2020", "2021", "2022"],
              itemGap: 100,
              textStyle: {
                color: "#fff",
                fontSize: 28,
              },
            },
            grid: {
              left: "80px",
              right: "30px",
              // bottom: '3%',
              top: "25%",

              // containLabel: true
            },
            xAxis: [
              {
                type: "category",
                boundaryGap: true,
                data: [
                  "1月",
                  "2月",
                  "3月",
                  "4月",
                  "5月",
                  "6月",
                  "7月",
                  "8月",
                  "9月",
                  "10月",
                  "11月",
                  "12月",
                ],
                axisLabel: {
                  interval: 0,
                  textStyle: {
                    color: "#d6e7f9",
                    fontSize: "28", //字体大小
                  },
                },
              },
            ],
            yAxis: {
              type: "value",
              splitLine: {
                lineStyle: {
                  color: "#77b3f169",
                },
              },
              axisLabel: {
                textStyle: {
                  color: "#d6e7f9",
                  fontSize: "28", //字体大小
                },
              },
            },
            series: [
              {
                name: "2020",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#34C1DD",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data0,
              },
              {
                name: "2021",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#AF9953",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data1,
              },
              {
                name: "2022",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#47346E",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data2,
              },
            ],
          };
          cross1.setOption(option);
        },
        // 社保基数减少 面积图5
        getCross5(data) {
          const cross1 = echarts.init(document.getElementById("cross5"));

          let crossData = data;
          let option = {
            color: ["#34C1DD", "#AF9953", "#47346E"],
            tooltip: {
              trigger: "axis",
              textStyle: {
                fontSize: 30,
              },
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
            },
            title: {
              text: "单位:元",
              padding: [40, 10, 10, 60],
              textStyle: {
                fontSize: 28,
                color: "#d6e7f9",
              },
            },
            legend: {
              icon: "circle",
              data: ["2020", "2021", "2022"],
              itemGap: 100,
              textStyle: {
                color: "#fff",
                fontSize: 28,
              },
            },
            grid: {
              left: "80px",
              right: "30px",
              // bottom: '3%',
              top: "25%",

              // containLabel: true
            },
            xAxis: [
              {
                type: "category",
                boundaryGap: true,
                data: [
                  "1月",
                  "2月",
                  "3月",
                  "4月",
                  "5月",
                  "6月",
                  "7月",
                  "8月",
                  "9月",
                  "10月",
                  "11月",
                  "12月",
                ],
                axisLabel: {
                  interval: 0,
                  textStyle: {
                    color: "#d6e7f9",
                    fontSize: "28", //字体大小
                  },
                },
              },
            ],
            yAxis: {
              type: "value",
              splitLine: {
                lineStyle: {
                  color: "#77b3f169",
                },
              },
              axisLabel: {
                textStyle: {
                  color: "#d6e7f9",
                  fontSize: "28", //字体大小
                },
              },
            },
            series: [
              {
                name: "2020",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#34C1DD",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data0,
              },
              {
                name: "2021",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#AF9953",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data1,
              },
              {
                name: "2022",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#47346E",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data2,
              },
            ],
          };
          cross1.setOption(option);
        },
        //社保人 面积图6
        getCross6(data) {
          const cross1 = echarts.init(document.getElementById("cross6"));

          let crossData = data;
          let option = {
            color: ["#34C1DD", "#AF9953", "#47346E"],
            tooltip: {
              trigger: "axis",
              textStyle: {
                fontSize: 30,
              },
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
            },
            title: {
              text: "单位:人",
              padding: [40, 10, 10, 60],
              textStyle: {
                fontSize: 28,
                color: "#d6e7f9",
              },
            },
            legend: {
              icon: "circle",
              data: ["2020", "2021", "2022"],
              itemGap: 100,
              textStyle: {
                color: "#fff",
                fontSize: 28,
              },
            },
            grid: {
              left: "80px",
              right: "30px",
              // bottom: '3%',
              top: "25%",

              // containLabel: true
            },
            xAxis: [
              {
                type: "category",
                boundaryGap: true,
                data: [
                  "1月",
                  "2月",
                  "3月",
                  "4月",
                  "5月",
                  "6月",
                  "7月",
                  "8月",
                  "9月",
                  "10月",
                  "11月",
                  "12月",
                ],
                axisLabel: {
                  interval: 0,
                  textStyle: {
                    color: "#d6e7f9",
                    fontSize: "28", //字体大小
                  },
                },
              },
            ],
            yAxis: {
              type: "value",
              splitLine: {
                lineStyle: {
                  color: "#77b3f169",
                },
              },
              axisLabel: {
                textStyle: {
                  color: "#d6e7f9",
                  fontSize: "28", //字体大小
                },
              },
            },
            series: [
              {
                name: "2020",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#34C1DD",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data0,
              },
              {
                name: "2021",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#AF9953",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data1,
              },
              {
                name: "2022",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#47346E",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data2,
              },
            ],
          };
          cross1.setOption(option);
        },

        // 税收骤降 面积图7
        getCross7(data) {
          const cross1 = echarts.init(document.getElementById("cross7"));

          let crossData = data;
          let option = {
            color: ["#34C1DD", "#AF9953", "#47346E"],
            tooltip: {
              trigger: "axis",
              textStyle: {
                fontSize: 30,
              },
              axisPointer: {
                type: "cross",
                label: {
                  backgroundColor: "#6a7985",
                },
              },
            },
            title: {
              text: "单位:元",
              padding: [40, 10, 10, 60],
              textStyle: {
                fontSize: 28,
                color: "#d6e7f9",
              },
            },
            legend: {
              icon: "circle",
              data: ["2020", "2021", "2022"],
              itemGap: 100,
              textStyle: {
                color: "#fff",
                fontSize: 28,
              },
            },
            grid: {
              left: "80px",
              right: "30px",
              // bottom: '3%',
              top: "25%",

              // containLabel: true
            },
            xAxis: [
              {
                type: "category",
                boundaryGap: true,
                data: [
                  "1月",
                  "2月",
                  "3月",
                  "4月",
                  "5月",
                  "6月",
                  "7月",
                  "8月",
                  "9月",
                  "10月",
                  "11月",
                  "12月",
                ],
                axisLabel: {
                  interval: 0,
                  textStyle: {
                    color: "#d6e7f9",
                    fontSize: "28", //字体大小
                  },
                },
              },
            ],
            yAxis: {
              type: "value",
              splitLine: {
                lineStyle: {
                  color: "#77b3f169",
                },
              },
              axisLabel: {
                textStyle: {
                  color: "#d6e7f9",
                  fontSize: "28", //字体大小
                },
              },
            },
            series: [
              {
                name: "2020",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#34C1DD",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data0,
              },
              {
                name: "2021",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#AF9953",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data1,
              },
              {
                name: "2022",
                type: "line",
                stack: "Total",
                smooth: true,
                lineStyle: {
                  width: 0,
                },
                showSymbol: false,
                areaStyle: {
                  opacity: 0.8,
                  color: "#47346E",
                },
                emphasis: {
                  focus: "series",
                },
                data: crossData.data2,
              },
            ],
          };
          cross1.setOption(option);
        },
        // 打开折线图
        openZxtFun() {
          console.log("打开折线图");
          this.showQyfx1 = true;
          this.$nextTick(() => {
            this.getCross1(this.ydl_cross);
            this.getCross2(this.ysl_cross);
            this.getCross3(this.yql_cross);
            this.getCross4(this.yszj_cross);
            this.getCross5(this.sbjs_cross);
            this.getCross6(this.sbrjs_cross);
            this.getCross7(this.sszj_cross);
          });
        },
        // 打开企业信用事件
        openQyxyFun() {
          console.log("打开企业信用事件");
          this.showXysj = true;
          this.showQyfx1 = false;

          this.showJy = false;
          this.showRc = false;
          this.showNh = false;
          this.showXh = false;
          document.getElementsByClassName("alt-qyfx")[0].style.display = "none";
        },
        backFun() {
          this.showXysj = false;
          this.showQyfx1 = false;
          document.getElementsByClassName("alt-qyfx")[0].style.display =
            "block";
        },
        // 打开雷达图
        openQyfxFun(name, index) {
          // console.log('window.top', window.top)
          // console.log('window.top.document', window.top.document)
          // console.log('window.top', window.top.document.querySelector('#page_middle'))
          console.log("打开企业风险预测弹窗");
          this.newCompanyNum = index;
          this.$nextTick(() => {
            this.getRadar1();
            this.getRadar2();
          });
        },

        treeCheck(node, list) {
          if (list.checkedKeys.length == 2) {
            //单选实现
            this.$refs.treeForm.setCheckedKeys([node.id]);
          }
        },
        checkChange(item, flag) {
          if (flag) {
            this.rmAll();
            this.mapObj = item;
            if (item.id == 4) {
              // this.upPopFun();
              this.rm3DText();
              return;
            }
            if (item.id == 5 || item.id == 6 || item.id == 7) {
              this.rm3DText();
              this.Histogram();
              return;
            }

            if (item.id < 15) {
              this.rm3DText();
              this.Histogram();
            } else {
              this.rmPopFun();
              this.addPoint(item.label);
            }
          }
        },
        addBankuai() {
          top.mapUtil.flyTo({
            x: 120.**************,
            y: 25.***************,
            z: 357215.**********,
            heading: 354.**************,
            tilt: 44.**************,
          });
          top.mapUtil.loadRegionLayer({
            layerid: "bankuai",
            data: [
              { name: "婺城区", color: [78, 107, 221, 1], height: 2800 },
              { name: "开发区", color: [78, 107, 221, 1], height: 2600 },
              { name: "金东区", color: [46, 81, 221, 1], height: 2400 },
              { name: "兰溪市", color: [78, 107, 221, 1], height: 2200 },
              { name: "浦江县", color: [110, 133, 221, 1], height: 2000 },
              { name: "义乌市", color: [110, 133, 221, 1], height: 1800 },
              { name: "东阳市", color: [78, 107, 221, 1], height: 1600 },
              { name: "磐安县", color: [110, 133, 221, 1], height: 1400 },
              { name: "永康市", color: [46, 81, 221, 1], height: 1200 },
              { name: "武义县", color: [110, 133, 221, 1], height: 1000 },
            ],
            onclick: function (e) {},
          });
        },
        //区划地图柱状图
        Histogram() {
          this.addBankuai();
          let data = [
            {
              pos: [119.*************, 29.***************, 0],
              text: `浦江县\n${Math.ceil(Math.random() * 200 + 50)}个`,
              color: [255, 255, 255, 1],
              type: "",
            },
            {
              pos: [119.**************, 29.***************, 0],
              text: `兰溪市\n${Math.ceil(Math.random() * 200 + 50)}个`,
              color: [255, 255, 255, 1],
              type: "",
            },
            {
              pos: [119.**************, 28.***************, 0],
              text: `婺城区\n${Math.ceil(Math.random() * 200 + 50)}个`,
              color: [255, 255, 255, 1],
              type: "",
            },
            {
              pos: [119.**************, 29.**************, 0],
              text: `金义新区\n${Math.ceil(Math.random() * 200 + 50)}个`,
              color: [255, 255, 255, 1],
              type: "",
            },
            {
              pos: [120.00438915928201, 29.28180844822686, 0],
              text: `义乌市\n${Math.ceil(Math.random() * 200 + 50)}个`,
              color: [255, 255, 255, 1],
              type: "",
            },
            {
              pos: [119.67587615578625, 28.777011171587162, 0],
              text: `武义县\n${Math.ceil(Math.random() * 200 + 50)}个`,
              color: [255, 255, 255, 1],
              type: "",
            },
            {
              pos: [120.04986352478184, 28.987053762616034, 0],
              text: `永康市\n${Math.ceil(Math.random() * 200 + 50)}个`,
              color: [255, 255, 255, 1],
              type: "",
            },
            {
              pos: [120.34254935807531, 29.305565856200392, 0],
              text: `东阳市\n${Math.ceil(Math.random() * 200 + 50)}个`,
              color: [255, 255, 255, 1],
              type: "",
            },
            {
              pos: [120.50638710113705, 29.009431602110855, 0],
              text: `磐安县\n${Math.ceil(Math.random() * 200 + 50)}个`,
              color: [255, 255, 255, 1],
              type: "",
            },
          ];
          top.mapUtil.loadTextLayer({
            layerid: "3Dtext",
            data: data,
            style: {
              size: 35,
            },
          });
          let barData = [
            { name: "浦江县", num: 0 },
            { name: "兰溪市", num: 0 },
            { name: "婺城区", num: 0 },
            { name: "金义新区", num: 0 },
            { name: "义乌市", num: 0 },
            { name: "武义县", num: 0 },
            { name: "永康市", num: 0 },
            { name: "东阳市", num: 0 },
            { name: "磐安县", num: 0 },
          ];
          for (let i = 0; i < barData.length; i++) {
            barData[i].num = Math.ceil(Math.random() * 200 + 50);
          }
          top.mapUtil.loadHistogram({
            data: barData,
          });
        },
        // 自定义经济弹窗
        async upPopFun() {
          var textData = await $get("personInTime");
          let maxValueArr = [];
          let maxValue = "";
          textData.map((item) => {
            item.value[0] = Math.ceil(Math.random() * 300);
            item.value[1] = Math.ceil(Math.random() * 300);
            item.value[2] = Math.ceil(Math.random() * 300);
            // 所有值中的最大值
            let a = +item.value[0];
            let b = +item.value[1];
            let c = +item.value[2];
            maxValueArr.push(a);
            maxValueArr.push(b);
            maxValueArr.push(c);
          });
          maxValue = Math.max(...maxValueArr);

          for (let i = 0; i < textData.length; i++) {
            let a1 = parseInt(Number(textData[i].value[0]));
            let a2 = parseInt(Number(textData[i].value[1]));
            let a3 = parseInt(Number(textData[i].value[2]));
            let a1_res = (a1 / maxValue).toFixed(2);
            let a2_res = (a2 / maxValue).toFixed(2);
            let a3_res = (a3 / maxValue).toFixed(2);
            console.log("最大值===》", a1_res, a2_res, a3_res);
            const url = `${baseURL.url}/static/citybrain/hjbh/img/rkzt/rkpc_bg.png`;
            let objData = {
              position: textData[i].pos,
              offset: [170, 20],
              content: `
                          <div style="min-width: 440px;overflow: hidden; height: 270px; position: relative;background: url('${url}') no-repeat;background-size: 100% 100%;">
                               <p style="position: absolute;
                               right: 45px;top: 30px;
               font-size: 28px;
               background-image: linear-gradient(180deg, #f7ad47, #fff, #ffd8a1);
               background-clip: text;
               -webkit-background-clip: text;
               color: transparent;
               font-weight: bold;
               font-style: italic;
               width: 160px;
              height: 30px;
               line-height: 30px;
               text-align: center;">${textData[i].name}</p>
                               <div style="width: 345px;overflow: hidden;
               position: absolute;
               top: 79px;
               left: 24px;
               height: 156px;
               padding: 0 20px;
               color: #fff;">
                                   <div style="font-size: 20px;height:33px">
                                       <span>互联网相关服务业：</span>
                                       <span style=" font-size: 20px;
               font-weight: 600;
               background-image: linear-gradient(180deg, #f7ad47, #fff, #ffd8a1);
               background-clip: text;
               -webkit-background-clip: text;
               color: transparent;">${textData[i].value[0]}户</span>
                                   </div>
                                   <div style="width: 100%;
               height: 8px;
               background-color: #144363;
               position: relative;
               border-radius: 12px;
               margin-bottom: 15px;">
                                       <div style="height: 8px !important;
               position: absolute;
               top: -1px;
               left: 0;
               border-radius: 12px;
               z-index: 100;background-image: linear-gradient(10deg, #ff4c4c, #ffa1a1);
               width:${
                 // a1 > 310 ? '100%' : a1 + 'px'
                 // a1_res * 310
                 a1_res * 100
               }%
               "></div>
                                   </div>
                                   <div style="font-size: 20px;height:33px">
                                       <span>文化数字内容服务业：</span>
                                       <span style=" font-size: 20px;
               font-weight: 600;
               background-image: linear-gradient(180deg, #f7ad47, #fff, #ffd8a1);
               background-clip: text;
               -webkit-background-clip: text;
               color: transparent;">${textData[i].value[1]}户</span>
                                   </div>
                                   <div style="width: 100%;
               height: 8px;
               background-color: #144363;
               position: relative;
               border-radius: 12px;
               margin-bottom: 15px;">
                                       <div style="height: 8px !important;
               position: absolute;
               top: -1px;
               left: 0;
               border-radius: 12px;
               z-index: 100;background-image: linear-gradient(10deg, #e9a53a, #e7aba3);width: ${
                 // a2 > 310 ? '100%' : a2 + 'px'
                 a2_res * 100
               }%"></div>
                                   </div>
                                   <div style="font-size: 20px;height:33px;display: flex;">
                                       <span style="white-space: nowrap;">
                                         软件技术服务业：</span>
                                       <span style=" font-size: 20px;white-space: nowrap;
               font-weight: 600;
               color:#00C0FF">${textData[i].value[2]}户
               </span>
                                   </div>
                                   <div style="width: 100%;
               height: 8px;
               background-color: #144363;
               position: relative;
               border-radius: 12px;
               margin-bottom: 15px;">
                                       <div style="height: 8px !important;
               position: absolute;
               top: -1px;
               left: 0;
               border-radius: 12px;
               z-index: 100; background-image: linear-gradient(10deg, #bb76db, #dcb4f3);width:${
                 // a3 > 310 ? '100%' : a3 + 'px'
                 // a3_res * 385
                 a3_res * 100
               }%"></div>
                                   </div>
                               </div>
                           </div>
                       `,
            };
            top.mapUtil._createPopup(objData);
          }
        },
        // 清楚弹窗
        rmPopFun() {
          top.mapUtil.removeAllLayers();
          //   top.mapUtil.removeLayer();
        },
        //清除柱状体

        rm3DText() {
          top.mapUtil.removeLayer("3DText");
        },
        getKeys(keys) {
          let obj = {};
          if (keys) {
            keys.forEach((item, i) => {
              obj[i] = item;
            });
          }
          return obj;
        },
        addPoint(name) {
          let tit = "市场监管_" + name;
          $get("/ys_point_all.json").then((res) => {
            let icon = res[tit].icon;
            let title = "",
              pointData = [],
              keyArr = [];
            let main = res[tit].main.map((a) => {
              title = a.data.title;
              keyArr = a.data.key;
              let valueArr = a.data.value;
              let str = {
                data: {
                  key: keyArr,
                  value: valueArr,
                  data: a.data,
                },
                lng: a.point.split(",")[0],
                lat: a.point.split(",")[1],
              };
              if (valueArr) {
                valueArr.forEach((item, i) => {
                  str[i] = item;
                });
              }
              pointData.push(str);
            });
            top.mapUtil.loadPointLayer({
              data: pointData,
              layerid: "sczt-" + icon, //图层id
              iconcfg: { image: "/static/spritesImage/" + icon + ".png" }, //图标
              onclick: this.pointClick,
              popcfg: {
                offset: [50, -100],
                dict: this.getKeys(keyArr),
                show: true,
              },
            });
          });
        },

        //清除点位
        rmAll() {
          top.mapUtil.removeAllLayers();
        },
      },
      created() {
        // this.$nextTick(() => {
        //   this.openQyfxFun()
        // })
      },
      mounted() {
        let _this = this;
        $api("scztjg_middle01").then((res) => {
          this.radar5 = res;
        });
        $api("scztjg_middle02").then((res) => {
          this.radar6 = res;
        });
        $api("scztjg_middle03").then((res) => {
          this.gsData = res;
        });
        $api("scztjg_middle04").then((res) => {
          this.jyEchart1 = res;
        });
        $api("scztjg_middle05").then((res) => {
          this.jyEchart2 = res;
        });
        $api("scztjg_middle06").then((res) => {
          this.jyTable = res;
        });
        $api("scztjg_middle07").then((res) => {
          this.rcEchartData = res;
        });
        $api("scztjg_middle08").then((res) => {
          this.rcPieData = res;
        });
        $api("scztjg_middle09").then((res) => {
          this.rcPieData1 = res;
        });
        $api("scztjg_middle10").then((res) => {
          this.rcTable = res;
        });
        $api("scztjg_middle11").then((res) => {
          this.nhEchart0 = res;
        });
        $api("scztjg_middle12").then((res) => {
          this.nhEchart1 = res;
        });
        $api("scztjg_middle13").then((res) => {
          this.nhEchart2 = res;
        });
        $api("scztjg_middle14").then((res) => {
          this.nhTable = res;
        });
        $api("scztjg_middle15").then((res) => {
          this.xhEchart2 = res;
        });
        $api("scztjg_middle16").then((res) => {
          this.xhPieData1 = res;
        });
        $api("scztjg_middle17").then((res) => {
          this.xhTable = res;
        });
        $api("scztjg_middle18").then((res) => {
          this.treeData = res;
        });

        window.addEventListener("message", function (e) {
          if (
            e.data &&
            e.data.type == "bankuaiClick" &&
            (_this.mapObj.id == "2" || _this.mapObj.id == "3")
          ) {
            window.parent.postMessage(
              JSON.stringify({
                type: "openIframe",
                name: "map_line",
                src:
                  baseURL.url +
                  "/static/citybrain/scjg/commont/scztjg/map_line.html",
                left: "2166px",
                top: "850px",
                width: "505px",
                height: "355px",
                zIndex: "10",
              }),
              "*"
            );
          }
        });
        this.initFun();
        // document.getElementsByClassName('alt-qyfx')[0].style.display = 'block'
        // this.closeWindow()
        // this.openQyfxFun()
      },
    });
  </script>
</html>

<style>
  .toolbar {
    width: 450px;
    height: 520px;
    border: 1px #576c80 solid;
    background-color: #051431;
    position: absolute;
    top: 0;
    left: 60px;
    padding: 20px 30px;
    box-sizing: border-box;
    overflow-y: scroll;
  }

  /* 设置滚动条的样式 */
  .toolbar::-webkit-scrollbar {
    width: 10px;
  }

  /* 滚动槽 */
  .toolbar::-webkit-scrollbar-track {
    border-radius: 5px;
  }

  /* 滚动条滑块 */
  .toolbar::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: rgba(35, 144, 207, 0.8);
  }

  .toolbar::-webkit-scrollbar-thumb:window-inactive {
    background: rgba(27, 146, 215, 1);
  }

  .el-tree {
    background: none;
    color: #fff;
    font-size: 30px;
  }

  .el-tree-node__label {
    font-size: 30px;
  }

  .el-tree-node__content {
    height: 50px;
    line-height: 50px;
  }

  .node-lable {
    line-height: 3.125rem;
    font-size: 30px;
    font-family: PangMenZhengDao;
    font-weight: bold;
    color: #c0d6ed;
    line-height: 58px;
  }

  .node-img {
    position: relative;
    left: 20px;
    top: 10px;
    width: 40px;
  }

  /* .el-tree-node__expand-icon{
        display: none;
    } */
  .el-tree-node.is-current > .el-tree-node__content,
  .el-tree-node__content:hover {
    background: linear-gradient(
      94deg,
      rgba(3, 97, 156, 0) 0%,
      #03619c 100%
    ) !important;
    border-radius: 0px 30px 30px 0px;
  }

  .el-tree-node__content {
    height: 50px;
  }

  .el-checkbox__inner {
    width: 20px;
    height: 20px;
  }

  .el-checkbox__inner::after {
    border: 3px #fff solid;
    width: 7px;
    height: 14px;
    left: 3px;
    top: -2px;
    border-left: 0;
    border-top: 0;
  }

  .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
    height: 4px;
    top: 8px;
  }
</style>
