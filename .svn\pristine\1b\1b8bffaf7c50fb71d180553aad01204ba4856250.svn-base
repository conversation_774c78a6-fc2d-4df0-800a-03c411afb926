@charset "UTF-8";
[v-cloak]{
display: none;
}

/* 密接和入境人员的样式 */
.mjry{
  margin-top: 30px;
}


/* 设置风险人员概况和人员管控的样式 */
.top{
  width: 100%;
  display: flex;
  justify-content: space-between;
  position: relative;
}
.top .left{
  width: 50%;
  padding-right: 30px;
  box-sizing: border-box;
}
.top .right{
  width: 50%;
  padding-left: 30px;
  box-sizing: border-box;
}


.top .fxry_header {
  width: 100% !important;
  background: none !important;
  /* background-position: 0 55px !important; */
}
.top .bgImg{
  width: 100%;
  position: absolute;
  top: 55px;
}

.top .fxry_header .title_hr {
  background-image: none !important;
}
.top .fxry_header .title_hr{
  width: auto !important;
}

/* 饼图左侧的文字样式 */

.left_1 {
  display: flex;
  height: calc(100% - 56px);
  margin: 20px 0 20px 130px;
}
.left_1_0 .text{
  color: rgb(231, 231, 231);
  font-size: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  top: 35%;
}
.left_1_0 .text .num{
  margin:0 10px;  
  color: #fff;
}
.left_1_0 .text .num{
  font-size: 53px;  
}
.left_1_0 .text>span{
  font-size: 27px;
}
.left_1_0{
  margin-right: 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  position: relative;
}
.left_1_1{
  margin-top:60px;
  display: flex;
  flex-direction: column;
  margin-left: 80px;
}

.left_1_1_0_0 {
  display: flex;
  align-items: center;
  height: 34px;
  margin-bottom: 60px;
}

.left_1_1_0_0_0 {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #00c0ff;
  margin-right: 20px;
}

.left_1_1_0_0_1 {
  font-family: SourceHanSansCN-Regular;
  font-size: 34px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #d6e7f9;
}
.left_1_1_0_0_1>span{
  font-size: 50px;
  background-image: linear-gradient(to bottom, #fff, #a7d7ff, #015192);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  margin-left: 10px;
}

.left_1_1_0_0_2 {
  font-family: DIN-Bold;
  font-size: 50px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 5px;
  background-image: linear-gradient(360deg, #ffeaae, #ffdb85, #ffcb5b);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  margin-left: 20px;
  font-weight: 700;
}




/* 原先的代码 */
.fxry_app_box {
  position: relative;
  box-sizing: border-box;
  width: 2045px;
  height: 1850px;
  background-image: url("../img/bg.png");
  background-size: 100% 100%;
  padding: 10px 55px 30px;
}

.fxry_app_box .fxry_header {
  width: 1935px;
  display: flex;
  height: 130px;
  align-items: center;
  justify-content: space-between;
  background: url("../img/一级标题3.png") no-repeat;
  background-position: 0 55px;
}

.fxry_app_box .fxry_header .title {
  font-size: 54px;
  /**background-image: -webkit-linear-gradient(top, #ffffff, #3883ff);**/
  background: linear-gradient(to bottom, #ffffff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  padding-bottom: 10px;
}

.fxry_app_box .fxry_header .title .title_icon_one {
  display: inline-block;
  margin-right: 10px;
  width: 78px;
  height: 75px;
  vertical-align: bottom;
  background-image: url("../img/一级标题1.png");
  background-size: 100% 100%;
}

.fxry_app_box .fxry_header .title_hr {
  position: relative;
  /**left: 30px;**/
  width: 50%;
  height: 21px;
  background-image: url("../img/一级标题2.png");
  background-size: 100% 100%;
}

.fxry_app_box .fxry_container .qsfx {
  padding-top: 15px;
  padding-bottom: 15px;
}

.fxry_app_box .fxry_container .qsfx .qsfx_head {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fxry_app_box .fxry_container .qsfx .qsfx_head h2 {
  font-size: 40px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}

.fxry_app_box .fxry_container .qsfx .qsfx_head i {
  display: inline-block;
}

.fxry_app_box .fxry_container .qsfx .qsfx_head .icon_left {
  width: 142px;
  height: 53px;
  background: url("../img/二级标题左.png") no-repeat center;
  background-size: 100% 100%;
  margin-right: 30px;
}

.fxry_app_box .fxry_container .qsfx .qsfx_head .icon_right {
  width: 142px;
  height: 53px;
  background: url("../img/二级标题右.png") no-repeat center;
  background-size: 100% 100%;
  margin-left: 30px;
}
.fxry_app_box .fxry_container .cdjdyfx {
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: center;
}


.fxry_app_box
  .fxry_container
  .cdjdyfx
  .cdjdyfx_item
  .cdjdyfx_item_title
  .point {
  color: #ffffff;
  font-size: 32px;
  white-space: nowrap;
}

.fxry_app_box
  .fxry_container
  .cdjdyfx
  .cdjdyfx_item
  .cdjdyfx_item_title
  .jdbackground {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 360px;
  height: 70px;
  background: url("../img/zhcg/组2400.png");
  background-size: 100% 100%;
}

.fxry_app_box
  .fxry_container
  .cdjdyfx
  .cdjdyfx_item
  .cdjdyfx_item_title
  .jdbackground
  .normplant {
  font-size: 54px;
  background-image: -webkit-linear-gradient(top, #ffffff, #ffcf7b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  margin-top: -20px;
}

.fxry_app_box
  .fxry_container
  .cdjdyfx
  .cdjdyfx_item
  .cdjdyfx_item_title
  .jdbackground
  .normplant
  span {
  font-size: 28px;
}

.fxry_app_box .fxry_container .cdjdyfx .cdjdyfx_item .wtputopt_title {
  width: 940px;
  box-sizing: border-box;
  padding: 40px 25px 0;
  color: #ffffff;
  font-size: 28px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table {
  color: #ffffff;
  font-size: 28px;
  margin-top: 40px;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table i {
  display: inline-block;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .online {
  color: #ffffff;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .online i {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #ffffff;
  vertical-align: middle;
  margin-right: 10px;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .outline {
  color: red;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .outline i {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: red;
  vertical-align: middle;
  margin-right: 10px;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .blue {
  height: 60px;
  background: #26476b;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .black {
  height: 60px;
  background: #0c2948;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table tr {
  margin-bottom: 20px;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .cell {
  font-weight: normal;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table th {
  width: 20%;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .el-table__row td {
  width: 20%;
}

.fxry_app_box .fxry_container .cdjdyfx .zhcg_table .el-table__row .right_cell {
  width: 40%;
}

.el-table__body-wrapper::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  background-color: skyblue;
  background-image: -webkit-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #ededed;
  border-radius: 5px;
}
.qsfenxbac{
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 360px;
  height: 270px;
  background: url('../img/adm/组1062076.png');
  background-size: 80% 48%;
  background-repeat: no-repeat;
  background-position: 50% 80%;
}

.zlContent p {
  width: 300px;
  color: #d6e7f9!important;
  font-size: 33px;
  padding: 20px;
}

.weekList {
  display: flex;
  padding: 0 70px;
}
.weekList .weekCont {
  width: 33%;
  display: flex;
}
.weekList .weekCont .contLis {
  
}
.weekList .weekCont .contLis h6{
  font-size: 28px;
  color: #d6e7f9!important;
  width: 180px;
}
.weekList .weekCont .contLis p{
  width: 180px;
  font-size: 40px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  padding: 0;
}

.botList {
  padding: 0 70px;
}

.botList ul li{
  display: flex; 
}
.botList ul li div {
  margin-bottom: 18px;
  margin-right: 18px;
  border-top: 6px solid #29608f;
  display: flex;
  width: 50%;
  background: rgba(14, 37, 66, 0.2);
}
.botList ul li div p:nth-child(2){
  width: 500px;
  font-size: 40px;
  line-height: 92px;
  background-image: -webkit-linear-gradient(top, #ebf2ff, #3883ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  padding: 0;
}