<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>领域3左侧面板</title>
        <script src="/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain3840/shgl/css/ly3-left.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/hjbh/js/echarts-liquidfill.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <!-- 轮播toolTip -->
    </head>
    <style>
        .table-con {
            height: 300px;
        }
        .lctbox {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        .lrdivitem {
            width: 100px;
            height: 69px;
            border-color: rgb(36, 198, 228);
            border-width: 1px;
            border-style: solid;
            color: rgb(255, 255, 255);
            font-size: 14px;
            padding: 0px;
            text-align: center;
            line-height: 20px;
            font-weight: normal;
            font-style: normal;
            background: rgb(0, 49, 111);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
        .lrdivitem > div {
            font-size: 20px;
            color: #fff;
            text-align: center;
            line-height: 28px;
        }
        .lrdivitem1 {
            width: 130px;
            margin-bottom: 20px;
        }
        .lrdivitem2 {
            width: 140px;
            margin-bottom: 20px;
        }
        .jtdiv {
            font-size: 32px;
            font-weight: 700;
            margin-top: -20px;
            color: rgb(0, 203, 143);
        }
    </style>
    <body>
        <div id="app" class="container" v-cloak>
            <nav>
                <s-header-title-2 htype="1" title="监管执法分析展示"></s-header-title-2>
            </nav>
            <div style="height: 300px; display: flex; justify-content: center">
                <div class="lctbox">
                    <div class="lrdivitem lrdivitem1">
                        <div>行政检查</div>
                    </div>
                    <div class="lrdivitem lrdivitem1">
                        <div>行政处罚</div>
                    </div>
                    <div class="lrdivitem lrdivitem1">
                        <div>行政强制</div>
                    </div>
                </div>
                <div class="lctbox">
                    <div class="jtdiv">→</div>
                </div>
                <div class="lctbox">
                    <div class="lrdivitem lrdivitem1">
                        <div>行政执法与刑事司法衔接</div>
                    </div>
                    <div class="lrdivitem lrdivitem1">
                        <div>实现监管业务数据全流程闭环管理</div>
                    </div>
                </div>
                <div class="lctbox">
                    <div class="jtdiv">→</div>
                </div>
                <div class="lctbox">
                    <div class="lrdivitem lrdivitem1">
                        <div>安全生产治理</div>
                    </div>
                    <div class="lrdivitem lrdivitem1">
                        <div>公共卫生服务</div>
                    </div>
                    <div class="lrdivitem lrdivitem1">
                        <div>食品药品安全监管</div>
                    </div>
                </div>
                <div class="lctbox">
                    <div class="jtdiv">→</div>
                </div>
                <div class="lctbox">
                    <div class="lrdivitem lrdivitem1">
                        <div>综合监测分析</div>
                    </div>
                </div>
                <div class="lctbox">
                    <div class="jtdiv">→</div>
                </div>
                <div class="lctbox">
                    <div class="lrdivitem lrdivitem1">
                        <div>放</div>
                    </div>
                    <div class="lrdivitem lrdivitem1">
                        <div>管</div>
                    </div>
                    <div class="lrdivitem lrdivitem1">
                        <div>服</div>
                    </div>
                </div>
            </div>

            <div class="top-con">
                <li
                    v-for="(item,index) in tabList"
                    :key="index"
                    :class="currentIndex===index?'active':''"
                    @click="changeTab(index,item)"
                >
                    {{item.name}}
                </li>
            </div>
            <div id="jgzf-chart"></div>
            <nav style="position: relative">
                <s-header-title-2 htype="1" title="市场监管分析展示"></s-header-title-2>
                <div
                    style="
                        position: absolute;
                        background-color: #5eb0f5;
                        color: #fff;
                        height: 40px;
                        right: 20px;
                        top: 15px;
                        font-size: 22px;
                        cursor: pointer;
                        line-height: 40px;
                        width: 100px;
                        text-align: center;
                    "
                    @click="openXq()"
                >
                    数据汇集
                </div>
            </nav>
            <div id="sq-chart"></div>
            <!-- <div class="btn">详情</div> -->
            <div class="table-con">
                <li v-for="(item,index) in tableList" :key="index">
                    <img src="/static/citybrain3840/shgl/img/alert1.png" alt="" v-if="item.state==1" />
                    <img src="/static/citybrain3840/shgl/img/alert2.png" alt="" v-if="item.state==2" />
                    <div style="margin-left: 20px">{{item.name}}</div>
                    <div style="margin-left: 80px">{{item.time}}</div>
                </li>
            </div>
            <div id="scjg-chart"></div>
        </div>
    </body>
    <script type="module">
        new Vue({
            el: "#app",
            data: {
                nowTime: "", //当前时间
                time: null,
                dom: null,
                scpDom: null,
                tabList: [
                    {
                        name: "热点问题",
                        id: "1",
                    },
                    {
                        name: "难点问题",
                        id: "2",
                    },
                    {
                        name: "痛点问题",
                        id: "3",
                    },
                ],
                currentIndex: 0,
                tableList: [],
            },
            methods: {
                openXq() {
                    top.commonObj.funOpenIframe({
                        width: "1900px",
                        height: "970px",
                        zIndex: "999",
                        src: "/static/citybrain3840/shgl/pages/ly3-left-dialog.html",
                        left: "945px",
                        top: "275px",
                        name: "ly3-left-dialog",
                    });
                },
                changeTab(index, data) {
                    this.currentIndex = index;
                    this.getJgzffxData(index);
                },
                init() {
                    $api("ldst_shgl_ly3", { type: 6 }).then((res) => {
                        this.BarchartsShow(res);
                    });
                    $api("ldst_shgl_ly3", { type: 4 }).then((res) => {
                        this.sqchartsShow(res);
                    });
                    $api("ldst_shgl_ly3", { type: 5 }).then((res) => {
                        this.tableList = res;
                    });
                    this.getJgzffxData(0);
                },
                getJgzffxData(index) {
                    if (index === 0) {
                        $api("ldst_shgl_ly3", { type: 1 }).then((res) => {
                            this.BarEchart01("jgzf-chart", res);
                        });
                    } else if (index === 1) {
                        $api("ldst_shgl_ly3", { type: 2 }).then((res) => {
                            this.BarEchart01("jgzf-chart", res);
                        });
                    } else {
                        $api("ldst_shgl_ly3", { type: 3 }).then((res) => {
                            this.BarEchart01("jgzf-chart", res);
                        });
                    }
                },
                //绘制水球图
                sqchartsShow(data) {
                    const myChartsState = echarts.init(document.getElementById("sq-chart"));
                    var fontColor = "#30eee9";
                    let x = data.map((item) => {
                        return item.name;
                    });
                    let y1 = data.map((item) => {
                        return item.value;
                    });
                    let option = {
                        series: [
                            {
                                type: "liquidFill",
                                //data: [0.6, 0.5, 0.4, 0.3],
                                data: [data[0], 0.2, 0.4],
                                radius: "70%",
                                // 水球颜色
                                color: ["#49d088", "#38b470", "#2aaf66"],
                                center: ["15%", "50%"],
                                // outline  外边
                                outline: {
                                    // show: false
                                    borderDistance: 5,
                                    itemStyle: {
                                        borderWidth: 5,
                                        borderColor: "#13FDCE",
                                    },
                                },
                                label: {
                                    normal: {
                                        // textStyle: {
                                        color: "#fff",
                                        insideColor: "#fff",
                                        fontSize: 23,
                                        lineHeight: 34,
                                        formatter: function (parms) {
                                            return parms.data.name + "\n" + parms.data.value * 100 + "次";
                                        },
                                        // }
                                    },
                                },
                                // 内图 背景色 边
                                backgroundStyle: {
                                    color: "#2e4562",
                                    // borderWidth: 5,
                                    // borderColor: 'red',
                                },
                            },
                            {
                                type: "liquidFill",
                                //data: [0.6, 0.5, 0.4, 0.3],
                                data: [data[1], 0.2, 0.3],
                                radius: "70%",
                                // 水球颜色
                                color: ["#FE5555", "#F07581", "#FB5E61"],
                                center: ["38%", "50%"],
                                // outline  外边
                                outline: {
                                    // show: false
                                    borderDistance: 5,
                                    itemStyle: {
                                        borderWidth: 5,
                                        borderColor: "#FE5555",
                                    },
                                },
                                label: {
                                    normal: {
                                        // textStyle: {
                                        color: "#fff",
                                        insideColor: "#fff",
                                        fontSize: 22,
                                        lineHeight: 34,
                                        formatter: function (parms) {
                                            return parms.data.name + "\n" + parms.data.value * 100 + "次";
                                        },
                                        // },
                                    },
                                },
                                // 内图 背景色 边
                                backgroundStyle: {
                                    color: "#2e4562",
                                    // borderWidth: 5,
                                    // borderColor: 'red',
                                },
                            },
                            {
                                type: "liquidFill",
                                //data: [0.6, 0.5, 0.4, 0.3],
                                data: [data[2], 0.3, 0.2],
                                radius: "70%",
                                // 水球颜色
                                color: ["#FFBF11", "#F4B30E", "#EACE36"],
                                center: ["62%", "50%"],
                                // outline  外边
                                outline: {
                                    // show: false
                                    borderDistance: 5,
                                    itemStyle: {
                                        borderWidth: 5,
                                        borderColor: "#FFBF11",
                                    },
                                },
                                label: {
                                    normal: {
                                        // textStyle: {
                                        color: "#fff",
                                        insideColor: "#fff",
                                        fontSize: 22,
                                        lineHeight: 34,
                                        formatter: function (parms) {
                                            return parms.data.name + "\n" + parms.data.value * 100 + "次";
                                        },
                                        // },
                                    },
                                },
                                // 内图 背景色 边
                                backgroundStyle: {
                                    color: "#2e4562",
                                    // borderWidth: 5,
                                    // borderColor: 'red',
                                },
                            },
                            {
                                type: "liquidFill",
                                //data: [0.6, 0.5, 0.4, 0.3],
                                data: [data[3], 0.2, 0.34],
                                radius: "70%",
                                // 水球颜色
                                color: ["#c821d4", "#9e41aa", "#73197f"],
                                center: ["85%", "50%"],
                                // outline  外边
                                outline: {
                                    // show: false
                                    borderDistance: 5,
                                    itemStyle: {
                                        borderWidth: 5,
                                        borderColor: "#af16d2",
                                    },
                                },
                                label: {
                                    normal: {
                                        // textStyle: {
                                        color: "#fff",
                                        insideColor: "#fff",
                                        fontSize: 22,
                                        lineHeight: 34,
                                        formatter: function (parms) {
                                            return parms.data.name + "\n" + parms.data.value * 100 + "次";
                                        },
                                        // },
                                    },
                                },
                                // 内图 背景色 边
                                backgroundStyle: {
                                    color: "#2e4562",
                                    // borderWidth: 5,
                                    // borderColor: 'red',
                                },
                            },
                        ],
                    };

                    myChartsState.setOption(option);
                    tools.loopShowTooltip(myChartsState, option, {
                        loopSeries: true,
                    }); //轮播
                },
                //绘制柱图
                BarchartsShow(data) {
                    const myChartsState = echarts.init(document.getElementById("scjg-chart"));
                    var fontColor = "#30eee9";
                    let x = data.map((item) => {
                        return item.name;
                    });
                    let y1 = data.map((item) => {
                        return item.value;
                    });
                    let y2 = data.map((item) => {
                        return item.value1;
                    });
                    let y3 = data.map((item) => {
                        return item.zb;
                    });
                    let option = {
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },

                        grid: {
                            left: "8%",
                            top: "18%",
                            right: "8%",
                            bottom: "15%",
                        },
                        legend: {
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                        },
                        xAxis: {
                            data: x,
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: "#aaa",
                                    width: 1,
                                },
                            },
                            axisTick: {
                                show: true, //隐藏X轴刻度
                                alignWithLabel: true,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff", //X轴文字颜色
                                    fontSize: 28,
                                },
                                interval: 0,
                                rotate: 30,
                            },
                        },
                        yAxis: [
                            {
                                type: "value",
                                name: "",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        width: 1,
                                        color: "#3d5269",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                            {
                                type: "value",
                                name: "",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                position: "right",
                                splitLine: {
                                    show: false,
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#fff",
                                        width: 2,
                                    },
                                },
                                axisLabel: {
                                    show: true,
                                    formatter: "{value} ", //右侧Y轴文字显示
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "违法数",
                                type: "bar",
                                barWidth: 30,
                                color: "#5087EC",
                                itemStyle: {
                                    normal: {
                                        color: "#5087EC",
                                    },
                                },
                                data: y1,
                            },
                            {
                                name: "违规数",
                                type: "bar",
                                barWidth: 30,
                                color: "#68bbc4",
                                itemStyle: {
                                    normal: {
                                        color: "#68bbc4",
                                    },
                                },
                                data: y2,
                            },
                            {
                                name: "占比",
                                type: "line",
                                yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                                showAllSymbol: true, //显示所有图形。
                                // symbol: "circle", //标记的图形为实心圆
                                symbolSize: 10, //标记的大小
                                itemStyle: {
                                    normal: {
                                        color: "#26D9FF",
                                        lineStyle: {
                                            color: "#26D9FF",
                                            width: 4,
                                        },
                                    },
                                },
                                data: y3,
                            },
                        ],
                    };
                    myChartsState.setOption(option);
                    tools.loopShowTooltip(myChartsState, option, {
                        loopSeries: true,
                    }); //轮播
                },
                BarEchart01(id, barData) {
                    const myEc = echarts.init(document.getElementById(id));
                    let yData = barData.map((item) => {
                        return item.name;
                    });
                    let value = barData.map((item) => {
                        return item.value;
                    });
                    let option = {
                        tooltip: {
                            trigger: "axis",
                            axisPointer: {
                                type: "shadow",
                            },
                            textStyle: {
                                fontSize: 24,
                            },
                        },
                        grid: {
                            top: "10%",
                            bottom: "10%",
                            left: "25%",
                            right: "5%",
                        },
                        xAxis: {
                            type: "value",

                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                fontSize: 24,
                                color: "#ffff",
                            },
                        },
                        yAxis: {
                            type: "category",
                            data: yData,
                            axisLabel: {
                                fontSize: 24,
                                color: "#ffff",
                            },
                        },
                        series: [
                            {
                                name: "数据",
                                type: "bar",
                                data: value,
                                label: {
                                    normal: {
                                        show: true,
                                        position: "right",
                                        formatter: "{c}",
                                        textStyle: {
                                            color: "#fff",
                                            fontSize: 20,
                                        },
                                    },
                                },
                            },
                        ],
                    };
                    myEc.setOption(option);
                },
            },
            //项目生命周期
            mounted() {
                this.init();
            },
        });
    </script>
</html>
