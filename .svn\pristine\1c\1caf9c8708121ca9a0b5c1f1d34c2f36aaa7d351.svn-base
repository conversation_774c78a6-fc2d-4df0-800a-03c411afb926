<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>市场监管详情</title>
        <script src="/static/citybrain/csdn/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <script src="/elementui/js/index.js"></script>
        <link rel="stylesheet" href="/static/css/sigma.css" />
        <link rel="stylesheet" href="/elementui/css/index.css" />
        <link rel="stylesheet" href="/static/css/animate_dn.css" />
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
        <style>
            #app {
                width: 1800px;
                /* height: 1630px; */
                height: 1300px;
                background: url("../../../img/bg.png") no-repeat;
                background-size: 100% 100%;
                padding-top: 20px;
                box-sizing: border-box;
                position: relative;
            }

            .closeDialog {
                position: absolute;
                right: 50px;
                top: 10px;
                color: white;
                font-size: 50px;
            }

            .line {
                width: 1700px;
                height: 400px;
                margin-top: 15px;
                margin-left: 50px;
            }

            .item {
                width: 820px;
                height: 300px;
                float: left;
            }

            .header-title2 .text {
                font-size: 24px !important;
            }
            .chart {
                width: 800px;
                height: 320px;
            }

            .longChart {
                width: 1700px;
                height: 320px;
            }
            .top-close {
                position: absolute;
                right: 0;
                top: 0;
                width: 80px;
                height: 80px;
                cursor: pointer;
                background-image: url("/static/images/common/components/close-1.png");
                background-size: 100% 100%;
            }
        </style>
    </head>

    <body>
        <div id="app">
            <div class="top-close" onclick="top.commonObj.funCloseIframe({name:'scjgDetail'});"></div>
            <div class="line">
                <nav style="padding: 0px 0">
                    <s-header-title2 style="width: 100%" title="市场主体监管" htype="2"></s-header-title2>
                </nav>
                <div class="item">
                    <div class="chart" id="chart1"></div>
                </div>
                <div class="item" style="margin-left: 50px">
                    <div class="chart" id="chart2"></div>
                </div>
            </div>
            <div class="line">
                <nav style="padding: 0px 0">
                    <s-header-title2 style="width: 100%" title="质量技术监管" htype="2"></s-header-title2>
                </nav>
                <div class="longChart" id="chart3"></div>
            </div>
            <div class="line">
                <nav style="padding: 0px 0">
                    <s-header-title2 style="width: 100%" title="信用监管分析" htype="2"></s-header-title2>
                </nav>
                <div class="item">
                    <div class="chart" id="chart4"></div>
                </div>
                <div class="item" style="margin-left: 50px">
                    <div class="chart" id="chart5"></div>
                </div>
            </div>
        </div>
    </body>
</html>
<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>
<script>
    var vm = new Vue({
        el: "#app",
        data: {},
        mounted() {
            this.initChart1();
            this.initChart2();
            this.initChart3();
            this.initChart4();
            this.initChart5();
        },
        methods: {
            initChart1() {
                let myChart = echarts.init(document.getElementById("chart1"));
                $api("ldst_xsqstjc_scjgDetail", { type: 1 }).then((res) => {
                    let data = res;
                    let color1 = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: "#00C0FF",
                        },
                        {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                        },
                    ]);
                    let color2 = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: "#2DF09F",
                        },
                        {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                        },
                    ]);
                    let option = {
                        color: [color1, color2],
                        tooltip: {
                            trigger: "axis",
                            borderWidth: 0,
                            axisPointer: {
                                // 坐标轴指示器，坐标轴触发有效
                                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                            },
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "28",
                            },
                        },
                        legend: {
                            data: ["新增企业", "新增个体户"],
                            textStyle: {
                                color: "#fff",
                                fontSize: 20,
                            },
                            itemGap: 30,
                        },
                        grid: {
                            top: 25,
                            left: 0,
                            right: 0,
                            bottom: 10,
                            containLabel: true,
                        },
                        itemStyle: {
                            borderRadius: 5,
                        },
                        xAxis: [
                            {
                                type: "category",
                                data: data.map((item) => item.name),
                                axisLabel: {
                                    color: "#fff",
                                    fontSize: 20,
                                },
                                axisPointer: {
                                    type: "shadow",
                                },
                            },
                        ],
                        yAxis: {
                            type: "value",

                            axisLabel: {
                                color: "#fff",
                                fontSize: 20,
                                formatter: "{value}户",
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.2)",
                                },
                            },
                        },
                        series: [
                            {
                                name: "新增企业",
                                type: "bar",
                                data: data.map((item) => item.value1),
                                barGap: "20%",
                                // barCategoryGap: 45,
                                barWidth: 18,
                            },
                            {
                                name: "新增个体户",
                                type: "bar",
                                data: data.map((item) => item.value2),
                                barWidth: 18,
                            },
                        ],
                    };
                    myChart.setOption(option);
                });
            },
            initChart2() {
                let myChart = echarts.init(document.getElementById("chart2"));
                $api("ldst_xsqstjc_scjgDetail", { type: 2 }).then((res) => {
                    let data = res;
                    let option = {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: "#00C0FF",
                            },
                            {
                                offset: 1,
                                color: "rgba(0,192,255,0)",
                            },
                        ]),
                        tooltip: {
                            trigger: "axis",
                            borderWidth: 0,
                            axisPointer: {
                                // 坐标轴指示器，坐标轴触发有效
                                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                            },
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "28",
                            },
                        },
                        legend: {
                            data: ["投资金额"],
                            textStyle: {
                                color: "#fff",
                                fontSize: 20,
                            },
                        },
                        grid: {
                            top: 25,
                            left: 0,
                            right: 0,
                            bottom: 10,
                            containLabel: true,
                        },
                        xAxis: [
                            {
                                type: "category",
                                data: data.map((item) => item.name),
                                axisLabel: {
                                    color: "#fff",
                                    fontSize: 20,
                                },
                                axisPointer: {
                                    type: "shadow",
                                },
                            },
                        ],
                        yAxis: [
                            {
                                type: "value",

                                interval: 2,
                                axisLabel: {
                                    color: "#fff",
                                    fontSize: 20,
                                    formatter: "{value}万元",
                                },
                                splitLine: {
                                    lineStyle: {
                                        color: "rgb(119,179,241,.2)",
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "投资金额",
                                type: "bar",
                                data: data.map((item) => item.value),
                                // barCategoryGap: 55,
                                barWidth: 20,
                                itemStyle: {
                                    borderRadius: 5,
                                },
                            },
                        ],
                    };
                    myChart.setOption(option);
                });
            },
            initChart3() {
                let myChart = echarts.init(document.getElementById("chart3"));
                $api("ldst_xsqstjc_scjgDetail", { type: 3 }).then((res) => {
                    let data = res;
                    let color1 = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: "#00C0FF",
                        },
                        {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                        },
                    ]);
                    let color2 = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: "#2DF09F",
                        },
                        {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                        },
                    ]);
                    let option = {
                        color: [color1, color2],
                        tooltip: {
                            trigger: "axis",
                            borderWidth: 0,
                            axisPointer: {
                                // 坐标轴指示器，坐标轴触发有效
                                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                            },
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "28",
                            },
                        },
                        legend: {
                            data: ["监管企业数", "特种设备数"],
                            textStyle: {
                                color: "#fff",
                                fontSize: 20,
                            },
                            itemGap: 30,
                        },
                        grid: {
                            top: 25,
                            left: 0,
                            right: 0,
                            bottom: 10,
                            containLabel: true,
                        },
                        itemStyle: {
                            borderRadius: 5,
                        },
                        xAxis: [
                            {
                                type: "category",
                                data: data.map((item) => item.name),
                                axisLabel: {
                                    color: "#fff",
                                    fontSize: 20,
                                },
                                axisPointer: {
                                    type: "shadow",
                                },
                            },
                        ],
                        yAxis: {
                            type: "value",

                            axisLabel: {
                                color: "#fff",
                                fontSize: 20,
                                formatter: "{value}个",
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.2)",
                                },
                            },
                        },
                        series: [
                            {
                                name: "监管企业数",
                                type: "bar",
                                data: data.map((item) => item.value1),
                                barGap: "20%",
                                // barCategoryGap: 130
                                barWidth: 25,
                            },
                            {
                                name: "特种设备数",
                                type: "bar",
                                data: data.map((item) => item.value2),
                                barWidth: 25,
                            },
                        ],
                    };
                    myChart.setOption(option);
                });
            },
            initChart4() {
                let myChart = echarts.init(document.getElementById("chart4"));
                $api("ldst_xsqstjc_scjgDetail", { type: 4 }).then((res) => {
                    let data = res;
                    let color1 = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: "#00C0FF",
                        },
                        {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                        },
                    ]);
                    let color2 = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: "#2DF09F",
                        },
                        {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                        },
                    ]);
                    let option = {
                        color: [color1, color2],
                        tooltip: {
                            trigger: "axis",
                            borderWidth: 0,
                            axisPointer: {
                                // 坐标轴指示器，坐标轴触发有效
                                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                            },
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "28",
                            },
                        },
                        legend: {
                            data: ["严重违法企业", "黑名单企业"],
                            textStyle: {
                                color: "#fff",
                                fontSize: 20,
                            },
                            itemGap: 30,
                        },
                        grid: {
                            top: 25,
                            left: 0,
                            right: 0,
                            bottom: 10,
                            containLabel: true,
                        },
                        itemStyle: {
                            borderRadius: 5,
                        },
                        xAxis: [
                            {
                                type: "category",
                                data: data.map((item) => item.name),
                                axisLabel: {
                                    color: "#fff",
                                    fontSize: 20,
                                },
                                axisPointer: {
                                    type: "shadow",
                                },
                            },
                        ],
                        yAxis: {
                            type: "value",

                            axisLabel: {
                                color: "#fff",
                                fontSize: 20,
                                formatter: "{value}户",
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.2)",
                                },
                            },
                        },
                        series: [
                            {
                                name: "严重违法企业",
                                type: "bar",
                                data: data.map((item) => item.value1),
                                barGap: "20%",
                                // barCategoryGap: 45,
                                barWidth: 18,
                            },
                            {
                                name: "黑名单企业",
                                type: "bar",
                                data: data.map((item) => item.value2),
                                barWidth: 18,
                            },
                        ],
                    };
                    myChart.setOption(option);
                });
            },
            initChart5() {
                let myChart = echarts.init(document.getElementById("chart5"));
                $api("ldst_xsqstjc_scjgDetail", { type: 5 }).then((res) => {
                    let data = res;
                    let color1 = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: "#00C0FF",
                        },
                        {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                        },
                    ]);
                    let color2 = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: "#2DF09F",
                        },
                        {
                            offset: 1,
                            color: "rgba(0,192,255,0)",
                        },
                    ]);
                    let option = {
                        color: [color1, color2],
                        tooltip: {
                            trigger: "axis",
                            borderWidth: 0,
                            axisPointer: {
                                // 坐标轴指示器，坐标轴触发有效
                                type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
                            },
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "28",
                            },
                        },
                        legend: {
                            data: ["事项数", "办结数"],
                            textStyle: {
                                color: "#fff",
                                fontSize: 20,
                            },
                            itemGap: 30,
                        },
                        grid: {
                            top: 25,
                            left: 0,
                            right: 0,
                            bottom: 10,
                            containLabel: true,
                        },
                        itemStyle: {
                            borderRadius: 5,
                        },
                        xAxis: [
                            {
                                type: "category",
                                data: data.map((item) => item.name),
                                axisLabel: {
                                    color: "#fff",
                                    fontSize: 20,
                                },
                                axisPointer: {
                                    type: "shadow",
                                },
                            },
                        ],
                        yAxis: {
                            type: "value",

                            axisLabel: {
                                color: "#fff",
                                fontSize: 20,
                            },
                            splitLine: {
                                lineStyle: {
                                    color: "rgb(119,179,241,.2)",
                                },
                            },
                        },
                        series: [
                            {
                                name: "事项数",
                                type: "bar",
                                data: data.map((item) => item.value1),
                                barGap: "20%",
                                // barCategoryGap: 45,
                                barWidth: 18,
                            },
                            {
                                name: "办结数",
                                type: "bar",
                                data: data.map((item) => item.value2),
                                barWidth: 18,
                            },
                        ],
                    };
                    myChart.setOption(option);
                });
            },
        },
    });
</script>
