<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>人才建设</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css">
    <link rel="stylesheet" href="/static/citybrain/ggfw/css/rcjs.css">
    <link rel="stylesheet" href="/static/css/sigma.css" />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/datav.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script> <!-- 轮播toolTip -->
</head>

<style>
    .line2-middle {
        position: relative;
    }

    .btn-1 {
        position: absolute;
        top: 156px;
        right: 148px;
        z-index: 1000;
        width: 117px;
        height: 30px;
        cursor: pointer;
    }

    .btn-2 {
        position: absolute;
        top: 190px;
        right: 148px;
        z-index: 1000;
        width: 117px;
        height: 30px;
        cursor: pointer;
    }

    .btn-3 {
        position: absolute;
        top: 230px;
        right: 148px;
        z-index: 1000;
        width: 117px;
        height: 30px;
        cursor: pointer;
    }

    /* 下拉框 */
    .el-select {
        width: 250px;
        position: absolute;
        margin-top: 10px;
        margin-left: 30px;
    }

    .el-input__inner {
        height: 50px !important;
        width: 250px !important;
        background-color: #00487f;
        color: #fff;
        font-size: 28px;
    }

    .el-select-dropdown {
        border: 1px solid #2578a6;
        background-color: #032f46d3;
    }

    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
        background-color: #00487f;
    }

    .el-select-dropdown__item {
        color: #fff;
        background-color: #00487f;
        font-size: 28px;
        height: 50px;
        line-height: 50px;
    }

    .el-select-dropdown__list {
        background-color: #00487f;
    }

    .el-select .el-input .el-select__caret {
        position: relative;
        left: 0px;
        font-size: 28px;
        color: #fff;
    }

    .el-button {
        background: linear-gradient(to bottom,
                rgba(0, 89, 147, 0.9),
                rgba(0, 32, 52, 0.9));
        color: #ffffff;
        width: 216px;
        height: 47px;
        border: #359cf8 1xp solid;
        font-size: 30px;
        position: absolute;
        margin-top: 10px;
        margin-left: 1218px;
    }

    .container {
        position: relative;
    }

    .xxxx-btn {
        position: absolute;
        margin-left: 13px;
        width: 97px;
        height: 113px;
        white-space: normal;
        padding: 0px 5px;
    }

    #button {
        position: absolute;
        top: 3px;
        left: 459px;
    }
</style>

<body>
    <div id="app" class="container" v-cloak>
        <el-button class="xxxx-btn" @click="xxxxDialog">详细信息</el-button>
        <div class="rcjs-top">
            <div class="rcjs-top-item" v-for="(item,index) in rcjsList" :key="index">
                <img src="/static/citybrain/ggfw/img/list.png" alt="">
                <span class="s-c-white s-font-30">{{item.name}}</span>
                <span class="s-c-blue-gradient s-font-35 s-w7">{{item.value}}</span>
                <span class="s-c-blue-gradient s-font-30">{{item.unit}}</span>
            </div>
        </div>
        <div class="rcjs-line2">
            <div class="line2-left">
                <nav>
                    <s-header-title-3 title="人才分布数量" />
                </nav>
                <div id="chart01" style="width:100%;height:300px;"></div>
            </div>
            <div class="line2-middle">
                <div class="btn-1" @click="peopleLevelDialog('高级职称')"></div>
                <div class="btn-2" @click="peopleLevelDialog('中级职称')"></div>
                <div class="btn-3" @click="peopleLevelDialog('初级职称')"></div>
                <nav>
                    <s-header-title-3 title="职称人员分布" />
                </nav>
                <div id="chart02" style="width:100%;height:300px;"></div>
            </div>
            <div class="line2-right">
                <nav>
                    <s-header-title-3 title="职称人员明细" />
                </nav>
                <div style="width:100%;height:300px;">
                    <div class="table table1">
                        <div class="th">
                            <div class="th_td" v-for="(item,index) in theadList" :key="index">
                                {{item}}
                            </div>
                        </div>
                        <div class="tbody" id="tbody" @mouseover="mouseenterEvent()" @mouseleave="mouseleaveEvent()">
                            <div class="tr" v-for="(item ,i) in tbodyList" :key="i">
                                <div class="tr_td" style="flex: 0.2">{{item.xh}}</div>
                                <div class="tr_td" style="flex: 0.2">{{item.xm}}</div>
                                <div class="tr_td" style="flex: 0.2">{{item.zsmc}}</div>
                                <div class="tr_td" style="flex: 0.2;">{{item.ssly}}</div>
                                <div class="tr_td" style="flex: 0.2">{{item.zclb}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="rcjs-line3">
            <div class="line3-left" style="position: relative;">
                <nav>
                    <el-select v-model="selectedValue" placeholder="初级职称" @change="changeSelected">
                        <el-option label="高级职称" value="1" key="1"></el-option>
                        <el-option label="中级职称" value="2" key="2"></el-option>
                        <el-option label="初级职称" value="3" key="3"></el-option>
                        <el-option label="参与人数" value="4" key="4"></el-option>
                        <el-option label="通过人数" value="5" key="5"></el-option>
                    </el-select>

                    <div class="gjhfz-text" style="top: 73px;">评定机构
                        <el-tag style="position: relative;top:-1px">357个</el-tag>
                    </div>
                    <s-header-title-2 htype="1" title="职称评定统计展示">
                        </s-header-title>
                </nav>
                <div id="chart03" style="width:100%;height:300px;"></div>
            </div>
            <div class="line3-right">
                <nav>
                    <s-header-title-2 htype="0" :click-flag="true" @click="openDiaog" title="专家库">
                        </s-header-title>
                </nav>
                <div style="width:100%;height:300px;">
                    <div class="zjk-item" v-for="(item,index) in zjkList" :key="index">
                        <p class="s-c-white s-font-30">{{item.name}}</p>
                        <div>
                            <dv-border-box-8 style="padding-top: 10px;">
                                <span class="s-c-blue-gradient s-font-40">{{item.value}}</span>
                                <span class="s-c-blue-gradient s-font-30">{{item.unit}}</span>
                            </dv-border-box-8>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="rcjs-line4" style="position: relative;">
            <nav>
                <s-header-title-2 htype="1" title="专业技术人员">
                    </s-header-title>
            </nav>
            <el-button id="button" @click="changHistogram">地图显示</el-button>
            <!-- <div class="gjhfz-text">基本医疗保险基金收入
                <el-tag style="position: relative;top:-1px">3.57亿元</el-tag>
            </div> -->
            <div id="chart04" style="width:100%;height:300px;"></div>
        </div>
        <div class="rcjs-line5">
            <div class="line5-left">
                <nav>
                    <s-header-title-2 htype="0" title="科技创新">
                        </s-header-title>
                </nav>
                <div style="width:100%;display:flex;">
                    <div class="kjcx-item" v-for="(item,index) in kjcxList" :key="index">
                        <span class="s-c-white s-font-30">{{item.name}}</span>
                        <div style="display: inline-block;">
                            <dv-border-box-8 style="padding-top: 10px;">
                                <span class="s-c-blue-gradient s-font-40">{{item.value}}</span>
                                <span class="s-c-blue-gradient s-font-30">{{item.unit}}</span>
                            </dv-border-box-8>
                        </div>
                    </div>
                </div>
                <div id="chart05" style="width:100%;height:320px;"></div>
            </div>
            <div class="line5-right">
                <nav>
                    <s-header-title-2 htype="0" title="综合趋势">
                        </s-header-title>
                </nav>
                <div style="width:100%;display:flex;">
                    <div class="kjcx-item" v-for="(item,index) in zhqsList" :key="index" style="width:100%;">
                        <span class="s-c-white s-font-30">{{item.name}}</span>
                        <div style="display: inline-block;width:20%;">
                            <dv-border-box-8 style="padding-top: 10px;">
                                <span class="s-c-blue-gradient s-font-40">{{item.value}}</span>
                                <span class="s-c-blue-gradient s-font-30">{{item.unit}}</span>
                            </dv-border-box-8>
                        </div>
                    </div>
                </div>
                <div id="chart06" style="width:100%;height:320px;"></div>
            </div>
        </div>
    </div>
</body>
<script type="module">

    new Vue({
        el: '#app',
        data() {
            return {
                rcjsList: [],
                time: null,
                dom: null,
                theadList: ['序号', '姓名', '证书名称', '所属领域', '职称类别'],
                tbodyList: [],
                zjkList: [],
                kjcxList: [],
                zhqsList: [],
                selectedValue: "3",
                boolHistogram: false,
                cjzcList: [],
                gjzcList: [
                    {
                        name: "婺城区",
                        value1: "3",
                        value2: "2",
                        value3: "2"
                    },
                    {
                        name: "金义新区",
                        value1: "8",
                        value2: "9",
                        value3: "4"
                    },
                    {
                        name: "东阳市",
                        value1: "2",
                        value2: "7",
                        value3: "18"
                    },
                    {
                        name: "义乌市",
                        value1: "6",
                        value2: "2",
                        value3: "4"
                    },
                    {
                        name: "永康市",
                        value1: "5",
                        value2: "6",
                        value3: "3"
                    },
                    {
                        name: "兰溪市",
                        value1: "4",
                        value2: "3",
                        value3: "5"
                    },
                    {
                        name: "浦江县",
                        value1: "3",
                        value2: "3",
                        value3: "7"
                    },
                    {
                        name: "武义县",
                        value1: "9",
                        value2: "8",
                        value3: "9"
                    },
                    {
                        name: "磐安县",
                        value1: "2",
                        value2: "7",
                        value3: "8"
                    },
                    {
                        name: "开发区",
                        value1: "4",
                        value2: "5",
                        value3: "8"
                    },
                ],
                zjzcList: [
                    {
                        name: "婺城区",
                        value1: "23",
                        value2: "21",
                        value3: "12"
                    },
                    {
                        name: "金义新区",
                        value1: "22",
                        value2: "11",
                        value3: "34"
                    },
                    {
                        name: "东阳市",
                        value1: "12",
                        value2: "12",
                        value3: "18"
                    },
                    {
                        name: "义乌市",
                        value1: "23",
                        value2: "21",
                        value3: "34"
                    },
                    {
                        name: "永康市",
                        value1: "56",
                        value2: "21",
                        value3: "12"
                    },
                    {
                        name: "兰溪市",
                        value1: "23",
                        value2: "16",
                        value3: "12"
                    },
                    {
                        name: "浦江县",
                        value1: "33",
                        value2: "21",
                        value3: "12"
                    },
                    {
                        name: "武义县",
                        value1: "17",
                        value2: "19",
                        value3: "12"
                    },
                    {
                        name: "磐安县",
                        value1: "18",
                        value2: "21",
                        value3: "16"
                    },
                    {
                        name: "开发区",
                        value1: "15",
                        value2: "21",
                        value3: "12"
                    },
                ],
            }
        },
        mounted() {
            this.init()
            this.scroll()
        },
        methods: {
            init() {
                $get("/ggfw/rsfw/rcjs01").then((res) => {
                    this.rcjsList = res;
                });
                $get("/ggfw/rsfw/rcjs02").then((res) => {
                    this.getChart01('chart01', res, '')
                });
                $get("/ggfw/rsfw/rcjs03").then((res) => {
                    this.getChart02('chart02', res)
                });
                $get("/ggfw/rsfw/rcjs04").then((res) => {
                    this.tbodyList = res
                });
                $get("/ggfw/rsfw/rcjs05").then((res) => {
                    this.cjzcList = res;
                    this.getChart03('chart03', res[0]);
                });
                $get("/ggfw/rsfw/rcjs06").then((res) => {
                    this.zjkList = res.slice(0, 2)
                    this.kjcxList = res.slice(2, 4)
                    this.zhqsList = res.slice(4, 5)
                });
                $get("/ggfw/rsfw/rcjs07").then((res) => {
                    this.getChart01('chart04', res, '人才分布')
                });
                $get("/ggfw/rsfw/rcjs08").then((res) => {
                    this.getChart04('chart05', res)
                });
                $get("/ggfw/rsfw/rcjs09").then((res) => {
                    this.getChart05('chart06', res)
                });
            },
            openDiaog() {
                let diaog = {
                    type: 'openIframe',
                    name: 'rcjs-dialog',
                    src: baseURL.url + '/static/citybrain/ggfw/commont/rcjs-dialog.html',
                    left: "calc(50% - 515px)",
                    top: "18%",
                    width: "1035px",
                    height: "1570px",
                    zIndex: "10",
                    argument: {
                        status: ""
                    }
                }
                top.window.parent.postMessage(JSON.stringify(diaog), '*')
            },

            /**
             * @description: 职称等级弹窗
             * @param {*} item 高级职称/中级职称/初级职称
             * @return {*}
             */
            peopleLevelDialog(item) {
                this.$nextTick(() => {
                    let diaog = {
                        type: 'openIframe',
                        name: 'rcjs-people-dialog',
                        src: baseURL.url + '/static/citybrain/ggfw/commont/rcjs-people-dialog.html',
                        left: "calc(50% - 515px)",
                        top: "18%",
                        width: "1035px",
                        height: "970px",
                        zIndex: "10",
                        argument: {
                            status: item
                        }
                    }
                    top.window.parent.postMessage(JSON.stringify(diaog), '*')
                })
            },

            /**
             * @description: 详细信息弹窗
             * @return {*}
             */
            xxxxDialog() {
                let diaog = {
                    type: 'openIframe',
                    name: 'rcjs-xxxx-dialog',
                    src: baseURL.url + '/static/citybrain/ggfw/commont/rcjs-xxxx-dialog.html',
                    left: "calc(50% - 815px)",
                    top: "18%",
                    width: "1635px",
                    height: "900px",
                    zIndex: "10",
                    argument: {
                        status: "高级职称"
                    }
                }
                top.window.parent.postMessage(JSON.stringify(diaog), '*')
            },

            /**
             * @description: 职称统计展示下拉框
             * @param {*} item
             * @return {*}
             */
            changeSelected(item) {
                this.selectedValue = item;
                switch (item) {
                    case "3":
                        $get("/ggfw/rsfw/rcjs05").then((res) => {
                            this.getChart03('chart03', res[0])
                            this.cjzcList = res[0];
                        });
                        break;
                    case "2":
                        this.getChart03('chart03', this.zjzcList)
                        break;
                    case "1":
                        this.getChart03('chart03', this.gjzcList)
                        break;
                    case "4":
                        $get("/ggfw/rsfw/rcjs05").then((res) => {
                            this.getChart03('chart03', res[1])
                            this.cjzcList = res[1];
                            console.log('%c  this.cjzcList ', 'font-size:13px; background:pink; color:#bf2c9f;', this.cjzcList);
                        });
                        break;
                    case "5":
                        $get("/ggfw/rsfw/rcjs05").then((res) => {
                            this.getChart03('chart03', res[2])
                            this.cjzcList = res[2];
                        });
                        break;
                    default:
                        break;
                }
            },

            /**
             * @description: 显示地图柱状图/移除柱状图
             * @return {*}
             */
            changHistogram() {
                if (this.boolHistogram) {
                    this.boolHistogram = false;
                    this.rmHistogram();
                } else {
                    this.boolHistogram = true;
                    this.Histogram();
                }
            },

            //区划地图柱状图
            Histogram() {
                var data = [];
                var tem = [{
                    "name": "婺城区",
                    "value1": "235",
                    "value2": "342",
                    "value3": "123"
                },
                {
                    "name": "金义新区",
                    "value1": "135",
                    "value2": "423",
                    "value3": "341"
                },
                {
                    "name": "东阳市",
                    "value1": "235",
                    "value2": "128",
                    "value3": "105"
                },
                {
                    "name": "义乌市",
                    "value1": "175",
                    "value2": "156",
                    "value3": "133"
                },
                {
                    "name": "永康市",
                    "value1": "234",
                    "value2": "356",
                    "value3": "162"
                },
                {
                    "name": "兰溪市",
                    "value1": "145",
                    "value2": "143",
                    "value3": "137"
                },
                {
                    "name": "浦江县",
                    "value1": "235",
                    "value2": "146",
                    "value3": "108"
                },
                {
                    "name": "武义县",
                    "value1": "145",
                    "value2": "198",
                    "value3": "156"
                },
                {
                    "name": "磐安县",
                    "value1": "145",
                    "value2": "175",
                    "value3": "126"
                },
                {
                    "name": "开发区",
                    "value1": "145",
                    "value2": "169",
                    "value3": "121"
                }];
                tem.forEach((element) => {
                    data.push(
                        {
                            name: element.name,
                            num: Number(element.value1) + Number(element.value2) + Number(element.value3),
                            unit: '个'
                        }
                    )
                });
                // switch (this.selectedValue) {
                //     case "3":
                //         this.cjzcList.forEach((element) => {
                //             data.push(
                //                 {
                //                     name: element.name,
                //                     num: Number(element.value1) + Number(element.value2) + Number(element.value3),
                //                     unit: '个'
                //                 }
                //             )
                //         });
                //         break;
                //     case "2":
                //         this.zjzcList.forEach((element) => {
                //             data.push(
                //                 {
                //                     name: element.name,
                //                     num: Number(element.value1) + Number(element.value2) + Number(element.value3),
                //                     unit: '个'
                //                 }
                //             )
                //         });
                //         break;
                //     case "1":
                //         this.gjzcList.forEach((element) => {
                //             data.push(
                //                 {
                //                     name: element.name,
                //                     num: Number(element.value1) + Number(element.value2) + Number(element.value3),
                //                     unit: '个'
                //                 }
                //             )
                //         });
                //         break;

                //     default:
                //         break;
                // }
                data = data.slice(
                    0,
                    data.length - 1
                );
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "rm3Dtext",
                    })
                );
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "Histogram", //功能名称
                        HistogramData: data
                    })
                );
            },

            //清除柱状体
            rmHistogram() {
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        "funcName": "rmHistogram", //清除柱状体
                    })
                );
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "rm3Dtext",
                    })
                );
                top.document.getElementById("map").contentWindow.Work.funChange(
                    JSON.stringify({
                        funcName: "3Dtext", //3D文字功能
                        textData: [
                            // pos文字的位置  //text 展示的文字
                            { pos: [119.94315399169922, 29.5630503845215, 11000], text: "浦江县" },
                            { pos: [119.46214447021484, 29.31345558166504, 11000], text: "兰溪市" },
                            { pos: [119.5569204711914, 29.00677101135254, 11000], text: "婺城区" },
                            { pos: [119.8483056640625, 29.188559951782227, 11000], text: "金义新区" },
                            { pos: [120.08206787109375, 29.322123641967773, 11000], text: "义乌市" },
                            { pos: [119.7269204711914, 28.79677101135254, 11000], text: "武义县" },
                            { pos: [120.1469204711914, 28.97677101135254, 11000], text: "永康市" },
                            { pos: [120.4169204711914, 29.24677101135254, 11000], text: "东阳市" },
                            { pos: [120.6299204711914, 29.06677101135254, 11000], text: "磐安县" },
                        ],
                        textSize: 40,
                        id: "text1",
                        // zoomShow: true,
                        color: [255, 255, 255, 1],
                    })
                );
            },

            scroll() {
                this.dom = document.getElementById('tbody')
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },

            mouseenterEvent() {
                clearInterval(this.time)
            },

            mouseleaveEvent() {
                this.time = setInterval(() => {
                    this.dom.scrollTop += 2
                    if (this.dom.scrollTop >= this.dom.scrollHeight - this.dom.offsetHeight) {
                        this.dom.scrollTop = 0
                    }
                }, 20)
            },
            getChart01(id, data, name) {
                echarts.init(document.getElementById(id)).dispose();
                let myEc = echarts.init(document.getElementById(id));
                var option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            // 坐标轴指示器，坐标轴触发有效
                            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
                        },
                        borderWidth: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.6)',
                        textStyle: {
                            color: 'white',
                            fontSize: '28',
                        },
                    },
                    legend: {
                        orient: 'horizontal',
                        // itemWidth: 18,
                        // itemHeight: 18,
                        top: '0%',
                        // icon: 'rect',
                        itemGap: 25,
                        textStyle: {
                            color: '#D6E7F9',
                            fontSize: 28,
                        },
                    },
                    grid: {
                        left: '2%',
                        right: '5%',
                        bottom: '10%',
                        top: '20%',
                        containLabel: true,
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: data.map((item) => { return item.name }),
                            offset: 20,
                            axisLine: {
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.3,
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                // rotate: -30,
                                textStyle: {
                                    fontSize: 28,
                                    color: 'white',
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: "单位:人",
                            type: 'value',
                            nameTextStyle: {
                                fontSize: 28,
                                color: '#D6E7F9',
                                padding: [5, 0]
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 28,
                                    color: '#D6E7F9',
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: name,
                            type: 'bar',
                            barWidth: 35,
                            yAxisIndex: 0,
                            smooth: true, //加这个
                            center: ['0%', '45%'],
                            radius: ['0%', '45%'],
                            itemStyle: {
                                normal: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {
                                            offset: 0,
                                            color: '#00C0FF',
                                        },
                                        {
                                            offset: 0.2,
                                            color: '#00C0FF',
                                        },
                                        {
                                            offset: 1,
                                            color: '#004F69',
                                        },
                                    ]),
                                    barBorderRadius: 4,
                                },
                            },
                            data: data.map((item) => { return item.value }),
                        },
                    ]
                }
                myEc.setOption(option)
            },
            getChart02(id, data) {
                echarts.init(document.getElementById(id)).dispose();
                let myEc = echarts.init(document.getElementById(id));
                let imgUrl = '/static/citybrain/djtl/img/djtl-left/echarts-bg.png'
                const option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{b}: <br/>{d}%',
                        borderWidth: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.6)',
                        textStyle: {
                            color: 'white',
                            fontSize: '30',
                        },
                    },
                    legend: {
                        orient: 'vertical',
                        itemWidth: 18,
                        itemHeight: 18,
                        left: '50%',
                        top: 'center',
                        itemGap: 10,
                        textStyle: {
                            color: '#D6E7F9',
                            fontSize: 28,
                            padding: [0, 0, 0, 10]
                        },
                    },
                    graphic: [
                        {
                            z: 1,
                            type: "image",
                            id: "logo1",
                            left: "6.8%",
                            top: "23%",
                            z: -10,
                            bounding: "raw",
                            rotation: 0, //旋转
                            origin: [0, 0], //中心点
                            scale: [0.5, 0.5], //缩放
                            style: {
                                image: imgUrl,
                                opacity: 1,
                            },
                        }
                    ],
                    series: [
                        {
                            name: "1",
                            type: 'pie',
                            radius: ['50%', '70%'],
                            center: ['20%', '50%'],
                            itemStyle: {
                                normal: {
                                    borderColor: "#0A1934",
                                    // borderWidth: 10
                                },
                            },
                            label: {
                                show: false,
                            },
                            data: data
                        }
                    ],
                }
                myEc.setOption(option)
            },
            getChart03(id, chartData) {
                const myCharts = echarts.init(document.getElementById(id))
                var legend = ["政策因素", "经济因素", "规划因素"];
                var colorList = ['#5087EC', '#68BBC4', '#58A55C'];
                var data = [];
                let x = chartData.map((item) => {
                    return item.name;
                })
                let y1 = chartData.map((item) => {
                    return item.value1;
                })
                let y2 = chartData.map((item) => {
                    return item.value2;
                })
                let y3 = chartData.map((item) => {
                    return item.value3;
                })
                data.push(y1, y2, y3);
                let option = {
                    tooltip: {
                        trigger: "item",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                    },
                    // color: colors,
                    legend: {
                        x: "center",
                        y: "15",
                        itemWidth: 20,
                        itemHeight: 20,
                        textStyle: {
                            color: "#fff",
                            fontSize: 24,
                        },
                        data: legend,
                    },
                    grid: {
                        left: "3%",
                        right: "4%",
                        bottom: "5%",
                        top: "20%",
                        containLabel: true,
                    },
                    xAxis: {
                        type: "category",
                        axisLine: {
                            lineStyle: {
                                color: '#77b3f1',
                                opacity: 0.3,
                            },
                        },
                        axisTick: {
                            show: false,
                        },
                        axisLabel: {
                            interval: 0,
                            // rotate: -30,
                            textStyle: {
                                fontSize: 28,
                                color: 'white',
                            },
                        },
                        data: x,
                    },
                    yAxis: [
                        {
                            name: "单位:人",
                            type: 'value',
                            nameTextStyle: {
                                fontSize: 28,
                                color: '#D6E7F9',
                                padding: [5, 0]
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 28,
                                    color: '#D6E7F9',
                                },
                            },
                        },
                    ],
                    series: [],
                };
                for (var i = 0; i < legend.length; i++) {
                    option.series.push({
                        name: legend[i],
                        type: "bar",
                        stack: "总量",
                        barWidth: 40,
                        itemStyle: {
                            normal: {
                                color: colorList[i]
                            },
                        },
                        label: {
                            show: false,
                            position: "inside",
                            textStyle: {
                                color: "#fff",
                                fontSize: 24
                            },
                        },
                        data: data[i],
                    });
                }
                myCharts.setOption(option);
                myCharts.getZr().on('mousemove', param => {
                    myCharts.getZr().setCursorStyle('default')
                })
            },
            getChart04(id, chartData) {
                let echarts0 = echarts.init(document.getElementById(id));
                var xData = chartData.map((item) => {
                    return item.year;
                }),
                    yData1 = chartData.map((item) => {
                        return item.value;
                    }),
                    yData2 = chartData.map((item) => {
                        return item.value1;
                    }),
                    borderData = [],
                    legend = ["企业数量", "企业年营业收入"],
                    colorArr = [
                        {
                            start: "rgba(71, 173, 245,",
                            end: "rgba(18, 58, 86,0.5)",
                        },
                        {
                            start: "rgba(218, 201, 126,",
                            end: "rgba(18, 58, 86,0.5)",
                        },
                    ];
                var normalColor = "rgba(255,255,255,0.5)";
                let seriesData = [];
                var borderHeight = 4;
                xData.forEach((element) => {
                    borderData.push(borderHeight);
                });
                [yData1, yData2].forEach((item, index) => {
                    var obj1 = {};
                    var obj2 = {};
                    if (index < 3) {
                        obj1 = {
                            name: legend[index],
                            type: "bar",
                            stack: legend[index],
                            data: item,
                            barWidth: "15%",
                            itemStyle: {
                                normal: {
                                    color: {
                                        type: "linear",
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [
                                            {
                                                offset: 0,
                                                color: colorArr[index].start + "0.7)",
                                            },
                                            {
                                                offset: 0.5,
                                                color: colorArr[index].start + "0.3)",
                                            },
                                            {
                                                offset: 1,
                                                color: colorArr[index].end,
                                            },
                                        ],
                                        globalCoord: false,
                                    },
                                },
                            },
                        };
                        obj2 = {
                            name: "",
                            type: "bar",
                            stack: legend[index],
                            yAxisIndex: 1,
                            itemStyle: {
                                normal: {
                                    color: colorArr[index].start + "1)",
                                },
                            },
                            data: borderData,
                        };
                        seriesData.push(obj1);
                        seriesData.push(obj2);
                    }
                });
                let option = {
                    grid: {
                        top: "25%",
                        right: 0,
                        left: 0,
                        bottom: 0,
                        containLabel: true,
                    },
                    legend: {
                        show: true,
                        itemWidth: 20,
                        icon: "square",
                        itemGap: 100,
                        itemHeight: 20,
                        top: "5%",
                        textStyle: {
                            color: "#fff",
                            fontSize: 24,
                        },
                        data: legend,
                    },
                    tooltip: {
                        trigger: "axis",
                        textStyle: {
                            fontSize: 30,
                        },
                        formatter: function (params) {
                            var str = "";
                            for (var i = 0; i < params.length; i++) {
                                if (params[i].seriesName !== "") {
                                    str += params[i].name + ":" + params[i].seriesName + params[i].value + "<br/>";
                                }
                            }
                            return str;
                        },
                    },
                    xAxis: [
                        {
                            type: "category",
                            data: xData,
                            axisLine: {
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.3,
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            axisLabel: {
                                interval: 0,
                                textStyle: {
                                    fontSize: 28,
                                    color: 'white',
                                },
                            },
                        },
                    ],
                    yAxis: [
                        {
                            name: "单位: 个",
                            type: 'value',
                            nameTextStyle: {
                                fontSize: 28,
                                color: '#D6E7F9',
                                padding: [0, 0]
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 28,
                                    color: '#D6E7F9',
                                },
                            },
                        },
                        {
                            name: "单位: 万元",
                            type: 'value',
                            nameTextStyle: {
                                fontSize: 28,
                                color: '#D6E7F9',
                                padding: [0, 0]
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: '#77b3f1',
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 28,
                                    color: '#D6E7F9',
                                },
                            },
                        },
                    ],
                    series: seriesData,
                };
                echarts0.setOption(option);
            },
            getChart05(id, chartData) {
                const myCharts = echarts.init(document.getElementById(id))
                let x = chartData.map((item) => {
                    return item.name;
                })
                let y1 = chartData.map((item) => {
                    return item.value;
                })
                let option = {
                    grid: {
                        left: "5%",
                        right: "10%",
                        top: "10%",
                        bottom: "10%",
                        containLabel: true,
                    },
                    tooltip: {
                        trigger: "axis",
                        borderWidth: 0,
                        backgroundColor: "rgba(0, 0, 0, 0.6)",
                        textStyle: {
                            color: "white",
                            fontSize: "28",
                        },
                    },
                    legend: {
                        show: true,
                        x: "center",
                        y: "10",
                        itemWidth: 20,
                        itemHeight: 20,
                        textStyle: {
                            color: "#fff",
                            fontSize: "24",
                        },
                    },
                    xAxis: [
                        {
                            type: "category",
                            offset: 20,
                            axisLine: {
                                //坐标轴轴线相关设置。数学上的x轴
                                show: true,
                                lineStyle: {
                                    color: "rgba(108, 166, 219, 0.3)",
                                },
                            },
                            axisLabel: {
                                //坐标轴刻度标签的相关设置
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: "#192a44",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            data: x,
                        },
                    ],
                    yAxis: [
                        {
                            name: "",
                            nameTextStyle: {
                                fontSize: 28,
                                color: "#D6E7F9",
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.1,
                                    width: 2,
                                },
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: "#77b3f1",
                                    opacity: 0.5,
                                    width: 2,
                                },
                            },
                            axisLabel: {
                                textStyle: {
                                    fontSize: 28,
                                    color: "#D6E7F9",
                                },
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: "#233653",
                                },
                            },
                        },
                    ],
                    series: [
                        {
                            name: "人才建设指数",
                            type: "line",
                            smooth: true,
                            symbolSize: 10,
                            itemStyle: {
                                normal: {
                                    color: "#5087EC",
                                    lineStyle: {
                                        color: "#5087EC",
                                        width: 4,
                                    },
                                },
                            },
                            data: y1,
                        }
                    ],
                };
                myCharts.setOption(option);
                myCharts.getZr().on('mousemove', param => {
                    myCharts.getZr().setCursorStyle('default')
                })
            },
        }
    })


</script>

</html>