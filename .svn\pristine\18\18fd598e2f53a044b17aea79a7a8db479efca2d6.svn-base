<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>数字社会指标分析左侧面板</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link
      rel="stylesheet"
      href="/static/citybrain/csdn/elementui/css/elementui.css"
    />
    <link
      rel="stylesheet"
      href="/static/citybrain3840/szhgg/css/szshzbfx-right.css"
    />
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/hjbh/js/echarts-liquidfill.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
    <!-- 轮播toolTip -->
  </head>

  <body>
    <div id="app" class="container" v-cloak>
      <nav>
        <s-header-title-2
          htype="1"
          title="数字社会多跨场景建设推进情况"
        ></s-header-title-2>
      </nav>
      <div class="btn">全省排名:第二名</div>
      <div class="szsh-con">
        <div id="szsh-chart1"></div>
        <div id="szsh-chart2"></div>
      </div>
      <el-select
        style="margin-left: 640px; margin-top: 60px"
        v-model="value"
        placeholder="月份"
        @change="change"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <div id="szsh-chart3"></div>
      <nav style="position: relative">
        <s-header-title-2 htype="1" title="民生服务分析"></s-header-title-2>
        <div
          class="btn"
          style="
            width: 100px;
            position: absolute;
            right: 200px;
            top: 5px;
            cursor: pointer;
          "
          @click="openTc()"
        >
          详情
        </div>
      </nav>
      <div class="tj-con">
        <li
          v-for="(item,index) in tjData"
          :key="index"
          @click="toMap3DText(index)"
          style="cursor: pointer"
        >
          <div class="title1">{{item.value}}{{item.unit}}</div>
          <div class="title2">{{item.name}}</div>
        </li>
      </div>
      <div id="szsh-chart4"></div>
    </div>
  </body>
  <script type="module">
    new Vue({
      el: '#app',
      data: {
        tjData: [],
        value: '3月',
        options: [
          {
            value: '1月',
            label: '1月',
          },
          {
            value: '2月',
            label: '2月',
          },
          {
            value: '3月',
            label: '3月',
          },
        ],
      },
      methods: {
        toMap3DText(index) {
          this.rm3DText()
          if (index == 0) {
            this.add3DText1()
          } else if (index == 1) {
            this.add3DText2()
          } else if (index == 2) {
            this.add3DText3()
          } else if (index == 3) {
            this.add3DText4()
          }
        },
        add3DText1() {
          top.mapUtil.loadTextLayer({
            layerid: 'text11',
            data: [
              // pos文字的位置  //text 展示的文字
              {
                pos: [119.87315399169922, 29.5030503845215, 11000],
                text: '205',
              },
              {
                pos: [119.44214447021484, 29.22345558166504, 11000],
                text: '201',
              },
              {
                pos: [119.5869204711914, 28.96677101135254, 11000],
                text: '365',
              },
              {
                pos: [119.8083056640625, 29.128559951782227, 11000],
                text: '125',
              },
              {
                pos: [120.05206787109375, 29.272123641967773, 11000],
                text: '12',
              },
              {
                pos: [119.7269204711914, 28.75677101135254, 11000],
                text: '135',
              },
              {
                pos: [120.1069204711914, 28.94677101135254, 11000],
                text: '15',
              },
              {
                pos: [120.4069204711914, 29.20677101135254, 11000],
                text: '191',
              },
              {
                pos: [120.5999204711914, 29.02677101135254, 11000],
                text: '20',
              },
            ],
            style: {
              size: 32,
              color: [242, 242, 242, 1],
            },
          })
        },
        add3DText2() {
          top.mapUtil.loadTextLayer({
            layerid: 'text11',
            data: [
              // pos文字的位置  //text 展示的文字
              {
                pos: [119.87315399169922, 29.5030503845215, 11000],
                text: '15%',
              },
              {
                pos: [119.44214447021484, 29.22345558166504, 11000],
                text: '16%',
              },
              {
                pos: [119.5869204711914, 28.96677101135254, 11000],
                text: '18%',
              },
              {
                pos: [119.8083056640625, 29.128559951782227, 11000],
                text: '20%',
              },
              {
                pos: [120.05206787109375, 29.272123641967773, 11000],
                text: '12%',
              },
              {
                pos: [119.7269204711914, 28.75677101135254, 11000],
                text: '13%',
              },
              {
                pos: [120.1069204711914, 28.94677101135254, 11000],
                text: '15%',
              },
              {
                pos: [120.4069204711914, 29.20677101135254, 11000],
                text: '19%',
              },
              {
                pos: [120.5999204711914, 29.02677101135254, 11000],
                text: '20%',
              },
            ],
            style: {
              size: 32,
              color: [242, 242, 242, 1],
            },
          })
        },
        add3DText3() {
          top.mapUtil.loadTextLayer({
            layerid: 'text11',
            data: [
              // pos文字的位置  //text 展示的文字
              {
                pos: [119.87315399169922, 29.5030503845215, 11000],
                text: '98',
              },
              {
                pos: [119.44214447021484, 29.22345558166504, 11000],
                text: '88',
              },
              {
                pos: [119.5869204711914, 28.96677101135254, 11000],
                text: '78',
              },
              {
                pos: [119.8083056640625, 29.128559951782227, 11000],
                text: '65',
              },
              {
                pos: [120.05206787109375, 29.272123641967773, 11000],
                text: '69',
              },
              {
                pos: [119.7269204711914, 28.75677101135254, 11000],
                text: '96',
              },
              {
                pos: [120.1069204711914, 28.94677101135254, 11000],
                text: '78',
              },
              {
                pos: [120.4069204711914, 29.20677101135254, 11000],
                text: '65',
              },
              {
                pos: [120.5999204711914, 29.02677101135254, 11000],
                text: '39',
              },
            ],
            style: {
              size: 32,
              color: [242, 242, 242, 1],
            },
          })
        },
        add3DText4() {
          top.mapUtil.loadTextLayer({
            layerid: 'text11',
            data: [
              // pos文字的位置  //text 展示的文字
              {
                pos: [119.87315399169922, 29.5030503845215, 11000],
                text: '25886',
              },
              {
                pos: [119.44214447021484, 29.22345558166504, 11000],
                text: '36955',
              },
              {
                pos: [119.5869204711914, 28.96677101135254, 11000],
                text: '258866',
              },
              {
                pos: [119.8083056640625, 29.128559951782227, 11000],
                text: '36954',
              },
              {
                pos: [120.05206787109375, 29.272123641967773, 11000],
                text: '698742',
              },
              {
                pos: [119.7269204711914, 28.75677101135254, 11000],
                text: '36987',
              },
              {
                pos: [120.1069204711914, 28.94677101135254, 11000],
                text: '36442',
              },
              {
                pos: [120.4069204711914, 29.20677101135254, 11000],
                text: '36452',
              },
              {
                pos: [120.5999204711914, 29.02677101135254, 11000],
                text: '81851',
              },
            ],
            style: {
              size: 32,
              color: [242, 242, 242, 1],
            },
          })
        },
        //清除3D文字方法
        rm3DText() {
          top.mapUtil.removeLayer('text11')
        },
        // static\citybrain3840\szhgg\pages\szsh-left-dialog.html
        openTc() {
          top.commonObj.funOpenIframe({
            width: '1900px',
            height: '970px',
            zIndex: '999',
            src: '/static/citybrain3840/szhgg/pages/szsh-left-dialog.html',
            left: '945px',
            top: '275px',
            name: 'szsh-left-dialog',
          })
        },
        init() {
          $api('ldst_szhgg_szsh', { type: 12 }).then((res) => {
            this.tjData = res
          })
          $api('ldst_szhgg_szsh', { type: 9 }).then((res) => {
            this.progresschartsShow(res)
          })
          $api('ldst_szhgg_szsh', { type: 10 }).then((res) => {
            this.PiechartsShow(res)
          })
          $api('ldst_szhgg_szsh', { type: 11 }).then((res) => {
            this.BarchartsShow(res)
          })
          $api('ldst_szhgg_szsh', { type: 13 }).then((res) => {
            this.BarChartShow1(res)
          })
        },
        change(item) {
          if (item === '2月') {
            $api('ldst_szhgg_szsh', { type: 11 }).then((res) => {
              this.BarchartsShow(res)
            })
            $get('/3840/szhgg/szsh/szsh11.json', { type: 11 }).then((res) => {
              this.BarchartsShow(res)
            })
          } else if (item === '3月') {
            $api('ldst_szhgg_szsh', { type: '11-1' }).then((res) => {
              this.BarchartsShow(res)
            })
          } else if (item === '4月') {
            $api('ldst_szhgg_szsh', { type: '11-2' }).then((res) => {
              this.BarchartsShow(res)
            })
          } else if (item === '5月') {
            $api('ldst_szhgg_szsh', { type: '11-3' }).then((res) => {
              this.BarchartsShow(res)
            })
          }
        },
        //绘制柱图
        BarchartsShow(data) {
          const myChartsState = echarts.init(
            document.getElementById('szsh-chart3')
          )
          var fontColor = '#30eee9'
          let x = data.map((item) => {
            return item.name
          })
          let y1 = data.map((item) => {
            return item.sheng
          })
          let y2 = data.map((item) => {
            return item.shi
          })
          let y3 = data.map((item) => {
            return item.zzl
          })
          let option = {
            tooltip: {
              trigger: 'axis',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
              axisPointer: {
                type: 'shadow',
              },
            },
            legend: {
              // align: "right",
              top: 0,
              textStyle: {
                color: '#fff',
                fontSize: 28,
              },
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '10%',
              containLabel: true,
            },
            xAxis: {
              data: x,
              axisLine: {
                show: true, //隐藏X轴轴线
                lineStyle: {
                  color: '#aaa',
                  width: 1,
                },
              },
              axisTick: {
                show: true, //隐藏X轴刻度
                alignWithLabel: true,
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#fff', //X轴文字颜色
                  fontSize: 28,
                },
                interval: 0,
                rotate: 30,
              },
            },
            yAxis: [
              {
                type: 'value',
                name: '',
                nameTextStyle: {
                  color: '#fff',
                  fontSize: 28,
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    width: 1,
                    color: '#3d5269',
                  },
                },
                axisTick: {
                  show: false,
                },
                axisLine: {
                  show: false,
                },
                axisLabel: {
                  show: true,
                  textStyle: {
                    color: '#fff',
                    fontSize: 28,
                  },
                },
              },
              {
                type: 'value',
                position: 'right',
                splitLine: {
                  show: false,
                  lineStyle: {
                    width: 1,
                    color: '#3d5269',
                  },
                },
                axisTick: {
                  show: false,
                },
                axisLine: {
                  show: false,
                },
                axisLabel: {
                  show: true,
                  formatter: '{value}%',
                  textStyle: {
                    color: '#fff',
                    fontSize: 28,
                  },
                },
              },
            ],
            series: [
              {
                name: '全省最佳应用',
                type: 'bar',
                data: y1,
                itemStyle: {
                  normal: {
                    color: '#2391ff',
                  },
                },
              },
              {
                name: '全市最佳应用',
                type: 'bar',
                data: y2,
                itemStyle: {
                  normal: {
                    color: '#ffc328',
                  },
                },
              },
              {
                name: '理论和制度成果',
                type: 'line',
                data: y3,
                yAxisIndex: 1,
                symbolSize: 0,
                lineStyle: {
                  width: 3,
                },
              },
            ],
          }
          myChartsState.setOption(option)
          tools.loopShowTooltip(myChartsState, option, {
            loopSeries: true,
          }) //轮播
        },
        //绘制进度条
        progresschartsShow(data) {
          const myChartsState = echarts.init(
            document.getElementById('szsh-chart1')
          )
          var fontColor = '#30eee9'
          let x = data.map((item) => {
            return item.name
          })
          let y1 = data.map((item) => {
            return item.value
          })
          var myColor = ['#23e4ab']
          let positionLeft = 0.4,
            max = 100 + 2 * positionLeft
          var option = {
            // backgroundColor: "#101E44",
            grid: {
              left: '4%',
              top: '12%',
              right: '5%',
              bottom: '8%',
              containLabel: true,
            },
            xAxis: [
              {
                max: max,
                show: false,
              },
            ],
            yAxis: [
              {
                type: 'category',
                inverse: true,
                axisTick: {
                  show: false,
                },
                axisLine: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                  inside: false,
                },
                data: x,
              },

              {
                axisLine: {
                  lineStyle: {
                    color: 'rgba(0,0,0,0)',
                  },
                },
                data: [],
              },
            ],
            series: [
              {
                name: '数据内框',
                type: 'bar',
                itemStyle: {
                  normal: {
                    barBorderRadius: 30,
                    color: '#00b5eb',
                  },
                },
                label: {
                  normal: {
                    show: true,
                    //   position: 'right',
                    color: '#fff',
                    fontSize: 28,
                    formatter: '{b}:{c}',
                    position: [0, '-35px'],
                  },
                },
                barWidth: 30,
                data: y1,
              },
              {
                name: '外框',
                type: 'bar',
                itemStyle: {
                  normal: {
                    barBorderRadius: 30,
                    color: '#fff',
                    fontSize: 28,
                    color: 'rgba(255, 255, 255, 0.14)', //rgba设置透明度0.14
                  },
                },
                barGap: '-100%',
                z: 0,
                barWidth: 30,
                data: [100, 100, 100, 100, 100],
              },
            ],
          }
          myChartsState.setOption(option)
          tools.loopShowTooltip(myChartsState, option, {
            loopSeries: true,
          }) //轮播
        },
        //绘制饼图
        PiechartsShow(data) {
          const myChartsPerson = echarts.init(
            document.getElementById('szsh-chart2')
          )
          var fontColor = '#30eee9'
          let option = {
            grid: {
              // left: "15%",
              right: '2%',
              // top: "30%",
              // bottom: "15%",
              containLabel: true,
            },
            legend: {
              // orient: "vertical",
              x: 'center',
              y: 0,
              itemWidth: 24,
              itemHeight: 14,
              align: 'left',
              textStyle: {
                color: '#fff',
                fontSize: 28,
              },
            },
            tooltip: {
              trigger: 'item',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
            },
            color: ['#5087EC', '#F2BD42', '#58A55C', '#F2BD42', '#EE752F'],
            series: [
              {
                name: '党政机关指标分析',
                type: 'pie',
                radius: '70%',
                center: ['50%', '50%'],
                data: data,
                itemStyle: {
                  normal: {
                    label: {
                      show: true,
                      color: '#fff',
                      fontSize: 25,
                      position: 'inside',
                      formatter: '{b}:\n{d}%',
                    },
                  },
                  labelLine: { show: false },
                },
              },
            ],
          }

          myChartsPerson.setOption(option)
          // tools.loopShowTooltip(myChartsPerson, option, {
          //     loopSeries: true,
          // }); //轮播
        },
        //绘制统计柱图
        BarChartShow1(data) {
          let myChartDisptch = echarts.init(
            document.getElementById('szsh-chart4')
          )
          var legend = ['医疗', '养老', '教育', '人社', '住房']
          var colorList = ['#5087EC', '#68BBC4', '#58a55c', '#f2bd42']
          var data1 = []
          let x = data.map((item) => {
            return item.name
          })
          let y1 = data.map((item) => {
            return item.yli
          })
          let y2 = data.map((item) => {
            return item.yl
          })
          let y3 = data.map((item) => {
            return item.jy
          })
          let y4 = data.map((item) => {
            return item.rs
          })
          let y5 = data.map((item) => {
            return item.zf
          })
          data1.push(y5, y4, y3, y2, y1)
          let option = {
            tooltip: {
              trigger: 'axis',
              borderWidth: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              textStyle: {
                color: 'white',
                fontSize: '30',
              },
              axisPointer: {
                type: 'shadow',
              },
            },
            // color: colors,
            legend: {
              x: 'center',
              y: '15',
              itemWidth: 20,
              itemHeight: 20,
              textStyle: {
                color: '#fff',
                fontSize: 28,
              },
              data: legend,
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '10',
              containLabel: true,
            },
            xAxis: {
              type: 'category',
              interval: 0,
              axisLabel: {
                color: '#fff',
                fontSize: 28,
                interval: 0,
                // rotate: 30,
              },
              axisLine: {
                show: false,
                lineStyle: {
                  color: '#bbb',
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  color: '#195384',
                },
              },
              data: x,
            },
            yAxis: {
              type: 'value',
              name: '',
              nameTextStyle: {
                color: '#fff',
                fontSize: 28,
              },
              axisLabel: {
                formatter: '{value}',
                textStyle: {
                  color: '#fff',
                  fontSize: 28,
                },
              },
              axisLine: {
                lineStyle: {
                  color: '#fff',
                },
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: false,
                lineStyle: {
                  color: '#11366e',
                },
              },
            },
            series: [],
          }
          for (var i = 0; i < legend.length; i++) {
            option.series.push({
              name: legend[i],
              type: 'bar',
              stack: '总量',
              barWidth: 55,
              itemStyle: {
                normal: {
                  color: colorList[i],
                },
              },
              label: {
                show: true,
                position: 'inside',
                textStyle: {
                  color: '#fff',
                  fontSize: 28,
                },
              },
              data: data1[i],
            })
          }
          myChartDisptch.setOption(option)
          tools.loopShowTooltip(myChartDisptch, option, { loopSeries: true })
        },
      },

      //项目生命周期
      mounted() {
        this.init()
      },
    })
  </script>
</html>
