<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>社会管理-左</title>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <script src="/Vue/vue.js"></script>
  <script src="/jquery/jquery-3.4.1.min.js"></script>
  <script src="/echarts/echarts.min.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/static/js/jslib/vue-count-to.min.js"></script>
  <script src="/static/citybrain/hjbh/js/echarts-liquidfill.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    .left_main_box {
      position: relative;
      width: 2070px;
      height: 1850px;
      background: url("/img/left-bg.png") no-repeat;
      background-size: 100%;
      padding: 20px 30px 30px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
    }

    .number {
      display: inline-block;
      font-size: 45px;
    }

    .number .numbg {
      display: inline-block;
      width: 41px;
      height: 53px;
      line-height: 54px;
      text-align: center;
      background: url("/static/citybrain/csdn/img/ywt/num-bg.png") no-repeat;
      background-size: contain;
      margin: 0 4px;
    }

    .tab_trun {
      list-style: none;
      width: 100%;
      height: 44px;
      display: flex;
      justify-content: space-around;
      border-bottom: 1px solid #162e4994;
      font-size: 32px;
      color: #6d7e94;
    }

    .tab_trun li {
      text-align: center;
      min-width: 180px;
    }

    .active_li {
      color: #fff !important;
      border-bottom: 4px solid #fff !important;
      margin-bottom: -3px;
    }

    .act {
      color: #0560c5;
    }
  </style>
</head>

<body>
  <div id="app">
    <div class="left_main_box">
      <div class="left">
        <nav>
          <s-header-title htype="2" title="基本概况" data-time="2022年9月21日"></s-header-title>
        </nav>
        <nav>
          <s-header-title-2 title="机动车保有量分布"></s-header-title-2>
        </nav>
        <div style="width: 100%; height: 440px">
          <div style="
                text-align: center;
                display: flex;
                justify-content: center;
                align-items: center;
              ">
            <p class="s-c-grey-light s-font-30">总量</p>
            <div class="number s-c-yellow-gradient" v-for="(item, i) in value" :key="i">
              <span class="numbg" v-if="item!=','&&item!='.'">
                <count-to :start-val="0" :end-val="Number(item)" :duration="3000" class="s-c-yellow-gradient">
                </count-to>
              </span>
              <span v-else>{{item}}</span>
            </div>
            <p class="s-c-yellow-gradient s-font-25">辆</p>
          </div>
          <div id="pieEchart01" style="width: 100%; height: 375px"></div>
        </div>
        <nav style="margin:10px;">
          <s-header-title-2 title="重点车辆分布"></s-header-title-2>
        </nav>
        <div id="pieEchart02" style="width: 100%; height: 440px"></div>
        <nav style="margin:10px;">
          <s-header-title-2 title="驾驶人分布"></s-header-title-2>
        </nav>
        <div style="width: 100%; height: 440px">
          <div class="s-flex s-row-between">
            <div v-for="(item,i) in list">
              <s-img-text-box src="/static/citybrain/csdn/img/ywt3-Right-new/r-005.png" img-width="87px"
                img-height="79px" :name="item.name" :value="item.value" :unit="item.unit" name-text-size="32px"
                value-text-size="44px" value-text-color="lg-yellow" unit-text-size="30px" unit-text-color="lg-yellow">
              </s-img-text-box>
            </div>
          </div>
          <nav style="margin-top: 40px">
            <ul class="tab_trun">
              <li style="cursor: pointer;" v-for="(item ,index) in fbName" :class="{active_li:isAct==index}"
                @click="changeFB(index)">{{item}}</li>
            </ul>
          </nav>
          <div v-show="isAct===0" class="s-flex s-row-between" style="margin-top: 50px">
            <div class="s-flex-1 s-flex s-row-between">
              <img src="/static/citybrain/hjbh/img/rkzt/nan.png" alt="" />
              <div id="qiuEchart01" style="width: 70%; height: 300px"></div>
            </div>
            <div class="s-flex-1 s-flex s-row-between">
              <img src="/static/citybrain/hjbh/img/rkzt/nv.png" alt="" />
              <div id="qiuEchart02" style="width: 70%; height: 300px"></div>
            </div>
          </div>
          <div v-show="isAct===1" id="barEchart02" style="width: 100%; height: 350px"></div>
        </div>
      </div>
      <!--  -->
      <div class="right">
        <nav>
          <s-header-title htype="2" title="交通警情" data-time="2022年9月21日"></s-header-title>
        </nav>
        <nav>
          <s-header-title-2 title="交通警情趋势分析"></s-header-title-2>
        </nav>
        <div class="s-flex s-row-between s-font-30 s-w7 s-c-grey-light"
          style="width: 150px; position: absolute; top: 170px; right: 50px;cursor: pointer;">
          <span v-for="(item ,index) in name" :class="{act:isActive===index}" @click="changeDate(index)">{{item}}</span>
        </div>
        <div style="display: flex;align-items: flex-end;justify-content: center;">
          <div class="text-name" style="font-size: 28px; color: rgb(255, 255, 255);margin-right: 20px;">交通警情</div>
          <div class="text-name" style="font-size: 38px; color: rgb(255, 255, 255);">2009800</div>
          <div class="text-name" style="font-size: 28px; color: rgb(255, 255, 255);">起</div>
        </div>
        <div id="LineEchart01" style="width: 100%; height: 440px"></div>
        <nav>
          <s-header-title-2 title="交通警情类型分布"></s-header-title-2>
        </nav style="margin:10px;">
        <div id="pieEchart03" style="width: 100%; height: 440px"></div>
        <nav style="margin:10px;">
          <s-header-title-2 title="高峰时段警情趋势"></s-header-title-2>
        </nav>
        <nav style="margin: 40px">
          <ul class="tab_trun">
            <li style="cursor: pointer;" v-for="(item ,index) in jt" :class="{active_li:isSG===index}"
              @click="changeSG(index)">{{item}}</li>
          </ul>
        </nav>
        <div id="lineEchart" style="width: 100%; height: 400px"></div>
      </div>
    </div>
  </div>

  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script>
    let vm = new Vue({
      el: "#app",
      data: {
        isSG: 0,
        jt: ['事故', "拥堵", "建议", "违法"],
        click_name: "年",
        list: [],
        isActive: 0,
        isAct: 0,
        name: ['年', "季度", '月', '周'],
        value: "",
        fbName: ["性别分布", "年龄分布"]
      },
      mounted() {
        this.initEchart();
        this.openIframe1();
        this.openIframe2();
      },
      methods: {
        changeSG(index) {
          this.isSG = index
          if (this.isSG == 0) {
            //$api('shgl_jtgl-left_jtgl05', { type: "事故" }).then(res => {
            //  this.lineEchart("lineEchart", res);
            //})
            $get('shgl/jtgl-left/jtgl05').then(res => {
              this.lineEchart("lineEchart", res);
            })
          } else if (this.isSG == 1) {
            //$api('shgl_jtgl-left_jtgl05', { type: "拥堵" }).then(res => {
            //  this.lineEchart("lineEchart", res);
            //})
            $get('shgl/jtgl-left/jtgl11').then(res => {
              this.lineEchart("lineEchart", res);
            })
          } else if (this.isSG == 2) {
            //$api('shgl_jtgl-left_jtgl05', { type: "建议" }).then(res => {
            //  this.lineEchart("lineEchart", res);
            //})
            $get('shgl/jtgl-left/jtgl12').then(res => {
              this.lineEchart("lineEchart", res);
            })
          } else if (this.isSG == 3) {
            //$api('shgl_jtgl-left_jtgl05', { type: "违法" }).then(res => {
            //  this.lineEchart("lineEchart", res);
            //})
            $get('shgl/jtgl-left/jtgl13').then(res => {
              this.lineEchart("lineEchart", res);
            })
          }
        },
        changeFB(index) {
          this.isAct = index
          if (this.isAct == 0) {
            // $api('shgl_jtgl-left_jtgl04', { type: "性别分布" }).then(res => {
            //  this.Echarts01("qiuEchart01", ["#0D9BCA", "#0bbef9"], res[0].menEc / 100);
            //  this.Echarts01("qiuEchart02", ["#5f4789", "#ca83ea"], res[0].womenEc / 100);
            // })
            $get('shgl/jtgl-left/jtgl04').then(res => {
              this.Echarts01("qiuEchart01", ["#0D9BCA", "#0bbef9"], res.menEc / 100);
              this.Echarts01("qiuEchart02", ["#5f4789", "#ca83ea"], res.womenEc / 100);
            })
          } else if (this.isAct == 1) {
            //$api('shgl_jtgl-left_jtgl04', { type: "年龄分布" }).then(res => {
            //  this.getEcharts01(res)
            //})
            $get('shgl/jtgl-left/jtgl07').then(res => {
              this.getEcharts01(res)
            })
          }
        },
        changeDate(index) {
          this.isActive = index
          if (this.isActive == 0) {
            //$api('shgl_jtgl-left_jtgl06', { type: "年" }).then(res => {
            //  this.setEchart("LineEchart01", res);
            //})
            $get('shgl/jtgl-left/jtgl06').then(res => {
              this.setEchart("LineEchart01", res);
            })
          } else if (this.isActive == 1) {
            //$api('shgl_jtgl-left_jtgl06', { type: "季" }).then(res => {
            //  this.setEchart("LineEchart01", res);
            //})
            $get('shgl/jtgl-left/jtgl08').then(res => {
              this.setEchart("LineEchart01", res);
            })
          } else if (this.isActive == 2) {
            //$api('shgl_jtgl-left_jtgl06', { type: "月" }).then(res => {
            //  this.setEchart("LineEchart01", res);
            //})
            $get('shgl/jtgl-left/jtgl09').then(res => {
              this.setEchart("LineEchart01", res);
            })
          } else if (this.isActive == 3) {
            //$api('shgl_jtgl-left_jtgl06', { type: "周" }).then(res => {
            //  this.setEchart("LineEchart01", res);
            //})
            $get('shgl/jtgl-left/jtgl10').then(res => {
              this.setEchart("LineEchart01", res);
            })
          }
        },
        initEchart() {

          // $api('shgl_jtgl-left_jtgl01').then(res => {
          //  let num = res.map(item => {
          //   return item.value
          // });
          // this.value = (num[0] + num[1] + num[2] + num[3]).toString()
          // this.setPie01("pieEchart01", res);
          //})
          $get('shgl/jtgl-left/jtgl01').then(res => {
            let num = res.echart.map(item => {
             return item.value
           });
           this.value = (num[0] + num[1] + num[2] + num[3]).toString()
           this.setPie01("pieEchart01", res.echart);
          })
          //$api('shgl_jtgl-left_jtgl04', { type: "性别分布" }).then(res => {
          //  this.Echarts01("qiuEchart01", ["#0D9BCA", "#0bbef9"], res[0].menEc / 100);
          //  this.Echarts01("qiuEchart02", ["#5f4789", "#ca83ea"], res[0].womenEc / 100);
          //})
          $get('shgl/jtgl-left/jtgl04').then(res => {
            console.log(res, 'ressss')
            this.Echarts01("qiuEchart01", ["#0D9BCA", "#0bbef9"], res.menEc / 100);
            this.Echarts01("qiuEchart02", ["#5f4789", "#ca83ea"], res.womenEc / 100);
          })
          // $api('shgl_jtgl-left_jtgl02').then(res => {
          //  this.setPie02("pieEchart02", res);
          // })
          $get('shgl/jtgl-left/jtgl02').then(res => {
            this.setPie02("pieEchart02", res);
          })
          // $api('shgl_jtgl-left_jtgl03').then(res => {
          //  this.setPie02("pieEchart03", res);
          // })
          $get('shgl/jtgl-left/jtgl03').then(res => {
            this.setPie02("pieEchart03", res);
          })
          $api('shgl_jtgl-left_jtgl03').then(res => {
            this.setPie02("pieEchart03", res);
          })
          $api('shgl_jtgl-left_jtgl04', { type: "驾驶人分布" }).then(res => {
            this.list = res
          })

          //$api('shgl_jtgl-left_jtgl05', { type: "事故" }).then(res => {
          //  this.lineEchart("lineEchart", res);
          //})
          $get('shgl/jtgl-left/jtgl05').then(res => {
              this.lineEchart("lineEchart", res);
            })
          //$api('shgl_jtgl-left_jtgl06', { type: "年" }).then(res => {
          //  this.setEchart("LineEchart01", res);
          //})
          $get('shgl/jtgl-left/jtgl06').then(res => {
            this.setEchart("LineEchart01", res);
          })


        },
        getEcharts01(res) {
          let myCharts = echarts.init(document.getElementById("barEchart02"));
          let yData = res.map((item) => {
            return item.name;
          });
          let value = res.map((item) => {
            return item.value;
          });
          let option = {
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "shadow",
              },
              textStyle: {
                fontSize: 30,
              },
            },
            grid: {
              top: "0",
              bottom: "10%",
              left: "20%",
              right: "30%"
            },
            xAxis: {
              type: "value",
              name: "%",
              nameTextStyle: {
                fontSize: 30,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                show: false,
              },
              axisLabel: {
                fontSize: 30,
                color: "#ffff",
              },
            },
            yAxis: {
              type: "category",
              data: yData,

              axisLabel: {
                fontSize: 30,
                color: "#ffff",
              },
            },
            series: [
              {
                type: "bar",
                data: value,
              },
            ],
          };
          myCharts.setOption(option);
        },
        setPie01(dom, echartData) {
          let echartsDom = echarts.init(document.getElementById(dom));

          let imgUrl = "/static/citybrain/djtl/img/djtl-left/echarts-bg.png";
          let option = {
            tooltip: {
              trigger: "item",
              formatter: "{b}: <br/>{c}辆",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "30",
              },
            },
            legend: {
              orient: "vertical",
              itemWidth: 18,
              itemHeight: 18,
              left: "50%",
              top: "20%",
              icon: "circle",
              itemGap: 40,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 30,
                padding: [0, 0, 0, 20],
              },
              formatter: function (name) {
                var data = option.series[0].data; //获取series中的data
                var total = 0;
                var tarValue;
                for (var i = 0, l = data.length; i < l; i++) {
                  total += data[i].value;
                  if (data[i].name == name) {
                    tarValue = data[i].value;
                  }
                }
                var p = (tarValue / total).toFixed(1) * 100;
                return name + "  " + tarValue + "辆  " + p + '%';
              },
            },
            graphic: [
              {
                z: 4,
                type: "image",
                id: "logo",
                left: "19.3%",
                top: "18%",
                z: -10,
                bounding: "raw",
                rotation: 0, //旋转
                origin: [-30, 30], //中心点
                scale: [0.69, 0.69], //缩放
                style: {
                  image: imgUrl,
                  opacity: 1,
                },
              },
            ],
            series: [
              {
                name: "",
                type: "pie",
                radius: ["60%", "80%"],
                center: ["30%", "50%"],
                itemStyle: {
                  normal: {
                    borderColor: "#0A1934",
                  },
                },
                label: {
                  show: false,
                },
                data: echartData,
              },
            ],
          };

          echartsDom.setOption(option);
          echartsDom.getZr().on("mousemove", (param) => {
            echartsDom.getZr().setCursorStyle("default");
          });
        },
        setPie02(dom, pieData02) {
          let echartsDom = echarts.init(document.getElementById(dom));
          let lenArr = [];
          pieData02.map((ele) => {
            lenArr.push(ele.name);
          });

          let option = {
            color: ["#f2bd42", "#60b565", "#5087ec", "#72cdd7"],
            tooltip: {
              trigger: "item",
              formatter: "{b}: {c} ({d}%)",
              textStyle: {
                fontSize: 30
              }
            },
            legend: {
              orient: "vertical",
              right: "5%",
              align: "right",
              top: "middle",
              textStyle: {
                fontSize: 24,
                color: "#f5f5f5",
              },
              data: lenArr,
            },
            series: [
              {
                name: "",
                type: "pie",
                center: ["40%", "50%"],
                radius: ["40%", "60%"],
                avoidLabelOverlap: false,
                label: {
                  normal: {
                    formatter: "{b|{b}}\n{d|{d}%}\n{c|{c}辆}",
                    rich: {
                      b: {
                        fontSize: 24,
                        color: "#FFF",
                        align: "left",
                        padding: 4,
                      },
                      hr: {
                        borderColor: "#CCCCCC",
                        width: "100%",
                        borderWidth: 2,
                        height: 0,
                      },
                      d: {
                        fontSize: 24,
                        align: "left",
                        padding: 4,
                        color: "#FFF",
                      },
                      c: {
                        fontSize: 24,
                        align: "left",
                        padding: 4,
                        color: "#FFF",
                      },
                    },
                  },
                },
                labelLine: {
                  normal: {
                    show: true,
                  },
                },
                data: pieData02,
              },
            ],
          };
          echartsDom.setOption(option);
          echartsDom.getZr().on("mousemove", (param) => {
            echartsDom.getZr().setCursorStyle("default");
          });
        },
        Echarts01(id, col, num) {
          let myEc = echarts.init(document.getElementById(id));
          let option = {
            title: [
              {
                show: false,
                text: "综合贡献度",
                x: "10%",
                y: "85%",
                textStyle: {
                  fontSize: 38,
                  fontWeight: "500",
                  color: "#fff",
                },
              },
            ],
            series: [
              {
                type: "liquidFill",
                radius: "75%",
                color: col,
                center: ["48%", "48%"],
                data: [
                  num,
                  {
                    value: num,
                    phase: Math.PI,
                  },
                ],
                label: {
                  normal: {
                    textStyle: {
                      fontSize: 55,
                      color: col[1],
                    },
                  },
                },
                itemStyle: {
                  normal: {
                    label: {
                      show: true,
                      formatter: (e) => {
                        return parseInt(e.data * 100);
                      },
                    },
                  },
                },
                backgroundStyle: {
                  borderWidth: 1,
                  color: "rgba(220, 192, 179, 0.06)",
                },
              },
            ],
          };
          myEc.setOption(option);
          myEc.getZr().on("mousemove", (param) => {
            myEc.getZr().setCursorStyle("default");
          });
        },
        lineEchart(id, lineData) {
          let myEc = echarts.init(document.getElementById(id));
          let datax = [],
            datay = [];
          lineData.map((ele) => {
            datax.push(ele.xdata);
            datay.push(ele.ydata);
          });
          let option = {
            title: {
              show: false,
              text: "用电量",
            },
            tooltip: {
              trigger: "axis",
            },
            legend: {
              show: false,
              data: ["2018", "2019"],
            },
            grid: {
              left: "3%",
              right: "4%",
              bottom: "8%",
              containLabel: true,
            },
            tooltip: {
              trigger: "axis",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              axisPointer: {
                lineStyle: {
                  color: "rgba(11, 208, 241, 1)",
                  type: "slider",
                },
              },
              textStyle: {
                color: "rgba(212, 232, 254, 1)",
                fontSize: 28,
              },
            },
            xAxis: [
              {
                type: "category",
                offset: 20,
                axisLine: {
                  //坐标轴轴线相关设置。数学上的x轴
                  show: true,
                  lineStyle: {
                    color: "rgba(108, 166, 219, 0.3)",
                  },
                },
                axisLabel: {
                  //坐标轴刻度标签的相关设置
                  rotate: -30,
                  textStyle: {
                    color: "#fff",
                    fontSize: 24,
                  },
                },
                splitLine: {
                  show: false,
                  lineStyle: {
                    color: "#192a44",
                  },
                },
                axisTick: {
                  show: false,
                },
                data: datax,
              },
            ],
            yAxis: [
              {
                name: "",
                min: (value) => {
                  return parseInt(value.min - 1);
                },
                nameTextStyle: {
                  fontSize: 24,
                  color: "#D6E7F9",
                  padding: [0, 20, 10, 0],
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: "#77b3f1",
                    opacity: 0.1,
                    width: 2,
                  },
                },
                axisTick: {
                  show: true,
                  lineStyle: {
                    color: "#77b3f1",
                    opacity: 0.5,
                    width: 2,
                  },
                },
                axisLabel: {
                  textStyle: {
                    fontSize: 24,
                    color: "#D6E7F9",
                  },
                },
                axisLine: {
                  show: false,
                  lineStyle: {
                    color: "#233653",
                  },
                },
              },
            ],
            series: [
              {
                name: "事故",
                type: "line",
                itemStyle: {
                  normal: {
                    color: "#3A84FF",
                    lineStyle: {
                      color: "#1b759c",
                      width: 4,
                    },
                    areaStyle: {
                      color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                        {
                          offset: 0,
                          color: "rgba(2, 92, 131,0.9)",
                        },
                        {
                          offset: 1,
                          color: "rgba(2, 92, 131,0.2)",
                        },
                      ]),
                    },
                  },
                },
                data: datay,
              },
            ],
          };
          myEc.setOption(option);
          myEc.getZr().on("mousemove", (param) => {
            myEc.getZr().setCursorStyle("default");
          });
        },
        setEchart(id, lineData) {
          let myEc = echarts.init(document.getElementById(id));
          let xdata = [],
            sdata = [],
            ydata = [],
            schain = [],
            ychain = [];
          lineData.forEach((ele) => {
            xdata.push(ele.xname);
            sdata.push(ele.sname);
            ydata.push(ele.yname);
            schain.push(ele.xpoint);
            ychain.push(ele.ypoint);
          });
          let option = {
            color: ["#00C0FF", "#A9DB52", "#FF4949", "#FFC665"],
            title: {
              text: "",
              top: "5px",
              left: "10px",
              textStyle: {
                color: "#fff",
                fontSize: 28,
                fontFamily: "SourceHanSansCN-Medium",
              },
            },
            tooltip: {
              trigger: "axis",
              borderWidth: 0,
              backgroundColor: "#000000",
              textStyle: {
                color: "#fff",
                fontSize: 30,
              },
            },
            legend: {
              selectedMode: true,
              top: "20px",
              itemGap: 20,
              textStyle: {
                color: "#fff",
                fontSize: 28,
                fontFamily: "SourceHanSansCN-Medium",
              },
              itemWidth: 18,
              itemHeight: 18,
            },
            grid: {
              top: "20%",
              left: "1%",
              right: "1%",
              bottom: "0%",
              containLabel: true,
            },
            xAxis: [
              {
                type: "category",
                data: xdata,
                splitLine: { show: false },
                axisTick: {
                  //y轴刻度线
                  show: false,
                },
                axisLine: {
                  lineStyle: {
                    color: "#0f2944", // 颜色
                    width: 1, // 粗细
                  },
                },
                offset: 15,
                axisLabel: {
                  rotate: 15,
                  textStyle: {
                    color: "#fff",
                    fontSize: 28,
                    fontFamily: "SourceHanSansCN-Medium",
                  },
                },
              },
            ],
            yAxis: [
              {
                type: "value",
                splitLine: {
                  lineStyle: {
                    color: "#0F3D60",
                  },
                },
                axisLabel: {
                  textStyle: {
                    color: "#fff",
                    fontSize: 28,
                    fontFamily: "SourceHanSansCN-Medium",
                  },
                },
              },

              {
                type: "value",
                name: "",

                interval: 20,
                nameTextStyle: {
                  color: "#d6e7f9",
                  fontSize: 28,
                },
                axisLine: {
                  //纵轴线
                  show: false,
                  lineStyle: {
                    color: "rgba(119,179,241,0.5)",
                    width: 1,
                  },
                },
                axisLabel: {
                  //纵轴线标签
                  formatter: "{value} %",
                  textStyle: {
                    color: "#d6e7f9",
                    fontSize: 28,
                  },
                  margin: 20,
                },
                // splitNumber: 4,
                splitLine: {
                  //分隔线
                  show: false,
                  lineStyle: {
                    color: "#114c93",
                    width: 1,
                  },
                },
              },
            ],
            series: [
              {
                cursor: "auto",
                name: "事故",
                type: "bar",
                data: sdata,
                barWidth: 16,
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "#00c0ff",
                      },
                      {
                        offset: 0.5,
                        color: "#075d8a",
                      },
                      {
                        offset: 1,
                        color: "#09355b",
                      },
                    ]),
                    barBorderRadius: 4,
                  },
                },
                label: {
                  show: false, //开启显示
                  position: "top", //在上方显示
                  textStyle: {
                    //数值样式
                    color: "#FFFFFF",
                    fontFamily: "SourceHanSansCN-Regular",
                    fontSize: 28,
                  },
                },
              },
              {
                name: "拥堵",
                data: ydata,
                type: "bar",
                barWidth: 16,
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "#f3bc5f",
                      },
                      {
                        offset: 0.5,
                        color: "#7c7357",
                      },
                      {
                        offset: 1,
                        color: "#455962",
                      },
                    ]),
                    barBorderRadius: 4,
                  },
                },
                label: {
                  show: false, //开启显示
                  position: "top", //在上方显示
                  textStyle: {
                    //数值样式
                    color: "#FFFFFF",
                    fontFamily: "SourceHanSansCN-Regular",
                    fontSize: 28,
                  },
                },
              },
              {
                name: "事故环比",
                type: "line",
                symbolSize: 12,
                data: schain,
                yAxisIndex: 1,
                itemStyle: {
                  color: "#00fefe",
                  borderColor: "#FFC460",
                  borderWidth: 0,
                },
              },
              {
                name: "结案率",
                type: "line",
                symbolSize: 12,
                data: ychain,
                yAxisIndex: 1,
                itemStyle: {
                  color: "#97e13c",
                  borderColor: "#FFC460",
                  borderWidth: 1,
                },
              },
            ],
          };

          myEc.setOption(option);
          myEc.getZr().on("mousemove", (param) => {
            myEc.getZr().setCursorStyle("default");
          });
        },
        openIframe1() {
          let left = {
            type: 'openIframe',
            name: 'jtgl-doing1',
            src: baseURL.url + '/static/citybrain/shgl/commont/jtgl-doing1.html',
            left: "2150px",
            top: "230px",
            width: "400px",
            height: "420px",
            zIndex: "10",
            argument: {
              status: ""
            }
          }
          window.parent.postMessage(
            JSON.stringify(left), '*'
          )
        },
        openIframe2() {
          let left = {
            type: 'openIframe',
            name: 'jtgl-doing3',
            src: baseURL.url + '/static/citybrain/shgl/commont/jtgl-doing3.html',
            left: "calc(50% - 1690px)",
            top: "1683px",
            width: "3380px",
            height: "400px",
            zIndex: "10",
            argument: {
              status: ""
            }
          }
          window.parent.postMessage(
            JSON.stringify(left), '*'
          )
        }
      },
    });
  </script>
</body>

</html>