<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Document</title>
  <script src="./Vue/vue.js"></script>
  <script src="./jquery/jquery-3.4.1.min.js"></script>
  <script src="./echarts/echarts.min.js"></script>
  <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Sans+SC:100,300,400,500,700,900"> -->
  <link rel="stylesheet" href="./css/fjfk-left.css" />
  <link rel="stylesheet" href="./css/common.css" />
</head>

<body>
  <div id="fjfk_app">
    <div class="fjfk_app_box">

      <div class="fjfk_header clearfix">
        <h2 class="title"> <i class="title_icon_one"></i>封控区</h2>
        <div class="title_hr" style="width: 920px;left: 0px"></div>
        <h3 class="title" style="font-weight: normal;font-size: 32px; margin-right: 70px">数据截止时间：2021年2月11日</h3>
      </div>

      <div class="fjfk-body">

        <!-- top -->
        <ul class="fjfk-body-top">
          <li>
            <img src="./img/fjfk_left_top_1.png" alt="">
            <div>
              <p class="text linear-blue_0">封控区数量</p>
              <p class="number">
                <span><b>0</b></span>
                <span><b>0</b></span>
                <span><b>4</b></span>
                <em class="linear-yellow_0">个</em>
              </p>
            </div>
          </li>
          <li>
            <img src="./img/fjfk_left_top_1.png" alt="">
            <div>
              <p class="text linear-blue_0">管理人数</p>
              <p class="number">
                <span><b>9</b></span>
                <span><b>9</b></span>
                <span><b>9</b></span>
                <span><b>9</b></span>
                <span><b>4</b></span>
                <em class="linear-yellow_0">个</em>
              </p>
            </div>
          </li>
          <li>
            <img src="./img/fjfk_left_top_1.png" alt="">
            <div>
              <p class="text linear-blue_0">累计检测</p>
              <p class="number">
                <span><b>9</b></span>
                <span><b>9</b></span>
                <span><b>9</b></span>
                <span><b>9</b></span>
                <span><b>4</b></span>
                <em class="linear-yellow_0">个</em>
              </p>
            </div>
          </li>
        </ul>

        <!-- middle -->
        <div class="fjfk-body-middle">
          <div class="fjfk_header_center_text">
            <div class="fjfk_header_title different_font">
              <div class="left_cell"><img src="./img/right.png" alt=""></div>
              <div class="titletext" style="font-size: 40px;text-align: center;margin-top: -10px;">检测情况</div>
              <div class="right_cell"><img src="./img/left.png" alt=""></div>
            </div>
          </div>

          <div class="fjfk-body-middle-header">
            <span class="linear-blue_0">检测出阳性</span>
            <span class="number"><b>0</b></span>
            <span class="number"><b>0</b></span>
            <span class="number"><b>9</b></span>
            <span class="linear-blue_0">人</span>
          </div>

          <div class="fjfk-body-middle-chart" id="checkChart"></div>
        </div>

        <!-- bottom -->
        <div class="fjfk-body-bottom">
          <div class="fjfk_header_center_text">
            <div class="fjfk_header_title different_font">
              <div class="left_cell"><img src="./img/right.png" alt=""></div>
              <div class="titletext" style="font-size: 40px;text-align: center;margin-top: -10px;">封控情况</div>
              <div class="right_cell"><img src="./img/left.png" alt=""></div>
            </div>
          </div>

          <div class="fjfk-body-bottom-header">
            <div v-for="(item, i) in tabsList" :key="i" :class="{ active: tabIndex === i }" @click="tabChange(i)">{{ item }}</div>
          </div>

          <div class="fjfk-body-bottom-left">
            <p class="fjfk-subTitle">
              <span class="l-12"></span>
              <span class="subTitle-text">管理数据</span>
            </p>
            <div class="left-content">
              <div v-for="(item, i) in manageData" :key="i">
                <p class="text">{{ item.text }}</p>
                <p class="number"><span>{{ item.value }}</span><b>{{ item.unit }}</b></p>
              </div>
            </div>
          </div>
          <div class="fjfk-body-bottom-right">
            <p class="fjfk-subTitle">
              <span class="l-12"></span>
              <span class="subTitle-text">特殊人群</span>
            </p>
            <div class="right-content">
              <div class="right-content-chart" id="peopleChart"></div>
              <div class="chart-legend">
                <div class="legend-item">
                  <div class="icon blue"></div>
                  <div class="name">残疾人</div>
                  <div class="percent value1">20.0%</div>
                </div>
                <div class="legend-item">
                  <div class="icon purple"></div>
                  <div class="name">未成年人</div>
                  <div class="percent value2">20.0%</div>
                </div>
                <div class="legend-item">
                  <div class="icon orange"></div>
                  <div class="name">老年人</div>
                  <div class="percent value3">20.0%</div>
                </div>
                <div class="legend-item">
                  <div class="icon yellow"></div>
                  <div class="name">滞留人员</div>
                  <div class="percent value4">20.0%</div>
                </div>
                <div class="legend-item">
                  <div class="icon red"></div>
                  <div class="name">低保户</div>
                  <div class="percent value5">20.0%</div>
                </div>
              </div>
            </div>
          </div>

        </div>

      </div>

    </div>
  </div>

  <script src="./js/fjfk-left.js"></script>
</body>

</html>

