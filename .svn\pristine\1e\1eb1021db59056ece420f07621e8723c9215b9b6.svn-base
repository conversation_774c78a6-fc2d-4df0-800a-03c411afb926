<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>人社服务-左</title>
  <script src="/static/citybrain/csdn/Vue/vue.js"></script>
  <script src="/static/js/jslib/datav.min.vue.js"></script>
  <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
  <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/static/js/jslib/axios.min.js"></script>
  <script src="/static/js/jslib/http.interceptor.js"></script>
  <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
  <style>
    @font-face {
      font-family: num1;
      src: url("/static/fonts/时尚中黑简体.ttf");
    }

    .cl_gry {
      color: #b6c4c9;
    }

    .haerder2 {
      width: 100%;
      height: 55px;
      background: url("/static/citybrain/ggfw/img/header2_bg.png") no-repeat;
    }

    .icon_message {
      display: inline-block;
      width: 30px;
      height: 30px;
      background-color: red;
      font-size: 26px;
      color: #fff;
      text-align: center;
      line-height: 30px;
      border-radius: 15px;
      font-style: inherit;
      margin-left: 20px;
    }

    .click_btn {
      position: absolute;
      top: 1000px;
      left: 1600px;
      display: flex;
    }

    .click_btn span {
      cursor: pointer;
      margin-right: 20px;
      width: 180px;
      height: 58px;
      text-align: center;
      line-height: 58px;
      display: inline-block;
      font-size: 30px;
      color: #c3e2fc;
      background: url("/static/citybrain/ggfw/img/but2.png") no-repeat;
      background-size: 100% 100%;
    }

    .active_area {
      background: url("/static/citybrain/ggfw/img/but1.png") no-repeat !important;
      background-size: 100% 100% !important;
      color: #fff !important;
    }
  </style>
</head>

<body>
  <div id="app" class="rsfw-left-main">
    <nav>
      <s-header-title title="参保情况" :data-time="nowTime"> </s-header-title>
    </nav>
    <div class="s-flex s-row-around s-flex-wrap">
      <p v-for="(item,i) in cbqk" class="s-flex s-row-between" style="width: 40%; height: 20px">
        <span class="cl_gry s-font-30">
          <img src="/static/citybrain/ggfw/img/list.png" width="25px" height="25px" />
          {{item.name}}
        </span>
        <span class="s-c-blue-gradient1 s-font-40 s-w7">
          <span style="font-family: num1">{{item.num}}</span>
          <span class="s-font-30">{{item.unit}}</span>
        </span>
      </p>
    </div>
    <nav class="s-m-t-10 s-m-b-10">
      <s-header-title-2 htype="1" title="企业职工养老保险参保" :data-time="nowTime">
      </s-header-title-2>
    </nav>
    <div class="s-flex">
      <div id="echarPie1" style="width: 30%; height: 280px"></div>
      <div id="echarBar1" style="width: 70%; height: 280px"></div>
    </div>
    <nav class="s-m-t-10 s-m-b-10">
      <s-header-title title="人才情况" :data-time="nowTime"> </s-header-title>
    </nav>
    <div class="s-flex s-row-around s-flex-wrap">
      <p v-for="(item,i) in rcqk" class="s-flex s-row-between" style="width: 30%; height: 40px">
        <span class="cl_gry s-font-30 s-flex" style="max-width: 55%">
          <img src="/static/citybrain/ggfw/img/list.png" width="25px" height="25px" />
          {{item.name}}
        </span>
        <span class="s-c-blue-gradient1 s-font-40 s-w7">
          <span style="font-family: num1">{{item.num}}</span>
          <span class="s-font-30">{{item.unit}}</span>
        </span>
      </p>
    </div>
    <p class="haerder2">
      <span class="s-font-35 s-c-blue-gradient1 s-m-l-60 s-w7">未完成区县</span>
      <i class="icon_message">2</i>
    </p>
    <p class="click_btn">
      <span :class="click_index==1?'active_area':''" @click="wwcqxClick(1,'婺城区')">
        婺城区
      </span>
      <span :class="click_index==2?'active_area':''" @click="wwcqxClick(2,'武义县')">
        武义县
      </span>
    </p>
    <div id="line_echart1" style="width: 100%; height: 280px"></div>
    <p class="haerder2">
      <span class="s-font-35 s-c-blue-gradient1 s-m-l-60 s-w7">
        技能证书分布
      </span>
    </p>
    <div id="line_echart2" style="width: 100%; height: 280px"></div>
  </div>

  <script>
    new Vue({
      el: "#app",
      data: {
        nowTime: "", //当前时间
        click_index: 1,
        cbqk: [],
        rcqk: [],
      },
      mounted() {
        this.initApi();
        this.getTime();
      },
      methods: {
        initApi() {
          $api("/ggfw_rsfw_rsfw_left_dialog_cbqk_list").then((res) => {
            this.cbqk = res;
          });
          $api("/ggfw_rsfw_rsfw_left_dialog_pie_echart").then((res) => {
            this.getPie01("echarPie1", res);
          });
          $api("/ggfw_rsfw_rsfw_left_dialog_bar_echart").then((res) => {
            this.getBar01("echarBar1", res);
          });
          $api("/ggfw_rsfw_rsfw_left_dialog_rcqk_list").then((res) => {
            this.rcqk = res;
          });
          $api("/ggfw_rsfw_rsfw_left_dialog_noover_bar_echart", { area: "婺城区" }).then(
            (res) => {
              this.getLine01("line_echart1", res);
            }
          );
          $api("/ggfw_rsfw_rsfw_left_dialog_jnzsfb_bar_echart").then(
            (res) => {
              this.getLine02("line_echart2", res);
            }
          );
        },
        wwcqxClick(index, type) {
          this.click_index = index
          $api("/ggfw_rsfw_rsfw_left_dialog_noover_bar_echart", { area: type }).then(
            (res) => {
              this.getLine01("line_echart1", res);
            }
          );
        },
        //获取当前时间
        getTime() {
          var data = new Date();
          var yesterday = new Date(data.setDate(data.getDate() - 1));
          this.nowTime =
            yesterday.getFullYear() +
            "年" +
            (yesterday.getMonth() + 1) +
            "月" +
            yesterday.getDate() +
            "日";
        },
        //企业职工-饼图
        getPie01(id, res) {
          let myEc = echarts.init(document.getElementById(id));
          let imgUrl = "/static/citybrain/djtl/img/djtl-left/echarts-bg.png";
          const option = {
            color: ["#167cdb", "#5ef6fd", "#22ef8c", "#def066"],
            tooltip: {
              trigger: "item",
              formatter: "{b}: <br/>{d}%",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "30",
              },
            },
            legend: {
              orient: "vertical",
              itemWidth: 18,
              itemHeight: 18,
              left: "50%",
              top: "25%",
              itemGap: 20,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 28,
                padding: [0, 0, 0, 10],
              },
            },
            graphic: [
              {
                z: 4,
                type: "image",
                id: "logo",
                left: "54.5%",
                top: "60%",
                z: -10,
                bounding: "raw",
                rotation: 0, //旋转
                origin: [-540, -198], //中心点
                scale: [0.5, 0.5], //缩放
                style: {
                  image: imgUrl,
                  opacity: 1,
                },
              },
            ],
            series: [
              {
                name: "",
                type: "pie",
                radius: ["55%", "75%"],
                center: ["23%", "52%"],
                itemStyle: {
                  normal: {
                    borderWidth: 10,
                    borderColor: "#0A1934",
                  },
                },
                label: {
                  show: false,
                },
                data: res,
              },
            ],
          };
          myEc.setOption(option);
        },
        //企业职工-柱状
        getBar01(id, res) {
          let myChart = echarts.init(document.getElementById(id));
          var legend = ["参保职工人数", "农民工", "缴费人数", "离退休人数"];
          var colorList = ["#44c6fc", "#fbe183", "#51e699", "#e378e8"];
          var data = [];
          let x = res.map((item) => {
            return item.area;
          });
          let y1 = res.map((item) => {
            return item.cb_num;
          });
          let y2 = res.map((item) => {
            return item.nm_num;
          });
          let y3 = res.map((item) => {
            return item.jf_num;
          });
          let y4 = res.map((item) => {
            return item.tx_num;
          });
          data.push(y1, y2, y3, y4);
          let option = {
            tooltip: {
              trigger: "item",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "30",
              },
            },
            legend: {
              x: "center",
              y: "15",
              itemWidth: 20,
              itemHeight: 20,
              itemGap: 50,
              textStyle: {
                color: "#fff",
                fontSize: 28,
              },
              data: legend,
            },
            grid: {
              left: "3%",
              right: "4%",
              bottom: "0",
              containLabel: true,
            },
            xAxis: {
              type: "category",
              axisLabel: {
                color: "#fff",
                fontSize: 28,
                // rotate: 45,
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#195384",
                },
              },
              splitLine: {
                show: false,
                lineStyle: {
                  color: "#195384",
                },
              },
              data: x,
            },
            yAxis: {
              type: "value",
              name: "单位：人",
              nameTextStyle: {
                color: "#fff",
                fontSize: 28,
              },
              axisLabel: {
                formatter: "{value}",
                textStyle: {
                  color: "#fff",
                  fontSize: 28,
                },
              },
              axisLine: {
                lineStyle: {
                  color: "#fff",
                },
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: "#11366e",
                },
              },
            },
            series: [],
          };
          for (var i = 0; i < legend.length; i++) {
            option.series.push({
              name: legend[i],
              type: "bar",
              stack: "总量",
              barWidth: 35,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 0.9, [
                    {
                      offset: 0,
                      color: colorList[i],
                    },
                    {
                      offset: 0.5,
                      color: colorList[i],
                    },
                    {
                      offset: 1,
                      color: "#031827",
                    },
                  ]),
                },
              },
              label: {
                show: false,
                position: "insideRight",
              },
              data: data[i],
            });
          }
          myChart.setOption(option);
          tools.loopShowTooltip(myChart, option, { loopSeries: true });
        },
        // 未完成区县
        getLine01(dom, echartsData) {
          let echarts0 = echarts.init(document.getElementById(dom));
          var xData = echartsData.map((item) => {
            return item.name;
          }),
            yData1 = echartsData.map((item) => {
              return item.value;
            }),
            yData2 = echartsData.map((item) => {
              return item.value1;
            }),
            borderData = [],
            legend = ["实际值", "目标值"],
            colorArr = [
              {
                start: "rgba(71, 173, 245,1)",
                end: "rgba(18, 58, 86,0.5)",
              },
              {
                start: "rgba(218, 201, 126,1)",
                end: "rgba(18, 58, 86,0.5)",
              },
            ];
          var normalColor = ["rgba(255,255,255,0.9)", "rgba(255,255,255,0.9)", "rgba(255,255,255,0.9)", "rgba(255,255,255,0.9)", "rgba(255,255,255,0.9)", "rgba(255,255,255,0.9)",];
          let seriesData = [];
          var borderHeight = 4;
          xData.forEach((element) => {
            borderData.push(borderHeight);
          });
          yData1.forEach((item, index) => {
            if (Math.abs(item - yData2[index]) > 400) {
              normalColor[index] = "red";
            }
          });
          [yData1, yData2].forEach((item, index) => {
            var obj1 = {};
            if (index < 3) {
              obj1 = {
                name: legend[index],
                type: "bar",
                // stack: legend[index],
                data: item,
                barWidth: "12%",
                itemStyle: {
                  normal: {
                    color: {
                      type: "linear",
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: colorArr[index].start + "0.7)",
                        },
                        {
                          offset: 0.5,
                          color: colorArr[index].start + "0.3)",
                        },
                        {
                          offset: 1,
                          color: colorArr[index].end,
                        },
                      ],
                      globalCoord: false,
                    },
                  },
                },
              };
              seriesData.push(obj1);
            }
          });
          let option = {
            grid: {
              top: "20%",
              right: 0,
              left: "2%",
              bottom: 0,
              containLabel: true,
            },
            legend: {
              show: true,
              itemWidth: 30,
              icon: "square",
              itemGap: 100,
              itemHeight: 30,
              top: "2%",
              textStyle: {
                color: "#fff",
                fontSize: 30,
              },
              data: legend,
            },
            tooltip: {
              trigger: "axis",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "30",
              },
              formatter: function (params) {
                var str = "";
                for (var i = 0; i < params.length; i++) {
                  if (params[i].seriesName !== "") {
                    str +=
                      params[i].name +
                      ":" +
                      params[i].seriesName +
                      params[i].value +
                      "<br/>";
                  }
                }
                return str;
              },
            },
            xAxis: [
              {
                type: "category",
                data: xData,
                axisPointer: {
                  type: "shadow",
                },
                axisLabel: {
                  textStyle: {
                    color: (params, index) => {
                      return normalColor[index];
                    },
                    fontSize: 26,
                  },
                  //坐标轴刻度标签的相关设置。
                  formatter: function (params) {
                    var newParamsName = ""; // 最终拼接成的字符串
                    var paramsNameNumber = params.length; // 实际标签的个数
                    var provideNumber = 9; // 每行能显示的字的个数
                    var rowNumber = Math.ceil(
                      paramsNameNumber / provideNumber
                    ); // 换行的话，需要显示几行，向上取整
                    /**
                     * 判断标签的个数是否大于规定的个数， 如果大于，则进行换行处理 如果不大于，即等于或小于，就返回原标签
                     */
                    // 条件等同于rowNumber>1
                    if (paramsNameNumber > provideNumber) {
                      /** 循环每一行,p表示行 */
                      for (var p = 0; p < rowNumber; p++) {
                        var tempStr = ""; // 表示每一次截取的字符串
                        var start = p * provideNumber; // 开始截取的位置
                        var end = start + provideNumber; // 结束截取的位置
                        // 此处特殊处理最后一行的索引值
                        if (p == rowNumber - 1) {
                          // 最后一次不换行
                          tempStr = params.substring(start, paramsNameNumber);
                        } else {
                          // 每一次拼接字符串并换行
                          tempStr = params.substring(start, end) + "\n";
                        }
                        newParamsName += tempStr; // 最终拼成的字符串
                      }
                    } else {
                      // 将旧标签的值赋给新标签
                      newParamsName = params;
                    }
                    //将最终的字符串返回
                    return newParamsName;
                  },
                },
                axisLine: {
                  lineStyle: {
                    color: "#0e3a63",
                  },
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
              },
            ],
            yAxis: [
              {
                type: "value",
                name: "数量 个",
                nameTextStyle: {
                  color: normalColor,
                  fontSize: 26,
                  padding: 10,
                },
                interval: 400,
                axisLabel: {
                  formatter: "{value}",
                  textStyle: {
                    color: normalColor,
                    fontSize: 26,
                  },
                },
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: "#0e3a63",
                  },
                },
              },
            ],
            series: seriesData,
          };
          echarts0.setOption(option);
        },
        // 技能证书分布
        getLine02(dom, echartsData) {
          let echarts0 = echarts.init(document.getElementById(dom));
          var xData = echartsData.map((item) => {
            return item.name;
          }),
            yData1 = echartsData.map((item) => {
              return item.value1;
            }),
            yData2 = echartsData.map((item) => {
              return item.value2;
            }),
            yData3 = echartsData.map((item) => {
              return item.value3;
            }),
            borderData = [],
            legend = ["初级工种", "中级工种", "高级工种"],
            colorArr = [
              {
                start: "rgba(71, 173, 245,1)",
                end: "rgba(18, 58, 86,0.5)",
              },
              {
                start: "rgba(218, 201, 126,1)",
                end: "rgba(18, 58, 86,0.5)",
              },
              {
                start: "rgba(86, 242, 161,1)",
                end: "rgba(27, 87, 75,0.5)",
              },
            ];
          xData.push("金华市");
          yData1.push(3800);
          yData2.push(3600);
          yData3.push(3000);
          var normalColor = "rgba(255,255,255,0.9)";
          let seriesData = [];
          var borderHeight = 4;
          xData.forEach((element) => {
            borderData.push(borderHeight);
          });
          [yData1, yData2, yData3].forEach((item, index) => {
            var obj1 = {};
            // var obj2 = {};
            if (index < 3) {
              obj1 = {
                name: legend[index],
                type: "bar",
                // stack: legend[index],
                data: item,
                barWidth: "12%",
                itemStyle: {
                  normal: {
                    color: {
                      type: "linear",
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: colorArr[index].start + "0.7)",
                        },
                        {
                          offset: 0.5,
                          color: colorArr[index].start + "0.3)",
                        },
                        {
                          offset: 1,
                          color: colorArr[index].end,
                        },
                      ],
                      globalCoord: false,
                    },
                  },
                },
              };
              // obj2 = {
              //   name: "",
              //   type: "bar",
              //   stack: legend[index],
              //   itemStyle: {
              //     normal: {
              //       color: colorArr[index].start + "1)",
              //     },
              //   },
              //   data: borderData,
              // };
              seriesData.push(obj1);
              // seriesData.push(obj2);
            }
          });
          let option = {
            grid: {
              top: "20%",
              right: 0,
              left: "2%",
              bottom: 0,
              containLabel: true,
            },
            legend: {
              show: true,
              itemWidth: 30,
              icon: "square",
              itemGap: 100,
              itemHeight: 30,
              top: "2%",
              textStyle: {
                color: "#fff",
                fontSize: 30,
              },
              data: legend,
            },
            tooltip: {
              trigger: "axis",
              borderWidth: 0,
              backgroundColor: "rgba(0, 0, 0, 0.6)",
              textStyle: {
                color: "white",
                fontSize: "30",
              },
              formatter: function (params) {
                var str = "";
                for (var i = 0; i < params.length; i++) {
                  if (params[i].seriesName !== "") {
                    str +=
                      params[i].name +
                      ":" +
                      params[i].seriesName +
                      params[i].value +
                      "<br/>";
                  }
                }
                return str;
              },
            },
            xAxis: [
              {
                type: "category",
                data: xData,
                axisPointer: {
                  type: "shadow",
                },
                axisLabel: {
                  textStyle: {
                    color: normalColor,
                    fontSize: 30,
                  },
                },
                axisLine: {
                  lineStyle: {
                    color: "#0e3a63",
                  },
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
              },
            ],
            yAxis: [
              {
                type: "value",
                name: "数量 个",
                nameTextStyle: {
                  color: normalColor,
                  fontSize: 26,
                  padding: 10,
                },
                interval: 400,
                axisLabel: {
                  formatter: "{value}",
                  textStyle: {
                    color: normalColor,
                    fontSize: 30,
                  },
                },
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: "#0e3a63",
                  },
                },
              },
            ],
            series: seriesData,
          };
          echarts0.setOption(option);
        },
      },
    });
  </script>
</body>

</html>