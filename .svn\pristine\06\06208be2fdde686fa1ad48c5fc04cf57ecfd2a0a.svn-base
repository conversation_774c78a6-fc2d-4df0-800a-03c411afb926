<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title>交通治理-地面公交</title>
    <script src="/Vue/vue.js"></script>
    <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
    <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css">
    <link rel="stylesheet" href="/static/citybrain/shgl/css/jtzl.css">
    <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
    <script src="/static/js/comjs/s.min.vue.js"></script>
    <script src="/static/js/jslib/axios.min.js"></script>
    <script src="/static/js/jslib/http.interceptor.js"></script>
    <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script> <!-- 轮播toolTip -->
</head>


<body>
    <div id="app">
        <div class="jtzl-main">
            <div class="floor-title">地面公交</div>
            <!-- 车辆营运 -->
            <div class="car" id="car-run"></div>
            <!-- 车辆调度 -->
            <div class="car" id="car-dispatch"></div>
            <!-- 车辆状态 -->
            <div class="car" id="car-state"></div>
            <!-- 驾驶员情况 -->
            <div class="car" id="person-situation"></div>
            <!-- 春运客流量趋势 -->
            <div class="car" id="car-trend"></div>
            <!-- 春运客流量预测 -->
            <div class="car" id="car-divine"></div>

        </div>
    </div>

    </div>

    <script type="module">

        new Vue({
            el: '#app',
            data: {
                runData: [],//车辆营运列表数据
                dispatchData: [],//车辆调度数据
                stateData: [],//车辆状态数据
                situationData: [],//驾驶员情况数据
                trendData: [],//春运客流量趋势数据
                divineData: [],//春运客流量预测数据

            },
            methods: {
                //初始化数据
                initData() {
                    this.getCarRunData();
                    this.getDivineDataData();
                    this.getPiePersonData();
                    this.getcarStateData();
                    this.getcarDisptchData();
                },
                //获取车辆营运、春运趋势数据
                getCarRunData() {
                    $api('shgl_jtzl_car-run',{type:1}).then(res => {
                        this.runData = res;
                        this.carRunchartsShow('car-run', '车辆运营', '运营里程数', '');
                        this.carRunchartsShow('car-trend', '春节客流量趋势', '春节客流量', '单位: 万人');
                    })
                },
                //获取驾驶员情况饼图
                getPiePersonData() {
                    $api('shgl_jtzl_car-person',{type:1}).then(res => {
                        this.situationData = res;
                        this.personchartsShow();
                    })
                },
                //获取车辆状态数据
                getcarStateData() {
                    $api('shgl_jtzl_car-state',{type:1}).then(res => {
                        this.stateData = res;
                        this.carStatechartsShow();
                    })
                },
                //获取车辆调度数据
                getcarDisptchData() {
                    $api('shgl_jtzl_car-disptch',{type:1}).then(res => {
                        this.dispatchData = res;
                        this.disptchChartShow();
                    })
                },
                //获取春运客流量预测数据
                getDivineDataData() {
                    $api('shgl_jtzl_car-divine',{type:1}).then(res => {
                        console.log(res);
                        this.divineData = res;
                        this.divinechartsShow();
                    })
                },
                //绘制车辆营运、春运趋势折现图
                carRunchartsShow(id, title, legend, unit) {
                    const myChartsRun = echarts.init(document.getElementById(id))
                    var fontColor = "#30eee9";
                    let x = this.runData.map((item) => {
                        return item.name;
                    })
                    let y = this.runData.map((item) => {
                        return item.value;
                    })
                    let option = {
                        // backgroundColor: "#11183c",
                        title: {
                            text: title,
                            x: "center",
                            top: "0",
                            textStyle: { color: "#fff", fontSize: "32" },
                        },
                        grid: {
                            left: "5%",
                            right: "10%",
                            top: "25%",
                            bottom: "5%",
                            containLabel: true,
                        },
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        legend: {
                            show: true,
                            x: "center",
                            y: "45",
                            itemWidth: 20,
                            itemHeight: 20,
                            textStyle: {
                                color: "#fff",
                                fontSize: "28px",
                            },
                            data: [legend],
                        },
                        xAxis: [
                            {
                                type: "category",
                                boundaryGap: false,
                                axisLabel: {
                                    color: '#fff',
                                    rotate: 45,
                                    fontSize: "28px",
                                },
                                axisLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#bbb",
                                    },
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#195384",
                                    },
                                },
                                data: x,
                            },
                        ],
                        yAxis: [
                            {
                                type: "value",
                                name: unit,
                              
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                axisLabel: {
                                    formatter: "{value}",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: "28px",
                                    },
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: "#fff",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#5087EC",
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: legend,
                                type: "line",
                                stack: "总量",
                                // symbol: "circle",
                                symbolSize: 10,
                                itemStyle: {
                                    normal: {
                                        color: "#0092f6",
                                        lineStyle: {
                                            color: "#5087EC",
                                            width: 4,
                                        },
                                    },
                                },
                                areaStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "rgba(71,121,213,1)",
                                                },
                                                {
                                                    offset: 1,
                                                    color: "rgba(71,121,213,0)",
                                                },
                                            ],
                                            false
                                        ),
                                    }
                                },
                                data: y,
                            },
                        ],
                    };
                    myChartsRun.setOption(option);
                },
                //绘制车辆调度柱图
                disptchChartShow() {
                    let myChartDisptch = echarts.init(document.getElementById('car-dispatch'));
                    var legend = ["待调度", "调度中", "已调度"];
                    var colorList = ['#5087EC', '#68BBC4', '#58A55C'];
                    var data = [];
                    let x = this.dispatchData.map((item) => {
                        return item.name;
                    })
                    let y1 = this.dispatchData.map((item) => {
                        return item.doingValue;
                    })
                    let y2 = this.dispatchData.map((item) => {
                        return item.alreadyValue;
                    })
                    let y3 = this.dispatchData.map((item) => {
                        return item.stayValue;
                    })
                    data.push(y3, y1, y2);
                    let option = {
                        title: {
                            text: '车辆调度',
                            x: "center",
                            top: "0",
                            textStyle: { color: "#fff", fontSize: "32" },
                        },
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        // color: colors,
                        legend: {
                            x: "center",
                            y: "45",
                            itemWidth: 20,
                            itemHeight: 20,
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                            data: legend,
                        },
                        grid: {
                            left: "3%",
                            right: "4%",
                            bottom: "13%",
                            containLabel: true,
                        },
                        xAxis: {
                            type: "category",
                            axisLabel: {
                                color: '#fff',
                                fontSize: 28,
                                // rotate: 45,
                            },
                            axisLine: {
                                show: false,
                                lineStyle: {
                                    color: "#bbb",
                                },
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: "#195384",
                                },
                            },
                            data: x,
                        },
                        yAxis: {
                            type: "value",
                            name: "单位:辆",
                            nameTextStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                            axisLabel: {
                                formatter: "{value}",
                                textStyle: {
                                    color: "#fff",
                                    fontSize: 28
                                },
                            },
                            axisLine: {
                                lineStyle: {
                                    color: "#fff",
                                },
                            },
                            axisTick: {
                                show: false,
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: "#11366e",
                                },
                            },
                        },
                        series: [],
                    };
                    for (var i = 0; i < legend.length; i++) {
                        option.series.push({
                            name: legend[i],
                            type: "bar",
                            stack: "总量",
                            barWidth: 85,
                            itemStyle: {
                                normal: {
                                    color: colorList[i]
                                },
                            },
                            label: {
                                show: false,
                                position: "insideRight",
                            },
                            data: data[i],
                        });
                    }
                    myChartDisptch.setOption(option);
                    tools.loopShowTooltip(myChartDisptch, option, { loopSeries: true });
                },
                //绘制春运客流量预测折线图
                divinechartsShow() {
                    const myChartsDivine = echarts.init(document.getElementById("car-divine"))
                    let x = this.divineData.map((item) => {
                        return item.name;
                    })
                    let y = this.divineData.map((item) => {
                        return item.value;
                    })
                    let index = this.divineData.map((item) => {
                        return item.rate;
                    })
                    let option = {
                        title: {
                            text: "春节客流量预测",
                            x: "center",
                            top: "0",
                            textStyle: { color: "#fff", fontSize: "32" },
                        },
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },

                        grid: {
                            left: "8%",
                            top: "18%",
                            right: "5%",
                            bottom: "10%",
                        },
                        legend: {
                            data: ["预测客流量", "预测增长量"],
                            top: "8%",
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                        },
                        xAxis: {
                            data: x,
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: "#aaa",
                                    width: 1,
                                },
                            },
                            axisTick: {
                                show: true, //隐藏X轴刻度
                                alignWithLabel: true,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff", //X轴文字颜色
                                    fontSize: 28,
                                },
                                interval: 0,
                                // rotate: 30,
                            },
                        },
                        yAxis: [
                            {
                                type: "value",
                                name: "单位:万人",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        width: 1,
                                        color: "#3d5269",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                            {
                                type: "value",
                                name: "单位:%",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                position: "right",
                                splitLine: {
                                    show: false,
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#fff",
                                        width: 2,
                                    },
                                },
                                axisLabel: {
                                    show: true,
                                    formatter: "{value} ", //右侧Y轴文字显示
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "预测客流量",
                                type: "bar",
                                barWidth: 70,
                                color: '#5087EC',
                                itemStyle: {
                                    normal: {
                                        color: '#5087EC'
                                    },
                                },
                                data: y,
                            },

                            {
                                name: "预测增长量",
                                type: "line",
                                yAxisIndex: 1, //使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
                                showAllSymbol: true, //显示所有图形。
                                // symbol: "circle", //标记的图形为实心圆
                                symbolSize: 10, //标记的大小
                                itemStyle: {
                                    normal: {
                                        color: "#26D9FF",
                                        lineStyle: {
                                            color: "#26D9FF",
                                            width: 4,
                                        },
                                    },
                                },
                                data: index,
                            },
                        ],
                    };

                    myChartsDivine.setOption(option);
                },
                //绘制车辆状态柱图
                carStatechartsShow() {
                    const myChartsState = echarts.init(document.getElementById("car-state"))
                    var fontColor = "#30eee9";
                    let x = this.stateData.map((item) => {
                        return item.name;
                    })
                    let y1 = this.stateData.map((item) => {
                        return item.runValue;
                    })
                    let y2 = this.stateData.map((item) => {
                        return item.offlineValue;
                    })
                    let y3 = this.stateData.map((item) => {
                        return item.doingValue;
                    })
                    let option = {
                        title: {
                            text: "车辆状态",
                            x: "center",
                            top: "0",
                            textStyle: { color: "#fff", fontSize: "32" },
                        },
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        legend: {
                            data: ["行驶中", "已离线", "进行中"],
                            // align: "right",
                            top: 43,
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                        },
                        grid: {
                            left: "3%",
                            right: "4%",
                            bottom: "3%",
                            containLabel: true,
                        },
                        xAxis: {
                            data: x,
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: "#aaa",
                                    width: 1,
                                },
                            },
                            axisTick: {
                                show: true, //隐藏X轴刻度
                                alignWithLabel: true,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff", //X轴文字颜色
                                    fontSize: 28,
                                },
                                interval: 0,
                                // rotate: 30,
                            },
                        },
                        yAxis: [
                            {
                                type: "value",
                                name: "单位:辆",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        width: 1,
                                        color: "#3d5269",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "行驶中",
                                type: "bar",
                                data: y1,
                                itemStyle: {
                                    normal: {
                                        color: '#5087EC'
                                    },
                                },
                            },
                            {
                                name: "已离线",
                                type: "bar",
                                data: y2,
                                itemStyle: {
                                    normal: {
                                        color: '#68BBC4'
                                    },
                                },
                            },
                            {
                                name: "进行中",
                                type: "bar",
                                data: y3,
                                itemStyle: {
                                    normal: {
                                        color: '#58A55C'
                                    },
                                },
                            },
                        ],
                    };
                    myChartsState.setOption(option);
                    tools.loopShowTooltip(myChartsState, option, {
                        loopSeries: true,
                    }); //轮播
                },
                //绘制驾驶员情况饼图
                personchartsShow() {
                    const myChartsPerson = echarts.init(document.getElementById("person-situation"))
                    var fontColor = "#30eee9";
                    let option = {
                        title: {
                            text: "驾驶员情况",
                            x: "center",
                            top: "0",
                            textStyle: { color: "#fff", fontSize: "32" },
                        },
                        grid: {
                            // left: "15%",
                            right: '2%',
                            // top: "30%",
                            // bottom: "15%",
                            containLabel: true,
                        },
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        color: ["#5087EC", "#68BBC4", "#58A55C", "#F2BD42", "#EE752F"],
                        series: [
                            {
                                name: "驾驶员情况",
                                type: "pie",
                                radius: "75%",
                                center: ["50%", "50%"],
                                data: this.situationData,
                                itemStyle: {
                                    color: '#fff',
                                    emphasis: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: "rgba(0, 0, 0, 0.5)",
                                    },
                                },
                                itemStyle: {
                                    normal: {
                                        label: {
                                            show: true,
                                            color: '#fff',
                                            fontSize: 28,
                                            //	                            position:'inside',
                                            formatter: "{b}:\n{d}%",
                                        },
                                    },
                                    labelLine: { show: true },
                                },
                            },
                        ],
                    };

                    myChartsPerson.setOption(option);
                    tools.loopShowTooltip(myChartsPerson, option, {
                        loopSeries: true,
                    }); //轮播
                },
            },
            //项目生命周期
            mounted() {
                this.initData();
            }


        })


    </script>
</body>

</html>