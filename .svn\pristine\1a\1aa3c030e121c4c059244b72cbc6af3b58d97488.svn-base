<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <title>交通运输指标分析左侧面板</title>
        <script src="/Vue/vue.js"></script>
        <script src="/static/citybrain/csdn/jquery/jquery-3.4.1.min.js"></script>
        <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
        <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
        <link rel="stylesheet" href="/static/citybrain3840/shgl/css/jtyszbfx-left.css" />
        <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
        <script src="/static/js/comjs/s.min.vue.js"></script>
        <script src="/static/js/jslib/axios.min.js"></script>
        <script src="/static/js/jslib/http.interceptor.js"></script>
        <script src="/static/citybrain/scjg/js/lib/echarts-auto-tooltip.js"></script>
        <!-- 轮播toolTip -->
    </head>

    <body>
        <div id="app" class="container" v-cloak>
            <!-- 城市交通态势监测 -->
            <nav>
                <s-header-title htype="2" title="城市交通态势监测"></s-header-title>
            </nav>
            <nav>
                <s-header-title-2 htype="1" title="监测分析"></s-header-title-2>
            </nav>
            <div id="jcfx-chart"></div>
            <nav>
                <s-header-title-2 htype="1" title="道路重点设施及单位展示"></s-header-title-2>
            </nav>
            <div id="dlzdss-chart"></div>
            <nav>
                <s-header-title-2 htype="1" title="车辆及事件展示"></s-header-title-2>
            </nav>
            <div id="cljsjzs-chart"></div>
            <nav>
                <s-header-title-2 htype="1" title="交通运行情况展示"></s-header-title-2>
            </nav>
            <div class="top-con">
                <li
                    v-for="(item,index) in tabList"
                    :key="index"
                    :class="currentIndex===index?'active':''"
                    @click="changeTab(index,item)"
                >
                    {{item.name}}
                </li>
            </div>
            <div v-show="currentIndex===0">
                <div id="jtyxqkzs-chart"></div>
                <div class="table table1">
                    <div class="th">
                        <div class="th_td" style="flex: 0.45" v-for="(item,index) in theadList" :key="index">
                            {{item}}
                        </div>
                    </div>
                    <div class="tbody" id="tbody1">
                        <div class="tr" v-for="(item ,i) in tbodyList" :key="i">
                            <div class="tr_td" style="flex: 0.45">{{item.name}}</div>
                            <div class="tr_td" style="flex: 0.45">{{item.zs}}</div>
                            <div class="tr_td" style="flex: 0.45">{{item.sd}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-show="currentIndex===1">
                <div class="table table1">
                    <div class="th">
                        <div class="th_td" style="flex: 0.45" v-for="(item,index) in theadListOne" :key="index">
                            {{item}}
                        </div>
                    </div>
                    <div class="tbody" id="tbody1">
                        <div class="tr" v-for="(item ,i) in tbList" :key="i">
                            <div class="tr_td" style="flex: 0.45">{{item.sort}}</div>
                            <div class="tr_td" style="flex: 0.45">{{item.name}}</div>
                            <div class="tr_td" style="flex: 0.45">{{item.ydzs}}</div>
                            <div class="tr_td" style="flex: 0.45">{{item.lxsd}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-show="currentIndex===2">
                <div class="table table1">
                    <div class="th">
                        <div class="th_td" style="flex: 0.45" v-for="(item,index) in thList" :key="index" :title="item">
                            {{item}}
                        </div>
                    </div>
                    <div class="tbody" id="tbody1">
                        <div class="tr" v-for="(item ,i) in List" :key="i">
                            <div class="tr_td" style="flex: 0.45">{{item.sort}}</div>
                            <div class="tr_td" style="flex: 0.45" :title="item.name">{{item.name}}</div>
                            <div class="tr_td" style="flex: 0.45">{{item.ydzs}}</div>
                            <div class="tr_td" style="flex: 0.45">{{item.sd}}</div>
                            <div class="tr_td" style="flex: 0.45">{{item.lxsj}}</div>
                            <div class="tr_td" style="flex: 0.45">{{item.yssj}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    <script type="module">
        new Vue({
            el: "#app",
            data: {
                nowTime: "", //当前时间
                tabList: [
                    {
                        name: "道路交通",
                        id: "1",
                    },
                    {
                        name: "公共交通",
                        id: "2",
                    },
                    {
                        name: "对外交通",
                        id: "3",
                    },
                ],
                theadListOne: ["排名", "区域", "拥堵指数", "旅行速度"],
                currentIndex: 0,
                theadList: ["道路", "拥堵指数", "平均速度"],
                tbodyList: [
                    {
                        name: "八达路",
                        zs: "1.456",
                        sd: "27.28km/h",
                    },
                ],
                thList: ["排名", "道路名称", "拥堵延时指数", "速度", "旅行时间", "延迟时间"],
                tbList: [],
                List: [],
            },
            methods: {
                //获取当前时间
                getTime() {
                    var data = new Date();
                    var yesterday = new Date(data.setDate(data.getDate() - 1));
                    this.nowTime =
                        yesterday.getFullYear() + "年" + (yesterday.getMonth() + 1) + "月" + yesterday.getDate() + "日";
                },
                changeTab(index, data) {
                    this.currentIndex = index;
                },
                init() {
                    $api("ldst_shgl_jtys", { type: "1" }).then((res) => {
                        this.LinechartsShow("jcfx-chart", res);
                    });
                    $api("ldst_shgl_jtys", { type: "2" }).then((res) => {
                        this.OptionchartsShow(res);
                    });
                    $api("ldst_shgl_jtys", { type: "3" }).then((res) => {
                        this.BarchartsShow(res);
                    });
                    $api("ldst_shgl_jtys", { type: "3-1" }).then((res) => {
                        this.tbList = res;
                    });
                    $api("ldst_shgl_jtys", { type: "3-2" }).then((res) => {
                        this.List = res;
                    });
                    $api("ldst_shgl_jtys", { type: "jtyx" }).then((res) => {
                        this.ydchartsShow(res);
                    });
                },
                //绘制折线图
                LinechartsShow(id, data) {
                    const myChartsRun = echarts.init(document.getElementById(id));
                    var fontColor = "#30eee9";
                    let option = {
                        // backgroundColor: "#11183c",
                        // title: {
                        //     text: title,
                        //     x: "center",
                        //     top: "0",
                        //     textStyle: { color: "#fff", fontSize: "32" },
                        // },
                        grid: {
                            left: "5%",
                            right: "10%",
                            top: "25%",
                            bottom: "5%",
                            containLabel: true,
                        },
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        legend: {
                            show: true,
                            x: "center",
                            y: "5",
                            itemWidth: 20,
                            itemHeight: 20,
                            textStyle: {
                                color: "#fff",
                                fontSize: "28px",
                            },
                            // data: [legend],
                        },
                        xAxis: [
                            {
                                type: "category",
                                boundaryGap: false,
                                axisLabel: {
                                    color: "#fff",
                                    rotate: 45,
                                    fontSize: "28px",
                                },
                                axisLine: {
                                    show: true,
                                    lineStyle: {
                                        color: "#bbb",
                                    },
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#195384",
                                    },
                                },
                                data: data.map((item) => {
                                    return item.name;
                                }),
                            },
                        ],
                        yAxis: [
                            {
                                type: "value",
                                name: "",
                                min: 0,
                                // max: 1000,
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                axisLabel: {
                                    formatter: "{value}",
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: "28px",
                                    },
                                },
                                axisLine: {
                                    lineStyle: {
                                        color: "#fff",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        color: "#5087EC",
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "航运交通运输量",
                                type: "line",
                                stack: "总量",
                                // symbol: "circle",
                                symbolSize: 10,
                                itemStyle: {
                                    normal: {
                                        color: "#0092f6",
                                        lineStyle: {
                                            color: "#5087EC",
                                            width: 4,
                                        },
                                    },
                                },
                                areaStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "rgba(71,121,213,1)",
                                                },
                                                {
                                                    offset: 1,
                                                    color: "rgba(71,121,213,0)",
                                                },
                                            ],
                                            false
                                        ),
                                    },
                                },
                                data: data.map((item) => {
                                    return item.hyjt;
                                }),
                            },
                            {
                                name: "客运交通运输量",
                                type: "line",
                                stack: "总量",
                                // symbol: "circle",
                                symbolSize: 10,
                                itemStyle: {
                                    normal: {
                                        color: "rgba(105, 187, 196,1)",
                                        lineStyle: {
                                            color: "rgba(105, 187, 196,1)",
                                            width: 2,
                                        },
                                    },
                                },
                                areaStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "rgba(105, 187, 196,1)",
                                                },
                                                {
                                                    offset: 1,
                                                    color: "rgba(105 ,187, 196,0)",
                                                },
                                            ],
                                            false
                                        ),
                                    },
                                },
                                data: data.map((item) => {
                                    return item.kyjt;
                                }),
                            },
                            {
                                name: "地铁日客运量",
                                type: "line",
                                stack: "总量",
                                // symbol: "circle",
                                symbolSize: 10,
                                itemStyle: {
                                    normal: {
                                        color: "rgba(84 ,156, 89,1)",
                                        lineStyle: {
                                            color: "rgba(84 ,156, 89,1)",
                                            width: 2,
                                        },
                                    },
                                },
                                areaStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "rgba(84 ,156, 89,1)",
                                                },
                                                {
                                                    offset: 1,
                                                    color: "rgba(84 ,156, 89,0)",
                                                },
                                            ],
                                            false
                                        ),
                                    },
                                },
                                data: data.map((item) => {
                                    return item.dt;
                                }),
                            },
                            {
                                name: "公交客运总量",
                                type: "line",
                                stack: "总量",
                                // symbol: "circle",
                                symbolSize: 10,
                                itemStyle: {
                                    normal: {
                                        color: "rgba(242, 191 ,72,1)",
                                        lineStyle: {
                                            color: "rgba(242, 191 ,72,1)",
                                            width: 2,
                                        },
                                    },
                                },
                                areaStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "rgba(242, 191 ,72,1)",
                                                },
                                                {
                                                    offset: 1,
                                                    color: "rgba(242, 191 ,72,0)",
                                                },
                                            ],
                                            false
                                        ),
                                    },
                                },
                                data: data.map((item) => {
                                    return item.kyjt;
                                }),
                            },
                            {
                                name: "航班准点率",
                                type: "line",
                                stack: "总量",
                                // symbol: "circle",
                                symbolSize: 10,
                                itemStyle: {
                                    normal: {
                                        color: "rgba(199, 84 ,29,1)",
                                        lineStyle: {
                                            color: "rgba(199, 84 ,29,1)",
                                            width: 2,
                                        },
                                    },
                                },
                                areaStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "rgba(199, 84 ,29,1)",
                                                },
                                                {
                                                    offset: 1,
                                                    color: "rgba(199, 84 ,29,0)",
                                                },
                                            ],
                                            false
                                        ),
                                    },
                                },
                                data: data.map((item) => {
                                    return item.hb;
                                }),
                            },
                            {
                                name: "出租车客运总量",
                                type: "line",
                                stack: "总量",
                                // symbol: "circle",
                                symbolSize: 10,
                                itemStyle: {
                                    normal: {
                                        color: "rgba(195, 25, 32,1)",
                                        lineStyle: {
                                            color: "rgba(195, 25, 32,1)",
                                            width: 2,
                                        },
                                    },
                                },
                                areaStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "rgba(195, 25, 32,1)",
                                                },
                                                {
                                                    offset: 1,
                                                    color: "rgba(195, 25, 32,0)",
                                                },
                                            ],
                                            false
                                        ),
                                    },
                                },
                                data: data.map((item) => {
                                    return item.czc;
                                }),
                            },
                            {
                                name: "城际交通轨道客运量",
                                type: "line",
                                stack: "总量",
                                // symbol: "circle",
                                symbolSize: 10,
                                itemStyle: {
                                    normal: {
                                        color: "rgba(91, 142, 237,1)",
                                        lineStyle: {
                                            color: "rgba(91, 142, 237,1)",
                                            width: 2,
                                        },
                                    },
                                },
                                areaStyle: {
                                    normal: {
                                        color: new echarts.graphic.LinearGradient(
                                            0,
                                            0,
                                            0,
                                            1,
                                            [
                                                {
                                                    offset: 0,
                                                    color: "rgba(91, 142, 237,1)",
                                                },
                                                {
                                                    offset: 1,
                                                    color: "rgba(91, 142, 237,0)",
                                                },
                                            ],
                                            false
                                        ),
                                    },
                                },
                                data: data.map((item) => {
                                    return item.cjjt;
                                }),
                            },
                        ],
                    };
                    myChartsRun.setOption(option);
                    tools.loopShowTooltip(myChartsRun, option, {
                        loopSeries: true,
                    }); //轮播
                },
                //绘制进度
                OptionchartsShow(data) {
                    const myChartsState = echarts.init(document.getElementById("dlzdss-chart"));
                    var fontColor = "#30eee9";
                    let x = data.map((item) => {
                        return item.name;
                    });
                    let y1 = data.map((item) => {
                        return item.value;
                    });
                    let x1 = data.map((item) => {
                        return item.depart;
                    });
                    var myColor = ["#5EB0F5"];
                    let positionLeft = 0.4,
                        max = 100 + 2 * positionLeft;
                    var option = {
                        // backgroundColor: "#101E44",
                        grid: {
                            left: "4%",
                            top: "12%",
                            right: "5%",
                            bottom: "8%",
                            containLabel: true,
                        },
                        xAxis: [
                            {
                                max: max,
                                show: false,
                            },
                        ],
                        yAxis: [
                            {
                                axisTick: "none",
                                axisLine: "none",
                                offset: "15",
                                axisLabel: {
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: "28",
                                    },
                                },
                                data: x,
                            },
                            {
                                axisTick: "none",
                                axisLine: "none",
                                show: true,
                                axisLabel: {
                                    textStyle: {
                                        color: "#ffffff",
                                        fontSize: "28",
                                    },
                                },
                                data: x1,
                            },
                            {
                                axisLine: {
                                    lineStyle: {
                                        color: "rgba(0,0,0,0)",
                                    },
                                },
                                data: [],
                            },
                        ],
                        series: [
                            {
                                name: "数据内框",
                                type: "bar",
                                itemStyle: {
                                    normal: {
                                        barBorderRadius: 30,
                                        color: "#00b5eb",
                                    },
                                },
                                label: {
                                    normal: {
                                        show: true,
                                        position: "right",
                                        color: "#fff",
                                        fontSize: 28,
                                        formatter: "{c}",
                                    },
                                },
                                barWidth: 30,
                                data: y1,
                            },
                            {
                                name: "外框",
                                type: "bar",
                                itemStyle: {
                                    normal: {
                                        barBorderRadius: 30,
                                        color: "#fff",
                                        fontSize: 28,
                                        color: "rgba(255, 255, 255, 0.14)", //rgba设置透明度0.14
                                    },
                                },
                                barGap: "-100%",
                                z: 0,
                                barWidth: 30,
                                data: [100, 100, 100, 100, 100],
                            },
                        ],
                    };

                    myChartsState.setOption(option);
                    tools.loopShowTooltip(myChartsState, option, {
                        loopSeries: true,
                    }); //轮播
                },
                //绘制柱图
                BarchartsShow(data) {
                    const myChartsState = echarts.init(document.getElementById("cljsjzs-chart"));
                    var fontColor = "#30eee9";
                    let x = data.map((item) => {
                        return item.name;
                    });
                    let y1 = data.map((item) => {
                        return item.cls;
                    });
                    let y2 = data.map((item) => {
                        return item.sjs;
                    });
                    let option = {
                        tooltip: {
                            trigger: "item",
                            borderWidth: 0,
                            backgroundColor: "rgba(0, 0, 0, 0.6)",
                            textStyle: {
                                color: "white",
                                fontSize: "30",
                            },
                        },
                        legend: {
                            data: ["车辆数", "事件数"],
                            // align: "right",
                            top: 0,
                            textStyle: {
                                color: "#fff",
                                fontSize: 28,
                            },
                        },
                        grid: {
                            left: "3%",
                            right: "4%",
                            bottom: "3%",
                            containLabel: true,
                        },
                        xAxis: {
                            data: x,
                            axisLine: {
                                show: true, //隐藏X轴轴线
                                lineStyle: {
                                    color: "#aaa",
                                    width: 1,
                                },
                            },
                            axisTick: {
                                show: true, //隐藏X轴刻度
                                alignWithLabel: true,
                            },
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    color: "#fff", //X轴文字颜色
                                    fontSize: 28,
                                },
                                interval: 0,
                                // rotate: 30,
                            },
                        },
                        yAxis: [
                            {
                                type: "value",
                                name: "单位:个",
                                nameTextStyle: {
                                    color: "#fff",
                                    fontSize: 28,
                                },
                                splitLine: {
                                    show: false,
                                    lineStyle: {
                                        width: 1,
                                        color: "#3d5269",
                                    },
                                },
                                axisTick: {
                                    show: false,
                                },
                                axisLine: {
                                    show: false,
                                },
                                axisLabel: {
                                    show: true,
                                    textStyle: {
                                        color: "#fff",
                                        fontSize: 28,
                                    },
                                },
                            },
                        ],
                        series: [
                            {
                                name: "车辆数",
                                type: "bar",
                                data: y1,
                                itemStyle: {
                                    normal: {
                                        color: "#5087EC",
                                    },
                                },
                            },
                            {
                                name: "事件数",
                                type: "bar",
                                data: y2,
                                itemStyle: {
                                    normal: {
                                        color: "#68BBC4",
                                    },
                                },
                            },
                        ],
                    };
                    myChartsState.setOption(option);
                    tools.loopShowTooltip(myChartsState, option, {
                        loopSeries: true,
                    }); //轮播
                },
                //绘制拥堵路线
                ydchartsShow(data) {
                    const myChartsState = echarts.init(document.getElementById("jtyxqkzs-chart"));
                    var fontColor = "#30eee9";
                    let x = data.map((item) => {
                        return item.name;
                    });
                    let y1 = data.map((item) => {
                        return item.value;
                    });
                    var colorList = ["#3DBB33 ", "#FCAD2C", "#EA0000", "#9B0011"];
                    var option = {
                        grid: {
                            containLabel: true,
                            left: 20,
                            right: -20,
                            top: 0,
                            bottom: 0,
                        },
                        tooltip: {
                            show: false,
                        },
                        legend: {
                            icon: "circle",
                            bottom: "3%",
                            left: "10%",
                            itemWidth: 0,
                            itemHeight: 0,
                            itemGap: 160,
                            textStyle: {
                                color: "#fff",
                                fontSize: "28",
                            },
                            data: x,
                        },
                        xAxis: {
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            splitLine: {
                                show: false,
                            },
                            axisLabel: {
                                show: false,
                            },
                        },
                        yAxis: {
                            data: ["sss"],
                            axisLabel: {
                                show: false,
                            },
                            axisLine: {
                                show: false,
                            },
                            axisTick: {
                                show: false,
                            },
                            splitLine: {
                                show: false,
                            },
                        },
                        color: colorList,
                        series: [],
                    };

                    data.forEach((item, index) => {
                        option.series.push({
                            type: "bar",
                            name: item.name,
                            stack: "1",
                            label: {
                                normal: {
                                    borderWidth: 10,
                                    distance: 30,
                                    align: "center",
                                    verticalAlign: "middle",
                                    borderRadius: 1,
                                    borderColor: colorList[index],
                                    backgroundColor: colorList[index],
                                    show: true,
                                    position: "top",
                                    formatter: "{c}%",
                                    color: "#fff",
                                    fontSize: "25",
                                },
                            },
                            barWidth: 30,
                            data: [item.value],
                            itemStyle: {
                                normal: {
                                    barBorderRadius: [0],
                                },
                            },
                        });
                        if (index === 0) {
                            option.series[index].itemStyle.normal.barBorderRadius = [5, 0, 0, 5];
                        } else if (index === data.length - 1) {
                            option.series[index].itemStyle.normal.barBorderRadius = [0, 5, 5, 0];
                        } else {
                            return;
                        }
                    });
                    myChartsState.setOption(option);
                    tools.loopShowTooltip(myChartsState, option, {
                        loopSeries: true,
                    }); //轮播
                },
            },
            //项目生命周期
            mounted() {
                this.getTime();
                this.init();
            },
        });
    </script>
</html>
