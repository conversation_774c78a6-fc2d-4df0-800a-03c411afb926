ul {
    list-style: none;
}

.rk_app_box {
    position: relative;
    box-sizing: border-box;
    width: 1700px;
    height: 1870px;
    background-image: url("../img/common/bg.png");
    background-size: 100% 100%;
    padding: 40px 55px 30px;
}

.dwrh_header {
    width: 3700px;
    display: flex;
    height: 100px;
    align-items: center;
    background: url("../img/rkzt/longs.png") no-repeat;
    background-position: 0 63px;
}

.dwrh_header .title {
    margin-top: 20px;
    font-size: 53px;
    font-family: "思源黑体 CNBOLD";
    background-image: linear-gradient(to bottom, #f0ffff, #74b4f4, #83b8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
    margin-left: 5px;
}

.dwrh_header .title_icon_one {
    width: 80px;
    height: 80px;
    position: relative;
    left: 0;
    top: -10px;
    background-image: url("../img/common/一级标题1.png");
    background-size: 100% 100%;
}

.dwrh_header .title_hr {
    margin: 10px 30px 0;
    width: 2540px;
    height: 21px;
    background-image: url("../img/common/一级标题2.png");
    background-size: 100% 100%;
}

.dwrh_header span {
    font-size: 32px;
    font-family: "思源黑体 CNMEDIUM";
    background-image: linear-gradient(to bottom, #83b8ff, #74b4f4, #f0ffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.wrapper {
    display: flex;
    align-items: center;
    height: 100px;
    margin: 0 20px;
    background: url('../img/rkzt/titleBg.png') 50% 100% no-repeat;
    /* margin-bottom: 70px; */
    position: relative;
    font-size: 34px;
    color: #fff;
}

.left .wrapper>span {
    position: absolute;
    top: 110px;
}

.left .wrapper p {
    width: 966px;
    padding-left: 115px;
}

.left .wrapper .text {
    margin-right: 10px;
    font-family: "思源黑体 CNMEDIUM";
}

.wrapper p>.number b {
    display: inline-block;
    width: 50px;
    height: 80px;
    line-height: 70px;
    text-align: center;
    background: url('../img/rkzt/numBg.png') no-repeat;
    margin: 0 4px;
    border-radius: 8px;
}

.left .s-c-bul-light {
    display: flex;
    align-items: center;
    justify-content: center;
}

.left_box {
    display: flex;
    text-align: center;
    width: 1598px;
    height: 470px;
    margin: 45px auto;
    background: url('../img/rkzt/leftBox.png');
}

.left_box .left-top1 {
    position: relative;
    font-size: 40px;
    color: #fff;
}

.left_box .left-top1 div {
    position: absolute;
    top: 120px;
    width: 100%;
    line-height: 30px;
}

.left_box .left-top1 div span:last-child {
    font-size: 48px;
    padding: 0;
    margin: 0;
}

.left .left_box .imgbul {
    position: absolute;
    top: 480px;
    left: 500px;
}

.left .left_box .imgred {
    position: absolute;
    top: 480px;
    left: 1100px;
}

.left .box2 .text {
    position: absolute;
    width:390px;
    text-align: left;

}

.left .box2 .text span {
    display: inline-block;
}

.left_box ul li {
    float: left;
    width: 225px;
    line-height: 20px;
    text-align: left;
    margin-bottom: 20px;
}

.left_box ul li p i {
    display: inline-block;
    width: 15px;
    height: 15px;
    margin-left: -20px;
    border-radius: 50%;
}

.left_box .ul1 {
    margin: 90px 0 0 37px;
}

.left_box .ul2 {
    margin-top: 38px;
}

.left_box .ul2 li {
    width: 242px;
}

.left_box .ul2 li:first-child {
    margin-top: 40px;
}

.left_box .ul2 li:nth-child(4) {
    margin-top: -90px;
}


.right_box {
    display: flex;
    text-align: center;
    width: 1582px;
    height: 450px;
    margin: 100px auto;
}

.right_box .s-flex-1 {
    font-size: 70px;
    color: #fff;
    height: 340px;
    margin-right: 10px;
    margin-top: 60px;
    background: url('../img/rkzt/lefttel.png') no-repeat;
}

.right_box .s-flex-1 span {
    display: inline-block;
    width: 170px;
    height: 200px;
    margin-top: 80px;
    line-height: 80px;
}

.right_box .s-flex-4 {
    background: url('../img/rkzt/rightcon.png') no-repeat;
}

.right_box .boxyuan {
    display: flex;
    align-items: center;
    justify-content: center;
}