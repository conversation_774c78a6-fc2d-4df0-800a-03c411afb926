<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>领域4-中间地图</title>
  <script src="/static/citybrain/csdn/Vue/vue.js"></script>
  <script src="/static/citybrain/csdn/echarts/echarts.min.js"></script>
  <link rel="stylesheet" href="/static/css/sigma.css" />
  <link rel="stylesheet" href="/static/css/animate_dn.css" />
  <link rel="stylesheet" href="/static/citybrain/csdn/elementui/css/elementui.css" />
  <script src="/static/citybrain/csdn/elementui/js/elementui.js"></script>
  <script src="/static/js/jslib/vue-count-to.min.js"></script>
  <script src="/static/js/comjs/s.min.vue.js"></script>
  <script src="/static/js/jslib/datav.min.vue.js"></script>
  <script src="/static/citybrain/hjbh/js/auto-tooltip.js"></script>
  <link rel="stylesheet" href="/static/citybrain3840/shgl/css/map-dialog.css" />
  <script src="/static/citybrain/csdn/js/DHWs_tc.js"></script>
</head>
<style>
  #map-middle {
    position: relative;
    top: 320px;
  }

  .tree {
    height: 225px;
  }

  .tool-box {
    width: 346px;
    display: flex;
    height: 66px;
    background: #0d2047;
    color: #fff;
    font-size: 82px;
    font-weight: bolder;
    position: absolute;
    /* left: 1084px; */
    top: -80px;
    padding-top: 12px;
    padding-left: 240px;
    box-sizing: border-box;
  }

  .tool-box img {
    width: 40px;
    height: 40px;
    margin-right: 15px;
    cursor: pointer;
  }

  .disabeld {
    cursor: not-allowed !important;
    pointer-events: none;
  }

  .topDiv {
    width: 640px;
    height: 170px;
    background: url("/img/left-bg.png") no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: -80px;
    left: 600px;
  }

  .topContainer {
    display: flex;
    justify-content: center;
  }

  .topContainer .item {
    width: 310px;
    height: 170px;
    text-align: center;
    background: url("../../../shgl/img/itembg.png") no-repeat;
    background-size: 100% 100%;
  }

  .topContainer .item .name {
    color: #fff;
    font-size: 30px;
    margin-top: 30px;
  }

  .topContainer .item .value {
    background: linear-gradient(to bottom,
        #ffebce,
        #ffffff,
        #ffc559,
        #ffffff);
    -webkit-background-clip: text;
    color: transparent;
    font-size: 50px;
    margin-top: 5px;
  }

  .topContainer .item .value span {
    font-size: 30px;
  }
</style>

<body>
  <div id="map-middle" v-cloak>
    <div class="tool-box">
      <img src="/static/citybrain3840/shgl/img/放大.png" alt="" @click="optionTool('big')"
        :class="currentLabel==='在线监督员'?'disabeld':''" />
      <img src="/static/citybrain3840/shgl/img/缩小.png" alt="" @click="optionTool('small')"
        :class="currentLabel==='在线监督员'?'disabeld':''" />
    </div>
    <div class="tree">
      <!-- @check="treeCheck" -->
      <el-tree :data="treeData" show-checkbox @check="treeCheck" :check-on-click-node="true" node-key="id" ref="tree"
        highlight-current @check-change="checkChange" class="auth-tree" :render-after-expand="false"
        icon-class="el-icon-caret-left" :default-checked-keys="[1]" default-expand-all :check-strictly="true">
        <div style="display: flex; align-items: center" slot-scope="{ node, data }">
          <div class="node-lable">
            {{ node.label }}({{data.count}})
            <span v-if="data.children">({{data.children.length}})</span>
            <img v-if="data.parentid!='szwh'" style="width: 30px; margin-right: 15px"
              :src="`/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/szwh-${data.code}.png`" alt="" />
          </div>
        </div>
      </el-tree>
    </div>
    <div class="topDiv">
      <div class="topContainer">
        <div class="item">
          <div class="name">设施总数</div>
          <div class="value">319365</div>
        </div>
        <div class="item">
          <div class="name">当前处置数</div>
          <div class="value">321</div>
        </div>
      </div>
    </div>
  </div>
</body>

<script src="/static/js/jslib/axios.min.js"></script>
<script src="/static/js/jslib/http.interceptor.js"></script>

<script>
  var vm = new Vue({
    el: "#map-middle",
    data() {
      return {
        treeData: [
          {
            id: 1,
            label: "部件",
            code: "景区",
            count: "135",
          },
          {
            id: 2,
            label: "案件",
            code: "等级民宿",
            count: "140",
          },
          {
            id: 3,
            label: "在线监督员",
            code: "文化馆",
            count: "10",
          },
        ],
        currentObj: {
          id: 1,
          label: "部件",
          code: "景区",
        },
        currentLabel: "部件",
      };
    },
    mounted() {
      this.flyTo();
      this.hotPowerMap();
      this.initMap();
      var that = this;
    },
    methods: {
      optionTool(type) {
        if (type === "big") {
          this.rmHot();
          this.getPoint(this.currentObj);
        } else {
          this.rmPoint(this.currentObj);
          this.hotPowerMap();
        }
      },
      initMap() {
        top.mapUtil.tool.changeBaseMap('black');
        $get("/textCity.json").then((res) => {
          let textData = [];
          top.mapUtil.loadTextLayer({
            layerid: "3Dtext",
            data: res,//数据
            style: {
              size: 35,//文字大小
              color: [242, 242, 242, 1],//文字颜色
            },
          })
        });
      },
      treeCheck(node, list) {
        if (list.checkedKeys.length == 2 || list.checkedKeys.length == 3) {
          //单选实现
          this.$refs.tree.setCheckedKeys([node.id]);
        }
      },
      checkChange(item, flag) {
        // debugger;
        console.log(item, flag);
        this.rmAllPoint();
        this.rmHot();
        this.rmPop();
        if (flag) {
          console.log(item, 111111111);
          this.currentObj = item;
          this.currentLabel = item.label;
          if (item.label === "案件" || item.label === "部件") {
            this.hotPowerMap();
          } else {
            this.getPoint(item);
          }
        } else {
          if (item.label === "案件" || item.label === "部件") {
            this.rmHot();
          } else {
            this.rmPoint(item);
          }
        }
      },

      getPoint(item) {
        let that = this;
        // that.rmPoint(item)
        $api("yxzl_szwh_center011", { code: item.code }).then((res) => {
          let pointData = [];
          let icon = res[0] ? "szwh-" + res[0].ly : "";
          res.forEach((obj, index) => {
            let str = {
              data: {
                pointId: "szcg",
                // title: title + '详情',
                // key: key,
                // value: value,
                obj,
              },
              point: obj.lng,
              lng: obj.lng.split(",")[0],
              lat: obj.lng.split(",")[1]
            };
            pointData.push(str);
          });
          console.log('111=>', pointData)
          console.log('222=>', icon)
          top.mapUtil.loadPointLayer({
            data: pointData,
            layerid: "szcg-" + item.id, //图层id
            iconcfg: {
              image: `/static/spritesImage/${icon}.png`,
              iconSize: 0.7
            }, //图标路径和图标大小
            onclick: function (e) {
              that.clickPoint(e)
            },//点位点击的回调事件

          })
        });
      },
      clickPoint(e) {
        debugger
        let that = this
        console.log('点击  回调事件', e)
        const item = e;
        let coor = e.point.split(",");
        console.log(item);
        if (item.data.obj.ly === "文化馆") {
          that.getDetail(item.data, coor);
        } else if (
          item.data.obj.ly === "等级民宿" ||
          item.data.obj.ly === "景区"
        ) {
          // item.obj.ly === '等级民宿'
          // debugger

          let title = item.data.obj.ly === "景区" ? "部件" : "案件";
          top.mapUtil._createPopup({
            layerid: 'syr',
            position: [coor[0], coor[1]],
            content: `<div
              style="
                width: max-content;
                position: absolute;
                border-radius: 5px;
                background-color: rgba(10, 31, 53, 0.8);
                z-index: 999999;
                -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
                box-shadow: inset 0 0 40px 0 #5ba3fa;
                padding: 24px;
              "
            >
              <div
                onclick=" this.parentNode.style.display = 'none'"
                style="
                  position: absolute;
                  right: 30px;
                  top: 10px;
                  font-size: 50px;
                  color: #fff;
                  cursor: pointer;
                  z-index:9
                "
              >
                x
              </div>
              <div class="container">
                <div style="margin: 26px;display: flex; font-size: 40px; color: #fff">
                  <span style="text-align: left">${title}ID ：</span>
                  <span>32424</span>
                </div>
                <div style="margin: 26px;display: flex; font-size: 40px; color: #fff">
                  <span style="text-align: left">${title}名称 ：</span>
                  <span>路灯搞坏</span>
                </div>
                <div style="margin: 26px;display: flex; font-size: 40px; color: #fff">
                  <span style="text-align: left">${title}位置 ：</span>
                  <span>金华市阳光路7点不亮了</span>
                </div>
                <div style="margin: 26px;display: flex; font-size: 40px; color: #fff">
                  <span style="text-align: left">${title}所属部门 ：</span>
                  <span>金华市城管局</span>
                </div>
                <div style="margin: 26px;display: flex; font-size: 40px; color: #fff">
                  <span style="text-align: left">${title}当前办理部门 ：</span>
                  <span>金华市城管局</span>
                </div>
                <div style="margin: 26px;display: flex; font-size: 40px; color: #fff">
                  <span style="text-align: left">所在区县：</span>
                  <span>路灯办</span>
                </div>
                 <div style="margin: 26px;display: flex; font-size: 40px; color: #fff">
                  <span style="text-align: left">所在网格：</span>
                  <span>金华市婺城区白龙桥镇第一网格</span>
                </div>
                <div
                  style="margin: 30px;display: flex; font-size: 50px; color: #fff; margin-top: 20px"
                >
                ${title}处置流程
                </div>
                <div>
                  <div
                    style="
                      display: flex;
                      font-size: 40px;
                      color: #fff;
                      padding-top: 20px;
                      box-sizing: border-box;
                    "
                  >
                    <div
                      style="
                        width: 180px;
                        height: 60px;
                        background-color: #31a7ff;
                        border-radius: 10px;
                        text-align: center;
                        padding-top: 2px;
                        box-sizing: border-box;
                        line-height: 48px;
                      "
                    >
                      上级
                    </div>
                    <div style="margin: 0 40px">→</div>
                    <div
                      style="
                        width: 180px;
                        height: 60px;
                        background-color: #31a7ff;
                        border-radius: 10px;
                        text-align: center;
                        padding-top: 2px;
                        box-sizing: border-box;
                        line-height: 48px;
                      "
                    >
                      受理
                    </div>
                    <div style="margin: 0 40px">→</div>
                    <div
                      style="
                        width: 180px;
                        height: 60px;
                        background-color: #31a7ff;
                        border-radius: 10px;
                        text-align: center;
                        padding-top: 2px;
                        box-sizing: border-box;
                        line-height: 48px;
                      "
                    >
                      立案
                    </div>
                  </div>
                  <div style="font-size: 40px; color: #fff; margin: 5px 0 0 670px">
                    ↓
                  </div>
                  <div
                    style="
                      display: flex;
                      font-size: 40px;
                      color: #fff;
                      padding-top: 5px;
                      box-sizing: border-box;
                      margin-bottom: 20px;
                    "
                  >
                    <div
                      style="
                        width: 180px;
                        height: 60px;
                        background-color: #31a7ff;
                        border-radius: 10px;
                        text-align: center;
                        padding-top: 2px;
                        box-sizing: border-box;
                        line-height: 48px;
                      "
                    >
                      派发
                    </div>
                    <div style="margin: 0 40px">→</div>
                    <div
                      style="
                        width: 180px;
                        height: 60px;
                        background-color: #31a7ff;
                        border-radius: 10px;
                        text-align: center;
                        padding-top: 2px;
                        box-sizing: border-box;
                        line-height: 48px;
                      "
                    >
                      办理
                    </div>
                    <div style="margin: 0 40px">→</div>
                    <div
                      style="
                        width: 180px;
                        height: 60px;
                        background-color: #31a7ff;
                        border-radius: 10px;
                        text-align: center;
                        padding-top: 2px;
                        box-sizing: border-box;
                        line-height: 48px;
                      "
                    >
                      结案
                    </div>
                  </div>
                </div>
              </div>
            </div>`,
            offset: [50, 100],
          })
        }
      },
      //飞入
      flyTo() {
        top.mapUtil.flyTo({
          destination: [119.95478050597587, 29.01613226366889],//飞行中心点
          zoom: 10,//飞行层级
          pitch: 40, //倾斜角
        })
      },
      //加载热力图
      hotPowerMap() {
        // $api("/zt_bsczt_qyrl").then((res) => {
        //   let hotMapData = [];
        //   res.map((item, index) => {
        //     // 画热力图的数据
        //     let pointArr = [];
        //     pointArr[0] = Number(item.jd);
        //     pointArr[1] = Number(item.wd);
        //     pointArr[2] = item.num;
        //     pointArr[3] = index + 1;
        //     hotMapData.push(pointArr);
        //   });
        //   const mapData = {
        //     funcName: "hotPowerMap",
        //     hotPowerMapData: hotMapData,
        //     offset: 256,
        //     heatMapId: "bscztHot",
        //     threshold: 6000,
        //     distance: 800,
        //     alpha: 0.3,
        //   };
        //   window.parent.document
        //     .getElementById("map")
        //     .contentWindow.Work.funChange(JSON.stringify(mapData));
        // });
        top.mapUtil.loadHeatmapLayer({
          layerid: 'bscztHot',//热力图id
          type: 'dynamic',
        })
      },
      //清除热力图
      rmHot() {
        top.mapUtil.removeLayer('bscztHot');
      },
      rmPoint(item) {

        top.mapUtil.removeLayer("szcg-" + item.id);
      },
      rmAllPoint() {
        let checkedNodes = this.$refs.tree.getCheckedNodes();
        let idArr = checkedNodes.map(item => "szcg-" + item.id)
        top.mapUtil.removeAllLayers(idArr);
      },
      getDetail(item, coor) {
        let that = this;
        $get("/3840/shgl/szhggqk/szcgdwxq").then((res) => {
          let result = res.filter((i) => {
            return item.obj.id == i.id;
          });
          let detail = result[0].detail;
          that.getCustom(result[0].path, coor, detail);
        });
      },
      rmPop() {
        top.mapUtil.removeLayer("syr")
      },
      getCustom(path, coor, detail) {
        let pathStr = JSON.stringify(path);
        top.mapUtil.removeLayer("TDT_TITLE_ID")
        const imgIcon = `${baseURL.url}/static/citybrain/tckz/img/tckz_gj/轨迹.png`;
        top.mapUtil._createPopup({
          layerid: 'syr',
          position: [coor[0], coor[1]],
          content: ` <div
              class="pop"
              style="
              width: 1000px;
              position: absolute;
              border-radius: 5px;
              background-color: rgba(10, 31, 53, 0.8);
              z-index: 999999;
              -webkit-box-shadow: 0 0 40px 0 #5ba3fa inset;
              box-shadow: inset 0 0 40px 0 #5ba3fa;
              padding: 24px;
            "
          >
          <div
             onclick=" this.parentNode.style.display = 'none';top.mapUtil.removeLayer('TDT_TITLE_ID')
             "

              style="
                position: absolute;
                right: 30px;
                top: 30px;
                font-size: 40px;
                color: #fff;
                cursor:pointer;
                z-index:9
              "
            >
              x
            </div>
            <div class="container">


              <div style="font-size: 50px;color: #71d8e3;font-weight:600; height: 44px;
    line-height: 44px;   margin-bottom: 20px;margin-left: 35px;" >监督员详情</div>



              <div
                class="item"
                style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
              >
                <span style="width: 30%; text-align: center">监督员id:</span>
                <span style="color: #eccc83; width: 70%">${detail.id}</span>
              </div>
              <div
                class="item"
                style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
              >
                <span style="width: 30%; text-align: center">监督员名字:</span>
                <span style="color: #eccc83; width: 70%">${detail.name}</span>
              </div>
              <div
                class="item"
                style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
              >
                <span style="width: 30%; text-align: center">监督员手机号:</span>
                <span style="color: #eccc83; width: 70%">${detail.phone}</span>
              </div>
              <div
                class="item"
                style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
              >
                <span style="width: 30%; text-align: center">监督员状态:</span>
                <span style="color: #eccc83; width: 70%">${detail.state}</span>
              </div>
              <div
                class="item"
                style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
              >
                <span style="width: 30%; text-align: center">责任网格id:</span>
                <span style="color: #eccc83; width: 70%">${detail.wgid}</span>
              </div>
              <div
                class="item"
                style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
              >
                <span style="width: 30%; text-align: center">监督员级别:</span>
                <span style="color: #eccc83; width: 70%">${detail.jb}</span>
              </div>
              <div
                class="item"
                style="display: flex; font-size: 40px; color: #fff; line-height: 70px"
              >
                <span style="width: 30%; text-align: center">所属区域:</span>
                <span style="color: #eccc83; width: 70%">${detail.area}</span>
              </div>

              <div
                class="item"
                style="display: flex; font-size: 40px; color: #fff; line-height: 70px;align-items: center;"
              >
                <span style="width: 32%; text-align: center">历史轨迹：</span>
                 <div style="max-width:430px; display: flex; flex-wrap: wrap;    justify-content: space-between;">
                  <button

                  style="
                    font-size: 28px; margin: 0 10px;
                    color: #fff;
                    background-color:#b98262;
                    border: unset;
                    width: 171px;
                    height: 60%;
                    height: 59px;
                    line-height: 59px;
                  "
                  onclick="

                  console.log('进入了')
                  window.parent.mapUtil.loadTraceLayer({
                    layerid: 'TDT_TITLE_ID',
                    coordinates: ${pathStr},
                    linecfg: {
                        width: 5
                    },
                    iconcfg: {
                        image: '${imgIcon}',
                        iconSize: [50, 50],
                    }
                     })
                   "
                >
                  点击查看
                </button>

          <button
            style="
              font-size: 28px;
              margin: 0 10px;
              color: #fff;
              background-color: #b98262;
              border: unset;
              width: 171px;
              height: 60%;
              height: 59px;
              line-height: 59px;
            "
            onclick="
             if (
              window.map.TDT_TITLE_ID
                ) {
                  window.map.TDT_TITLE_ID.stop()
                }
             "
          >
            暂停
          </button>
          <button
            style="
              font-size: 28px;
              color: #fff;
              background-color: #b98262;
              border: unset;
              width: 171px;
              height: 60%;
              height: 59px;
              line-height: 59px;
              margin:  10px;
            "
            onclick="
             if (
              window.map.TDT_TITLE_ID
                ) {
                  window.map.TDT_TITLE_ID.start()
                }
             "
          >
            播放
          </button>
          <button
            style="
              font-size: 28px;
              margin:  10px;
              color: #fff;
              background-color: #b98262;
              border: unset;
              width: 171px;
              height: 60%;
              height: 59px;
              line-height: 59px;
            "
            onclick="
             if (
              window.map.TDT_TITLE_ID
                ) {
                  window.map.TDT_TITLE_ID.remove()
                }
             "
          >
            取消
          </button>
                  </div>
              </div>
            </div>
          </div>`,
          offset: [50, 100],
        })
      },
    },
    destroyed() {
      this.rmAllPoint();
      this.rmPop();
    },
  });
</script>

</html>